<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_CHANGE_PASSWORD);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-Control" content="no-cache,must-revalidate">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta http-equiv="Expires" content="0">
<title>修改密码</title>
<link rel="stylesheet" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" href="${basePath}plugins/static/css/edit.css">
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/base64.js"></script>
<script language="javascript">
	var baseContext = "${basePath}/frame/useraction/";
	var basePath = "${basePath}";
	var hasSubmit = false;
	
	$(function() {
		assemblys.getMenuIcon({
			funCode : "${funCode}",
			hasOrg : false,
			dom : $("b#menuIcon")
		});
	});
	
	//保存
	function saveSubmit() {
		var oldPwdValue = $("#oldPwdValue").val();
		var pwdValue = $("#pwdValue").val();
		var pwdValue1 = $("#pwdValue1").val();
		
		var passwordLengthRule = $("#passwordLengthRule").val();
		if (passwordLengthRule == "" && pwdValue.length < 1) {
			assemblys.alert("密码不能为空");
			$("#pwdValue").focus();
			return;
		} else if (passwordLengthRule != "" && pwdValue.length < passwordLengthRule) {
			assemblys.alert("密码最少要" + passwordLengthRule + "位");
			$("#pwdValue").focus();
			return;
		}
		
		if (pwdValue != pwdValue1) {
			assemblys.alert("新密码确认不符");
			$("#pwdValue1").focus();
			return;
		}
		
		var passwordComplexRule = $("#passwordComplexRule").val();
		if (passwordComplexRule == "1") {
			var pwdnum = 0;
			var pwdRegex = new RegExp('(?=.*[0-9])');
			if (pwdRegex.test(pwdValue)) {
				pwdnum += 1;
			}
			pwdRegex = new RegExp('(?=.*[A-Z])');
			if (pwdRegex.test(pwdValue)) {
				pwdnum += 1;
			}
			pwdRegex = new RegExp('(?=.*[a-z])');
			if (pwdRegex.test(pwdValue)) {
				pwdnum += 1;
			}
			pwdRegex = new RegExp('(?=.*[^a-zA-Z0-9])');
			if (pwdRegex.test(pwdValue)) {
				pwdnum += 1;
			}
			if (pwdnum < 3) {
				assemblys.alert("你的密码复杂度太低（密码中必须包含大写字母、小写字母、数字、特殊字符中的至少3种）");
				$("#password").focus();
				return;
			}
		}
		assemblys.confirm("此操作需要重新登录，你确定修改吗？", function() {
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			var base64 = new Base64();
			var url = baseContext + "changePWD.spring";
			$.ajax({
				type : "post",
				url : url,
				data : {
					oldPwdValue : base64.encode(oldPwdValue),
					pwdValue : base64.encode(pwdValue)
				},
				dataType : "text",
				success : function(data) {
					if (data == "OK") {
						var domain = document.domain;
						document.cookie = 'pass=0;expires='+(new Date(1))+';domain='+domain+';path=/'+basePath.split("/")[3]+'/frame/login';
						assemblys.msg('密码修改成功', function() {
							// 退出系统
							top.window.document.body.onbeforeunload = null;
							top.window.location.href = basePath + "/frame/logout.jsp";
							
						});
					} else if (data == "OLD_ERR") {
						hasSubmit = false;
						assemblys.alert("旧密码输入错误");
						$("#oldPwdValue").focus();
					} else {
						hasSubmit = false;
						assemblys.alert("服务器异常，请稍后重试");
					}
				},
				error : function() {
					hasSubmit = false;
					assemblys.alert("你请求的页面有异常");
				}
			});
		})
	}

	function getPassWord(obj) {
		var src = $(obj).attr("src");
		if (src.indexOf("no-eye.png") != -1) {
			$(obj).parent().find("input[type='text']").addClass("layui-hide");
			$(obj).parent().find("input[type='password']").removeClass("layui-hide");
			$(obj).attr("src", basePath + "/frame/images/default/ico/eye.png");
		} else {
			$(obj).parent().find("input[type='text']").removeClass("layui-hide");
			$(obj).parent().find("input[type='password']").addClass("layui-hide");
			$(obj).attr("src", basePath + "/frame/images/default/ico/no-eye.png");
		}
	}

	function setValue(obj) {
		var value = $(obj).val();
		$(obj).siblings().val(value);
	}
</script>
</head>
<body>
	<!-- disableautocomplete 此属性禁止自动显示下拉提示 -->
	<form action="" method="post" class="layui-form" disableautocomplete>
		<input id="passwordLengthRule" name="passwordLengthRule" type="hidden" value='<c:out value="${passwordLengthRule}"/>'>
		<input id="passwordComplexRule" name="passwordComplexRule" type="hidden" value='<c:out value="${passwordComplexRule}"/>'>
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<c:if test="${!empty ps}">
				<span class="head1_text fw700" style="color: red;"> - PS：<c:out value="${ps}"/></span>
			</c:if>
		</div>
		<div class="bodys">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<font color="red">*</font>
					旧密码
				</label>
				<div class="layui-input-inline">
					<!-- 额外增加的内容，解决部分浏览器版本自动填充的问题 ，不影响功能使用  start -->
					<input type="password" style="display: none;" autocomplete="new-password">
					<!-- 额外增加的内容，解决部分浏览器版本自动填充的问题 ，不影响功能使用  end -->
					<input type="text" maxlength="20" maxlength="20" class="layui-input h28 layui-hide" onkeyup="setValue(this);">
					<input type="password" id="oldPwdValue" name="oldPwdValue" maxlength="20" lay-verify="required|specialCharacters" onkeyup="setValue(this);" maxlength="20" class="layui-input h28">
				</div>
				<img style="cursor: pointer; padding-top: 10px" src="${basePath}/frame/images/default/ico/eye.png" onclick="getPassWord(this)">
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<font color="red">*</font>
					新密码
				</label>
				<div class="layui-input-inline">
					<input type="text" maxlength="20" maxlength="20" class="layui-input h28 layui-hide" onkeyup="setValue(this);">
					<input type="password" id="pwdValue" name="pwdValue" maxlength="20" lay-verify="required|specialCharacters|noChinese" onkeyup="setValue(this);" maxlength="20" class="layui-input h28">
				</div>
				<img style="cursor: pointer; padding-top: 10px" src="${basePath}/frame/images/default/ico/eye.png" onclick="getPassWord(this)">
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<font color="red">*</font>
					新密码确认
				</label>
				<div class="layui-input-inline">
					<input type="text" maxlength="20" maxlength="20" class="layui-input h28 layui-hide" onkeyup="setValue(this);">
					<input type="password" id="pwdValue1" name="pwdValue1" maxlength="20" lay-verify="required|specialCharacters|noChinese" onkeyup="setValue(this);" maxlength="20" class="layui-input h28">
				</div>
				<img style="cursor: pointer; padding-top: 10px" src="${basePath}/frame/images/default/ico/eye.png" onclick="getPassWord(this)">
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">&nbsp;</label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="保存" lay-submit lay-filter="save" />
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			noChinese : function(value, item) {
				var reg = /[^\x00-\xff]/;
				if (reg.test(value)) {
					return "不能包含中文";
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			specialCharacters : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】";
				}
			},
			isNum : function(value, item) {
				if (isNaN(value) || parseFloat(value) < 0) {
					return "必须大于等于0";
				}
			}
		});
		form.on("submit(save)", function(data) {
			saveSubmit();
			return false;
		});
	});
	$(function() {
		$("#oldPwdValue").focus();
	});
</script>