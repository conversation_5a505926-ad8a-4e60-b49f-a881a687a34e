var scanQRCodeBind = {
	// 绑定操作
	optType : "bindUser",
	userCode : userCode,
	// 二维码缓存
	QRCodeConfigCache : {},
	/**
	 * 加载扫码
	 * @param moblieCode
	 * @returns
	 */
	loadQRCode : function() {
		
		if (ddUserID) {
			$("#MOBLIE_DINGDING").attr("hasDisable", "false").find("img").attr("title", "已绑定").attr("src", basePath + "frame/login/images/code1.png");
		}
		if (wxUserID) {
			$("#MOBLIE_QIYEWENXIN").attr("hasDisable", "false").find("img").attr("title", "已绑定").attr("src", basePath + "frame/login/images/code2.png");
		}
		if (wxOpenID) {
			$("#MOBLIE_WECHAT").attr("hasDisable", "false").find("img").attr("title", "已绑定").attr("src", basePath + "frame/login/images/code3.png");
		}
		if (fsUserID) {
			$("#MOBLIE_FEISHU").attr("hasDisable", "false").find("img").attr("title", "已绑定").attr("src", basePath + "frame/login/images/code4.png");
		}
		
		var QRType = [ "MOBLIE_QIYEWENXIN", "MOBLIE_DINGDING", "MOBLIE_WECHAT","MOBLIE_FEISHU" ];
		for (var i = 0; i < QRType.length; i++) {
			var type = QRType[i];
			// moblie 拿到的配置信息
			scanQRCodeBind.getMoblieConfig(type);
		}
		// 监听
		scanQRCodeBind.loadMonitor();
	},
	// 获取配置
	getMoblieConfig : function(moblieCode) {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/moblieSetting/getByCode.spring",
			type : "get",
			data : {
				"moblieCode" : moblieCode,
				"compNo" : compNo,
				"appCode" : assemblys.top.pubApp.appCode.indexOf(",") != -1 ? assemblys.top.pubApp.appCode.replace("APP,", "") : ""
			},
			dataType : "json",
			success : function(moblie) {
				// 存在，并且启用
				if (moblie.status && moblie.status == 1) {
					$("#" + moblieCode).removeClass("layui-hide");
					scanQRCodeBind.QRCodeConfigCache[moblieCode] = moblie;
				}
			}
		});
	},
	setQRImageStyle : function(id) {
		if (id == "MOBLIE_DINGDING") {
			$("#QRImage iframe").css({
				"position" : "fixed",
				"top" : "0",
				"left" : "0",
				"right" : "0",
				"bottom" : "0",
				"margin" : "auto"
			});
			
			$("#QRImage").css("display", "");
			
		} else if (id == "MOBLIE_QIYEWENXIN") {
			$("#QRImage iframe").css({
				"position" : "fixed",
				"top" : "0",
				"left" : "0",
				"right" : "0",
				"bottom" : "0",
				"margin" : "auto",
				"background" : "#fff",
				"padding-top" : "20px"
			});
			
			$("#QRImage").css("display", "");
			
		} else if (id == "MOBLIE_WECHAT") {
			// 开启容器
			$("#QRImageGZHDiv").show();
			$("#QRImageGZH").css({
				"position" : "fixed",
				"top" : "0",
				"left" : "0",
				"right" : "0",
				"bottom" : "0",
				"margin" : "auto",
				"width" : "300px"
			});
		}//MOBLIE_FEISHU
		 else if (id == "MOBLIE_FEISHU") {
			// 开启容器
			$("#QRImageGZHDiv").show();
			$("#QRImageGZH").css({
				"position" : "fixed",
				"top" : "0",
				"left" : "0",
				"right" : "0",
				"bottom" : "0",
				"margin" : "auto",
				"width" : "300px"
			});
		}
	},
	/**
	 * 加载监听器
	 */
	loadMonitor : function() {
		
		// 触发
		$('.QR_code').children("img").bind("click", function() {
			$('.QR_delete').css({
				marginLeft : '200px',
				marginBottom : '190px'
			});
			
			var id = $(this).parent().attr("id");
			var hasDisable = $(this).parent().attr("hasDisable") || "true";
			// 如果不存在
			if (hasDisable == "false") {
				scanQRCodeBind.unbindUser(this);
				return;
			}
			
			if (id == "MOBLIE_DINGDING") {
				ddAndWxUtil.ddQRCodeInit(scanQRCodeBind.QRCodeConfigCache[id], "QRImage")
			} else if (id == "MOBLIE_QIYEWENXIN") {
				ddAndWxUtil.wxQRCodeInit(scanQRCodeBind.QRCodeConfigCache[id], "QRImage")
			} else if (id == "MOBLIE_WECHAT") {
				wxGZHUtil.getQRCode(scanQRCodeBind.QRCodeConfigCache[id], "QRImageGZH", function(data) {
					// 绑定
					var url = basePath + "/frame/excludeUrl/scanCode/scanCodeLogin.spring?optType=bindUser"
					url += "&wxOpenID=" + data.wxOpenID;
					url += "&state=wxOpenID";
					url += "&compNo=" + data.compNo;
					url += "&userCode=" + data.userCode;
					location.href = url;
					
				});
			} else if (id == "MOBLIE_FEISHU") {
				return;
			}
			// 设置样式
			scanQRCodeBind.setQRImageStyle(id);
			// 打开渲染二维码区和遮罩
			$('.QR_window_show,.QR_window').show();
			
		})

		// 点click销毁
		$('.QR_delete').bind('click', function() {
			$("#QRImage").removeAttr("style").empty();
			$("#QRImageGZH").attr("src", "").removeAttr("style");
			// 关闭二维码区、遮罩和公众号容器
			$('.QR_window_show,.QR_window,#QRImageGZHDiv').hide();
		})

	},
	/**
	 * 解绑信息
	 */
	unbindUser : function(obj) {
		var loginKey = $(obj).attr("loginKey");
		assemblys.confirm("确定解除当前绑定信息吗", function() {
			// 返回JSON形式
			$.ajax({
				url : basePath + "frame/login/unbindUser.spring",
				type : "post",
				data : {
					"loginKey" : loginKey,
					"compNo" : compNo,
					"userCode" : userCode
				},
				dataType : "json",
				success : function(data) {
					assemblys.msg("解绑成功", function() {
						location.reload();
					});
				}
			});
		});
		
	},
	init : function() {
		scanQRCodeBind.loadQRCode();
	}
}
scanQRCodeBind.init();

/*s += "<div class=\"item\" data-z=\""
.concat(zType, "\" data-path=")
.concat(path, ">")
.concat(root ? '' : lineTop(index == 0 && !root || index == tag_index && zType == 'left', index == len - 1 || index == tag_index && zType == 'right'), "<div class=\"content\"><div class=\"template\" draggable=\"")
.concat(isdrop, "\">")
.concat(itemTemplate(item, path), "</div></div>")
.concat(item.children && item.children.length > 0 && expandBox || "")
.concat(tag)
.concat(
item.children && item.children.length > 0 ? 
"".concat(tag == "" ? lineDom : "", "<div class=\"row\">")
.concat(template(item.children, item.children.length == 1, false, parent + (parent && "-") + index, tagIndex, zr), "</div>") : '', "</div>");*/