// 全局变量
var param = {
	appID : 0,
	userID : 0,
	roleID : 0,
	formFunID : 0,
	hasAllRight : false
}

var selectAppFuncList = {
	// 初始化
	initParam : function() {
		param.appID = $("#appID").val();
		param.userID = $("#userID").val();
		param.roleID = $("#roleID").val();
		param.formFunID = $("#formFunID").val();
		param.hasAllRight = $("#hasAllRight").val();
		
		// 加载
		selectAppFuncList.initTable();
	},
	/**
	 * 获取数据
	 */
	initTable : function() {
		$.ajax({
			url : basePath + "frame/appFunsSet/getAppFunsList.spring",
			type : "post",
			dataType : "json",
			data : param,
			success : function(data) {
				hasSubmit = false;
				if (data.result == "success") {
					// Dom结构处理
					selectAppFuncList.loadTable(data);
				} else {
					assemblys.alert("获取列表数据出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("获取列表数据出错，请联系管理员");
			}
		});
	},
	// 加载表格
	loadTable : function(data) {
		// items数据
		var items = data.appfunList;
		// 渲染目标
		var $tableView = $("#tableView");
		// 清空dom
		$tableView.empty();
		// 如果无结果
		if (items.length == 0) {
			$tableView.html(selectAppFuncList.notDataView());
		} else {
			// 克隆对象
			var $tr = $("#tr_template");
			// td总长度
			var tdLength = $tr.children().length;
			// td映射关系
			var keys = [ "funID", "funName" ];
			var subID = 0;
			// 处理table body
			for (var i = 0, length = items.length; i < length; i++) {
				// 克隆tr
				var $trClone = $tr.clone();
				$trClone.removeClass("layui-hide").removeAttr("id");
				// 获取所有td
				var $tds = $trClone.children();
				// 每个tr 第一个td
				var $td0 = $tds.eq(0);
				// 每个map
				var itemsObj = items[i];
				// --- 绑定操作 start --
				var key0 = keys[0];
				$td0.find("[name='funID']").attr(key0, itemsObj[key0])
				// --- 绑定操作 end --
				// 根据td映射关系，从数据对象中进行数据填装
				for (var j = 1; j < tdLength; j++) {
					var key = keys[j];
					var value = itemsObj[key];
					if (key == "funName") {
						value = itemsObj["funName"] + "<font style='color: #BEBEBE;'> - " + itemsObj["funCode"] + "</font>";
					}
					// 填装TD
					$tds.eq(j).html(value);
				}
				
				var rights = itemsObj["rights"].split(",");
				for (var k = 0; k < rights.length; k++) {
					$tds.eq(parseInt(rights[k]) + 1).html("<i class='layui-icon2' >&#xea18;</i>");
				}
				
				$tds.eq(8).html(itemsObj["funDesc"]);
				
				if (subID == 0 || subID != itemsObj["subID"]) {
					var $bigTrClone = $tr.clone();
					$bigTrClone.removeClass("layui-hide").removeAttr("id");
					$bigTrClone.empty().append("<td style='background:#F2F2F2;' colspan='" + $("#tr_template").children().length + "' align='left'><strong style='margin-left:5px;' >" + itemsObj["subName"] + "</strong></td>");
					// 写入table
					$tableView.append($bigTrClone);
					subID = itemsObj["subID"];
				}
				// 写入table
				$tableView.append($trClone);
			}
			selectAppFuncList.loadMoitor();
		}
	},
	/**
	 * 加载分液器
	 */
	loadMoitor : function() {
		var form = layui.form;
		form.on('checkbox(funIDALL)', function(data) {
			var flag = $("#funIDALL").is(":checked");
			$("input:checkbox[name='funID']").each(function() {
				if (flag) {
					$(this).parent().find(".layui-unselect").addClass("layui-form-checked");
				} else {
					$(this).parent().find(".layui-unselect").removeClass("layui-form-checked");
				}
				$(this).prop("checked", flag);
			});
			return false;
		});
		form.render();
		
	},
	
	/**
	 * 无数据时
	 */
	notDataView : function() {
		// 无数据时，获取td长度，IE缺少会页面变形
		var length = $("#tr_template").children().length;
		return "<tr><td colspan='" + length + "' align='center'>没有相关数据！</td></tr>";
	},
	init : function() {
		selectAppFuncList.initParam();
	},
	// 确定
	save : function() {
		var funIDs = new Array();
		var hasFunNum = 0;
		$("input[name='funID']:checked").each(function() {
			if ($(this).attr("funID")) {
				funIDs.push($(this).attr("funID"));
				hasFunNum++;
			}
			
		})
		if (hasFunNum == 0) {
			assemblys.msg("请选择要克隆的应用功能");
			return false;
		}
		
		assemblys.confirm("你确定要将权限克隆到选中应用功能吗？<br><font style='color:red'>「将会清除当前所选功能相关的组织架构权限」</font>", function() {
			// 防止重复提交
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			$.ajax({
				url : basePath + "frame/roleright/copyOrgRights.spring",
				data : {
					"userID" : param.userID,
					"roleID" : param.roleID,
					"formFunID" : param.formFunID,
					"funIDs" : funIDs.join(";")
				},
				dataType : "json",
				success : function(data) {
					if (data.result == "success") {
						assemblys.msg("克隆成功", function() {
							// 关闭窗口
							assemblys.closeWindow();
						});
					} else {
						assemblys.alert("服务运行出错，克隆失败");
					}
				},
				error : function() {
					assemblys.alert("获取服务接口无响应，请联系管理员");
				}
			});
		});
	}
}

selectAppFuncList.init();
