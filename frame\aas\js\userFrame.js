var param = {
	keyword : "",
	compNo : "",
	method : "createXml",
	type : "CD",
	level : "0",
	appGroupName : "医疗管控平台"
};

function getDict() {
	// 返回JSON形式
	return $.ajax({
		url : basePath + "frame/dict/getDictByCode.spring",
		type : "get",
		data : {
			"dictCode" : "LoginPasswordRule",
			"appCode" : "APP",
		},
		dataType : "json",
		success : function(data) {
			if (data.result == "success") {
				if (data.dict) {
					$("#showContainer").removeClass("layui-hide");
				}
			}
		}
	});
}

function initRight() {
	// 返回JSON形式
	return $.ajax({
		url : basePath + "frame/useraction/initRight.spring",
		type : "get",
		dataType : "json",
		success : function(data) {
			if (data.result == "success") {
				window.hasExecRight = data.hasExecRight;
			}
		}
	});
}

// 树
var tree_data = getNodes();
layui.tree.render({
	elem : '#tree',
	click : function(item) {
		item = item.data;
		if (item.title != "医疗管控平台") {
			$("#hide").show();
			doAction1(item.id == item.compNo ? "" : item.id, item.title, item.compNo, item.compName);
		} else {
			$("#hide").hide();
			doAction1("", "", "", "");
		}
		
	},
	data : tree_data.length > 1 ? [ {
		"title" : "医疗管控平台",
		"spread" : true,
		children : tree_data
	} ] : tree_data,
	onlyIconControl : true
});

var form = layui.form;
form.on('radio(showOut)', function(data) {
	getUserList(1, 20);
});
layui.form.render();

function getNodes() {
	var treeType = "CSD";
	var firstOrg = "c";
	var funCode = "LIMIT_USER";
	var orgUse = "99";
	
	// 为了保持contorller处理数据的一致性
	var list = "";
	var url = basePath + "frame/department/getTreeList.spring?treeType=" + treeType + "&org=" + firstOrg + "&funCode=" + funCode + "&orgUse=" + orgUse + "&cancelState=1";
	$.ajax({
		type : "post",
		url : url,
		dataType : "json",
		async : false,
		success : function(data) {
			if (data.result == "success") {
				list = data.compList;
			}
		},
		error : function(data) {
			assemblys.alert("网络错误，请联系网络管理员")
		}
	});
	return list;
}

// 点击科室
function doAction1(deptID, deptName, compNo, compName) {
	$("#deptId").val(deptID);
	$("#deptName").val(deptName);
	$("#compNo").val(compNo);
	$("#compName").val(compName);
	getUserList(1, 20);
}

//去空格
function trim(str) {
	var regBegSpace = /^(\s+)/;
	var regEndSpace = /(\s+)$/;
	var r = str.replace(regBegSpace, "").replace(regEndSpace, "");
	return (r);
}

//根据条件查询
function serchSubmit() {
	var compNo = document.getElementById("compNo").value;
	var deptId = document.getElementById("deptId").value;
	var compName = document.getElementById("compName").value;
	var deptName = document.getElementById("deptName").value;
	var serchWhere = trim(document.getElementById("serchWhere").value);
	document.forms[0].action = baseContext + "list.spring?serchWhere=" + serchWhere + "&CompNo=" + compNo + "&DeptID=" + deptId + "&CompName=" + compName + "&DeptName=" + deptName;
	document.forms[0].submit();
	
}
//修改密码、模板
function editPwd(obj) {
	var userId = $(obj).attr("userId");
	var userName = $(obj).attr("userName");
	var compNo = $(obj).attr("compNo");
	var url_pop = baseContext + "showPwd.spring?userId=" + userId + "&compNo=" + compNo + "&flag=PWD";
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '密码修改设置:用户-' + userName,
		scrollbal : false,
		area : [ '500px', '410px' ],
		content : url_pop
	});
}
//新增
function newSubmit() {
	var url = baseContext + "newUser.spring?compNo=" + $("#compNo").val() + "&deptId=" + $("#deptId").val();
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '新增用户',
		area : [ '870px', '80%' ],
		content : url,
		end : function() {
			getUserList(1, 20);
		}
	});
}
//停用或启用用户
function cancelOpen(obj) {
	var userId = $(obj).attr("userId");
	var userName = $(obj).attr("userName");
	var opt = $(obj).attr("opt");
	if (opt == "1") {
		openUser(userId, userName);
	} else {
		cancel(userId, userName);
	}
}

// 取消用户
function cancel(userId, userName) {
	assemblys.confirm("确认停用用户 [" + userName + "] 吗？", function() {
		var method = "GET";
		var url = baseContext + "cancelUser.spring?userId=" + userId;
		var content = null;
		var responseType = "text";
		var callback = cancelBack;
		$.ajax({
			"url" : url,
			"type" : method,
			"data" : content,
			"dataType" : responseType,
			"success" : callback,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	});
}
function cancelBack(data) {
	var http_request = {
		responseText : data
	};
	if (http_request.responseText == "CANCEL_OK") {
		assemblys.msg("停用成功", function() {
			$(".layui-laypage-btn").click();
		});
	} else {
		assemblys.msg("服务器异常");
	}
}
//启用用户
function openUser(userId, userName) {
	assemblys.confirm("确认启用用户 [" + userName + "] 吗？", function() {
		var method = "GET";
		var url = baseContext + "openUser.spring?userId=" + userId;
		var content = null;
		var responseType = "text";
		var callback = openBack;
		$.ajax({
			"url" : url,
			"type" : method,
			"data" : content,
			"dataType" : responseType,
			"success" : callback,
			"error" : function(e) {
				assemblys.alert("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	});
}

function openBack(data) {
	var http_request = {
		responseText : data
	};
	if (http_request.responseText == "OPEN_OK") {
		assemblys.msg("启用成功", function() {
			$(".layui-laypage-btn").click();
		});
	} else {
		assemblys.msg("服务器异常");
	}
}

//解锁用户
function enableUser(obj) {
	var userID = $(obj).attr("userId");
	var userName = $(obj).attr("userName");
	assemblys.confirm("确认解锁用户 [" + userName + "] 吗？", function() {
		var url = baseContext + "enableUser.spring";
		$.ajax({
			"url" : url,
			"type" : "post",
			"data" : {
				userID : userID
			},
			"dataType" : "json",
			"success" : function(data) {
				if (data.result == "success") {
					assemblys.msg("解锁成功", function() {
						$(".layui-laypage-btn").click();
					});
				}
			},
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	});
}

//分配角色
function editRole(obj) {
	
	var userId = $(obj).attr("userId");
	var userName = $(obj).attr("userName");
	var compNo = $(obj).attr("compNo");
	
	if (document.getElementById("reSubmit").value == "YES") {
		var deptId = document.getElementById("deptId").value;
		var serchWhere = document.getElementById("serchWhere").value;
		var compName = document.getElementById("compName").value;
		var deptName = document.getElementById("deptName").value;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '给当前用户【 ' + userName + "】分配角色",
			scrollbal : false,
			area : [ '600px', '530px' ],
			content : baseContext + "editRole.spring?userId=" + userId + "&userName=" + encodeURIComponent(userName) + "&CompNo=" + compNo,
			end : function() {
				$(".layui-laypage-btn").click();
			}
		});
	} else {
		assemblys.msg("请等待...");
	}
}

//修改用户资料....
function editUser(obj) {
	
	var userId = $(obj).attr("userId");
	var userName = $(obj).attr("userName");
	
	if (document.getElementById("reSubmit").value == "YES") {
		var compNo = document.getElementById("compNo").value;
		var deptId = document.getElementById("deptId").value;
		var serchWhere = document.getElementById("serchWhere").value;
		var compName = document.getElementById("compName").value;
		var deptName = document.getElementById("deptName").value;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '编辑用户',
			area : [ '870px', '80%' ],
			content : baseContext + "editUser.spring?userId=" + userId + "&userName=" + encodeURIComponent(userName) + "&compNo=" + compNo + "&deptId=" + deptId + "&serchWhere=" + encodeURIComponent(serchWhere) + "&CompName=" + encodeURIComponent(compName) + "&DeptName=" + encodeURIComponent(deptName),
			end : function() {
				$(".layui-laypage-btn").click();
			}
		});
	} else {
		assemblys.msg("请等待...");
	}
}

var cloneParam = {
	userID : "",
	userName : ""
}
//复制权限
function cloneRight(obj) {
	cloneParam.userID = $(obj).attr("userId");
	cloneParam.userName = $(obj).attr("userName");
	var userName = $(obj).attr("userName");
	var compNo = $(obj).attr("compNo");
	// 指定医院，选科室人员
	layer.open({
		type : 2,
		title : "将【" + userName + "】权限复制给",
		scrollbar : false,
		area : [ '600px', '450px' ],
		content : basePath + "plugins/udSelector/selectUserPage.jsp?compNo=" + compNo + "&type=user&callback=cloneCallback&model=radio"
	});
	
}

function cloneCallback(data) {
	var userID = cloneParam.userID;
	var userName = cloneParam.userName;
	assemblys.confirm("确定将【" + userName + "】复制给【" + data.userName + "】？", function() {
		var url = baseContext + "cloneRight.spring?toCompNo=" + data.compNo + "&toUserCode=" + data.userCode + "&fromUserId=" + userID + "&str=";
		$.ajax({
			"url" : url,
			"type" : "post",
			"dataType" : "text",
			"success" : function(data) {
				if (data == 1) {
					assemblys.msg("复制权限成功");
				} else {
					assemblys.msg("复制失败，请联系系统管理员");
				}
				$(".layui-laypage-btn").click();
			},
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	});
}

function getUserList(page, intPageSize) {
	var searchWhere = trim(document.getElementById("serchWhere").value);
	if (searchWhere.indexOf("/") > 0) {
		searchWhere = "";
	}
	var url = baseContext + "list.spring?page=" + page + "&intPageSize=" + intPageSize;
	var ser = $("form").serialize();
	if (searchWhere) {
		ser = "serchWhere=" + searchWhere + "&" + ser;
		ser = encodeURI(ser);
	}
	
	window._page = page;
	window._intPageSize = intPageSize;
	$.ajax({
		url : url,
		data : ser,
		success : function(data) {
			$(".tableDiv").empty().append(data);
			var form = layui.form;
			layui.form.render();
			if (window.hasExecRight) {
				$(".list-relation").removeClass("layui-hide");
				$("td.list-relation i").mouseenter(function(e) {
					assemblys.tips(this, "根据已绑定的企业微信、钉钉、公众号、飞书等显示对应的图标，点击图标解除绑定，解除后图标消失。", 0, "bottom");
				});
			}
			
		}
	});
}

function syncUserPinYin() {
	assemblys.confirm("该操作会同步人员拼音字段，你确定要继续吗？", function() {
		var url = basePath + "/frame/useraction/syncUserPinYin.spring";
		$(".loading").removeClass("layui-hide");
		$.ajax({
			type : "get",
			url : url,
			success : function(data) {
				$(".loading").addClass("layui-hide");
				assemblys.alert("已同步人员拼音字段");
			}
		});
	});
}

function unbindUser(obj) {
	var loginName = $(obj).attr("loginName");
	var userName = $(obj).attr("userName");
	assemblys.confirm("确定解除『" + userName + "』的【" + loginName + "】绑定信息吗？", function() {
		var loginKey = $(obj).attr("loginKey");
		var userCode = $(obj).attr("userCode");
		var compNo = $(obj).attr("compNo");
		// 返回JSON形式
		$.ajax({
			url : basePath + "frame/login/unbindUser.spring",
			type : "post",
			data : {
				"loginKey" : loginKey,
				"compNo" : compNo,
				"userCode" : userCode
			},
			dataType : "json",
			success : function(data) {
				assemblys.msg("解绑成功", function() {
					getUserList(window._page, window._intPageSize);
				});
			}
		});
	});
	
}
