var param = {
	keyword : "",
	compNo : "",
	funCode : "LIMIT_USER_RIGHTS",
	orgUse : "99",
	appID : "0"
};

// 用于右侧界面刷新
var theUrl = "";

// 用于查询用户、角色
var baseContext = basePath + "/frame/roleright/";

// 用于加载右侧权限界面
var submitUrl = basePath + "/frame/roleright/index.spring?1=1";

// 防止二次弹出窗
var hasOpenWindow = true;

$(function() {
	getAppList();
	initUserTree();
	initRoleTree();
});

/**
 * 监听树切换
 * 
 * @returns
 */
var form = layui.form;
form.on('radio(juese)', function(data) {
	$("[name=treeType2][value=comp]").next().click();
	$("[name=treeType3][value=role]").next().click();
	changeTree('role');
});

form.on('radio(yonghu)', function(data) {
	$("[name=treeType2][value=comp]").next().click();
	$("[name=treeType3][value=role]").next().click();
	changeTree('user');
});

form.render();

// 切换树显示
function changeTree(type) {
	if (type == 'user') {
		$("div.treeDiv-user").removeClass("layui-hide");
		$("div.treeDiv-role").addClass("layui-hide");
	} else {
		$("div.treeDiv-user").addClass("layui-hide");
		$("div.treeDiv-role").removeClass("layui-hide");
	}
}

/**
 * *=====================================用户 start
 * ==================================
 */

/**
 * 加载用户树
 */
function initUserTree() {
	// 树
		var tree_data = getNodes();
		layui.tree.render({
			elem : '#tree1',
			parent : 'div.treeDiv-user',
			click : function(item) {
				var item = item.data;
				
				if (item.type && item.type == 'root') {
					return;
				}
				
				if (item.hasChildren == undefined) {
					assemblys.msg("请选择科室下的用户");
				} else {
					doAction1(item.id, item.title, item.compNo, item.compName)
				}
			},
			data : [ {
				"title" : "医疗管控平台",
				"spread" : true,
				children : tree_data,
				type : 'root'
			} ]
		});
}

/**
 * 获取用户树
 */
function getNodes() {
	// 为了保持contorller处理数据的一致性
	var list = "";
	var url = basePath + "frame/department/getTreeList2.spring";
	$.ajax({
		type : "post",
		url : url,
		dataType : "json",
		async : false,
		data : {
			"funCode" : param.funCode,
			"orgUse" : param.orgUse,
			"hasAllRight" : hasAllRight,
			"appID" : $("#appList").val()
		},
		success : function(data) {
			if (data.result == "success") {
				list = data.compList;
			} else {
			}
		},
		error : function(data) {
			assemblys.alert("网络错误，请联系网络管理员")
		}
	});
	return list;
}

// 点击科室
function doAction1(deptID, deptName, compNo, compName) {
	if (!hasOpenWindow) {
		return;
	}
	param.compNo = compNo;
	var url = basePath + "/frame/tree/findUserList.spring?1=1&deptId=" + deptID + "&t=" + new Date().getTime();
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '用户列表',
		scrollbar : false,
		maxmin: true,
		area : [ '700px', '400px' ],
		content : url,
		success : function() {
			hasOpenWindow = false;
		},
		end : function() {
			hasOpenWindow = true;
		}
	});
}

/**
 * 模糊查询-用户
 */
function findUser() {
	var text = $.trim(document.getElementById("user_txt").value);
	if (text == "") {
		assemblys.msg("请输入查询条件");
		document.getElementById("user_txt").focus();
		return;
	}
	var method = "POST";
	var url = baseContext + "findUser.spring";
	var content = {
		text : text,
		hasAllRight : hasAllRight
	};
	var responseType = "text";
	var callback = findUserBack;
	$.ajax({
		"url" : url,
		"type" : method,
		"data" : content,
		"dataType" : responseType,
		"success" : callback,
		"error" : function(e) {
			assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
		}
	});
}

function findUserBack(data) {
	var http_request = {
		responseText : data
	};
	if (!hasOpenWindow) {
		return;
	}
	var temp = http_request.responseText.split("$|$");
	if (temp[1] == 1) {
		param.compNo = temp[6];
		// 判断权限
		if(checkCompApp()){
			// 直接跳
			doAction2(temp[2], temp[4], temp[3], temp[5]);
		} else {
			assemblys.msg("当前查询的用户所属医院无该应用权限", null, 5000);
		}
		
	} else if (temp[1] == 0) {
		assemblys.msg("没有查询到该用户");
	} else {
		var url = baseContext + "findUserList.spring?text=" + temp[0] + "&t=" + new Date().getTime() + "&hasAllRight=" + hasAllRight;
		url = encodeURI(url);
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '查找用户列表',
			scrollbar : false,
			maxmin: true,
			area : [ '700px', '400px' ],
			content : url,
			success : function() {
				hasOpenWindow = false;
			},
			end : function() {
				hasOpenWindow = true;
			}
		});
	}
}

// 点击用户/员工
function doAction2(userId, empNo, empName, deptID) {
	empName = encodeURIComponent(empName);
	getHtml(submitUrl + "&userId=" + userId + "&app_list=false" + "&empName=" + empName);
}

/**
 * *=====================================用户 end
 * ==================================
 */

/**
 * *=====================================角色 start
 * ==================================
 */

/**
 * 加载角色树
 */
function initRoleTree() {
	
		// 树
		var tree_data = getRoleNodes();
		layui.tree.render({
			elem : '#tree2',
			parent:"div.treeDiv-role",
			click : function(item) {
				var item = item.data;
				if (item.type && item.type == 'root') {
					return;
				}
				if (item.compNo) {
					param.compNo = item.compNo;
					doRoleAction1(item.id, item.title);
				}
			},
			data : [ {
				"title" : "医疗管控平台",
				"spread" : true,
				children : tree_data,
				type : 'root'
			} ]
		});
	
}

/**
 * 获取角色树
 */
function getRoleNodes() {
	// 为了保持contorller处理数据的一致性
	var list = "";
	var url = basePath + "frame/department/getRoleTreeList.spring";
	$.ajax({
		type : "post",
		url : url,
		dataType : "json",
		async : false,
		data : {
			"hasAllRight" : hasAllRight,
			"appID" : $("#appList").val()
		},
		success : function(data) {
			if (data.result == "success") {
				list = data.appList;
			} else {
			}
		},
		error : function(data) {
			assemblys.alert("网络错误，请联系网络管理员")
		}
	});
	return list;
}

/**
 * 模糊查询 - 角色
 */
function findRole() {
	var text = $.trim(document.getElementById("role_txt").value);
	if (text == "") {
		assemblys.msg("请输入查询条件");
		document.getElementById("role_txt").focus();
		return;
	}
	var method = "GET";
	var url = baseContext + "findRole.spring?text=" + text + "&hasAllRight=" + hasAllRight;
	url += "&appID=" + $("#appList").val();
	var content = null;
	var responseType = "text";
	var callback = findRoleBack;
	url = encodeURI(url);
	$.ajax({
		"url" : url,
		"type" : method,
		"data" : content,
		"dataType" : responseType,
		"success" : callback,
		"error" : function(e) {
			assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
		}
	});
}

function findRoleBack(data) {
	var http_request = {
		responseText : data
	};
	if (!hasOpenWindow) {
		return;
	}
	var temp = http_request.responseText.split("$|$");
	if (temp[1] == 1) {
		param.compNo = temp[5];
		// 直接跳
		doRoleAction1(temp[2], temp[3]);
	} else if (temp[1] == 0) {
		assemblys.msg("没有查询到该角色");
	} else {
		var width = "550px";
		var height = "560px";
		var left = screen.width / 2 - width / 2;
		var top = screen.height / 2 - height / 2;
		var url_pop = baseContext + "findRoleList.spring?text=" + temp[0] + "&t=" + new Date().getTime() + "&hasAllRight=" + hasAllRight;
		url_pop += "&appID=" + $("#appList").val();
		url_pop = encodeURI(url_pop);
		var ua = navigator.userAgent.toLowerCase();
		if (ua.indexOf("dingtalk-win") > -1) {// 钉钉浏览器
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '查找角色列表',
				scrollbar : false,
				area : [ '700px', '400px' ],
				content : url_pop,
				success : function() {
					hasOpenWindow = false;
				},
				end : function() {
					hasOpenWindow = true;
				}
			});
		} else {
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '查找角色列表',
				scrollbar : false,
				area : [ '700px', '400px' ],
				content : url_pop,
				success : function() {
					hasOpenWindow = false;
				},
				end : function() {
					hasOpenWindow = true;
				}
			});
		}
	}
}

// 点击角色
function doRoleAction1(roleID, roleName) {
	roleName = encodeURIComponent(roleName);
	getHtml(submitUrl + "&roleId=" + roleID + "&app_list=true" + "&roleName=" + roleName);
}

/**
 * *=====================================角色 end
 * ==================================
 */

/**
 * 获取医院授权的应用
 * 
 * @param compNo
 * @returns
 */

// 上一次记录的医院
var prevCompNo = "";

function getAppList() {
	var hasData = false;
	var url = basePath + "/frame/comp/getCompAppRight.spring";
	$.ajax({
		type : "post",
		url : url,
		data : {
			"menuRight" : 0
		},
		dataType : "json",
		async : false,
		success : function(data) {
			if (data.result == "success") {
				var option = "";
				if (data.appList.length != 0) {
					$.each(data.appList, function(i, e) {
						// 第一次时默认第一个
						if ((i == 0 && param.appID == "0") || prevCompNo != param.compNo) {
							param.appID = e.appID;
							prevCompNo = param.compNo;
						}
						// 是否选中
						var hasSelect = "";
						if (param.appID == e.appID) {
							hasSelect = "selected";
						}
						option += "<option value='" + e.appID + "' " + hasSelect + "  >" + e.appName + "</option>";
					});
					$("#appList").html(option);
					hasData = true;
				} else {
					hasData = false;
				}
			} else {
				assemblys.alert("获取权限应用出错，请刷新或重试")
			}
			
		},
		error : function(data) {
			assemblys.alert("网络错误，请联系网络管理员")
		}
	});
	layui.use([ 'form', 'layer' ], function() {
		var layer = layui.layer;
		var form = layui.form;
		form.on("select(appList)", function(data) {
			param.appID = data.value;
			
			if ($(".treeDiv-user").is(":hidden")) {
				
				// 每次切换重置角色
				$("#topTitle").text("");
				$("#tableBody").empty();
				
				initRoleTree();
				
			} else {
				initUserTree();
				
				// 加载权限
				if(checkCompApp()){
					// 刷新系统功能
					toUserRightIndex(theUrl);
				}else{
					// 选了分配对象才提示
					if(param.compNo){
						assemblys.msg("当前分配对象所属医院无该应用权限", null, 5000);
					}
					$("#tableBody").empty();
				}
			}
			
		});
		form.render();
	});
	
	// 控制下拉框显示
	if (hasData) {
		$("#appListView").css("display", "");
	} else {
		$("#appListView").css("display", "none");
	}
	return hasData;
}


// 检查当前医院是否有权限
function checkCompApp(){
	var hasResult = false;
	var url = basePath + "/frame/comp/hasCompApp.spring";
	$.ajax({
		type : "post",
		url : url,
		data : {
			"appID" : $("#appList").val(),
			"compNo" : param.compNo
		},
		dataType : "json",
		async : false,
		success : function(data) {
			hasResult = data.hasCompApp;
		}
	});
	return hasResult;
} 

// 计算布局
function computeSize() {
	var tableHeight = 600;
	var height = $(window).height();
	var treeHeight = (height / 100 * 96) + "px";
	// 计算树高度
	$(".treeDiv").css({
		"min-height" : treeHeight,
		"max-height" : treeHeight
	})
	// 计算表格高度
	return tableHeight = height / 100 * 86;
}

function getHtml(url) {
	// 进入系统功能列表
	toUserRightIndex(url);
}

function toUserRightIndex(url) {
	$.ajax({
		type : "post",
		url : url,
		async : false,
		data : {
			"appId" : $("#appList").val(),
			"hasAllRight" : hasAllRight
		},
		dataType : "html",
		success : function(data) {
			theUrl = url;
			$("#topTitle").text("");
			$("#tableBody").html(data);
		},
		error : function(data) {
			assemblys.msg("加载页面失败");
		}
	});
}
