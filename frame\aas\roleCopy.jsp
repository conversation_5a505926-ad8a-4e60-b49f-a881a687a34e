<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<jsp:include page="../page/menuIcon.jsp"><jsp:param value="<%=BaseConstant.FUN_CODE_LIMIT_ROLE%>" name="funCode" /></jsp:include>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html>
<html>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>角色管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/common.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var baseContext = "${basePath}/frame/roleaction/";
	var basePath = "${basePath}";
	var isSubmit = false;
	
	$(function() {
		getCompList();
	});
	
	function toCheck() {
		var checkedNum = $("input:checkBox[name='eventCheckBox']:checked").length;
		var num = $("input:checkBox[name='eventCheckBox']").length;
		if (num == checkedNum) {//选中数等于当前复选框数，即选中全选
			$("#nult").prop("checked", true);
			$("#nult").parent().find(".layui-unselect").addClass("layui-form-checked");
		} else {//否则去掉全选
			$("#nult").prop("checked", false);
			$("#nult").parent().find(".layui-unselect").removeClass("layui-form-checked");
		}
	}

	//单页选中
	function mulitCheckAll() {
		var flag = $("#nult").is(":checked");
		$("input:checkbox[name='eventCheckBox']").each(function() {
			if (flag) {
				$(this).parent().find(".layui-unselect").addClass("layui-form-checked");
			} else {
				$(this).parent().find(".layui-unselect").removeClass("layui-form-checked");
			}
			$(this).prop("checked", flag);
		});
	}

	function saveSelect() {
		var selects = "";
		//复制角色所到的医院
		var forCompNo = $("#forCompNo").val();
		
		// 获取选中的角色
		$("table tr:gt(0)").each(function(i, n) {
			var chebox = $(this).find("td:eq(0)").find("div").attr("class");
			if (chebox && chebox.indexOf("layui-form-checked") != -1) {
				if (i != 0) {
					selects += ",";
				}
				selects += $(this).find("td:eq(0)").find("input").val();
			}
		});
		
		//去除首位的 ‘，’
		if (selects.charAt(0) == ',') {
			selects = selects.substring(1);
		}
		
		//未选择任何模板
		if (selects.length == 0) {
			assemblys.msg("请先选择角色");
			return;
		}
		
		if (recheck(selects, forCompNo)) {
			assemblys.msg("选择中有角色编号重复");
			return;
		}
		
		var url = basePath + "frame/roleaction/saveRoleSelect.spring";
		assemblys.confirm("确定复制?", function() {
			if (!isSubmit) {
				isSubmit = true;
				$.ajax({
					async : false,
					url : url,
					type : "post",
					dataType : "json",
					data : {
						"compNo" : forCompNo,
						"selects" : selects
					},
					success : function(data) {
						if (data.result == "success") {
							assemblys.msg("操作成功", function() {
								assemblys.closeWindow();
								parent.refresh();
							});
						} else {
							assemblys.msg("操作不成功，请联系管理员");
						}
					}
				});
			}
		});
	}

	function recheck(selects, compNo) {
		var flag = false;
		var url = basePath + "frame/roleaction/roleCheck.spring";
		$.ajax({
			async : false,
			url : url,
			type : "post",
			dataType : "json",
			data : {
				compNo : compNo,
				appID : $("#appID").val(),
				selects : selects
			},
			success : function(data) {
				if (data.result == "success") {
					flag = false;
				} else {
					flag = true;
				}
			}
		});
		
		return flag;
		
	}

	function getCompList() {
		$.ajax({
			url : basePath + "frame/common/getMenuIcon.spring",
			data : {
				funCode : "${funCode}",
				hasOrg : true
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var compList = [];
					var forCompNo = $("#forCompNo").val();
					for ( var i in data.data.compList) {
						if (data.data.compList[i].compNo != forCompNo) {
							data.data.compList[i].title = data.data.compList[i].compName;
							data.data.compList[i].id = data.data.compList[i].compNo;
							compList.push(data.data.compList[i]);
						}
					}
					layui.use([ 'tree' ], function() {
						// 树
						var $ = layui.jquery;
						layui.tree.render({
							elem : '#tree',
							click : function(item) {
								$("#compNo").val(item.data.compNo);
								chooseComp();
							},
							data : compList
						});
					});
					
					$("div[data-id='" + $("#compNo").val() + "']").find("div.layui-tree-main").css("background-color", "rgb(190, 190, 190)");
				} else {
					assemblys.alert("获取数据出错，请联系管理员");
				}
			},
			error : function() {
				assemblys.alert("获取数据出错，请联系管理员");
			}
		});
	}
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<!-- 页总数 -->
		<input type="hidden" id="appID" name="appID" value="<c:out value="${appID}"/>">
		<input type="hidden" id="compNo" name="compNo" value="<c:out value="${compNo}"/>">
		<input type="hidden" id="forCompNo" name="forCompNo" value="<c:out value="${forCompNo}"/>">
		<!-- 系统列表 -->
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<div class="head0">
			<div class="head0_right fr">
				<c:if test="${compNo != '0'}">
					<button type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="saveSelect()" value="确定">确定</button>
				</c:if>
			</div>
		</div>
		<div class="bodys">
			<div class="layui-row">
				<div class="treeDiv tree_noSearch">
					<div class="treeHead">医院</div>
					<!-- tree -->
					<ul id="tree" class="tree-table-tree-box layui-box layui-tree"></ul>
				</div>
				<div class="tableDiv table_noSearch">
					<table class="layui-table main_table">
						<!--标题栏-->
						<tr class="main_title">
							<td width="80">
								<input type="checkbox" id="nult" name="nult" lay-filter="nult" lay-skin="primary" value="true" />
							</td>
							<td width="150">角色名称</td>
							<td width="150">角色编号</td>
							<td>备注</td>
							<td width="80">顺序号</td>
						</tr>
						<c:set var="p_iCounts" value="${1}" />
						<c:forEach items="${bean}" var="element" varStatus="vs">
							<c:set var="p_iCounts" value="${p_iCounts + 1}" />
							<c:if test="${vs.index%2 eq 0}">
								<tr class="comTab_R1">
							</c:if>
							<c:if test="${vs.index%2 eq 1}">
								<tr class="comTab_R2">
							</c:if>
							<td align="center" style="width: 20px">
								<input type="checkbox" name="eventCheckBox" id="eventCheckBox" lay-skin="primary" lay-filter="eachone" value="${element.roleId}" onclick="toCheck();" style="margin: 0px; padding: 0px;">
							</td>
							<td align="left">
								<c:out value="${element.roleName}" />
							</td>
							<td align="left">
								<c:out value="${element.roleCode}" />
							</td>
							<td align="left">
								<c:out value="${element.roleDesc}" />
							</td>
							<td align="center">
								<fmt:formatNumber type="number" value="${element.seqNo }" pattern="0.00" />
							</td>
							</tr>
						</c:forEach>
						<c:if test="${p_iCounts == 1}">
							<tr class="comTab_R2">
								<td colspan="5" style="text-align: center;">暂无数据！</td>
							</tr>
						</c:if>
					</table>
					<!-- 分页 -->
					<!-- 当前页 -->
					<input type="hidden" id="curPage" name="curPage" value="${page.intPageCount}">
					<!-- 页总数 -->
					<input type="hidden" id="totalPage" name="appFunsForm" value="${page.intRowCount}">
					<!-- 当前页 -->
					<input type="hidden" id="pageNo" name="curPageNum" value="${page.intPage}">
					<!-- 单页数 -->
					<input type="hidden" id="pageSize" name="pageSize" value="${page.intPageSize}">
					<!-- 分页组件 -->
					<div class="layui-table-page layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
						<div id="layui-table-page1" style="margin: 5px;"></div>
					</div>
				</div>
			</div>
		</div>
	</form>
	<script type="text/javascript">
		layui.use([ 'form', 'laypage', 'layer' ], function() {
			var laypage = layui.laypage;
			var layer = layui.layer;
			var form = layui.form;
			
			laypage.render({
				elem : 'layui-table-page1',
				count : '${page.intRowCount}',
				limit : '${page.intPageSize}',
				curr : '${page.intPage}',
				layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
				jump : function(obj, first) {
					if (!first) {
						document.forms[0].action = baseContext + "copyRole.spring?page=" + obj.curr + "&intPageSize=" + obj.limit;
						document.forms[0].submit();
					}
				}
			});
			form.render("select", "layui-table-page");
			
			form.on("select(compList)", function(data) {
				
				if (!data.value) {
					assemblys.msg("请选择医院");
					return;
				}
				;
				$("#compNo").val(data.value);
				chooseComp();
			});
			
			form.on('checkbox(nult)', function(data) {
				mulitCheckAll();
				return false;
			});
			
			form.on('checkbox(eachone)', function(data) {
				toCheck();
				return false;
			});
			form.render();
		});
		
		//查询医院
		function chooseComp() {
			var compNo = $("#compNo").val();
			var appID = $("#appID").val();
			if (compNo != null || compNo != "") {
				var forCompNo = $("#forCompNo").val();
				var url = baseContext + "copyRole.spring?compNo=" + compNo + "&forCompNo=" + forCompNo + "&appID=" + appID;
				window.location.href = url;
			}
		}
	</script>
</body>
</html>
