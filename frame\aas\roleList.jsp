<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html>
<html>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>角色管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/common.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = basePath + "/frame/roleaction/";
	
	$(function() {
		getAppList();
		getCompList();
	});
	
	//新增
	function newSubmit() {
		var compNo = $("#compNo").val();
		var appID = $("select[name=appID]").val();
		var url = baseContext + "newRole.spring?appID=" + appID + "&compNo=" + compNo;
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '新增角色',
				scrollbar : false,
				area : [ '850px', '420px' ],
				content : url
			});
		});
	}

	//选择模板复制
	function copyRole() {
		var compNo = $("#compNo").val();
		var appID = $("select[name=appID]").val();
		var url = baseContext + "copyRole.spring?appID=" + appID + "&forCompNo=" + compNo;
		var compName = $("div[data-id='" + compNo + "']").find("span.layui-tree-txt").text();
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '从列表中选择角色复制给【' + compName + '】',
				scrollbar : false,
				area : [ '80%', '80%' ],
				content : url
			});
		});
	}

	function refresh() {
		var compNo = $("#compNo").val();
		var url = baseContext + "list.spring?compNo=" + compNo;
		window.location.reload();
		
	}

	//删除一个角色
	function deleteRole(obj) {
		var roleId = $(obj).attr("roleId");
		var roleName = $(obj).attr("roleName");
		assemblys.confirm("确认删除角色「" + roleName + "」吗？", function() {
			var method = "GET";
			var url = baseContext + "deleteRole.spring?roleId=" + roleId;
			var content = null;
			var responseType = "text";
			var callback = delBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.alert("服务器异常，请稍后再试或联系管理员");
				}
			});
		})
	}

	function delBack(responseText) {
		if (responseText == "DEL_OK") {
			assemblys.msg("操作成功", function() {
				location.reload();
			});
		} else {
			assemblys.alert("服务器异常");
		}
	}

	//编辑角色
	function editRole(obj) {
		var roleId = $(obj).attr("roleId");
		var appID = $(obj).attr("appID");
		var compNo = $("#compNo").val();
		var url = baseContext + "editRole.spring?roleId=" + roleId + "&appID=" + appID + "&compNo=" + compNo;
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '编辑角色',
				scrollbar : false,
				area : [ '850px', '420px' ],
				content : url
			});
		});
	}

	//显示用户
	function showUsers(obj) {
		var roleId = $(obj).attr("roleId");
		var roleName = $(obj).attr("roleName");
		var compNo = $("#compNo").val();
		var url_pop = baseContext + "showusers.spring?roleId=" + roleId + "&roleName=" + encodeURIComponent(roleName) + "&compNo=" + compNo;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '查看拥有「' + roleName + '」角色的用户',
			area : [ '90%', '90%' ],
			content : url_pop
		});
	}

	function getAppList() {
		$.ajax({
			type : "post",
			url : basePath + "frame/comp/getCompAppRight.spring",
			data : {
				"menuRight" : 0
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var options = "";
					var appID = $("input[param=appID]").val();
					for ( var i in data.appList) {
						options += "<option value='" + data.appList[i].appID + "' " + (appID == data.appList[i].appID ? "selected" : "") + ">" + data.appList[i].appName + "</option>";
					}
					$("select[name=appID]").append(options);
					layui.form.render("select", "appIDDiv");
					
					layui.form.on("select(appID)", function(data) {
						var url = basePath + "frame/roleaction/list.spring";
						url += "?funCode=" + $("#funCode").val();
						url += "&appID=" + $("#appID").val();
						url += "&compNo=" + $("#compNo").val();
						window.location.href = url;
					});
				} else {
					assemblys.alert("获取数据出错，请联系管理员");
				}
			},
			error : function() {
				assemblys.alert("获取数据出错，请联系管理员");
			}
		});
	}

	function getCompList() {
		$.ajax({
			url : basePath + "frame/common/getMenuIcon.spring",
			data : {
				funCode : "${funCode}",
				hasOrg : true
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					
					var compLength = 0;
					var compList = data.data.compList;
					for ( var i in compList) {
						compList[i].title = compList[i].compName;
						compList[i].id = compList[i].compNo;
						compLength++;
					}
					
					var compNo = $("#compNo").val();
					if (compNo != "") {
						$("#newSubmitButton").removeClass("layui-hide");
						if (compLength >= 2) {
							$("#copyRoleButton").removeClass("layui-hide");
						}
					} else {
						$("#newSubmitButton").addClass("layui-hide");
						$("#copyRoleButton").addClass("layui-hide");
					}
					
					layui.use([ 'tree' ], function() {
						// 树
						var $ = layui.jquery;
						layui.tree.render({
							elem : '#tree',
							click : function(item) {
								
								$("#compNo").val(item.data.compNo);
								var url = basePath + "frame/roleaction/list.spring";
								url += "?funCode=" + $("#funCode").val();
								url += "&appID=" + $("#appID").val();
								url += "&compNo=" + $("#compNo").val();
								window.location.href = url;
								
							},
							data : compList.length > 1 ? [ {
								"title" : "医疗管控平台",
								"spread" : true,
								children : compList
							} ] : compList,
						});
					});
					
					// 选中
					$("div[data-id='" + $("#compNo").val() + "']").find("div.layui-tree-main").css("background-color", "#DCEFE6");
					
					// 图标
					data = data.data;
					var menuIconType = data.menuIcon == "" ? "layui-icon2" : "layui-icon" + data.menuIconType;
					$("b#menuIcon").append('<i class="' + menuIconType + '" >' + (data.menuIcon || "&#xe779;") + '</i>').append('<span> ' + (data.menuName) + '</span>');
					
				} else {
					assemblys.alert("获取数据出错，请联系管理员");
				}
			},
			error : function() {
				assemblys.alert("获取数据出错，请联系管理员");
			}
		});
	}
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<!-- 页总数 -->
		<input type="hidden" param="appID" value="<c:out value='${appID}'/>">
		<input type="hidden" id="compNo" name="compNo" value="<c:out value='${compNo}'/>">
		<input type="hidden" id="funCode" name="funCode" value="<c:out value='${funCode}'/>">
		<!-- 系统列表 -->
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr">
				<label class="fw700">应用系统&nbsp;</label>
				<div class="layui-input-inline h28 lh28 layui-form" lay-filter="appIDDiv" style="margin-top: -4px;">
					<select id="appID" name="appID" lay-filter="appID"></select>
				</div>
				<c:if test="${roleCopy == true}">
					<button type="button" id="copyRoleButton" class="layui-btn layui-btn-sm  skin-btn-minor h28 lh28 layui-hide" onclick="copyRole()">复制</button>
				</c:if>
				<button type="button" id="newSubmitButton" type="button" class="layui-btn layui-btn-sm h28 lh28 layui-hide" onclick="newSubmit()">新增</button>
			</div>
		</div>
		<div class="bodys">
			<div class="layui-row">
				<div class="layui-tab" lay-filter="docDemoTabBrief">
					<label class="layui-form-label2"> 关键字：</label>
					<div class="layui-input-inline h28 lh28">
						<input type="text" id="keyword" class="layui-input" placeholder="角色编号/角色名称" name="keyword" />
					</div>
					<button type="button" onclick="getUserList(1,20);" value="查询" class="layui-btn layui-btn-sm">查询</button>
					<label class="layui-form-label2"> 应用功能 </label>
					<div class="layui-input-inline h28 lh28">
						<select name="funID" lay-filter="funID">
						</select>
					</div>
					<label class="layui-form-label2"> 功能点 </label>
					<div class="layui-input-inline h28 lh28">
						<select name="rpID" lay-filter="rpID">
						</select>
					</div>
					
					<label class="layui-form-label2"> 组织架构 </label>
					<div class="layui-input-inline h28 lh28">
						<select name="orgrights" lay-filter="orgrights">
							<option value="" selected="">无</option>
							<option value="99" >集团</option>
							<option value="12" >本医院</option>
							<option value="11" >本科室</option>
							<option value="-1" >自定义</option>
						</select>
					</div>
					</br>
					<div style="margin-top: 10px;">
						<label class="layui-form-label2"> 自定义条件（医院） </label>
						<div class="layui-input-inline h28 lh28">
							<select name="customWhereCompNo" lay-filter="customWhereCompNo">
							</select>
						</div>
						<label class="layui-form-label2">自定义条件（科室）</label>
						<div class="layui-input-inline h28 lh28">
							<select name="customWhereDeptID" lay-filter="customWhereDeptID">
								<option value="4" selected="">无</option>
							</select>
						</div>
					</div>
				</div>
					<div class="treeDiv" style="top: 90px;">
						<div class="treeHead">目录树</div>
						<!-- tree -->
						<ul id="tree" class="tree-table-tree-box layui-box layui-tree"></ul>
					</div>
					<div class="tableDiv" style="top: 85px;">
						<table class="layui-table main_table">
							<!--标题栏-->
							<tr class="main_title">
								<td width="120">操作</td>
								<td width="250">角色编号</td>
								<td width="250">角色名称</td>
								<td>备注</td>
								<td>组织架构</td>
								<td width="80">顺序号</td>
							</tr>
							<c:set var="p_iCounts" value="${1}" />
							<c:forEach items="${bean}" var="element" varStatus="vs">
								<c:set var="p_iCounts" value="${p_iCounts + 1}" />
								<c:if test="${vs.index%2 eq 0}">
									<tr class="comTab_R1">
								</c:if>
								<c:if test="${vs.index%2 eq 1}">
									<tr class="comTab_R2">
								</c:if>
								<td align="center">
									<i class="layui-icon layui-icon-edit i_check" title="编辑" roleId="<c:out value="${element.roleId}"/>" appID="<c:out value="${element.appID}"/>" onclick="editRole(this)"></i>
									<i class="layui-icon layui-icon-delete i_delete" title="删除" roleId="<c:out value="${element.roleId}"/>" roleName="<c:out value="${element.roleName}"/>" onclick="deleteRole(this)"></i>
									<i class="layui-icon layui-icon-search i_check" title="查询用户" roleId="<c:out value="${element.roleId}"/>" roleName="<c:out value="${element.roleName}"/>" onclick="showUsers(this)"></i>
									<i class="layui-icon layui-icon-group i_check" title="复制权限" userid="41746" username="邵敏111" compno="CF148" onclick="cloneRight(this)"></i>
								</td>
								<td align="left">
									<c:out value="${element.roleCode}" />
								</td>
								<td align="left">
									<c:out value="${element.roleName}" />
								</td>
								<td align="left">
									<c:out value="${element.roleDesc}" />
								</td>
								<td align="left">本医院</td>
								<td align="center">
									<fmt:formatNumber type="number" value="${element.seqNo }" pattern="0.00" />
								</td>
								</tr>
							</c:forEach>
							<c:if test="${p_iCounts == 1}">
								<tr class="comTab_R2">
									<td colspan="5" style="text-align: center;">暂无数据！</td>
								</tr>
							</c:if>
						</table>
						<!-- 分页组件 -->
						<div class="layui-table-page layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
							<div id="layui-table-page1" style="margin: 5px;"></div>
						</div>
					</div>
			</div>
		</div>
	</form>
	<script type="text/javascript">
		layui.use([ 'form', 'laypage', 'layer' ], function() {
			var laypage = layui.laypage;
			var layer = layui.layer;
			var form = layui.form;
			
			laypage.render({
				elem : 'layui-table-page1',
				count : '${page.intRowCount}',
				limit : '${page.intPageSize}',
				curr : '${page.intPage}',
				layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
				jump : function(obj, first) {
					if (!first) {
						document.forms[0].action = baseContext + "list.spring?page=" + obj.curr + "&intPageSize=" + obj.limit;
						document.forms[0].submit();
					}
				}
			});
			form.render("select", "layui-table-page");
			
			form.render();
		});
	</script>
</body>
</html>
<script type="text/javascript" src="${basePath}/frame/aas/js/roleList.js"></script>