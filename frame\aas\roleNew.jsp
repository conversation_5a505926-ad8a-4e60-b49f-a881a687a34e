<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>新增角色</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}/frame/roleaction/";
	
	$(function() {
		getAppList($("#compNo").val());
		$("#roleCode").focus();
		initTips();
		layui.form.render();
	});
	
	function initTips () {
		$("i[whetherUpdateTips]").mouseenter(function(e) {
			assemblys.tips(this, "同步科室人员和导入科室人员数据时，新增用户时分配的默认角色", 0, "bottom");
		});
	};
	
	//保存
	function saveSubmit() {
		var roleName = $.trim(document.getElementById("roleName").value);
		var roleCode = $.trim(document.getElementById("roleCode").value);
		var remark = $.trim(document.getElementById("remark").value);
		var roleType = $.trim(document.getElementById("roleTypeList").value);
		var isAssignable = $.trim(document.getElementById("isAssignable").value);
		var isSubAdmin = $.trim(document.getElementById("isSubAdministrator").value);
		var compNo = $.trim(document.getElementById("compNo").value);
		var seqNo = $.trim(document.getElementById("seqNo").value);
		var appID = $("select[name=appID]").val();
		var isDefault =  $("input[name=isDefault]:checked").val();
		if (isNaN(seqNo)) {
			assemblys.msg("顺序号不正确，请重新输入");
			document.getElementById("seqNo").focus();
			return;
		}
		var pars = "roleName=" + encodeURIComponent(roleName) + "&roleCode=" + encodeURIComponent(roleCode) + "&remark=" + encodeURIComponent(remark) + "&roleType=" + roleType + "&isAssignable=" + isAssignable + "&isSubAdministrator=" + isSubAdmin + "&seqNo=" + seqNo + "&compNo=" + compNo + "&appID=" + appID + "&isDefault=" + isDefault;
		var url = baseContext + "saveRole.spring?1=1";
		$.ajax({
			"url" : url,
			"data" : pars,
			"success" : saveBack,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}

	function saveBack(doc) {
		var status = doc.getElementsByTagName("status")[0].childNodes[0].nodeValue;
		var temp = status.split(";");
		if (temp[0] == "ROLECODE_HAS") {
			assemblys.msg("角色编号已经存在");
			document.getElementById("roleCode").select();
		} else if (temp[0] == "ROLENAME_HAS") {
			assemblys.msg("角色名称已经存在");
			document.getElementById("roleName").select();
		} else if (temp[0] == "SAVE_OK") {
			document.getElementById("roleId").value = temp[1];
			assemblys.msg("保存成功", function() {
				parent.roleList.findData();
				assemblys.closeWindow();
			});
		} else {
			assemblys.alert("新增出错，请检查服务器是否正常运行");
		}
	}

	function setRole(appId, appName) {
		var roleId = document.getElementById("roleId").value;
		var roleName = document.getElementById("roleName").value;
		var change = "YES";
		var method = "GET";
		var url = baseContext + "addAppRole.spring?roleId=" + roleId + "&appId=" + appId + "&change=" + change;
		var content = null;
		var responseType = "text";
		$.ajax({
			"url" : url,
			"type" : method,
			"data" : content,
			"dataType" : responseType,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}

	function getAppList(compNo) {
		$.ajax({
			type : "post",
			url : basePath + "/frame/comp/getCompAppRight.spring",
			dataType : "json",
			data : {
				"menuRight" : 0
			},
			success : function(data) {
				if (data.result == "success") {
					var options = "";
					var appID = $("input[param=appID]").val();
					for ( var i in data.appList) {
						options += "<option value='" + data.appList[i].appID + "' " + (appID == data.appList[i].appID ? "selected" : "") + ">" + data.appList[i].appName + "</option>";
					}
					$("select[name=appID]").append(options);
					layui.form.render("select", "appIDDiv");
				} else {
					assemblys.alert("获取数据出错，请联系管理员");
				}
			},
			error : function() {
				assemblys.alert("获取数据出错，请联系管理员");
			}
		});
	}
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<input id="roleId" name="roleId" type="hidden" value=''>
		<input type="hidden" name="isAssignable" id="isAssignable" value="0">
		<input type="hidden" name="isSubAdministrator" id="isSubAdministrator" value="0">
		<input param="appID" type="hidden" value='<c:out value="${appID}"/>'>
		<input id="compNo" name="compNo" type="hidden" value='<c:out value="${compNo}"/>'>
		<div class="bodys bodys_noTop ">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					角色编号
				</label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" class="comEdit_Input" id="roleCode" name="roleCode" lay-verify="required|limit|character" limit="50" placeholder="请输入角色编号" autocomplete="off" maxlength="50" />
				</div>
				<label class="layui-form-label">
					<span style="color: red">*</span>
					角色名称
				</label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" class="comEdit_Input" id="roleName" name="roleName" lay-verify="required|limit|character" limit="50" placeholder="请输入角色名称" autocomplete="off" maxlength="50" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					应用
				</label>
				<div class="layui-input-inline layui-form" lay-filter="appIDDiv">
					<select name="appID" lay-verify="required" lay-filter="appID" disabled></select>
				</div>
				<label class="layui-form-label">
					<span style="color: red">*</span>
					默认角色 <i class="layui-icon2" whetherUpdateTips>&#xe997;</i>
				</label>
				<div class="layui-input-inline">
					<input type="radio" name="isDefault" value="0" title="否" checked/>
					<input type="radio" name="isDefault" value="1" title="是" <c:if test="${cancelState==1}">checked</c:if>/>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					顺序号
				</label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" lay-verify="required|limit|float" limit="5" value="1.00" maxlength="5" id="seqNo" name="seqNo" />
				</div>
				<input type="hidden" id="roleTypeList" name="roleTypeList" value="1">
			</div>	
			<div class="layui-form-item">
				<label class="layui-form-label"> 备注</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" id="remark" name="remark" rows="2" cols="50" maxlength="200" lay-verify="limit|character" limit="200"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			float : function(value, item) {
				if (parseFloat(value) > 99999.9999 || parseFloat(value) <= 0) {
					return "顺序号必须大于0且小于100000";
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		form.on("submit(save)", function(data) {
			saveSubmit();
		});
	});
</script>
</html>
