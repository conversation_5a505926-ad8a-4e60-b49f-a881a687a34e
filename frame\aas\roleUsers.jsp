<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>角色管理-角色的用户</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}/frame/roleaction/";
	
	$(function() {
		init();
	});
	
	//初始化
	function init() {
		var ua = navigator.userAgent.toLowerCase();
		if (ua.indexOf("dingtalk-win") > -1) {//钉钉浏览器
			document.getElementById("closeBtn").style.display = "inline";
		}
	}
</script>
</head>
<body class="body_noTop">
	<form action="" method="post" class="layui-form">
		<input type="hidden" id="curPage" name="curPage" value="<c:out value="${page.intPage}"/>">
		<!-- 当前页 -->
		<input type="hidden" id="totalPage" name="totalPage" value="<c:out value="${page.intPageCount}"/>">
		<!-- 页总数 -->
		<input type="hidden" id="roleId" name="roleId" value="<c:out value="${roleId}"/>">
		<!-- roleId -->
		<input type="hidden" id="compNo" name="compNo" value="<c:out value="${compNo}"/>">
		<!-- compNo -->
		<input type="hidden" id="roleName" name="roleName" value="<c:out value="${roleName}"/>">
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<div class="head0">
			<div class="head0_right fr">
				<div class="layui-input-inline">
					<input type="checkbox" title="只显示停用用户" lay-filter="tp" name="tp" value="OUT" lay-skin="primary" <c:if test="${tp == 'OUT' }">checked="checked"</c:if> />
				</div>
				<button type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="newSubmit()" value="分配">分配</button>
			</div>
		</div>
		<div class="bodys">
			<div class="tableDiv table_noTree table_noSearch">
				<table class="layui-table main_table" style="margin-bottom: 0;" cellpadding="0" cellspacing="0">
					<!--标题栏-->
					<tr class="main_title">
						<td width="80">操作</td>
						<td width="20%">用户编号</td>
						<td>用户名称</td>
						<td>医院名称</td>
						<td width="20%">科室名称</td>
					</tr>
					<c:set var="p_iCounts" value="${1}" />
					<c:forEach items="${users}" var="element" varStatus="vs">
						<c:set var="p_iCounts" value="${p_iCounts + 1}" />
						<c:if test="${vs.index%2 eq 0}">
							<tr class="comTab_R1">
						</c:if>
						<c:if test="${vs.index%2 eq 1}">
							<tr class="comTab_R2">
						</c:if>
						<td align="center">
							<i class="layui-icon layui-icon-delete i_delete" title="删除" userCode="<c:out value="${element.userCode}"/>" onclick="deleteRole(this)"></i>
						</td>
						<td class="comTab_Td" align="center">
							<c:out value="${element.userCode}" />
						</td>
						<td class="comTab_Td" align="left">
							<c:out value="${element.userName}" />
						</td>
						<td class="comTab_Td" align="center">
							<c:out value="${element.compName}" />
						</td>
						<td class="comTab_Td" align="center">
							<c:out value="${element.deptName}" />
						</td>
						</tr>
					</c:forEach>
					<c:if test="${p_iCounts == 1}">
						<tr class="comTab_R2">
							<td colspan="5" style="text-align: center;">暂无数据！</td>
						</tr>
					</c:if>
				</table>
				<!-- 分页组件 -->
				<div class="layui-table-page layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
					<div id="layui-table-page1" style="margin: 5px;"></div>
				</div>
				<!--说明-->
				<div class="comTab_Sn"></div>
			</div>
		</div>
	</form>
	<script type="text/javascript">
		layui.use([ 'form', 'laypage' ], function() {
			var form = layui.form;
			var laypage = layui.laypage;
			laypage.render({
				elem : 'layui-table-page1',
				count : '${page.intRowCount}',
				limit : '${page.intPageSize}',
				curr : '${page.intPage}',
				layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count', ],
				jump : function(obj, first) {
					if (!first) {
						var roleId = document.getElementById("roleId").value;
						var compNo = document.getElementById("compNo").value;
						var roleName = document.getElementById("roleName").value;
						document.forms[0].action = baseContext + "showusers.spring?page=" + obj.curr + "&intPageSize=" + obj.limit + "&roleId=" + roleId + "&compNo=" + compNo + "&roleName=" + encodeURIComponent(roleName);
						document.forms[0].submit();
					}
				}
			});
			form.render("select", "layui-table-page");
			
			form.on("checkbox(tp)", function(data) {
				document.forms[0].submit();
			});
			form.render();
		})

		function newSubmit() {
			var compNo = $("#compNo").val();
			layer.open({
				type : 2,
				title : "分配给",
				scrollbar : false,
				area : [ '800px', '80%' ],
				content : basePath + "plugins/udSelector/selectUserPage.jsp?compNo=" + compNo + "&type=user&callback=saveCallback"
			});
			
		}

		function saveCallback(list) {
			// 存在数据
			if (list.length > 0) {
				// 取值
				var userCodes = new Array();
				for ( var index in list) {
					userCodes.push(list[index].userCode);
				}
				var userCode = userCodes.join(";");
				var compNo = $("#compNo").val();
				var state = 1;
				var roleId = document.getElementById("roleId").value;
				url = basePath + "/frame/roleaction/userAction.spring?compNo=" + compNo + "&userCode=" + userCode + "&roleId=" + roleId + "&state=" + state;
				if (userCode != "" && userCode != null) {
					$.ajax({
						url : url,
						type : 'post',
						dataType : 'json',
						async : false,
						success : function(data) {
							if (data.result == "success") {
								assemblys.msg("分配成功", function() {
									document.forms[0].submit();
								});
							} else {
								assemblys.msg("新增失败");
							}
						}
					})
				}
			}
		}

		function deleteRole(obj) {
			var userCode = $(obj).attr("userCode");
			var compNo = $("#compNo").val();
			var state = 0;
			var roleId = document.getElementById("roleId").value;
			var url = basePath + "/frame/roleaction/userAction.spring?compNo=" + compNo + "&roleId=" + roleId + "&userCode=" + userCode + "&state=" + state;
			assemblys.confirm("确定删除该角色的用户吗？", function() {
				$.ajax({
					url : url,
					type : 'post',
					async : false,
					dataType : 'json',
					success : function(data) {
						if (data.result == "success") {
							assemblys.msg("删除成功", function() {
								document.forms[0].submit();
							})
						} else {
							assemblys.msg("删除失败");
						}
					}
				})
			});
		}
	</script>
</body>
</html>
