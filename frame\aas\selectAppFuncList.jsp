<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>复制组织架构</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css">
<script type="text/javascript">
	var basePath = "${basePath}";
	var hasSubmit = false;
</script>
</head>
<body class="body">
	<form class="layui-form" id="form1" action="">
		<input type="hidden" id="appID" value="${param.appID}">
		<input type="hidden" id="userID" value="${param.userID}">
		<input type="hidden" id="roleID" value="${param.roleID}">
		<input type="hidden" id="formFunID" value="${param.formFunID}">
		<input type="hidden" id="hasAllRight" value="${param.hasAllRight}">
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2">&#xe779;</i>
				组织架构克隆
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="selectAppFuncList.save();" value="确定克隆" />
			</div>
		</div>
		<div class="bodys">
			<div class="tableDiv table_noTree table_noSearch">
				<table class="layui-table main_table" cellpadding="0" cellspacing="0">
					<thead>
						<tr id="tr_template" class="layui-hide">
							<td align="center">
								<input type="checkbox" name="funID" lay-filter="fundID" lay-skin="primary">
							</td>
							<td align="left"></td>
							<td align="center"></td>
							<td align="center"></td>
							<td align="center"></td>
							<td align="center"></td>
							<td align="center"></td>
							<td align="center"></td>
							<td align="left"></td>
						</tr>
						<tr class="main_title">
							<td width="50">
								<input type="checkbox" id="funIDALL" lay-filter="funIDALL" lay-skin="primary">
							</td>
							<td width="320">子系统/功能</td>
							<td>浏览</td>
							<td>新增</td>
							<td>编辑</td>
							<td>删除</td>
							<td>执行</td>
							<td>监督</td>
							<td>描述</td>
						</tr>
					</thead>
					<tbody id="tableView">
					</tbody>
				</table>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}/frame/aas/js/selectAppFuncList.js?v=1.01"></script>