<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<!DOCTYPE html>
<html>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	String rootPath = application.getRealPath("/").replace("\\", "\\\\");
	request.setAttribute("basePath", basePath);
%>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=GBK">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>用户管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}/frame/useraction/";
	var isSubmit = false;
	$(document).ready(function() {
		
		assemblys.getMenuIcon({
			funCode : assemblys.getParam("funCode"),
			hasOrg : false,
			dom : $("b#menuIcon")
		});
		
		getDict().then(function(){
			return initRight();
		}).then(function() {
			getUserList(1, 20);
		});
	});
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<input type="hidden" id="compNo" name="CompNo" value="<c:out value='${CompNo}'/>">
		<!-- 医院ID -->
		<input type="hidden" id="deptId" name="DeptID" value="<c:out value='${DeptID}'/>">
		<!-- 科室ID -->
		<input type="hidden" id="compName" name="CompName" value="<c:out value='${CompName}'/>">
		<input type="hidden" id="deptName" name="DeptName" value="<c:out value='${DeptName}'/>">
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<div class="layui-layer-shade loading layui-hide" style="z-index: 88888; background-color: rgb(57, 61, 73); opacity: 0.8;">
		</div>
		<div class="layui-layer layui-layer-dialog layui-layer-msg layui-layer-hui loading layui-hide" style="z-index: 99999;position: absolute;left: 48%; top:48% ">
			<div class="layui-layer-content">正在导出数据</div>
		</div>
		
		<!-- 是否重复 -->
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr" id="hide" style="display: none">
				<button type="button" onclick="newSubmit()" value="新增" class="layui-btn layui-btn-sm">新增</button>
			</div>
		</div>
		<div class="bodys">
			<div class="layui-tab" lay-filter="docDemoTabBrief" style="width: 100%; line-height: 28px;">
				<label class="layui-form-label2"> 关键字：</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" id="serchWhere" class="layui-input" placeholder="编号/名称" name="serchWhere" onkeydown="if(event.keyCode===13) getUserList(1,20);"/>
				</div>
				<button type="button" onclick="getUserList(1,20);" value="查询" class="layui-btn layui-btn-sm">查询</button>
				<label class="layui-form-label2">筛选：</label>
				<input type="radio" lay-skin="primary" lay-filter="showOut" name="showOut" checked="checked" value="0" title="无" />
				<input type="radio" lay-skin="primary" lay-filter="showOut" name="showOut" value="1" title="只显示停用" />
				<span id="showContainer" class="layui-hide">
					<input type="radio" lay-skin="primary" lay-filter="showOut" name="showOut" value="2" title="只显示锁定" />
				</span>
				<div class="fr">
						<div class="inblock filter_item fr" onclick="syncUserPinYin();">
							<i class="layui-icon2" style="font-size: 16px;" title="同步人员拼音">&#xea52;</i>
							同步人员拼音
						</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="treeDiv">
					<div class="treeHead">目录树</div>
					<!-- tree -->
					<ul id="tree" class="tree-table-tree-box layui-box layui-tree">
					</ul>
				</div>
				<div class="tableDiv"></div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/aas/js/userFrame.js?ver=*******"></script>
