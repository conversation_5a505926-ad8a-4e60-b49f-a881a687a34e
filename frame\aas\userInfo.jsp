<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@page import="org.hyena.frame.Globals,org.hyena.frame.view.User"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	String ws_basePath = "ws://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
	User user = (User) request.getSession().getAttribute(Globals.KEY_USER);
	request.setAttribute("basePath", basePath);
	request.setAttribute("ws_basePath", ws_basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-Control" content="no-cache,must-revalidate">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta http-equiv="Expires" content="0">
<title>个人信息</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/modules/laydate/default/laydate.css">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/event-audit.css">
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script>
	var basePath = "${basePath}";
	var ws_basePath = "${ws_basePath}";
	var compNo = "${compNo}";
	var userCode = "${userCode}";
	var wxOpenID = "${wxOpenID}";
	var wxUserID = "${wxUserID}";
	var ddUserID = "${ddUserID}";
	var fsUserID = "${fsUserID}";
	var errorMsg = "${errorMsg}";
	
	$(function() {
		
		getSafeRule();
		
		if (errorMsg == "bindError") {
			assemblys.alert("绑定失败，该用户不在此组织架构当中");
		}
		
	})

	//修改用户资料....
	function editUser() {
		var userId = "${userId}";
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '编辑用户',
			area : [ '870px', '80%' ],
			content : basePath + "frame/useraction/editUser.spring?userId=" + userId + "&type=1",
			end : function() {
				location.reload();
			}
		});
	}

	function getSafeRule() {
		// 返回JSON形式
		$.ajax({
			url : basePath + "frame/dict/getDictByCode.spring",
			type : "get",
			data : {
				"dictCode" : "loginMode",
				"appCode" : "APP"
			},
			dataType : "json",
			success : function(data) {
				if (data.dict && data.dict.length > 0) {
					var temp = data.dict[0];
					// 加个 true ，默认不做登录扫码开关控制，如果要加上，就把true去掉  -- caijun.pan
					if (temp && temp["dictContent"].indexOf("3") != -1 || true) {
						$(".scanQRCodeLogin").removeClass("layui-hide");
					}
				}
			}
		});
		
	}
</script>
<style type="text/css">
.QR_window {
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 9999;
	display: none;
}

.QR_window_show {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	opacity: 0.3;
	background-color: rgb(0, 0, 0);
	filter: alpha(opacity = 30);
	BACKGROUND-COLOR: #000;
	zoom: 1;
	display: none;
	BACKGROUND-COLOR: #000;
	zoom: 1;
}

#QRImageGZHDiv {
	position: fixed;
	inset: 0px;
	margin: auto;
	width: 300px;
	font-size: 50px;
	background: #fff;
	height: 350px;
	top: 0px;
	bottom: 0px;
	left: 0px;
	right: 0px;
	display: none;
}

#QRImageGZHDiv font {
	text-align: center;
	width: 100%;
	font-size: 13px;
	height: 40px;
	line-height: 40px;
	bottom: 0px;
	position: absolute;
	color: #737C84;
	margin: 0 auto;
}

.QR_delete {
	font-size: 58px;
	color: #ffffff;
	position: absolute;
	margin-left: 213px;
	margin-bottom: 215px;
	left: 50%;
	bottom: 50%;
	cursor: pointer;
}

.layui-table img {
	max-width: 400px;
}

.QR_CODE_CONTENT {
	text-align: left;
}

.QR_CODE_CONTENT span {
	margin: 0px 5px;
}

.QR_CODE_CONTENT img {
	width: 60px;
	cursor: pointer;
}

.QR_help {
	color: #A0A0A0;
	font-size: 15px;
	cursor: help;
	line-height: 18px;
}

.tright {
	white-space: normal;
	text-align: right;
	padding: 7px 12px 7px 12px;
}

.tleft {
	white-space: normal;
	text-align: left;
	padding: 7px 12px 7px 12px;
}
</style>
</head>
<body>
	<div class="layui-tab-content lr_box">
		<div class="bodys">
			<ul class="layui-nav layui-nav-tree left" lay-filter="test">
				<li class="layui-nav-item layui-nav-itemed subject eventDetail" id="patientInfo">
					<a class="main_table_title">
						个人信息
						<button type="button" style="position: absolute; right: 5px; top: 4px; color: white;" onclick="editUser()" value="编辑" class="layui-btn layui-btn-sm">编辑</button>
					</a>
					<dl class="layui-nav-child main_table_box">
						<dd>
							<table class="layui-table main_table detail_table">
								<tbody>
									<tr>
										<td class="tright">所属医院</td>
										<td class="tleft">
											<c:out value="${compName }" />
										</td>
										<td class="tright">所属科室</td>
										<td class="tleft">
											<c:out value="${deptName }" />
										</td>
									</tr>
									<tr>
										<td class="tright">用户编号</td>
										<td class="tleft">
											<c:out value="${userCode }" />
										</td>
										<td class="tright">用户名称</td>
										<td class="tleft">
											<c:out value="${userName }" />
										</td>
									</tr>
									<tr>
										<td class="tright">拼音缩写</td>
										<td class="tleft">
											<c:out value="${pinyin }" />
										</td>
										<td class="tright">顺序号</td>
										<td class="tleft">
											<c:out value="${sequence }" />
										</td>
									</tr>
									<tr>
										<td class="tright">性 别</td>
										<td class="tleft">
											<c:choose>
												<c:when test="${sex == '0' }">
													男
												</c:when>
												<c:when test="${sex == '1' }">
													女
												</c:when>
												<c:when test="${sex == '2' }">
													未知
												</c:when>
											</c:choose>
										</td>
										<td class="tright">本人学历</td>
										<td class="tleft">
											<c:out value="${education }" />
										</td>
									</tr>
									<tr>
										<td class="tright">手机号码</td>
										<td class="tleft">
											<c:out value="${tel }" />
										</td>
										<td class="tright">出生日期</td>
										<td class="tleft">${fn:substring(birthday, 0, 10)}</td>
									</tr>
									<tr>
										<td class="tright">电子邮箱</td>
										<td class="tleft" colspan="3">
											<c:out value="${email }" />
										</td>
									</tr>
									<tr>
										<td class="tright">职称</td>
										<td class="tleft">
											<c:out value="${title }" />
										</td>
										<td class="tright">身份类别</td>
										<td class="tleft">
											<c:out value="${identityClass }" />
										</td>
									</tr>
									<tr>
										<td class="tright">行政主管人员</td>
										<td class="tleft">
											<c:out value="${isManager eq '0' ? '未知' : '' }" />
											<c:out value="${isManager eq '1' ? '是' : '' }" />
											<c:out value="${isManager eq '2' ? '否' : '' }" />
										</td>
										<td class="tright">入职时间</td>
										<td class="tleft">${fn:substring(entryDate, 0, 10)}</td>
									</tr>
									<tr>
										<td class="tright">现任职务</td>
										<td class="tleft">
											<c:out value="${currentDuty }" />
										</td>
										<td class="tright">兼任行政职务</td>
										<td class="tleft">
											<c:out value="${otherDuty }" />
										</td>
									</tr>
									<tr>
										<td class="tright">执业证书编号</td>
										<td class="tleft">
											<c:out value="${certificateNo }" />
										</td>
										<td class="tright">首次注册执业时间</td>
										<td class="tleft">${fn:substring(firstCertificateTime, 0, 10)}</td>
									</tr>
									<tr id="binding_tr" class="scanQRCodeLogin layui-hide">
										<td class="tright">
											扫码绑定
											<i class="layui-icon2 QR_help" onmouseover="assemblys.tips(this,'如果二维码加载失败，请尝试更换谷歌或更高版本浏览器',3000,'right')">&#xe725; </i>
										</td>
										<td colspan="3" class="QR_CODE_CONTENT">
											<span id="MOBLIE_DINGDING" class="QR_code  layui-hide">
												<img src="${basePath}frame/login/images/code12.png" loginKey="ddUserID" title="未绑定" />
											</span>
											<span id="MOBLIE_QIYEWENXIN" class="QR_code  layui-hide ">
												<img src="${basePath}frame/login/images/code22.png" loginKey="wxUserID" title="未绑定" />
											</span>
											<span id="MOBLIE_WECHAT" class="QR_code  layui-hide">
												<img src="${basePath}frame/login/images/code32.png" loginKey="wxOpenID" title="未绑定" />
											</span>
											<span id="MOBLIE_FEISHU" class="QR_code  layui-hide">
												<img src="${basePath}frame/login/images/code42.png" loginKey="fsUserID" title="未绑定" />
											</span>
											<div class="QR_window">
												<div class="QR_window_show"></div>
												<div id="QRImage" style="display: none;"></div>
												<div id="QRImageGZHDiv">
													<img id="QRImageGZH">
													<font>请使用"微信"扫描二维码登录"公众号"</font>
												</div>
												<i class="layui-icon layui-icon-close QR_delete"></i>
												<div class="ruler"></div>
											</div>
										</td>
									</tr>
								</tbody>
							</table>
						</dd>
					</dl>
				</li>
			</ul>
		</div>
	</div>
</body>
<script type="text/javascript" src="${basePath}frame/loginUtil/ddLogin.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/wwLogin-1.0.0.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/ddAndWxUtil.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/wxGZHUtil.js"></script>
<script type="text/javascript" src="${basePath}frame/aas/js/scanQRCodeBind.js"></script>
</html>
