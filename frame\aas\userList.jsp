<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	String rootPath = application.getRealPath("/").replace("\\", "\\\\");
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE html>
<html>
<table class="layui-table main_table">
	<!--标题栏-->
	<tr class="main_title">
		<td width="120">操作</td>
		<td class="list-relation layui-hide" width="100">关联 <i class="layui-icon2">&#xe890;</i></td>
		<td width="120">编号</td>
		<td width="120">名称</td>
		<td width="200">所属科室</td>
		<td width="120">所属医院</td>
		<td>所属角色</td>
	</tr>
	<tr>
		<c:set var="p_iCounts" value="${1}" />
		<c:forEach items="${bean}" var="element" varStatus="vs">
			<c:set var="p_iCounts" value="${p_iCounts + 1}" />
			<c:if test="${vs.index%2 eq 0}">
				<tr class="comTab_R1">
			</c:if>
			<c:if test="${vs.index%2 eq 1}">
				<tr class="comTab_R2">
			</c:if>
			<td align="center">
				<i class="layui-icon layui-icon-friends i_check" title="角色分配" userId="<c:out value="${element.userId}"/>" userName="<c:out value="${element.userName}"/>" compNo="<c:out value="${element.compNo}"/>" onclick="editRole(this)"></i>
				<c:choose>
					<c:when test="${!empty element.pauseDate}">
						<i class="layui-icon layui-icon-play i_check" title="启用" userId="<c:out value="${element.userId}"/>" userName="<c:out value="${element.userName}"/>"  opt="1"  onclick="cancelOpen(this)"></i>
					</c:when>
					<c:otherwise>
						<i class="layui-icon layui-icon-pause i_check" title="停用" userId="<c:out value="${element.userId}"/>" userName="<c:out value="${element.userName}"/>" opt="0"  onclick="cancelOpen(this)"></i>
					</c:otherwise>
				</c:choose>
				<i class="layui-icon2 i_check" style="font-size: 12px;" title="修改密码" userId="<c:out value="${element.userId}"/>" userName="<c:out value="${element.userName}"/>" compNo="<c:out value="${element.compNo}"/>" onclick="editPwd(this)">&#xe6ad;</i>
				<i class="layui-icon layui-icon-group i_check" title="复制权限" userId="<c:out value="${element.userId}"/>" userName="<c:out value="${element.userName}"/>" compNo="<c:out value="${element.compNo}"/>" onclick="cloneRight(this)"></i>
				<c:if test="${showOut == 2 }">
					<i class="layui-icon2 i_check" style="font-size: 12px;" title="解锁用户" userId="<c:out value="${element.userId}"/>" userName="<c:out value="${element.userName}"/>" onclick="enableUser(this)">&#xeafb; </i>
				</c:if>
			</td>
			<td class="list-relation layui-hide" align="center">
				<c:if test="${!empty element.ddUserID}">
					<img src="${basePath}frame/login/images/code1.png" loginKey="ddUserID" loginName="钉钉" userCode="<c:out value="${element.userCode}" />" userName="<c:out value="${element.userName}" />" compNo="<c:out value="${element.compNo}" />" style="width: 20px;" onclick="unbindUser(this)">
				</c:if>
				<c:if test="${!empty element.wxUserID}">
					<img src="${basePath}frame/login/images/code2.png" loginKey="wxUserID" loginName="企业微信" userCode="<c:out value="${element.userCode}" />" userName="<c:out value="${element.userName}" />" compNo="<c:out value="${element.compNo}" />" style="width: 20px;" onclick="unbindUser(this)">
				</c:if>
				<c:if test="${!empty element.wxOpenID}">
					<img src="${basePath}frame/login/images/code3.png" loginKey="wxOpenID" loginName="公众号" userCode="<c:out value="${element.userCode}" />" userName="<c:out value="${element.userName}" />" compNo="<c:out value="${element.compNo}" />" style="width: 20px;" onclick="unbindUser(this)">
				</c:if>
				<c:if test="${!empty element.fsUserID}">
					<img src="${basePath}frame/login/images/code4.png" loginKey="fsUserID" loginName="飞书" userCode="<c:out value="${element.userCode}" />" userName="<c:out value="${element.userName}" />" compNo="<c:out value="${element.compNo}" />" style="width: 20px;" onclick="unbindUser(this)">
				</c:if>
			</td>
			<td align="center">
				<a class="layui-a-hasClick" style="text-decoration: underline;" userId="<c:out value="${element.userId}"/>" userName="<c:out value="${element.userName}"/>" onclick="editUser(this);" title="编辑用户资料">
					<c:out value="${element.userCode}" />
				</a>
			</td>
			<td align="center">
				<c:out value="${element.userName}" />
			</td>
			<td align="left">
				<c:out value="${element.deptName}" />
			</td>
			<td align="center">
				<c:out value="${element.compName}" />
			</td>
			<td align="left">
				<c:out value="${element.userRoleStr}" />
			</td>
		</c:forEach>
		<c:if test="${p_iCounts==1 }">
			<td colspan="6" align="center">暂无数据！</td>
		</c:if>
	</tr>
</table>
<!-- 分页组件 -->
<div class="layui-table-page layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
	<div id="layui-table-page1" style="margin: 5px;"></div>
</div>
</html>
<script type="text/javascript">
	layui.use([ 'form', 'laypage', 'layer' ], function() {
		var layer = layui.layer;
		var laypage = layui.laypage;
		var form = layui.form;
		laypage.render({
			elem : 'layui-table-page1',
			count : '${page.intRowCount}',
			limit : '${page.intPageSize}',
			curr : '${page.intPage}',
			layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
			jump : function(obj, first) {
				if (!first) {
					getUserList(obj.curr, obj.limit);
				}
			}
		});
		form.render("select", "layui-table-page");
	});
</script>