<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>新增用户</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<link rel="stylesheet" href="${basePath}plugins/formSelects/formSelects-v4.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/formSelects/formSelects-v4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var baseContext = "${basePath}/frame/useraction/";
	var basePath = "${basePath}";
	var compNo = "";
	var deptID = "";
	
	$(function() {
		initNew();
		initTips();
		layui.form.render();
	});
	
	function initTips(){
		$("i[whetherUpdateTips]").mouseenter(function(e) {
			assemblys.tips(this, "同步数据时，是否更新当前用户。是：更新，否：不更新。", 0, "bottom");
		});
	}
	
	//初始化
	function initNew() {
		
		compNo = $("input[param='compNo']").val();
		deptID = $("input[param='deptID']").val();
		
		getDict().then(function() {
			initDept(compNo);
			document.getElementById("userCode").focus();
		});
	}

	function getDict() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictByCode.spring",
			type : "get",
			data : {
				"dictCode" : "PasswordDefaultConfig",
				"appCode" : "APP",
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					if (data.dict && data.dict[0].dictContent) {
						$("#userPwd").val(data.dict[0].dictContent);
					}
				}
			}
		});
	}

	function initDept(compNo) {
		$.ajax({
			url : basePath + "frame/department/getAllDeptList.spring",
			dataType : "json",
			data : {
				"compNo" : compNo
			},
			async : false,
			success : function(data) {
				if (data.result == "success") {
					$("select[xm-select=uDeptId] option:gt(0)").remove();
					var deptList = data.deptList;
					for (var i = 0; i < deptList.length; i++) {
						$("select[xm-select=uDeptId]").append('<option value="'+deptList[i].deptID+'">' + deptList[i].deptName + '</option>');
					}
					
					layui.formSelects.render("uDeptId");//装填数据之后，需要做刷新操作，不然下拉内容不显示
					layui.formSelects.value('uDeptId', deptID.split(","));
				}
			}
		});
	}

	// 获取用户名称拼音 add by jj.ye 20131217
	function getUserNamePY(obj) {
		var userName = $.trim(obj.value);
		var method = "GET";
		var url = baseContext + "getUserNamePY.spring?userName=" + encodeURIComponent(userName);
		var content = null;
		var responseType = "text";
		var callback = getUserNamePYBack;
		$.ajax({
			"url" : url,
			"type" : method,
			"data" : content,
			"dataType" : responseType,
			"success" : callback,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}

	// 回调获取用户名称 add by jj.ye 20131217
	function getUserNamePYBack(userNamePY) {
		document.getElementById("pinyin").value = userNamePY;
	}

	//保存
	function saveSubmit() {
		var userCode = document.getElementById("userCode");
		var userName = document.getElementById("userName");
		var userPwd = document.getElementById("userPwd");
		var sequence = document.getElementById("sequence");
		var c = document.getElementById("compNo");
		var uDept = document.getElementById("uDeptId");
		var tel = $.trim(document.getElementById("tel").value);
		var pinyin = $.trim(document.getElementById("pinyin").value);
		/* 报告者资料新增字段 */
		var birthday = $.trim(document.getElementById("birthday").value);
		var identityClass = $.trim(document.getElementById("identityClass").value);
		var title = $.trim(document.getElementById("title").value);
		var isManager = $.trim(document.getElementById("isManager").value);
		var education = $.trim(document.getElementById("education").value);
		var uDeptId = layui.formSelects.value('uDeptId', "valStr");
		
		if (c.selectedIndex == '-1' || c.selectedIndex == '') {
			assemblys.msg("请选择所属医院");
			uDept.value = 0;
			c.focus();
			return false;
		}
		
		if (!uDeptId) {
			assemblys.msg("请选择所属科室");
			return false;
		} else if ($.trim(userCode.value) == "") {
			assemblys.msg("用户编号不能为空");
			userCode.focus();
			return false;
		} else if ($.trim(userName.value) == "") {
			assemblys.msg("用户名称不能为空");
			userName.focus();
			return false;
		} else if ($.trim(userPwd.value) == "") {
			assemblys.msg("密码不能为空");
			userPwd.focus();
			return false;
		} /* else if ($.trim(sequence.value) == "") {
						assemblys.msg("顺序号不能为空");
						sequence.focus();
						return false;
		} */else if (parseFloat(sequence.value) > 100000 || parseFloat(sequence.value) < 0) {
			assemblys.msg("顺序号必须是0-100000");
			sequence.focus();
			return false;
		}
		
		var sex = $.trim(document.getElementById("sex").options[document.getElementById("sex").selectedIndex].value);
		var email = $.trim(document.getElementById("email").value);
		var entryDate = $.trim(document.getElementById("entryDate").value);
		var currentDuty = $.trim(document.getElementById("currentDuty").value);
		var otherDuty = $.trim(document.getElementById("otherDuty").value);
		var firstCertificateTime = $.trim(document.getElementById("firstCertificateTime").value);
		var certificateNo = $.trim(document.getElementById("certificateNo").value);
		
		var pars = "userCode=" + encodeURIComponent($.trim(userCode.value)) + "&userPwd=" + $.trim(userPwd.value) + "&userName=" + encodeURIComponent($.trim(userName.value)) + "&uDeptId=" + uDeptId + "&sequence=" + $.trim(sequence.value) + "&tel=" + tel + "&pinyin=" + pinyin;
		pars += "&sex=" + sex + "&email=" + email + "&entryDate=" + entryDate + "&currentDuty=" + currentDuty + "&otherDuty=" + otherDuty + "&firstCertificateTime=" + firstCertificateTime + "&certificateNo=" + certificateNo;
		pars += "&birthday=" + birthday + "&identityClass=" + identityClass + "&title=" + title + "&isManager=" + isManager + "&education=" + education;
		pars += "&compNo=" + compNo;
		var url = baseContext + "saveUser.spring?1=1";
		$.ajax({
			"url" : url,
			"type" : "post",
			"data" : pars,
			"success" : saveBack,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}

	function saveBack(doc) {
		var status = doc.getElementsByTagName("status")[0].childNodes[0].nodeValue;
		if (status == "EXIST") {
			assemblys.msg("用户编号已存在，请重新输入");
		} else if (status == "TEL_EXIST") {
			assemblys.alert("此手机号已存在，请重新输入");
			document.getElementById("tel").focus();
			return;
		} else if (status == "OK") {
			assemblys.msg("保存成功", function() {
				assemblys.closeWindow();
				var deptID = document.getElementById("uDeptId").value;
				var compNo = document.getElementById("compNo").value;
				var userCode = $.trim(document.getElementById("userCode").value);
				assemblys.closeWindow();
			})

		} else {
			assemblys.alert("新增失败，请检查你的操作是否正确或联系管理员");
		}
	}
</script>
</head>
<body>
	<input type="hidden" param="compNo" value="<c:out value="${compNo}"/>" />
	<input type="hidden" param="deptID" value="<c:out value="${deptId}"/>" />
	<form action="" method="post" class="layui-form">
		<div class="bodys bodys_noTop">
			<table class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>
						所属医院
					</label>
					<div class="layui-input-inline">
						<select id="compNo" name="compNo" size="1" lay-filter="compNo" lay-verify="required" onchange='deptID = "";initDept(this.value);'>
							<option value=""></option>
							<c:forEach items="${compList }" var="comp" varStatus="vs">
								<option <c:if test="${comp.compNo == compNo}">selected</c:if> value="${comp.compNo}"><c:out value="${comp.compName}"></c:out></option>
							</c:forEach>
						</select>
					</div>
					<label class="layui-form-label">
						<span style="color: red">*</span>
						所属科室
					</label>
					<div class="layui-input-inline">
						<select lay-verify="required" name="uDeptId" xm-select-search="" xm-select="uDeptId" xm-select-radio>
							<option value=""></option>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>
						用户编号
					</label>
					<div class="layui-input-inline">
						<input class="layui-input" id="userCode" name="userCode" type="text" lay-verify="required|limit|character" limit="70" maxlength="70" value="">
					</div>
					<label class="layui-form-label">
						<span style="color: red">*</span>
						用户名称
					</label>
					<div class="layui-input-inline">
						<input class="layui-input" id="userName" name="userName" type="text" lay-verify="required|limit|character" limit="70" maxlength="70" value="" onchange="getUserNamePY(this);">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 拼音缩写 </label>
					<div class="layui-input-inline">
						<input class="layui-input" id="pinyin" name="pinyin" type="text" readonly="readonly"  value="">
					</div>
					<label class="layui-form-label"> 密码 </label>
					<div class="layui-input-inline">
						<input class="layui-input" id="userPwd" name="userPwd" type="text" lay-verify="required|limit" limit="20" maxlength="20" value="888888">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>
						顺序号
					</label>
					<div class="layui-input-inline">
						<input class="layui-input" id="sequence" name="sequence" type="text" lay-verify="required" value="1.00">
					</div>
				</div>
				<hr>
				<div class="layui-form-item">
					<label class="layui-form-label"> 性别 </label>
					<div class="layui-input-inline">
						<select id="sex" name="sex" size="1">
							<option value="">请选择</option>
							<option value="2">未知</option>
							<option value="0">男</option>
							<option value="1">女</option>
						</select>
					</div>
					<label class="layui-form-label"> 本人学历</label>
					<div class="layui-input-inline">
						<select name="education" id="education">
							<option value="">请选择</option>
							<c:forEach items="${educationList }" var="education" varStatus="vs">
								<option value="${education.dictName }">${education.dictName }</option>
							</c:forEach>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 手机号码 </label>
					<div class="layui-input-inline">
						<input class="layui-input" id="tel" name="tel" type="text" size="20" lay-verify="" maxlength="100" value="">
					</div>
					<label class="layui-form-label"> 出生日期 </label>
					<div class="layui-input-inline">
						<input type="text" value="${userBean.birthday }" name="birthday" id="birthday" class="layui-input" onkeydown="keyDown(event,document.getElementById('compUse'));" />
						<i class="layui-icon layui-icon-date i_time2" style="right: 4px; top: 11px;"></i>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 电子邮箱 </label>
					<div class="layui-input-inline">
						<input class="layui-input" id="email" name="email" type="text" value="">
					</div>
					<label class="layui-form-label"> 是否更新 <i class="layui-icon2" whetherUpdateTips>&#xe890;</i></label>
					<div class="layui-input-inline">
						<select name="whetherUpdate">
							<option value="1">是</option>
							<option value="0">否</option>
						</select>
					</div>
				</div>
				<hr>
				<div class="layui-form-item">
					<label class="layui-form-label"> 职称 </label>
					<div class="layui-input-inline">
						<select name="title" id="title">
							<option value="">请选择</option>
							<c:forEach items="${titleList }" var="title" varStatus="vs">
								<option value="${title.dictName }">${title.dictName }</option>
							</c:forEach>
						</select>
					</div>
					<label class="layui-form-label"> 身份类别 </label>
					<div class="layui-input-inline">
						<select name="identityClass" id="identityClass">
							<option value="">请选择</option>
							<c:forEach items="${identityClassList }" var="identityClass" varStatus="vs">
								<option value="${identityClass.dictName }">${identityClass.dictName }</option>
							</c:forEach>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 行政主管人员 </label>
					<div class="layui-input-inline">
						<select name="isManager" id="isManager">
							<option value="">请选择</option>
							<option value="0" <c:if test="${userBean.isManager eq '0'}">selected</c:if>>未知</option>
							<option value="1" <c:if test="${userBean.isManager eq '1'}">selected</c:if>>是</option>
							<option value="2" <c:if test="${userBean.isManager eq '2'}">selected</c:if>>否</option>
						</select>
					</div>
					<label class="layui-form-label"> 入职时间 </label>
					<div class="layui-input-inline">
						<input class="layui-input" value="<%=org.hyena.frame.util.DateTimeUtil.getCurDateTime("yyyy-MM-dd")%>" type="text" name="entryDate" id="entryDate" onkeydown="keyDown(event,document.getElementById('compUse'));" />
						<i class="layui-icon layui-icon-date i_time2" style="right: 4px; top: 11px;"></i>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 现任职务 </label>
					<div class="layui-input-inline">
						<input class="layui-input" id="currentDuty" name="currentDuty" type="text" value="">
					</div>
					<label class="layui-form-label"> 兼任行政职务 </label>
					<div class="layui-input-inline">
						<input class="layui-input" id="otherDuty" name="otherDuty" type="text" value="">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 执业证书编号 </label>
					<div class="layui-input-inline">
						<input class="layui-input" id="certificateNo" name="certificateNo" type="text" value="">
					</div>
					<label class="layui-form-label"> 首次注册执业时间 </label>
					<div class="layui-input-inline">
						<input class="layui-input" value="<%=org.hyena.frame.util.DateTimeUtil.getCurDateTime("yyyy-MM-dd")%>" type="text" name="firstCertificateTime" id="firstCertificateTime" onkeydown="keyDown(event,document.getElementById('compUse'));" />
						<i class="layui-icon layui-icon-date i_time2" style="right: 4px; top: 11px;"></i>
					</div>
				</div>
				<div class="layui-form-item">
					<div class="layui-form-item" style="display: none;">
						<div class="layui-input-inline">
							<select id="uid">
								<c:forEach items="${bean}" var="bean1" varStatus="vs">
									<option value='<c:out value="${bean1.code}"/>'><c:out value="${bean1.name}" /></option>
								</c:forEach>
							</select>
						</div>
					</div>
				</div>
				<hr>
				<div class="layui-form-item">
					<label class="layui-form-label"></label>
					<div class="layui-input-inline">
						<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
						<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
					</div>
				</div>
			</table>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return "只能填写整数";
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		form.on("submit(save)", function(data) {
			saveSubmit();
		});
		
		form.on("select(compNo)", function(data) {
			data.elem.onchange();
		});
		
	});
	
	layui.use('laydate', function() {
		var laydate = layui.laydate;
		//执行一个laydate实例
		laydate.render({
			elem : '#entryDate',//指定元素
			trigger : 'click', //采用click弹出
			ready : function(date) {
				//可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
		
		laydate.render({
			elem : '#firstCertificateTime', //指定元素
			trigger : 'click', //采用click弹出
			ready : function(date) {
				//可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
		laydate.render({
			elem : '#birthday', //指定元素
			trigger : 'click', //采用click弹出
			ready : function(date) {
				//可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
	});
</script>
</html>
