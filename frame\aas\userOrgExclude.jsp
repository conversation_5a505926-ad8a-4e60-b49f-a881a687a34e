<%@ page contentType="text/html; charset=UTF-8" language="java"%>




<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	String baseURL = request.getContextPath();
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>组织架构排除页面</title>
<link rel="stylesheet" type="text/css" href="<%=baseURL%>/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="<%=baseURL%>/plugins/static/css/edit.css" />
<script type="text/javascript" src="<%=basePath%>plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="<%=baseURL%>/plugins/layui/layui.js"></script>
<script type="text/javascript" src="<%=baseURL%>/plugins/layui/assemblys.js"></script>

<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}/frame/roleright/";
	
	//分配/取消权限
	function exclude(obj, code) {
		var userId = "", roleId = "", funId = "", orgType = "", flagId = "", flag = "", compNo = "", app_LIST = "";
		funId = document.getElementById("funId").value;
		orgType = document.getElementById("orgType").value;
		flagId = document.getElementById("flagId").value;
		flag = document.getElementById("flag").value;
		app_LIST = document.getElementById("app_LIST").value;
		compNo = document.getElementById("compNo").value;
		userId = (app_LIST == "false") ? document.getElementById("userId").value : userId;
		roleId = (app_LIST == "true") ? document.getElementById("roleId").value : roleId;
		
		if (obj.checked) {
			var method = "GET";
			var url = baseContext + "exconText.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo + "&flagId=" + flagId + "&flag=" + flag + "&app_LIST=" + app_LIST + "&code=" + code;
			var content = null;
			var responseType = "text";
			var callback = excoBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		} else {
			var method = "GET";
			var url = baseContext + "excanText.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo + "&flagId=" + flagId + "&flag=" + flag + "&app_LIST=" + app_LIST + "&code=" + code;
			var content = null;
			var responseType = "text";
			var callback = excaBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		}
	}

	function excoBack(data) {
		var http_request = {responseText:data};
				if (http_request.responseText == "ADD_EX_ORG_OK") {
					assemblys.msg("操作成功", null, 500);
				} else {
					assemblys.alert("操作失败");
				}
	}

	function excaBack(data) {
		var http_request = {responseText:data};
				if (http_request.responseText == "DEL_EX_ORG_OK") {
					assemblys.msg("操作成功", null, 500);
				} else {
					assemblys.alert("操作失败");
				}
	}
</script>
</head>
<body>
	<form action="" method="post" id="form1" name="form1" class="layui-form">
		<input type="hidden" id="userId" name="userId" value="<c:out value="${userId}"/>">
		<!-- 用户ID -->
		<input type="hidden" id="roleId" name="roleId" value="<c:out value="${roleId}"/>">
		<!-- 角色ID -->
		<input type="hidden" id="app_LIST" name="app_LIST" value="<c:out value="${App_LIST}"/>">
		<!-- 1\2树 -->
		<input type="hidden" id="funId" name="funId" value="<c:out value="${funId}"/>">
		<!-- 功能点ID -->
		<input type="hidden" id="orgType" name="orgType" value="<c:out value="${orgType}"/>">
		<!-- 单选 -->
		<input type="hidden" id="flagId" name="flagId" value="<c:out value="${flagId}"/>">
		<!-- 科室\医院ID -->
		<input type="hidden" id="compNo" name="compNo" value="<c:out value="${compNo}"/>">
		<!-- 医院ID -->
		<input type="hidden" id="flag" name="flag" value="<c:out value="${flag}"/>">
		<input type="hidden" id="appId" name="appId" value="<c:out value="${appId}"/>">
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<div class="layui-form-inline">
					<c:forEach items="${bean}" var="element" varStatus="vs">
						<c:if test="${App_LIST == 'true'}">
							<c:if test="${element.rdeptId != 0}">
								<input type="checkbox" lay-filter="checkbox1" lay-skin="primary" checked="checked" code="<c:out value="${element.code}"/>"
							</c:if>
							<c:if test="${element.rdeptId == '0'}">
								<input type="checkbox" lay-filter="checkbox1" lay-skin="primary" code="<c:out value="${element.code}"/>"
							</c:if>title="<c:out value="${element.name}"/>"/>
						</c:if>
						<c:if test="${App_LIST == 'false'}">
							<c:if test="${element.rdeptId != 0}">
								<input type="checkbox" lay-filter="checkbox1" lay-skin="primary" disabled="disabled" checked="checked" code="<c:out value="${element.code}"/>"
							</c:if>
							<c:if test="${element.rdeptId == '0'}">
								<c:if test="${element.udeptId != 0}">
									<input type="checkbox" lay-filter="checkbox1" lay-skin="primary" checked="checked" code="<c:out value="${element.code}"/>"
								</c:if>
								<c:if test="${element.udeptId == '0'}">
									<input type="checkbox" lay-filter="checkbox1" lay-skin="primary" code="<c:out value="${element.code}"/>"
								</c:if>
							</c:if>
										title="<c:out value="${element.name}"/>"/>
						</c:if>
					</c:forEach>
				</div>
			</div>
		</div>
	</form>
</body>
<script>
	layui.use([ 'form' ], function() {
		var form = layui.form;
		form.on('checkbox(checkbox1)', function(data) {
			exclude(data.elem, data.elem.getAttribute("code"));
		});
		
		// 禁用多选框用灰色
		$(".bodys").find(".layui-checkbox-disbaled").children("i").css("background","#BEBEBE");
		
		
		if($.trim($(".layui-form-inline").html()).length == 0 ){
			$(".layui-form-inline").html("<p style='text-align:center;'>该医院暂无排除科室</p>")
		}
		
		
	});
</script>
</html>
