<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=GBK" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>密码修改</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css">
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/base64.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}/frame/useraction/";
	
	$(function() {
		init();
	});
	
	function init() {
		if (document.getElementById("flag").value == "PWD") {
			document.getElementById("newPwd").focus();
		} else {
			document.getElementById("tempList").value = document.getElementById("temps").value;
		}
	}

	function editPwd() {
		var newPwd = "";
		var userId = $("#userId").val();
		if (document.getElementById("flag").value == "PWD") {
			newPwd = $.trim(document.getElementById("newPwd").value);
			if (newPwd.length < 1) {
				assemblys.alert("密码不能为空");
				document.getElementById("newPwd").focus();
				return;
			}
			//管理员修改，不要求长度
			if ($("#passwordComplexRule").val() == "1") {
				var pwdnum = 3;//管理员修改，不要求复杂度
				var pwdRegex = new RegExp('(?=.*[0-9])');
				if (pwdRegex.test(newPwd)) {
					pwdnum += 1;
				}
				pwdRegex = new RegExp('(?=.*[A-Z])');
				if (pwdRegex.test(newPwd)) {
					pwdnum += 1;
				}
				pwdRegex = new RegExp('(?=.*[a-z])');
				if (pwdRegex.test(newPwd)) {
					pwdnum += 1;
				}
				pwdRegex = new RegExp('(?=.*[^a-zA-Z0-9])');
				if (pwdRegex.test(newPwd)) {
					pwdnum += 1;
				}
				if (pwdnum < 3) {
					assemblys.alert("你的密码复杂度太低（密码中必须包含大写字母、小写字母、数字、特殊字符中的至少3种）");
					document.getElementById("newPwd").select();
					return;
				}
			}
		} 
		var compNo = $("#compNo").val();
		var method = "GET";
		var base64 = new Base64();
		var url = baseContext + "editPwd.spring";
		$.ajax({
			url : url,
			type : "post",
			data : {
				"newPwd" : base64.encode(newPwd),
				"compNo" : compNo,
				"userId" : userId,
				"flag" : document.getElementById("flag").value
			},
			dataType : "text",
			success : function(data) {
				if (data == "OK") {
					var temp = "";
					if (document.getElementById("flag").value == "PWD") {
						temp = "密码";
					} else {
						temp = "模板";
					}
					assemblys.msg("修改" + temp + "成功", function() {
						assemblys.closeWindow();
					});
				} else {
					assemblys.alert("服务器异常");
				}
			},
			error : function() {
				assemblys.alert("你请求的页面有异常");
			}
		});
	}
	function changeToparamer(where) {
		var temp = '';
		var b = '';
		var c = '';
		var d = '';
		var e = '';
		for (var i = 0; i < where.length; i++) {
			if ((where.charAt(i)) == '%') {
				b = encodeURIComponent(where.charAt(i));
				temp = temp + b;
			} else if ((where.charAt(i)) == '&') {
				c = encodeURIComponent(where.charAt(i));
				temp = temp + c;
			} else if ((where.charAt(i)) == '#') {
				d = encodeURIComponent(where.charAt(i));
				temp = temp + d;
			} else if ((where.charAt(i)) == '+') {
				e = encodeURIComponent(where.charAt(i));
				temp = temp + e;
			} else {
				temp = temp + where.charAt(i);
			}
		}
		return temp;
	}
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<div class="bodys bodys_noTop">
			<input id="compNo" type="hidden" value='<c:out value="${compNo}"></c:out>'>
			<input id="flag" name="flag" type="hidden" value='<c:out value="${flag}"></c:out>'>
			<input id="userId" name="userId" type="hidden" value='<c:out value="${userBean.userId}"></c:out>'>
			<input id="passwordComplexRule" name="passwordComplexRule" type="hidden" value='<c:out value="${passwordComplexRule}"/>'>
			<input id="passwordLengthRule" name="passwordLengthRule" type="hidden" value='<c:out value="${passwordLengthRule}"/>'>
			<div class="layui-form-item">
				<label class="layui-form-label">医院</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" readonly="readonly" value="<c:out value="${userBean.compName}" />">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">科室</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" readonly="readonly" value="<c:out value="${userBean.deptName}" />">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">用户名称</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" readonly="readonly" value="<c:out value="${userBean.userName}" />">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">用户编号</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" readonly="readonly" value="<c:out value="${userBean.userCode}" />">
				</div>
			</div>
			<c:if test="${flag == 'PWD'}">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<font color="red">*</font>
						新密码
					</label>
					<div class="layui-input-inline">
						<input id="newPwd" name="newPwd" type="text" lay-verify="required|specialCharacters|noChinese" value="" maxlength="20" autocomplete="off" class="layui-input">
					</div>
				</div>
			</c:if>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		form.render();
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.length < limit) {
					return "长度不能小于" + limit;
				}
			},
			noChinese : function(value, item) {
				var reg = /[^\x00-\xff]/;
				if (reg.test(value)) {
					return "不能包含中文";
				}
			},
			specialCharacters : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】";
				}
			}
		});
		form.on("submit(save)", function(data) {
			editPwd();
			return false;
		});
	});
</script>
</html>
