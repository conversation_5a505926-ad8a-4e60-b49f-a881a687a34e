<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%@ page import="org.springframework.web.context.support.WebApplicationContextUtils"%>
<%@ page import="org.springframework.context.ApplicationContext"%>
<%@ page import="org.hyena.frame.util.*"%>
<%@ page import="org.hyena.frame.service.FrameRightService"%>
<%@ page import="org.hyena.frame.Globals,org.hyena.frame.view.*"%>
<jsp:useBean id="MsgUser" scope="session" class="org.hyena.frame.pojo.UserInfo" />
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);

	User user = (User) session.getAttribute(Globals.KEY_USER);

	ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(application);
	FrameRightService frameRightService = (FrameRightService) ac.getBean("FtnAEMSFrameRightService");

	// 是否有所有权限
	String hasAllRight = "";
	hasAllRight = (request.getParameter("hasAllRight") != null) ? request.getParameter("hasAllRight") : "false";
	if ("true".equals(hasAllRight)) {
		// 查询当前人的权限--防止越级
		boolean hasRight = frameRightService.hasRight(Integer.valueOf(user.getUserId()),
				BaseConstant.FUN_CODE_LIMIT_USER_RIGHTS, String.valueOf(Globals.RIGHTPOINT_VIEW), true);
		if (!hasRight) {
			hasAllRight = "false";
		}
	} else {
		hasAllRight = "false";
	}
	String funCode = "true".equals(hasAllRight)
			? BaseConstant.FUN_CODE_LIMIT_USER_RIGHTS
			: BaseConstant.FUN_CODE_LIMIT_USER_RIGHTS_COMP;
	request.setAttribute("hasAllRight", hasAllRight);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>分配权限的框架</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css">
</head>
<body style="margin: 0px; height: 100%; overflow-y: hidden;">
	<!-- 标题栏 -->
	<div class="head0">
		<span class="head1_text fw700">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<span id="topTitle"></span>
		</span>
		<div class="head0_right fr">
			<form class="layui-form" id="appListView" style="display: none;">
				<input type="hidden" name="hasAllRight" value="<c:out value='${hasAllRight}'/>">
				<label class="layui-form-label2">应用系统</label>
				<div class="layui-input-inline">
					<select id="appList" name="appList" lay-filter="appList"></select>
				</div>
			</form>
		</div>
	</div>
	<div class="bodys">
		<div class="layui-row ">
			<form name='tree' action='' method="post" target="treeFrame" class="layui-form">
				<!-- tree -->
				<div class="treeDiv treeDiv-user" style="top: 10px; width: 170px; min-width: 170px; overflow: auto;">
					<input type="hidden" name="treeTypeValue" id="treeTypeValue" value='<c:out value="${treeTypeValue}"/>'>
					<div class="treeHead" style="width: 100%; padding: 0px;">目录树</div>
					<div style="width: 100%; padding-top: 5px; padding-left: 5px;">
						<input type="radio" name="treeType2" checked="checked" value="comp" title="用户">
						<input type="radio" name="treeType2" lay-filter="juese" value="role" title="角色">
					</div>
					<div class="userTreeList1" style="width: 100%; padding-left: 5px;">
						<input class="layui-input  fl" type="text" style="width: 117px; height: 30px; color: #999" id="user_txt" placeholder="编号/名称" value="" onkeydown="if(event.keyCode == 13) {$(this).next().click();}" />
						<button type="button" class="layui-btn  layui-btn-sm" onclick="findUser()">查询</button>
					</div>
					<ul class="userTreeList1" id="tree1" style="position: absolute; top: 110px; left: 0px; right: 0px;">
					</ul>
				</div>
				<div class="treeDiv treeDiv-role layui-hide" style="top: 10px; width: 170px; min-width: 170px; overflow: auto;">
					<div class="treeHead" style="width: 100%; padding: 0px;">目录树</div>
					<div style="width: 100%; padding-top: 5px; padding-left: 5px;">
						<input type="radio" name="treeType3" lay-filter="yonghu" value="comp" title="用户">
						<input type="radio" name="treeType3" value="role" checked="checked" title="角色">
					</div>
					<div class="userTreeList2" style="width: 100%; padding-left: 5px; ">
						<input type="text" style="width: 117px; height: 30px; color: #999" class="layui-input fl" id="role_txt" placeholder="编号/名称" value="" onkeydown="if(event.keyCode == 13) {$(this).next().click();}">
						<button type="button" class="layui-btn  layui-btn-sm" onclick="findRole()">查询</button>
					</div>
					<ul id="tree2" class="tree-table-tree-box userTreeList2" style="position: absolute; top: 110px; bottom: 0px; left: 0px; right: 0px; overflow: auto;">
					</ul>
				</div>
			</form>
			<form action="" name="mainForm" method="post" class="layui-form">
				<div class="tableDiv table_noSearch" style="left: 190px;">
					<div style="padding-right: 10px;">
						<table class="layui-table main_table" style="margin-bottom: 0;" cellpadding="0" cellspacing="0">
							<tr class="main_title">
								<td width="30%" align="center">子系统/功能</td>
								<td width="8%" align="center">组织架构</td>
								<td width="7%" align="center">浏览</td>
								<td width="7%" align="center">新增</td>
								<td width="7%" align="center">编辑</td>
								<td width="7%" align="center">删除</td>
								<td width="7%" align="center">执行</td>
								<td width="7%" align="center">监督</td>
							</tr>
						</table>
					</div>
					<div id="tableBody">
					</div>
				</div>
			</form>
		</div>
	</div>
</body>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}frame/aas/js/userRightFrameList.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var hasAllRight = "";
	
	$(function() {
		
		assemblys.getMenuIcon({
			funCode : assemblys.getParam("funCode"),
			hasOrg : false,
			dom : $("b#menuIcon")
		});
		
		hasAllRight = $("input[name='hasAllRight']").val();
		
		$(document).on("mouseenter", "td:has(div.layui-form-checkbox)", function(e) {
			var remark = $(this).find("input[rpid]").attr("remark");
			if (remark) {
				assemblys.tips(this, remark, 0, "left");
			}
		}).on("mouseleave", "td:has(div.layui-form-checkbox)", function(e) {
			var remark = $(this).find("input[rpid]").attr("remark");
			if (remark) {
				assemblys.closeMsg();
			}
		});
	})
</script>
</html>
