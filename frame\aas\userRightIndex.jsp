<%@ page contentType="text/html; charset=UTF-8" language="java"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<!DOCTYPE html>
<html>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>分配权限</title>
<script language="javascript" type="text/javascript">
	var baseContext = "${basePath}/frame/roleright/";
	var hasAllRight = "";
	
	//自刷新
	function flush() {
		getHtml(theUrl);//theUrl是当前地址
	}
	//初始化
	function init() {
		
		hasAllRight = $("#hasAllRight").val();
		
		var appList = $("#flag").val();
		// 标题头
		$("#topTitle").text("");
		if (appList == 'false') {
			var hasRole = $.trim($("#hasRole").text());
			$("#topTitle").attr("title", hasRole);
			if (hasRole.length > 40) {
				hasRole = hasRole.substring(0, 39) + "……";
			}
			$("#topTitle").append("【" + assemblys.escape($("#empName").val()) + "】" + hasRole);
			
		} else {
			$("#topTitle").attr("title", "");
			$("#topTitle").append("【" + assemblys.escape($("#roleName").val()) + "】");
		}
		
		// 禁用多选框用灰色
		$(".main_table").find(".layui-checkbox-disbaled").children("i").css("background", "#BEBEBE");
	}

	//分配权限
	function setRole(obj, rightPoint) {
		
		var funId = $(obj).attr("funId");
		var funName = $(obj).attr("funName");
		
		var temp = "";
		var userId = "";
		var roleId = "";
		var rights = "";
		var flag = $("#flag").val();
		if (flag == "false") {
			userId = $("#userId").val();
		} else {
			roleId = $("#roleId").val();
		}
		temp = (rightPoint == "1") ? "浏览" : (rightPoint == "2") ? "新增" : (rightPoint == "3") ? "编辑" : (rightPoint == "4") ? "删除" : (rightPoint == "5") ? "执行" : (rightPoint == "6") ? "监督" : "";
		rights = "1";
		if (obj.checked) {
			var method = "GET";
			var url = baseContext + "assignRole.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&rightPoint=" + rightPoint + "&rights=" + rights;
			var content = null;
			var responseType = "text";
			var callback = assignBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系管理员");
				}
			});
		} else {
			var method = "GET";
			var url = baseContext + "cancelRole.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&rightPoint=" + rightPoint;
			var content = null;
			var responseType = "text";
			var callback = cancelBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系管理员");
				}
			});
		}
	}

	function assignBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "ADD_OK") {
			assemblys.msg("操作成功", null, 500);
		} else {
			assemblys.alert("操作失败");
		}
	}

	function cancelBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "DEL_OK") {
			assemblys.msg("操作成功", null, 500);
		} else {
			assemblys.alert("操作失败");
		}
	}

	//选择系统
	function selectSubmit(obj) {
		var userId = $("#userId").val();
		var empName = $("#empName").val();
		var roleName = $("#roleName").val();
		document.forms[0].action = baseContext + "index.spring?appId=" + obj.value + "&userId=" + userId + "&app_list=false" + "&empName=" + empName + "&roleName=" + roleName;
		document.forms[0].submit();
	}

	//获取整个子系统未分配的权限数据
	function setAllData(subId) {
		var rpId = new Array();
		$("input[name='rightCheckbox']:not(:checked)").each(function(i, e) {
			var rpID = $(this).attr("rpID");
			var checkSubId = $(this).attr("subId");
			if (checkSubId == subId) {
				rpId.push(rpID);
			}
		})
		return rpId;
	}

	//分配整个子系统权限
	function setAllRole(obj) {
		
		var subName = $(obj).attr("subName");
		var subId = $(obj).attr("subId");
		
		//获取整个子系统未分配的权限数据
		var rpIdArray = setAllData(subId);
		
		var userId = "", roleId = "";
		var flag = $("#flag").val();
		if (flag == "false") {
			userId = $("#userId").val();
		} else {
			roleId = $("#roleId").val();
		}
		assemblys.confirm("为 [" + subName + "] 分配所有权限吗？", function() {
			var method = "GET";
			var url = baseContext + "assignAllRole.spring?userId=" + userId + "&roleId=" + roleId + "&subId=" + subId + "&hasAllRight=" + hasAllRight + "&rpIdArray=" + rpIdArray;
			var content = null;
			var responseType = "text";
			var callback = assignAllBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系管理员");
				}
			});
		});
	}

	function assignAllBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "ADD_All_OK") {
			assemblys.msg("操作成功", function() {
				flush();
			}, 1000);
		} else {
			assemblys.alert("操作失败");
		}
	}

	//获取整个子系统已分配的权限数据
	function cancelAllData(subId) {
		var rpId = new Array();
		$("input[name='rightCheckbox']:checked").each(function(i, e) {
			var rpID = $(this).attr("rpID");
			var checkSubId = $(this).attr("subId");
			if (checkSubId == subId) {
				rpId.push(rpID);
			}
		})
		return rpId;
	}

	//取消整个子系统权限
	function cancelAllRole(obj) {
		
		var subName = $(obj).attr("subName");
		var subId = $(obj).attr("subId");
		
		//获取整个子系统已分配的权限数据
		var rpIdArray = cancelAllData(subId);
		
		var userId = "", roleId = "";
		var flag = $("#flag").val();
		if (flag == "false") {
			userId = $("#userId").val();
		} else {
			roleId = $("#roleId").val();
		}
		assemblys.confirm("为 [" + subName + "] 取消所有权限吗？", function() {
			var method = "GET";
			var url = baseContext + "cancelAllRole.spring?userId=" + userId + "&roleId=" + roleId + "&subId=" + subId + "&rpIdArray=" + rpIdArray + "&hasAllRight=" + hasAllRight;
			var content = null;
			var responseType = "text";
			var callback = cancelAllBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系管理员");
				}
			});
		});
	}

	function cancelAllBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "DEL_ALL_OK") {
			assemblys.msg("操作成功", function() {
				flush();
			}, 1000);
		} else {
			assemblys.alert("操作失败");
		}
	}

	function goOrgt(obj) {
		
		var funId = $(obj).attr("funId");
		var funName = $(obj).attr("funName");
		
		var userId = "";
		var roleId = "";
		var appId = "";
		var flag = "";
		var empName = "";
		var roleName = "";
		var title = "";
		flag = $("#flag").val();
		if (flag == "false") {
			userId = $("#userId").val();
			empName = $("#empName").val();
			title = "[ " + funName + " - 用户：" + empName + " ]";
		} else {
			roleId = $("#roleId").val();
			roleName = $("#roleName").val();
			title = "[ " + funName + " - 角色：" + roleName + " ]";
		}
		appId = $("#appId").val();
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '分配组织架构权限  ' + title,
			scrollbar : false,
			area : [ '98%', '100%' ],
			content : baseContext + "goOrg.spring?hasAllRight=" + hasAllRight + "&appId=" + appId + "&userId=" + userId + "&app_list=" + flag + "&roleId=" + roleId + "&funId=" + funId + "&funName=" + encodeURIComponent(funName) + "&empName=" + encodeURIComponent(empName) + "&roleName=" + encodeURIComponent(roleName) + "&" + $(document.forms[0]).serialize()
		});
		
	}
</script>
<style type="text/css">
.table-main-title {
	height: 20px;
	line-height: 20px;
	background-color: rgb(242, 242, 242);
	color: #000000;
	font-weight: 700;
	cursor: default;
	vertical-align: middle;
	padding: 0px 10px;
}

.table-main-button {
	height: 20px;
	line-height: 20px;
	float: right;
	margin-left: 10px;
}
.tableDiv {
    overflow: hidden;
}

#tableBody{
    position: absolute;
    top: 40px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    overflow: auto;
}
</style>
</head>
<body class="noStyle body_noTop">
		<input type="hidden" id="hasAllRight" name="hasAllRight" value="<c:out value="${hasAllRight}"/>">
		<input type="hidden" id="empName" name="empName" value="<c:out value="${empName}"/>">
		<!-- 用户 -->
		<input type="hidden" id="roleName" name="roleName" value="<c:out value="${roleName}"/>">
		<!-- 角色 -->
		<input type="hidden" id="userId" name="userId" value="<c:out value="${userId}"/>">
		<!-- 用户ID -->
		<input type="hidden" id="roleId" name="roleId" value="<c:out value="${roleId}"/>">
		<!-- 角色ID -->
		<input type="hidden" id="appId" name="appId" value="<c:out value="${appId}"/>">
		<!-- 系统ID -->
		<input type="hidden" id="flag" name="flag" value="<c:out value="${App_LIST}"/>">
		<!-- 1\2树 -->
		<c:if test="${App_LIST == 'false'}">
			<div id="hasRole" style="display: none;">
				- 拥有角色：<%
				// 这里的<%必须贴着： 
					String strRoles = (request.getAttribute("roles") == null) ? "" : (String) request.getAttribute("roles");
					strRoles = "" + strRoles.replaceAll(",", "&nbsp;/&nbsp;");
					out.print(strRoles);
			%>
			</div>
		</c:if>
		<table class="layui-table main_table" style="margin-bottom: 0;" cellpadding="0" cellspacing="0">
			<c:set var="p_iCounts" value="${1}" />
			<c:set var="ind" value="${0}" />
			<c:forEach items="${list}" var="element" varStatus="vs">
				<c:set var="p_iCounts" value="${p_iCounts + 1}" />
				<tr>
					<c:if test="${element.flag == 'TITLE'}">
						<tr name="trTitle" trSubId="${element.subId}">
							<td colspan="8" align="left" bgcolor="#F2F2F2">
								<span class=" table-main-title">
									<i class='layui-icon2'>&#xe79f;</i>
									<c:out value="${element.subName}" />
								</span>
								<c:if test="${!empty element.subId}">
									<input type="button" subName="<c:out value="${element.subName}"/>" subId="<c:out value="${element.subId}"/>" onclick='cancelAllRole(this)' value="取消" class="layui-btn layui-btn-sm table-main-button skin-btn-stress">
									<input type="button" subName="<c:out value="${element.subName}"/>" subId="<c:out value="${element.subId}"/>" onclick='setAllRole(this)' value="分配" class="layui-btn layui-btn-sm  table-main-button">
								</c:if>
							</td>
						</tr>
					</c:if>
				<tr name="${element.subId}" class="comTab_R1 ${empty element.remark ? '' : 'layui-hide'}">
					<td width="30%" class="comTab_Td">
						<strong style="margin-left: 15px;">
							<c:out value="${element.funName}" /> <i class="layui-icon2" style="color: #A0A0A0;font-size: 16px;" onmouseover="assemblys.tips(this,'<c:out value="${element.funDesc}"/>' || '缺少说明',0,'top')" onmouseout="assemblys.closeMsg();">&#xe890;</i>
						</strong>
						<font style="color: #BEBEBE;">
							-
							<c:out value="${element.funCode}" />
						</font>
					</td>
					<td width="8%" align="center" class="comTab_Td">
						<c:if test="${element.isOrgRight}">
							<i class="layui-icon layui-icon-share i_check" title="组织架构" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='goOrgt(this)'></i>
						</c:if>
					</td>
					<c:if test="${App_LIST=='false'}">
						<td width="7%" align="center" class="comTab_Td">
							<c:if test="${!empty element.rp1ID}">
								<c:if test="${!empty element.rp1RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp1Remark}"/>" rpID="<c:out value="${element.rp1ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,1)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked" disabled="disabled">
								</c:if>
								<c:if test="${empty element.rp1RoleRight}">
									<c:if test="${!empty element.rp1UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp1Remark}"/>" rpID="<c:out value="${element.rp1ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,1)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked">
									</c:if>
									<c:if test="${empty element.rp1UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp1Remark}"/>" rpID="<c:out value="${element.rp1ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,1)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
									</c:if>
								</c:if>
							</c:if>
						</td>
						<td width="7%" align="center" class="comTab_Td">
							<c:if test="${!empty element.rp2ID}">
								<c:if test="${!empty element.rp2RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp2Remark}"/>" rpID="<c:out value="${element.rp2ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,2)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked" disabled="disabled">
								</c:if>
								<c:if test="${empty element.rp2RoleRight}">
									<c:if test="${!empty element.rp2UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp2Remark}"/>" rpID="<c:out value="${element.rp2ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,2)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked">
									</c:if>
									<c:if test="${empty element.rp2UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp2Remark}"/>" rpID="<c:out value="${element.rp2ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,2)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
									</c:if>
								</c:if>
							</c:if>
						</td>
						<td width="7%" align="center" class="comTab_Td">
							<c:if test="${!empty element.rp3ID}">
								<c:if test="${!empty element.rp3RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp3Remark}"/>" rpID="<c:out value="${element.rp3ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,3)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked" disabled="disabled">
								</c:if>
								<c:if test="${empty element.rp3RoleRight}">
									<c:if test="${!empty element.rp3UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp3Remark}"/>" rpID="<c:out value="${element.rp3ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,3)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked">
									</c:if>
									<c:if test="${empty element.rp3UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp3Remark}"/>" rpID="<c:out value="${element.rp3ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,3)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
									</c:if>
								</c:if>
							</c:if>
						</td>
						<td width="7%" align="center" class="comTab_Td">
							<c:if test="${!empty element.rp4ID}">
								<c:if test="${!empty element.rp4RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp4Remark}"/>" rpID="<c:out value="${element.rp4ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,4)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked" disabled="disabled">
								</c:if>
								<c:if test="${empty element.rp4RoleRight}">
									<c:if test="${!empty element.rp4UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp4Remark}"/>" rpID="<c:out value="${element.rp4ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,4)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked">
									</c:if>
									<c:if test="${empty element.rp4UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp4Remark}"/>" rpID="<c:out value="${element.rp4ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,4)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
									</c:if>
								</c:if>
							</c:if>
						</td>
						<td width="7%" align="center" class="comTab_Td">
							<c:if test="${!empty element.rp5ID}">
								<c:if test="${!empty element.rp5RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp5Remark}"/>" rpID="<c:out value="${element.rp5ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,5)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked" disabled="disabled">
								</c:if>
								<c:if test="${empty element.rp5RoleRight}">
									<c:if test="${!empty element.rp5UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp5Remark}"/>" rpID="<c:out value="${element.rp5ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,5)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked">
									</c:if>
									<c:if test="${empty element.rp5UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp5Remark}"/>" rpID="<c:out value="${element.rp5ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,5)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
									</c:if>
								</c:if>
							</c:if>
						</td>
						<td width="7%" align="center" class="comTab_Td">
							<c:if test="${!empty element.rp6ID}">
								<c:if test="${!empty element.rp6RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp6Remark}"/>" rpID="<c:out value="${element.rp6ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,6)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked" disabled="disabled">
								</c:if>
								<c:if test="${empty element.rp6RoleRight}">
									<c:if test="${!empty element.rp6UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp6Remark}"/>" rpID="<c:out value="${element.rp6ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,6)' lay-skin="primary" lay-filter="checkbox" type="checkbox" checked="checked">
									</c:if>
									<c:if test="${empty element.rp6UserRight}">
										<input name='rightCheckbox' remark="<c:out value="${element.rp6Remark}"/>" rpID="<c:out value="${element.rp6ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,6)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
									</c:if>
								</c:if>
							</c:if>
						</td>
					</c:if>
					<c:if test="${App_LIST=='true'}">
						<td class="comTab_Td" width="7%" align="center">
							<c:if test="${!empty element.rp1ID}">
								<c:if test="${empty element.rp1RoleRight}">
									<input id='' name='rightCheckbox' remark="<c:out value="${element.rp1Remark}"/>" rpID="<c:out value="${element.rp1ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" onclick='setRole(this,1)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
								<c:if test="${!empty element.rp1RoleRight}">
									<input id='' name='rightCheckbox' remark="<c:out value="${element.rp1Remark}"/>" rpID="<c:out value="${element.rp1ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" checked="checked" onclick='setRole(this,1)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
							</c:if>
						</td>
						<td class="comTab_Td" width="7%" align="center">
							<c:if test="${!empty element.rp2ID}">
								<c:if test="${empty element.rp2RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp2Remark}"/>" rpID="<c:out value="${element.rp2ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' onclick='setRole(this,2)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
								<c:if test="${!empty element.rp2RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp2Remark}"/>" rpID="<c:out value="${element.rp2ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' checked="checked" onclick='setRole(this,2)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
							</c:if>
						</td>
						<td class="comTab_Td" width="7%" align="center">
							<c:if test="${!empty element.rp3ID}">
								<c:if test="${empty element.rp3RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp3Remark}"/>" rpID="<c:out value="${element.rp3ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' onclick='setRole(this,3)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
								<c:if test="${!empty element.rp3RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp3Remark}"/>" rpID="<c:out value="${element.rp3ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' checked="checked" onclick='setRole(this,3)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
							</c:if>
						</td>
						<td class="comTab_Td" width="7%" align="center">
							<c:if test="${!empty element.rp4ID}">
								<c:if test="${empty element.rp4RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp4Remark}"/>" rpID="<c:out value="${element.rp4ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' onclick='setRole(this,4)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
								<c:if test="${!empty element.rp4RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp4Remark}"/>" rpID="<c:out value="${element.rp4ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' checked="checked" onclick='setRole(this,4)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
							</c:if>
						</td>
						<td class="comTab_Td" width="7%" align="center">
							<c:if test="${!empty element.rp5ID}">
								<c:if test="${empty element.rp5RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp5Remark}"/>" rpID="<c:out value="${element.rp5ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' onclick='setRole(this,5)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
								<c:if test="${!empty element.rp5RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp5Remark}"/>" rpID="<c:out value="${element.rp5ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' checked="checked" onclick='setRole(this,5)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
							</c:if>
						</td>
						<td class="comTab_Td" width="7%" align="center">
							<c:if test="${!empty element.rp6ID}">
								<c:if test="${empty element.rp6RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp6Remark}"/>" rpID="<c:out value="${element.rp6ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' onclick='setRole(this,6)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
								<c:if test="${!empty element.rp6RoleRight}">
									<input name='rightCheckbox' remark="<c:out value="${element.rp6Remark}"/>" rpID="<c:out value="${element.rp6ID}"/>" subId="<c:out value="${element.subId}"/>" funId="<c:out value="${element.funId}"/>" funName="<c:out value="${element.funName}"/>" id='' checked="checked" onclick='setRole(this,6)' lay-skin="primary" lay-filter="checkbox" type="checkbox">
								</c:if>
							</c:if>
						</td>
					</c:if>
				</tr>
			</c:forEach>
		</table>
</body>
<script>
	$("tr[name='trTitle']").each(function(i, e) {
		var trSubId = $(this).attr("trSubId");
		var length = $("tr[name='" + trSubId + "']:visible").length;
		if (length == 0) {
			$("tr[name='" + trSubId + "']").remove();
			$(this).remove();
		}
	});
	layui.use([ 'form' ], function() {
		var form = layui.form;
		form.on('checkbox(checkbox)', function(data) {
			data.elem.onclick();
		});
		form.render();
	});
	init();
</script>
</html>
