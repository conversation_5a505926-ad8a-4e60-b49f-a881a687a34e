<%@ page contentType="text/html; charset=UTF-8" language="java"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<html>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>分配权限-组织架构</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/common.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/style.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = basePath + "/frame/roleright/";
	var hasAllRight = "${hasAllRight}";
	var before = "";
	
	$(function(){
		init();
	});
	
	//去空格
	function trim(str) {
		var regBegSpace = /^(\s+)/;
		var regEndSpace = /(\s+)$/;
		var r = str.replace(regBegSpace, "").replace(regEndSpace, "");
		return (r);
	}
	
	//刷新本页
	function flushOrg() {
		var userId = "", roleId = "", funId = "", empName = "", roleName = "", funName = "";
		funId = document.getElementById("funId").value;
		var flag = document.getElementById("flag").value;
		appId = document.getElementById("appId").value;
		empName = document.getElementById("empName").value;
		roleName = document.getElementById("roleName").value;
		funName = document.getElementById("funName").value;
		if (flag == "false") {
			userId = document.getElementById("userId").value;
		} else {
			roleId = document.getElementById("roleId").value;
		}
		document.forms[0].action = baseContext + "goOrg.spring?hasAllRight=" + hasAllRight + "&appId=" + appId + "&userId=" + userId + "&app_list=" + flag + "&roleId=" + roleId + "&funId=" + funId + "&empName=" + encodeURIComponent(empName) + "&roleName=" + encodeURIComponent(roleName) + "&funName="
				+ encodeURIComponent(funName);
		disabledAllCheckBox();
		document.forms[0].submit();
	}
	
	//初始化
	function init() {
		var temp = document.getElementById("orgType").value;
		if (temp != "") {
			for (i = 0; i < document.forms[0].elements.length; i++) {
				if (document.forms[0].elements[i].type == "radio") {
					if (temp == document.forms[0].elements[i].value) {
						document.forms[0].elements[i].checked = true;
					}
				}
			}
		}
		before = temp;
		
		// 设置医院下有科室权限的图标
		var cCount = 1 + parseInt(document.getElementById("compCount").value);
		for (j = 1; j < cCount; j++) {
			if (document.getElementById("hasDepts" + j) && parseInt(document.getElementById("hasDepts" + j).value) > 0 && document.getElementById("imgHasDepts" + j)) {
				document.getElementById("imgHasDepts" + j).style.display = "";
				document.getElementById("imgHasDepts" + j).title = "该医院下有 " + document.getElementById("hasDepts" + j).value + " 个科室权限";
			}
		}
		layui.form.render();
		
		// 禁用多选框用灰色
		$(".main_table").find(".layui-checkbox-disbaled").children("i").css("background", "#BEBEBE");
		
	}
	
	//展开/隐藏科室
	function showThat(tdId) {
		if (document.getElementById("deptTd" + tdId).style.display == "") {
			document.getElementById("deptTd" + tdId).style.display = "none";
		} else {
			document.getElementById("deptTd" + tdId).style.display = "";
		}
	}
	
	//展开/隐藏全部医院下的科室
	function showAllThat(obj) {
		var dataValue = $(obj).attr("dataValue");
		$(obj).attr("dataValue", dataValue == "show" ? "close": "show");
		var cCount = 1 + parseInt($("#compCount").val());
		var chk = document.getElementById("chkHasDeptsComp");
		for (i = 1; i < cCount; i++) {
			if (chk.checked) {
				var hasView = $("#compTd" + i).css("display");
				if(hasView != "none" && $("#compTd" + i).length>0){
					if(dataValue=="show"){
						$("#compTd" + i+",#deptTd" + i ).show();
					}else{
						$("#deptTd" + i ).hide();
					}
				}
			} else{
				if($("#compTd" + i).length>0){
					if(dataValue=="show"){
						$("#deptTd" + i ).show();
					}else{
						$("#deptTd" + i ).hide();
					}
				}
			}
		}
	}
	//仅显示有科室权限的医院 
	function showHasDeptsComp() {
		var cCount = 1 + parseInt($("#compCount").val());
		var chk = document.getElementById("chkHasDeptsComp");
		for (i = 1; i < cCount; i++) {
			$("#deptTd" + i).hide();
			if (chk.checked) {
				var checkLength = $("#deptTd" + i).find("input[type='checkbox']:checked").length;
				if(checkLength == 0){
					$("#compTd" + i).hide();
				}
			} else{
				$("#compTd" + i).show();
			}
		}
	}
	
	// 仅显示已分配的科室
	function showHasDepts() {
		var cCount = 1 + parseInt($("#compCount").val());
		var chk = document.getElementById("chkHasDepts");
		for (i = 1; i < cCount; i++) {
			if (chk.checked) {
				$("#deptTd" + i).find("input[type='checkbox']:checked").next().css("background","yellow");
			} else{
				$("#deptTd" + i).find("input[type='checkbox']:checked").next().css("background","");
			}
		}
	}
	
	
	//单选分配
	function catchThat(obj) {
		var userId = "", roleId = "", funId = "";
		funId = document.getElementById("funId").value;
		var flag = document.getElementById("flag").value;
		if (flag == "false") {
			userId = document.getElementById("userId").value;
		} else {
			roleId = document.getElementById("roleId").value;
		}
		var orgType = obj.value;
		var showInfo = (orgType == "group") ? "要分配集团权限？" : (orgType == "comp") ? "要分配本医院权限？" : (orgType == "dept") ? "要分配本科室权限？" : (orgType == "one") ? "选择自定义将取消所有权限？" : "";
		if (orgType != "one") {
			assemblys.confirm(showInfo, function() {
				var method = "GET";
				var url = baseContext + "assignOrg.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&flag=" + flag;
				var content = null;
				var responseType = "text";
				var callback = assignOrgBack;
				$.ajax({
					"url" : url,
					"type" : method,
					"data" : content,
					"dataType" : responseType,
					"success" : callback,
					"error" : function(e) {
						assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
					}
				});
			}, function() {
				for (i = 0; i < document.forms[0].elements.length; i++) {
					if (document.forms[0].elements[i].type == "radio") {
						if (document.forms[0].elements[i].value == before) {
							document.forms[0].elements[i].checked = true;
						}
					}
				}
				layui.form.render();
			});
		} else {
			assemblys.confirm(showInfo, function() {
				var method = "GET";
				var url = baseContext + "deleteOrg.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId;
				var content = null;
				var responseType = "text";
				var callback = deleteOrgBack;
				$.ajax({
					"url" : url,
					"type" : method,
					"data" : content,
					"dataType" : responseType,
					"success" : callback,
					"error" : function(e) {
						assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
					}
				});
			}, function() {
				for (i = 0; i < document.forms[0].elements.length; i++) {
					if (document.forms[0].elements[i].type == "radio") {
						if (document.forms[0].elements[i].value == before) {
							document.forms[0].elements[i].checked = true;
						}
					}
				}
				layui.form.render();
			});
		}
	}
	function assignOrgBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "ADD_ORG_OK") {
			assemblys.msg("操作成功", function() {
				flushOrg();
			});
		} else {
			assemblys.alert("操作失败");
		}
	}
	function deleteOrgBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "DEL_ORG_OK") {
			flushOrg();
		} else {
			assemblys.alert("操作失败");
		}
	}
	//所有科室
	function allDept(obj) {
		
		var compNo = $(obj).attr("compNo");
		var compName = $(obj).attr("compName");
		
		var userId = "", roleId = "", funId = "", orgType = "2";
		funId = document.getElementById("funId").value;
		var flag = document.getElementById("flag").value;
		if (flag == "false") {
			userId = document.getElementById("userId").value;
		} else {
			roleId = document.getElementById("roleId").value;
		}
		if (obj.checked) {
			var method = "GET";
			var url = baseContext + "assignComp.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo + "&flag=" + flag;
			var content = null;
			var responseType = "text";
			var callback = allDeptBack;
			disabledAllCheckBox();
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		} else {
			assemblys.confirm("为[" + compName + "]取消此权限？", function() {
				var method = "GET";
				var url = baseContext + "cancelComp.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo;
				var content = null;
				var responseType = "text";
				var callback = cancelCompBack;
				disabledAllCheckBox();
				$.ajax({
					"url" : url,
					"type" : method,
					"data" : content,
					"dataType" : responseType,
					"success" : callback,
					"error" : function(e) {
						assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
					}
				});
			}, function() {
				obj.checked = true;
				layui.form.render();
			});
		}
	}
	//使所有checkbox不可用
	function disabledAllCheckBox() {
		var cbs = document.getElementsByTagName("input");
		for (var i = 0; i < cbs.length; i++) {
			if (cbs[i].type.toLowerCase() == "checkbox" || cbs[i].type.toLowerCase() == "radio")
				cbs[i].setAttribute("disabled", "disabled");
		}
	}
	
	function allDeptBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "ADD_COMP_ORG_OK") {
			flushOrg();
		} else {
			assemblys.alert("操作失败");
		}
	}
	function cancelCompBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "DEL_COMP_ORG_OK") {
			flushOrg();
		} else {
			assemblys.alert("操作失败");
		}
	}
	//某个科室
	function oneDept(obj) {
		
		var compNo = $(obj).attr("compNo");
		var deptId = $(obj).attr("deptId");
		var deptName = $(obj).attr("deptName");
		
		var userId = "", roleId = "", funId = "", orgType = "1";
		funId = document.getElementById("funId").value;
		var flag = document.getElementById("flag").value;
		if (flag == "false") {
			userId = document.getElementById("userId").value;
		} else {
			roleId = document.getElementById("roleId").value;
		}
		if (obj.checked) {
			var method = "GET";
			var url = baseContext + "assignDept.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo + "&deptId=" + deptId + "&flag=" + flag;
			var content = null;
			var responseType = "text";
			var callback = oneDeptBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		} else {
			assemblys.confirm("为[" + deptName + "]取消此权限？", function() {
				var method = "GET";
				var url = baseContext + "cancelDept.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&deptId=" + deptId;
				var content = null;
				var responseType = "text";
				var callback = cancelDeptBack;
				$.ajax({
					"url" : url,
					"type" : method,
					"data" : content,
					"dataType" : responseType,
					"success" : callback,
					"error" : function(e) {
						assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
					}
				});
			}, function() {
				obj.checked = true;
				layui.form.render();
			});
			
		}
	}
	function oneDeptBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "ADD_DEPT_ORG_OK") {
		} else {
			assemblys.alert("操作失败");
		}
	}
	function cancelDeptBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "DEL_DEPT_ORG_OK") {
		} else {
			assemblys.alert("操作失败");
		}
	}
	//排除页
	function goExclude(flags, flagId, compNo) {
		var userId = "", roleId = "", funId = "", orgType = "", app_LIST = "", appId = "";
		funId = document.getElementById("funId").value;
		app_LIST = document.getElementById("flag").value;
		appId = document.getElementById("appId").value;
		if (app_LIST == "false") {
			userId = document.getElementById("userId").value;
		} else {
			roleId = document.getElementById("roleId").value;
		}
		if (flags == "DEPT") {
			orgType = "1";
			var url_pop = baseContext + "exclude.spring?app_LIST=" + app_LIST + "&flag=DEPT&flagId=" + flagId + "&userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&appId=" + appId;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '排除科室',
				scrollbar : false,
				area : [ '850px', '430px' ],
				content : url_pop,
				end : function() {
					flushOrg();
				}
			});
		} else {
			orgType = "0";
			var url_pop = baseContext + "exclude.spring?app_LIST=" + app_LIST + "&flag=EMP&flagId=" + flagId + "&userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo + "&appId=" + appId;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '排除科室',
				scrollbar : false,
				area : [ '850px', '400px' ],
				content : url_pop,
				end : function() {
				}
			});
		}
	}
	//排除医院
	function exclude(obj, compNo) {
		var userId = "", roleId = "", funId = "", orgType = "2", flagId = "", flag = "COMP", app_LIST = "";
		funId = document.getElementById("funId").value;
		app_LIST = document.getElementById("flag").value;
		userId = (app_LIST == "false") ? document.getElementById("userId").value : userId;
		roleId = (app_LIST == "true") ? document.getElementById("roleId").value : roleId;
		if (obj.checked) {
			var method = "GET";
			var url = baseContext + "exconText.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo + "&flagId=" + flagId + "&flag=" + flag + "&app_LIST=" + app_LIST;
			var content = null;
			var responseType = "text";
			var callback = excoBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		} else {
			assemblys.confirm("取消排除此医院？", function() {
				var method = "GET";
				var url = baseContext + "excanText.spring?userId=" + userId + "&roleId=" + roleId + "&funId=" + funId + "&orgType=" + orgType + "&compNo=" + compNo + "&flagId=" + flagId + "&flag=" + flag + "&app_LIST=" + app_LIST;
				var content = null;
				var responseType = "text";
				var callback = excaBack;
				$.ajax({
					"url" : url,
					"type" : method,
					"data" : content,
					"dataType" : responseType,
					"success" : callback,
					"error" : function(e) {
						assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
					}
				});
			}, function() {
				obj.checked = true;
				layui.form.render(); //更新全部
			});
		}
	}
	function excoBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "ADD_EX_ORG_OK") {
			flushOrg();
		} else {
			assemblys.alert("操作失败");
		}
	}
	function excaBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "DEL_EX_ORG_OK") {
			flushOrg();
		} else {
			assemblys.alert("操作失败");
		}
	}
	
	// 复制组织架构
	function copyOrgRights() {
		
		var url = basePath + "frame/aas/selectAppFuncList.jsp?1=1";
		url += "&appID=" + $("#appId").val();
		url += "&roleID=" + $("#roleId").val();
		url += "&userID=" + $("#userId").val();
		url += "&formFunID=" + $("#funId").val();
		url += "&hasAllRight=" + hasAllRight;
		
		layer.open({
			content : url,
			type : 2,
			skin : 'layui-layer-aems',
			title : "选择应用功能",
			scrollbar : false,
			area : [ '80%', '90%' ]
		});
	
	}

</script>
<style type="text/css">
.main_table td {
	padding: 10px 10px;
}
</style>
</head>
<body>
	<form action="" name="mainForm" method="post" class="layui-form">
		<input type="hidden" id="userId" name="userId" value="<c:out value="${userId}"/>">
		<%-- 用户ID --%>
		<input type="hidden" id="roleId" name="roleId" value="<c:out value="${roleId}"/>">
		<%-- 角色ID --%>
		<input type="hidden" id="appId" name="appId" value="<c:out value="${appId}"/>">
		<%-- 系统ID --%>
		<input type="hidden" id="flag" name="flag" value="<c:out value="${App_LIST}"/>">
		<%-- 1\2树，true表示角色，false表示用户 --%>
		<input type="hidden" id="funId" name="funId" value="<c:out value="${funId}"/>">
		<%-- 功能点ID --%>
		<input type="hidden" id="funName" name="funName" value="<c:out value="${funName}"/>">
		<%-- 功能点名称 --%>
		<input type="hidden" id="orgType" name="orgType" value="<c:out value="${orgType}"/>">
		<%-- 单选 --%>
		<input type="hidden" id="empName" name="empName" value="<c:out value="${empName}"/>">
		<%-- 用户名称 --%>
		<input type="hidden" id="roleName" name="roleName" value="<c:out value="${roleName}"/>">
		<div class="head0">
			<div class="head0_right fr" style="height: 38px; line-height: 38px;">
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" style="margin-top: 5px;" onclick="copyOrgRights();" value="组织架构克隆">
			</div>
		</div>
		<div class="bodys">
			<table id="TTBB" class="layui-table main_table" border="1" cellspacing="0" cellpadding="0" rules="cols">
				<tr>
					<td align="left" valign="center" style="background-color: #f9f9f9; padding: 3px 3px 3px 30px;" colspan="2">
						<c:if test="${hasAllRight }">
							<input type="radio" onclick='catchThat(this)' name="radioType" id="radioType" value="group" title="集团">
						</c:if>
						<input type="radio" onclick='catchThat(this)' name="radioType" id="radioType" value="comp" title="本医院">
						<input type="radio" onclick='catchThat(this)' name="radioType" id="radioType" value="dept" title="本科室">
						<input type="radio" onclick='catchThat(this)' name="radioType" id="radioType" value="one" title="自定义">
					</td>
					<td align="left" style="background-color: #f9f9f9; padding: 9px;">
						<i onclick='showAllThat(this)' dataValue="show" style="cursor: pointer" class="layui-icon layui-icon-down i_check" border="0" width="16" height="16" align="absmiddle" title="展开全部"></i>
						<input type="checkbox" lay-skin="primary" onclick='showHasDeptsComp()' id="chkHasDeptsComp" title="仅显示有科室权限的医院" />
						<input type="checkbox" lay-skin="primary" onclick='showHasDepts()' id="chkHasDepts" title="已分配的科室高亮显示" />
					</td>
				</tr>
				<c:set var="p_iCounts" value="${0}" />
				<%-- 是否医院不为空 --%>
				<c:set var="p_iCounts2" value="${0}" />
				<%-- 是否可以关闭科室tr --%>
				<c:set var="p_iCounts3" value="${0}" />
				<%-- 此前用于没5个科室换一行，现在不使用 --%>
				<c:set var="p_iCounts4" value="${0}" />
				<%-- 医院流水号 --%>
				<c:set var="hasDepts" value="${0}" />
				<%-- 有权限的科室个数 --%>
				<c:set var="all_comp" value="${0}" />
				<c:set var="ex_comp" value="${0}" />
				<c:set var="aa_table" value="${0}" />
				<%-- 科室table标志 --%>
				<c:set var="aa_tr" value="${0}" />
				<%-- 科室tr标志 --%>
				<c:set var="aa_td" value="${0}" />
				<%-- 科室td标志 --%>
				<%-- 偶数行 class="TrList0"，奇数行 class="TrList1" --%>
				<%-- orgList循环 --%>
				<c:forEach items="${orgList}" var="element" varStatus="vs">
					<c:if test="${!empty element.compName}">
						<input type="hidden" id="hasDepts<c:out value="${p_iCounts4}"/>" value="<c:out value="${hasDepts}"/>" title="用于判断某医院下是否有科室被勾选" />
						<c:set var="p_iCounts" value="${1}" />
						<c:set var="p_iCounts3" value="${0}" />
						<c:set var="p_iCounts4" value="${p_iCounts4 + 1}" />
						<c:set var="hasDepts" value="${0}" />
					</c:if>
					<c:if test="${empty element.compName}">
						<c:set var="p_iCounts" value="${0}" />
					</c:if>
					<c:if test="${p_iCounts == 1}">
						<%-- 医院不为空，此时处理医院tr --%>
						<c:if test="${aa_tr==1 }">
							<%-- 循环到下一个医院后，关闭科室tr标志 --%>
							<c:set var="aa_td" value="${0}" />
							<c:set var="aa_tr" value="${0}" />
							<c:if test="${aa_td<=3 }">
								<td width="25%" colspan="${4-aa_td}" style="border: 1px solid lightgray;"></td>
							</c:if>
							</tr>
						</c:if>
						<c:if test="${aa_table==1 }">
							<%-- 循环到下一个医院后，关闭科室table标志 --%>
							<c:set var="aa_table" value="${0}" />
			</table>
			</c:if>
			<c:if test="${p_iCounts2 == 1}">
				<%-- 关闭科室tr --%>
				</td>
				</tr>
			</c:if>
			<%-- 权限医院 --%>
			<c:if test="${fn:contains(compNos,element.compNo) || hasAllRight}">
				<tr id="compTd<c:out value="${p_iCounts4}"/>" class="TrTitle">
					<td align="left" onclick='showThat(<c:out value="${p_iCounts4}"/>)' style="cursor: pointer">
						<font color="darkblue">
							<b>
								<c:out value="${element.compName}" />
							</b>
						</font>
						<img id="imgHasDepts<c:out value="${p_iCounts4}"/>" style="display: none" src="${basePath}/frame/images/default/ico/enabledco_0.gif" border="0" width="16" height="16" align="absmiddle" title="该医院下有科室权限" />
					</td>
					<td align="left">
						&nbsp;&nbsp;
						<c:if test="${App_LIST == 'true'}">
							<%-- 角色 --%>
							<c:if test="${element.roleIsExclude == '1'}">
								<input type="checkbox" lay-skin="primary" disabled="disabled" title="所有科室">
							</c:if>
							<c:if test="${element.roleIsExclude == '0'}">
								<c:if test="${element.roleRight == '1'}">
									<input type="checkbox" lay-skin="primary" checked="checked" compNo="<c:out value="${element.compNo}"/>" compName="<c:out value="${element.compName}"/>" onclick='allDept(this)' title="所有科室">
								</c:if>
							</c:if>
						</c:if>
						<c:if test="${App_LIST == 'false'}">
							<%-- 用户 --%>
							<c:if test="${element.roleRight == '1'}">
								<c:if test="${element.roleIsExclude == '0'}">
									<c:set var="ex_comp" value="${0}" />
									<input type="checkbox" lay-skin="primary" disabled="disabled" checked="checked" title="所有科室">
								</c:if>
								<c:if test="${element.roleIsExclude == '1'}">
									<c:set var="ex_comp" value="${1}" />
									<input type="checkbox" lay-skin="primary" disabled="disabled" title="所有科室">
								</c:if>
							</c:if>
						</c:if>
						<c:if test="${element.roleRight == '0'}">
							<c:if test="${element.userRight == '0'}">
								<c:set var="all_comp" value="${0}" />
								<input type="checkbox" lay-skin="primary" compNo="<c:out value="${element.compNo}"/>" compName="<c:out value="${element.compName}"/>" onclick='allDept(this)' title="所有科室">
							</c:if>
							<c:if test="${element.userRight == '1'}">
								<c:set var="all_comp" value="${1}" />
								<c:if test="${element.userIsExclude == '1'}">
									<input type="checkbox" lay-skin="primary" disabled="disabled" compNo="<c:out value="${element.compNo}"/>" compName="<c:out value="${element.compName}"/>" onclick='allDept(this)' title="所有科室">
								</c:if>
								<c:if test="${element.userIsExclude == '0'}">
									<input type="checkbox" lay-skin="primary" checked="checked" compNo="<c:out value="${element.compNo}"/>" compName="<c:out value="${element.compName}"/>" onclick='allDept(this)' title="所有科室">
								</c:if>
							</c:if>
						</c:if>
					</td>
					<td align="left">
						<i onclick='showThat(<c:out value="${p_iCounts4}"/>)' class="layui-icon layui-icon-down i_check" border="0" width="16" height="16" align="absmiddle" title="展开科室"></i>
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
			</c:if>
			<tr id="deptTd<c:out value="${p_iCounts4}"/>" class="TrList1" style="display: none;">
				<td colspan="3">
					<c:set var="p_iCounts2" value="${1}" />
			</c:if>
			<c:if test="${p_iCounts == 0}">
				<%-- 医院为空，开始处理科室tr --%>
				<c:set var="p_iCounts3" value="${p_iCounts3 + 1}" />
				<%--								<c:if test="${p_iCounts3 == 5 }"><br><c:set var="p_iCounts3" value="${0}"/></c:if>--%>
				<c:if test="${aa_table==0 }">
					<%-- 科室table标志 --%>
					<c:set var="aa_table" value="${1}" />
					<table style="width: 100%; border: 2px solid lightgray; margin: 10px 0px;">
						</c:if>
						<c:if test="${aa_tr==0 }">
							<%-- 科室tr标志 --%>
							<c:set var="aa_tr" value="${1}" />
							<tr>
						</c:if>
						<c:set var="aa_td" value="${aa_td+1}" />
						<%-- 科室td标志 --%>
						<td width="25%" style="border: 1px solid lightgray;">
							<%-- 用户 --%>
							<c:if test="${App_LIST == 'false'}">
								<c:if test="${element.deptName != ''}">
									<span style="width: 200px; vertical-align: top">
										<%-- 显示科室名称span --%>
										<c:if test="${element.roleRight == '1'}">
											<input type="checkbox" <c:if test="${ex_comp == 0}">checked="checked"</c:if> disabled="disabled"
										</c:if>
										<c:if test="${element.roleRight == '0'}">
											<c:if test="${element.userRight == '0'}">
												<input type="checkbox" compNo="<c:out value="${element.compNo}"/>" deptId="<c:out value="${element.deptId}"/>" deptName="<c:out value="${element.deptName}"/>" onclick='oneDept(this)'
											</c:if>
											<c:if test="${element.userRight == '1'}">
												<c:set var="hasDepts" value="${hasDepts+1}" />
												<input type="checkbox" <c:if test="${all_comp == 1}">disabled="disabled"</c:if> checked="checked" style="border: 1px solid #1e5180; outline: 1px solid #1e5180;" compNo="<c:out value="${element.compNo}"/>" deptId="<c:out value="${element.deptId}"/>" deptName="<c:out value="${element.deptName}"/>" onclick='oneDept(this)'
											</c:if>
										</c:if>
										lay-skin="primary" title="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
										<c:out value="${element.deptName}" />
										" >
										<%-- 											【<c:out value="${element.deptName}"/>】</font>&nbsp;&nbsp; --%>
									</span>
								</c:if>
							</c:if>
							<%-- 角色 --%>
							<c:if test="${App_LIST=='true'}">
								<c:if test="${element.deptName != ''}">
									<div style="vertical-align: top;">
										<%-- 显示科室名称span --%>
										<input type="checkbox" <c:if test="${element.roleRight == '1'}">
													<c:set var="hasDepts" value="${hasDepts+1}"/>
													checked="checked" style="border:1px solid #1e5180; outline: 1px solid #1e5180;"
												</c:if> compNo="<c:out value="${element.compNo}"/>" deptId="<c:out value="${element.deptId}"/>" deptName="<c:out value="${element.deptName}"/>" onclick='oneDept(this)' lay-skin="primary"
											title="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<c:out value="${element.deptName}"/>">
										<%-- 											【<c:out value="${element.deptName}"/>】</font> &nbsp;&nbsp; --%>
									</div>
								</c:if>
							</c:if>
						</td>
						<c:if test="${aa_td==4 }">
							<%-- 科室td标志，四列控制 --%>
							<c:set var="aa_td" value="${0}" />
							<c:set var="aa_tr" value="${0}" />
							</tr>
						</c:if>
						</c:if>
						</c:forEach>
						<%-- orgList循环 end --%>
						<c:if test="${aa_tr==1 }">
							<%-- 总体循环后，关闭科室tr标志 --%>
							<c:set var="aa_td" value="${0}" />
							<c:set var="aa_tr" value="${0}" />
							<c:if test="${aa_td<=3 }">
								<td width="25%" colspan="${4-aa_td}" style="border: 1px solid lightgray;"></td>
							</c:if>
							</tr>
						</c:if>
						<c:if test="${aa_table==1 }">
							<%-- 总体循环后，关闭科室table标志 --%>
							<c:set var="aa_table" value="${0}" />
					</table>
				</c:if>
				</table>
		</div>
		<input type="hidden" id="hasDepts<c:out value="${p_iCounts4}"/>" value="<c:out value="${hasDepts}"/>" title="用于判断最后的那个医院下是否有科室被勾选" />
		<input type="hidden" id="compCount" value="<c:out value="${p_iCounts4}"/>" title="医院数量" />
	</form>
</body>
<script>
	layui.use([ 'form','laypage' ], function() {
	
		//form对象，layer对象(这里用不上)
		var form = layui.form, layer = layui.layer;
		
		form.on('checkbox', function(data) {
			data.elem.onclick();
		});
		form.on('radio', function(data) {
			data.elem.onclick();
		});
	});	
</script>
</html>
