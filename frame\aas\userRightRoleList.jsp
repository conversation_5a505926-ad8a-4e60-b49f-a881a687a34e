<%@ page contentType="text/html; charset=UTF-8" language="java"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<!DOCTYPE html>
<html>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>分配权限-角色列表</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = basePath + "/frame/roleright/";
	
	function slRole(obj) {
		var roleID = $(obj).attr("param1");
		var roleName = $(obj).attr("param2");
		var appID = $(obj).attr("param3");
		var compNo = $(obj).attr("param4");
		parent.param.compNo = compNo;
		parent.doRoleAction1(roleID, roleName, appID);
		assemblys.closeWindow();
	}
</script>
</head>
<body>
	<form action="" method="post">
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2">${empty menuIcon ? '&#xe779;' : menuIcon}</i>
				查找结果--双击选择
			</span>
		</div>
		<div class="bodys">
			<div class="tableDiv table_noTree table_noSearch">
				<!--菜单外框-->
				<table class="layui-table main_table" style="margin-bottom: 0;" cellpadding="0" cellspacing="0">
					<!--标题栏-->
					<tr class=" main_title">
						<td style="width: 200px;">医院</td>
						<td style="width: 200px;">角色名称</td>
					</tr>
					<c:set var="p_iCounts" value="${1}" />
					<c:forEach items="${list}" var="element" varStatus="vs">
						<c:set var="p_iCounts" value="${p_iCounts + 1}" />
						<c:set var="trClass" value="${vs.index%2 eq 0 ? 'comTab_R1' : 'comTab_R2'}"></c:set>
						<tr param1='<c:out value="${element.roleId}"/>' param2='<c:out value="${element.roleName}"/>' param3='<c:out value="${element.appID}"/>' param4='<c:out value="${element.compNo}"/>' class="${trClass}" ondblclick="slRole(this)">
							<td class="comTab_Td" align="center" style="width: 60px;">
								<c:out value="${element.roleType}" />
							</td>
							<td class="comTab_Td" align="left">
								<c:out value="${element.roleName}" />
							</td>
						</tr>
					</c:forEach>
				</table>
				<!--说明-->
				<div class="comTab_Sn">
					<div>【说明】</div>
					<div class="comTab_SnTxt">
						<li class="comTab_SnLi">
							该列表
							<span style="color: red">只列出前10条记录</span>
							,如没有查询到建议录入更精确的查询条件
						</li>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
<script>
	layui.use([ 'form', 'laypage' ], function() {
		//form对象，layer对象(这里用不上)
		var form = layui.form, layer = layui.layer;
	});
</script>
</html>
