<%@ page contentType="text/html; charset=UTF-8" language="java"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>分配权限-用户列表</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/layui/layui.js"></script>
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}/frame/roleright/";
	
	function slUser(obj) {
		
		var userId = $(obj).attr("userId");
		var empNo = $(obj).attr("empNo");
		var empName = $(obj).attr("empName");
		var deptID = $(obj).attr("deptID");
		var compNo = $(obj).attr("compNo");
		
		// 父页面
		parent.param.compNo = compNo;
		if (parent.checkCompApp()) {
			parent.doAction2(userId, empNo, empName, deptID);
			assemblys.closeWindow();
		} else {
			assemblys.msg("当前选择的用户所属医院无该应用权限", null, 5000);
		}
	}
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<!-- 是否重复 -->
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2">${empty menuIcon ? '&#xe779;' : menuIcon}</i>
				查找结果--双击选择
			</span>
		</div>
		<div id="comInfoPrompt"></div>
		<!--菜单外框-->
		<div class="bodys">
			<div class="tableDiv table_noTree table_noSearch">
				<table class="layui-table main_table" style="margin-bottom: 0;" cellpadding="0" cellspacing="0">
					<!--标题栏-->
					<tr class=" main_title">
						<td width="120">医院</td>
						<td width="120">科室</td>
						<td>姓名(编号)</td>
						<td style="min-width: 80px;">状态</td>
					</tr>
					<c:set var="p_iCounts" value="${1}" />
					<c:forEach items="${list}" var="element" varStatus="vs">
						<c:set var="p_iCounts" value="${p_iCounts + 1}" />
						<c:if test="${vs.index%2 eq 0}">
							<tr class="comTab_R1" userId="<c:out value="${element.userId}"/>" empNo="<c:out value="${element.userCode}"/>" empName="<c:out value="${element.userName}"/>" deptID="<c:out value="${element.deptId}"/>" compNo="<c:out value="${element.compNo}"/>" ondblclick="slUser(this)">
						</c:if>
						<c:if test="${vs.index%2 eq 1}">
							<tr class="comTab_R2" userId="<c:out value="${element.userId}"/>" empNo="<c:out value="${element.userCode}"/>" empName="<c:out value="${element.userName}"/>" deptID="<c:out value="${element.deptId}"/>" compNo="<c:out value="${element.compNo}"/>" ondblclick="slUser(this)">
						</c:if>
						<td class="comTab_Td" align="left">
							<c:out value="${element.compName}" />
						</td>
						<td class="comTab_Td" align="left">
							<c:out value="${element.deptName}" />
						</td>
						<td class="comTab_Td" align="center">
							<c:out value="${element.userName}" />
							(
							<c:out value="${element.userCode}" />
							)
						</td>
						<td class="comTab_Td" align="center">
							<c:if test="${element.pauseDate == null}">有效</c:if>
							<c:if test="${element.pauseDate != null}">
								<b style="color: red">停用</b>
							</c:if>
						</td>
						</tr>
					</c:forEach>
				</table>
				<!--说明-->
				<div class="comTab_Sn">
					<div>【说明】</div>
					<div class="comTab_SnTxt">
						<li class="comTab_SnLi">
							该列表
							<span style="color: red">只列出前10条记录</span>
							,如没有查询到建议录入更精确的查询条件
						</li>
					</div>
				</div>
			</div>
		</div>
		</div>
	</form>
</body>
</html>
