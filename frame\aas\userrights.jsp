<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta http-equiv="expires" content="0">
<title>分配角色</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" media="all" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<style type="text/css">
.layui-form-item .layui-input-inline {
	width: 238px;
}
</style>
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}frame/useraction/";
	
	function setRole(data) {
		var userId = document.getElementById("userId").value;
		var userName = document.getElementById("userName").value;
		var method = "GET";
		for (var i = 0; i < data.length; i++) {
			var url = baseContext + "addUserRole.spring?userId=" + userId + "&roleId=" + data[i].value;
			var content = null;
			var responseType = "text";
			var callback = addBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				async : false,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		}
		assemblys.msg("添加角色成功", function() {
			reload();
		});
	}

	function reload() {
		var userName = document.getElementById("userName").value;
		userName = encodeURIComponent(userName);
		location.href = baseContext + "editRole.spring?userId=${userId}&userName=" + userName + "&CompNo=${compNo}&appID=" + $("select[name=appID]").val();
	}

	function delRole(data) {
		var userId = document.getElementById("userId").value;
		var userName = document.getElementById("userName").value;
		var method = "GET";
		for (var i = 0; i < data.length; i++) {
			var url = baseContext + "delUserRole.spring?userId=" + userId + "&roleId=" + data[i].value;
			var content = null;
			var responseType = "text";
			var callback = delBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				async : false,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		}
		assemblys.msg("取消角色成功", function() {
			reload();
		});
	}

	function addBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText != "ADD_ROLE") {
			assemblys.msg("服务器异常");
		}
	}

	function delBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText != "DEL_ROLE") {
			assemblys.msg("服务器异常");
		}
	}

	function getAppList() {
		return $.ajax({
			url : basePath + "frame/comp/getCompAppRight.spring",
			data : {
				"menuRight" : 0
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var appID = "${appID}";
					var exclude = $("#exclude").val();
					var html = "";
					for ( var i in data.appList) {
						if (exclude && exclude == data.appList[i].appID) {
							// 过滤
						} else {
							html += "<option value='" + data.appList[i].appID + "' " + (appID == data.appList[i].appID ? "selected" : "") + ">" + data.appList[i].appName + "</option>";
						}
					}
					$("select[name=appID]").append(html);
					layui.form.render();
					layui.form.on('select(appID)', function(data) {
						getUserRoleData();
					});
				}
			}
		});
	}

	function getUserRoleData() {
		return $.ajax({
			url : basePath + "frame/useraction/getUserRoleData.spring",
			dataType : "json",
			data : {
				userID : $("#userId").val(),
				appID : $("select[name=appID]").val(),
				compNo : $("#compNo").val()
			},
			success : function(data) {
				var bean2 = data.bean2;
				var userRoles = data.userRoles;
				userRoles = userRoles ? userRoles.split(",") : [];
				var data1 = new Array();
				var data2 = userRoles;
				for (var i = 0; i < bean2.length; i++) {
					var bean = {
						value : bean2[i].roleID,
						title : bean2[i].roleName
					};
					data1.push(bean);
				}
				//显示搜索框
				layui.transfer.render({
					elem : '#test4',
					data : data1,
					title : [ '未拥有角色', '已拥有角色' ],
					showSearch : true,
					value : data2,
					onchange : function(data, index) {
						//data得到当前被穿梭的数据
						//如果数据来自左边，index 为 0，否则为 1
						//0添加，1删除
						if (index == 0) {
							setRole(data);
						} else {
							delRole(data);
						}
						
					}
				});
			}
		});
	}
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<input type="hidden" id="userId" name="userId" value="<c:out value="${userId}"/>">
		<input type="hidden" id="userName" name="userName" value="<c:out value="${userName}"/>">
		<input type="hidden" id="compNo" name="compNo" value="<c:out value="${compNo}"/>">
		<input type="hidden" id="exclude" value="<c:out value="${exclude}"/>">
		<div class="bodys bodys_noTop">
			<div class="layui-form-item fr" style="margin-right: 30px;">
				为&nbsp;[
				<font color="highlight">
					<c:out value="${userName}" />
				</font>
				]&nbsp;分配角色
			</div>
			<div style="clear: both"></div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 应用系统 </label>
				<div class="layui-input-inline" style="width: 150px;">
					<select name="appID" lay-filter="appID"></select>
				</div>
			</div>
			<div style="margin-left: 50px" id="test4" class="demo-transfer"></div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form', 'transfer', 'layer', 'util' ], function() {
		var $ = layui.$;
		var form = layui.form;
		form.on('checkbox(checkRole)', function(data) {
			data.elem.onclick();
		});
		
		getAppList().then(function(data) {
			return getUserRoleData();
		});
		
	});
</script>
</html>
