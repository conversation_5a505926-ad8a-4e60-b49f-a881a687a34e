body {
    margin: 0;
    padding: 0 30px 0 50px;
}

#introduction {   
    position: absolute;
    left: 10px;
    top: 50px;
    right: 382px;
}

.introduction {
    position: relative;
    height: 400px;
}

.aboutLogo {
    margin: 0 0 20px 2%;
}

#introduction span, .honor, .link {
    width: 96%;
    display: block;
    margin: 0 auto 20px;
    letter-spacing: 1px;
    line-height: 32px;
    letter-spacing: 3px;
    line-height: 55px;
    font-weight: 700;
}

#introduction p {
    width: 96%;
    text-indent: 2em;
    margin: 0 auto;
    letter-spacing: 5px;
    line-height: 35px;
}

.col-md-3 {
    width: 20%;
    float: left;
}

.qualifyImg {
    width: auto;
    border: none;
}

.qualifyImgBar {
    overflow: hidden;
    text-align: center;
}

.qualifyImgBar img {
    width: 80%;
}

.qualifyImg span {
    display: block;
    text-align: center;
    color: #010101;
    font-size: 14px;
    margin: 5px auto;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.row {
    margin-top: 20px;
}

.row:after, .row:before {
    content: " ";
    display: table;
}

.row:after {
    clear: both;
}

.building {
    position: absolute;
    right: 2%;
    top: 50px;
}

.building_img {
    width: 221px;
}

.link_info {
    width: 544px;
    display: inline-block;
    vertical-align: top;
    padding-left: 2%;
}

.web, .site {
    line-height: 69px;
}

/* .code {
    width: 100px;
    vertical-align: middle;
}

.code_text  {
    vertical-align: middle;
    margin: 0 30px 0 20px;
} */

.code1 {
    width: 100px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 50px;
}

.code2 {
    width: 100px;
}

.codeText {
    display: block;
    text-align: center;
    margin-top: 13px;
}

@media screen and (max-width: 1680px) {
    #introduction {
        right: 306px;
    }
}
@media screen and (max-width: 1440px) {
}
@media screen and (max-width: 1280px) {
}

