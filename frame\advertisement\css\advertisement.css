body {
	display: block !important;
}

body {
	margin: 0;
	padding: 20px 30px 0 50px;
	visibility: visible;
}

.aboutLogo {
	margin: 0 0 20px 2%;
	position: absolute;
}
.titleDiv{
	display: flex; justify-content: center;
}
.title {
	text-align: center;
	font-size: 25px;
	font-weight: 400;
	color: #84888C;
}
.titlePhone {
/*    display: flex;*/
    justify-content: right;
}


.producOutside {
	padding-top: 30px;
	display: flex;
	justify-content:center;
	border: solid 1px #D0D0D0;
    border-left: hidden;
    border-right: hidden;
    border-top: hidden;
}

.producLining {
/*	max-width: 1000px;*/
/*	padding: 5px 0px 20px 0px;*/
}

.producCentre {
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap-reverse;
	margin-top: 10px;
	
	max-width: 925px;
	margin: auto;
}

.productTitle {
	text-align: center;
	font-size: 20px;
	color: #84888C;
	width: 120px;
	line-height: 40px;
	border: solid 1px #D0D0D0;
	border-radius: 5px;
	background-color : white;
	transition: background-color 0.3s ease-in-out;
	margin: 10px;
	
	float: left;
}

.productTitle:hover {
	cursor: pointer;
	background-color: #529BF7;
	color: white;
}
.selctProduct{
	cursor: pointer;
	background-color: #529BF7;
	color: white;
}

.productImg {
	padding-top: 20px;
}

.productImgHide {
	display: none;
}

.productImg img {
	width: 100%;
}
/* ------------------------------- */
 