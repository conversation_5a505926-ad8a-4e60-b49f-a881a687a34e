.solutionContent {
    position: relative;
    right: -30px;
}
.solutionContent {
    background: #fff;
    margin-bottom: 50px;
}

.solutionContent {
    padding: 0 40px;
}
.col-md-9 {
    width: 75%;
}
.col-md-1, .col-md-10, .col-md-11, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9 {
    float: left;
}


.solutionContent h1 {
    color: #000;
    font-weight: bold;
}
.solutionContent h1 {
    font-size: 21px;
    line-height: 25px;
}

.solutionContent p {
    text-align: Justify;
    text-justify: inter-ideograph;
    color: #2d2d2d;
    text-indent: 2em;
}
.solutionContent p {
    font-size: 16px;
    line-height: 25px;
    margin-top: 10px;
    margin-bottom: 15px;
}
.brief {
    width: 100%;
}
.brief {
    padding-top: 20px;
    padding-bottom: 20px;
}

.solutionContent img {
    display: block;
    margin-left: auto;
    margin-right: auto;
}
.solutionContent img {
    width: 80%;
    margin-bottom: 120px;
}

.brief {
    width: 100%;
}
.solutionBigImg, .solutionMiddleImg {
    max-width: auto;
    min-width: auto;
    margin: 20px auto;
}
.brief {
    padding-top: 20px;
    padding-bottom: 20px;
}
img {
    vertical-align: middle;
}

.solutionContent h2 {
    background: #0075CF;
    color: #fff;
    text-align: center;
    display: inline;
    border-radius: 5px;
}
.solutionContent h2 {
    padding: 6px 14px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
}
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
}

.solutionContent p.endP {
    font-size: 14px;
}
.solutionContent p {
    text-align: Justify;
    text-justify: inter-ideograph;
    color: #2d2d2d;
    text-indent: 2em;
}
.solutionContent p {
    font-size: 16px;
    line-height: 25px;
    margin-top: 10px;
    margin-bottom: 15px;
}
body, h1, h2, h3, h4, h5, h6, p, ul, ol, li, form, img, dl, dt, dd, table, tr, td, th, div, span, button, input, label, em {
    font-family: SimSun;
}
body, h1, h2, h3, h4, h5, h6, p, ul, ol, li, form, img, dl, dt, dd, table, tr, td, th, div, span, button, input, label, em {
    margin: 0px;
    border: 0px;
    padding: 0px;
    list-style-type: none;
    font-family: "微软雅黑";
    font-size: 14px;
    color: #000;
    outline: none;
}
dt {
    font-weight: 700;
}
.solutionContent dl dt {
    text-indent: 0;
}
.solutionContent dl dt {
    text-indent: 1.8em;
}
.solutionContent dl dt, .solutionContent dl dd {
    color: #2d2d2d;
}
.solutionContent dl dt {
    font-size: 18px;
    line-height: 30px;
    margin-top: 30px;
}
.solutionContent dl dd {
    margin-bottom: 10px;
}
.solutionContent dl dd {
    text-indent: 2em;
}
.solutionContent dl dd {
    font-size: 16px;
    line-height: 30px;
}
.solutionContent dl dd {
    margin-bottom: 10px;
}

.solutionContent dl dd {
    text-indent: 2em;
}
.solutionContent dl dt, .solutionContent dl dd {
    color: #2d2d2d;
}
.solutionContent dl dd {
    font-size: 16px;
    line-height: 30px;
}
body, h1, h2, h3, h4, h5, h6, p, ul, ol, li, form, img, dl, dt, dd, table, tr, td, th, div, span, button, input, label, em {
    font-family: SimSun;
}

.solutionContent dl dt, .solutionContent dl dd {
    color: #2d2d2d;
}