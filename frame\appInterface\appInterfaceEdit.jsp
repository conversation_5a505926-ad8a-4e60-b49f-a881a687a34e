<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=GBK" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>应用接口管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var hasSubmit = false;
</script>
<style type="text/css">
.container1, .container2, .external {
	display: none;
}
</style>
</head>
<body>
	<form id="form1" class="layui-form" onsubmit="return false;">
		<input type="hidden" id="appInterfaceID" name="appInterfaceID" value="<c:out value="${appInterfaceID}"/>">
		<input type="hidden" id="forAppCode" value="<c:out value="${appCode}"/>">
		<div class="head0">
			<span class="head1_text fw700"> </span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm btn_save" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys ">
			<div id="warnningMsg" style="font-size: 30px; color: red; text-align: left;" class="layui-form-item"></div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					应用系统
				</label>
				<div class="layui-input-inline">
					<select id="appCode" name="appCode" lay-filter="appCode">
					</select>
				</div>
				<label class="layui-form-label"> 状态 </label>
				<div class="layui-input-inline">
					<input type="radio" name="state" value="1" title="启用" checked="checked" />
					<input type="radio" name="state" value="0" title="停用" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					接口编号
				</label>
				<div class="layui-input-inline">
					<input type="text" id="interfaceCode" name="interfaceCode" limit="200" lay-verify="required|limitc" class="layui-input" />
				</div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					接口名称
				</label>
				<div class="layui-input-inline">
					<input type="text" name="interfaceName" limit="200" lay-verify="required|limitc" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					接口标识
				</label>
				<div class="layui-input-inline">
					<input type="text" name="interfaceTitle" value="<c:out value="${interfaceTitle}"/>" limit="200" lay-verify="required|limitc" class="layui-input" />
				</div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					版本号
				</label>
				<div class="layui-input-inline">
					<input type="text" name="version" value="--" limit="200" lay-verify="required|limitc" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 接口支持 </label>
				<div class="layui-input-inline" style="padding-top: 0px;">
					<input type="checkbox" name="supportType" value="1" title="前端" lay-filter="supportType" />
					<input type="checkbox" name="supportType" value="2" title="后端" lay-filter="supportType" />
				</div>
				<label class="layui-form-label"> 接口级别 </label>
				<div class="layui-input-inline">
					<select name="level">
						<option value="1">一级</option>
						<option value="2">二级</option>
						<option value="3">三级</option>
						<option value="-1">特殊</option>
					</select>
				</div>
			</div>
			<fieldset class="layui-elem-field container1 ">
				<legend>前端配置</legend>
				<div class="layui-field-box">
					<div class="layui-form-item">
						<label class="layui-form-label"> 请求方式 </label>
						<div class="layui-input-inline">
							<select name="method">
								<option value="unknown">无</option>
								<option value="GET">GET</option>
								<option value="POST">POST</option>
								<option value="JS">JS引入</option>
							</select>
						</div>
						<label class="layui-form-label"> 调用地址 </label>
						<div class="layui-input-inline">
							<input class="layui-input" type="text" id="mappingURL" name="mappingURL" value="" lay-verify="limitc|charck" limit="200" autocomplete="off">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label"> 返回类型 </label>
						<div class="layui-input-inline">
							<input type="radio" name="returnType" value="unknown" title="无" checked="checked" />
							<input type="radio" name="returnType" value="JSON" title="JSON" />
							<input type="radio" name="returnType" value="HTML" title="HTML" />
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">接口描述 </label>
					<div class="layui-input-block">
						<textarea style="height: 450px; width: 850px;" id="remark" name="remark" cols="500" rows="3"  class="layui-textarea"></textarea>
					</div>
				</div>
			</fieldset>
			<fieldset class="layui-elem-field container1">
				<legend style="color: red;">前端接口测试</legend>
				<div class="layui-field-box">
					<div class="layui-collapse" lay-accordion="">
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">
								<strong>接口调试 ①</strong>
								- 【接口返回类型为： JSON】
								<i class="layui-icon layui-colla-icon"></i>
							</h2>
							<div class="layui-colla-content">
								<blockquote class="layui-elem-quote layui-text">
									<strong style="color: #009100;">说明</strong>
									<br>
									实际调用接口时，通过BasePath + 【映射地址】模式进行调试，具体入参，请查看【接口描述】。
								</blockquote>
								<div class="layui-form-item">
									<label class="layui-form-label">入参 (param)</label>
									<div class="layui-input-block">
										<textarea id="testParam1" cols="500" rows="3" class="layui-textarea" placeholder="key1=value1&key2=value2" title="key1=value1&key2=value2"></textarea>
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">返参 (result)</label>
									<div class="layui-input-block">
										<textarea style="height: 200px;" id="testResult1" readonly="readonly" cols="500" rows="3" class="layui-textarea" placeholder="这里将会返回接口调用的结果"></textarea>
									</div>
								</div>
								<div class="layui-form-item">
									<div class="layui-input-inline">
										<input type="button" class="layui-btn layui-btn-sm btn_save" value="调试" onclick="pubOpt.testAppInterface1()" />
									</div>
								</div>
							</div>
						</div>
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">
								<strong>接口调试 ②</strong>
								- 【接口返回类型为：HTML】
								<i class="layui-icon layui-colla-icon"></i>
							</h2>
							<div class="layui-colla-content">
								<blockquote class="layui-elem-quote layui-text">
									<strong style="color: #009100;">说明</strong>
									<br>
									实际调用接口时，通过BasePath + 【映射地址】模式进行调试，具体入参，请查看【接口描述】。
								</blockquote>
								<div class="layui-form-item">
									<label class="layui-form-label">入参 (param)</label>
									<div class="layui-input-block">
										<textarea id="testParamForHtml1" cols="500" rows="3" class="layui-textarea" placeholder="key1=value1&key2=value2" title="key1=value1&key2=value2"></textarea>
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">返参 (result)</label>
									<div class="layui-input-block">
										<input type="text" readonly="readonly" value="返回HTML的结果将通过新窗口打开" class="layui-input">
									</div>
								</div>
								<div class="layui-form-item">
									<div class="layui-input-inline">
										<input type="button" class="layui-btn layui-btn-sm btn_save" value="调试" onclick="pubOpt.testAppInterfaceForHtml1()" />
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</fieldset>
			<fieldset class="layui-elem-field container2 ">
				<legend>后端配置</legend>
				<div class="layui-field-box">
					<div class="layui-form-item">
						<label class="layui-form-label"> 接口类 </label>
						<div class="layui-input-inline">
							<input type="text" id="interfaceClass" name="interfaceClass" value="" limit="200" lay-verify="limitc" class="layui-input" />
						</div>
						<label class="layui-form-label"> 接口函数 </label>
						<div class="layui-input-inline">
							<input type="text" id="interfaceMethod" name="interfaceMethod" value="" limit="200" lay-verify="limitc" class="layui-input" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label"> 返回类型 </label>
						<div class="layui-input-inline">
							<input type="radio" name="returnType2" value="unknown" title="无" checked="checked" />
							<input type="radio" name="returnType2" value="JSON" title="JSON" />
							<input type="radio" name="returnType2" value="Object" title="Object" />
						</div>
						<label class="layui-form-label"> 接口范围支持</label>
						<div class="layui-input-inline">
							<input type="radio" name="scope" value="inside" title="内部" lay-filter="scope" checked="checked" />
							<input type="radio" name="scope" value="external" title="外部" lay-filter="scope" />
						</div>
					</div>
					<div class="layui-form-item external ">
						<label class="layui-form-label"> 解耦接口类 </label>
						<div class="layui-input-inline">
							<input type="text" name="decouplingClass" value="" limit="200" lay-verify="limitc" class="layui-input" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">接口描述 </label>
						<div class="layui-input-block">
							<textarea style="height: 450px; width: 850px;" id="remark2" name="remark2" cols="500" rows="3" class="layui-textarea"></textarea>
						</div>
					</div>
				</div>
			</fieldset>
			<fieldset class="layui-elem-field container2">
				<legend style="color: red;">后端接口说明</legend>
				<div class="layui-field-box">
					<div class="layui-collapse" lay-accordion="">
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">
								<strong>接口说明 ①</strong>
								- 【内部注入】
								<i class="layui-icon layui-colla-icon"></i>
							</h2>
							<div class="layui-colla-content">
								<blockquote class="layui-elem-quote layui-text">
									<strong style="color: #009100;">调用者将直接在调用层，动态注入【接口类】后，通过【接口类.接口函数】的形式进行调用。（详情请移至【调用说明】）</strong>
								</blockquote>
							</div>
						</div>
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">
								<strong>接口说明 ②</strong>
								- 【外部动态注入】
								<i class="layui-icon layui-colla-icon"></i>
							</h2>
							<div class="layui-colla-content">
								<blockquote class="layui-elem-quote layui-text">
									<strong style="color: #009100;">需提供一个外部接口实现类实现【ExternalApiInterface】后重写invoke的方法，来定制接口的处理逻辑。（详情请移至【调用说明】）</strong>
								</blockquote>
							</div>
						</div>
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">
								<strong>接口说明 ③</strong>
								- 【授权码测试】
								<i class="layui-icon layui-colla-icon"></i>
							</h2>
							<div class="layui-colla-content">
								<blockquote class="layui-elem-quote layui-text">
									<strong style="color: #009100;">撰写【ExternalApiInterface】后，只需要调用者获取【授权码】访问应用公用接口【/frame/api/getApiInterface.spring】即可</strong>
								</blockquote>
							</div>
						</div>
					</div>
				</div>
			</fieldset>
			<div class="comTab_Sn">
				<div class="comTab_SnTxt">
					<li class="comTab_SnLi" style="margin-left: 10px;">
						<strong>字段说明:</strong>
					</li>
					<br>
					<li class="comTab_SnLi" style="margin-left: 20px;">
						<strong>应用系统：</strong>
						接口属于哪个系统提供
					</li>
					<li class="comTab_SnLi" style="margin-left: 20px;">
						<strong>状&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;态：</strong>
						用于判断当前接口是否能使用状态
					</li>
					<li class="comTab_SnLi" style="margin-left: 20px;">
						<strong>接口编号：</strong>
						定义唯一性，并且对应接口类调用的方法名
					</li>
					<li class="comTab_SnLi" style="margin-left: 20px;">
						<strong>接口名称：</strong>
						用于简述接口的作用
					</li>
					<li class="comTab_SnLi" style="margin-left: 20px;">
						<strong>接口支持：</strong>
						接口是否前后端调用
					</li>
					<li class="comTab_SnLi" style="margin-left: 20px;">
						<strong style="color: red;">前端配置：</strong>
						<ul>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>请求方式：</strong>
								该接口是否属于GET请求还是POST
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>调用地址：</strong>
								前端接口的调用地址
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>返回类型：</strong>
								一般如果是JSON，都是Ajax发起，如果是HTML一般情况都是用嵌套的Iframe的Src属性实现
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>接口描述：</strong>
								多数时候，用于开发者说明接口【入参】等其他注意事项，注意明确的入参的顺序
							</li>
						</ul>
					</li>
					<li class="comTab_SnLi" style="margin-left: 20px;">
						<strong style="color: red;">后端配置：</strong>
						<ul>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>接口类：</strong>
								对应后台哪个Service类，如果需要支持后台调用，一般采用注入的模式实现调用
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>函数名称：</strong>
								通过Service类中调用哪个方法，例如commonServcie.getDeptList中就定义getDeptList为函数名
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>返回类型：</strong>
								一般情况都是返回JSON，有些接口是返回List或者实体类
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>接口范围支持：</strong>
								内部：一般只支持场景1️的使用，外部：需要动态注入，并且第三方调用的区别，就是基于外部的基础上做一个授权。
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>解耦接口类：</strong>
								如果是外部应用接口，必须由开发者写一个接口实现类，来用于代码层解耦。
							</li>
							<li class="comTab_SnLi" style="margin-left: 20px;">
								<strong>接口描述：</strong>
								多数时候，用于开发者说明【后端调用接口】等其他注意事项，注意明确的入参的顺序
							</li>
						</ul>
					</li>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/appInterface/js/appInterfaceEdit.js?ver=1.6"></script>
<script type="text/javascript" src="${basePath}plugins/fileUpload/ueditor.config.js"></script>
<script type="text/javascript" src="${basePath}plugins/fileUpload/ueditor.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/fileUpload/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript" src="${basePath}plugins/fileUpload/pubUploader.js"></script>
