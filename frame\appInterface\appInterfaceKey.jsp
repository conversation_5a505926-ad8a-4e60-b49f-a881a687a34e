<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<jsp:include page="../page/menuIcon.jsp"><jsp:param value="<%=BaseConstant.FUN_CODE_APP_INTERFACE_MANAGE%>" name="funCode" /></jsp:include>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>接口调用说明</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
</script>
<style type="text/css">
.remark {
	display: none;
}
</style>
</head>
<body>
	<form id="form1" class="layui-form" onsubmit="return false;">
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">接口授权码</label>
				<div class="layui-input-inline">
					<textarea id="remark1" cols="500" rows="3" lay-verify="limitc|charck" style="width: 380px;" limit="2000" class="layui-textarea"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> </label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm btn_save" value="解析" onclick="pubAppInterface.parseKey()" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">解析结果</label>
				<div class="layui-input-inline">
					<textarea id="remark2" readonly="readonly" cols="500" rows="3" lay-verify="limitc|charck" style="width: 380px;" limit="2000" class="layui-textarea"></textarea>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}frame/appInterface/js/appInterfaceKey.js"></script>

