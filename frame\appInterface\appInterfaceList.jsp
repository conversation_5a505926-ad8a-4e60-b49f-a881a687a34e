<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_APP_INTERFACE_MANAGE);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>应用接口管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var funCode = "${funCode}";
	// 防止重复提交
	var hasSubmit = false;
</script>
<style type="text/css">
.comTab_SnLi {
	margin-top: 2px;
}
</style>
</head>
<body>
	<form id="form1" name="form1" class="layui-form" onsubmit="return false;">
		<input type="text" id="copyBtn" value="" style="position: fixed; top: 0px;">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr">
				<span style="color: red;">注：双击行可直接查看接口信息</span>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28 skin-btn-minor" onclick="pubAppInterface.openRemark3();" value="接口分级" />
				<input type="button" class="layui-btn layui-btn-sm h28 lh28 skin-btn-minor" onclick="pubAppInterface.openRemark();" value="我要开发接口" />
				<input type="button" class="layui-btn layui-btn-sm h28 lh28 skin-btn-minor" onclick="pubAppInterface.openRemark2();" value="我要使用接口" />
				<label class="layui-form-label2">应用系统</label>
				<div class="layui-input-inline">
					<select id="appCode" lay-filter="appCode">
					</select>
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="pubAppInterface.editAppInterface();" value="新增" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">关键字</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" id="keyword" name="keyword" value="" autocomplete="off" placeholder="接口编号/接口名称" title="接口编号/接口名称" class="layui-input">
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="查询" onclick="pubAppInterface.findAppInterfaceListData();">
				<label class="layui-form-label2">版本号</label>
				<div class="layui-input-inline" style="width: 80px;">
					<select id="version" lay-filter="version">
					</select>
				</div>
				<label class="layui-form-label2">接口级别</label>
				<div class="layui-input-inline" style="width: 80px;">
					<select id="level" lay-filter="level">
						<option value="">全部</option>
						<option value="1">一级</option>
						<option value="2">二级</option>
						<option value="3">三级</option>
						<option value="-1">特殊</option>
					</select>
				</div>
				<label class="layui-form-label2">状态</label>
				<input type="radio" name="state" value="" title="全部" lay-filter="state" />
				<input type="radio" name="state" value="1" title="启用" lay-filter="state" checked="checked" />
				<input type="radio" name="state" value="0" title="停用" lay-filter="state" />
				<input type="checkbox" name="scope" value="external" title="只看外部接口" lay-filter="scope" lay-skin="primary">
			</div>
			<div class="filter_box fr">
				<div class="inblock filter_item fr" onclick="pubAppInterface.download();">
					<i class="layui-icon2" style="font-size: 16px; padding-top: 4px;" title="授权接口文档">&#xe806;</i>
					文档
				</div>
				<div class="inblock filter_item fr" onclick="pubAppInterface.parseKey();">
					<i class="layui-icon2" style="font-size: 16px;" title="解析接口授权码">&#xe81e;</i>
					解析
				</div>
			</div>
			<div class="layui-row">
				<div class="treeDiv">
					<div class="treeHead">接口标识</div>
					<!-- tree -->
					<ul id="tree" class="tree-table-tree-box"></ul>
				</div>
				<div class="tableDiv">
					<table class="layui-table main_table" cellpadding="0" cellspacing="0">
						<thead>
							<tr id="tr_template" class="layui-hide" title="双击行查看接口信息" ondblclick="$(this).find('[type=find]').click();">
								<td align="center">
									<i class="layui-icon layui-icon-search i_check" type="find" title="详情" style="cursor: pointer"></i>
									<i class="layui-icon layui-icon-edit  i_check" type="edit" title="编辑" style="cursor: pointer"></i>
									<i class="layui-icon layui-icon-delete i_delete" type="del" title="删除" style="cursor: pointer"></i>
									<i class="layui-icon2 i_check" type="sql" title="生成SQL" style="cursor: pointer">&#xea80;</i>
									<i class="layui-icon2 i_check" type="key" title="生成接口授权码" style="cursor: pointer">&#xe81f;</i>
								</td>
								<td align="center"></td>
								<td align="left"></td>
								<td align="left"></td>
								<td align="left"></td>
								<td align="center"></td>
								<td align="center"></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr class="main_title">
								<td width="100">操作</td>
								<td width="60">应用编号</td>
								<td style="min-width: 100px;">接口标识</td>
								<td style="min-width: 150px;">接口编号</td>
								<td style="min-width: 150px;">接口名称</td>
								<td width="100">接口支持</td>
								<td width="60">接口级别</td>
								<td width="70">版本号支持</td>
								<td width="60">状态</td>
							</tr>
						</thead>
						<tbody id="tableView">
						</tbody>
					</table>
					<jsp:include page="/plugins/common/jsp/comLayuiPage.jsp"></jsp:include>
					<br>
					<hr>
					<br>
					<div class="comTab_Sn">
						<div>【开发者说明】</div>
						<br>
						<div class="comTab_SnTxt">
							<li class="comTab_SnLi" style="margin-left: 10px;">
								<strong>如何开发接口？</strong>
								如果你想自己开发应用接口，请移步右上角
								<input type="button" class="layui-btn layui-btn-xs skin-btn-minor" value="我要开发接口">
								学习并了解接口的应用场景
							</li>
							<li class="comTab_SnLi" style="margin-left: 10px;">
								<strong>如何使用接口？</strong>
								使用该模块提供的接口功能前，请移步右上角
								<input type="button" class="layui-btn layui-btn-xs skin-btn-minor" value="我要使用接口">
								学习接口使用场景
							</li>
							<hr>
							<li class="comTab_SnLi" style="margin-left: 10px;">
								<strong>如何获取【前后端接口】使用示例？</strong>
								点击某个接口的
								<i class="layui-icon layui-icon-search i_check" style="cursor: pointer"></i>
								，可以查看接口的调用示例，更多移步
								<input type="button" class="layui-btn layui-btn-xs skin-btn-minor" value="我要使用接口">
							</li>
							<li class="comTab_SnLi" style="margin-left: 10px;">
								<strong>前端接口无法调用时？</strong>
								请尝试 - ajax 增加【 skipDataCheck : true 】, 跳过预处理模式
							</li>
							<hr>
							<li class="comTab_SnLi" style="margin-left: 10px;">
								<strong>如何获取接口授权码？</strong>
								通过点击
								<i class="layui-icon2 i_check" style="font-size: 18px;" title="生成接口授权码">&#xe81f;</i>
								进行生成，只有符合【外部接口】的配置才能生成接口授权码，更多请移步
								<input type="button" class="layui-btn layui-btn-xs skin-btn-minor" value="我要使用接口">
							</li>
							<li class="comTab_SnLi" style="margin-left: 10px;">
								<strong>如何确认接口授权码是否正确？</strong>
								通过点击
								<i class="layui-icon2 i_check" style="font-size: 18px;" title="解析接口授权码">&#xe81e;</i>
								进入页面进行相应操作，即可看解析结果
							</li>
							<li class="comTab_SnLi" style="margin-left: 10px;">
								<strong>第三方外部接口授权统一调用地址</strong>
								【basePath + '/frame/api/getApiInterface.spring'】
							</li>
						</div>
					</div>
					<hr>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/appInterface/js/appInterfaceList.js?Ver=1.8"></script>