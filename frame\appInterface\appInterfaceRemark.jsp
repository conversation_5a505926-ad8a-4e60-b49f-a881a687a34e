<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_APP_INTERFACE_MANAGE);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>接口调用说明</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var funCode = "${funCode}";
</script>
<style type="text/css">
.remark {
	display: none;
}
</style>
</head>
<body style="display: none;">
	<form class="layui-form" onsubmit="return false;">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
		</div>
		<div class="bodys">
			<div class="tableDiv  table_noTree" style="top: 15px;">
				<div class="layui-collapse" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>场景认识</strong>
						</h2>
						<div class="layui-colla-content layui-show">
							<hr>
							<div style="margin: 10px; text-align: center;">
								<img src="${basePath}/frame/appInterface/images/scene.png" style="max-width: 900px;">
							</div>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">场景一：</strong>
								一般集成于1936框架的应用系统，可以直接通过Service注入获取的接口，我们称【内部】接口，非【1936框架】提供的接口都是是外部接口，一般内部接口都是由【1936框架】的开发者去提供。
							</blockquote>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">场景二：</strong>
								<span style="color: red;">所以我们大多数时候都是开发【场景二】的接口</span>
								，作为开发者，例如你跟进的是【不良事件】项目，你要开发一个【上报不良事件】的接口，就要考虑使用方的环境，例如我们的【预警应用】系统检测到数据时，可以自动进行上报，但是实际环境，有【不良事件】不一定会有【预警应用】系统，如果将不良事件的代码如同内部接口那样注入到【预警应用】系统，那【预警应用】系统就无法独立运行，因此外部接口与内部接口的唯一区别，就是做了一层解耦。
							</blockquote>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">场景三：</strong>
								当提供外部接口后，除了【1936框架】的集成系统间能够相互使用以外，我们还可以将该接口提供给第三方系统，例如【HIS】、【护理系统】等，区别在于调用方要做一次授权即可。
							</blockquote>
							<div class="layui-form-item">
								<label class="layui-form-label"></label>
								<div style="text-align: center; font-size: 20px;">
									<a style="color: blue" id="see" onclick="pubAppInterface.isee(this)"> (＠￣ー￣＠) 点我, 查看接口开发说明</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<hr class="remark">
				<div class="layui-collapse remark" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>前端接口说明</strong>
						</h2>
						<div class="layui-colla-content">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>
											前端使用【AJAX】的调用模式，例如我们是【不良事件】，提供一个【获取不良事件字典】给【预警应用】，如果【预警应用】可以通过前端发送请求方式，作为【开发者】就可以考虑提供【前端接口】，
											<span style="color: red;">但是如果提供的接口涉及事务，就不要提供post请求的接口。</span>
										</strong>
									</li>
									<br>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">图例：</strong>
										<blockquote class="layui-elem-quote layui-text">
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/image11.png" style="max-width: 900px;">
											</div>
										</blockquote>
									</li>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">示例：（以下代码是使用者参考用的）</strong>
										<pre class="layui-code">
// 返回JSON形式
$.ajax({
	url : basePath + "frame/dict/getDictByCode.spring",
	type : "get", // 你作为开发者，接口定义请求是啥，这里调用者就要写啥 - 目前只支持 post 和 get
	data : {
	   dictCode : "XXX",
	   appCode  : "XXX",
	},
	dataType : "json",
	success : function(data) {
	  // 结果
	},
	error : function(){
	}
});

// 返回HTML形式
var url = basePath + "接口调用地址";
$("iframe").attr("src",url);
// url可以用新窗口，或者用iframe，一般通过嵌套实现。

							</pre>
									</li>
									<br>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="layui-collapse remark" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>后端接口说明 —— 内部</strong>
						</h2>
						<div class="layui-colla-content ">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>内部接口由【1936框架】的【开发者】定义，底层就是一个Service定义一个方法。</strong>
									</li>
									<br>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">示例：（以下代码是使用者参考用的）</strong>
										<pre class="layui-code"> 
// 注入									
@Autowired
private DictService&lt;?&gt; dictService;

// 调用
dictService.getDictByCode("XXX","XXX","");
										</pre>
									</li>
									<br>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="layui-collapse remark" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>后端接口说明 —— 外部</strong>
							<span style="float: right; color: red;"> 重点</span>
						</h2>
						<div class="layui-colla-content ">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>外部接口主要有一个特点就是【解耦】，下图就是定义一个外部接口的示例，例如我们【持续改进】系统提供了一个【获取PDCA任务】的接口，那么就会有一个对应的【pdcaReportService】类，提供一个【getTask】的方法，该方法在【持续改进】这个系统称做内部接口，如果这个接口要暴露给【不良事件】、【投诉纠纷】那就需要一个解耦的过程。</strong>
									</li>
									<br>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">图例：</strong>
										<blockquote class="layui-elem-quote layui-text">
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/image22.png" style="max-width: 900px;">
											</div>
										</blockquote>
									</li>
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>因此我们要知道如何编写这个【ExternalApiInterface】解耦类，目前的规范，就是用应用编号【appCode】 + 【ExternalApiInterface】来定义，例如不良事件的解耦类就是【AersExternalApiInterface】，持续改进的解耦类则就是【PdcaExternalApiInterface】</strong>
									</li>
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>为了统一解耦的写法规则，【1936框架】提供了【org.hyena.frame.interfaces.ExternalApiInterface】抽象类作为规范</strong>
										<div style="margin: 10px; text-align: center;">
											<img src="${basePath}/frame/appInterface/images/image33.png" style="max-width: 900px;">
										</div>
									</li>
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>我们在对应的应用目录建立【interfaces】的包名，并且新增一个Java类只需要继承【org.hyena.frame.interfaces.ExternalApiInterface】并进行重写【invoke】的方法（下图以PDCA的解耦类为例）</strong>
										<div style="margin: 10px; text-align: center;">
											<img src="${basePath}/frame/appInterface/images/image44.png" style="max-width: 900px;">
										</div>
									</li>
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>然后我们就可以在【invoke】的方法内写我们定义的接口逻辑，【interfaceCode】就是应用接口管理的【接口编号】，【param】就是接口的入参，返参统一返回JSON数据</strong>
									</li>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">示例：（以下代码是使用者参考用的）</strong>
										<pre class="layui-code"> 
// 如果要在不良事件中调用pdca的接口
// 动态注入，这里bean名指向【持续改进】的实现类（实现类是接口提供者去开发）
@Qualifier("PdcaExternalApiInterface")
@Autowired(required = false)
private ExternalApiInterface pdcaApiInterface;

Map&lt;String , Object&gt; param = new HashMap&lt;String , Object&gt;();
if(pdcaApiInterface != null){
   JSONObject resutlJson = pdcaApiInterface.invoke("getTask",param);
}

										
										</pre>
									</li>
									<br>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="layui-collapse remark " lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>接口调用说明 —— 第三方</strong>
						</h2>
						<div class="layui-colla-content ">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>
											给第三方用的接口，只需要完成【外部接口】的开发，第三方的调用形式和外部接口的使用对比，仅仅是多了一步【授权】而已，具体可以查看
											<input type="button" class="layui-btn layui-btn-xs" value="我要使用接口">
											的第三方调用外部接口规则
										</strong>
									</li>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}frame/appInterface/js/appInterfaceRemark.js?ver=5.1.0"></script>
