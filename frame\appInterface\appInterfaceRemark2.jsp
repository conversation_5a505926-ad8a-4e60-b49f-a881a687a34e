<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_APP_INTERFACE_MANAGE);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>接口调用说明</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var funCode = "${funCode}";
</script>
<style type="text/css">
.remark {
	display: none;
}
</style>
</head>
<body style="display: none;">
	<form class="layui-form" onsubmit="return false;">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
		</div>
		<div class="bodys">
			<div class="tableDiv  table_noTree" style="top: 15px;">
				<div class="layui-collapse" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>场景认识</strong>
						</h2>
						<div class="layui-colla-content layui-show">
							<hr>
							<div style="margin: 10px; text-align: center;">
								<img src="${basePath}/frame/appInterface/images/scene.png" style="max-width: 900px;">
							</div>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">场景一：</strong>
								一般集成于1936框架的应用系统，无论【前端 / 后端】都可直接通过ajax访问【调用地址】或 注入【接口类】来进行调用。
							</blockquote>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">场景二：</strong>
								例如【不良事件】开放了上报接口，但是【持续改进】需要调用【不良事件】的接口，考虑集成环境，医院有了【持续改进】，不一定有【不良事件】，因此如果直接在【持续改进】的代码里用【接口类】注入的方式，会导致没有【不良事件】的代码导致编译时报错，而无法运行。因此需要解耦。
							</blockquote>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">场景三：</strong>
								以上两种都属于集成环境的调用方式，第二种比第一种就多了一层【接口解耦】的操作，然而第三种是要实现接口解耦的前提下，并且增加【授权】验证方式，才能提供给外部系统。
							</blockquote>
							<div class="layui-form-item">
								<label class="layui-form-label"></label>
								<div style="text-align: center; font-size: 20px;">
									<a style="color: blue" id="see" onclick="pubAppInterface.isee(this)"> (＠￣ー￣＠) 点我, 查看接口调用说明</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<hr class="remark">
				<div class="layui-collapse remark" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>前端接口说明</strong>
							<span style="float: right; color: red;"> 仅限【场景一、场景二】调用</span>
						</h2>
						<div class="layui-colla-content">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>前端使用【AJAX】的调用模式，目前【应用接口管理】支持返回【JSON】与【HTML】两种格式，具体返回什么，是取决于【接口开发者】</strong>
									</li>
									<br>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">图例：</strong>
										<blockquote class="layui-elem-quote layui-text">
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/image1.png" style="max-width: 900px;">
											</div>
										</blockquote>
									</li>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">示例：</strong>
										<pre class="layui-code">
// 返回JSON形式
$.ajax({
	url : basePath + "frame/dict/getDictByCode.spring",
	type : "get", // 提供方接口定义请求是啥，这里就是啥 - 目前只支持 post 和 get
	data : {
	   dictCode : "XXX",
	   appCode  : "XXX",
	},
	dataType : "json",
	success : function(data) {
	  // 结果
	},
	error : function(){
	}
});

// 返回HTML形式
var url = basePath + "接口调用地址";
$("iframe").attr("src",url);
// url可以用新窗口，或者用iframe，一般通过嵌套实现。

							</pre>
									</li>
									<br>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="layui-collapse remark" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>后端接口说明 —— 内部</strong>
							<span style="float: right; color: red;"> 仅限【场景一】使用</span>
						</h2>
						<div class="layui-colla-content ">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>后台调用内部接口，统一采用【接口类】的Service注入的模式进行调用</strong>
									</li>
									<br>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">图例：</strong>
										<blockquote class="layui-elem-quote layui-text">
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/image2.png" style="max-width: 900px;">
											</div>
										</blockquote>
									</li>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">示例：</strong>
										<pre class="layui-code"> 
// 注入									
@Autowired
private DictService&lt;?&gt; dictService;

// 调用
dictService.getDictByCode("XXX","XXX","");
										</pre>
									</li>
									<br>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="layui-collapse remark" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>后端接口说明 —— 外部</strong>
							<span style="float: right; color: red;"> 仅限【场景二】使用</span>
						</h2>
						<div class="layui-colla-content ">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>后台调用外部接口，统一采用【解耦接口类】的ApiInterface注入的模式进行调用，而不是【接口类】</strong>
									</li>
									<br>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">图例：</strong>
										<blockquote class="layui-elem-quote layui-text">
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/image3.png" style="max-width: 900px;">
											</div>
										</blockquote>
									</li>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<strong style="color: blue;">示例：</strong>
										<pre class="layui-code"> 
// 如果要在不良事件中调用pdca的接口
// 动态注入，这里bean名指向【持续改进】的实现类（实现类是接口提供者去开发）
@Qualifier("PdcaExternalApiInterface")
@Autowired(required = false)
private ExternalApiInterface pdcaApiInterface;

Map&lt;String , Object&gt; param = new HashMap&lt;String , Object&gt;();
if(pdcaApiInterface != null){
   JSONObject resutlJson = pdcaApiInterface.invoke("savePdca",param);
}


// 如果要在不良事件中调用投诉纠纷的接口
// 动态注入，这里bean名指向【投诉纠纷】的实现类
@Qualifier("CdmsExternalApiInterface")
@Autowired(required = false)
private ExternalApiInterface cdmsApiInterface;
Map&lt;String , Object&gt; param = new HashMap&lt;String , Object&gt;();
if(cdmsApiInterface != null){
   JSONObject resutlJson = cdmsApiInterface.invoke("接口编号",param);
}	
										
										</pre>
										<blockquote class="layui-elem-quote layui-text">
											<strong style="color: blue;">动态注入说明：</strong>
											ExternalApiInterface是1936提供的一个外部接口（抽象类），里面有一个invoke函数，例如【持续改进】要提供接口，就需要【接口提供者】在【持续改进】中撰写一个实现类（继承ExternalApiInterface）【 应用名 + ExternalApiInterface 】（按照JAVA命名规范，类名首字母要大写），所有要提供给外部应用系统使用的接口，全部写在ExternalApiInterface实现类中。 【接口调用者】就能在自己系统中，通过【示例】中提供的方式，注入到自己的系统当中。
											<br>
											<br>
											<strong style="color: blue;">问题：</strong>
											<span style="color: red;">如何撰写ExternalApiInterface的实现类？（图例有误差，以Demo实例为准）</span>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/image4.png" style="max-width: 900px;">
											</div>
											<br>
											演示实例：(WebRoot/集成示例/src/org/hyena/demo/interfaces/DemoExternalApiInterface.java)
											<br>
											应用实例：(WebRoot/集成示例/src/org/hyena/demo2/controller/DemoController2.java)
										</blockquote>
									</li>
									<br>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="layui-collapse remark " lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>接口调用说明 —— 第三方</strong>
							<span style="float: right; color: red;"> 仅限【场景三】使用</span>
						</h2>
						<div class="layui-colla-content ">
							<div class="comTab_Sn">
								<div class="comTab_SnTxt">
									<li class="comTab_SnLi" style="margin-left: 10px;">
										<strong>请在使用授权模式调用接口前，需确认一下是否满足调用条件 </strong>
									</li>
									<br>
									<li class="comTab_SnLi" style="margin-left: 20px;">
										<h3 style="color: red;">必备条件：</h3>
										<hr>
										<blockquote class="layui-elem-quote layui-text">
											<strong style="color: blue;">1、接口后端的【接口范围支持】必须是【外部】，因为【内部接口】转【外部接口】需要一个解耦过程（相当于抽象出来一个通用的接口类型），并且还需要一个授权过程（相当于你要把内部接口暴露出去，别人才知道有这个东西）</strong>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/img1.png" style="max-width: 900px;">
											</div>
											<br>
											<strong style="color: blue;">2、接口必须由开发者在本应用系统提供【ExternalApiInterface】的实现类，这一步是给接口做解耦，由【接口提供者】抽象接口类型</strong>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/img2.png" style="max-width: 900px;">
											</div>
											<br>
											<strong style="color: blue;">3、接口授权码，我们通过【接口范围支持】为【外部】类型接口，可以通过小图标直接生成接口授权码（interfaceAuthCode）</strong>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/img3.png" style="max-width: 900px;">
											</div>
											<br>
											<strong style="color: blue;">4、满足以上三种条件后，下一步是获取【永久凭证】，如何获取？</strong>
											<br>
											<span style="margin-left: 15px;">4.1 先前往【授权注册】，根据实际情况把客户端信息（第三方）填入，并且选取第三方要用到的【授权接口】（就是开放给第三方的接口，无关的就不要选中），确认信息无误点击【申请授权】。</span>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/img4.png" style="max-width: 900px;">
											</div>
											<span style="margin-left: 15px; color: red;"> 注意：如果看不见【授权接口】模块，请按键盘的【~】键进行显示，【~】在【Tab】键上方。</span>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/img5.png" style="max-width: 900px;">
											</div>
											<span style="margin-left: 15px;">4.2 再到【授权审核】，确认申请信息无误后，点击【同意授权】。</span>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/img6.png" style="max-width: 900px;">
											</div>
											<span style="margin-left: 15px;">4.3 授权通过后，找到刚刚【已通过】的授权信息，并且点击放大镜进去后，就能下载【永久凭证】。</span>
											<div style="margin: 10px; text-align: center;">
												<img src="${basePath}/frame/appInterface/images/img7.png" style="max-width: 900px;">
											</div>
										</blockquote>
										<br>
										<h3 style="color: red;">如何调用接口：</h3>
										<hr>
										<pre class="layui-code">
										
										
// 后端
由第三方环境决定，参考前端的参数规范用HTTP工具发送即可，这些信息尽量用后端发送，一没有跨域问题，二提高保密性。
										
// 前端
$.ajax({
	url :  basePath + 'frame/api/getApiInterface.spring',
	headers : {
	        "clientid" : "xxxxxxx", 
 	        "accesstoken" : "xxxxxxx", 
 	        "interfaceauthcode" : "根据【应用接口管理】中的【外部】接口小图标生成", 
 	        "opt" : "send", 
	},
	type : "post",
	data : {
		// 入参
	},
	dataType : "json",
	success : function(data) {
	  // 结果
	},
	error : function(){
	}
});


										</pre>
									</li>
									<br>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}frame/appInterface/js/appInterfaceRemark2.js?ver=5.1.0"></script>
