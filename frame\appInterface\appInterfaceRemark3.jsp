<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_APP_INTERFACE_MANAGE);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>接口分级说明</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var funCode = "${funCode}";
	
	$(function() {
		assemblys.getMenuIcon({
			funCode : funCode,
			hasOrg : false,
			dom : $("b#menuIcon"),
			menuName : "接口分级说明"
		});
	});
</script>
<style type="text/css">
.remark {
	display: none;
}
</style>
</head>
<body>
	<form class="layui-form" onsubmit="return false;">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
		</div>
		<div class="bodys">
			<div class="tableDiv table_noTree" style="top: 15px;">
				<div class="layui-collapse" lay-accordion>
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">
							<strong>接口分级</strong>
						</h2>
						<div class="layui-colla-content layui-show">
							<hr>
							<div style="margin: 10px; text-align: center;">
								<img src="${basePath}/frame/appInterface/images/scene2.png" style="max-width: 900px;">
							</div>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">一级接口：</strong>
								前端 / 后端提供的接口，只通过入参返回数据的接口，通常定义为一级接口。
							</blockquote>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">二级接口：</strong>
								开发者基于一级接口调用后，进行二次封装，提供对应的Js、Css路径，使用者通过引入Js和Css来进行调用JS中提供的函数，并且返回HTML的形式。通常定义为二级接口。
							</blockquote>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">三级接口：</strong>
								由开发者定义一个完整的HTML页面，而这个页面基于一级、二级接口组装出来的页面，并且将这个完整的HTML当做一个大型接口提供，通常定义为三级接口。
							</blockquote>
							<blockquote class="layui-elem-quote layui-text">
								<strong style="color: blue;">分级概念：</strong>
								这是一个抽象化的过程，如果开发者定义了一个HTML接口（三级接口），这个接口返回一个数据列表；其他使用者觉得这个HTML接口能满足需求，自己的系统代码就可以按照这个三级接口说明去使用。 如果不满足需求，使用者可以自己定义一个HTML作为容器，去模拟三级接口进行拼凑，用三级接口使用到的一级、二级接口进行组装成自己的个性化页面。如果某些二级接口也不满足，那么同理，使用者可以进行降级使用，通过一级接口返回的数据拼装成自己的页面。
							</blockquote>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
