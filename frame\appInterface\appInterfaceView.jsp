<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=GBK" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>应用接口管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/event-audit.css">
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var hasSubmit = false;
</script>
<style type="text/css">
.beforeEnd, .afterEnd {
	display: none;
}

.tright {
	font-weight: bold;
}

.layui-table td[colspan='3'] {
	word-break: break-all;
	word-wrap: break-word;
	white-space: pre-wrap;
}
</style>
</head>
<body>
	<form id="form1" class="layui-form" onsubmit="return false;">
		<input type="hidden" id="appInterfaceID" name="appInterfaceID" value="<c:out value="${param.appInterfaceID}"/>">
		<div class="layui-tab-content lr_box">
			<div class="void">已停用</div>
			<div class="bodys">
				<ul class="layui-nav layui-nav-tree left" lay-filter="test">
					<li class="layui-nav-item layui-nav-itemed ">
						<a class="main_table_title skin-div-css">接口信息</a>
						<dl class="layui-nav-child main_table_box">
							<dd>
								<table class="layui-table main_table detail_table">
									<tbody>
										<tr>
											<td class="tright">接口标识</td>
											<td class="tleft">
												<div style="color: blue;" interfaceTitle></div>
											</td>
											<td class="tright">接口级别</td>
											<td class="tleft" level></td>
										</tr>
										<tr>
											<td class="tright">应用编号</td>
											<td class="tleft" appCode></td>
											<td class="tright">状态</td>
											<td class="tleft" state></td>
										</tr>
										<tr>
											<td class="tright">接口编号</td>
											<td class="tleft" interfaceCode></td>
											<td class="tright">接口名称</td>
											<td class="tleft" interfaceName></td>
										</tr>
										<tr>
											<td class="tright">接口支持</td>
											<td class="tleft" supportType></td>
											<td class="tright">版本号支持</td>
											<td class="tleft" style="color: green;" version></td>
										</tr>
										<tr>
											<td class="tright">创建人</td>
											<td class="tleft" createUserName></td>
											<td class="tright">创建时间</td>
											<td class="tleft" createDate></td>
										</tr>
										<tr>
											<td class="tright">修改人</td>
											<td class="tleft" optUserName></td>
											<td class="tright">修改时间</td>
											<td class="tleft" optDate></td>
										</tr>
									</tbody>
								</table>
							</dd>
						</dl>
					</li>
					<div class="beforeEnd">
						<li class="layui-nav-item layui-nav-itemed  ">
							<a class="main_table_title skin-div-css">前端配置信息</a>
							<dl class="layui-nav-child main_table_box">
								<dd>
									<table class="layui-table main_table detail_table">
										<tbody>
											<tr>
												<td class="tright">请求方式</td>
												<td class="tleft" method></td>
												<td class="tright">返回类型</td>
												<td class="tleft" returnType></td>
											</tr>
											<tr>
												<td class="tright">调用地址</td>
												<td colspan="3" class="tleft" mappingURL></td>
											</tr>
											<tr id="binding_tr">
												<td class="tright">接口描述</td>
												<td colspan="3" class="tleft">
													<div class="QR_box" remark></div>
												</td>
											</tr>
											<tr id="binding_tr">
												<td colspan="4" class="tleft">
													调用实例：
													<span id="qdcodeCopy" style="color: blue;">点我复制</span>
													<pre id="qdcode" class="layui-code">无</pre>
												</td>
											</tr>
										</tbody>
									</table>
								</dd>
							</dl>
						</li>
					</div>
					<div class="afterEnd">
						<li class="layui-nav-item layui-nav-itemed  ">
							<a class="main_table_title skin-div-css">后端配置信息</a>
							<dl class="layui-nav-child main_table_box">
								<dd>
									<table class="layui-table main_table detail_table">
										<tbody>
											<tr>
												<td class="tright">接口类</td>
												<td class="tleft" interfaceClass></td>
												<td class="tright">接口函数</td>
												<td class="tleft" interfaceMethod></td>
											</tr>
											<tr>
												<td class="tright">返回类型</td>
												<td class="tleft" returnType2></td>
												<td class="tright">接口范围支持</td>
												<td class="tleft" scope></td>
											</tr>
											<tr>
												<td class="tright">解耦接口类</td>
												<td colspan="3" class="tleft" decouplingClass></td>
											</tr>
											<tr>
												<td class="tright">接口描述</td>
												<td colspan="3" class="tleft">
													<div class="QR_box" remark2></div>
												</td>
											</tr>
											<tr class="inside">
												<td colspan="4" class="tleft">
													调用实例：
													<span id="hdcodeCopy" style="color: blue;">点我复制</span>
													<pre id="hdcode" class="layui-code">无</pre>
												</td>
											</tr>
										</tbody>
									</table>
								</dd>
							</dl>
						</li>
					</div>
				</ul>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/appInterface/js/appInterfaceView.js?ver=1.4"></script>
