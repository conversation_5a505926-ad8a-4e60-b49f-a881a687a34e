$(function() {
	pubObj.init();
});

/**
 * 全局参数
 */
var param = {
	appInterfaceID : "0",
	appCode : ""
}

/**
 * 全局控制
 */
var pubObj = {
	/**
	 * 写入全局参数
	 */
	initParam : function() {
		param.appInterfaceID = $("#appInterfaceID").val();
		param.appCode = $("#forAppCode").val();
	},
	/**
	 * 必填校验
	 */
	loadChecks : function() {
		var form = layui.form;
		// 自定义表单校验
		form.verify({
			// 长度校验
			limitc : function(value, item) { // value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			floatc : function(value, item) {
				if (parseFloat(value) > 99999.9999 || parseFloat(value) <= 0) {
					return "顺序号必须大于0且小于100000";
				}
			},
			intc : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			charck : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		form.render();
	},
	/**
	 * 加载监听器
	 */
	loadMonitor : function() {
		var form = layui.form;
		
		form.on("checkbox(supportType)", function(data) {
			if (data.elem.checked) {
				$(".container" + data.value).show();
			} else {
				$(".container" + data.value).hide();
			}
		});
		form.on("radio(scope)", function(data) {
			if (data.value == "external") {
				$(".external").show();
			} else {
				$(".external").hide();
			}
		});
		
		// 这里监听是为了让必填校验生效
		form.on("submit(save)", function(data) {
			pubAppInterface.saveAppInterface();
		});
		form.render();
		
		layui.element.render();
		
	},
	/**
	 * 重新渲染
	 */
	refreshForm : function() {
		// 这里监听是为了让必填校验生效
		var form = layui.form;
		form.render();
	},
	// 初始化
	init : function() {
		
		// 初始化全局参数
		pubObj.initParam();
		
		// 获取数据
		pubAppInterface.getAppInterface();
		
		// 加载应用系统
		pubAppInterface.getAppList();
		
		// 加载必填校验
		pubObj.loadChecks();
		
		// 加载监听器
		pubObj.loadMonitor();
	}
}

/**
 * 全局业务
 */
var pubAppInterface = {
	/**
	 * 获取应用系统
	 */
	getAppList : function() {
		$.ajax({
			url : basePath + "frame/comp/getCompAppRight.spring",
			data : {
				"menuRight" : 0
			},
			dataType : "json",
			async : false,
			success : function(data) {
				if (data.result == "success") {
					var appList = data.appList;
					for (var i = 0; i < appList.length; i++) {
						var hasSelected = "";
						if (param.appCode == appList[i].appCode) {
							hasSelected = "selected";
						}
						$("#appCode").append('<option   ' + hasSelected + ' value="' + appList[i].appCode + '">' + appList[i].appName + '</option>');
					}
				} else {
					assemblys.alert("获取应用系统出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("获取应用系统出错，请联系管理员");
			}
		});
		
	},
	/**
	 * 获取应用接口
	 */
	getAppInterface : function() {
		// 新增时，不加载数据
		if (param.appInterfaceID == "0") {
			pubUploader.initEditor("remark", true, 2);
			pubUploader.initEditor("remark2", true, 2);
			return;
		} else {
			$("#warnningMsg").html("注意：<br>&nbsp;&nbsp;&nbsp;如果你不是本接口的【提供者】，禁止修改接口信息<br>&nbsp;&nbsp;&nbsp;如果你是接口【调用者】请进入详情参考该接口信息<span style='font-size: 20px;color: blue;float: right;line-height: 60px;cursor: pointer;' onclick=\"$(this).parent().remove();\" ><(－︿－)> 朕知道了</span><hr>");
		}
		$.ajax({
			url : basePath + "frame/appinterface/getAppInterface.spring",
			type : "post",
			data : {
				appInterfaceID : param.appInterfaceID
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var appInterface = data.appInterface;
					// 回显表单
					for ( var key in appInterface) {
						$("#form1").find("input[type='text'][name='" + key + "']").val(appInterface[key]);
						$("#form1").find("select[name='" + key + "']").val(appInterface[key]);
						$("#form1").find("textarea[name='" + key + "']").val(appInterface[key]);
						if (key != "remark" && key != "remark2") {
							$("#form1").find("input[type='radio'][name='" + key + "'][value='" + appInterface[key] + "']").prop("checked", "checked");
						}
					}
					
					// 重新渲染
					pubObj.refreshForm();
					
					// 回显接口支持
					var supportType = appInterface["supportType"];
					if (supportType) {
						var supportTypeList = supportType.split(",");
						for ( var index in supportTypeList) {
							$("#form1").find("input[type='checkbox'][name='supportType'][value='" + supportTypeList[index] + "']").next().click();
						}
					}
					
					// 回显解耦类
					$("#form1").find("input[type='radio'][name='scope'][value='" + appInterface["scope"] + "']").next().click();
					
					// 富文本
					var _remark = pubUploader.initEditor("remark", true, 2);
					_remark.ready(function(e) {
						_remark.setContent(assemblys.replaceAll(appInterface["remark"], "\n", "<br>"));
					});
					// 富文本
					var _remark2 = pubUploader.initEditor("remark2", true, 2);
					_remark2.ready(function(e) {
						_remark2.setContent(assemblys.replaceAll(appInterface["remark2"], "\n", "<br>"));
					});
				} else {
					assemblys.alert("获取应用接口数据出错，请刷新重试");
				}
				
			},
			error : function() {
				assemblys.alert("获取应用接口数据出错，请联系管理员");
			}
		});
	},
	/**
	 * 保存数据
	 */
	saveAppInterface : function() {
		// 防止重复交
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		$.ajax({
			url : basePath + "frame/appinterface/saveAppInterface.spring",
			type : "post",
			data : $("#form1").serialize(),
			dataType : "json",
			success : function(data) {
				if (data.result == "repeat") {
					assemblys.alert("同应用系统下，该接口编号已存在，请修改", function() {
						$("#interfaceCode").focus();
						hasSubmit = false;
					});
				} else if (data.result == "success") {
					assemblys.msg("保存成功", function() {
						// 刷新列表
						assemblys.closeWindow();
						parent.pubAppInterface.findAppInterfaceListData();
						hasSubmit = false;
					});
				} else {
					assemblys.alert("保存应用接口数据出错，请刷新重试");
					hasSubmit = false;
				}
			},
			error : function() {
				assemblys.alert("保存应用接口数据出错，请联系管理员");
				hasSubmit = false;
			}
		});
	}
}

var pubOpt = {
	/**
	 * 前端接口测试 - 返回JSON
	 */
	testAppInterface1 : function() {
		var mappingURL = $("#mappingURL").val()
		if (!mappingURL) {
			assemblys.msg("调用地址不能为空");
			return;
		}
		// 请求方式
		var method = $("input[name='method']:checked").val();
		if (method == "unknown") {
			assemblys.msg("请选择请求方式");
			return;
		}
		// 防止重复交
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		$.ajax({
			url : basePath + mappingURL,
			type : method,
			data : $("#testParam1").val(),
			dataType : "json",
			success : function(data) {
				hasSubmit = false;
				$("#testResult1").html(assemblys.escape(JSON.stringify(data)));
			},
			error : function() {
				hasSubmit = false;
			}
		});
		
	},
	/**
	 * 前端接口测试 -- 返回HTML
	 */
	testAppInterfaceForHtml1 : function() {
		var mappingURL = $("#mappingURL").val()
		if (!mappingURL) {
			assemblys.msg("调用地址不能为空");
			return;
		}
		// 防止重复交
		if (hasSubmit) {
			return;
		}
		var url = basePath + mappingURL + "?1=1&" + $("#testParamForHtml1").val();
		window.open(url, "接口测试", 'width=800,height=400');
	}
}
