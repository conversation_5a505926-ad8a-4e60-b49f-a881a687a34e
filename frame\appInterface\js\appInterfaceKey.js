/**
 * 全局业务
 */
var pubAppInterface = {
	/**
	 * 解析授权码
	 */
	parseKey : function() {
		var remark1 = $("#remark1").val();
		if (remark1 && remark1.length == 0) {
			assemblys.msg("授权码不能为空");
			return;
		}
		var url = basePath + "frame/appinterface/parseAppinterfaceKey.spring";
		$.ajax({
			url : url,
			type : "post",
			data : {
				"key" : remark1
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					$("#remark2").val(JSON.stringify(data.value));
				} else {
					assemblys.alert("解析授权码出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("解析授权码出错，请联系管理员");
			}
		});
		
	}
}
