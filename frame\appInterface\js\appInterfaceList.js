$(function() {
	pubObj.init();
});

/**
 * 全局参数
 */
var param = {
	keyword : "",
	state : "1",
	appCode : "APP",
	scope : "",
	interfaceTitle : "",
	version : "",
	level : "",
	// 分页参数
	curPageNum : "1",
	pageSize : "20"
}

/**
 * 全局控制
 */
var pubObj = {
	// 初始化
	init : function() {
		
		assemblys.getMenuIcon({
			funCode : funCode,
			hasOrg : false,
			dom : $("b#menuIcon")
		});
		
		pubAppInterface.getAppList().then(function() {
			return pubAppInterface.getInterfaceTitleList();
		}).then(function() {
			return pubAppInterface.getInterfaceVersionList();
		}).then(function() {
			// 加载监听器
			pubAppInterface.loadMonitor();
			// 查询数据
			pubAppInterface.findAppInterfaceListData();
		});
	}
}

/**
 * 全局业务
 */
var pubAppInterface = {
	/**
	 * 获取应用系统
	 */
	getAppList : function() {
		return $.ajax({
			url : basePath + "frame/comp/getCompAppRight.spring",
			data : {
				"menuRight" : 0
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var appList = data.appList;
					var customAppCode = localStorage.getItem("CUSTOM_APPCODE_VALUE");
					for (var i = 0; i < appList.length; i++) {
						var selected = "";
						if (customAppCode == appList[i].appCode) {
							selected = "selected";
							param.appCode = customAppCode;
						}
						$("#appCode").append('<option  ' + selected + ' value="' + appList[i].appCode + '">' + appList[i].appName + '</option>');
					}
				} else {
					assemblys.alert("获取应用出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("获取应用数据出错，请联系管理员");
			}
		});
		
	},
	getInterfaceTitleList : function() {
		return $.ajax({
			url : basePath + "frame/appinterface/getInterfaceTitleList.spring",
			dataType : "json",
			data : {
				appCode : param.appCode
			},
			success : function(data) {
				if (data.result == "success") {
					var treeData = [ {
						title : "全部"
					} ];
					var interfaceTitleList = data.interfaceTitleList;
					// 处理数据
					for (var i = 0; i < interfaceTitleList.length; i++) {
						var temp = interfaceTitleList[i];
						temp.title = temp.interfaceTitle;
						treeData.push(temp);
					}
					
					layui.tree.render({
						elem : '#tree', //绑定元素
						data : treeData,
						click : function(item) {
							param.interfaceTitle = item.data.interfaceTitle || "";
							pubAppInterface.findAppInterfaceListData();
						}
					});
					
				} else {
					assemblys.alert("获取接口标识出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("获取接口标识出错，请联系管理员");
			}
		});
		
	},
	getInterfaceVersionList : function() {
		return $.ajax({
			url : basePath + "frame/appinterface/getInterfaceVersionList.spring",
			dataType : "json",
			data : {
				appCode : param.appCode
			},
			success : function(data) {
				if (data.result == "success") {
					// 清空
					$("#version").empty();
					var versionList = data.versionList;
					$("#version").append('<option value="">全部</option>');
					for (var i = 0; i < versionList.length; i++) {
						var temp = versionList[i];
						// 回显
						var hasSelect = "";
						if (temp.version == param.version) {
							hasSelect = "selected";
						}
						$("#version").append('<option value="' + temp.version + '" ' + hasSelect + ' >' + temp.version + '</option>');
					}
					layui.form.render();
					
				} else {
					assemblys.alert("获取版本号出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("获取版本号出错，请联系管理员");
			}
		});
		
	},
	/**
	 * 加载监听器
	 */
	loadMonitor : function() {
		var form = layui.form;
		form.on("select(appCode)", function(data) {
			param.appCode = data.value;
			
			// 清空
			param.interfaceTitle = "";
			param.version = "";
			
			// 更新接口标识
			pubAppInterface.getInterfaceTitleList();
			
			// 更新版本号
			pubAppInterface.getInterfaceVersionList();
			
			// 查询
			pubAppInterface.findAppInterfaceListData();
			
			localStorage.setItem("CUSTOM_APPCODE_VALUE", data.value);
			
		});
		
		form.on("radio(state)", function(data) {
			param.state = data.value;
			// 查询
			pubAppInterface.findAppInterfaceListData();
		});
		
		form.on("select(version)", function(data) {
			param.version = data.value;
			// 查询
			pubAppInterface.findAppInterfaceListData();
		});
		
		form.on("select(level)", function(data) {
			param.level = data.value;
			// 查询
			pubAppInterface.findAppInterfaceListData();
		});
		
		form.on("checkbox(scope)", function(data) {
			// 赋值
			param.scope = data.elem.checked ? data.value : "";
			// 查询
			pubAppInterface.findAppInterfaceListData();
		});
		
		form.render();
		
	},
	/**
	 * 查询
	 */
	findAppInterfaceListData : function() {
		// 关键字
		param.keyword = $.trim($("#keyword").val());
		// 初始化为第一页
		param.curPageNum = "1";
		// 查询
		pubAppInterface.getAppInterfaceListData();
	},
	/**
	 * 获取数据
	 */
	getAppInterfaceListData : function() {
		
		// 防止重复提交
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		$.ajax({
			url : basePath + "frame/appinterface/getAppInterfaceListData.spring",
			type : "post",
			dataType : "json",
			data : param,
			success : function(data) {
				hasSubmit = false;
				if (data.result == "success") {
					// Dom结构处理
					pubAppInterface.loadTableView(data);
				} else {
					assemblys.alert("获取表单列表数据出错，请刷新重试");
				}
			}
		});
	},
	/**
	 * 新增/编辑
	 */
	editAppInterface : function(obj) {
		var appInterfaceID = 0;
		var title = "新增应用接口";
		// 存在就是编辑
		if (obj) {
			appInterfaceID = $(obj).attr("appInterfaceID");
			title = "编辑应用接口";
		}
		var url = basePath + "frame/appinterface/toAppInterfaceEdit.spring?appInterfaceID=" + appInterfaceID + "&appCode=" + param.appCode;
		url += "&interfaceTitle=" + (appInterfaceID == 0 ? param.interfaceTitle : "");
		layer.open({
			content : url,
			type : 2,
			skin : 'layui-layer-aems',
			title : title,
			scrollbar : false,
			area : [ '90%', '90%' ],
			end : function() {
				// 更新接口标识
				pubAppInterface.getInterfaceTitleList();
			}
		});
	},
	/**
	 * 详情
	 */
	findAppInterface : function(obj) {
		var appInterfaceID = $(obj).attr("appInterfaceID");
		var url = basePath + "frame/appInterface/appInterfaceView.jsp?appInterfaceID=" + appInterfaceID;
		layer.open({
			content : url,
			type : 2,
			skin : 'layui-layer-aems',
			title : "接口详情",
			scrollbar : false,
			area : [ '90%', '90%' ]
		});
	},
	/**
	 * 删除
	 */
	delAppInterface : function(obj) {
		var appInterfaceID = $(obj).attr("appInterfaceID");
		var interfaceName = $(obj).attr("interfaceName");
		assemblys.confirm("你确定要删除接口「" + interfaceName + "」吗?", function() {
			// 防止重复提交
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			$.ajax({
				url : basePath + "frame/appinterface/delAppInterface.spring",
				type : "post",
				data : {
					appInterfaceID : appInterfaceID
				},
				dataType : "json",
				success : function(data) {
					hasSubmit = false;
					if (data.result == "success") {
						assemblys.msg("删除成功", function() {
							
							// 更新接口类
							pubAppInterface.getInterfaceTitleList();
							
							// 查询
							pubAppInterface.getAppInterfaceListData();
							
						});
					} else {
						assemblys.alert("删除应用接口数据出错，请刷新重试");
					}
				},
				error : function() {
					assemblys.alert("删除应用接口数据出错，请联系管理员");
				}
			});
		});
		
	},
	/**
	 * 解析授权码
	 */
	parseKey : function() {
		var url = basePath + "frame/appInterface/appInterfaceKey.jsp";
		layer.open({
			content : url,
			type : 2,
			skin : 'layui-layer-aems',
			title : "解析授权码",
			scrollbar : false,
			area : [ '600px', '400px' ]
		});
	},
	/**
	 * 生成授权码
	 */
	generateKey : function(obj) {
		var appInterfaceID = $(obj).attr("appInterfaceID");
		$.ajax({
			url : basePath + "frame/appinterface/generateAppinterfaceKey.spring",
			type : "post",
			data : {
				appInterfaceID : appInterfaceID
			},
			dataType : "json",
			success : function(data) {
				hasSubmit = false;
				if (data.result == "success") {
					assemblys.coryText(data.key);
					assemblys.msg("已复制到剪切板");
				} else {
					assemblys.alert("生成授权码出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("生成授权码出错，请联系管理员");
			}
		});
	},
	/**
	 * 无数据时
	 */
	notDataView : function() {
		// 无数据时，获取td长度，IE缺少会页面变形
		var length = $("#tr_template").children().length;
		return "<tr><td colspan='" + length + "' align='center'>没有相关数据！</td></tr>";
	},
	/**
	 * 页面显示
	 */
	loadTableView : function(data) {
		
		// 获取数据
		var pager = data.pager;
		
		// 渲染目标
		var $tableView = $("#tableView");
		
		// 清空dom
		$tableView.empty();
		
		// 获取数据
		var items = pager.items;
		
		// 如果无结果
		
		if (items.length == 0) {
			
			$tableView.html(pubAppInterface.notDataView());
			
		} else {
			
			// 克隆对象
			var $tr = $("#tr_template");
			// td总长度
			var tdLength = $tr.children().length;
			// td映射关系 
			var keys = [ "appInterfaceID", "appCode", "interfaceTitle", "interfaceCode", "interfaceName", "supportType", "level", "version", "state" ];
			
			// 处理table body
			for (var i = 0, length = items.length; i < length; i++) {
				// 克隆tr
				var $trClone = $tr.clone();
				$trClone.removeClass("layui-hide");
				
				// 获取所有td
				var $tds = $trClone.children();
				// 每个tr 第一个td
				var $td0 = $tds.eq(0);
				// 每个map
				var itemsObj = items[i];
				
				// --- 绑定操作 start --
				var key0 = keys[0];
				var key4 = keys[4];
				$td0.find("[type='edit']").attr(key0, itemsObj[key0]).click(function(e) {
					pubAppInterface.editAppInterface(this);
				});
				
				$td0.find("[type='find']").attr(key0, itemsObj[key0]).click(function(e) {
					pubAppInterface.findAppInterface(this);
				});
				
				// 创建需要绑定的Json
				var attrJson = {};
				attrJson[key0] = itemsObj[key0];
				attrJson[key4] = itemsObj[key4];
				$td0.find("[type='del']").attr(attrJson).click(function(e) {
					pubAppInterface.delAppInterface(this);
				});
				
				$td0.find("[type='sql']").attr(attrJson).click(function(e) {
					pubAppInterface.exportSql(this);
				});
				
				// 如果是外部接口
				if (itemsObj["scope"] == "external") {
					$td0.find("[type='key']").attr(key0, itemsObj[key0]).click(function(e) {
						pubAppInterface.generateKey(this);
					});
				} else {
					$td0.find("[type='key']").remove();
				}
				
				// --- 绑定操作 end --
				
				// 根据td映射关系，从数据对象中进行数据填装
				for (var j = 1; j < tdLength; j++) {
					var key = keys[j];
					var value = itemsObj[key];
					// 填装TD
					$tds.eq(j).html(pubAppInterface.specialHandle(key, value));
				}
				// 写入table
				$tableView.append($trClone);
			}
		}
		// 加载分页器
		pubAppInterface.loadPager(pager);
	},
	/**
	 * 加载分页器
	 */
	loadPager : function(pager) {
		var count = pager.totalCount;
		var limit = pager.pageSize;
		var curr = pager.curPageNum;
		var form = layui.form;
		var laypage = layui.laypage;
		laypage.render({
			elem : 'layui-table-page1',
			count : count,
			limit : limit,
			curr : curr,
			layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
			jump : function(page, first) {
				if (!first) {
					param.curPageNum = page.curr;
					param.pageSize = page.limit;
					// 获取数据
					pubAppInterface.getAppInterfaceListData();
				}
			}
		});
		layui.form.render();
	},
	/**
	 * value特殊处理
	 */
	specialHandle : function(key, value) {
		// 状态处理
		if (key == "state") {
			return value == "1" ? "<font style=\"color: green;\">启用</font>" : "<font style=\"color: red;\">停用</font>";
		}
		if (key == "supportType") {
			if (value == "1") {
				return "<font style='color:orange;'>前端</font>";
			} else if (value == "2") {
				return "<font style='color:blue;'>后端</font>";
			} else if (value == "1,2") {
				return "<font style='color:orange;'>前端</font> / <font style='color:blue;'>后端</font>";
			} else {
				return "无";
			}
		}
		if (key == "level") {
			return pubAppInterface.levelMapping[value];
		}
		return value;
	},
	levelMapping : {
		"1" : "<span style='color:blue;' >一级</span>",
		"2" : "<span style='color:green;'>二级</span>",
		"3" : "<span style='color:red;'>三级</span>",
		"-1" : "<span style='color:gray;'>特殊</span>"
	},
	// 导出SQL
	exportSql : function(obj) {
		var appInterfaceID = $(obj).attr("appInterfaceID");
		var interfaceName = $(obj).attr("interfaceName");
		var url = basePath + "frame/common/exportSql.spring?sign=appInterface&code=" + appInterfaceID + "&sqlName=" + encodeURIComponent(interfaceName);
		location.href = url;
	},
	openRemark3 : function() {
		var url = basePath + "frame/appInterface/appInterfaceRemark3.jsp";
		assemblys.parent.addTab(null, "什么是接口分级", url);
	},
	openRemark : function() {
		var url = basePath + "frame/appInterface/appInterfaceRemark.jsp";
		assemblys.parent.addTab(null, "如何开发接口", url);
	},
	openRemark2 : function() {
		var url = basePath + "frame/appInterface/appInterfaceRemark2.jsp";
		assemblys.parent.addTab(null, "如何使用接口", url);
	},
	download : function() {
		// 判断如果是Ipad则不支持导出功能功能
		var ua = navigator.userAgent.toLowerCase();
		var s = ua.match(/iPad/i);
		if (s == "ipad") {
			assemblys.msg("Ipad不支持该处导出功能，请更换设备导出");
			return false;
		}
		var url = basePath + "frame/common/downloadTemplate.spring?fileName=" + encodeURIComponent("授权接口文档.doc");
		location.href = url;
	}
}
