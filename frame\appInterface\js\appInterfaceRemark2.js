/**
 * 全局业务
 */
var pubAppInterface = {
	isee : function(obj) {
		$('.remark').show();
		$(obj).parents('.layui-show').removeClass('layui-show');
		$(obj).remove();
		localStorage.setItem("hasSeeScene2", "1");
	},
	init : function() {
		
		assemblys.getMenuIcon({
			funCode : funCode,
			hasOrg : false,
			dom : $("b#menuIcon"),
			menuName : "接口调用说明"
		});
		
		var hasSeeScene = localStorage.getItem("hasSeeScene2") || "0";
		if (hasSeeScene == "1") {
			$("#see").click();
		}
		$("body").show();
	}
}
pubAppInterface.init();