$(function() {
	pubObj.init();
});

/**
 * 全局参数
 */
var param = {
	appInterfaceID : "0"
}

/**
 * 全局控制
 */
var pubObj = {
	/**
	 * 写入全局参数
	 */
	initParam : function() {
		param.appInterfaceID = $("#appInterfaceID").val();
	},
	// 初始化
	init : function() {
		
		// 初始化全局参数
		pubObj.initParam();
		
		// 获取数据
		pubAppInterface.getAppInterface();
		
		// 加载分类监听
		layui.element.render("nav", "test");
		
	}
}

/**
 * 全局业务
 */
var pubAppInterface = {
	/**
	 * value特殊处理
	 */
	specialHandle : function(key, value) {
		// 状态处理
		if (key == "state") {
			return value == "1" ? "<font style=\"color: green;\">启用</font>" : "<font style=\"color: red;\">停用</font>";
		}
		if (key == "returnType" || key == "returnType2" || key == "method") {
			return value == "unknown" ? "无" : value;
		}
		if (key == "createDate" || key == "optDate") {
			return assemblys.dateToStr(value.time);
		}
		if (key == "remark" || key == "remark2") {
			return value;
		}
		if (key == "supportType") {
			if (value == "1") {
				$(".beforeEnd").show();
				return "<font style='color:orange;'>前端</font>";
			} else if (value == "2") {
				$(".afterEnd").show();
				return "<font style='color:blue;'>后端</font>";
			} else if (value == "1,2") {
				$(".beforeEnd,.afterEnd").show();
				return "<font style='color:orange;'>前端</font> / <font style='color:blue;'>后端</font>";
			} else {
				return "无";
			}
		}
		if (key == "scope") {
			if (value == "inside") {
				return "<font style=\"color: red;\">内部</font>";
			} else if (value == "external") {
				return "<font style=\"color: blue;\">外部</font>";
			} else {
				return "无";
			}
		}
		if (key == "level") {
			return parent.pubAppInterface.levelMapping[value];
		}
		return value;
	},
	/**
	 * 获取应用接口
	 */
	getAppInterface : function() {
		$.ajax({
			url : basePath + "frame/appinterface/getAppInterface.spring",
			type : "post",
			data : {
				appInterfaceID : param.appInterfaceID
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var appInterface = data.appInterface;
					// 回显表单
					for ( var key in appInterface) {
						$("#form1").find("[" + key + "]").html(pubAppInterface.specialHandle(key, appInterface[key]));
					}
					
					// 前端样例
					if (appInterface["mappingURL"] && appInterface["level"] != -1) {
						var html = "";
						var coryHtml1 = "";
						if (appInterface["method"] == "JS") {
							html += '	// 引入JS，按照接口描述，进行调用 \n';
							html += '	&lt;script type="text/javascript" src="../../../' + appInterface["mappingURL"] + '" &gt;&lt;/script&gt;\n';
							coryHtml1 += '<script type="text/javascript" src="../../../' + appInterface["mappingURL"] + '" ></script>';
						} else if (appInterface["returnType"] == "JSON") {
							html += '	// 返回JSON形式\n';
							html += '	$.ajax({ \n';
							html += '		url : basePath + "' + appInterface["mappingURL"] + '",\n';
							html += '		type : "' + appInterface["method"].toLowerCase() + '",\n';
							html += '		data : {\n';
							html += '		   "key" : value\n';
							html += '		},\n';
							html += '		dataType : "json",\n';
							html += '		// skipDataCheck : true, - 如果接口响应回来的数据有问题，请增加该参数\n';
							html += '		success : function(data) {\n';
							html += '		  // 结果\n';
							html += '		},\n';
							html += '		error : function(){\n';
							html += '		}\n';
							html += '	});\n';
							coryHtml1 += '	$.ajax({ \n';
							coryHtml1 += '		url : basePath + "' + appInterface["mappingURL"] + '",\n';
							coryHtml1 += '		type : "' + appInterface["method"].toLowerCase() + '",\n';
							coryHtml1 += '		data : {\n';
							coryHtml1 += '		   "key" : value\n';
							coryHtml1 += '		},\n';
							coryHtml1 += '		dataType : "json",\n';
							coryHtml1 += '	 	skipDataCheck : true,\n';
							coryHtml1 += '		success : function(data) {\n';
							coryHtml1 += '		},\n';
							coryHtml1 += '		error : function(){\n';
							coryHtml1 += '		}\n';
							coryHtml1 += '	});\n';
						} else {
							html += '	// 返回HTML形式 \n';
							html += '	var url = basePath + "' + appInterface["mappingURL"] + '";\n';
							html += '	$("iframe").attr("src",url).css("height",$(window).height();\n';
							html += '	// 一般通过嵌套实现。\n';
							html += '	&lt;iframe src="" width="100%" frameborder="0"&gt;&lt;/iframe&gt;\n';
							coryHtml1 += '	var url = basePath + "' + appInterface["mappingURL"] + '";\n';
							coryHtml1 += '	$("iframe").attr("src",url).css("height",$(window).height());\n';
							coryHtml1 += '	<iframe src="" width="100%" frameborder="0"></iframe>\n';
						}
						$("#qdcode").html(html);
						$("#qdcodeCopy").click(function() {
							assemblys.coryText(coryHtml1);
						});
					}
					
					// 后端样例
					var interfaceClass = appInterface["interfaceClass"];
					var interfaceMethod = appInterface["interfaceMethod"];
					if (interfaceClass && appInterface["level"] != -1) {
						var html = "";
						var coryHtml2 = "";
						if (appInterface["scope"] == "inside") {
							var tempName = interfaceClass.substring(0, 1).toLowerCase() + interfaceClass.substring(1, interfaceClass.length);
							html += ' // 当前接口类，在调用的地方进行自动注入\n';
							html += '@Autowired\n';
							html += 'private ' + interfaceClass + '&lt;?&gt; ' + tempName + ';\n';
							html += '\n';
							html += '//调用演示\n';
							html += '' + tempName + '.' + interfaceMethod + '(param);\n';
							coryHtml2 += ' @Autowired\n ';
							coryHtml2 += ' private ' + interfaceClass + '<?> ' + tempName + '; \n';
							coryHtml2 += '' + tempName + '.' + interfaceMethod + '(param);\n';
						} else {
							var decouplingClass = appInterface["decouplingClass"];
							var tempName = decouplingClass.substring(0, 1).toLowerCase() + decouplingClass.substring(1, decouplingClass.length);
							html += '// 动态注入，这里bean名指向具体应用的实现类（实现类是接口提供者去开发）\n';
							html += '@Qualifier("' + decouplingClass + '")\n';
							html += '@Autowired(required = false)\n';
							html += 'private ExternalApiInterface ' + tempName + ';\n';
							html += '\n';
							html += 'Map&lt;String , Object&gt; param = new HashMap&lt;String , Object&gt;();\n';
							html += 'if(' + tempName + ' != null){\n';
							html += '  JSONObject resutlJson = ' + tempName + '.invoke("' + interfaceMethod + '",param);\n';
							html += '}\n';
							
							coryHtml2 += ' @Qualifier("' + decouplingClass + '" )\n ';
							coryHtml2 += ' @Autowired(required = false) \n ';
							coryHtml2 += ' private ExternalApiInterface ' + tempName + ';\n';
							coryHtml2 += ' \n';
							coryHtml2 += ' Map<String , Object> param = new HashMap<String , Object>();\n';
							coryHtml2 += ' if(' + tempName + ' != null){\n ';
							coryHtml2 += '  JSONObject resutlJson = ' + tempName + '.invoke("' + interfaceMethod + '",param);\n ';
							coryHtml2 += ' }\n ';
						}
						$("#hdcode").html(html);
						$("#hdcodeCopy").click(function() {
							assemblys.coryText(coryHtml2);
						});
						
					}
					
					// 是否停用
					if (appInterface["state"] == 0) {
						$(".void").show();
					}
				} else {
					assemblys.alert("获取应用接口数据出错，请刷新重试");
				}
			},
			error : function() {
				assemblys.alert("获取应用接口数据出错，请联系管理员");
			}
		});
	}
}
