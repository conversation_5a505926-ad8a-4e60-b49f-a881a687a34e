<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=GBK">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>编辑系统</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = "${basePath}/frame/appaction/";
	var hasSubmit = false;
	
	$(function() {
		
		$("#appCode").focus();
		
		// 获取皮肤
		pubSkin.getSkinList();
	});
	
	//保存
	function resaveApp() {
		var appId = $.trim($("#appId").val());
		var appName = $.trim($("#appName").val());
		var appCode = $.trim($("#appCode").val());
		var seqNo = $.trim($("#seqNo").val());
		var server = $.trim($("#server").val());
		var indexPage = $.trim($("#indexPage").val());
		var onClick = "";
		var openInNewWindow = $.trim($("#openInNewWindow").val());
		var showUI = $.trim($("#showUI").val());
		var accessType = $.trim($("#accessType").val());
		var logo = $.trim($("#logo").val());
		var versionIndex = $.trim($("#versionIndex").val());
		var virtual = $("input[name=virtual]").prop("checked") ? 1:0;
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		var url = baseContext + "resaveApp.spring?1=1";
		$.ajax({
			"url" : url,
			"data" : {
				"appId" : appId,
				"appName" : appName,
				"appCode" : appCode,
				"seqNo" : seqNo,
				"onClick" : onClick,
				"server" : server,
				"indexPage" : indexPage,
				"openInNewWindow" : openInNewWindow,
				"showUI" : showUI,
				"accessType" : accessType,
				"logo" : logo,
				"versionIndex" : versionIndex,
				"virtual" : virtual
			},
			success : function(data) {
				var status = data.getElementsByTagName("status")[0].childNodes[0].nodeValue;
				if (status == "THE_RECORD_HAS") {
					assemblys.msg("名称或编号已经存在");
					hasSubmit = false;
				} else if (status == "THE_SEQNO_HAS") {
					assemblys.msg("顺序号已经存在，请重新输入");
					$("#seqNo").focus();
					hasSubmit = false;
				} else if (status == "EDIT_OK") {
					// 保存皮肤
					pubSkin.saveSkin().then(function(data) {
						if (data.result == "success") {
							assemblys.msg("修改成功", function() {
								parent.location.reload();
							});
						} else {
							assemblys.alert("修改皮肤数据失败");
							hasSubmit = false;
						}
					});
				} else {
					assemblys.alert("新增出错，请检查服务器是否正常运行");
					hasSubmit = false;
				}
			}
		});
	}

	var pubSkin = {
		// 获取皮肤列表
		getSkinList : function() {
			var url = basePath + "/frame/skin/getSkinList.spring";
			$.ajax({
				url : url,
				type : "get",
				data : {
					appCode : $("#appCode").val()
				},
				dataType : "json",
				skipDataCheck : true,
				success : function(data) {
					if (data.result == "success") {
						var skinList = data.data.skinList;
						if (skinList.length > 0) {
							for (var i = 0; i < skinList.length; i++) {
								var skin = skinList[i];
								var checked = skin.checked ? "selected" : "";
								$("#skin").append('<option  ' + checked + ' value="' + skin.dictContent + '" >' + skin.dictName + '</option>')
							}
						} else {
							$("#skin").append('<option value="0">默认</option>')
						}
						layui.form.render("select");
					}
				},
				error : function(e) {
					assemblys.msg("获取皮肤信息异常");
				}
			});
		},
		// 保存皮肤
		saveSkin : function() {
			var url = basePath + "/frame/skin/saveSkin.spring";
			return $.ajax({
				url : url,
				type : "post",
				data : {
					"appCode" : $("#appCode").val(),
					"skinValue" : $("#skin").val(),
					"userCode" : ""
				},
				dataType : "json",
				async : false,
				skipDataCheck : true
			});
		}
	}
</script>
</head>
<body class="body_noTop">
	<form action="" method="post" class="layui-form">
		<input type="hidden" id="appId" name="appId" value="<c:out value="${app.appID}"/>">
		<div class="bodys bodys_noTop">
			<table class="layui-table main_table">
				<tbody>
					<div class="layui-form-item">
						<label class="layui-form-label">
							<span style="color: red">*</span>
							应用编号
						</label>
						<div class="layui-input-inline">
							<input type="text" id="appCode" name="appCode" value="<c:out value="${app.appCode}"/>" lay-verify="required|character" autocomplete="off" class="layui-input h28">
						</div>
						<label class="layui-form-label">
							<span style="color: red">*</span>
							应用名称
						</label>
						<div class="layui-input-inline">
							<input type="text" id="appName" name="appName" value="<c:out value="${app.appName}"/>" lay-verify="required|character" class="layui-input h28">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label"> 指向站点 </label>
						<div class="layui-input-inline">
							<input type="text" id="server" name="server" value="<c:out value="${app.server}"/>" autocomplete="off" class="layui-input h28">
						</div>
						<label class="layui-form-label"> 工作台 </label>
						<div class="layui-input-inline">
							<input type="text" id="indexPage" name="indexPage" value="<c:out value="${app.indexPage}"/>" autocomplete="off" class="layui-input h28">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">
							<span style="color: red">*</span>
							顺序号
						</label>
						<div class="layui-input-inline">
							<input type="text" id="seqNo" name="seqNo" value="<c:out value="${app.seqNo}"/>" lay-verify="required|integer|seq" autocomplete="off" class="layui-input h28">
						</div>
						<label class="layui-form-label"> LOGO </label>
						<div class="layui-input-inline">
							<input type="text" id="logo" name="logo" value="<c:out value="${app.logo}"/>" autocomplete="off" class="layui-input h28">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">
							<span style="color: red">*</span>
							接入方式
						</label>
						<div class="layui-input-inline">
							<select class="layui-input" id="accessType" lay-filter="accessType">
								<option value="1" <c:if test="${app.accessType == '1'}">selected="selected"</c:if>>Session</option>
								<option value="2" <c:if test="${app.accessType == '2'}">selected="selected"</c:if>>JWT验证</option>
								<option value="3" <c:if test="${app.accessType == '3'}">selected="selected"</c:if>>单点</option>
							</select>
						</div>
						<label class="layui-form-label"> 版本信息 </label>
						<div class="layui-input-inline">
							<input type="text" id="versionIndex" name="versionIndex" value="<c:out value="${app.versionIndex}"/>" maxlength="200" autocomplete="off" class="layui-input h28">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label"> 初始皮肤 </label>
						<div class="layui-input-inline">
							<select class="layui-input" id="skin" lay-filter="skin">
							</select>
						</div>
						<label class="layui-form-label">是否虚拟应用</label>
						<div class="layui-input-inline">
							<input type="checkbox" lay-skin="switch" name="virtual" lay-text="是|否" ${app.virtual == 1 ? 'checked' : ''}>
						</div>
					</div>
					<input type="hidden" id="openInNewWindow" name="openInNewWindow" value="0">
					<input type="hidden" id="showUI" name="showUI" value="0">
					<div class="layui-form-item">
						<label class="layui-form-label"> </label>
						<div class="layui-input-inline">
							<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
							<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
						</div>
					</div>
				</tbody>
			</table>
			<div class="head3">
				<div>【说明】</div>
				<div class="comTab_SnTxt">
					<li class="comTab_SnLi" style="margin-left: 30px">
						<strong>指向站点：</strong>
						当应用系统与框架不是部署在同一个站点时，输入应用系统的站点地址
					</li>
					<li class="comTab_SnLi" style="margin-left: 30px">
						<strong>工作台：</strong>
						加载应用系统时显示的【工作台】地址，支持相对路径和HTTP请求。
						<br>
						例：【/应用目录/portal/index.html】/ 【https://www.baidu.com】
					</li>
					<li class="comTab_SnLi" style="margin-left: 30px">
						<strong>LOGO：</strong>
						顶部菜单选择应用时显示，支持相对路径和Http路径，例：【/应用目录/images/LOGO.png】
					</li>
					<li class="comTab_SnLi" style="margin-left: 30px">
						<strong>版本信息：</strong>
						在框架右上角，点击【关于平台】，用于查看应用更新信息，支持相对路径和HTTP请求。
						<br>
						例：【/应用目录/version.html】/ 【https://www.baidu.com】
					</li>
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			seq : function(value, item) {
				if (parseFloat(value) > 99999.9999 || parseFloat(value) <= 0) {
					return "顺序号必须大于0且小于100000";
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写正整数';
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		form.on("submit(save)", function(data) {
			resaveApp();
		});
		form.render();
	});
</script>
</html>
