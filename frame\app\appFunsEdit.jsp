<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=GBK" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>修改功能</title>
<link rel="stylesheet" type="text/css" href="<%=baseURL%>/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="<%=baseURL%>/plugins/static/css/edit.css" />
<script type="text/javascript" src="<%=basePath%>/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="<%=basePath%>/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="<%=basePath%>/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var contextPath = "<%=baseURL%>/";
	var isSubmit = false;
	
	$(function() {
		$("#funName").focus();
	});
	
	function getRadioValueByName(radioName) {
		var radioValues = document.getElementsByName(radioName);
		for (var i = 0; i < radioValues.length; i++) {
			if (radioValues[i].checked) {
				return radioValues[i].value;
			}
		}
	}
	function gotoUpdate() {
		var funID = document.getElementById("funID").value;
		var funName = document.getElementById("funName").value;
		var funCode = document.getElementById("funCode").value;
		var seqNo = document.getElementById("seqNo").value;
		var funDesc = document.getElementById("funDesc").value;
		var orgRight = getRadioValueByName("orgRight");
		var isAssignable = getRadioValueByName("isAssignable");
		var appID = document.getElementById("appID").value;
		var subID = document.forms[0].subID.options[document.forms[0].subID.selectedIndex].value;
		
		if (isNaN(seqNo)) {
			assemblys.msg("顺序号不正确，请重新输入");
			document.getElementById("seqNo").focus();
			return;
		}	
		
		
		var url = contextPath + "frame/appFunsSet/update.spring?1=1";
		var pars = {"funID":funID,"funName":funName,"funCode":funCode,"appID":appID,"subID":subID,"funDesc":funDesc,"orgRight":orgRight,"isAssignable":isAssignable,"seqNo":seqNo};
		if (isSubmit == true)
			return;
		isSubmit = true;
		$.ajax({
			"type" : "post",
			"url" : url,
			"data" : pars,
			"success" : updateHandle,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}
	function updateHandle(doc) {
		var appID = document.getElementById("appID").value;
		var subID = document.forms[0].subID.options[document.forms[0].subID.selectedIndex].value;
		var status = doc.getElementsByTagName("status")[0].childNodes[0].nodeValue;
		if (status != null && status == "1") {
			assemblys.msg("功能名称或者编号已存在，不能重复添加");
			isSubmit = false;
		} else {
			assemblys.msg("修改成功", function(index) {
				assemblys.closeWindow();
				window.parent.appList();
			});
		}
	}
</script>
</head>
<body>
	<form action="/frame/appFunsSet/update.spring?1=1" class="layui-form">
		<input type="hidden" name="appID" value="<c:out value='${appID}'/>" id="appID" />
		<input type="hidden" name="funID" value="<c:out value='${funID}'/>" id="funID" />
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					功能编号
				</label>
				<div class="layui-input-inline">
					<input type="text" name="funCode" value="<c:out value='${funCode}'/>" size="200" id="funCode" maxlength="200" class="layui-input">
					<script type="text/javascript">$("#funCode").attr("lay-verify","required|character");</script>
				</div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					功能名称
				</label>
				<div class="layui-input-inline">
					<input type="text" name="funName" value="<c:out value='${funName}'/>" id="funName" class="layui-input" size="200" maxlength="200">
					<script type="text/javascript">$("#funName").attr("lay-verify","required|character");</script>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					所属子系统
				</label>
				<div class="layui-input-inline">
					<select name="subID" id="subID">
						<c:forEach items="${subSystems}" var="subSystem">
							<option value="${subSystem.code}" <c:if test="${subID == subSystem.code}">selected</c:if>>${subSystem.name}</option>
						</c:forEach>
					</select>
				</div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					顺序号
				</label>
				<div class="layui-input-inline">
					<input type="text" name="seqNo" value="<c:out value='${seqNo}'/>" size="10" id="seqNo" class="layui-input" maxlength="10" />
					<script type="text/javascript">$("#seqNo").attr("lay-verify","required|float");</script>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					组织架构
				</label>
				<div class="layui-input-inline">
					<input type="radio" name="orgRight" id="orgRight" value="0" title="否" <c:if test="${orgRight == '0'}">checked</c:if> />
					<input type="radio" name="orgRight" id="orgRight" value="1" title="是" <c:if test="${orgRight == '1'}">checked</c:if> />
				</div>
				<input type="hidden" name="isAssignable" value="1" />
				<%-- <label class="layui-form-label">是否可以转授权 </label>
				<div class="layui-input-inline">
					<input type="radio" name="isAssignable" id="isAssignable" value="0" title="否" <c:if test="${isAssignable == '0'}">checked</c:if> />
					<input type="radio" name="isAssignable" id="isAssignable" value="1" title="是" <c:if test="${isAssignable == '1'}">checked</c:if> />
				</div> --%>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">功能描述 </label>
				<div class="layui-input-inline" style="width: 520px;">
					<textarea name="funDesc" id="funDesc" lay-verify="limit|character" limit="200" cols="80" rows="3" class="layui-textarea">${funDesc}</textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> </label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm " value="保存" lay-submit lay-filter="save" />
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			float : function(value, item) {
				if (parseFloat(value)>99999.9999||parseFloat(value)<=0) {
					return "顺序号必须大于0且小于100000";
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		
		form.on("submit(save)", function(data) {
			gotoUpdate();
		});
		form.render();
	});
</script>
</html>
