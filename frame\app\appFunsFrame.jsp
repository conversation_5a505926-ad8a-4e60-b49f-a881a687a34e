<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>功能列表</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css">
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
</script>
</head>
<body>
	<form action="${basePath}frame/appFunsSet/list.spring" onsubmit="return false" class="layui-form">
		<input type="hidden" name="appID" id="appID" value="0">
		<input type="hidden" name="subID" id="subID" value="0">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
				<input type="hidden" name="appName" id="appName" value="<c:out value='${appFunsForm.appName}'/>">
				<span id="AppName"> </span>
				<input type="hidden" name="subName" id="subName" value="<c:out value='${appFunsForm.subName}'/>">
				<span id="SubName"> </span>
			</span>
			<div class="head0_right fr" id="hide">
				<button type="button" class="layui-btn layui-btn-sm" onclick="newAppFuns()">新增</button>
			</div>
		</div>
		<div class="bodys">
			<div class="layui-row ">
				<div class="treeDiv tree_noSearch">
					<div class="treeHead">目录树</div>
					<!-- tree -->
					<ul id="tree" class="tree-table-tree-box layui-box layui-tree">
					</ul>
				</div>
				<div class="tableDiv table_noSearch" id="appList"></div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="${basePath}frame/app/js/appList.js"></script>
</html>