<%@ page language="java" pageEncoding="UTF-8"%>







<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=GBK" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>新增功能</title>
<link rel="stylesheet" type="text/css" href="<%=baseURL%>/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="<%=baseURL%>/plugins/static/css/edit.css" />
<script type="text/javascript" src="<%=baseURL%>/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="<%=baseURL%>/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="<%=baseURL%>/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var contextPath = "<%=baseURL%>/";
	var basePath = "${basePath}";
	$(function() {
		$("#funName").focus();
	});
	
	function getRadioValueByName(radioName) {
		var radioValues = document.getElementsByName(radioName);
		for (var i = 0; i < radioValues.length; i++) {
			if (radioValues[i].checked) {
				return radioValues[i].value;
			}
		}
	}

	function gotoSave() {
		var funName = $.trim(document.getElementById("funName").value);
		var funCode = $.trim(document.getElementById("funCode").value);
		var seqNo = $.trim(document.getElementById("seqNo").value);
		var funDesc = $.trim(document.getElementById("funDesc").value);
		var orgRight = getRadioValueByName("orgRight");
		var isAssignable = getRadioValueByName("isAssignable");
		var appID = document.getElementById("appID").value;
		if(document.forms[0].subID.options[document.forms[0].subID.selectedIndex]){
			var subID = document.forms[0].subID.options[document.forms[0].subID.selectedIndex].value;
		} else {
			assemblys.msg("该应用没有子系统，请先新建");
			return;
		}
		
		if (isNaN(seqNo)) {
			assemblys.msg("顺序号不正确，请重新输入");
			document.getElementById("seqNo").focus();
			return;
		}
		var url = contextPath + "frame/appFunsSet/save.spring?1=1";
		var pars = {"funName":funName,"funCode":funCode,"appID":appID,"subID":subID,"funDesc":funDesc,"orgRight":orgRight,"isAssignable":isAssignable,"seqNo":seqNo};
		$.ajax({
			"type" : "post",
			"url" : url,
			"data" : pars,
			"success" : saveHandle,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}

	function saveHandle(doc) {
		var appID = document.getElementById("appID").value;
		var subID = document.forms[0].subID.options[document.forms[0].subID.selectedIndex].value;
		var status = doc.getElementsByTagName("status")[0].childNodes[0].nodeValue;
		if (status != null && status == "1") {
			assemblys.msg("功能名称或者编号已存在，不能重复添加");
		} else {
			assemblys.msg("保存成功", function(index) {
				window.parent.appList();
				assemblys.closeWindow();
			});
		}
	}
</script>
</head>
<body>
	<form action="/frame/appFunsSet/save.spring?1=1" class="layui-form ">
		<input type="hidden" name="appID" value="<c:out value='${appID}'/>" id="appID" />
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					功能编号
				</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="funCode" name="funCode" maxlength="200" lay-verify="required|character" />
				</div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					功能名称
				</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="funName" name="funName" maxlength="200" lay-verify="required|character" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					所属子系统
				</label>
				<div class="layui-input-inline">
					<select name="subID" id="subID">
						<c:forEach items="${subSystems}" var="subSystem">
							<option value="${subSystem.code}" <c:if test="${subID == subSystem.code}">selected</c:if>>${subSystem.name}</option>
						</c:forEach>
					</select>
				</div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					顺序号
				</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="seqNo" name="seqNo" value="1.00" maxlength="10" lay-verify="required|float" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					组织架构
				</label>
				<div class="layui-input-inline">
					<input type="radio" name="orgRight" id="orgRight" value="0" title="否" <c:if test="${orgRight == '0'}">checked</c:if> />
					<input type="radio" name="orgRight" id="orgRight" value="1" title="是" <c:if test="${orgRight == '1'}">checked</c:if> />
				</div>
				<input type="hidden" name="isAssignable" value="1" />
				<%-- 	<label class="layui-form-label">是否可以转授权 </label>
				<div class="layui-input-inline">
					<input type="radio" name="isAssignable" id="isAssignable" value="0" title="否" <c:if test="${isAssignable == '0'}">checked</c:if> />
					<input type="radio" name="isAssignable" id="isAssignable" value="1" title="是" <c:if test="${isAssignable == '1'}">checked</c:if> />
				</div> --%>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">功能描述 </label>
				<div class="layui-input-inline" style="width: 520px;">
					<textarea class="layui-textarea" lay-verify="limit|character" limit="200" id="funDesc" name="funDesc" cols="500" rows="3"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> </label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm " value="保存" lay-submit lay-filter="save" />
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			float : function(value, item) {
				if(parseFloat(value)>99999.9999||parseFloat(value)<=0){
					return "顺序号必须大于0且小于100000";
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		
		form.on("submit(save)", function(data) {
			gotoSave();
		});
		form.render();
	});
</script>
</html>
