<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<table class="layui-table main_table">
	<!--标题栏-->
	<tr class="main_title">
		<td width="90">操作</td>
		<td width="180">功能编号</td>
		<td width="180">功能名称</td>
		<td width="80">组织架构</td>
		<td width="80">顺序号</td>
		<td>功能描述</td>
	</tr>
	<c:set var="hasData" value="0" />
	<c:forEach items="${APPFUNS_LIST}" var="appFuns" varStatus="vs">
		<c:set var="hasData" value="${hasData + 1}" />
		<tr>
			<td align="center">
				<i class="layui-icon layui-icon-edit i_check" title="编辑" param1="<c:out value='${appFuns.funID}'/>" onclick='gotoUpd(this)'></i>
				<i class="layui-icon layui-icon-delete i_delete" title="删除" param1="<c:out value='${appFuns.funID}'/>" onclick='gotoDel(this)'></i>
				<i class="layui-icon layui-icon-add-circle i_check" title="功能点设置" param1="<c:out value='${appFuns.funID}'/>" param2="<c:out value='${appID}'/>" onclick="gotoSet(this)"></i>
				<i class="layui-icon2" title="生成SQL" param1="<c:out value='${appFuns.funID}'/>" param2="<c:out value='${appFuns.funCode}'/>"  param3="<c:out value='${appFuns.funName}'/>"  onclick="exportSql(this)">&#xea80;</i>
			</td>
			<td align="left">
				<c:out value="${appFuns.funCode}" />
			</td>
			<td align="left">
				<c:out value="${appFuns.funName}" />
			</td>
			<td align="center">
				<c:if test="${!appFuns.isOrgRight}">
					<font color="black">否</font>
				</c:if>
				<c:if test="${appFuns.isOrgRight}">
					<font color="red">是</font>
				</c:if>
			</td>
			<td align="center">
				<c:out value="${appFuns.seqNo}" />
			</td>
			<td align="left">
				<c:out value="${appFuns.funDesc}" />
			</td>
		</tr>
	</c:forEach>
	<c:if test="${hasData == 0}">
		<tr class="comTab_R2">
			<td colspan="6" style="text-align: center;">暂无数据！</td>
		</tr>
	</c:if>
</table>
<!-- 当前页 -->
<input type="hidden" id="curPageNum" name="curPageNum" value="${appFunsForm.pageNo}">
<!-- 页总数 -->
<input type="hidden" id="totalPage" name="totalPage" value="${appFunsForm.totalRecord}">
<!-- 当前页 -->
<input type="hidden" id="pageNo" name="pageNo" value="${appFunsForm.pageNo}">
<!-- 单页数 -->
<input type="hidden" id="pageSize" name="pageSize" value="${appFunsForm.pageSize}">
<!-- 分页组件 -->
<div class="layui-table-page layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
	<div id="layui-table-page1" style="margin: 5px;"></div>
</div>
<script type="text/javascript">
	layui.use([ 'laypage', 'form' ], function() {
		var laypage = layui.laypage;
		var form = layui.form;
		laypage.render({
			elem : 'layui-table-page1',
			count : '${appFunsForm.totalRecord}',
			limit : '${appFunsForm.pageSize}',
			curr : '${appFunsForm.pageNo}',
			layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
			jump : function(obj, first) {
				if (!first) {
					document.getElementById("pageNo").value = obj.curr;
					document.getElementById("pageSize").value = obj.limit;
					appList();
				}
			}
		});
		form.render("select", "layui-table-page");
	});
</script>
