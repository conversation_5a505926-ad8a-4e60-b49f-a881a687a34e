<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_APPLICATION);
%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>应用系统管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = basePath + "frame/appaction/";
	
	$(function() {
		assemblys.getMenuIcon({
			funCode : "${funCode}",
			hasOrg : false,
			dom : $("b#menuIcon")
		});
	});
	
	//新增
	function newApp() {
		var url = baseContext + "newApp.spring?1=1";
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '新增应用系统',
				scrollbar : false,
				area : [ '80%', '80%' ],
				content : url
			});
		});
		
	}
	//删除一个应用系统
	function deleteApp(obj) {
		var appId = $(obj).attr("param1");
		var appName = $(obj).attr("param2");
		assemblys.confirm("确认删除应用系统「" + appName + "」吗？", function() {
			var method = "GET";
			var url = baseContext + "deleteApp.spring?appId=" + appId;
			var content = null;
			var responseType = "text";
			var callback = delBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		});
	}

	function delBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "1") {
			assemblys.msg("删除成功", function() {
				document.forms[0].action = baseContext + "index.spring?1=1";
				document.forms[0].submit();
			});
		} else if (http_request.responseText == "CANOT_DEL") {
			assemblys.alert("删除失败！此系统下面有子系统或者菜单和角色");
		} else {
			assemblys.alert("服务器异常");
		}
	}
	//编辑某个应用系统
	function editApp(obj) {
		var appId = $(obj).attr("param1");
		var url = baseContext + "editApp.spring?appId=" + appId;
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '编辑应用系统',
				scrollbar : false,
				area : [ '80%', '80%' ],
				content : url
			});
		});
	}

	//导航到子系统
	function goSubList(appId) {
		document.forms[0].action = baseContext + "goSup.spring?appId=" + appId;
		document.forms[0].submit();
	}

	// 导出初始化SQL
	function exportFrameSQL(obj) {
		var url = basePath + "frame/appaction/exportFrameSQL.spring?1=1";
		url += "&appID=" + obj.getAttribute("param1");
		url += "&appName=" + encodeURIComponent(obj.getAttribute("param2"));
		location.href = url;
	}
</script>
</head>
<body>
	<form name="listForm" action="" method="post" onsubmit="return false;">
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES" />
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr">
				<button type="button" class="layui-btn layui-btn-sm" onclick="newApp()">新增</button>
			</div>
		</div>
		<div class="bodys">
			<div class="tableDiv table_noTree table_noSearch">
				<table class="layui-table main_table">
					<tr class="main_title">
						<td width="80">操作</td>
						<td width="40">LOGO</td>
						<td width="120">应用编号</td>
						<td width="140">应用名称</td>
						<td width="90">子系统</td>
						<td>指向站点</td>
						<td>工作台</td>
						<td>版本信息</td>
						<td width="80">接入方式</td>
						<td width="50">顺序号</td>
					</tr>
					<c:set var="count" value="0" />
					<c:forEach items="${bean}" var="element" varStatus="vs">
						<c:set var="count" value="${count + 1 }" />
						<tr>
							<td align="center">
								<i class="layui-icon layui-icon-edit i_check" title="编辑" param1="<c:out value='${element.appId}'/>" onclick="editApp(this)"></i>
								<i class="layui-icon layui-icon-delete i_delete" title="删除" param1="<c:out value='${element.appId}'/>" param2="<c:out value='${element.appName}'/>" onclick="deleteApp(this)"></i>
								<i class="layui-icon2 i_delete" title="导出" param1="<c:out value='${element.appId}'/>" param2="<c:out value='${element.appName}'/>" onclick="exportFrameSQL(this)">&#xea80;</i>
							</td>
							<td align="center">
								<c:choose>
									<c:when test="${element.logo != null && element.logo != ''}">
										<c:choose>
											<c:when test="${element.logo.indexOf('http') != -1}">
												<img src="${element.logo }" width="50px">
											</c:when>
											<c:otherwise>
												<img src="${basePath}/${element.logo }" width="50px">
											</c:otherwise>
										</c:choose>
									</c:when>
									<c:otherwise>
										<img src="${basePath}frame/common/getLogo.spring?text=${fn:substring(element.appName,0,1)}" width="50px">
									</c:otherwise>
								</c:choose>
							</td>
							<td>
								<c:out value="${element.appCode}" />
							</td>
							<td>
								<c:out value="${element.appName}" />
							</td>
							<td align="center">
								<a href="${basePath}frame/appaction/goSup.spring?1=1&appId=<c:out value="${element.appId}"/>" class="layui-a-hasClick">
									<c:out value="${element.subCount}" />
									个子系统
								</a>
							</td>
							<td>
								<c:out value="${element.server}" />
							</td>
							<td>
								<c:out value="${element.indexPage}" />
							</td>
							<td align="left">
								<c:out value="${element.versionIndex}" />
							</td>
							<td align="center">
								<c:choose>
									<c:when test="${element.accessType == 1}">Session</c:when>
									<c:when test="${element.accessType == 2}">JWT验证</c:when>
									<c:when test="${element.accessType == 3}">单点</c:when>
									<c:otherwise></c:otherwise>
								</c:choose>
							</td>
							<td align="center">
								<c:out value="${element.SeqNo}" />
							</td>
						</tr>
					</c:forEach>
				</table>
				<!-- 分页组件 -->
				<div class="layui-table-page" style="border-width: 1px; height: 38px; padding: 0px; width: auto;">
					<div id="layui-table-page1" style="margin: 5px;"></div>
				</div>
				<!--说明-->
				<div class="comTab_Sn">
					<div>【说明】</div>
					<div class="comTab_SnTxt">
						<li class="comTab_SnLi">
							点击
							<font color=blue>子系统</font>
							栏目进入子系统管理功能；
						</li>
					</div>
				</div>
			</div>
		</div>
	</form>
	<script type="text/javascript">
		layui.use([ 'laypage' ], function() {
			var laypage = layui.laypage;
			laypage.render({
				elem : 'layui-table-page1'//关联的时Id=layui-table-page1
				,
				count : '${page.intRowCount}'//总条数
				,
				limit : '${page.intPageSize}'//行数
				,
				curr : '${page.intPage}'//当前页
				,
				layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
				jump : function(obj, first) {
					if (!first) {
						document.forms[0].action = baseContext + "index.spring?page=" + obj.curr + "&pageSize=" + obj.limit;
						document.forms[0].submit();
					}
				}
			});
			
		});
	</script>
</body>
</html>
