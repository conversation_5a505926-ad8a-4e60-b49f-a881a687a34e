var param = {
	keyword : ""
};

$(function() {
	
	assemblys.getMenuIcon({
		funCode : assemblys.getParam("funCode"),
		hasOrg : false,
		dom : $("b#menuIcon")
	});
	
	// 加载树
	treeList();
	
	// 第一个节点
	$(".layui-tree-txt:eq(1)").click();
});

function treeList() {
	var tree = getNodesName();
	layui.tree.render({
		elem : '#tree',
		click : function(item) {
			var item = item.data;
			
			if (item.code == undefined && item.title != "应用系统") {
				$("#appID").val(item.appID);
				$("#AppName").text("/ " + item.title);
				$("#subID").val(0);
				$("#SubName").text("");
				appList();
			}
			
			if (item.code != 0 && item.code != undefined) {
				$("#appID").val(item.appID);
				$("#AppName").text("/ " + item.appName);
				$("#subID").val(item.code);
				$("#SubName").text("/ " + item.title);
				
				appList();
			}
			
			if (item.title != "应用系统") {
				$("#hide").css("display", "");
			} else {
				$("#hide").css("display", "none");
			}
			
		},
		data : [ {
			"title" : "应用系统",
			"spread" : true,
			children : tree
		} ]
	});
}

function getNodesName() {
	
	var list = "";
	var url = basePath + "frame/funs/appTree.spring";
	$.ajax({
		type : "post",
		url : url,
		dataType : "json",
		async : false,
		success : function(data) {
			if (data.result == "success") {
				for (var i = 0; i < data.treeList.length; i++) {
					if (data.treeList[i].children) {
						for (var j = 0; j < data.treeList[i].children.length; j++) {
							data.treeList[i].children[j].title = data.treeList[i].children[j].name;
							data.treeList[i].children[j].appID = data.treeList[i].appID;
							data.treeList[i].children[j].appName = data.treeList[i].title;
						}
					}
				}
				list = data.treeList;
			} else {
				assemblys.alert("获取目录树出错");
			}
		},
		error : function(data) {
			assemblys.alert("网络错误，请联系网络管理员")
		}
	});
	return list;
}

// 加载内容
function appList() {
	var url = basePath + "frame/appFunsSet/list.spring?1=1";
	// 一开始进入页面
	if ($("#appID").val() == 0) {
		$("#appID").val("4");
		$("#AppName").text("/ 公用");
	}
	$.ajax({
		type : "post",
		url : url,
		data : $(document.forms[0]).serialize(),
		dataType : "html",
		success : function(data) {
			$("#appList").html(data);
		},
		error : function(data) {
			assemblys.alert("网络错误");
		}
	})
}

function newAppFuns() {
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '新增功能',
		scrollbar : false,
		area : [ '850px', '450px' ],
		content : basePath + "frame/appFunsSet/toNew.spring?1=1&" + $(document.forms[0]).serialize(),
		end : function() {
			
		}
	});
}

function gotoUpd(obj) {
	var id = $(obj).attr("param1");
	var appID = document.getElementById("appID").value;
	var subID = document.getElementById("subID").value;
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '编辑功能',
		scrollbar : false,
		area : [ '850px', '450px' ],
		content : basePath + "frame/appFunsSet/toEdit.spring?1=1&funID=" + id + "&appID=" + appID + "&subID=" + subID,
		end : function() {
			
		}
	});
}

function gotoDel(obj) {
	var id = $(obj).attr("param1");
	assemblys.confirm("确定要删除吗?", function() {
		$.ajax({
			url : basePath + "frame/funs/checkHasRightPoint.spring",
			data : {
				funID : id
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					$.ajax({
						type : "post",
						url : basePath + "frame/appFunsSet/del.spring?1=1&funID=" + id,
						data : $(document.forms[0]).serialize(),
						async : false,
						dataType : "json",
						success : function(data) {
							if (data.result == "success") {
								assemblys.msg("删除成功", function() {
									appList();
								});
							} else {
								assemblys.alert("删除出错");
							}
						},
						error : function(data) {
							assemblys.alert("网络错误");
						}
					});
				} else if (data.result == "has") {
					assemblys.msg("当前功能下存在子功能点，请先删除子功能点");
				}
			},
			error : function(data) {
				assemblys.msg("网络错误");
			}
		});
		
	});
	
}

function gotoSet(obj) {
	var funId = $(obj).attr("param1");
	var appId = $(obj).attr("param2");
	var subID = document.getElementById("subID").value;
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '功能点设置',
		scrollbar : false,
		data : $(document.forms[0]).serialize(),
		area : [ '1000px', '600px' ],
		content : basePath + "frame/rightPointSet/list.spring?1=1&funID=" + funId + "&appID=" + appId + "&subID=" + subID,
		end : function() {
		}
	});
}

function exportSql(obj) {
	var funID = $(obj).attr("param1");
	var funCode = $(obj).attr("param2");
	var funName = $(obj).attr("param3");
	var url = basePath + "frame/common/exportSql.spring?sign=func&code=" + funID + "&sqlName=" + encodeURIComponent(funCode + "-" + funName);
	location.href = url;
}
