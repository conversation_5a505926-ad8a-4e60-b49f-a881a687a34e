<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<title>修改功能点</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	
	$(document).ready(function() {
		layui.use([ 'form' ], function() {
			var form = layui.form;
		});
		$("#rightPointName").focus();
	});
	
	function getRadioValueByName(radioName) {
		var radioValues = document.getElementsByName(radioName);
		for (var i = 0; i < radioValues.length; i++) {
			if (radioValues[i].checked) {
				return radioValues[i].value;
			}
		}
	}
	function gotoUpdate() {
		var rightPointName = document.getElementById("rightPointName").value;
		var openToUser = getRadioValueByName("openToUser");
		var rpID = document.getElementById("rpID").value;
		var appID = document.getElementById("appID").value;
		var funID = document.getElementById("funID").value;
		var coding = document.forms[0].coding.options[document.forms[0].coding.selectedIndex].value;
		var funFileUrl = encodeURIComponent(document.getElementById("funFileUrl").value);
		var imgUrl = encodeURIComponent(document.getElementById("imgUrl").value);
		var remark = document.getElementById("remark").value;
		var url = basePath + "frame/rightPointSet/update.spring?1=1";
		var pars = {
			"rpID" : rpID,
			"rightPointName" : rightPointName,
			"openToUser" : openToUser,
			"appID" : appID,
			"funID" : funID,
			"coding" : coding,
			"funFileUrl" : funFileUrl,
			"imgUrl" : imgUrl,
			"remark" : remark
		};
		$.ajax({
			"type" : "post",
			"url" : url,
			"data" : pars,
			"success" : updateHandle,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}
	function updateHandle(doc) {
		var appID = document.getElementById("appID").value;
		var funID = document.getElementById("funID").value;
		var status = doc.getElementsByTagName("status")[0].childNodes[0].nodeValue;
		if (status != null && status == "1") {
			assemblys.msg("功能点类型已存在，不能重复添加");
		} else {
			assemblys.msg("保存成功", function() {
				window.parent.reload();//ie8保存弹窗问题
			});
		}
	}
</script>
<body>
	<form action="/frame/rightPointSet/update.spring?1=1" class="layui-form">
		<input type="hidden" name="appID" id="appID" value="<c:out value='${appFunsForm.appID}'/>">
		<input type="hidden" name="funID" id="funID" value="<c:out value='${appFunsForm.funID}'/>">
		<input type="hidden" name="rpID" id="rpID" value="<c:out value='${rightPointForm.rpID}'/>">
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					功能点名称
				</label>
				<div class="layui-input-inline">
					<input type="text" name="rightPointName" value="<c:out value='${rightPointName}'/>" id="rightPointName" size="30" maxlength="30" class="layui-input">
					<script type="text/javascript">
						$("#rightPointName").attr("lay-verify", "required|character");
					</script>
				</div>
				<div class="layui-form-item"></div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					功能点类型
				</label>
				<div class="layui-input-inline">
					<select name="coding" id="coding">
						<option value="1" <c:if test="${rightPointForm.coding==1}" >selected="selected"</c:if>>浏览</option>
						<option value="2" <c:if test="${rightPointForm.coding==2}" >selected="selected"</c:if>>新增</option>
						<option value="3" <c:if test="${rightPointForm.coding==3}" >selected="selected"</c:if>>编辑</option>
						<option value="4" <c:if test="${rightPointForm.coding==4}" >selected="selected"</c:if>>删除</option>
						<option value="5" <c:if test="${rightPointForm.coding==5}" >selected="selected"</c:if>>执行</option>
						<option value="6" <c:if test="${rightPointForm.coding==6}" >selected="selected"</c:if>>监督</option>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					菜单显示
				</label>
				<div class="layui-input-inline">
					<input type="radio" name="openToUser" id="openToUser" value="0" title="否" <c:if test="${openToUser == '0'}">checked</c:if>>
					<input type="radio" name="openToUser" id="openToUser" value="1" title="是" <c:if test="${openToUser == '1'}">checked</c:if>>
				</div>
				<div class="layui-form-item"></div>
				<label class="layui-form-label">URL </label>
				<div class="layui-input-inline">
					<input type="text" name="funFileUrl" value="<c:out value='${funFileUrl}'/>" id="funFileUrl" size="80" class="layui-input">

				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">快捷图标URL </label>
				<div class="layui-input-inline">
					<input type="text" name="imgUrl" value="${imgUrl}" id="imgUrl" size="80" class="layui-input">

				</div>
				<div class="layui-input-inline"></div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">备注 </label>
				<div class="layui-input-inline">
					<textarea id="remark" name="remark" class="layui-textarea" lay-verify="limit|character" limit="200"><c:out value='${rightPoint.remark}'/></textarea>
				</div>
				<div class="layui-input-inline"></div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm " value="保存" lay-submit lay-filter="save" />
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		form.on("submit(save)", function(data) {
			gotoUpdate();
		});
		form.render();
	});
</script>
</html>
