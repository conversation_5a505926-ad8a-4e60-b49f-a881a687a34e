<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_APPFUNS);
%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta http-equiv="expires" content="0">
<title>功能点列表</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var contextPath = "${basePath}"
	var servletMappingStr = "*.do";
	var basePath = "${basePath}";
	
	$(function() {
		assemblys.getMenuIcon({
			funCode : "${funCode}",
			hasOrg : false,
			dom : $("b#menuIcon"),
			menuName : "功能点列表"
		});
	});
	
	function newRightPoint() {
		var appID = document.getElementById("appID").value;
		var funID = document.getElementById("funID").value;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '新增功能点',
			scrollbar : false,
			area : [ '500px', '530px' ],
			content : contextPath + "frame/rightPointSet/toNew.spring?1=1&appID=" + appID + "&funID=" + funID,
			end : function() {
			}
		});
	}
	function gotoUpd(obj) {
		var id = $(obj).attr("param1");
		var appID = document.getElementById("appID").value;
		var funID = document.getElementById("funID").value;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '功能点编辑',
			scrollbar : false,
			area : [ '500px', '530px' ],
			content : contextPath + "frame/rightPointSet/toEdit.spring?1=1&rpID=" + id + "&appID=" + appID + "&funID=" + funID,
			end : function() {
			}
		});
	}
	function gotoDel(obj) {
		var id = $(obj).attr("param1");
		var appID = document.getElementById("appID").value;
		var funID = document.getElementById("funID").value;
		assemblys.confirm("确定要删除吗?", function() {
			$.ajax({
				"url" : contextPath + "frame/rightPointSet/del.spring?1=1&rpID=" + id + "&appID=" + appID + "&funID=" + funID,
				"dataType" : "json",
				success : function(data) {
					if (data.result == "success") {
						assemblys.msg("删除成功", function() {
							location.href = contextPath + "frame/rightPointSet/list.spring?1=1&appID=" + appID + "&funID=" + funID;
						})
					}
				}
			});
		});
		
	}
	function back() {
		var appID = document.getElementById("appID").value;
		var funID = document.getElementById("funID").value;
		var subID = document.getElementById("subID").value;
		document.forms[0].action = contextPath + "frame/app/appFunsFrame.jsp";
		document.forms[0].submit();
	}
	//ie8弹窗问题解决
	function reload() {
		window.location.href = window.location.href;
	}
</script>
</head>
<body>
	<!--鼠标移动表格变色-->
	<form action="/frame/rightPointSet/list.spring?1=1" class="layui-form">
		<input type="hidden" name="appID" value="<c:out value='${appID}'/>" id="appID" />
		<input type="hidden" name="funID" value="<c:out value='${funID}'/>" id="funID" />
		<input type="hidden" value="<c:out value='${subID}'/>" id="subID" name="subID">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
				(
				<c:out value="${rightPointForm.appName}" />
				/
				<c:out value="${rightPointForm.funName}" />
				)
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" onclick="newRightPoint()" value="新增"></input>
				<!-- <input type="button" class="layui-btn layui-btn-sm  " onclick="back()" value="返回"></input> -->
			</div>
		</div>
		<div class="bodys">
			<!--菜单外框-->
			<div class="tableDiv table_noTree table_noSearch">
				<table class="layui-table main_table" style="margin-bottom: 0;" cellpadding="0" cellspacing="0">
					<!--标题栏-->
					<tr class="main_title">
						<td width="60">操作</td>
						<td width="150">功能点名称</td>
						<td width="150">功能点类型</td>
						<td width="150">是否控制菜单显示</td>
						<td>URL</td>
					</tr>
					<c:forEach items="${APPSUBFUNS_LIST}" var="rightPoint" varStatus="vs">
						<tr>
							<td align="center">
								<i class="layui-icon layui-icon-edit i_check" title="编辑" param1="<c:out value='${rightPoint.rpID}'/>" onclick='gotoUpd(this)'></i>
								<i class="layui-icon layui-icon-delete i_delete" title="删除" param1="<c:out value='${rightPoint.rpID}'/>" onclick='gotoDel(this)'></i>
							</td>
							<td align="center">
								<c:out value="${rightPoint.rpName}" />
							</td>
							<td align="center">
								<c:if test="${rightPoint.rightPoint == '1'}">浏览</c:if>
								<c:if test="${rightPoint.rightPoint == '2'}">新增</c:if>
								<c:if test="${rightPoint.rightPoint == '3'}">编辑</c:if>
								<c:if test="${rightPoint.rightPoint == '4'}">删除</c:if>
								<c:if test="${rightPoint.rightPoint == '5'}">执行</c:if>
								<c:if test="${rightPoint.rightPoint == '6'}">监督</c:if>
							</td>
							<td align="center">
								<c:if test="${!rightPoint.openToUser}">否</c:if>
								<c:if test="${rightPoint.openToUser}">是</c:if>
							</td>
							<td align="left">${rightPoint.FunImplement}</td>
						</tr>
					</c:forEach>
				</table>
			</div>
			<!--说明-->
			<div class="comTab_Sn"></div>
		</div>
		</div>
	</form>
</body>
</html>
