<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_APPLICATION);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>子系统管理</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/common.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var baseContext = "${basePath}frame/appaction/";
	var basePath = "${basePath}";
	
	$(function() {
		assemblys.getMenuIcon({
			funCode : "${funCode}",
			hasOrg : false,
			dom : $("b#menuIcon"),
			menuName : "应用子系统管理"
		});
	});
	
	//新增
	function newSubApp(obj) {
		var appId = $(obj).attr("param1");
		var url = baseContext + "newSubApp.spring?appId=" + appId;
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '新增应用子系统',
				scrollbar : false,
				area : [ '480px', '320px' ],
				content : url
			});
		});
	}
	//删除一个子系统
	function deleteSub(obj) {
		var subId = $(obj).attr("param1");
		var subName = $(obj).attr("param2");
		assemblys.confirm("确认删除系统--[" + subName + "]吗？", function() {
			var method = "GET";
			var url = baseContext + "deleteSub.spring?subId=" + subId;
			var content = null;
			var responseType = "text";
			var callback = delBack;
			$.ajax({
				"url" : url,
				"type" : method,
				"data" : content,
				"dataType" : responseType,
				"success" : callback,
				"error" : function(e) {
					assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
				}
			});
		})
	}
	function delBack(data) {
		var http_request = {
			responseText : data
		};
		if (http_request.responseText == "1") {
			assemblys.msg("删除成功", function() {
				var appId = document.getElementById("appId").value;
				document.forms[0].action = baseContext + "goSup.spring?appId=" + appId;
				document.forms[0].submit();
			});
		} else if (http_request.responseText == "CANOT_DEL") {
			assemblys.alert("删除失败！此系统下面有子系统或者菜单和角色");
		} else {
			assemblys.alert("服务器异常");
		}
	}
	//编辑某个应用系统
	function editSub(obj) {
		var subId = $(obj).attr("param");
		var url = baseContext + "editSub.spring?subId=" + subId;
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '修改应用子系统',
				scrollbar : false,
				area : [ '480px', '320px' ],
				content : url
			});
		});
	}
	//返回
	function back() {
		document.forms[0].action = baseContext + "index.spring?1=1";
		document.forms[0].submit();
	}
</script>
</head>
<body>
	<form action="" method="post" class="layui-form">
		<input type="hidden" id="appId" name="appId" value="<c:out value="${app.appID}"/>">
		<!-- appId -->
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<!-- 是否重复 -->
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr">
				<button type="button" class="layui-btn layui-btn-sm  h28 lh28" param1="<c:out value='${app.appID}'/>" onclick="newSubApp(this)" style="margin-left: 90px;">新增</button>
				<button type="button" class="layui-btn layui-btn-sm skin-btn-normal h28 lh28" onclick="back()">返回</button>
			</div>
		</div>
		<div class="bodys">
			<div class="tableDiv table_noTree table_noSearch">
				<table class="layui-table main_table">
					<!--标题栏-->
					<tr class="main_title">
						<td width="50">操作</td>
						<td>应用编号</td>
						<td>应用名称</td>
						<td width="70">顺序号</td>
					</tr>
					<c:set var="hasData" value="0" />
					<c:forEach items="${subs}" var="element" varStatus="vs">
						<c:set var="hasData" value="${hasData + 1}" />
						<tr>
							<td align="center">
								<i param='<c:out value="${element.subID}"/>' class="layui-icon layui-icon-edit i_check" title="编辑" onclick="editSub(this)"></i>
								<i param1='<c:out value="${element.subID}"/>' param2='<c:out value="${element.subName}"/>' class="layui-icon layui-icon-delete i_delete" title="删除" onclick="deleteSub(this)"></i>
							</td>
							<td>
								<c:out value="${element.subCode}" />
							</td>
							<td>
								<c:out value="${element.subName}" />
							</td>
							<td align="center">
								<c:out value="${element.seqNo}" />
							</td>
						</tr>
					</c:forEach>
					<c:if test="${hasData == 0}">
						<tr class="comTab_R2">
							<td colspan="4" style="text-align: center;">暂无数据！</td>
						</tr>
					</c:if>
				</table>
			</div>
		</div>
	</form>
</body>
</html>
