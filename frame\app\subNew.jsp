<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>新增子系统</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = basePath + "frame/appaction/";
	
	$(function() {
		initNew();
	});
	
	//去空格
	function trim(str) {
		var regBegSpace = /^(\s+)/;
		var regEndSpace = /(\s+)$/;
		var r = str.replace(regBegSpace, "").replace(regEndSpace, "");
		return (r);
	}
	//初始化
	function initNew() {
		document.getElementById("subCode").focus();
	}

	//保存
	function saveSub() {
		var subName = trim(document.getElementById("subName").value);
		var subCode = trim(document.getElementById("subCode").value);
		var seqNo = trim(document.getElementById("seqNo").value);
		var appId = trim(document.getElementById("appId").value);
		var pars = {
			"subName" : subName,
			"subCode" : subCode,
			"seqNo" : seqNo,
			"appId" : appId
		};
		var url = baseContext + "saveSup.spring?1=1";
		$.ajax({
			"type" : "post",
			"url" : url,
			"data" : pars,
			"success" : saveBack,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}
	function saveBack(doc) {
		var status = doc.getElementsByTagName("status")[0].childNodes[0].nodeValue;
		if (status == "THE_RECORD_HAS") {
			assemblys.msg("名称或编号已经存在");
		} else if (status == "ADD_OK") {
			assemblys.msg("保存成功", function() {
				var appId = document.getElementById("appId").value;
				document.forms[0].action = baseContext + "goSup.spring?appId=" + appId;
				document.forms[0].submit();
				//assemblys.closeWindow();
				parent.location.reload();
			})
		} else {
			assemblys.alert("新增出错，请检查服务器是否正常运行");
		}
	}
//-->
</script>
</head>
<body class="body_noTop">
	<form action="" method="post" class="layui-form">
		<input type="hidden" id="appId" name="appId" value="<c:out value="${appId}"/>">
		<div class="bodys bodys_noTop">
			<table class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>
						子系统编号
					</label>
					<div class="layui-input-inline">
						<input type="text" id="subCode" name="subCode" lay-verify="required|character" class="layui-input h28">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>
						子系统名称
					</label>
					<div class="layui-input-inline">
						<input type="text" id="subName" name="subName" lay-verify="required|character" autocomplete="off" class="layui-input h28">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>
						子系统顺序号
					</label>
					<div class="layui-input-inline">
						<input type="text" value="<c:out value="${maxValue}"/>" id="seqNo" name="seqNo" lay-verify="required|integer|seq" autocomplete="off" class="layui-input h28">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"></label>
					<div class="layui-input-inline">
						<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
						<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
					</div>
				</div>
			</table>
		</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写正整数';
				}
			},
			seq : function(value, item) {
				if (parseFloat(value) > 99999.9999 || parseFloat(value) <= 0) {
					return "顺序号必须大于0且小于100000";
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		form.on("submit(save)", function(data) {
			saveSub();
		});
		form.render();
	});
</script>
</html>
