var pubData = {
	// 初始化
	init : function() {
		assemblys.getMenuIcon({
			funCode : param.get("funCode"),
			hasOrg : false,
			dom : $("b#menuIcon")
		}).then(function() {
			// 第一次加载
			pubData.getDataList();
			// 监听
			pubData.loadMonitor();
		})
	},
	/**
	 * 获取数据列表
	 */
	getDataList : function() {
		assemblys.tableRender({
			elem : '#list',
			url : basePath + 'frame/apply/getVisitorApplyList.spring?' + param.__form(),
			height : "auto",
			cols : [ [ {
				title : '操作',
				width : 80,
				align : "center",
				templet : function(d) {
					var html = '';
					html += '<i class="layui-icon layui-icon-edit  i_check"   lay-event="exec" title="审核" style="cursor: pointer"></i>';
					if (d.state == "1") {
						html += '<i class="layui-icon layui-icon-friends i_check"   lay-event="role" title="角色分配" style="cursor: pointer"></i>';
					}
					return html;
				}
			}, {
				title : '状态',
				align : "center",
				width : 100,
				templet : function(d) {
					return d.state == "1" ? "<font style=\"color: green;\">已审核</font>" : "<font style=\"color: red;\">待审核</font>";
				}
			}, {
				title : '单位/公司',
				align : "left",
				width : 150,
				templet : function(d) {
					var html = '';
					html += assemblys.htmlDecode(d.organ);
					return html;
				}
			}, {
				title : '姓名',
				align : "left",
				width : 150,
				templet : function(d) {
					var html = '';
					html += assemblys.htmlDecode(d.name);
					return html;
				}
			}, {
				title : '手机号',
				align : "left",
				minWidth : 150,
				templet : function(d) {
					var html = '';
					html += assemblys.htmlDecode(d.phone);
					return html;
				}
			}, {
				title : '备注',
				align : "left",
				minWidth : 300,
				templet : function(d) {
					var html = '';
					html += assemblys.htmlDecode(d.remark);
					return html;
				}
			}, {
				title : 'ip',
				align : "center",
				width : 150,
				templet : function(d) {
					var html = '';
					html += assemblys.htmlDecode(d.ip);
					return html;
				}
			}, {
				title : '申请时间',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.applyDate ? assemblys.dateToStr(d.applyDate) : "";
				}
			}, {
				title : '有效期至',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.validDate ? assemblys.dateToStr(d.validDate) : "";
				}
			} ] ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
			},
			// 绑定事件
			events : {
				exec : pubData.exec,
				role : pubData.role
			
			}
		});
		
	},
	exec : function(data) {
		// 申请页面
		var url = basePath + "frame/apply/visitorApplyEdit.html?wxOpenID=" + data.openID;
		layer.open({
			content : url,
			type : 2,
			skin : 'layui-layer-aems',
			title : "审核",
			scrollbar : false,
			area : [ '600px', '400px' ]
		});
	},
	role : function(data) {
		var userName = data.name;
		var relationUserID = data.relationUserID;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '给当前用户【 ' + userName + "】分配角色",
			scrollbal : false,
			area : [ '600px', '530px' ],
			content : basePath + "frame/useraction/editRole.spring?exclude=APP&userId=" + relationUserID + "&userName=" + encodeURIComponent(userName) + "&CompNo=FK9999",
			end : function() {
				$(".layui-laypage-btn").click();
			}
		});
	},
	// 加载监听器
	loadMonitor : function() {
		// 监听提交
		var form = layui.form;
		form.on("radio(state)", function(data) {
			pubData.getDataList();
		});
		form.on("checkbox(valid)", function(data) {
			pubData.getDataList();
		});
		// 渲染
		form.render();
	}
}

pubData.init();
