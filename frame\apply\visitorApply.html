<!DOCTYPE html>
<html>
<head>
<title>公告信息</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css">
<style type="text/css">
.layui-form-label {
	width: 150px;
}

input[type=text] {
	width: 550px;
}

.help {
	margin-left: 5px;
	color: blue;
	cursor: pointer;
}
</style>
<body class="skin-0">
	<form class="layui-form" onsubmit="return false" lay-filter="param">
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					你的单位/公司名
				</label>
				<div class="layui-input-inline">
					<input type="text" name="organ" placeholder="请输入正确的信息，以便审核通过" lay-verify="required|specialCharacters|limit" limit="200" maxlength="200" autocomplete="off" class="layui-input h28">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					你的姓名
				</label>
				<div class="layui-input-inline">
					<input type="text" name="name" placeholder="请填写" lay-verify="required|specialCharacters|limit" limit="200" maxlength="200" autocomplete="off" class="layui-input h28">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					你的手机号码
				</label>
				<div class="layui-input-inline">
					<input type="text" name="phone" placeholder="请输入正确的手机号码，以便审核通过" lay-verify="required|limit|num" limit="200" maxlength="200" autocomplete="off" class="layui-input h28">
				</div>
			</div>
			<!-- <div class="layui-form-item">
				<label class="layui-form-label"> 邮箱 </label>
				<div class="layui-input-inline">
					<input type="text" name="email" placeholder="请填写" lay-verify="limit" limit="200" maxlength="200" autocomplete="off" class="layui-input h28">
				</div>
			</div> -->
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					备注
				</label>
				<div class="layui-input-block">
					<textarea name="remark" cols="500" rows="3" placeholder="例如：我想了解一下：医务管理、不良事件、投诉纠纷、指标管理、危急值管理等产品" lay-verify="required|limit" limit="2000" style="height: 180px;" class="layui-textarea"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					验证码
				</label>
				<div class="layui-input-inline" style="width: 500px;">
					<input type="text" name="gzhAuthCode" placeholder="请填写" lay-verify="required|limit" limit="6" maxlength="6" autocomplete="off" class="layui-input h28" style="width: 140px; display: inline-block;" />
					<input type="button" id="getAuth" class="layui-btn layui-btn-sm " value="点击获取" />
					<span class="help">收不到验证码？</span>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> </label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm btn_save" value="申请" lay-submit lay-filter="save" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript">
	var wxOpenID;
	var compNo;
	var isSubmit = false;
	var hasSubmit = false;
	
	$(function() {
		assemblys.alert("你当前没有访问权限，请完善下列申请信息，获取访客账号，我们将在1~3个工作日处理，请留意官方公众号信息");
		// 获取ID
		wxOpenID = assemblys.getParam("wxOpenID");
		compNo = assemblys.getParam("compNo");
		// 监听
		var form = layui.form;
		form.on("submit(save)", function() {
			save();
		});
		$("#getAuth").on("click", function() {
			getAuth();
		});
		$(".help").on("click", function() {
			assemblys.alert("如果收不到验证信息，请检查是否屏蔽了公众号通知");
		});
		// 渲染
		form.render();
	});
	
	function save() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		var formData = param.__json();
		formData["wxOpenID"] = wxOpenID;
		formData["compNo"] = compNo;
		$.ajax({
			url : basePath + "frame/apply/saveVisitorApply.spring",
			type : "post",
			data : formData,
			dataType : "json",
			success : function(data) {
				assemblys.alert("申请成功，请等待管理员审批", function() {
					assemblys.closeWindow();
				});
			}
		});
	}

	function getAuth() {
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		$.ajax({
			url : basePath + "frame/excludeUrl/scanCode/getGZHAuthCode.spring",
			type : "get",
			data : {
				"wxOpenID" : wxOpenID,
				"compNo" : compNo
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					assemblys.msg("已发送验证码");
					var second = 60;
					var timer = setInterval(function() {
						second--;
						$("#getAuth").val(second + " 秒后可再次获取").css("background", "#BEBEBE");
						if (second <= 1) {
							$("#getAuth").val("点击获取").css("background", "");
							clearInterval(timer);
							hasSubmit = false;
						}
					}, 1000);
				} else {
					assemblys.alert("验证码获取失败，请稍后再试", function() {
						hasSubmit = false;
					});
				}
			}
		});
	}
</script>
</html>
