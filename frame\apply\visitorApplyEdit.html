<!DOCTYPE html>
<html>
<head>
<title>公告信息</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css">
<style type="text/css">
.help {
	margin-left: 5px;
	color: blue;
	cursor: pointer;
}
</style>
<body class="skin-0">
	<form class="layui-form" onsubmit="return false" lay-filter="param">
		<input type="hidden" name="wxOpenID" value="">
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					有效期至
				</label>
				<div class="layui-input-inline">
					<input type="text" id="time" name="validDate" placeholder="请选择时间" lay-verify="required" readonly="readonly" autocomplete="off" class="layui-input h28">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> </label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm btn_save" value="审批" lay-submit lay-filter="save" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript">
	$(function() {
		var form = layui.form;
		form.on("submit(save)", function() {
			save();
		});
		form.render();
		//执行一个laydate实例
		assemblys.dateRender({
			trigger : 'click',
			elem : '#time',
			type : "date",
			ready : function(date) {
				//可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
	});
	
	function save() {
		if (window.hasSubmit) {
			return;
		}
		window.hasSubmit = true;
		$.ajax({
			url : basePath + "frame/apply/updateVisitorApply.spring",
			type : "post",
			data : {
				"wxOpenID" : param.get("wxOpenID"),
				"validDate" : param.get("validDate")
			},
			dataType : "json",
			success : function(data) {
				assemblys.msg("处理成功", function() {
					parent.pubData.getDataList();
					assemblys.closeWindow();
				});
			}
		});
	}
</script>
</html>
