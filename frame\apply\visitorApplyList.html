<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>单点登录</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<style type="text/css">
.layui-icon2 {
	cursor: pointer;
}

.hideBtn {
	display: none;
}
</style>
</head>
<body>
	<form class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="funCode" value="">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr"></div>
		</div>
		<div class="bodys">
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">关键字</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" id="keyword" name="keyword" value="" autocomplete="off" placeholder="单位/姓名/手机号" title="单位/姓名/手机号" class="layui-input">
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="查询" onclick="pubData.getDataList();">
				<label class="layui-form-label2">状态</label>
				<input type="radio" name="state" value="" title="全部" lay-filter="state" checked="checked" />
				<input type="radio" name="state" value="0" title="待审核" lay-filter="state" />
				<input type="radio" name="state" value="1" title="已审核" lay-filter="state" />
				<input type="checkbox" name="valid" value="1" title="只看过期" lay-filter="valid" lay-skin="primary">
			</div>
			<!-- 表格 -->
			<div class="tableDiv table_noTree">
				<div id="list"></div>
			</div>
		</div>
	</form>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/visitorApplyList.js"></script>
</html>
