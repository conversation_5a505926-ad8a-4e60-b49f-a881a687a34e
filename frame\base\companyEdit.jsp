<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="org.hyena.frame.constant.ApiInterfaceConstant"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
	String baseURL = request.getContextPath();
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("SYSTEM_APP_CODE", ApiInterfaceConstant.SYSTEM_APP_CODE);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>编辑医院资料</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var baseContext = "${basePath}/frame/comp/";
	var basePath = "${basePath}";
	var cityID = "${comp.cityID}";
	var SYSTEM_APP_CODE = "";
	
	$(function(){
		
		SYSTEM_APP_CODE = $("#SYSTEM_APP_CODE").val();
		
		initCity();
		
	})
	//去空格
	function trim(str) {
		var regBegSpace = /^(\s+)/;
		var regEndSpace = /(\s+)$/;
		var r = str.replace(regBegSpace, "").replace(regEndSpace, "");
		return (r);
	}
	
	function initCity() {
		
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			data : {
				"dictTypeCode" : "SYSTEM_APP_CITY",
				"appCode" : SYSTEM_APP_CODE
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var cityList = data.dictList;
					for (var i = 0; i < cityList.length; i++) {
						$("#cityId").append('<option value="'+cityList[i].dictCode+'" '+(cityID == cityList[i].dictCode ? 'selected':'')+'>' + cityList[i].dictName + '</option>');
					}
					layui.use("form", function() {
						layui.form.render("select", "cityId");
					});
				}
			}
		});
		
	}
	
	//保存
	function editSubmit() {
		var compNo = trim(document.getElementById("compNo").value);
		var compName = trim(document.getElementById("compName").value);
		var compName2 = trim(document.getElementById("compName2").value);
		var shortName = trim(document.getElementById("shortName").value);
		var empNoPre = trim(document.getElementById("empNoPre").value);
		var bossEmpNo = trim(document.getElementById("bossEmpNo").value);
		var bossEmpName = trim(document.getElementById("bossEmpNoName").value);
		var principalEmpNo = trim(document.getElementById("principalEmpNo").value);
		var cityId = document.getElementById("cityId").value || 0;
		var prizeName = (trim(document.getElementById("prizeName").value) == 1) ? "花红" : "奖金";
		var indId = document.getElementById("indId").value;
		var seqNo = trim(document.getElementById("seqNo").value);
		var creatDate = trim(document.getElementById("creatDate").value);
		var compUse = trim(document.getElementById("compUse").value);
		var cancelDate = trim(document.getElementById("cancelDate").value);
		var compAddress = trim(document.getElementById("compAddress").value);
		var remark = trim(document.getElementById("remark").value);
		var postCode = trim(document.getElementById("postCode").value);
		var phone = trim(document.getElementById("phone").value);
		var compID = trim(document.getElementById("compID").value);
		
		if (isNaN(seqNo)) {
			assemblys.msg("顺序号不正确，请重新输入");
			document.getElementById("seqNo").focus();
			return;
		}	
		
		var url = baseContext + "editSaveComp.spring?1=1";
		$.ajax({
			"url" : url,
			"type" : "post",
			"data" : {
				"compNo" : compNo,
				"compName" : compName,
				"compName2" : compName2,
				"shortName" : shortName,
				"empNoPre" : empNoPre,
				"bossEmpNo" : bossEmpNo,
				"bossEmpName" : bossEmpName,
				"principalEmpNo" : principalEmpNo,
				"cityId" : cityId,
				"prizeName" : prizeName,
				"indId" : indId,
				"seqNo" : seqNo,
				"creatDate" : creatDate,
				"compUse" : compUse,
				"cancelDate" : cancelDate,
				"compAddress" : compAddress,
				"remark" : remark,
				"postCode" : postCode,
				"phone" : phone,
				"compID": compID
			},
			"success" : saveBack,
			"error" : function(e) {
				assemblys.msg("服务器异常，请稍后再试或联系电脑部...");
			}
		});
	}
	function saveBack(doc) {
		var temp = doc.getElementsByTagName("status")[0].childNodes[0].nodeValue;
		if (temp == "EDIT_COMP_OK") {
			assemblys.msg("保存成功", function() {
				assemblys.closeWindow();
			})
		} else if (temp == "HAS_SEQ") {
			document.getElementById("seqNo").select();
			assemblys.msg("保存失败，顺序号重复");
		} else if (temp == "NOHAS_EMP_FR") {
		} else if (temp == "NOHAS_EMP_PR") {
			assemblys.msg("医院负责人编号不存在或者已经离职");
		} else if (temp == "yes_Cancle") {
			assemblys.msg("医院名简称已存在，且已被取消");
			document.getElementById("shortName").select();
		} else if (temp == "yes") {
			assemblys.msg("医院名简称已存在");
			document.getElementById("shortName").select();
		} else {
			assemblys.alert("保存出错，请检查服务器是否正常运行");
		}
	}
</script>
</head>
<body>
	<form action="" method="post" onsubmit="return false;" class="layui-form">
		<input type="hidden" id="compNo" name="compNo" value="<c:out value="${comp.compNo}"/>">
		<input type="hidden" id="compID" name="compID" value="<c:out value="${comp.compID}"/>">
		<input type="hidden" id="SYSTEM_APP_CODE" name="SYSTEM_APP_CODE" value="<c:out value='${SYSTEM_APP_CODE}'/>">
		<div class="bodys bodys_noTop ">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					医院第一全称
				</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="compName" name="compName" value="<c:out value="${comp.compName}"/>" lay-verify="required|limit|character" limit="50" maxlength="50" />
				</div>
				<label class="layui-form-label"> 医院第二全称 </label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="compName2" name="compName2" value="<c:out value="${comp.compName2}"/>" lay-verify="limit|character" limit="20" maxlength="20" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					医院名简称
				</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="shortName" name="shortName" value="<c:out value="${comp.shortName}"/>" lay-verify="required|limit|character" limit="20" maxlength="20" />
				</div>
				<label class="layui-form-label"> 邮政编码 </label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" id="postCode" name="postCode" value="<c:out value="${comp.postCode}"/>" lay-verify="limit" limit="6" maxlength="6" value="">
				</div>
			</div>
			<input type="hidden" id="empNoPre" name="empNoPre" value="">
			<input value="" id="principalEmpNoName" name="principalEmpNoName" type="hidden">
			<input value="" id="principalEmpNo" name="principalEmpNo" type="hidden">
			<input value="1" id="prizeName" name="prizeName" type="hidden">
			<input value="0" id="indId" name="indId" type="hidden">
			<input value="0" id="compUse" name="compUse" type="hidden">
			<div class="layui-form-item">
				<label class="layui-form-label">法人代表</label>
				<div class="layui-input-inline">
					<input class="layui-input" id="bossEmpNoName" name="bossEmpNoName" value="<c:out value="${comp.bossEmpName}"/>" type="text" lay-verify="limit" limit="20" maxlength="20" value="<c:out value="${comp.bossEmpName}"/>">
					<input class="layui-input" id="bossEmpNo" name="bossEmpNo" type="hidden" value="<c:out value="${comp.bossEmpNo}"/>" lay-verify="limit" limit="20" maxlength="20" value="">
				</div>
				<label class="layui-form-label">所在城市</label>
				<div class="layui-input-inline layui-form" lay-filter="cityId">
					<select class="layui-input" id="cityId" lay-search="">
						<option value='0'>请选择</option>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">创建日期</label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" name="creatDate" id="creatDate" value="<fmt:formatDate value="${comp.creatDate}" pattern="yyyy-MM-dd"/>" />
					<i class="layui-icon layui-icon-date i_time2" style="right: 4px; top: 11px;"></i>
				</div>
				<label class="layui-form-label">
					<span class="start">*</span>
					顺序号
				</label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" id="seqNo" name="seqNo" value="<c:out value="${comp.seqNo}"/>" lay-verify="required|limit|float" limit="7" maxlength="7" value="">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">单位地址</label>
				<div class="layui-input-inline">
					<input class="layui-input" id="compAddress" name="compAddress" value="<c:out value="${comp.compAddress}"/>" type="text" lay-verify="limit" limit="100" size="80" maxlength="100" value="">
				</div>
				<label class="layui-form-label"> 联系电话 </label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" id="phone" name="phone" value="<c:out value="${comp.phone}"/>" lay-verify="limit" limit="12" value="" maxlength="12">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">取消日期</label>
				<div class="layui-input-inline">
					<input class="layui-input" type="text" name="cancelDate" id="cancelDate" readonly="readonly" value="<fmt:formatDate value="${comp.cancelDate}" pattern="yyyy-MM-dd"/>" />
					<i class="layui-icon layui-icon-date i_time2" style="right: 4px; top: 11px;"></i>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 备注 </label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" lay-verify="limit|character" limit="50" id="remark" name="remark" cols="50" rows="3"><c:out value="${comp.remark}" /></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			float : function(value, item) {
				if(parseFloat(value)>99999.9999||parseFloat(value)<=0){
					return "顺序号必须大于0且小于100000";
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			}
		});
		form.on("submit(save)", function(data) {
			editSubmit(data);
		});
		layui.use('laydate', function() {
			var laydate = layui.laydate;
			//执行一个laydate实例
			laydate.render({
				elem : '#creatDate'//指定元素
				,trigger: 'click' //采用click弹出
				,ready: function(date){
				      //可以自定义时分秒
				      var now = new Date();
				      this.dateTime.hours=now.getHours();
				      this.dateTime.minutes=now.getMinutes();
				     }
			});
			laydate.render({
				elem : '#cancelDate' //指定元素
				,trigger: 'click' //采用click弹出
				,ready: function(date){
				      //可以自定义时分秒
				      var now = new Date();
				      this.dateTime.hours=now.getHours();
				      this.dateTime.minutes=now.getMinutes();
				     }
			});
		});
	});
</script>
</html>
