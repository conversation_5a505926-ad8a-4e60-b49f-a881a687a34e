<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.ApiInterfaceConstant"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@page import="org.hyena.frame.view.*,java.util.*,org.hyena.frame.service.*"%>
<!DOCTYPE html>
<html>
<%
	String path = request.getContextPath();
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";

	//查询参数处理
	int compUse = -1;
	if (request.getParameter("compUse") != null && !request.getParameter("compUse").equals(""))
		compUse = Integer.parseInt(request.getParameter("compUse"));
	String cityID = "0";
	if (request.getParameter("cityID") != null && !request.getParameter("cityID").equals(""))
		cityID = request.getParameter("cityID");
	int indID = 0;
	if (request.getParameter("indID") != null && !request.getParameter("indID").equals(""))
		indID = Integer.parseInt(request.getParameter("indID"));

	request.setAttribute("basePath", basePath);
	request.setAttribute("cityID", cityID);
	request.setAttribute("SYSTEM_APP_CODE", ApiInterfaceConstant.SYSTEM_APP_CODE);
%>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-Control" content="no-cache,must-revalidate">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta http-equiv="Expires" content="0">
<title>医院列表</title>
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var baseContext = basePath + "/frame/comp/";
	var cityID = "";
	var SYSTEM_APP_CODE = "";
	
	$(function() {
		
		assemblys.getMenuIcon({
			funCode : assemblys.getParam("funCode"),
			hasOrg : false,
			dom : $("b#menuIcon")
		});
		
		cityID = $("input[param='cityID']").val();
		SYSTEM_APP_CODE = $("input[param='SYSTEM_APP_CODE']").val();
		initCity();
	})

	function initCity() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			data : {
				"dictTypeCode" : "SYSTEM_APP_CITY",
				"appCode" : SYSTEM_APP_CODE
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var cityList = data.dictList;
					for (var i = 0; i < cityList.length; i++) {
						$("#cityID").append('<option value="' + cityList[i].dictCode + '" ' + (cityID == cityList[i].dictCode ? 'selected' : '') + '>' + cityList[i].dictName + '</option>');
					}
					layui.use("form", function() {
						layui.form.render("select", "cityIDDiv");
					});
				}
			}
		});
		
	}

	//新增
	function newSubmit() {
		var url = baseContext + "newComp.spring?1=1";
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '新增医院',
				scrollbar : false,
				area : [ '850px', '80%' ],
				content : url,
				end : function() {
					document.forms[0].submit();
				}
			});
		});
	}

	//编辑
	function editComp(obj) {
		var compNo = $(obj).attr("param1");
		var url = baseContext + "editComp.spring?compNo=" + compNo;
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '编辑医院',
				scrollbar : false,
				area : [ '850px', '80%' ],
				content : url,
				end : function() {
					document.forms[0].submit();
				}
			});
		});
	}

	// 分配系统模块
	function exceApp(obj) {
		var compNo = $(obj).attr("param1");
		var compName = $(obj).attr("param2");
		var url = basePath + "/frame/base/companyRights.jsp?compNo=" + compNo;
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				title : '对「' + compName + '」进行应用系统授权',
				scrollbar : false,
				area : [ '650px', '320px' ],
				content : url
			});
		});
	}

	function onCheckForm() {
		document.getElementById('curPage').value = "1";
	}
</script>
</head>
<body>
	<form class="layui-form" action="" method="post" onsubmit="return onCheckForm()">
		<input type="hidden" id="reSubmit" name="reSubmit" value="YES">
		<input type="hidden" param="SYSTEM_APP_CODE" value="<c:out value='${SYSTEM_APP_CODE}'/>">
		<input type="hidden" param="cityID" value="<c:out value='${cityID}'/>">
		<!-- 是否重复 -->
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr">
				<div class="layui-input-inline">
					<div class="layui-input-inline layui-form" lay-filter="cityIDDiv">
						<select size="1" name="cityID" id="cityID" lay-search="" lay-filter="cityID" class="e_type h31 fw700">
							<option value="0">全部地区</option>
						</select>
					</div>
				</div>
				<div class="layui-input-inline">
					<input type="checkbox" name="showCancel" id="showCancel" title="显示已取消医院" lay-skin="primary" lay-filter="showCancel" <c:if test="${showCancel == 'yes' }">checked="checked"</c:if> />
				</div>
				<button type="button" class="layui-btn layui-btn-sm  h28 lh28" onclick="newSubmit()">新增</button>
			</div>
		</div>
		<div class="bodys">
			<div class="tableDiv table_noTree table_noSearch">
				<table class="layui-table main_table">
					<!--标题栏-->
					<tr class="main_title">
						<td width="40">操作</td>
						<td width="70">医院编号</td>
						<td>医院第一名称</td>
						<td>医院简称</td>
						<td width="100" style="display: none;">医院负责人</td>
						<td width="100">法人代表</td>
						<td width="100">所在城市</td>
						<td width="60">顺序号</td>
					</tr>
					<c:set var="hasData" value="0" />
					<c:forEach items="${compList}" var="element" varStatus="vs">
						<c:set var="hasData" value="${hasData + 1}" />
						<tr>
							<td align="center">
								<i class="layui-icon layui-icon-edit i_check" title="编辑" param1="<c:out value='${element.compNo}'/>" onclick="editComp(this)"></i>
								<i class="layui-icon layui-icon-share i_check" title="分配系统" param1="<c:out value='${element.compNo}'/>" param2="<c:out value="${element.compName}" />" onclick="exceApp(this)"></i>
							</td>
							<td align="center">
								<c:out value="${element.compNo}" />
							</td>
							<td align="left">
								<a class="layui-a-hasClick" param1="<c:out value='${element.compNo}'/>" onclick="editComp(this)">
									<c:out value="${element.compName}" />
								</a>
							</td>
							<td align="left">
								<c:out value="${element.shortName}" />
							</td>
							<td align="center" style="display: none;">
								<c:out value="${element.pmpName}" />
								<c:if test="${!empty element.principalEmpNo}">
									[<c:out value="${element.principalEmpNo}" />]
								</c:if>
							</td>
							<td align="left">
								<c:out value="${element.bossEmpName}" />
							</td>
							<td align="left">
								<c:out value="${element.cityName}" />
							</td>
							<td align="center">
								<c:out value="${element.seqNo}" />
							</td>
						</tr>
					</c:forEach>
					<c:if test="${hasData == 0}">
						<tr class="comTab_R2">
							<td colspan="7" style="text-align: center;">暂无数据！</td>
						</tr>
					</c:if>
				</table>
				<!-- 分页组件 -->
				<div class="layui-table-page layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
					<div id="layui-table-page1" style="margin: 5px;"></div>
				</div>
			</div>
		</div>
	</form>
	<script type="text/javascript">
		layui.use([ 'form', 'laypage', 'layer' ], function() {
			var form = layui.form, layer = layui.layer;
			var laypage = layui.laypage;
			laypage.render({
				elem : 'layui-table-page1',
				count : '${page.intRowCount}',
				limit : '${page.intPageSize}',
				curr : '${page.intPage}',
				layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
				jump : function(obj, first) {
					if (!first) {
						document.forms[0].action = baseContext + "list.spring?page=" + obj.curr + "&pageSize=" + obj.limit;
						document.forms[0].submit();
					}
				}
			});
			form.render("select", "layui-table-page");
			
			form.on("checkbox(showCancel)", function(data) {
				document.forms[0].submit();
			});
			form.on("select(cityID)", function(data) {
				document.forms[0].submit();
			});
			form.render();
		})
	</script>
</body>
</html>
