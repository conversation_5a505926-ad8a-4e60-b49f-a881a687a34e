<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<title>新增科室</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/edit.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/frame/js/common.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var network_err = "网站服务器繁忙，请稍后提交";
	var isSubmit = false;// 防止重复提交
	
	// 保存
	function saveDept() {
		if (!isSubmit) {
			isSubmit = true;
			var url = basePath + "frame/department/doDepartmentEditSub.spring?";
			var param = $("#departmentForm").serialize();
			$.ajax({
				type : "post",
				data : param,
				url : url,
				success : function(data) {
					if (data.result != "error") {
						if (data == "DEPT_REPEAT") {
							var deptShortName = $("#deptShortName").val();
							assemblys.msg("科室名称 [" + deptShortName + "] 已经存在");
							$("#deptShortName").focus();
							isSubmit = false;
						} else if (data == "EXIST") {
							assemblys.msg("当前科室下尚存在科室人员，取消失败");
							isSubmit = false;
						} else {
							var deptName = data;
							assemblys.msg("修改科室 [" + deptName + "] 成功", function() {
								isSubmit = false;
								parent.initTree();
								parent.$("div[data-id='" + $("#deptId").val() + "'] span.layui-tree-txt").text(deptShortName);
								assemblys.closeWindow();
							});
						}
					} else {
						assemblys.msg("你请求的页面有异常");
						isSubmit = false;
					}
					
				},
				error : function() {
					assemblys.msg(network_err);
					isSubmit = false;
				}
			});
		}
	}

	function cancelDept() {
		var deptId = $("#deptId").val();
		msg = "取消科室前请确保该科室的相关资料已经妥善处理，确定吗？";
		if (!isSubmit) {
			assemblys.confirm(msg, function() {
				isSubmit = true;
				var parentDeptId = $("#parentDeptId").val();
				var remark = encodeURIComponent($("#remark").val());
				url = basePath + "frame/department/doDepartmentCancel.spring?deptId=" + deptId + "&parentDeptId=" + parentDeptId;
				url += "&remark=" + remark;
				
				$.ajax({
					type : "post",
					url : url,
					success : function(data) {
						if (data == "SUCCESS") {
							assemblys.msg("取消科室成功", function() {
								assemblys.closeWindow()
							});
						} else if (data == "EXIST") {
							assemblys.msg("当前科室下尚存在科室人员，取消失败");
							isSubmit = false;
						} else {
							assemblys.msg("你请求的页面有异常");
							isSubmit = false;
						}
					},
					error : function() {
						assemblys.msg(network_err);
						isSubmit = false;
					}
				});
			}, function() {
				isSubmit = false;
			});
		}
		isSubmit = true;
	}

	function restoreDept() {
		if (!isSubmit) {
			isSubmit = true;
			var deptId = $("#deptId").val();
			var parentDeptId = $("#parentDeptId").val();
			var url = basePath + "frame/department/doDepartmentRestore.spring?deptId=" + deptId + "&parentDeptId=" + parentDeptId;
			$.ajax({
				type : "post",
				url : url,
				success : function(data) {
					if (data != "ERROR") {
						assemblys.msg("恢复科室成功", function() {
							location.reload();
						});
					} else {
						assemblys.msg("你请求的页面有异常");
					}
					isSubmit = false;
				},
				error : function() {
					assemblys.msg(network_err);
					isSubmit = false;
				}
			});
		}
	}
//-->
</script>
</head>
<body>
	<form id="departmentForm" class="layui-form" name="departmentForm" onkeydown="nextInput()" method="post">
		<input type="hidden" id="compNo" name="compNo" value="<c:out value="${compNo}"/>">
		<input type="hidden" id="compName" name="compName" value="<c:out value="${compName}"/>">
		<input type="hidden" id="parentDeptId" value="<c:out value="${deptId}"/>">
		<input type="hidden" id="parentDeptName" value="<c:out value="${deptName}"/>">
		<input type="hidden" id="deptId" name="deptId" value="<c:out value="${department.deptId}"/>">
		<input type="hidden" id="deptClass" value="<c:out value="${department.deptClass}"/>">
		<div class="bodys  bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">所属医院 </label>
				<div class="layui-input-inline">
					<input type="text" readonly="readonly" class="layui-input " value="<c:out value="${compName}"/>" />
				</div>
				<label class="layui-form-label">所属科室 </label>
				<div class="layui-input-inline">
					<select size="1" name="parentId" id="parentId" lay-search >
						<option value="0">-= 无上级科室 =-</option>
						<c:forEach items="${departmentList}" var="dept" varStatus="vs">
							<c:if test="${dept.deptId!=department.deptId}">
								<option value="${dept.deptId}" <c:if test="${dept.deptId==department.parentId}">selected</c:if>>${dept.deptName}</option>
							</c:if>
						</c:forEach>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					科室名称
				</label>
				<div class="layui-input-inline">
					<input type="text" name="deptShortName" lay-verify="required|limit|character" id="deptShortName" size="0" value="${department.deptShortName}" class="layui-input" />
				</div>
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					顺序号
				</label>
				<div class="layui-input-inline">
					<input type="text" name="seqNo" id="seqNo" lay-verify="required|integer|seq" maxlength="13" limit="13" value="<fmt:parseNumber value='${department.seqNo}' pattern='#0.00'></fmt:parseNumber>" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 科室分类 </label>
				<div class="layui-input-inline">
					<select name="deptClass" lay-filter="deptClass">
					</select>
				</div>
				<label class="layui-form-label"> 科室电话 </label>
				<div class="layui-input-inline">
					<input type="text" name="deptTel" id="deptTel"  value="${department.deptTel}" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 是否更新 <i class="layui-icon2" whetherUpdateTips>&#xe890;</i></label>
				<div class="layui-input-inline">
					<select name="whetherUpdate">
						<option value="1" <c:if test="${department.whetherUpdate == 1}">selected</c:if>>是</option>
						<option value="0" <c:if test="${department.whetherUpdate == 0}">selected</c:if>>否</option>
					</select>
				</div>
			</div>
			<c:if test="${syncStatu }">
				<div class="layui-form-item">
					<label class="layui-form-label">同步备注 </label>
					<div class="layui-input-inline" style="width: 520px;">
						<textarea maxlength="200" name="syncChange" id="syncChange" cols="70" rows="3" class="layui-textarea" id="syncChange"></textarea>
					</div>
				</div>
			</c:if>
			<input type="hidden" name="planNum" value="20">
			<input type="hidden" name="deptUse" value="0" />
			<input type="hidden" name="deptleaderId" value="0" />
			<input type="hidden" name="mgrEmpNo" value="" />
			<input type="hidden" name="indId" value="0" />
			<div class="layui-form-item">
				<label class="layui-form-label">备注 </label>
				<div class="layui-input-inline" style="width: 520px;">
					<textarea maxlength="1000" name="remark" id="remark" cols="70" rows="3" lay-verify="character" class="layui-textarea">${department.remark}</textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-block">
					<c:if test='${department.cancelDate != null}'>
						<font color="gray">
							<fmt:formatDate pattern="yyyy-MM-dd HH:mm" value="${department.cancelDate}" />
							(已取消)
						</font>
						<input type="button" class="layui-btn btn_save" value="恢复" onclick="restoreDept();">
					</c:if>
					<c:if test="${department.cancelDate == null}">
						<input type="button" class="layui-btn btn_save" lay-submit value="保存" lay-filter="save">
						<input type="button" class="layui-btn btn_save layui-btn-danger" value="取消科室" onclick="cancelDept();">
					</c:if>
					<input type="button" class="layui-btn btn_back" value="关闭" onclick="assemblys.closeWindow()">
				</div>
			</div>
		</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (value) {
					if (!(/^[0-9]*$/.test(value))) {
						return "只能填写整数";
					}
				}
			},
			seq : function(value, item) {
				if (parseFloat(value) > 99999.9999 || parseFloat(value) <= 0) {
					return "顺序号必须大于0且小于100000";
				}
			},
			character : function(value, item) {
				if (value.indexOf("<") > -1 || value.indexOf(">") > -1 || value.indexOf("'") > -1 || value.indexOf("\"") > -1 || value.indexOf("&") > -1 || value.indexOf("%") > -1) {
					return "不能包含【<】【>】【'】【\"】【&】【%】";
				}
			},
			tel : function(value, item) {
				if (value) {
					var phoneReg = /(^1[3-9][0-9]{9}$)|(^[0-9]{3}-[0-9]{8}$)|(^[0-9]{4}-[0-9]{8}$)|(^[0-9]{8}$)|(^[0-9]{10}$)|(^[0-9]{3}-[0-9]{3}-[0-9]{4}$)/;
					if (!phoneReg.test(value)) {
						return "请输入正确的手机号或电话号！";
					}
				}
			}
		
		});
		
		form.on("submit(save)", function(data) {
			saveDept();
		});
		
		form.render();
		
		$("#deptShortName").focus();
	});
</script>
</html>
<script type="text/javascript" src="${basePath}frame/base/js/departmentAdd.js"></script>