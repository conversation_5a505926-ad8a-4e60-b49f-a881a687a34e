<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>
<table class="layui-table main_table">
	<input type="hidden" id="syncStatu" name="syncStatu" value="${syncStatu}">
	<!--标题栏-->
	<thead>
		<tr class="main_title">
			<td width="80">序号</td>
			<td width="200">科室编号</td>
			<td width="200">科室名称</td>
			<td width="200">所属医院</td>
			<td width="200">科室分类</td>
			<td width="200">科室电话</td>
			<c:if test="${syncStatu}">
				<td>同步备注</td>
			</c:if>
			<td width="80">顺序号</td>
		</tr>
	</thead>
	<c:if test="${! empty departmentList}">
		<!--列表内容 -->
		<c:forEach items="${departmentList}" var="dept" varStatus="s">
			<tr class="comTab_R${s.count%2+1}">
				<td style="text-align: center;">${s.count}</td>
				<td style="text-align: left;">${dept.DeptCode}</td>
				<td style="text-align: left;">
					<a class="layui-a-hasClick" compNo="<c:out value="${dept.compNo }" />" compName="<c:out value="${dept.compName }"/>" deptId="<c:out value="${dept.deptId}"/>" deptName="<c:out value="${dept.deptName}"/>" syncStatu="<c:out value="${syncStatu }"/>" onclick="editDept(this)">
						<c:out value="${dept.deptShortName}" />
					</a>
					<c:if test="${dept.childrenCount > 0}">
						<i title="当前科室存在下级科室" style="cursor: help; color: #009688;" class="layui-icon layui-icon-senior"></i>
					</c:if>
				</td>
				<td style="text-align: center;">${dept.compName}</td>
				<td style="text-align: center;">${dept.DeptClassName}</td>
				<td style="text-align: center;">${dept.deptTel}</td>
				<c:if test="${syncStatu}">
					<td style="text-align: center;">
						<c:out value="${dept.SyncChange}" />
					</td>
				</c:if>
				<td style="text-align: center;">${dept.seqNo}</td>
			</tr>
		</c:forEach>
	</c:if>
	<c:if test="${ empty departmentList}">
		<tr class="comTab_R2">
			<c:set var="trNum" value="7" />
			<c:if test="${syncStatu}">
				<c:set var="trNum" value="8" />
			</c:if>
			<td colspan="${trNum }" style="text-align: center;">暂无数据！</td>
		</tr>
	</c:if>
</table>
<!-- 分页组件 -->
<div class="layui-table-page layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
	<div id="layui-table-page1" style="margin: 5px;"></div>
</div>
<!--说明-->
<div class="comTab_Sn">
	<div>【说明】</div>
	<div class="comTab_SnTxt">
		<li class="comTab_SnLi" style="margin-left: 30px">只显示同级别的科室</li>
	</div>
</div>
<script type="text/javascript">
	var layer = layui.layer;
	var laypage = layui.laypage;
	var form = layui.form;
	laypage.render({
		elem : 'layui-table-page1',
		count : '${page.intRowCount}',
		limit : '${page.intPageSize}',
		curr : '${page.intPage}',
		layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
		jump : function(obj, first) {
			if (!first) {
				getDeptList(obj.curr, obj.limit);
			}
		}
	});
	form.render("select", "layui-table-page");
</script>
