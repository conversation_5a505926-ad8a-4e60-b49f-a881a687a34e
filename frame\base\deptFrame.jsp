<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%@ page import="org.springframework.context.ApplicationContext"%>
<%@ page import="org.springframework.web.context.support.WebApplicationContextUtils"%>
<%@ page import="org.hyena.frame.Globals,org.hyena.frame.view.*"%>
<%@ page import="org.hyena.frame.service.FrameRightService"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);

	// 权限类
	ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(application);
	FrameRightService frameRightService = (FrameRightService) ac.getBean("FtnAEMSFrameRightService");
	// 用户
	User user = (User) session.getAttribute(Globals.KEY_USER);

	// 同步器执行操作
	boolean syncExec = frameRightService.hasRight(Integer.parseInt(user.getUserId()),
			BaseConstant.FUN_CODE_FRAME_DEPT, String.valueOf(Globals.RIGHTPOINT_EXEC), true);
	request.setAttribute("syncExec", syncExec);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>科室科室列表</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/common.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
	var isSubmit = false;
	
	$(document).ready(function() {
		assemblys.getMenuIcon({
			funCode : assemblys.getParam("funCode"),
			hasOrg : false,
			dom : $("b#menuIcon")
		}).then(function() {
			$("#compNo").val("");
			// 加载树
			initTree();
		})
	});
</script>
</head>
<body>
	<iframe name="importframe" style="display: none;"></iframe>
	<form id="form1" action="" method="post" class="layui-form">
		<input type="hidden" id="compNo" name="compNo" value="">
		<input type="hidden" id="compName" name="compName" value="">
		<input type="hidden" id="deptId" name="deptId" value="">
		<input type="hidden" id="deptName" name="deptName" value="">
		<input type="hidden" id="hasChildren" name="hasChildren" value="">
		<!-- 标题栏 -->
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr">
				<span id="toolBar" style="display: none;">
					<label id="fileName" class="layui-form-label2  fw700" style="padding-top: 7px;"></label>
					<div class="layui-btn layui-btn-sm skin-btn-minor" id="selectFile">
						选择文件
						<input style="width: 68px; height: 31px; position: absolute; top: 5px; right: 178px; cursor: pointer; opacity: 0; filter: alpha(opacity : 0); z-index: 999;" type="file" id="excelurl" name="excelurl" onchange="setFileName()" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
					</div>
					<input type="button" value="模板下载" class="layui-btn layui-btn-sm skin-btn-minor" onclick="downloadTemplate()" />
					<input id="import" type="button" value="导入" class="layui-btn layui-btn-sm skin-btn-minor" onclick="importexcel();" />
					<input type="button" value="新增" class="layui-btn layui-btn-sm " onclick="newDept()" />
				</span>
			</div>
		</div>
		<div class="bodys">
			<div class="layui-tab head0_right " style="width: 100%; line-height: 28px;">
				<label class="layui-form-label2"> 关键字： </label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" id="serchWhere" class="layui-input" placeholder="科室名称" title="科室名称" name="serchWhere" size="20" value="" />
				</div>
				<button type="button" onclick="getDeptList(1, 20);" value="查询" class="layui-btn layui-btn-sm">查询</button>
				<label class="layui-form-label2"> 状态 </label>
				<input type="radio" name="cancelState" value="0" title="全部" lay-filter="cancelState" <c:if test="${cancelState==0}">checked</c:if> />
				<input type="radio" name="cancelState" value="1" title="未取消" lay-filter="cancelState" <c:if test="${cancelState!=0 && cancelState!=2}">checked</c:if> />
				<input type="radio" name="cancelState" value="2" title="已取消" lay-filter="cancelState" <c:if test="${cancelState==2}">checked</c:if> />
				<c:if test="${syncExec }">
					<div class="fr">
						<div class="inblock filter_item fr" onclick="syncDeptAndUser();">
							<i class="layui-icon2" style="font-size: 16px;" title="同步科室人员">&#xea5a;</i>
							同步科室人员
						</div>
					</div>
				</c:if>
			</div>
			<div class="layui-row ">
				<div class="treeDiv">
					<div class="treeHead">目录树</div>
					<!-- tree -->
					<ul id="tree" class="tree-table-tree-box layui-box layui-tree">
					</ul>
				</div>
				<div class="tableDiv"></div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/base/js/deptFrameList.js?ver=1.3"></script>