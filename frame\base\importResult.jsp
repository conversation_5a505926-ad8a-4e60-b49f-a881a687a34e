<%@ page contentType="text/html; charset=utf-8" language="java"%>
<html>
<script type="text/javascript">
	var result = "${result}";
	var errors = "${errors}";
	switch (result) {
	case "success":
		try {
			if (errors != "") {
				parent.assemblys.closeMsg();
				setTimeout(function() {
					parent.isSubmit = false;
					parent.assemblys.alert(errors + "无法导入，员工编号或手机号已存在", function() {
						window.parent.location.reload();
					});
				}, 100)
			} else {
				parent.assemblys.closeMsg();
				setTimeout(function() {
					parent.isSubmit = false;
					parent.assemblys.alert("导入成功", function() {
						window.parent.location.reload();
					});
				}, 100)
			}
			//window.parent.toRefresh(); 
		} catch (e) {
		}
		break;
	case "errorType":
		try {
			parent.assemblys.alert("excel类型不匹配，请检查上传excel类型", function() {
				window.parent.location.reload();
			});
		} catch (e) {
		}
		break;
	case "errorEmpty":
		try {
			parent.assemblys.alert("excel为空");
			window.parent.location.reload();
		} catch (e) {
		}
		break;
	case "errorFormat":
		try {
			parent.assemblys.alert("excel格式不匹配，请使用模板", function() {
				window.parent.location.reload();
			});
		} catch (e) {
		}
		break;
	case "errorDeptUser":
		try {
			parent.assemblys.alert("${message}", function() {
				window.parent.location.reload();
			});
		} catch (e) {
		}
		break;
	case "errorChar":
		try {
			parent.assemblys.alert("${message}不能包含【<】【>】【'】【\"】【&】【%】", function() {
				window.parent.location.reload();
			});
		} catch (e) {
		}
		break;
	case "error":
		try {
			parent.assemblys.alert("导入失败", function() {
				window.parent.location.reload();
			});
		} catch (e) {
		}
		break;
	case "errorDB":
		try {
			parent.assemblys.alert("导入数据库失败，请联系管理员", function() {
				window.parent.location.reload();
			});
		} catch (e) {
		}
		break;
	default:
		try {
			parent.assemblys.alert("上传excel出错，请联系管理员", function() {
				window.parent.location.reload();
			});
		} catch (e) {
		}
	}
</script>
</html>
