$(function() {
	getExceAppList();
});

function getExceAppList() {
	$.ajax({
		type : "post",
		url : basePath + "/frame/comp/getExceAppList.spring",
		data : {
			compNo : compNo
		},
		dataType : "json",
		success : function(data) {
			if (data.result == "success") {
				var appList = data.appList;
				var html = "";
				for (var i = 0; i < appList.length; i++) {
					html += '<input type="checkbox" lay-skin="primary" lay-filter="checkComp" ' + (appList[i].compAppID > 0 ? "checked" : "") + ' title="' + appList[i].appName + '" onclick="setCompApp(' + appList[i].appID + ',this)" />'
				}
				$("#compAppList").empty();
				$("#compAppList").html(html);
				layui.use([ 'form' ], function() {
					var form = layui.form;
					form.on('checkbox(checkComp)', function(data) {
						data.elem.onclick();
					});
					form.render();
				});
			} else {
				assemblys.alert("获取后台数据出错，请联系管理员");
			}
		},
		error : function() {
			assemblys.alert("获取数据出错，请联系管理员");
			isSubmit = false;
		}
	});
}

function setCompApp(appID, obj) {
	if (hasSubmit) {
		return;
	}
	var flag = $(obj).prop("checked");
	hasSubmit = true;
	$.ajax({
		type : "post",
		url : basePath + "/frame/comp/setCompApp.spring",
		data : {
			compNo : compNo,
			appID : appID,
			type : (flag ? "add" : "del")
		},
		dataType : "json",
		success : function(data) {
			hasSubmit = false;
			if (data.result == "success") {
				assemblys.msg((flag ? "授权" : "取消") + "成功");
			} else {
				assemblys.alert("分配失败，请刷新或重试", function() {
					getExceAppList();
				});
			}
		},
		error : function() {
			assemblys.alert("分配数据出错，请联系管理员");
			isSubmit = false;
		}
	});
}