/**
 * 新增科室
 */
var departmentAdd = {
	//字典获取科室类型
	getDeptClass : function() {
		return $.ajax({
			url : basePath + "frame/department/getDeptClass.spring",
			type : "get",
			dataType : "json",
		}).then(function(data) {
			if (data.length > 0) {
				$("select[name='deptClass']").empty();
				$("select[name='deptClass']").append('<option value="0">无</option>');
				var optins = '';
				var deptClass = $("#deptClass").val();
				for (var i = 0; i < data.length; i++) {
					optins += '<option ' + (deptClass == data[i].dictContent ? "selected" : "") + ' value="' + data[i].dictContent + '">' + data[i].dictName + '</option>';
				}
				$("select[name='deptClass']").append(optins);
				layui.form.render();
			}
		});
	},
	init : function() {
		departmentAdd.getDeptClass();
		departmentAdd.initTips();
	},
	initLayui : function() {
		
	},
	initTips : function() {
		$("i[whetherUpdateTips]").mouseenter(function(e) {
			assemblys.tips(this, "同步数据时，是否更新当前科室，此设置不包括科室下的用户。是：更新，否：不更新。", 0, "bottom");
		});
	}
}

departmentAdd.init();