function spreadNode(data, spreadID) {
	var isSpread = false;
	for (var i = 0; i < data.length; i++) {
		if (data[i].id == spreadID) {
			data[i].spread = true;
			return true;
		}
		
		if (data[i].children) {
			isSpread = spreadNode(data[i].children, spreadID);
		}
		
		if (isSpread) {
			data[i].spread = true;
			return isSpread;
		}
	}
	return isSpread;
}

function initTree() {
	// 树
	var tree_data = getNodes();
	
	if (window.spreadID) {
		spreadNode(tree_data[0].children, window.spreadID);
	}
	
	var form = layui.form;
	layui.tree.render({
		id : "deptTree",
		elem : '#tree',
		click : function(item) {
			
			var bean = item.data;
			// 如果是顶级，就隐藏按钮
			if (bean.code && bean.code == "common") {
				window.spreadID = "";
				$("#toolBar").css("display", "none");
			} else {
				window.spreadID = bean.id;
				$("#toolBar").css("display", "");
			}
			var compNo = "";
			var compName = "";
			var deptID = "";
			var deptName = "";
			if (bean.hasChildren == undefined) {
				compNo = bean.id || "";
				compName = bean.title;
			} else {
				compNo = bean.compNo || "";
				compName = bean.compName;
				deptID = bean.id;
				deptName = bean.title;
			}
			
			goC(compNo, compName, deptID, deptName);
			
		},
		data : tree_data,
		onlyIconControl : true
	});
	
	form.on('radio(cancelState)', function(data) {
		initTree();
		getDeptList(1, 20);
	});
	form.render();
	
	// 第一个节点
	var compNo = $("#compNo").val() || "";
	if (compNo == "") {
		$(".layui-tree-txt:eq(0)").click();
	}
	
}

function getNodes() {
	var treeType = "CSD";
	var firstOrg = "c";
	var funCode = "FRAME_DEPT";
	var orgUse = "99";
	
	// 为了保持contorller处理数据的一致性
	var list = "";
	var url = basePath + "frame/department/getTreeList.spring?treeType=" + treeType + "&org=" + firstOrg + "&funCode=" + funCode + "&orgUse=" + orgUse + "&cancelState=" + $("input[name='cancelState']:checked").val();
	$.ajax({
		type : "post",
		url : url,
		dataType : "json",
		async : false,
		success : function(data) {
			if (data.result == "success") {
				list = data.compList;
			}
		},
		error : function(data) {
			assemblys.alert("网络错误，请联系网络管理员")
		}
	});
	return list.length > 1 ? [ {
		"title" : "医疗管控平台",
		"spread" : true,
		children : list,
		code : "common"
	} ] : list;
}

// 计算布局
function computeSize() {
	var tableHeight = 600;
	var height = $(window).height();
	var treeHeight = (height / 100 * 96) + "px";
	// 计算树高度
	$(".treeDiv").css({
		"min-height" : treeHeight,
		"max-height" : treeHeight
	})
	// 计算表格高度
	return tableHeight = height / 100 * 86;
}

// 点击医院
function goC(compNo, compName, deptID, deptName) {
	$("#deptId").val(deptID);
	$("#deptName").val(deptName);
	$("#compNo").val(compNo);
	$("#compName").val(compName);
	$("#hasChildren").val("true");
	getDeptList(1, 20);
}

// 新增科室
function newDept() {
	var compNo = document.getElementById("compNo").value;
	var compName = document.getElementById("compName").value;
	var deptId = document.getElementById("deptId").value;
	var deptName = document.getElementById("deptName").value;
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '新增科室',
		scrollbal : false,
		area : [ '850px', '420px' ],
		content : basePath + "frame/department/toDepartmentAdd.spring?compNo=" + compNo + "&compName=" + encodeURIComponent(compName) + "&deptId=" + deptId + "&deptName=" + encodeURIComponent(deptName),
		end : function() {
//			reloadTreeData()
			getDeptList(1, 20);
		}
	});
}

function editDept(obj) {
	var compNo = $(obj).attr("compNo");
	var compName = $(obj).attr("compName");
	var deptId = $(obj).attr("deptId");
	var deptName = $(obj).attr("deptName");
	var syncStatu = $(obj).attr("syncStatu");
	
	var url = basePath + "frame/department/toDepartmentEdit.spring?compNo=" + compNo + "&compName=" + encodeURIComponent(compName) + "&deptId=" + deptId + "&deptName=" + encodeURIComponent(deptName) + "&curDeptId=" + deptId + "&syncStatu=" + syncStatu;
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		title : '编辑科室',
		scrollbal : false,
		area : [ '850px', '420px' ],
		content : url,
		end : function() {
			getDeptList(1, 20);
		}
	});
}

function downloadTemplate() {
	// 判断如果是Ipad则不支持导出功能功能
	var ua = navigator.userAgent.toLowerCase();
	var s = ua.match(/iPad/i);
	if (s == "ipad") {
		assemblys.msg("Ipad不支持该处导出功能，请更换设备导出");
		return false;
	}
	var url = basePath + "frame/common/downloadTemplate.spring?fileName=" + encodeURIComponent("科室人员模板.xls");
	location.href = url;
	
}

function importexcel() {
	if (isSubmit) {
		assemblys.msg("正在处理...");
		return;
	}
	isSubmit = true;
	var fileObj = document.getElementById("excelurl");
	if (fileObj.value == fileObj.defaultValue) {
		assemblys.msg("请先上传excel");
		isSubmit = false;
		return;
	} else if (compNo == '') {
		assemblys.msg("请先选择医院");
		isSubmit = false;
		return;
	}
	var compNo = document.getElementById("compNo").value;
	var url = basePath + "frame/department/importFile.spring?compNo=" + compNo;
	$("#form1").attr("action", url);
	$("#form1").attr("method", "post");
	$("#form1").attr("enctype", "multipart/form-data");
	$("#form1").attr("target", "importframe");
	assemblys.loadingMsg("正在导入...");
	$("#form1").submit();
}

function setFileName() {
	var fileName = $("#excelurl").val();
	if (fileName != null && fileName.length > 0) {
		// 获取文件名 并显示文件名
		var name = fileName;
		if (fileName.length > 40) {
			fileName = fileName.substring(0, 40) + "...";
		}
		$("#fileName").text(fileName);
		$("#fileName").attr("title", name);
	} else {
		// 清空文件名
		$("#fileName").text("");
		$("#fileName").removeAttr("title");
	}
}

function getDeptList(page, intPageSize) {
	var url = basePath + "frame/department/toDepartmentList.spring?page=" + page + "&intPageSize=" + intPageSize;
	var ser = $("form").serialize();
	$.ajax({
		url : url,
		data : ser,
		success : function(data) {
			$(".tableDiv").empty().append(data);
		}
	});
}

function syncDeptAndUser() {
	assemblys.confirm("该操作会实时触发科室人员接口定时器同步，你确定要继续吗？", function() {
		var url = basePath + "/frame/interface/testInterfaceSync.spring";
		$.ajax({
			type : "get",
			url : url,
			success : function(data) {
				assemblys.alert("已执行同步<br><span style='color:red;'>如同步失败，请检查【DeptAndUserInterface】接口配置或【接口日志】是否正常，若成功可以手动刷新查看同步结果</span>");
			}
		});
	});
}
