<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans 
			http://www.springframework.org/schema/beans/spring-beans.xsd 
			http://www.springframework.org/schema/context 
			http://www.springframework.org/schema/context/spring-context.xsd 
			http://www.springframework.org/schema/tx 
			http://www.springframework.org/schema/tx/spring-tx.xsd 
			http://www.springframework.org/schema/task 
			http://www.springframework.org/schema/task/spring-task-4.1.xsd 
			http://www.springframework.org/schema/aop 
			http://www.springframework.org/schema/aop/spring-aop.xsd">
	<!-- 此配置适合Annotation，非常优雅、简单的spring配置文件，减少编写冗余的配置文件 huangxin 2012.3 -->

	<!-- datasource 配置 -->
	<bean id="datasource" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="resourceRef">
			<value>false</value>
		</property>
		<property name="jndiName">
			<value>java:comp/env/jdbc/FtnAppCommon</value>
		</property>
	</bean>

	<!-- sessionFactory 配置 -->
	<!-- 非Annotation时,使用org.springframework.orm.hibernate3.LocalSessionFactoryBean, 它注入实体类的方式是setMappingResources(),而Hibernate Annotation所用的映射方式 不是mapping resource,而是mapping class,这就要用到LocalSessionFactoryBean的子类 AnnotationSessionFactoryBean了.因为AnnotationSessionFactoryBean它支持实体的注入 方式setAnnotatedClasses,即对应Hibernate中的mapping class.参见这两个类的源代码. -->
	<bean id="sessionFactory" class="org.hibernate.SessionFactory">
		<property name="dataSource">
			<ref bean="datasource" />
		</property>
	</bean>

	<!-- 基本事务定义,使用transactionManager作事务管理 -->
	<bean id="txHibernateManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="datasource" />
	</bean>

	<!-- 文件上传 -->
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="524288000" />
	</bean>

	<!-- springMVC中普通类调用注解service方法 -->
	<bean id="springBeanFactoryUtils" class="org.hyena.frame.util.SpringBeanFactoryUtils" />

	<!-- 参数校验器 -->
	<bean id="validator" class="org.hyena.frame.config.argument.MustValidator" />

	<tx:advice id="txAdvice" transaction-manager="txHibernateManager">
		<tx:attributes>
			<tx:method name="save*" propagation="REQUIRED" rollback-for="Exception" />
			<tx:method name="update*" propagation="REQUIRED" />
			<tx:method name="exec*" propagation="REQUIRED" />
			<tx:method name="insert*" propagation="REQUIRED" />
			<tx:method name="edit*" propagation="REQUIRED" />
			<tx:method name="del*" propagation="REQUIRED" />
			<tx:method name="*" propagation="SUPPORTS" />
		</tx:attributes>
	</tx:advice>

	<!-- 为事务管理定义切面 -->
	<aop:config proxy-target-class="true">
		<aop:advisor pointcut="execution(* org.hyena.frame..service.*.*(..))" advice-ref="txAdvice" />
	</aop:config>

	<!-- 使Spring关注Annotation -->
	<context:annotation-config />

	<!-- 让Spring通过自动扫描来查询和管理Bean -->
	<context:component-scan base-package="org.hyena.frame">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<!-- 让Spring启用task扫描 -->
	<task:annotation-driven />

	<!-- 定时器需要执行的代码(开始):同步科室人员 -->
	<bean id="synchroDeptAndUserTask" class="org.hyena.frame.task.SynchroDeptAndUserTask" />
	<task:scheduled-tasks>
		<task:scheduled ref="synchroDeptAndUserTask" method="synchroDeptAndUser" cron="0 0 1 * * ?" />
	</task:scheduled-tasks>

</beans>
