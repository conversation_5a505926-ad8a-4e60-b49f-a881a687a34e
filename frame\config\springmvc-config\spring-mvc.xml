<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:task="http://www.springframework.org/schema/task"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans 
			http://www.springframework.org/schema/beans/spring-beans.xsd 
			http://www.springframework.org/schema/context 
			http://www.springframework.org/schema/context/spring-context.xsd 
			http://www.springframework.org/schema/tx 
			http://www.springframework.org/schema/tx/spring-tx.xsd 
			http://www.springframework.org/schema/task  
			http://www.springframework.org/schema/task/spring-task.xsd
			http://www.springframework.org/schema/aop 
			http://www.springframework.org/schema/aop/spring-aop.xsd
			http://www.springframework.org/schema/mvc 
			http://www.springframework.org/schema/mvc/spring-mvc.xsd">

	<mvc:annotation-driven validator="validator">  
        <mvc:message-converters register-defaults="true">  
            <bean class="org.hyena.frame.config.response.JSONObjectHttpMessageConverter"></bean>  
        </mvc:message-converters>  
    </mvc:annotation-driven>
    
	<context:component-scan base-package="org.hyena" use-default-filters="false">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller" />
		<context:exclude-filter type="aspectj" expression="org.hyena.national..*"/>
	</context:component-scan>
	
	<mvc:default-servlet-handler />
	<mvc:resources location="/_customize/,/" mapping="/**"/>
</beans>