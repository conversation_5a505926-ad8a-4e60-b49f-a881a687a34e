/*总样式*/
* { border: 0; margin: 0; padding: 0; }
a { text-decoration: none; }
a:hover {
	text-decoration: underline;
}
body {
	background: #fff;
	color: #333333;
	font-family: "宋体 Courier New";
	font-size: 12px;
	line-height: 20px;
}
/*蓝色栏目框*/
.portlet-header {
	margin: 0px;
	padding: 5px 16px;
	cursor: move;
	font-size: 14px;
	background-repeat: no-repeat;
	background-position: 5px center;
	text-indent: 18px;
	font-weight: bold;
	color: #4878AD;
	background-color: #CEEBF7;
	background-image: url(../../images/default/bi/circle.jpg);
}
.portlet-content {
	width: 98%;
	margin: 1.5%;
}

/*表格样式*/
.table THEAD {
	background-image: url(../../images/default/bi/table-tr.jpg);
	font-family: Tahoma, Arial, Helvetica;
	font-size:  12px;
	color: #4878AD;
	text-align: left;
	text-indent: 12px;
	line-height: 30px;
	background-repeat: no-repeat;
	background-position: left bottom;
}
.table TFOOT {
    font-family: Tahoma, Arial, Helvetica;
    font-size:  12px;
    color: #cccccc;
	text-indent: 12px;
}

.table td {
	border-top:0px solid gray;
	border-left:0px solid gray;
	border-bottom: 1px dotted gray;
	overflow: hidden;
	height: 25px;
	text-indent: 12px;
	line-height: 25px;
}
.table tr:hover{ background:#eeeeee;}
	
.table1 THEAD {
	height: 25px;
	background-image: url(../../images/default/bi/table-tr.jpg);
	font-family: Tahoma, Arial, Helvetica;
	font-size:  12px;
	color: black;
}
.table1 TFOOT {
	height: 25px;
    font-family: Tahoma, Arial, Helvetica;
    font-size:  12px;
    color: green;
}
.table1 td {
	overflow: hidden;
	text-align: center;
	height: 25px;
	vertical-align: middle;
	
}		
.bg1{ background: #EEEEEE;}

.table2 THEAD {
	height: 25px;
	background-image: url(../../images/default/bi/table-tr.jpg);
	font-family: Tahoma, Arial, Helvetica;
	text-align: left;
	font-size:  12px;
	color: black;
	text-indent: 12px;
}
.table2 TFOOT {
	height: 25px;
    font-family: Tahoma, Arial, Helvetica;
    font-size:  12px;
    color: green;
}
.table2 td {
	overflow: hidden;
	height: 25px;
	vertical-align: middle;
	text-indent: 12px;
	min-width: 50px;
}		



	
/*每日一笑*/
#divJoke .portlet-content #div2 img {
	float: left;
	height: 130px;
	width: 130px;
	display: block;
}
#div2 div {
	display: block;
	float: left;
	height: 116px;
	width: 130px;
}
.joke_nav {
	display: block;
	text-align: right;
}



/*待办事宜*/
.list1 {
	background-color: #FFFFFF;
	width: 80px;
	height: 70px;
	float: left;
	font-size: 12px;
	text-align: center;
	line-height: 14px;
	padding-left: 6px;
	padding-left: 12px;
	padding-top: 12px;
	padding-bottom: 10px;
}
/*微博关注人*/
.list2 {
	background-color: #FFFFFF;
	height: 80px;
	width: 70px;
	float: left;
	font-size: 12px;
	text-align: center;
	line-height: 20px;
	padding-top: 10px;
	padding-bottom: 10px;
	
}




.blue-border {
	border: 1px solid #C2DBD5;
}

//--------新加样式 begin----------
.container { margin: 0 auto; width: 100%; }
.column { float: left; padding-bottom: 100px; }
.portlet { margin: 0 8px 8px 0; }
.portlet-header .ui-icon { float: right; cursor:pointer;}
.btn {background: #e2f4ff url("../../../portal/css/images/ui-bg_glass_75_e6e6e6_1x400.png") 50% 50% repeat-x; padding: 4px 5px 0 5px; border: 1px solid #c8e9f0;}
.btn_cur {background: #e2f4ff url("../../../portal/css/images/ui-bg_glass_75_dadada_1x400.png") 50% 50% repeat-x; padding: 4px 5px 0 5px; border: 1px solid #c8e9f0;}
//---------------end---------------