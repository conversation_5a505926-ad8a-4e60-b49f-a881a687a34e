/* ======================================================
// * <p>Title:			海边沙滩 </p>
// * <p>Copyright:		Copyright (c) 2012 </p>
// * <p>Company:		Fortune </p>
// * @version:			1.0
// * @date:				2012-11-22
// ======================================================
*/

@charset "utf-8";
/* CSS Document */
html,body{height:100%;} 
body{
	margin:0px;
	background-color:#000;
	background:url(../../images/default/bottomMenu/bg.jpg);
}
#right{
	float:left;
	font-family:\5b8b\4f53,Arial,sans-serif;
	color:#333;
	font-size:12px;
	padding-left:30px;
	padding-top:7px;
	background:url(../../images/default/bottomMenu/logo15x14.png) no-repeat 10px 6px;
}
#split{
	float:left;
	width:10px;
	padding-left:10px;
	background:url(../../images/default/bottomMenu/seperator.jpg) no-repeat 10px 4px;
}
#tel{
	float:left;
	font-family:\5b8b\4f53,Arial,sans-serif;
	color:#333;
	font-size:12px;
	padding-left:20px;
	padding-top:7px;
	letter-spacing:1px;
}

#online{
	float:right;
	font-family:\5b8b\4f53,Arial,sans-serif;
	color:#333;
	font-size:12px;
	padding-right:20px;
	padding-top:7px;
	letter-spacing:1px;
}

#online a {color:#252525; text-decoration:none; font:12px \5b8b\4f53,Arial,sans-serif;}

#online a:hover {color:#0000FF;text-decoration:none;}
#msgNum{
	float:right;
	font-family:\5b8b\4f53,Arial,sans-serif;
	color:#333;
	font-size:12px;
	padding-left:8px;
	padding-top:7px;
	letter-spacing:1px;
}
#msgNum a {color:#252525; text-decoration:none; font:12px \5b8b\4f53,Arial,sans-serif;}
#msgNum a:hover {color:#0000FF;text-decoration:none;}