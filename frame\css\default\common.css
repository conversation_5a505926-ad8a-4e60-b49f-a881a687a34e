/* ======================================================
 * <p>Title:		公共页面样式－默认皮肤 </p>
 * <p>Description:	页面样式标准CSS，用于新增、修改、列表等页面 </p>
 * <p>Copyright:	Copyright (c) 2006 </p>
 * <p>Company:		Clifford </p>
 * @author:			xf.chen
 * @version:		1.00
 * @date:			2012-11-01

	日期		修改人		说明
	===========================================
	2013-05-07	dx.yan		修复多处bug
======================================================= */
/************************************
 * 全局样式 
 ************************************/
html,body{width:100%;}
body{
	font:12px \5b8b\4f53,Arial,sans-serif;
	margin:30px 0px 0px 0px;
	background-color:#F7F6F6;
}
table{width:100%; border-collapse:collapse;}
form{margin:0; padding:0;}
input,textarea,select,option{
	font:12px \5b8b\4f53,Arial,sans-serif;
}
input[type="radio"],input[type="checkbox"] {vertical-align:middle; margin-top:-2px; margin-bottom:1px;}



/*qi修改*/
td{
	padding: 8px 3px 7px 3px;
}
td#comTabTL,td#comTabTM,td#comTabTR{padding:1px 3px 0px 3px;}
/*标题行*/
th {
	/*8.5修改qi*/
	border-left: 1px solid #cecece; 
	border-right: 1px solid #cecece; 
	border-top: 1px solid #cecece;
	border-bottom:1px solid #cecece;
	letter-spacing: 2px;
	text-align:center;
	padding: 8px 3px 7px 3px;
	/*background-color:#E0E0E0;*/
	background-color: #CFECFA;
	font-weight:100;
	color:#000;
	/*background: url("../../images/default/background/tableTopBg.jpg") repeat-x center bottom;*/
}
label{
	cursor:pointer;
}
.comFL{float:left;}
.comFR{float:right}
.comCursor{cursor:pointer;}
/************************************************************************************************************
 ************************************************************************************************************
 **************************************************    公用样式   *******************************************
 ************************************************************************************************************
  ************************************************************************************************************/
/* *号样式 */
.red{
	color:#F00;
	padding-right:5px;
}

.colorMale {color: #162C71;} /* 男士姓名文字颜色 */
.colorFemale {color: #D57305;} /* 女士姓名文字颜色 */
.colorNoSex {color: #000000;} /* 未知性别的名字文字颜色 */

/* 按钮、图片等禁用的样式 ie 不支持对背景图片直接使用滤镜 */
.comDisabled {
    color: gray;
    cursor: default;
    opacity: .3;
    -moz-opacity: .3;
    filter: alpha(opacity=30);
}

/* 图片按钮样式 */
.comImgBtn {
    cursor: pointer;
}

/*数据统计里表格*/
table.report-table td,table.report-table th{
	border:1px solid #cecece;
} 
/************************************************************************************************************
 ************************************************************************************************************
 **************************************************  列表界面样式（普通） *****************************************
 ************************************************************************************************************
  ************************************************************************************************************/
 
/*背景 overflow:hidden;属性会导致输入框输入时异常 */
#comBgMinDiv {
	position: fixed;
	top:0px;
	min-width:100%;
	width: 100%;
	height:30px;
	z-index: 999;
	-moz-opacity:0.9;
	/*filter:alpha(opacity=90);*/
	opacity: 0.9;
	/*
	background:url(../../images/default/background/pageTopBg.gif) repeat-x left top;
	*/
	background:#fff;
	border-bottom:1px solid #D3D3D3;
	_position:absolute;
	_bottom:auto;
	_top:expression(eval(document.documentElement.scrollTop));
}

/*用于存放更多的查询内容*/
#comMoreDiv, #comMoreMinDiv {
	display:block;
	padding:5px;
	background-color:#eeeeee;
	border:solid 1px solid #cecece;
	border-bottom:none;
}

/*修改#comMoreDiv*/
#comMoreDiv{
	border: 1px solid #cecece;
	margin-bottom: 10px;
	padding:10px;
	/*事件管理上方div qi修改*/
	background-color: #F4F4F4;
}

/*标题栏-标题*/
#comBarTitle{
	color:#333333;
	font-size:12px;
	padding-left:23px;
	padding-top:8px;
	padding-right:50px;
	letter-spacing: 1px;
	/*text-shadow:#ffffff 1px 1px 1px;
	filter: Shadow(Color=white, Direction=135, Strength=1);*/
	float:left;
	min-width:50px !important;
	background:url(../../images/default/ico/titleIco.gif) no-repeat 5px 8px;
	font-weight: bold;
}
.comBarTitle_eN{float:left;display: inline-block;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}


/*标题栏-文字*/
.comBarTxtDiv{
	float:left;
	color:#000000;
	padding:5px 1px;
}

/*标题栏-靠右对齐**/
.comBarTxtDivRight{
	float:right;
	color:#000000;
	padding:5px 10px 5px 1px;
}
.comBarTxtDiv img, .comBarTxtDivRight img, #comMoreDiv img, #comMoreMinDiv img {margin-bottom:-4px;}

/*标题栏-分隔线*/
.comBarSplit{
	padding:0 6px;
	margin:0;
	width:2px;
	height:100%;
	background:url(../../images/default/background/pageTopBgSplit.png) no-repeat 4px;
}

/*标题栏-分隔线，旧版，不建议使用*/
.comBarSplitDiv{
	float:left;
	padding-left:6px;
	padding-right:6px;
	width:2px;
	height:100%;
	background:url(../../images/default/background/pageTopBgSplit.png) no-repeat 6px 8px;
}
/*标题栏-大控件(输入框、按钮、下拉列表)，旧版，不建议使用*/
.comBarControlDiv{
	float:left;
	color:#000000;
	padding-left:1px;
	padding-right:1px;
	padding-top:5px;
}

/*排列竖列表*/
.hiddenInput{
	list-style-type:none;
	margin:0px;
	padding:0px;
	padding-top:2px;
}
.show .showInput{
	background:url(../../images/default/ico/unfold.png) no-repeat right;
	background-color: #40BBEA;
	padding-right: 1.1em;
}
.show .showInputHover{
	background:url(../../images/default/ico/fold.png) no-repeat right;
	background-color: #026EA1;
	padding-right: 1.1em!important;
}
.hiddenInput input{	
	width:100%;
	padding:0 0.8em;
	text-align:left;
}
.hiddenInput input:hover{
	padding:0 0.8em;
}

/*标题栏-小控件(checkbox、radio)，旧版，不建议使用*/
.comBarControlDiv_s{
	float:left;
	color:#000000;
	padding-left:1px;
	padding-right:1px;
	padding-top:7px;
}


/*操作提醒栏*/
#comInfoPrompt{
	position: fixed;
	top:-1000px;
	right:20px;
	width:200px;
	padding-top:20px;
	padding-bottom:20px;
	text-align:center;
	min-height:20px !important;
	z-index: 1000;	
	-moz-opacity:0.9; 
	filter:alpha(opacity=90);
	opacity: 0.9;
	background-color:yellow;
	border-bottom:#000000 solid 1px;
	border-left:#000000 solid 1px;
	border-right:#000000 solid 1px;
}
/*数据处理中图标*/
#comWaitDivImg{
	position:absolute;
	left:50%;
	top:50%;
	width:50px;
	height:50px;
  	margin-left:-25px;
  	margin-top:-25px;
	border:#000 solid 1px;
	z-index:999999;
	background: url(../../images/default/background/loading.gif) no-repeat center 10px;
	filter:alpha(opacity=80); 		/*IE*/ 
	-moz-opacity:0.8;				/* Moz + FF */ 
	opacity: 0.8;					/*FireFox*/
	background-color:#FFFFFF;
}
/*数据处理中背景*/
#comWaitDivBg{
	position:absolute;
	width:100%;
	z-index:999998;
	background-color:#000000;
	filter:alpha(opacity=60); 		/*IE*/ 
	-moz-opacity:0.6;				/* Moz + FF */ 
	opacity: 0.6;					/*FireFox*/
	top:0;
	bottom:0;
	height:auto!important;
	overflow:auto!important;
	overflow:visible;
}
/********************/
#comMenuFrameDiv, #comMenuFrameMinDiv{
	position: absolute; /* 改属性导致标题的半透明效果无效，但去掉后页面高度无法占慢屏幕 */
	min-width: 100%;
	width: 100%;
	z-index: 2;
	top:30px;
	bottom:0;
	height:auto!important;
	overflow:auto!important;
	overflow:visible;
}

/*菜单外框*/
#comMenuFrame{
	width: 100%;
	height: 100%;
	padding:5px;
	padding-left:0px;
}

/*菜单列表*/
#comMenuTab{
	width:100%;
	height:100%;
	border:0px;
	padding:0px;
	margin:0px;
}
/*菜单列表顶部TD*/
#comTabTL{
	width:7px;
	height:8px;
	
}
#comTabTM{
	
}
#comTabTR{
	width:7px;
	height:8px;
}
/*菜单列表中部TD*/
#comTabML{
	width:7px;
	
}
#comTabMR{
	width:7px;	
	
}
#comTabMM{
	vertical-align:top;
	/*background-color:#FFF; 8.5修改qi*/
	padding-bottom:20px;/*如果更变些值，需将对应的皮肤.js中的变量comTabMM_Padding_Bottom的值也相应调整，两个值一样*/
}
/*菜单列表底部TD*/
#comTabBL{
	width:7px;
	height:8px;
	
}
#comTabBM{
		
}
#comTabBR{
	width:7px;
	height:8px;
	
}

/********************************************* 分页按钮 *****************************************************/
/*列表*/
.comTab_Table {   
	/*
	border-left: 1px solid #cecece;  
	border:1px solid #cecece; */
	width:100%;   
	padding: 0;   
	margin: 0;
	line-height:120%;
}

/*内容行_奇数行_第1、3、5、7、...行*/
.comTab_R1{
	background-color:#FFF;
}
/*内容行_偶数行_第2、4、6、8、...行*/
.comTab_R0, .comTab_R2{
	background-color:#F9F9F9;
}
/*内容行_鼠标移上效果*/
.comTab_Table tr:hover{
	background-color:#DFE9FD;
}
/*内容行_鼠标移上TD的效果*/
.comTab_Table td:hover{
	background-color:#B3CDFF;
}

.comTab_Table .eventname:hover{
	color: #0000ff;
}

.comTab_Table td{
	border: 1px solid #cecece;
	/*padding: 4px 3px 2px 3px;  qi修改*/
	padding: 8px 3px 7px 3px;
	word-wrap:break-word;
	word-break:break-all;
}
.comTab_Td{   
	/*8.5修改qi*/
	/*border: 1px solid #cecece;*/
	border-top: none;
	/*padding: 4px 3px 2px 3px;  qi修改*/ 
}

.comTab_Button{
	margin-top:10px;
	letter-spacing:1px;
	height:20px;
	float:left;
	width:100%;
}
.comTab_Button_L{
	float:left;
	padding:4px 5px 0px 5px;
}
.comTab_Button_R{
	float:right;
	padding:4px 5px 0px 5px;
}
/*分页中控件所在的DIV*/
.comTab_Button_Control{
	padding:0px 5px 0px 5px;
}
/*页码输入框*/
.comTab_Button_Input{
	width:30px;
	border:0px;
	height:10px;
	border-bottom:#333 solid 1px;
}
/*GO按钮*/
.comTab_Button_Go{
	width:35px;
	height:20px;
	padding-bottom:8px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:10px;
}
.comTab_Sn{
	margin-top:6px;
	float:left;
	width:100%;
}
.comTab_SnTxt{
	margin-left:30px;
}
.comTab_SnTxt li, .comTab_SnLi{
	list-style-type:circle;
	padding: 2px 0 2px 0;
}


/************************************************************************************************************
 ************************************************************************************************************
 **************************************************  列表界面样式（标题冻结） *****************************************
 ************************************************************************************************************
  ************************************************************************************************************/
/*标头Div标式*/  
.comTab_Fz_Tit{
	z-index:3;
	height:28px;
	background-color:#FFF;
	margin-left:0px;
	border-bottom:#999 solid 1px;
	position:absolute;
}
/*标头Td标式*/  
.comTab_Fz_TitTd{
	float:left;
	height:100%;
	border-left: 1px solid #cecece; 
	border-bottom: 1px solid #cecece;   
	border-top: 1px solid #cecece; 
	letter-spacing: 2px;
	text-align:center;
	padding: 8px 3px 0px 3px;
	background-color:#00648E;
	height:21px;
	font-weight:100;
	color:#FFF;
}
/*标头对应的Table的标式*/  
.comTab_Fz_Table{
	float:left; 
	width:100%; 
	overflow:auto; 
	border-bottom:1px solid #cecece;
}
/************************************************************************************************************
 ************************************************************************************************************
 **************************************************  编辑界面样式 *******************************************
 ************************************************************************************************************
 ************************************************************************************************************/
.comEdit_Table{
	border-top: 1px solid #cecece;
	border-left: 1px solid #cecece; 
	width:100%;   
	padding:0;   
	margin:0; 
	/*修改密码背景色 qi修改*/
	background-color: #ffffff;
}
/*大标题*/
.comEdit_tdTitBig{
	border-bottom: 1px solid #cecece;
	border-right: 1px solid #cecece;
	padding:5px;
	padding-left:10px;
	white-space:nowrap;
	/*FILTER: blur(strength=2,direction=150);*/
	text-shadow:#ffffff 1px 1px 1px;
	background-color: #E9E9E9;
	/*******将原来修改密码表格最上面行的渐变背景改掉 qi修改
	background-image: url(../../images/default/ico/edit-table-th.gif);
	background-repeat: repeat;
	background-position: left top;
	*******/
	font-size: 13px;
	font-weight: bold;
}
/*大标题内容*/
.comEdit_tdTitCon{
	padding:5px;
	padding-left:30px;
	white-space:nowrap;
	FILTER: blur(strength=0,direction=150);
	text-shadow:#5A8DD3 0px 0px 0px;
}
/*标题列*/
.comEdit_tdTit{
	border-right: 1px solid #cecece;
	border-bottom: 1px solid #cecece;
	padding:8px 5px 6px 5px;
	width:110px;
	text-align:right;
	white-space:nowrap;
}
/*内容列*/
.comEdit_tdCon{
	border-right: 1px solid #cecece;
	border-bottom: 1px solid #cecece; 
	padding:3px 7px 3px 5px;	
}
/*提交按钮栏*/
.comEdit_buttonTd{
	border: 1px solid #cecece;
	/*******将原来修改密码表格最下面行的顶部border去掉 qi修改*/
	border-top: none;
	background-color:#F7F3F7;
	text-align:center;
	/*******将原来修改密码表格最下面行的拉高  原来3px qi修改*/
	padding:5px;
}
/*设置输入框、select等控件显示的位置*/
.comEdit_conDivL{
	margin-right:50px;
}
/*设置查询按钮图标显示的位置*/
.comEdit_conDivR{
	float:right;
	width:45px;
	text-align:left;
}
/*Input输入框(可操作)*/
.comEdit_Input{
	border:none;
	border-bottom:#87A3BD solid 1px;
	width:100%;
}
/*Input输入框(只读)*/
.comEdit_InputRead{
	border:none;
	border-bottom:#87A3BD solid 1px;
	width:100%;
	background-color:#EEEEEE;
}
/*TextArea*/
.comEdit_TextArea{
	width:100%;
	border:#87A3BD solid 1px;	
}
/*TextArea（只读)*/
.comEdit_TextAreaRead{	
	width:100%;
	border:#87A3BD solid 1px;	
	background-color:#EEEEEE;
}
/*附件列表*/
.comEdit_attachment{
	line-height:16px;
	padding-top:5px;
}
/*** 文本输入框当前剩余字符数显示 ***/
.comEdit_TxtArea_numShow{
	font-weight:bold;
	font-family:Candara,Corbel;
	font-size:20px;
}
/**********************************自动伸缩*****************************/
/*自动伸缩框所在TD样式*/
.comEdit_StretchAreaTd{
	border-right: 1px solid #cecece;
	border-bottom: 1px solid #cecece; 
	padding:5px;
	padding-right:10px;
  	vertical-align:top; 
	height:22px;
}
/*自动伸缩框所在DIV样式*/
.comEdit_StretchAreaDivOut{
	position:relative;
	width:100%
}
.comEdit_StretchAreaDivInner{
	position: absolute; 
	width:100%
}
/*自动伸缩框自已的样式*/
.comEdit_StretchArea{
	height:18px; 
	width:100%; 
	-moz-opacity:0.9; 
	filter:alpha(opacity=90);
	opacity: 0.9;
}
/*自动伸缩框字数显示层的样式*/
.comEdit_StretchAreaDiv{
	position: absolute; 
	z-index:9999;
	width:42px;
	height:29px;
	padding:8px 0px 0px 0px;
	font-size:16px;
	text-align:center;
	color:#FFF;
	background:url(../../images/default/background/textAreaFontNumBg.png) no-repeat left top;
}
/*弹出层（在当前页面，通过DIV的方式，打开弹出框口）*/
.comPopWin{
	position:absolute;
	left:50%;
	top:50%;
	margin-left:-120px;
	margin-top:-150px;
	z-index:999;
	width:240px;
	height:300px;
	background:#FFF;
	box-shadow:1px 2px 3px #999999;
	border:1px solid #5B5B5C;
	border-radius:4px;
	padding:10px;
}
/************************************************************************************************************
 ************************************************************************************************************
 **************************************************  空白提醒页样式 *******************************************
 ************************************************************************************************************
 ************************************************************************************************************/
/*空白提醒页面_笑脸*/
#comEmptyPage_Face{
	position:absolute;
	z-index:999;
	width:100px;
	height:60px;
	background:url(../../images/default/background/emptyPageFace.gif) no-repeat right top;
}
/*空白提醒页面_文字*/
#comEmptyPage_Txt{
	position:absolute;
	z-index:998;
	font-size:14px;
	background-color:#DFE0E1; 
	padding:5px;
	border:#99999A solid 1px;
}
/*空白提醒页面_LOGO*/
#comEmptyPage_Logo{
	position:absolute;
	z-index:997;
	width:130px;
	height:117px;
	background:url(../../images/default/background/emptyPageLogo.png) no-repeat right top;
}
/*空白提醒页面_LOGO背景*/
#comEmptyPage_LogoBg{
	position:absolute;
	z-index:996;
	width:420px;
	height:312px;
	background:url(../../images/default/background/emptyPageLogoBg.gif) no-repeat right top;
}
/************************************************************************************************************
 ************************************************************************************************************
 **************************************************  标签栏 *******************************************
 ************************************************************************************************************
 ************************************************************************************************************/
/* Tab 选项卡 */
.comTab {
	background-color:#FFF;
    background-image: url(../../images/default/background/tabLine.jpg);
    background-repeat: repeat-x;
    background-position: left bottom;
    height: 35px;
    /*margin: 2px 5px;  8.5修改qi*/*/
    min-width:760px;
}
.comTab a { color:#666666; text-decoration:none;}
.comTab a:hover { color:#1E90FF;}
.comTab h1 {
    font-size: 14px;
    font-weight: bold;
    color: #33ABDF;
    line-height: 35px;
    background-image: url(../../images/default/background/tabTitleDot.jpg);
    background-repeat: no-repeat;
    background-position: left center;
    text-indent: 20px;
    float: left;
    margin:0 10px 0 0; 
    padding:0;
}
.comTab ul {
    height: 35px;
    display: block;
    list-style:none;
    margin:0; 
    padding:0;
}
.comTab li {
    display: block;
    float: left;
    height: 35px;
    padding: 0 10px;
    line-height: 35px;
    list-style:none;
    margin:0; 
}
.comTab .comTabCurrent {
    background-image: url(../../images/default/background/tabArrow.jpg);
    background-repeat: no-repeat;
    background-position: center bottom;
    font-weight: bold;
    color: #000000;
}

/*标签栏外框（旧样式，不允许使用）*/
#comTabDiv{
	left:7px;
	z-index:3;
	height:28px;
	background-color:#FFF;
	margin-left:0px;
	border-bottom:#999 solid 1px;
	opacity: 0.8;					/*FireFox*/
	filter:alpha(opacity=80); 		/*IE*/ 
	-moz-opacity:0.8;				/* Moz + FF */ 	
}
/*标签栏激活栏（旧样式，不允许使用）*/
.comTabSel{
	float:left;
	border-top:#F60 2px solid;
	padding-left:10px;
	padding-right:10px;
	background-color:#FC0;
	text-align:center;
	padding-top:8px;
	height:18px;
	color:#000;
}
/*标签栏未激活栏（旧样式，不允许使用）*/
.comTabOth{
	color:#000;
	float:left;
	height:16px;
	margin-top:5px;
	padding-left:10px;
	padding-right:10px;
	padding-top:5px;
	background-color:#FEE47D;
	margin-left:2px;
}


/************************************************************************************************************
 ************************************************************************************************************
 **************************************************  目录树样式 *******************************************
 ************************************************************************************************************
 ************************************************************************************************************/
/*关闭状态下侧工具条*/
.comTree_DivClose{position:absolute;z-index:801;width:3px;height:100%;}
/*关闭状态下按钮显示*/
.comTree_DivCloseIco{border-right:#666 solid 1px;border-bottom:#666 solid 1px;position:absolute;z-index:802;width:10px;padding-left:2px;padding-top:6px;height:20px;background-color:#FCFC8B;-moz-opacity:0.9;filter:alpha(opacity=90);opacity: 0.9;}
/********************打开状态下********************/
.comTree_DivOpen{border-right:#666 solid 1px;position:absolute;z-index:901;height:100%;background-color:#FFF;-moz-opacity:0.9;filter:alpha(opacity=90);opacity: 0.9;}
/*关闭层，用于当目录树为液态展开时，点击该层以光闭目录树*/
.comTree_DivCloseClick{position:absolute;z-index:101;width:100%;height:100%;}
/*标题*/
.comTree_DivTitle{width:100%;height:30px;z-index:999;background:url(../../images/default/background/pageTopBg.gif) repeat-x left top;}
.comTree_Sn{color:#000;font-size:14px;padding-left:3px;padding-top:8px;text-shadow:#CCC 1px 1px 1px;min-width:80px !important;}
.comTree_Ico{float:right;padding-top:8px;padding-right:5px;}
/*内容*/
.comTree_Inner{/*background-color:#E4E4E4;*/padding:3px;}

/************************************************************************************************************
 ************************************************************************************************************
 **************************************************  操作指引层 *******************************************
 ************************************************************************************************************
 ************************************************************************************************************/

/*操作指引提醒_外边框*/
.com_OptTit_Box { 
	left:100px;
   	position:absolute;
	z-index:998;
	width:240px;
    line-height:22px;
    background:#FFF;
    box-shadow:1px 2px 3px #999999;
    border:1px solid #5B5B5C;
    border-radius:4px;
    text-align:left;
	padding:5px;
    color:#0C7823;
}
.com_OptTit_BoxBg{ 
	position:absolute;
	z-index:997;
	width:100%;
	height:100%;
	background-color:#000;
	-moz-opacity:0.2; 
	filter:alpha(opacity=20);
	opacity: 0.2;
}
/*操作指引提醒_文字显示*/
.com_OptTit_Txt{padding-left:5px;}
/*操作指引提醒_关闭按钮*/
.com_OptTit_Close{float:right;margin-left:0px;cursor:pointer; color:#F00;}
.com_OptTit_Go{float:right;margin-right:10px;cursor:pointer; color:#06F;}
/*操作指引提醒_三角形*/
.com_OptTit_Arr{position:absolute;overflow:hidden;width:0;height:0;border-width:10px;}

/*操作指引提醒_三角形*/
.com_OptTit_T {left:15px;/*========================多变*/border-style:dashed dashed solid dashed;}
/*操作指引提醒_三角形(背景)*/
.com_OptTit_T_Bg{top:-20px;border-color:transparent transparent #000 transparent;}
/*操作指引提醒_三角形(线条)*/
.com_OptTit_T_Line{top:-19px;border-color:transparent transparent #FFF transparent;}

/*操作指引提醒_三角形*/
.com_OptTit_L {top:10px;/*========================多变*/border-style:dashed solid dashed dashed;}
/*操作指引提醒_三角形(背景)*/
.com_OptTit_L_Bg{left:-20px;border-color:transparent #000 transparent transparent;}
/*操作指引提醒_三角形(线条)*/
.com_OptTit_L_Line{left:-19px;border-color:transparent #FFF transparent transparent;}

/*操作指引提醒_三角形*/
.com_OptTit_R {top:10px;/*========================多变*/border-style:dashed dashed dashed solid;}
/*操作指引提醒_三角形(背景)*/
.com_OptTit_R_Bg{right:-20px;border-color:transparent transparent transparent #000;}
/*操作指引提醒_三角形(线条)*/
.com_OptTit_R_Line{right:-19px;border-color:transparent transparent transparent #FFF;}

/*操作指引提醒_三角形*/
.com_OptTit_B {left:15px;/*========================多变*/border-style:solid dashed dashed dashed;}
/*操作指引提醒_三角形(背景)*/
.com_OptTit_B_Bg{bottom:-20px;border-color:#000 transparent transparent transparent;}
/*操作指引提醒_三角形(线条)*/
.com_OptTit_B_Line{bottom:-19px;border-color:#FFF transparent transparent transparent;}

/*操作指引提醒_三角形*/
.com_OptTit_RB {right:15px;/*========================多变*/border-style:solid dashed dashed dashed;}
/*操作指引提醒_三角形(背景)*/
.com_OptTit_RB_Bg{bottom:-20px;border-color:#000 transparent transparent transparent;}
/*操作指引提醒_三角形(线条)*/
.com_OptTit_RB_Line{bottom:-19px;border-color:#FFF transparent transparent transparent;}

/*操作指引提醒_三角形*/
.com_OptTit_RT {right:15px;/*========================多变*/border-style:dashed dashed solid dashed;}
/*操作指引提醒_三角形(背景)*/
.com_OptTit_RT_Bg{top:-20px;border-color:transparent transparent #000 transparent;}
/*操作指引提醒_三角形(线条)*/
.com_OptTit_RT_Line{top:-19px;border-color:transparent transparent #FFF transparent;}
/*结合jquery闪动字体,可见common.js-shake*/
.com_shake_box{ width:50px; height:12px; line-height:12px; background:#f0f0f0; border:1px solid #ddd; text-align:center; font-size:10px; position:relative; z-index:0; background-color:#ccc; color:#000; text-decoration:none;margin-top: 3px;}
.com_shake_default_box{width:50px; height:12px; line-height:12px;margin-top: 3px;}
.com_shake_red{ border:1px solid #d00; background:#ffe9e8; color:#d00;margin-top: 3px;}


/* 分页样式 begin */
.comPagination {
	display: block;
	position:relative;
	overflow:hidden;
	background-color:#E8E8E8;
	border:solid 1px #C9C9C9;
	border-top: none;
}
.comPaginationLeft {
    float:left;
    padding-left:10px;
    line-height:28px;
    overflow:hidden;
	color:#000000;
}
.comPaginationRight {
    float:right;
    line-height:28px;
    padding-right:5px;
	color:#000000;
}

.comPagination a {
    text-decoration:none;
    line-height:16px;
		padding:2px 6px 3px 6px;
}
.comPaginationFirst {
    background:url(../../images/default/ico/first.gif) no-repeat;
}
.comPaginationFirstDisabled {
    cursor: default;
    background:url(../../images/default/ico/first_gray.gif) no-repeat;
}
.comPaginationPrev {
    background:url(../../images/default/ico/prev.gif) no-repeat;
}
.comPaginationPrevDisabled {
    cursor: default;
    background:url(../../images/default/ico/prev_gray.gif) no-repeat;
}
.comPaginationNext {
    background:url(../../images/default/ico/next.gif) no-repeat;
}
.comPaginationNextDisabled {
    cursor: default;
    background:url(../../images/default/ico/next_gray.gif) no-repeat;
}
.comPaginationLast {
    background:url(../../images/default/ico/last.gif) no-repeat;
}
.comPaginationLastDisabled {
    cursor: default;
    background:url(../../images/default/ico/last_gray.gif) no-repeat;
}
.comPaginationSeparator {
  height: 16px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 3px 3px;
}
.comPaginationNum {
	border:1px solid #ccc;
	margin:0 2px;
	width:30px;
	text-align:center;	
	outline:none;
	+vertical-align:middle;
	position:relative;
	_left:0;_top:-1px;
	height:14px;
}
/* 分页样式 end */
