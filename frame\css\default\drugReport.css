body,h1,h2,h3,h4,h5,h6,input,textarea,p,ul,ol,li,form,img,dl,dt,dd,table,tr,td,th,blockquote,fieldset,div,strong,label,em,a,span,select{
   margin:0px;
   border:0px;
   border:none;
   padding:0px;
   list-style-type:none;
   text-decoration: none;
    font-weight: normal;
	/* color: #000; */
	outline: none;
    font-family: "΢���ź�";
   }
/* body{text-align:center;background-color:#ffffff;} */
.clear{clear:both;}
.float_L{float: left;}
.float_R{float: right;}

.drugTabpage{width: 98%;margin:0px auto;}
.title{text-align: center;}
.title h1{font-size: 25px;font-weight: bold;line-height: 50px;}
.subDes label{font-weight: bold;margin-right: 10px;vertical-align: middle;}
.subDes label input[type="checkBox"]{margin-left: 5px;vertical-align: middle;}
.subDes label input[type="text"]{margin-left:5px;min-width:200px;border-bottom: 1px solid #222;}
#comBarTitle span{font-weight: bold;color: #333333;}

.mainTable{border-collapse:collapse;margin:20px auto;}
.mainTable thead td{border:none;width:50% !important;}
.mainTable tfoot td{border:none;}
.mainTable td{border:1px solid #000;padding:10px 5px;text-align: left;}
.mainTable td textarea{width: 100%;overflow:auto;resize : none;color: #848484;padding:5px;box-sizing: border-box;}
.mainTable td.leftText{border-right: none;}
.mainTable td.rightText{border-left: none;}
.mainTable tr.centerTr td{text-align: center;}

.mainTable td.wrapTd{padding:0px;}
.innerTable{width:100%;border-collapse: collapse;border:none;margin: 0px;}
.innerTable td{border:none;}
.innerTable td.inTabCol1{border-right: 1px solid #000;width: 60px;}
.innerTable td.inTabRowLast{border-bottom: 1px solid #000;}


/**checkBox**/
.checkWrap{display: inline-block;margin-right: 10px;vertical-align: middle;}
.checkWrap input[type="checkBox"]{margin-left: 5px;vertical-align: middle;}

.label-45{display:inline-block;width: 45px;text-align: right;}
.inputWrap-45{float: right;margin-left: -50px;width: 100%;}
.input-45{width: 100%;padding-left: 50px;box-sizing: border-box;}

.label-65{display:inline-block;width: 65px;text-align: right;}
.inputWrap-65{float: right;margin-left: -70px;width: 100%;}
.input-65{width: 100%;padding-left: 70px;box-sizing: border-box;}

.label-100{display:inline-block;width: 100px;text-align: right;}
.inputWrap-100{float: right;margin-left: -105px;width: 100%;}
.input-100{width: 100%;padding-left: 105px;box-sizing: border-box;}

.label-130{display:inline-block;width: 130px;text-align: right;}
.inputWrap-130{float: right;margin-left: -135px;width: 100%;}
.input-130{width: 100%;padding-left: 135px;box-sizing: border-box;}

.label-150{display:inline-block;width: 150px;text-align: right;}
.inputWrap-150{float: right;margin-left: -155px;width: 100%;}
.input-150{width: 100%;padding-left: 155px;box-sizing: border-box;}

.btn-primary{color:rgba(255,255,255,.75)}

.btn{
	color: #fff;
	font-size:13px;
	cursor:pointer;
	background-color: #40BBEA;
	border: 0px;
	width:45px;
	height:20px;
}
.FloatR{
	float: right;
}
.marginR6{
	margin-right:0px;
}


/**16.11.28 ���**/
.label-col{display:block;float:left;}
.label-col-10{width:10%;}
.label-col-20{width:20%;}
.label-col-30{width:30%;}
.label-col-40{width:40%;}
.label-col-50{width:50%;}
.label-col-60{width:60%;}
.label-alignR{text-align:right;}
.label-borderR{border-right:1px solid #000;}
.inputWidth-200{width:200px;}
.inputWidth-100{width:100px;}

.innerTable2{width:100%;border-collapse: collapse;border:none;margin: 0px;}
.innerTable2 td{border:1px solid  #000;}
.innerTable2 td.noneTop{border-top:none;}
.innerTable2 td.noneBottom{border-bottom:none;}
.innerTable2 td.noneLeft{border-left:none;}
.innerTable2 td.noneRight{border-right:none;}

.titletd{
	background-color:gray;
	font-size:16px;
	font-weight:bold;
}
.footcss{
	text-align:right;
	font-size:14px;
	font-weight:bold;
}
.comTable {border:none;}
.comTable td{border:none;}
