@charset "utf-8";

.assigndetailBg{background:#EBEBEB!important;}
.comTab_Table{margin:0 auto;}

.out_div{
	float: left;
	height:auto;
	width: 50%;
	margin-top: 5px;
    margin-bottom:5px;
	
}
.out_div  p{
	line-height:25px;
}
.blockUI h4{float:none;}
#settingdiv,#settingdiv1{
	border:solid 1px #D3D3D3;
	border-radius:5px;
	margin: 25px 8px;
    margin-bottom: 45px;
	height:auto;
	width: 94%;
	background:#ffffff;
	padding-bottom: 28px;
}
#settingdiv1{width:98%;}
#settingdiv p,#settingdiv1 p{
	margin-left: 30px;
	text-align: left;
    font-size: 14px;
    margin-right: 15px;
    color: #666;
    line-height: 25px;
	margin-top:0px;
	margin-bottom:0px;
}
#settingdiv .title,#settingdiv1 .title{
	/*
	display:block;
	width:120px;
	position:relative;
	top:-15px;
	text-align: center;
	*/
	font-weight: bold;
	font-size: 16px;
	color: #2FA4E7;
	position:relative;
	top:-28px;
	left: 10px;
	padding-bottom:20px;
}
#settingdiv .content,#settingdiv1 .content{
	text-align: left;
	font-size: 14px;
	margin-right: 15px;
	color:#555;
	line-height:25px;
}

.content1{
	display: inline-block;
	min-width: 200px;
}
.content2{
	display: inline-block;
	min-width: 200px;
}

#other{
	border:solid 1px #D3D3D3;
	border-radius:5px;
	margin:25px 8px;
	height:auto;
	width: 98%;
	background:#ffffff;
	padding-bottom: 30px;
    padding-top: 10px;
}
#other p{
	/*margin-left: 30px;*/
	margin-top:0px;
	margin-bottom:0px;
}
#other .title{
	/*
	display:block;
	width:120px;
	position:relative;
	top:-15px;
	text-align: center;
	*/
	font-weight: bold;
	font-size: 16px;
	color: #2FA4E7;
	position:relative;
	top:-33px;
	left: 10px;
}
#other .content{
	text-align: left;
	font-size: 14px;
	margin-right: 15px;
}
.comPopWinBg{
 position:absolute;
 width:100%;
 height:100%;
 z-index:998;
 background-color:#000000;
 filter:alpha(opacity=60); /*IE*/
    -moz-opacity:0.6;	 /* Moz + FF */
    opacity: 0.6;	 /*FireFox*/
 top:0;
 bottom:0;
 overflow:auto!important;
 overflow:visible;

}

#dd{
  font-size: 14px;
  line-height: 20px;
  color: #555;
  /*margin-bottom:30px;*/
  margin-bottom: 10px;
  margin-top: 10px;
  background:#EBEBEB;
}

#bar{
	height:2px;
	background-color:#f7f7f7;
	background-repeat: repeat-x;
	overflow: hidden;
	box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
	margin-bottom:10px;
	margin-left:7%;
	margin-right:7%;
}

h1, h2, h3, h4, h5, h6 {
  margin: 10px 0;
  font-family: 'Microsoft Yahei',sans-serif;
  font-weight: bold;
  line-height: 20px;
  color: #317eac;
  text-rendering: optimizelegibility;
  float:left;
}

.expand { 
	height:2px; 
	margin:2px 0; 
	background:#53C1EB; 
	position:absolute;
	background-image: linear-gradient(to bottom,#53C1EB,#036EA2);
	box-shadow:0px -1px 0px  rgba(0,0,0,0.25);
}
.expand img{
	float:right;
	margin-top: -5px;
    margin-right: -2px;
}

.commit0       { width:9.5%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }
.commit1       { width:42.5%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }
.commit2       { width:75.5%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }
.commit3       { width:42%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }
.commit4       { width:62.4%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }
.commit5       { width:82.5%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;		}
.commit6       { width:0%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;		}

.commitRoll0       { width:33%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }
.commitRoll1       { width:58%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }
.commitRoll2       { width:86%;  -moz-animation:html5 2s ease-out;       -webkit-animation:html5 2s ease-out;       }


.num_class1{
	width: 40px;
	height: 20px;
	text-align:center;
	border-top-left-radius: 10%;
	border-top-right-radius: 10%;
	border-bottom-right-radius: 10%;
	border-bottom-left-radius: 10%;
	background-color: rgba(231, 29, 39, 1);
	display: inline-block;
	line-height: 20px;
	margin-top: -15px;
	color: rgb(255, 255, 255);
	border-color: rgb(138, 193, 226);
}
.num_class2{
	width: 40px;
	height: 20px;
	text-align:center;
	border-top-left-radius: 10%;
	border-top-right-radius: 10%;
	border-bottom-right-radius: 10%;
	border-bottom-left-radius: 10%;
	background-color: rgba(231, 86, 29, 1);
	display: inline-block;
	line-height: 20px;
	margin-top: -15px;
	color: rgb(255, 255, 255);
	border-color: rgb(138, 193, 226);
}
.num_class3{
	width: 40px;
	height: 20px;
	text-align:center;
	border-top-left-radius: 10%;
	border-top-right-radius: 10%;
	border-bottom-right-radius: 10%;
	border-bottom-left-radius: 10%;
	background-color: rgba(102, 102, 102, 1);
	display: inline-block;
	line-height: 20px;
	margin-top: -15px;
	color: rgb(255, 255, 255);
	border-color: rgb(138, 193, 226);
}
.num_class4{
	width: 40px;
	height: 20px;
	text-align:center;
	border-top-left-radius: 10%;
	border-top-right-radius: 10%;
	border-bottom-right-radius: 10%;
	border-bottom-left-radius: 10%;
	background-color: rgba(209, 209, 209, 1);
	display: inline-block;
	line-height: 20px;
	margin-top: -15px;
	color: rgb(255, 255, 255);
	border-color: rgb(138, 193, 226);
}

.es_c_0{
	color: blue; 
	font-weight: bold;
}
.es_c_1{
	font-weight: bold;
}
.es_c_2{
	color: green; 
	font-weight: bold;
}
.es_c_3{
	color: green; 
	font-weight: bold;
}	
.es_c_5{
	color: green; 
	font-weight: bold;
}
.es_c_4{
	color: red; 
	font-weight: bold;
}
.es_c_6{
	color: #DD5600; 
	font-weight: bold;
}
   #showBigDivImg{
   position:absolute;
   /*
   left:50%;
   top:50%;
   width:50px;
   height:50px;
   */
   max-width:1100px;
   margin-left:-10px;
   margin-top:-25px;
   border:#000 solid 1px;
   z-index:999999;
   background-color:#FFFFFF;
  }
  /*��ݴ����б���*/
  #showBigDivBg{
  display:none;
   position:absolute;
   width:100%;
   z-index:999998;
   background-color:#000000;
   filter:alpha(opacity=60); /*IE*/
   -moz-opacity:0.6;	 /* Moz + FF */
   opacity: 0.6;	 /*FireFox*/
   top:0;
   bottom:0;
   height:auto!important;
   overflow:auto!important;
   overflow:visible;
  }
  #showBigDiv{
   display:none;
  }
  .copytable{
	width:300px;
	margin:0 auto;
}
.underline{
	border-bottom:1px solid gray;
	height:30px;
	margin-bottom: 5px;
}

/**流程状态图**/
.flow_bar_Table{
	border: none;
	width:auto;
	margin:0 auto;
}
.flow_bar_Table td{
	border:none;
	float:left;
	cursor: pointer;
}

.text_01 {
width: 140px;
height: 54px;
text-align: center;
line-height: 38px;
position: relative;
padding-left: 5px;
padding-right: 5px;
}
.flow_bar_right {
font-size: 14px;
color: #fff;
}
.flow_bg_g {
background: url(../../images/default/background/flow_bg_g.png) no-repeat;
}
.text_word {
display: block;
text-align: center;
margin-top: -8px;
color: #ffff00;
line-height: 19px;
font-size: 12px;
max-height: 20px;
white-space: nowrap;
overflow: hidden;
text-overflow: ellipsis;
}
.next_02{position: absolute;}
.right {
float: left;
margin-left: 5px;
margin-top: 10px;
margin-right: 5px;
}
.flow_hover{background-position: 0px 0px!important;}
.flow_finish{line-height: 54px;}
.flow_current{background-position: 0px -108px;}
.flow_disabled{background-position:0px -54px;color: #fff;}
.flow_disabled .text_word{color: #fff;}
.flow_cancel:after{
	content: "作废";
	color: #ff0000;
	font-weight: bolder;
	position: absolute;
	font-size: 25px;
	top: 5px;left: 32px;
	border:3px solid red;
	padding:0px 15px;
}

ul.showAllName{margin:0px;padding:0px 5px;list-style: none;position: absolute;top: 48px;left:0px;z-index: 99;width: 130px;background-color: #509EBE;display: none;border-bottom-right-radius: 10px;border-bottom-left-radius: 10px;}
ul.showAllName li{list-style: none;color: #ffff00;text-align: center;height: 28px;line-height: 28px;font-size: 12px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.hasMore:after{content: "...";}