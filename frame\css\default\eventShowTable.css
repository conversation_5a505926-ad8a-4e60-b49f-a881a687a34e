body {
	background: #F7F6F6;
}

.enventShowTabs {
	width: 100%;
}

.tabNameContain {
	
}

.enventShowTabs	label {
	cursor: pointer;
	background: #fff;
	color: #666;
	padding: 0.97% 3%;
	float: left;
	margin-right: 5px;
	margin-bottom: 0px;
	border: 1px solid transparent;
}

label:hover {
	color: #555;
}

.enventShowTabs	label.active {
	border: 1px solid #ddd;
	border-top: 2px solid #036EA2;
	border-bottom: 1px solid #fff;
	color: #555;
	margin-bottom: -1px;
}

.panels .panel.current {
	opacity: 1;
	transition: .3s;
	-webkit-transition: .3s;
	z-index: 2;
	border: 1px solid #ddd;
}

.panels {
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	float: left;
	clear: both;
	width: 100%;
	background: #fff;
	border-radius: 0 10px 10px 10px;
	position: relative;
	z-index: 1;
}

.panel {
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	width: 100%;
	position: absolute;
	opacity: 1;
	background: #fff;
	border-radius: 10px 10px 10px 10px;
	border-top: 1px solid #ddd;
	padding: 1%;
}

.panel p {
	line-height: 40px;
	margin: 0px;
	margin-right: 8px;
	padding: 0px 4px;
	border-bottom: 1px solid #DDDDDD;
}

.panel p a {
	color: #06a59f;
	padding-left: 5px;
}

.current p:hover {
	background: #eeeeee !important;
}

