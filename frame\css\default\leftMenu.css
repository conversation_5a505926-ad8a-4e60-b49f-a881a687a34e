/* ======================================================
// * <p>Title:			海边沙滩 </p>
// * <p>Copyright:		Copyright (c) 2012 </p>
// * <p>Company:		Fortune </p>
// * @version:			1.0
// * @date:				2012-11-22
// ======================================================
*/

@charset "utf-8";
/* CSS Document */
/********************************************************************************** A 总体　****/
html,body{height:100%;} 
body{
	font:12px \5b8b\4f53,<PERSON>l,sans-serif;
	margin:0px;
	background-color:#DAD6D3;
}
/*背景*/
#bgDiv{
	width: 0;
	height: 0;
	display:none;
}
/*当菜单显示高度大于当前浏览器窗口高度时，显示该操作指引*/
#menuOptHelp{
	position: absolute;
	width: 100%;
	height:120px;
	z-index:999;
	bottom:0px;
	background:url(../../images/help/showMoreMenu.gif) no-repeat center bottom;
}
/********************************************************************************** B 菜单列表 ****/
/*菜单外框的DIV，作用：把该菜单放置在<div id="bgDiv">的上方*/
#menuFrameDiv{
	position: absolute;
	width: 129px;
	height:100%;
	z-index: 2;
	-moz-opacity:0.9; 
	filter:alpha(opacity=90);
	opacity: 0.90;
	border-top: 1px solid #2884AF;
}

#menuTabMM{
	height:100%;
	padding-top:2px;
	background-color:#1878A7;
	background-image: url(../../images/default/leftMenu/bg.gif);
	background-repeat: repeat;
	background-position: left top;
}


/********************************************************************************** C 菜单样式 ****/
/*一级菜单默认样式*/

.menuLevel1_out{
	color:#A8E3FF;
	height:30px;
	line-height:30px;
	padding-top:0px;
	padding-left:10px;
	word-spacing:0px;
	font-size:12px;
	cursor:pointer;
	border-bottom:1px solid #2884AF;
}
/*一级菜单鼠标Over时默认样式*/
.menuLevel1_over{
	color:#ffffff;
	height:30px;
	line-height:30px;
	padding-top:0px;
	padding-left:10px;
	word-spacing:0px;
	font-size:12px;
	cursor:pointer;
	border-bottom:1px solid #2884AF;
}
/*一级菜单鼠标click时默认样式*/
.menuLevel1_click{
	color:#A8E3FF;
	height:30px;
	line-height:30px;
	padding-top:0px;
	padding-left:10px;
	word-spacing:0px;
	font-size:12px;
	cursor:pointer;
	border-bottom:1px solid #2884AF;
}

.MenuLevel1Txt {
	/*padding-top:6px;*/
	padding-left:4px;
}

/*一级菜单分隔线*/
.menuLevel1_SplitLen{
	color:#000000;
	height:0;
	background:url(../../images/default/leftMenu/list_splitline.gif) no-repeat center top;
	-moz-opacity:0.5; 
	filter:alpha(opacity=50);
	opacity: 0.50;
}
/******************************************************************************** C 二级菜单样式 ****/
.menuLevel2Div{
	/*width:125px;*/
}
.menuLevel2_out{
	color:#FFFFFF;
	font-size:12px;
	background-color:#012F30;
	color:#A4A4A4;
	border-bottom:#2884AF solid 1px;
	/*min-height:16px !important;*/
	height:30px;
	line-height:30px;
	/*padding-top:5px;*/
	padding-left:18px;
	cursor:pointer;
}
.menuLevel2_over{
	color:#FFFFFF;
	font-size:12px;
	background-color:#E6E8EA;
	color:#000;
	border-bottom:#2884AF solid 1px;
	/*
	min-height:16px !important;
	padding-top:5px;
	*/
	height:30px;
	line-height:30px;
	padding-left:18px;
	cursor:pointer;
}
.menuLevel2_click{
	color:#FFFFFF;
	font-size:12px;
	background-color:#E6E8EA;
	color:#000;
	border-bottom:#2884AF solid 1px;
	/*
	min-height:16px !important;
	padding-top:5px;
	*/
	padding-left:18px;
	cursor:pointer;
	height:30px;
	line-height:30px;
}
