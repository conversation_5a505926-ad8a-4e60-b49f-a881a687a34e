@charset "utf-8";
/* 页面总体 */
body {
	margin: 0;
	padding: 0;
	color:#222;
	font-size:12px;
	font-family:Verdana,Arial,Helvetica,sans-serif;
	background-image: url(../../images/default/login/bg.jpg);
	background-repeat: repeat-x;
	background-position: left top;
	background-color: #f4f4f4;
}
a { color:#727272; text-decoration:none;}
a:hover { color:#1E90FF;}
ul,li {list-style:none; margin:0px; padding:0px;}
a, li, .knowledgeLink {word-wrap:normal; word-break:keep-all; white-space: nowrap;overflow:hidden; text-overflow: ellipsis;}
img {border:0;}

.clearfix{*zoom:1;}
.clearfix:after{display:block; overflow:hidden; clear:both; height:0; visibility:hidden; content:".";}

/* 登录页外框 */
#container {width: 1240px; margin: 0 auto;}

/* 顶部 */
#top {
	height: 79px;
	width: 1240px;
	margin: 0 auto;
	background-image: url(../../images/default/login/bgTop.png);
	background-repeat: no-repeat;
	background-position: right -5px;
}
#top img {
	margin: 10px 0 0 15px; 
}
/* 广告框顶部的背景 */
#loginTop {
    margin-left:726px;
	height: 10px;
	width: 253px;
	background-image: url(../../images/default/login/bgLogin.png);
	background-repeat: no-repeat;
	background-position: 0px -16px;
}


/* 左侧列 */
#left {
	float: left;
	width: 280px;
    padding:10px;
	background-color: #FFFFFF;
	border: 1px solid #D7D7D7;
	min-height: 666px;
}

/* 中间列 */
#center {
	float: left;
	width: 668px;
	margin-left: 8px;
	margin-right: 6px;
}

/* 右侧列 */
#right {
	float: right;
	width: 230px;
    padding:10px;
	background-color: #FFFFFF;
	border: 1px solid #D7D7D7;
	min-height: 666px;
}


/* 板块标题样式 */
.titleBg { float: left; background-color: #eee; width:280px;}
.titleBgCenter { float: left; background-color: #eee; width:646px;}
.titleBlue {
	float: left;
	font-size: 14px;
	font-weight: bold;
	color: #31A2F0;
	line-height: 17px;
	height: 17px;
	background-image: url(../../images/default/login/iconTitle.png);
	background-repeat: no-repeat;
	background-position: left center;
	text-indent: 18px;
	background-color: #ffffff;
}
.titleEn {
	float: left;
	font-size: 12px;
	color: #999999;
	height: 17px;
	line-height: 17px;
	font-weight: normal;
	padding-right: 2px;
	padding-left: 2px;
	background-color: #ffffff;
}
.titleMore {
	float: right;
	padding-right: 5px;
}

.titleRight {
    float: left; 
    width:230px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #aaaaaa;
	padding-bottom: 3px;
}
.titleBlack {
	float: left;
	font-size: 14px;
	font-weight: bold;
	line-height: 17px;
	height: 17px;
}


/* 左侧－公告 简报 */
#notice, #info {
	min-height: 100px;
	width: 280px;
	float: left;
}
#noticeList, #infoList, #recruitList {
	padding-top:5px;
	width: 265px;
	float: left;
	padding-left: 5px;
	padding-bottom: 20px;
}

#noticeList ul li, #infoList ul li {
	padding-left:20px;
    height:25px;
	line-height:25px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 -17px;
}
#noticeList ul li a, #infoList ul li a {
	float:left;
	width:192px;
}

/* 左侧－招聘 */
#recruitList ul {
	line-height: 25px;
	float: left;
}
#recruitList li {
	display: inline;
	float:left;
	width: 110px;
	padding-left: 20px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 4px;
}

/* 左侧－投诉举报建议 */
#complaint {
	width: 280px;
	min-height: 200px;
	float: left;
}
#complaintTitle {
	background-image: url(../../images/default/login/titleComplaint.jpg);
	background-repeat: no-repeat;
	background-position: left 8px;
	width: 250px;
	padding-top: 3px;
	padding-right: 10px;
	padding-bottom: 3px;
	padding-left: 5px;
}
#complaint ul li {
	height:64px;
	width: 190px;
	padding:14px 0 0 64px;
	color:#777;
}
#complaint ul li strong {
	display:block;
	height:20px;
	color:#333;
	font-size:14px;
}





/* more */
#recruitTitle a, #questionnaireTitle a, #otherTitle a {
    line-height:30px;
	margin-left: 210px;
}



/* 投诉举报建议 */
#complaintRight {
	width: 230px;
	min-height: 200px;
	float: left;
}
#complaintRightTitle {
	background-image: url(../../images/default/login/titleComplaint.jpg);
	background-repeat: no-repeat;
	background-position: left 8px;
	width: 210px;
	padding-top: 3px;
	padding-right: 10px;
	padding-bottom: 3px;
	padding-left: 5px;
}
#complaintRight ul li {
	height:64px;
	width: 150px;
	padding:14px 0 0 64px;
	color:#777;
}
#complaintRight ul li strong {
	display:block;
	height:20px;
	color:#333;
	font-size:14px;
}





/* 各列表的点击数 */
#questionnaire ul li span,
#questionnaireCenter ul li span,
#newsList ul li span,
#noticeList ul li span,
#infoList ul li span,
.knowledgeNum {
	float:right;
	color:#828282;
	font-size: 10px;
	padding-left:10px;
}



/* 中间列－广告 */
#centerSliderLogin {
	height: 301px;
	width: 668px;
	float: left;
	padding: 5px 0 5px 0;
	background-color: #FFFFFF;
	border: 1px solid #D7D7D7;
	margin-bottom: 10px;
	border-top:0;

	background-image: url(../../images/default/login/bgLogin.png);
	background-repeat: no-repeat;
	background-position: 415px -20px;

}
/* 广告外框 */
#sliderContainer {
	height: 300px;
	width: 410px;
	float: left;
	padding-top: 0px;
	padding-left: 8px;
}


/* 登录 */
#loginContainer {
	height: 300px;
	width: 230px;
	float: right;
	padding: 0;
	margin: 0 15px 0 0;
	_margin: 0 8px 0 0;
	background-image: url(../../images/default/login/holiday.jpg);
	background-repeat: no-repeat;
	background-position: right bottom;
}
#loginTitle {
	height: 36px;
	width: 215px;
	float: left;
	padding-left: 10px;
}
#loginUser {
	height: 45px;
	width: 215x;
	float: left;

}
#loginPwd {
	height: 40px;
	width: 215x;
	float: left;

}
#loginBtn{
	height: 45px;
	width: 215px;
	float: left;
	margin-left: 8px;
}
#loginBtn img {cursor:pointer;}

/* 登录输入框 */
#username, #password {
	width:152px;
	height:26px;
	font-size:14px;
	border:1px solid #D3D3D3;
	background-color: #F8F8F8;
	margin-left: 12px;
	line-height: 24px;
	padding-left: 37px;
}
#username {
	background-image: url(../../images/default/login/loginUser.jpg);
	background-repeat: no-repeat;
	background-position: left top;
}
#password {
	background-image: url(../../images/default/login/loginPwd.jpg);
	background-repeat: no-repeat;
	background-position: left top;
}
#loginTip{
	width: 215px;
	float: left;
	margin-left: 8px;
	color: red;
	font-size: 14px;
	line-height: 1.5;
	padding: 3px;
	background-color: #F8F8F8;
}





/* 新闻 */
.newsContainer {
	width: 648px;
	float: left;
	padding: 15px 10px 15px 10px;
	background-color: #FFFFFF;
	border: 1px solid #D7D7D7;
	margin-bottom: 10px;
}

#news {
	min-height: 100px;
	width: 648px;
	float: right;
	margin-left: 0px;
}
#newsList {
	padding-top:5px;
	width: 430px;
	min-height: 100px;
	float: left;
	padding-left: 10px;
}
#newsImg {
	display: block;
	float: left;
	padding-top: 8px;
	padding-left: 5px;
}
#newsImg img {
	border: 1px solid #CCCCCC;
	padding: 4px;
}
#newsList ul li {
	padding-left:20px;
    height:25px;
	line-height:25px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 -17px;
}
#newsList ul li a {
	float:left;
	width:350px;
}




/* 知识库 */
#knowledge {
	min-height: 100px;
	width: 648px;
	float: right;
}
#knowledgeList {
	padding-top:5px;
	width: 636px;
	float: left;
}
#knowledgeList ul li {
	padding-left:20px;
	height:25px;
	line-height:25px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 -17px;
}
.knowledgeLink {
	float:left;
	width:550px;
}

/* 问卷+其他－右侧 */
#otherContainer {
    float: left;
    width: 668px;
}

.others {
    width: 304px;
    padding: 10px;
    background-color: #FFFFFF;
    border: 1px solid #D7D7D7;
}






/* 问卷调查－右侧 */
#questionnaire {
	min-height: 100px;
	width: 230px;
	float: left;
	margin-top: 10px;
}
#questionnaire ul {
	width: 210px;
	padding-left: 5px;
}
#questionnaire ul li, #questionnaireCenter ul li {
	padding-left:20px;
	line-height:25px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 4px;
}
#questionnaire ul li a {
    float:left;
	width: 145px;
}

/* 问卷调查－中间 */
#questionnaireCenter {
	min-height: 100px;
	width: 300px;
	float: left;
	margin-top: 10px;
}
#questionnaireCenter ul {
	width: 280px;
	padding-left: 5px;
}
#questionnaireCenter ul li a {
    float:left;
	width: 215px;
}


/* 其他－右侧 */
#other {
	width: 230px;
	min-height: 100px;
	float: left;
	margin-top: 10px;
}
#other ul, #otherCenter ul {
	padding-left: 5px;
	display: block;
	line-height: 25px;
	float: left;
}
#other li {
	display: inline;
	float:left;
	width: 90px;
	overflow:hidden;
	padding-left: 20px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 4px;
}

/* 其他－中间 */
#otherCenter {
	width: 300px;
	min-height: 100px;
	float: left;
	margin-top: 10px;
}
#otherCenter li {
	display: inline;
	float:left;
	width: 120px;
	overflow:hidden;
	padding-left: 20px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 4px;
}






/* 底部版权 */
#copyrights {
	height: 30px;
	width: 600px;
	float: right;
	text-align: right;
	color: #666666;
	margin-top: 10px;
}





/* jq焦点图特效插开始件 */
.slider {
	width:400px;
	height:300px;
	overflow:hidden;
	position:relative;
	padding: 0;
}
.slider ul {
	height:300px;
	position:absolute;
}
.slider ul li {
	float:left;
	width:400px;
	height:300px;
	overflow:hidden;
	position:relative;
	background:#000;
}
.slider ul li div {position:absolute; overflow:hidden;}
.slider .btnBg {
	position:absolute;
	width:400px;
	height:20px;
	left:0;
	bottom:0;
	background:#000;
	display:none;
}
.slider .btn {
	position:absolute;
	width:400px;
	height:13px;
	padding:0 0 0 10px;
	right:0;
	bottom:5px;
	text-align:right;
}
.slider .btn span {
    display:inline-block; 
    _display:inline; 
    _zoom:1; 
    width:18px; 
    height:18px; 
    line-height:18px; 
    text-align:center; 
    _font-size:0; 
    margin-left:2px; 
    cursor:pointer; 
    background:#999999;
}
.slider .btn span.on {background:#ff6600;}
.slider .preNext {
    width:25px; 
    height:50px; 
    position:absolute; 
    top:120px; 
    background:url(../../images/slid/sprite.png) no-repeat 0 0; cursor:pointer;
}
.slider .pre {left:0;}
.slider .next {right:0; background-position:right top;}




/* 新闻页面div begin */
#divOverlay{ display:none; position:absolute; z-index:998; width:100%; background-color:#aaa; filter:alpha(opacity=80); opacity:0.8; moz-opacity:0.8; text-align:center;} 
#divNewsContainer{ display:none; position:absolute; z-index:999; top:20px; left:100px; width:800px; height:450px; border:1px solid #666; text-align:center;} 

#divNewsTitle{background-color:#eee; height:20px; line-height:20px;} 
#divNewsTitle span {float:left; color:#727272;} 
#divNewsTitle a {float:right;} 
#divNewsContent{text-align:center;} 
/* 新闻页面div end */

/* 知识库页面div add by jj.ye 2013.09.14 begin */
#divKnowledgeContainer{ display:none; position:absolute; z-index:999; top:20px; left:90px; width:800px; height:450px; border:1px solid #666; text-align:center;} 
#divKnowledgeTitle{background-color:#eee; height:20px; line-height:20px;} 
#divKnowledgeTitle span {float:left; color:#727272;} 
#divKnowledgeTitle a {float:right;} 
#divKnowledgeContent{text-align:center;} 
/* 知识库页面div add by jj.ye 2013.09.14 end */





/* 以下为新闻列表页面样式 */

/* 新闻内页外框 */
#containerSubPage {width: 978px; margin: 0 auto;}

/* 顶部 */
#newsTop {
	height: 67px;
	width: 978px;
	background-image: url(../../images/default/login/newsTop.jpg);
	background-repeat: no-repeat;
	background-position: left top;
	margin-top: 0;
	margin-right: auto;
	margin-bottom: 0;
	margin-left: auto;
}
#newsTop img {
	margin: 10px 0 0 15px; 
}

#newsLeft {
	width: 270px;
	float: left;
	background-repeat: repeat-y;
	background-position: left top;
	background-image: url(../../images/default/login/leftBoxBg.jpg);
}

#newsLeftTop {
	width: 270px;
	float: left;
	background-repeat: repeat-y;
	background-position: left top;
	background-image: url(../../images/default/login/newsLeftTop.jpg);
	height: 9px;
}
#newsLeftBottom {
	height: 20px;
	width: 270px;
	float: left;
	background-repeat: no-repeat;
	background-position: left bottom;
	background-image: url(../../images/default/login/leftBoxBottom.jpg);
}


.newsLeftTitle {
	height: 25px;
	background-image: url(../../images/default/login/titleLine.jpg);
	background-repeat: no-repeat;
	background-position: 25px bottom;
}
.newsLeftTitle span {
    margin-top: 3px;
    margin-left: 25px;
    width: 180px;
    float: left;
	font-size: 14px;
	font-weight: bold;
	color: #31A2F0;
	background-color: #ffffff;
}


.newsLeftListTopBg {
	background-repeat: no-repeat;
	background-position: left top;
	background-image: url(../../images/default/login/leftBoxBgBlue.jpg);
}

.newsLeftList {
	min-height: 100px;
	width: 270px;
	float: left;
}

.newsLeftListMargin {
	margin-top:10px;
}
.newsLeftList a {
    line-height:20px;
}
.newsLeftList ul li span, #newsMainList ul li span {
	float:right;
	color:#828282;
	font-size: 10px;
	padding-left:10px;
}

.newsLeftList ul {
	width: 210px;
	padding-left: 22px;
}
.newsLeftList ul li {
	padding-left:20px;
	line-height:25px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 -17px;
}

/* 右侧列 */
#newsRight {
	width: 708px;
	float: right;
	background-image: url(../../images/default/login/rightBoxBg.jpg);
	background-repeat: repeat-y;
	background-position: left top;
}

#newsBanner {
	width: 708px;
	float: right;
	background-image: url(../../images/default/login/newsBannerBg.jpg);
	background-repeat: repeat-y;
	background-position: left top;
	height: 206px;
}

/* 右框顶部 */
.rightBoxBottom {
	height: 15px;
	width: 708px;
	float: right;
	background-image: url(../../images/default/login/rightBoxBottom.jpg);
	background-repeat: no-repeat;
	background-position: left top;
}
/* 右框底部 */
.rightBoxTop {
	height: 9px;
	width: 708px;
	float: right;
	background-image: url(../../images/default/login/rightBoxTop.jpg);
	background-repeat: no-repeat;
	background-position: left top;
}

/* jq焦点图特效插开始件 */
.newsSlider {
	margin-top: 1px;
	margin-left: 21px;
	width:667px;
	height:162px;
	overflow:hidden;
	position:relative;
}
.newsSlider ul {
	height:162px;
	position:absolute;
}
.newsSlider ul li {
	float:left;
	width:667px;
	height:162px;
	overflow:hidden;
	position:relative;
	background:#000;
}
.newsSlider ul li div {position:absolute; overflow:hidden;}
.newsSlider .btnBg {
	position:absolute;
	width:667px;
	height:20px;
	left:0;
	bottom:0;
	background:#000;
	display:none;
}
.newsSlider .btn {
	position:absolute;
	width:667px;
	height:13px;
	padding:0 0 0 10px;
	right:0;
	bottom:5px;
	text-align:right;
}
.newsSlider .btn span {
    display:inline-block; 
    _display:inline; 
    _zoom:1; 
    width:18px; 
    height:18px; 
    line-height:18px; 
    text-align:center; 
    _font-size:0; 
    margin-left:2px; 
    cursor:pointer; 
    background:#999999;
}
.newsSlider .btn span.on {background:#ff6600;}
.newsSlider .preNext {
    width:25px; 
    height:50px; 
    position:absolute; 
    top:50px; 
    background:url(../../images/slid/sprite.png) no-repeat 0 0; cursor:pointer;
}
.newsSlider .pre {left:0;}
.newsSlider .next {right:0; background-position:right top;}


#newsBrief h1 {
	font-size: 14px;
	display: block;
	margin-left: 15px;
	line-height: 28px;
	float: left;
	padding-top: 5px;
}
#newsBrief p {
    text-indent:2em;
	display: block;
	margin-left: 15px;
	line-height: 16px;
	float: left;
	width: 400px;
	height: 60px;
}

#newsMainList {
	padding-top:5px;
	width: 608px;
	float: left;
	padding-left: 20px;
}
#newsMainList ul li {
	padding-left:20px;
	height:30px;
	line-height:30px;
	background-image: url(../../images/default/login/icon.gif);
	background-repeat: no-repeat;
	background-position: 0 6px;
	border: 1px #CCCCCC;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: dotted;
	border-left-style: none;
}
#newsMainList ul li a {
	float:left;
	width:545px;
}
#newsPagenation {
	width: 660px;
	color: #999999;
	display: block;
	padding: 15px 25px;
	float: right;
}

/* 分页样式 begin */
.comPagination {
    overflow:hidden;
    background-color:#eeeeee;
    border:solid 0px #C9C9C9;
}
.comPaginationLeft {
    width:400px;
    float:left;
    padding-left:10px;
    line-height:28px;
    overflow:hidden;
}
.comPaginationRight {
    float:right;
    line-height:28px;
    padding-right:5px;
}

.comPagination a {
    text-decoration:none;
    line-height:16px;
    padding:2px 6px 3px 6px;
}
.comPaginationFirst {
    background:url(../../images/default/ico/first.gif) no-repeat;
}
.comPaginationFirstDisabled {
    cursor: default;
    background:url(../../images/default/ico/first_gray.gif) no-repeat;
}
.comPaginationPrev {
    background:url(../../images/default/ico/prev.gif) no-repeat;
}
.comPaginationPrevDisabled {
    cursor: default;
    background:url(../../images/default/ico/prev_gray.gif) no-repeat;
}
.comPaginationNext {
    background:url(../../images/default/ico/next.gif) no-repeat;
}
.comPaginationNextDisabled {
    cursor: default;
    background:url(../../images/default/ico/next_gray.gif) no-repeat;
}
.comPaginationLast {
    background:url(../../images/default/ico/last.gif) no-repeat;
}
.comPaginationLastDisabled {
    cursor: default;
    background:url(../../images/default/ico/last_gray.gif) no-repeat;
}
.comPaginationSeparator {
	height: 16px;
	border-left: 1px solid #ccc;
	border-right: 1px solid #fff;
	margin: 3px 3px;
}
.comPaginationNum {
	border:1px solid #ccc;
	margin:0 2px;
	width:30px;
	text-align:center;	
	outline:none;
	+vertical-align:middle;
	position:relative;
	_left:0;_top:-1px;
	height:14px;
}
/* 分页样式 end */

