/****面板样式****/
.panelMain { 
   border : 2px solid #F4F4F4;
   position :absolute; 
}
/****面板title****/
.panelTitle {
   background-color : #F4F4F4; 
}
/****面板title 左边标题****/
.panelTitleLeft {
  font-family:宋体;
  font-size:16px;
  font-weight:bold;
  color:#0099CC;
  padding-left:0.5em;
}
/****面板title 右边关闭****/
.panelTitleRight {
  font-family:宋体;
  font-size:16px;
  font-weight:bold;
  color:#0099CC;
  padding-left:0.5em;
  cursor : pointer; 
  width : 1%;
}
.panelTitleRight:hover {
   color:red;
}
/****面板内容****/
.panelContent { 
  background-color : #FFF;
}
/****面板阴影****/
.panelShadeDiv { 
  background-color:#8B8378;
  position :absolute;
  z-index: 100000; 
  border : 1px solid #DCDCDC;
}
.panelBackGroundDiv { 
  left : 0px;
  top : 0px;
  background-color:blue;
  position :absolute;
  z-index: 99999; 
  opacity : 0.1;
  filter : alpha(opacity=10);
}