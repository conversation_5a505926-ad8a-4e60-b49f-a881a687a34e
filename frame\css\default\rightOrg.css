/* ======================================================
 * <p>Title:		公共页面样式 </p>
 * <p>Description:	页面样式标准CSS，用于新增、修改、列表等页面 </p>
 * <p>Copyright:	Copyright (c) 2006 </p>
 * <p>Company:		Clifford </p>
 * @author:			dx.yan
 * @version:		1.00
 * @date:			2006-7-10
 * @update:         x.huang 由于新框架不是在最新该页面升级，结果升级后发现这个页面很多功能丢失，所以分配组织架构界面样式独立用该样式,需美工优化该文件和做其他皮肤
======================================================= */
/************************************
 * 全局样式 
 ************************************/
/*** body 边界，滚动条样式 ***/
body {
	background-color: #ffffff;

	margin-left: 5px;
	margin-top: 5px;
	margin-right: 5px;
	margin-bottom: 5px;
}

/*** 全局字体 ***/
body, td, th, p, div, span, li, input, textarea, a{
	font-size: 12px;
	font-family: 宋体;
}

/*** 超级链接样式 ***/
a:link {
	color: #333333;
	text-decoration: none;
}
a:visited {
	color: #999999;
	text-decoration: none;
}
a:hover {
	color: #ff6600;
	text-decoration: underline;
}
a:active {
	color: #ff9900;
	text-decoration: none;
}

/*** 表格边框样式 ***/
table {
	border-collapse: collapse; 
}

/*** 单元格边距样式（请将表格属性统一为：cellspacing="0" cellpadding="0"） ***/
td{
	padding-left: 3px;
	padding-right: 3px;
	padding-top: 2px;
	padding-bottom: 2px;
}

/*** 表头行样式，相当于 TrTitle ***/
th {
	padding-left: 3px;
	padding-right: 3px;
	padding-top: 2px;
	padding-bottom: 2px;

	text-align:center;	
	height:22px;
	font-size: 12px;
	background: url("../images/background/tableTopBg.gif") repeat-x center top;
}

/* form */
form {
	margin: 0; 
}


/************************************
 * 页面布局表格样式 
 ************************************/
/*** 通用表格样式 ***/
.TableGeneral{
	width: 100%;
	border-collapse: collapse;
	border-color: #99CCFF;

	border-top: #aab3b3 1px solid; 
	border-bottom: #aab3b3 1px solid; 
	border-right: #858b8c 1px solid;
	border-left: #858b8c 1px solid; 
}

/*** 无间隔的单元格，用于外层布局表格 ***/
.TdNoPadding{
	padding: 0;
	margin: 0;
}

/*** 分隔行 ***/
.TrSeparator{
	height: 3px;
	padding: 0;
	margin: 0;
}

/*** 灰色分隔行 ***/
.TrSeparatorGray{
	background-color: #e2e2e2;
	height: 2px;
	padding: 0;
	margin: 0;
}

/*** 页面标题表格样式 ***/
.TablePageTitle{
	width: 100%;
	height: 30px;
	background: url("../images/ico/titleIco.gif") no-repeat 5px center;	
	background-color: #f7f7f7;

	border-top: #aab3b3 1px solid; 
	border-bottom: #aab3b3 1px solid; 
	border-right: #858b8c 1px solid;
	border-left: #858b8c 1px solid; 
}
/*** 页面标题字体 ***/
.TdPageTitle{
	height: 30px;
	padding-left: 30px;
	padding-right: 30px;
	padding-bottom: 0px;
	padding-top: 3px;
	font-weight: bold;
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: 2px;
	word-break: keep-all;
	white-space: nowrap;
}
/*** 页面标题右侧单元格 ***/
.TdPageTitleRight{
	text-align:right;
	padding-right:5px;
	vertical-align:middle;
}
/*** 大分类单元格样式 ***/
.TdBigTitle{
	padding-left:5px;
	padding-top:5px;
	text-align: left;
	background-color: #E1FB74;
	font-weight:bold;
	height:20px;
}
/*** 项目单元格样式 ***/
.TdInfoTitle{
	padding:3px;
	text-align: right;
	background-color: #f9f9f9;
	width: 100px;
}
/*** 内容单元格样式 ***/
.TdInfoContent{  
	padding:3px 7px 3px 3px;
	text-align:left;
}


/*** 表头行样式 ***/
.TrTitle{
	text-align:center;	
	font-size: 12px;
	height:20px;
	background: url("../images/background/tableTopBg.gif") repeat-x center bottom;
}
/*** 汇总行样式 ***/
.TrSum{
	background-color: #f0f0f0;
	font-weight: bold;
	height: 20px;
	background: url("../images/background/tableBottomBg.gif") repeat-x bottom scroll;
}


/*** 鼠标停留时列表行的样式：偶数行0，奇数行1 ***/
.TrMouseOver0{
	color: #ffffff;
	background-color: #4e8ae2;
}
.TrMouseOver1{
	color: #ffffff;
	background-color: #4e8ae2;
}
/**选中列表行的颜色*/
.TrMouseClick{
	color: #000000;
	background-color: #DDEBFF;
}
/*** 列表行样式：偶数行0，奇数行1 ***/
.TrList0{
	color: #000000;
	background-color: #ffffff;
}
.TrList1{
	color: #000000;
	background-color: #eeeeee;
}



/*** 列表内容单元格样式 ***/
.TdListContent{
	padding-left: 5px;
	padding-right: 5px;
	padding-top: 2px;
	padding-bottom: 2px;
}

/*** 操作图标单元格样式 ***/
.TdListOpt{
	width:1px;
	padding-left: 5px;
	padding-right: 5px;
	padding-top: 5px;
	padding-bottom: 0px;
	text-align:center;
	word-spacing:2px;
}

/*** 编辑页面中，提交按钮之间的间隔 ***/
.TdWordSpacing10{
	word-spacing:10px;
}


/*** 分页表格样式 ***/
.TablePagination{
	width: 100%;
	height: 30px;
	letter-spacing: 2px;
	background-color: #f7f7f7;
	padding-left: 5px;
	padding-right: 5px;

	border-top: #aab3b3 1px solid;
	border-bottom:  #aab3b3 1px solid;
	border-right: #858b8c 1px solid;
	border-left:  #858b8c 1px solid;
}

/*** 分页表格内容单元格样式 ***/
.TdPagination{
	padding-left: 10px;
	padding-right: 10px;

}
/*** 分页链接字体样式 ***/
.LinkPagination:link, .LinkPagination:visited, .LinkPagination:active {
 	color:#000000;
	cursor:pointer;
}
.LinkPagination:hover {
	color: #ff6600;
}
/*** 分页链接不可操作字体样式 ***/
.LinkPaginationDisabled:link, .LinkPaginationDisabled:visited, .LinkPaginationDisabled:hover, .LinkPaginationDisabled:active {
 	color:#999999;
	cursor:default;
	text-decoration: none;
}



/************************************
 * 输入框样式
 ************************************/
/*** 只读输入框 ***/
input.InputReadonly { 
	background-color: #eeeeee;
}
/*** 细边框输入框 ***/
input.InputPlane {
	border-color: #a0a0a0;
	border-style: solid; 
	border-width: 1px; 
}
/*** 细边框只读输入框 ***/
input.InputPlaneReadonly {
	background-color: #eeeeee;
	border-color: #a0a0a0;
	border-style: solid; 
	border-width: 1px; 
}
/*** 下划线输入框 ***/
input.InputLine { 
	border-color: #a0a0a0;
	border-style: solid; 
	border-bottom-width: 1px; 
	border-left-width: 0; 
	border-right-width: 0; 
	border-top-width: 0; 
}
/*** 下划线只读输入框 ***/
input.InputLineReadonly {
	background-color: #eeeeee;
	border-color: #a0a0a0;
	border-style: solid;
	border-bottom-width: 1px;
	border-left-width: 0;
	border-right-width: 0;
	border-top-width: 0;
}
/*** 下划线粗字体输入框 ***/
input.InputLineBold {
	font-weight: bold;
	border-color: #a0a0a0;
	border-style: solid;
	border-bottom-width: 1px;
	border-left-width: 0;
	border-right-width: 0;
	border-top-width: 0;
}
/*** 无边框输入框 ***/
input.InputNoBorder {
	border: 0;
}
/*** 操作按钮 ***/
input.InputButton{
	text-decoration: none;
	background-color: #ffffff;
	letter-spacing: 3px;
	border-width: 1px;
	border-color: #a0a0a0;
	border-style: outset;
	height: 20px;
	padding-top: 3px;
	padding-left: 7px;
	padding-right: 3px;
	padding-bottom: 2px;
}
input.InputButtonMouseOver{
	color: #ff6600;
	text-decoration: none;
	background-color: #f9f9f9;
	letter-spacing: 3px;
	border-width: 1px;
	border-color: #a0a0a0;
	border-style: outset;
	height: 20px;
	padding-top: 3px;
	padding-left: 7px;
	padding-right: 3px;
	padding-bottom: 2px;
	cursor:pointer;
}

/*** 细边框文本区 ***/
textarea.TextareaPlane {
	border-color: #a0a0a0;
	border-style: solid; 
	border-width: 1px; 
}
/*** 自动伸缩TextArea样式 ***/
/*自动伸缩框所在TD样式*/
.TextAreaStretchTD{
  	vertical-align:top;
	padding-top:3px;
	height:25px;
}
/*自动伸缩框所在DIV样式*/
.TextAreaStretchDivOut{
	position:relative;
	width:100%;
	height:100%;
}
.TextAreaStretchDivInner{
	position:absolute; 
  	width:100%; 
 	height:0px;
}
/*自动伸缩框自已的样式*/
textarea.TextAreaStretch {
	height:18px;
}

/************************************
 * 文本样式
 ************************************/
/*** 标题 ***/
.TextTitle {
	color: darkblue;
	font-weight: bold;
	font-size: 14px;
}
/*** 关键字 ***/
.TextKeyword{
	color: peru;
	font-weight: bold;
	font-size: 12px;
}
/*** 有边框的提示信息 ***/
.TextTip {
	background: #f8fef2;
	color: #333333;
	border: 1px solid #999999; 
	margin-top: 1px;
	margin-bottom: 1px; 
	padding: 5px; 
}
/*** 对、是、正确字体颜色：绿色 ***/
.TextYes{
	color: #008000;
}
/*** 错、否、无效字体颜色：红色 ***/
.TextNo{
	color: #ff0000;
}
/*** 普通签名 ***/
.TextSignature{
	font-size: 24pt;
	font-family: 华文行楷,隶书,楷体_GB2312,宋体;
	font-weight: bolder;
	color: black;
	white-space: nowrap;
}

/************************************
 * 其他样式
 ************************************/
/*** 图形按钮 ***/
img.ImgButton{
	cursor: pointer;
	border: 0;
	vertical-align:bottom;
}
/*** 注意事项的“注：”的样式 ***/
p.PNotice{
	color: red;
	padding-top: 6px;
	padding-left: 6px;
	margin-bottom: 0;
}
