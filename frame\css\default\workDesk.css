@charset "utf-8";
body {font:12px/1.5 \5b8b\4f53,Arial,sans-serif;background:#ffffff; margin:0; padding:0;}
a, div, span, li, table, td, input, label, select, option, textarea, button, fieldset, legend {font:12px/1.5 \5b8b\4f53,Arial,sans-serif;}
a {color:#000; text-decoration:none; font:12px/1.5 \5b8b\4f53,Arial,sans-serif;}
a:visited {color:#000; text-decoration:none;}
a:hover {color:#FF6600; text-decoration:underline;}
a:active {color:#FF6600; text-decoration:none;}
img {border:0;}
form{margin:0; padding:0;}
.ui-widget input,.ui-widget select, .ui-widget textarea, .ui-widget button, input,textarea,select,option{
	font:12px \5b8b\4f53,Arial,sans-serif;
}
.ui-widget input,.ui-widget select, .ui-widget textarea, .ui-widget button, input,textarea {border:1px solid #A1C4E0;}
.ui-widget textarea, textarea {overflow:auto; padding:1px; }

.jazz-ui-StyledBox-header {font-size:12px; font-weight:bold; height:20px; padding: 3px 5px 3px 1px;}
.ui-icon-a {font-size:12px; font-weight:nomal; letter-spacing: 0px;}

.ulList {margin:0; padding:0; list-style:none;}

.ulList li{
    font-size:12px; 
    line-height:18px;
    padding-top:6px;
    margin: 0 10px;
    border: 1px #CCCCCC;
    border-top-style: none;
    border-right-style: none;
    border-bottom-style: dotted;
    border-left-style: none;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
	word-wrap:normal;
	word-break:keep-all;
}

.numberTodo{color:red;} 
.numberNormal{color:balck;} 
.numberMoney{color:navy;} 

.floatL {float:left;}
.floatR {float:right;}


.notWrap {word-wrap:normal; word-break:keep-all; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;}

/* 外框 */
.container {width:100%; height:100%;}
.containerTd {padding:0 8px;vertical-align: top;}

.tableList{
	table-layout: fixed;
	width:100%;
}
.tableList td{
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
    /*padding: 6px 3px 0px 3px; qi修改 common.css中的td,.comTab_Table td也同样*/
    padding: 8px 3px 7px 3px;
    border: 1px #CCCCCC;
    border-top-style: none;
    border-right-style: none;
    border-bottom-style: dotted;
    border-left-style: none;
}
.workDeskBig body {font:14px/1.5 \5b8b\4f53,Arial,sans-serif;}
.workDeskBig a, .workDeskBig div,  .workDeskBig span, .workDeskBigli, .workDeskBig table, .workDeskBig td, .workDeskBig input, .workDeskBig label, .workDeskBig select, .workDeskBig option, .workDeskBig textarea, .workDeskBig button, .workDeskBig fieldset, .workDeskBig legend {font:14px/1.5 \5b8b\4f53,Arial,sans-serif;}
.workDeskBig a { font:14px/1.5 \5b8b\4f53,Arial,sans-serif;}
.workDeskBig .ui-widget input, .workDeskBig .ui-widget select, .workDeskBig .ui-widget textarea, .workDeskBig .ui-widget button, .workDeskBig input,.workDeskBig textarea,.workDeskBig select,.workDeskBig option{
	font:14px \5b8b\4f53,Arial,sans-serif;
}

.workDeskBig .jazz-ui-StyledBox-header {font-size:14px; font-weight:bold; height:20px; padding: 3px 5px 3px 1px;}
.workDeskBig .ui-icon-a {font-size:14px; font-weight:nomal; letter-spacing: 0px;}


.workDeskBig .ulList li{
    font-size:14px; 
}


