@charset "utf-8";
body {font:14px/1.5 \5b8b\4f53,Arial,sans-serif;background:#ffffff; margin:0; padding:0;}
a, div, span, li, table, td, input, label, select, option, textarea, button, fieldset, legend {font:14px/1.5 \5b8b\4f53,Arial,sans-serif;}
a {color:#000; text-decoration:none; font:14px/1.5 \5b8b\4f53,Arial,sans-serif;}
a:visited {color:#000; text-decoration:none;}
a:hover {color:#FF6600; text-decoration:underline;}
a:active {color:#FF6600; text-decoration:none;}
img {border:0;}
form{margin:0; padding:0;}
.ui-widget input,.ui-widget select, .ui-widget textarea, .ui-widget button, input,textarea,select,option{
	font:14px \5b8b\4f53,Arial,sans-serif;
}
.ui-widget input,.ui-widget select, .ui-widget textarea, .ui-widget button, input,textarea {border:1px solid #A1C4E0;}
.ui-widget textarea, textarea {overflow:auto; padding:1px; }

.jazz-ui-StyledBox-header {font-size:14px; font-weight:bold; height:20px; padding: 0px 5px 1px 1px;}
.ui-icon-a {font-size:14px; font-weight:nomal; letter-spacing: 0px;}

.ulList {margin:0; padding:0; list-style:none;}

.ulList li{
    font-size:14px; 
    letter-spacing: 2px;
    line-height:20px;
    padding-top:8px;
    margin: 0 10px;
    border: 1px #CCCCCC;
    border-top-style: none;
    border-right-style: none;
    border-bottom-style: dotted;
    border-left-style: none;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
	word-wrap:normal;
	word-break:keep-all;
}

.numberTodo{color:red;} 
.numberNormal{color:balck;} 
.numberMoney{color:navy;} 

.floatL {float:left;}
.floatR {float:right;}


.notWrap {word-wrap:normal; word-break:keep-all; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;}

/* 外框 */
.container {width:100%; height:100%;}
.containerTd {padding:0 8px;vertical-align: top;}

.tableList{
	table-layout: fixed;
	width:100%;
}
.tableList td{
    letter-spacing: 2px;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
    padding: 8px 3px 0px 3px;
    border: 1px #CCCCCC;
    border-top-style: none;
    border-right-style: none;
    border-bottom-style: dotted;
    border-left-style: none;
}
.workDeskSmall body {font:12px/1.5 \5b8b\4f53,Arial,sans-serif;}
.workDeskSmall a, .workDeskSmall div,  .workDeskSmall span, .workDeskSmallli, .workDeskSmall table, .workDeskSmall td, .workDeskSmall input, .workDeskSmall label, .workDeskSmall select, .workDeskSmall option, .workDeskSmall textarea, .workDeskSmall button, .workDeskSmall fieldset, .workDeskSmall legend {font:12px/1.5 \5b8b\4f53,Arial,sans-serif;}
.workDeskSmall a { font:12px/1.5 \5b8b\4f53,Arial,sans-serif;}
.workDeskSmall .ui-widget input, .workDeskSmall .ui-widget select, .workDeskSmall .ui-widget textarea, .workDeskSmall .ui-widget button, .workDeskSmall input,.workDeskSmall textarea,.workDeskSmall select,.workDeskSmall option{
	font:12px \5b8b\4f53,Arial,sans-serif;
}

.workDeskSmall .jazz-ui-StyledBox-header {font-size:12px; font-weight:bold; height:20px; padding: 3px 5px 3px 1px;}
.workDeskSmall .ui-icon-a {font-size:12px; font-weight:nomal; letter-spacing: 0px;}


.workDeskSmall .ulList li{
    font-size:12px; 
}


