@charset "UTF-8";

.msgbox {
	opacity: 0.92;
	filter: alpha(opacity = 92);
	width: 400px;
	min-height: 120px;
	position: absolute;
	right: 4%;
	bottom: 4%;
	z-index: 99999;
	border-radius: 6px;
	padding: 9px;
	box-shadow: 1px 1px 20px rgb(0 0 0/ 30%) !important;
}

.msgTitle {
	height: 18px;
	line-height: 18px;
	color: black;
	font-size: 14px;
	font-weight: bolder;
}

.msgbar {
	float: right;
	font-size: 11px;
	cursor: pointer;
	padding-right: 10px;
	color: #5e5959;
}

.msghr {
	background: #BEBEBE;
}

.msgConfirm {
	width: 100%;
	height: 20px;
	line-height: 20px;
	padding: 5px;
	text-align: right;
}

.msgConfirm a {
	float: right;
	color: #5e5959;
	padding-right: 5px;
}

.msgRemark {
	max-height: 60px;
	overflow-y: auto;
	margin: 5px;
}

.msgBottom {
	text-align: right;
}

.zcm-content {
	padding: 15px;
	font-size: 16px;
}

#zcmClose {
	position: absolute;
	bottom: 10px;
	right: 10px;
	width: 100px;
}

html body.layui-layout-body .zcmCloseG {
	background: #BEBEBE !important;
	color: #ffffff !important;
}