@charset "UTF-8";

div.aems-div {
	position: absolute;
	width: 900px;
	height: 500px;
	background-color: white;
	border: 1px solid #ccc;
	display: none;
	left: -200px;
}

ul.aems-div-app {
	position: absolute;
	top: 0px;
	left: 0px;
	bottom: 0px;
	width: 140px;
	margin: 0;
	overflow-y: auto;
}

ul.aems-div-app::-webkit-scrollbar {
	width: 3px;
}

ul.aems-div-app li img {
	width: 16px;
	height: 16px;
	line-height: 16px;
	vertical-align: middle;
	padding-bottom: 3px;
	padding-right: 3px;
}

div.aems-div-menu {
	position: absolute;
	top: 0px;
	left: 140px;
	bottom: 0px;
	right: 0px;
	overflow-y: auto;
	padding: 5px;
	cursor: auto;
}

div.aems-div-menu::-webkit-scrollbar {
	width: 10px;
}

div.aems {
	height: 40px !important;
	line-height: 40px !important;
}

li.aems_li {
	background: none;
	padding: 0px 5px;
}

.aems-div-menu-one {
	height: auto;
	line-height: normal;
	padding: 5px;
	border-bottom: 2px dashed;
}

.aems-div-menu-one b, .aems-div-menu-one i {
	font-size: 16px;
}

div.aems-div-menu-two {
	width: 160px;
	display: inline-block;
	overflow: hidden;
	vertical-align: top;
	margin: 0px 5px;
	cursor: pointer;
}

.aems-div-menu-two span {
	font-size: 14px;
}

.aems-div-menu-two-parent {
	padding: 5px;
}

.aems-div-menu-search-icon {
	font-size: 24px;
	vertical-align: middle;
	padding: 3px;
}

.aems-div-menu-search-input {
	height: 30px !important;
	line-height: 30px !important;
	width: 250px;
	vertical-align: middle;
	border-radius: 0% 15px 15px 0%;
	display: inline-block;
}

.aems-div-menu-search {
	margin-left: 6px;
}

.aems-div-menu-two span.layui-badge-dot {
	position: relative;
	top: inherit;
	vertical-align: top;
}

.aems-div-menu-two:hover {
	background-color: #c1c1c1;
}

#compAppList li.layui-this:after {
	content: '〉';
	position: absolute;
	right: 0px;
}

img.aems-div-menu-kejin {
	position: absolute;
	top: 3px;
	right: 5px;
}

ul.aems_ul  li.aems_li {
	padding: 0px;
	background: #efefef;
}