<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑审批流程节点</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/approvalBelongFlowNodeEdit.css">
</head>
<body>
	<div class="head0">
		<span class="head1_text fw700">
			<i menuIcon class="layui-icon2"></i>
			<span approvalFlowName>节点设置</span>
		</span>
		<div class="head0_right fr">
			<input type="button" class="layui-btn layui-btn-sm" value="保存" onclick="approvalBelongFlowNodeEdit.submit();" />
			<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" />
		</div>
	</div>
	<div class="bodys">
		<div class="layui-table main_table" style="margin-bottom: 0;">
			<form class="layui-form" lay-filter="param">
				<input id="paramSaveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="" lay-submit lay-filter="saveParam" />
				<input type="hidden" name="funCode" />
				<input type="hidden" name="appCode" />
				<input type="hidden" name="compNo" />
				<input type="hidden" name="approvalBelongFlowNodeID" />
				<input type="hidden" name="approvalBelongFlowNodeCode" />
				<input type="hidden" name="customApprovalFlowNodeCode" />
				<input type="hidden" name="customApprovalFlowCode" />
				<input type="hidden" name="approvalBelongCode" />
				<input type="hidden" name="approvalIndex" />
				<input type="hidden" name="optUID" />
				<input type="hidden" name="optUserName" />
				<input type="hidden" name="optDate" />
				<input type="hidden" name="current" />
				<input type="hidden" name="state" />
				<input type="hidden" name="index" />
				<fieldset class="layui-hide">
                    <legend>基础信息（不可修改）</legend>
                </fieldset>
				<div class="layui-form-item layui-hide">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						节点名称
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required|limit" readonly="readonly" limit="200" name="approvalBelongFlowNodeName" class="layui-input" />
					</div>
					<label class="layui-form-label"> 业务编号 </label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="businessCode|limit" readonly="readonly" limit="200" name="businessCode" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item layui-hide">
					<label class="layui-form-label"> 审批表单 </label>
					<div class="layui-input-inline">
						<input type="text" name="approvalCustomFormName" class="layui-input" placeholder="默认" readonly="readonly" />
						<input type="hidden" name="approvalCustomFormCode" />
					</div>
					<label class="layui-form-label"> 状态流转<i class="layui-icon2" approvalNodeStateTips>&#xe890;</i> </label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="integer" limit="200" name="approvalNodeState" class="layui-input" readonly="readonly"/>
					</div>
				</div>
				<fieldset class="layui-hide">
                    <legend>审批条件（不可修改）</legend>
                </fieldset>
				<div class="layui-form-item layui-hide">
					<label class="layui-form-label"> 审批条件 </label>
					<div class="layui-input-inline">
						<select name="approvalCondition" lay-filter="approvalCondition">
							<option value="">无</option>
							<option value="into">进入审批</option>
							<option value="skip">跳过审批</option>
						</select>
					</div>
					<label approvalCondition class="layui-form-label layui-hide"> 条件类型 </label>
					<div approvalCondition class="layui-input-inline layui-hide">
						<select name="approvalConditionType" lay-filter="approvalConditionType">
							<option value="0">单条件</option>
							<option value="1" disabled="disabled">表达式</option>
							<option value="2">接口</option>
						</select>
					</div>
				</div>
				<div approvalCondition class="layui-form-item  layui-hide">
					<label approvalConditionType="0" class="layui-form-label"> 字段归属 </label>
					<div approvalConditionType="0" class="layui-input-inline">
						<select name="approvalFieldBelong" lay-filter="approvalFieldBelong">
							<option value="form">表单</option>
							<option value="approval">审批内容</option>
						</select>
					</div>
				</div>
				<div approvalCondition class="layui-form-item layui-hide">
					<label approvalConditionType="0" class="layui-form-label">
						<span style="color: red;">*</span>
						字段业务编号
					</label>
					<div approvalConditionType="0" class="layui-input-inline" style="width: 150px;">
						<input type="text" lay-verify="required|limit" readonly="readonly" limit="200" approvalConditionBusinessCode class="layui-input" lay-filter="approvalConditionSingle" />
					</div>
					<div approvalConditionType="0" class="layui-input-inline" style="width: 100px;">
						<select approvalConditionSymbol lay-filter="approvalConditionSingle">
							<option value="=">等于</option>
							<option value="<>">不等于</option>
							<option value="LIKE">包含</option>
							<option value="NOT LIKE">不包含</option>
							<option value=">">大于</option>
							<option value=">=">大于等于</option>
							<option value="<">小于</option>
							<option value="<=">小于等于</option>
						</select>
					</div>
					<label approvalConditionType="0" class="layui-form-label">
						<span style="color: red;">*</span>
						字段值
					</label>
					<div approvalConditionType="0" class="layui-input-inline">
						<input type="text" lay-verify="required|limit" readonly="readonly" limit="1000" approvalConditionBusinessValue class="layui-input" lay-filter="approvalConditionSingle" />
					</div>
					<label approvalConditionType="1" class="layui-form-label layui-hide"> 表达式 </label>
					<div approvalConditionType="1" class="layui-input-block layui-hide">
						<textarea name="approvalConditionConfig" class="layui-textarea" readonly="readonly"></textarea>
					</div>
				</div>
				<fieldset class="layui-hide">
                    <legend>审批权限</legend>
                </fieldset>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						节点类型
					</label>
					<div class="layui-input-block layui-form" lay-filter="approvalFlowNodeTypeDiv" lay-verify="checkboxRequired" title="节点类型">
						<input type="radio" title="普通节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="0" class="layui-input" checked />
						<input type="radio" title="会签节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="3" class="layui-input" />
						<input type="radio" title="循环节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="2" class="layui-input" />
						<input type="radio" title="指定审批节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="1" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item" >
					<label class="layui-form-label">
						<span style="color: #ff0000">*</span>
						改派原因
					</label>
					<div class="layui-input-block" >
						<textarea class="layui-textarea" name="reason" lay-verify="required">/</textarea>
					</div>
				</div>
			</form>
			<form class="layui-form" lay-filter="approvalFlowNodeData">
				<input id="approvalFlowNodeDataSaveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="" lay-submit lay-filter="saveApprovalFlowNodeData" />
				<div approvalFlowNodeType="0" class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						审批权限
					</label>
					<div class="layui-input-inline">
						<select name="approvalRight" lay-filter="approvalRight">
							<option value="1">审批科室</option>
							<option value="2">功能组织架构</option>
							<option value="3" checked>指定审批人</option>
						</select>
					</div>
					<label approvalRight="1" class="layui-form-label">
						<span style="color: red;">*</span>
						审批科室
					</label>
					<div approvalRight="1" class="layui-input-inline">
						<select name="approvalDeptID" lay-verify="required"></select>
					</div>
					<label approvalRight="3" class="layui-form-label layui-hide">
						<span style="color: red;">*</span>
						指定审批人
					</label>
					<div approvalRight="3" class="layui-input-inline layui-hide">
						<input type="text" name="approvalNames" class="layui-input" lay-verify="required" readonly="readonly" onclick="approvalBelongFlowNodeEdit.toSelectApprovalUser(this);" />
						<input type="hidden" name="approvalUIDs"  />
					</div>
				</div>
				<div approvalFlowNodeType="0" approvalRight="2" class="layui-form-item layui-hide">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						功能点
					</label>
					<div class="layui-input-inline">
						<select name="approvalFunCode" lay-verify="required"></select>
					</div>
					<label class="layui-form-label">审批口径</label>
					<div class="layui-input-inline">
						<select name="customFieldCode" lay-filter="customFieldCode">
							<option value="">接口（默认上报人科室）</option>
						</select>
					</div>
				</div>
				<div approvalFlowNodeType="3" class="layui-form-item layui-hide">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						指定会签人员
					</label>
					<div class="layui-input-inline">
						<input type="text" name="approvalNames" class="layui-input" lay-verify="required" readonly="readonly" onclick="approvalBelongFlowNodeEdit.toSelectApprovalUser(this);" />
						<input type="hidden" name="approvalUIDs"  />
					</div>
				</div>
			</form>
		</div>
	</div>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/approvalBelongFlowNodeEdit.js"></script>
<script type="text/javascript">
	$(function() {
		approvalBelongFlowNodeEdit.init();
	});
</script>