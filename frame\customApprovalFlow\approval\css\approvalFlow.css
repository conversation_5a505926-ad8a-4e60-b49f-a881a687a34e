div.flow-container {
	position: absolute;
	right: 10px;
	top: 10px;
	bottom: 0px;
	width: 250px;
}

.right_item, .item_finished, .item_unfinished {
	padding: 10px 10px 5px;
	position: relative;
	text-align: center;
	cursor: pointer;
	background: #f2f2f2;
	border: 1px solid #d0d0d0;
	border-radius: 3px;
}

.item_green h3 {
	color: #009688;
	font-size: 16px;
	text-align: left;
}

.item_red h3 {
	color: red;
	font-size: 16px;
	text-align: left;
}

.item_gray h3 {
	color: gray;
	font-size: 16px;
	text-align: left;
}

.item_red h3 {
	color: #ee4f4f;
	font-size: 16px;
	text-align: left;
}

.item_gray h3 {
	color: #A0A0A0;
	font-size: 16px;
	text-align: left;
}

.item_yellow h3 {
	color: #D75C07;
	font-size: 16px;
	text-align: left;
}

.item_content {
	display: block;
	color: #000;
	text-align: left;
}

.i_current {
	color: #D75C07;
}

.i_finish {
	color: #1AA194;
}

.back {
	position: absolute;
	top: 60%;
	left: -2px;
	margin-top: -27px;
	width: 14px;
	font-size: 14px;
	z-index: 1000;
	color: #f00;
}

.right .i_back {
	width: 14px;
	height: 14px;
	background: url('../images/back2.png') no-repeat;
}

.back_info {
	margin-left: 4px;
}

.back_info ul {
	font-size: 14px;
	margin-left: 45px;
}

.lr_box .right .back_info h3 {
	background: none;
	color: #009688;
	margin-bottom: 10px;
}

.back_info li {
	font-size: 14px;
	color: #000;
	padding: 2px 0px;
}

.right .back_info li span {
	background: none;
	color: #000;
}

.layui-elem-quote {
    padding: 5px;
    margin-bottom: 0px;
}

.flow-current-approver-div {
	max-height: 250px; 
	overflow : auto;
	text-align: left;
}

.flow-container-btn{
	padding: 5px;
	position: relative;
}

.flow-container-btn:first-child{
	margin-left:20px;
}

.flow-container-btn button{
	width: 27%;
	text-align: center;
	padding: 0;
}

.flow-move {
	position: relative;
	z-index: 999;
}

.loop-node {
	border-right: 1px solid;
	border-top: 1px solid;
	border-bottom: 1px solid;
	width: 20px;
	position: absolute;
	right: -22px;
	top: 10px;
	color: #BEBEBE;
	bottom: 50px;
}

.loop-node:before {
	content: '←';
	position: absolute;
	top: -13px;
	font-size: 20px;
}

.loop-node:after {
	content: '循环';
	position: absolute;
	top: 20px;
	left:15px;
	font-size: 12px;
	background-color: white;
}

ul.flow-container-ul {
	position: absolute;
	top: 40px;
	left: 5px;
	bottom: 0px;
	right: 30px;
}

ul.flow-container-ul-not-button {
	top: 10px;
}

li.flow-log-right_text {
    list-style-type: none!important;
}

div.flow-log-more {
    color: #009688;
    cursor: pointer;
    margin-top: 15px;
    padding-left: 15px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.flow-log-more i {
    display: inline-block;
    transition: all .3s;
}

.flow-log-more-click i {
    -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=-1, M12=1.2246467991473532e-16, M21=-1.2246467991473532e-16, M22=-1, SizingMethod='auto expand')";
    -moz-transform: rotate(180deg) translateY(2px);
    -o-transform: rotate(180deg) translateY(2px);
    -webkit-transform: rotate(180deg) translateY(2px);
    transform: rotate(180deg) translateY(2px);
}

.layui-colla-title{
	background: #dbf0e6;
}

.tright{
	text-align: right;
}

.tleft{
	text-align: left;
}

.layui-table {
    margin: 0;
}

.layui-colla-content {
	padding: 0px;
}