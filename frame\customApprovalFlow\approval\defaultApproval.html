<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>填写意见</title>
<link rel="stylesheet" type="text/css" href="../../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../../plugins/static/css/edit.css" />
<link rel="stylesheet" type="text/css" href="css/defaultApproval.css?version=1.0.1.0" />
</head>
<body class="layui-hide">
	<form id="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="approvalBelongCode" />
		<input type="hidden" name="approvalType" value="0"/>
		<input type="hidden" name="approvalBelongFlowNodeCode" />
		<input type="hidden" name="funCode" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="compNo" />
		<input type="hidden" name="saveFlag" value="0" />
		<input type="hidden" name="inLoop" />
		<input type="hidden" name="isDefault" value="0"/>
		<input type="hidden" name="approvalBelongFlowNodeRecordDraftCode" />
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2">&#xe8af;</i>
				<span>审批</span>
			</span>
			<div class="head0_right fr">
				<label dynamic class="layui-form-label layui-hide">
					<span style="color: #ff0000">*</span>
					指定审批人
				</label>
				<div dynamic class="layui-input-inline layui-hide">
					<input type="text" class="layui-input" readonly="readonly" onclick="defaultApproval.toSelectApprovalUser(this);"/>
					<input type="hidden" name="approvalFlowNodeData"  />
				</div>
				<input type="button" class="layui-btn layui-btn-sm" value="保存草稿" onclick="defaultApproval.save(2);">
				<input type="button" class="layui-btn layui-btn-sm" value="提交"   lay-submit="" lay-filter="save">
				<input btn="inLoop" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束循环" onclick="defaultApproval.inLoopOnClick(this);">
				<input type="button" class="layui-btn layui-btn-sm layui-hide" lay-submit="" lay-filter="saveLoop">
				<input btn="countersign" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束会签" lay-submit="" lay-filter="saveCountersign">
				<input btn="finish" type="button" class="layui-btn layui-btn-sm layui-hide" value="一键结束" lay-submit="" lay-filter="saveFinish">
				<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()">
			</div>
		</div>
		<div class="bodys">
			<div class="layui-form-item" approvalContent>
				<label id="approvalContentTitle" class="layui-form-label">
					<span style="color: #ff0000">*</span>
					审批意见
				</label>
				<div class="layui-input-inline" style="width: 80%;">
					<textarea cols="80" rows="3" class="layui-textarea" id="approvalContent" name="approvalContent" lay-verify="editorRequired|checkHasTable" style="height: 300px;"></textarea>
				</div>
			</div>
			<div id="stateStatusNo" class="layui-form-item layui-hide">
				<label class="layui-form-label">
					表单状态流转
				</label>
				<div class="layui-input-inline layui-form" lay-filter="stateStatusNoDiv">
					<select name="stateStatusNo">
						<option value="-999">默认</option>
					</select>
				</div>
			</div>
			<div id="copy" class="layui-form-item">
				<label class="layui-form-label">
					抄送
				</label>
				<div class="layui-input-inline" style="width: 80%;">
					<input type="text" name="copyUserNames" class="layui-input" readonly="readonly" onclick="defaultApproval.toSelectCopyUser(this);" />
					<input type="hidden" name="copyUserCodes" />
				</div>
			</div>
			<div class="layui-form-item" uploader>
				<label class="layui-form-label"> 附件上传 </label>
				<div class="layui-input-inline" style="width: 520px;">
					<button type="button" class="layui-btn layui-btn-sm" onclick="pubUploader.openFiles(defaultApproval.attaCallback)">点击上传</button>
					<textarea id="uploadEditor" style="display: none;"></textarea>
				</div>
			</div>
			<div class="layui-form-item" uploader>
				<label class="layui-form-label"> 附件列表 </label>
				<div class="layui-input-inline" style="width: 80%;">
					<blockquote id="ueditorFileDiv" class="layui-elem-quote layui-quote-nm" style="min-width: 200px; width: auto; margin-bottom: 0px;">
						<p>无</p>
					</blockquote>
				</div>
			</div>
			<div class="layui-form-item layui-hide">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline" style="width: 400px;">
					<input type="button" class="layui-btn layui-btn-sm" value="保存草稿" onclick="defaultApproval.save(2);">
					<input type="button" class="layui-btn layui-btn-sm" value="提交" lay-submit="" lay-filter="save">
					<input btn="inLoop" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束循环" onclick="defaultApproval.inLoopOnClick(this);">
					<input type="button" class="layui-btn layui-btn-sm layui-hide" lay-submit="" lay-filter="saveLoop">
					<input btn="countersign" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束会签" lay-submit="" lay-filter="saveCountersign">
					<input btn="finish" type="button" class="layui-btn layui-btn-sm layui-hide" value="一键结束" lay-submit="" lay-filter="saveFinish">
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()">
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../../plugins/fileUpload/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../../plugins/fileUpload/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../../plugins/fileUpload/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../../plugins/fileUpload/pubUploader.js?version=*******"></script>
<script type="text/javascript" charset="utf-8" src="js/defaultApproval.js?version=*******"></script>
<script type="text/javascript" charset="utf-8">
	$(function(){
		defaultApproval.init();
	});
</script>
</html>
