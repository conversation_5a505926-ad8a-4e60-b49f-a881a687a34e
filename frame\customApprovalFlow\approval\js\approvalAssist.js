var approvalAssist = {
	filterValue : [],
	init : function() {
		approvalAssist.approvalAssistInit();
		approvalAssist.initLayui();
	},
	approvalAssistInit : function() {
		return $.ajax({
			url : basePath + "frame/approvalFlowRecord/approvalAssistInit.spring",
			dataType : "json",
			data : {
				approvalBelongCode : param.get("approvalBelongCode"),
				funCode : param.get("funCode"),
				appCode : param.get("appCode"),
				approvalBelongFlowNodeCode : param.get("approvalBelongFlowNodeCode"),
			},
			success : function(data) {
				for (var i = 0; i < data.approverList.length; i++) {
					approvalAssist.filterValue.push(data.approverList[i].UID);
				}
			}
		});
	},
	initLayui : function() {
		layui.form.on("submit(save)", function() {
			approvalAssist.saveAssistingPeople();
			return false;
		});
		layui.form.render();
	},
	toSelectApprovalUser : function(obj) {
		window.__multipleSelectParam = {
			placeholder : "用户名称",
			URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
			param : {
				funCode : param.get("funCode"),
				rightPoint : 1,
			},
			field : {
				name : "userName",
				value : "uID"
			},
			filterValue : approvalAssist.filterValue,
			parseData : function(data) {
				return data.users;
			},
			values : (function(){
				var values = [];
				if(param.get("assistUID").length !=0){
					var nameArr = param.get("assistUserName").split(",");
					var valueArr = param.get("assistUID").split(",");
					for(var i = 0 ;i < valueArr.length;i++){
						var rightObj = {
								name : nameArr[i],
								value: valueArr[i]
						}
						values.push(rightObj)
					}
				}
				return values;
			})(),
			callback : function(data) {
				var names = [];
				var values = [];
				for (var i = 0; i < data.length; i++) {
					names.push(data[i].name);
					values.push(data[i].value);
				}
				obj.value = names.join(",");
				$(obj).next().val(values.join(","));
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '800px', '800px' ],
			title : "选择用户",
			scrollbar : false,
			content : basePath + "plugins/components/multipleSelect/multipleSelect.html"
		});
		
	},
	saveAssistingPeople : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		var url = basePath + "frame/approvalFlowRecord/saveAssistingPeople.spring";
		$.ajax({
			url : url,
			type : "post",
			data : param.__form(),
			dataType : "json",
			success : function(data) {
				assemblys.msg('提交成功', function() {
					parent.location.reload();
					assemblys.closeWindow();
				});
			}
		});
	},
}