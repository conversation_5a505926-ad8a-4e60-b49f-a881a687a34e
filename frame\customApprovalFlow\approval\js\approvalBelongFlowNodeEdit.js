var approvalBelongFlowNodeEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			return approvalBelongFlowNodeEdit.getDeptList();
		}).then(function() {
			return approvalBelongFlowNodeEdit.getCustomApprovalRightFunList();
		}).then(function() {
			return approvalBelongFlowNodeEdit.getCustomFormDeptFieldList();
		}).then(function() {
			$("span[approvalFlowName]").text("改派");
			// 先绑定方法再回显
			approvalBelongFlowNodeEdit.initLayui();
			approvalBelongFlowNodeEdit.initApprovalCondition();
			return approvalBelongFlowNodeEdit.getCustomApprovalFlowNode();
		}).then(function() {
			if (param.get("seqNo") == 0) {
				$("input[name=approvalFlowNodeType][value=1],input[name=approvalFlowNodeType][value=2]").attr("disabled", "disabled");
				layui.form.render("radio", "approvalFlowNodeTypeDiv");
			}
		});
	},
	initLayui : function() {
		
		layui.form.on("select(approvalRight)", function(data) {
			$("[approvalRight]").addClass("layui-hide");
			$("[approvalRight='" + data.value + "']").removeClass("layui-hide");
		});
		
		layui.form.on("select(approvalCondition)", function(data) {
			
			if (data.value) {
				$("[approvalCondition]").removeClass("layui-hide");
			} else {
				$("[approvalCondition]").addClass("layui-hide");
			}
			
		});
		
		layui.form.on("select(approvalConditionType)", function(data) {
			$("[approvalConditionType]").addClass("layui-hide");
			$("[approvalConditionType='" + data.value + "']").removeClass("layui-hide");
		});
		
		layui.form.on("radio(approvalFlowNodeType)", function(data) {
			$("[approvalFlowNodeType]").addClass("layui-hide");
			$("[approvalFlowNodeType='" + data.value + "']").removeClass("layui-hide");
			
			// 点击普通节点时，会显示错误
			if (data.value == 0) {
				$("[approvalRight]").addClass("layui-hide");
				$("[approvalRight='" + $("select[name='approvalRight']").val() + "']").removeClass("layui-hide");
			}
		});
		
		layui.form.on("submit(saveParam)", function() {
			$("#approvalFlowNodeDataSaveBtn").click();
			return false;
		});
		
		layui.form.on("submit(saveApprovalFlowNodeData)", function() {
			approvalBelongFlowNodeEdit.saveCustomApprovalFlowNode();
			return false;
		});
		
		layui.form.on("select(approvalConditionSingle)", function(data) {
			approvalBelongFlowNodeEdit.handleApprovalConditionConfig();
		});
		
		layui.form.render();
	},
	initApprovalCondition : function() {
		$("input[lay-filter='approvalConditionSingle']").change(function(e) {
			approvalBelongFlowNodeEdit.handleApprovalConditionConfig();
		});
	},
	handleApprovalConditionConfig : function() {
		var approvalConditionBusinessCode = $("input[approvalConditionBusinessCode]").val();
		var approvalConditionSymbol = $("select[approvalConditionSymbol]").val();
		var approvalConditionBusinessValue = $("input[approvalConditionBusinessValue]").val();
		param.set("approvalConditionConfig", "$[" + approvalConditionBusinessCode + "] " + approvalConditionSymbol + " ${" + approvalConditionBusinessValue + "}");
	},
	getDeptList : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			data : {
				compNo : param.get("compNo")
			},
			skipDataCheck : true,
			success : function(data) {
				var deptList = data.deptList;
				var $select = $("select[name='approvalDeptID']");
				var html = "";
				for (var i = 0; i < deptList.length; i++) {
					html += '<option value="' + deptList[i].DeptID + '">' + deptList[i].DeptName + '</option>';
				}
				$select.append(html);
			}
		});
	},
	getCustomApprovalRightFunList : function() {
		return $.ajax({
			url : basePath + "frame/appFunsSet/getCustomApprovalRightFunList.spring",
			data : {
				appCode : param.get("appCode")
			},
			success : function(data) {
				var funList = data.funList;
				var $select = $("select[name='approvalFunCode']");
				var html = "";
				for (var i = 0; i < funList.length; i++) {
					html += '<option value="' + funList[i].funCode + '">' + funList[i].funName + '</option>';
				}
				$select.append(html);
			}
		});
	},
	getCustomFormDeptFieldList : function() {
		return $.ajax({
			url : basePath + "frame/approvalFlow/getDeptFieldList.spring",
			data : {
				appCode : param.get("appCode"),
				approvalBelongCode : param.get("approvalBelongCode"),
			},
			success : function(data) {
				var deptFieldList = data.deptFieldList;
				var $select = $("select[name='customFieldCode']");
				var html = "";
				for (var i = 0; i < deptFieldList.length; i++) {
					html += '<option value="' + deptFieldList[i].value + '">' + deptFieldList[i].name + '</option>';
				}
				$select.append(html);
			}
		});
	},
	submit : function() {
		// 处理隐藏的表单数据，不提交到后台
		$("input[type=hidden],input[type=text],textarea,select").parents("div.layui-input-inline:hidden").find("input[type=hidden],input[type=text],textarea,select").each(function(i, e) {
			$(this).attr("lay-verify-temp", $(this).attr("lay-verify")).removeAttr("lay-verify");
		}).attr("disabled", "disabled");
		
		// 1秒后还原，处理数据应该不会超过一秒，因为有很多情况需要还原，例如必填项校验没有通过，这种情况没有回调不能手动还原数据，所以这里用了定时执行
		setTimeout(function() {
			// 已经提交了,可以还原回来，不管是否成功提交
			$("input[type=hidden],input[type=text],textarea,select").parents("div.layui-input-inline:hidden").find("input[type=hidden],input[type=text],textarea,select").each(function(i, e) {
				$(this).attr("lay-verify", $(this).attr("lay-verify-temp")).removeAttr("lay-verify-temp");
			}).removeAttr("disabled");
		}, 1000);
		$('#paramSaveBtn').click();
	},
	getCustomApprovalFlowNode : function() {
		return $.ajax({
			url : basePath + "frame/approvalFlow/getApprovalBelongFlowNode.spring",
			data : {
				approvalBelongFlowNodeID : param.get("approvalBelongFlowNodeID"),
				appCode : param.get("appCode")
			},
			success : function(data) {
				if (data.approvalBelongFlowNode) {
					
					$("input[name=approvalFlowNodeType][value=1],input[name=approvalFlowNodeType][value=2]").attr("disabled", "disabled");
					layui.form.render("radio", "approvalFlowNodeTypeDiv");
					
					param.set(null, data.approvalBelongFlowNode);
					approvalBelongFlowNodeEdit.getCustomForm(data.approvalBelongFlowNode.approvalCustomFormCode);
					
					// 回显单条件的字段业务编号和字段值，这些数据是自动合成表达式保存的，数据库没有相应字段保存
					if (data.approvalBelongFlowNode.approvalConditionType == 0 && data.approvalBelongFlowNode.approvalConditionConfig) {
						var approvalConditionBusinessCodeAry = data.approvalBelongFlowNode.approvalConditionConfig.match(/\$\[.+\]/);
						if (approvalConditionBusinessCodeAry[0]) {
							$("input[approvalConditionBusinessCode]").val(approvalConditionBusinessCodeAry[0].substr(2, approvalConditionBusinessCodeAry[0].length - 3));
						}
						
						var approvalConditionBusinessValueAry = data.approvalBelongFlowNode.approvalConditionConfig.match(/\$\{.+\}/);
						if (approvalConditionBusinessValueAry[0]) {
							$("input[approvalConditionBusinessValue]").val(approvalConditionBusinessValueAry[0].substr(2, approvalConditionBusinessValueAry[0].length - 3));
						}
						
						var approvalConditionSymbol = data.approvalBelongFlowNode.approvalConditionConfig.replace(approvalConditionBusinessCodeAry[0], "").replace(approvalConditionBusinessValueAry[0], "").trim();
						$("select[approvalConditionSymbol]").next().find("dd[lay-value='" + approvalConditionSymbol + "']").click();
					}
					
					// 这里如果approvalFlowNodeData数据不是正确的json格式可能会出错，但不影响功能，只是不会回显，暂时不做特殊处理
					var approvalFlowNodeDataObj = JSON.parse(data.approvalBelongFlowNode.approvalFlowNodeData);
					approvalFlowNodeDataObj.approvalRight = 3;
					approvalFlowNodeData.set(null, approvalFlowNodeDataObj);
					
					if (approvalFlowNodeDataObj.approvalUIDs) {
						var approvalUIDs = [];
						if (data.approvalBelongFlowNode.approvalFlowNodeType == 0) {
							approvalUIDs.push(approvalFlowNodeDataObj.approvalUIDs);
						} else {
							approvalUIDs.push("");// 存在两个approvalUIDs，回显需要处理一下，如果是会签节点，需要回显到第二个approvalUIDs
							approvalUIDs.push(approvalFlowNodeDataObj.approvalUIDs);
						}
						approvalFlowNodeData.set("approvalUIDs", approvalUIDs);
						approvalBelongFlowNodeEdit.getApprovalUsers(approvalFlowNodeDataObj.approvalUIDs);
					}
					
					// 回显完成后，把基础信息和审批条件只读和禁用，只允许修改审批权限
					param.__$.find("select").attr("disabled", "disabled");
					layui.form.render("select", "param");
				}
			}
		});
	},
	saveCustomApprovalFlowNode : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		return $.ajax({
			url : basePath + "frame/approvalFlow/saveApprovalBelongFlowNode.spring",
			data : param.__form() + "&approvalFlowNodeData=" + encodeURIComponent(JSON.stringify(approvalFlowNodeData.__json())),
			type : "post",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					parent.location.reload();
					assemblys.closeWindow();
				});
			}
		});
	},
	getCustomForm : function(customFormCode) {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomForm.spring",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : param.get("appCode")
			},
			success : function(data) {
				if (!data.customForm) {
					data.customForm = {
						customFormName : ""
					}
				}
				param.set("approvalCustomFormName", data.customForm.customFormName);
			}
		});
	},
	getApprovalUsers : function(approvalUIDs) {
		return $.ajax({
			url : basePath + "frame/useraction/getUserListByUIDs.spring",
			data : {
				"UIDs" : approvalUIDs
			},
			success : function(data) {
				var names = [];
				for (var i = 0; i < data.userList.length; i++) {
					names.push(data.userList[i].userName);
				}
				$("input[name=approvalUIDs]").prev("input:visible").val(names.join(","));
			}
		});
	},
	toSelectApprovalUser : function(obj) {
		window.__multipleSelectParam = {
			placeholder : "用户名称",
			URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
			param : {
				funCode : param.get("funCode"),
//				compNo : param.get("compNo"),
				rightPoint : 1,
			},
			field : {
				name : "userName",
				value : "uID"
			},
			parseData : function(data) {
				return data.users;
			},
			values : (function(){
				var values = [];
				var approvalUIDs = $("input[name='approvalNames']:visible").next().val();
				if(approvalUIDs){
					var nameArr = $("input[name='approvalNames']:visible").val().split(",");
					var valueArr = approvalUIDs.split(",");
					for(var i = 0 ;i < valueArr.length;i++){
						var rightObj = {
								name : nameArr[i],
								value: valueArr[i]
						}
						values.push(rightObj)
					}
				}
				return values;
			})(),
			callback : function(data) {
				var names = [];
				var values = [];
				for (var i = 0; i < data.length; i++) {
					names.push(data[i].name);
					values.push(data[i].value);
				}
				obj.value = names.join(",");
				$(obj).next().val(values.join(","));
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toSelectApprovalCustomFormCode",
			area : [ '800px', '800px' ],
			title : "选择用户",
			scrollbar : false,
			content : basePath + "plugins/components/multipleSelect/multipleSelect.html"
		});
		
	},
	toSelectApprovalCustomFormCode : function(obj) {
		window.__singleSelectParam = {
			parentName : "表单分类",
			parentURL : basePath + "frame/customFormType/getCustomFormTypeList.spring",
			parentField : {
				name : "customFormTypeName",
				value : "customFormTypeCode"
			},
			parentParam : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
			},
			parentParseData : function(data) {
				return data.customFormTypeList;
			},
			placeholder : "表单名称",
			URL : basePath + "frame/newCustomForm/getCustomFormList.spring",
			param : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
				customFormClass : "1"
			},
			field : {
				name : "customFormName",
				value : "customFormCode"
			},
			parseData : function(data) {
				return data.customFormList;
			},
			callback : function(name, value) {
				obj.value = name;
				$(obj).next().val(value);
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toSelectApprovalCustomFormCode",
			area : [ '500px', '900px' ],
			title : "选择表单",
			scrollbar : false,
			content : basePath + "plugins/components/singleSelect/singleSelect.html"
		});
		
	},
	clearApprovalCustomFormValue : function() {
		param.set("approvalCustomFormCode", "");
		param.set("approvalCustomFormName", "");
	}
}