;
(function(window) {
	var approvalFlow = {
		basePath : "",
		approvalBelongCode : "",
		currentApprovalBelongFlowNode : null,
		approvalBelongFlowNodeList : [],
		loopNode : null,
		param : {},
		hasStateFlow : true,
		initFlow : function(param) {
			//流程初始化前回调
			if (param.initFlowBeforeCallback) {
				if (typeof param.initFlowBeforeCallback == "function") {
					// 父窗口、当前窗口、参数
					param.initFlowBeforeCallback.call(parent.window, window, param);
				}
			}
			
			approvalFlow.param = param;
			var basePath = approvalFlow.basePath = param.basePath || window.basePath;
			var appCode = approvalFlow.appCode = param.appCode;
			var funCode = approvalFlow.funCode = param.funCode;
			var compNo = approvalFlow.compNo = param.compNo;
			var approvalBelongCode = approvalFlow.approvalBelongCode = param.approvalBelongCode;
			var selector = approvalFlow.selector = param.selector;
			return approvalFlow.getApprovalBelongFlowNodeList(appCode, basePath, approvalBelongCode, funCode, param).then(function(ul) {
				var div = approvalFlow.getFlowContainerBtnDiv(param);
				div.push(ul);
				//流程初始化后回调
				if(param.initFlowAfterCallback){
					if (typeof param.initFlowAfterCallback == "function") {
						// 父窗口、当前窗口、参数
						param.initFlowAfterCallback.call(parent.window, window,param, ul.children.length > 0 ? divElement : null);
					}
				}
				var divElement = assemblys.createElement(div, $(selector).empty()[0]);
				approvalFlow.handleLoopNode();
				approvalFlow.initLayui(param);
				return ul.children.length > 0 ? divElement : null;
			});
		},
		// 处理循环节点指示器
		handleLoopNode : function() {
			// 循环节点处理
			if (approvalFlow.loopNode) {
				var $beginNode = null;
				var beginIndex = -1;
				var endIndex = -1;
				var approvalFlowNodeData = JSON.parse(approvalFlow.loopNode.approvalFlowNodeData);
				var $flowMove = $("div.flow-move");
				for (var i = 0; i < approvalFlow.approvalBelongFlowNodeList.length; i++) {
					// 找出开始节点
					if (approvalFlowNodeData.beginNode == approvalFlow.approvalBelongFlowNodeList[i].approvalBelongFlowNodeCode) {
						$beginNode = $flowMove.eq(i);
						$beginNode.append('<div class="loop-node"></div>');
						beginIndex = i;
					}
					
					// 开始节点后的节点全部放到开始节点div.flow-move内
					if (i > beginIndex && beginIndex > -1) {
						var $thisFlowMove = $("div.flow-move[approvalBelongFlowNodeCode='" + approvalFlow.approvalBelongFlowNodeList[i].approvalBelongFlowNodeCode + "']");
						$beginNode.append($thisFlowMove.children());
						$thisFlowMove.remove();
					}
					
					if (approvalFlow.loopNode == approvalFlow.approvalBelongFlowNodeList[i]) {
						endIndex = i;
						break;
					}
				}
				
				approvalFlow.beginIndex = beginIndex;
				approvalFlow.endIndex = endIndex;
				
				// 隐藏最后一个节点的日志
				$beginNode.find("li.layui-timeline-item:last").find("div.flow-log-div").addClass("layui-hide");
				var $loopNode = $beginNode.find("div.loop-node");
				$loopNode.height(($beginNode.height() - 60) + "px");
				// 显示最后一个节点的日志
				$beginNode.find("li.layui-timeline-item:last").find("div.flow-log-div").removeClass("layui-hide");
				
				var $parent = $flowMove.parent();
				var top = 10;
				$flowMove.each(function(i, e) {
					if (i != beginIndex) {
						$parent.append($(this).children());
						$(this).remove();
					} else {
						$(this).append('<div class="loop-node"></div>');
						$parent.append($(this));
					}
				});
				$loopNode.css("top", top + "px");
			}
		},
		initLayui : function(param) {
			var dropdown = layui.dropdown;
			var btns = [];
			
			if (param.hasApproval && approvalFlow.hasExecRight && approvalFlow.currentApprovalBelongFlowNode.state != 2) {
				btns.push({
					title : '协助',
					type : 2
				});
				btns.push({
					title : '改派',
					type : 1,
				});
				
				btns.push({
					title : '更新流程',
					type : 4,
				});
			}
			
			if (param.hasApproval && approvalFlow.canAssist) {
				btns.push({
					title : '协助意见',
					type : 3,
				});
			}
			
			approvalFlow.btns = btns;
			if (btns.length == 0) {
				$("div.flow-container-btn button[other]").remove();
			} else {
				approvalFlow.dropdownRender(window);
			}
			
		},
		dropdownRender : function(w) {
			var param = approvalFlow.param;
			var btns = approvalFlow.btns;
			
			w.layui.dropdown.render({
				elem : 'button[other]',
				data : btns,
				click : function(obj) {
					if (obj.type == 1) {
						var node = approvalFlow.currentApprovalBelongFlowNode;
						var url = approvalFlow.basePath + "frame/customApprovalFlow/approval/approvalBelongFlowNodeEdit.html?funCode=" + approvalFlow.funCode;
						url += "&appCode=" + approvalFlow.appCode;
						url += "&compNo=" + approvalFlow.compNo;
						url += "&approvalBelongFlowNodeID=" + approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeID;
						url += "&approvalBelongCode=" + approvalFlow.currentApprovalBelongFlowNode.approvalBelongCode;
						layer.open({
							type : 2,
							skin : 'layui-layer-aems',
							closeBtn : 0,
							area : [ '900px', '500px' ],
							title : approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeName + " - 改派",
							maxmin : true,
							scrollbar : false,
							content : url
						});
					} else if (obj.type == 2) {
						layer.open({
							type : 2,
							skin : 'layui-layer-aems',
							closeBtn : 0,
							area : [ '850px', '500px' ],
							title : approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeName + " - 协助",
							maxmin : true,
							scrollbar : false,
							content : basePath + "frame/customApprovalFlow/approval/approvalAssist.html?appCode=" + approvalFlow.appCode + "&funCode=" + approvalFlow.funCode + "&approvalBelongCode=" + approvalFlow.approvalBelongCode + "&approvalBelongFlowNodeCode=" + approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode + "&compNo=" + approvalFlow.compNo
						});
					} else if (obj.type == 3) {
						approvalFlow.toApproval(1);
					} else if (obj.type == 4) {
						approvalFlow.updateApprovalFlow(param);
					}
				}
			});
		},
		updateApprovalFlow : function(param) {
			var basePath = param.basePath || window.basePath;
			var appCode = param.appCode;
			var approvalBelongCode = param.approvalBelongCode;
			assemblys.confirm("此操作不更新当前节点的审批人，会更新当前节点后面的节点数据，如需更改审批人请改派。确认更新当前流程吗？", function() {
				$.ajax({
					url : basePath + "/frame/approvalFlow/updateApprovalFlow.spring",
					data : {
						approvalBelongCode : approvalBelongCode,
						appCode : appCode,
					},
					type : "POST"
				}).then(function(data) {
					assemblys.msg("更新成功", function() {
						approvalFlow.initFlow(param);
					});
				});
			});
		},
		getFlowContainerBtnDiv : function(param) {
			
			if (!param.hasApproval) {
				return [];
			}
			
			if (!approvalFlow.currentApprovalBelongFlowNode && !approvalFlow.hasExecRight && !approvalFlow.canApproval && !approvalFlow.canAssist) {
				return [];
			}
			
			var btns = [];
			if (approvalFlow.canApproval) {
				btns.push({
					tagName : "button",
					type : "button",
					className : "layui-btn layui-btn-sm",
					innerText : "审批",
					onclick : function() {
						approvalFlow.toApproval();
					}
				});
				btns.push({
					tagName : "button",
					type : "button",
					className : "layui-btn layui-btn-sm",
					innerText : "回退",
					onclick : function() {
						approvalFlow.toRollback();
					}
				});
			}
			
			if (approvalFlow.hasExecRight || approvalFlow.canAssist) {
				btns.push({
					attr : {
						"other" : ""
					},
					tagName : "button",
					type : "button",
					className : "layui-btn layui-btn-sm",
					innerHTML : "操作&nbsp;",
					children : [ {
						tagName : "i",
						className : "layui-icon layui-icon-down",
					} ]
				});
			}
			var div = [ {
				tagName : "div",
				className : "flow-container-btn",
				children : btns
			} ];
			return div;
		},
		getApprovalBelongFlowNodeList : function(appCode, basePath, approvalBelongCode, funCode, param) {
			
			return $.ajax({
				url : basePath + "/frame/approvalFlow/getApprovalBelongFlowNodeList.spring",
				data : {
					approvalBelongCode : approvalBelongCode,
					appCode : appCode,
					funCode : funCode,
				}
			}).then(function(data) {
				approvalFlow.currentApprovalBelongFlowNode = data.currentApprovalBelongFlowNode;
				if (!approvalFlow.currentApprovalBelongFlowNode && data.approvalBelongFlowNodeList && data.approvalBelongFlowNodeList.length > 0) {
					approvalFlow.currentApprovalBelongFlowNode = data.approvalBelongFlowNodeList[data.approvalBelongFlowNodeList.length - 1];
				}
				if (data.currentApprovalBelongFlowNode) {
					var currentApprovalBelongFlowNodeCode = approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode;
					return $.ajax({
						url : basePath + "/frame/approvalFlow/initFlow.spring",
						data : {
							approvalBelongCode : approvalBelongCode,
							approvalBelongFlowNodeCode : currentApprovalBelongFlowNodeCode,
							appCode : appCode,
							funCode : funCode,
						}
					}).then(function(d) {
						for ( var key in data) {
							d[key] = data[key];
						}
						return d;
					});
				} else {
					return data;
				}
			}).then(function(data) {
				approvalFlow.approvalBelongFlowNodeList = data.approvalBelongFlowNodeList;
				approvalFlow.canApproval = data.canApproval;
				approvalFlow.canAssist = data.canAssist;
				approvalFlow.hasExecRight = data.hasExecRight;
				var elementData = [];
				for (var i = 0; i < data.approvalBelongFlowNodeList.length; i++) {
					elementData.push(approvalFlow.createFlow(data, data.approvalBelongFlowNodeList[i]));
				}
				
				var ul = {
					"tagName" : "ul",
					"className" : "flow-container-ul" + (!param.hasApproval || (!approvalFlow.currentApprovalBelongFlowNode && !approvalFlow.hasExecRight && !approvalFlow.canApproval && !approvalFlow.canAssist) ? " flow-container-ul-not-button" : ""),
					"children" : elementData
				}

				return ul;
			});
		},
		createFlow : function(data, approvalBelongFlowNode) {
			var approverList = data.approverList;
			var assistingPeopleList = data.assistingPeopleList;
			var approvalFlowNodeTypeMap = data.approvalFlowNodeTypeMap;
			var logMap = data.logMap;
			if (approvalBelongFlowNode.approvalFlowNodeType == 2) {
				approvalFlow.loopNode = approvalBelongFlowNode;
			}
			return {
				attr : {
					approvalBelongFlowNodeCode : approvalBelongFlowNode.approvalBelongFlowNodeCode
				},
				tagName : "div",
				className : "flow-move",
				children : [ {
					"tagName" : "li",
					"className" : "layui-timeline-item",
					"children" : [ approvalBelongFlowNode.current == 2 ? {
						"tagName" : "div",
						"className" : "back",
						"innerText" : "回退",
						"children" : [ {
							"tagName" : "div",
							"className" : "i_back"
						} ]
					} : null, {
						"tagName" : "i",
						"className" : (function() {
							var className = "layui-icon layui-timeline-axis ";
							if (approvalBelongFlowNode.current == 1) {
								className += "i_current";
							} else if (approvalBelongFlowNode.state == 1 || approvalBelongFlowNode.state == -1) {
								className += "i_finish";
							}
							return className;
						})(),
						"innerHTML" : (function() {
							if (approvalBelongFlowNode.current == 1) {
								return "&#xe60e;";
							} else if (approvalBelongFlowNode.state == 1 || approvalBelongFlowNode.state == -1) {
								return "&#x1005;";
							} else {
								return "&#xe651;";
							}
						})()
					}, {
						"tagName" : "div",
						"className" : "layui-timeline-content layui-text",
						"children" : [ {
							"tagName" : "div",
							"className" : (function() {
								if (approvalBelongFlowNode.current == 1) {
									return "right_item skin-div-css  item_red";
								} else if (approvalBelongFlowNode.state == 1 || approvalBelongFlowNode.state == -1) {
									return "right_item skin-div-css  item_green";
								} else {
									return "right_item skin-div-css  item_gray";
								}
							})(),
							"children" : [ {
								"tagName" : "h3",
								"children" : [ {
									"tagName" : "span",
									"innerText" : approvalBelongFlowNode.approvalBelongFlowNodeName,
								}, {
									"tagName" : "span",
									"innerText" : " - ",
									"style" : {
										"fontSize" : "12px"
									}
								}, {
									"tagName" : "span",
									"innerText" : approvalFlowNodeTypeMap[approvalBelongFlowNode.approvalFlowNodeType] + "节点",
									"style" : {
										"fontSize" : "10px"
									}
								} ]
							}, {
								"tagName" : "span",
								"className" : "item_content",
								"innerText" : (function() {
									if (approvalBelongFlowNode.approvedRecordList) {
										var users = new Array();
										for (var i = 0; i < approvalBelongFlowNode.approvedRecordList.length; i++) {
											users.push(approvalBelongFlowNode.approvedRecordList[i].deptName + " - " + approvalBelongFlowNode.approvedRecordList[i].userName);
										}
										return users.join("\n");
									}
									return "";
								})()
							}, approvalBelongFlowNode.current == 1 && approvalBelongFlowNode.approvalIndex > 0 ? {
								"tagName" : "div",
								"className" : "flow-current-approver-div",
								"children" : [ {
									"tagName" : "blockquote",
									"className" : "layui-elem-quote ",
									"title" : "审批人名单",
									"style" : {
										"margin" : "5px 0px"
									},
									"children" : (function() {
										var userList = approverList;
										var userHtml = [];
										if (userList && userList.length > 0) {
											for (var i = 0; i < userList.length; i++) {
												userHtml.push({
													"tagName" : "span",
													"className" : "item_content",
													"innerHTML" : (userList[i].deptName + " - " + userList[i].userName),
												});
											}
										}
										
										// 未审协助人
										if (assistingPeopleList && assistingPeopleList.length > 0) {
											for (var i = 0; i < assistingPeopleList.length; i++) {
												userHtml.push({
													"tagName" : "span",
													"className" : "item_content",
													"innerHTML" : (assistingPeopleList[i].DeptName + " - " + assistingPeopleList[i].UserName),
												});
											}
										}
										
										return userHtml;
									})()
								} ]
							} : null ]
						}, {
							tagName : "div",
							className : "flow-log-div",
							style : {
								"text-align" : "left"
							},
							children : (function() {
								var uls = []
								var logList = logMap[approvalBelongFlowNode.approvalIndex];
								if (logList) {
									for (var i = 0; i < logList.length; i++) {
										uls.push({
											tagName : "ul",
											className : i > 1 ? "layui-hide flow-log-moreul" : "",
											children : [ {
												tagName : "li",
												className : "right_data",
												innerText : assemblys.dateToStr(logList[i].createDate)
											}, {
												tagName : "li",
												className : "flow-log-right_text",
												innerText : logList[i].logContent
											} ]
										});
									}
								}
								
								return uls;
							})()
						}, logMap[approvalBelongFlowNode.approvalIndex] && logMap[approvalBelongFlowNode.approvalIndex].length > 2 ? {
							tagName : "div",
							className : "flow-log-more",
							onclick : function() {
								var $ul = $(this).prev().find(".flow-log-moreul");
								if ($(this).hasClass('flow-log-more-click')) {
									$(this).removeClass('flow-log-more-click');
									$ul.addClass("layui-hide");
								} else {
									$(this).addClass('flow-log-more-click');
									$ul.removeClass("layui-hide");
								}
								
								$("div.loop-node").css("top", ($("div.loop-node").prev().offset().top - 40) + "px")
							},
							children : [ {
								tagName : "span",
								innerText : "查看更多",
							}, {
								tagName : "i",
								className : "layui-icon",
								innerHTML : "&#xe625;",
							} ]
						} : null ]
					} ]
				} ]
			};
		},
		getApprovalBelongFlowNodeRecordList : function(param) {
			var basePath = param.basePath || window.basePath;
			var appCode = param.appCode;
			var approvalBelongCode = param.approvalBelongCode;
			var selector = param.selector;
			
			return $.ajax({
				url : basePath + "/frame/approvalFlowRecord/getApprovalBelongFlowNodeRecordList.spring",
				data : {
					approvalBelongCode : approvalBelongCode,
					appCode : appCode,
				}
			}).then(function(data) {
				var result = [];
				if (data.approvalBelongFlowNodeRecordList.length > 0) {
					for (var i = 0; i < data.approvalBelongFlowNodeRecordList.length; i++) {
						result.push(approvalFlow.createRecord(param, data.user, basePath, data.approvalBelongFlowNodeRecordList[i], i));
					}
				} else {
					result.push({
						"tagName" : "div",
						"className" : "layui-colla-item",
						"children" : [ {
							"tagName" : "h2",
							"className" : "layui-colla-title skin-div-css",
							"innerHTML" : "无审批记录"
						}, {
							"tagName" : "div",
							"className" : "layui-colla-content layui-show",
							"children" : [ {
								"tagName" : "table",
								"className" : "layui-table main_table detail_table",
								"style" : {
									"table-layout" : "fixed"
								},
								"children" : [ {
									"tagName" : "tr",
									"className" : "layui-table-tr",
									"children" : [ {
										"tagName" : "td",
										"style" : {
											"text-align" : "center"
										},
										"innerText" : "无审批记录"
									} ]
								} ]
							} ]
						} ]
					});
				}
				
				assemblys.createElement({
					tagName : "div",
					className : "layui-collapse",
					children : result
				}, $(selector)[0]);
				
				layui.element.render();
			});
		},
		createRecord : function(param, user, basePath, approvalBelongFlowNodeRecord, i) {
			var titleName = approvalBelongFlowNodeRecord.approvalBelongFlowNodeName + "：" + approvalBelongFlowNodeRecord.deptName + " - " + approvalBelongFlowNodeRecord.userName + " - " + assemblys.dateToStr(approvalBelongFlowNodeRecord.CreateDate);
			if (approvalBelongFlowNodeRecord.ApprovalType == 2) {
				titleName += "<font style='color: red;'>【回退】</font>";
			}
			
			if (!param.noEdit && user.uID == approvalBelongFlowNodeRecord.ApprovalUID) {
				titleName += '<button type="button" class="layui-btn layui-btn-xs" style="margin-left: 10px;" onclick="event.stopPropagation();approvalFlow.toApproval(' + approvalBelongFlowNodeRecord.ApprovalType + ',\'' + approvalBelongFlowNodeRecord.ApprovalBelongFlowNodeRecordCode + '\');">编辑</button>';
			}
			
			var approvalContentList = JSON.parse(approvalBelongFlowNodeRecord.ApprovalContent);
			return {
				"tagName" : "div",
				"className" : "layui-colla-item",
				"children" : [ {
					"tagName" : "h2",
					"className" : "layui-colla-title skin-div-css",
					"innerHTML" : titleName
				}, {
					"tagName" : "div",
					"className" : "layui-colla-content" + (i == 0 ? " layui-show" : ""),
					"children" : [ {
						"tagName" : "table",
						"className" : "layui-table main_table detail_table",
						"style" : {
							"table-layout" : "fixed"
						},
						"children" : (function() {
							var result = [];
							for (var i = 0; i < approvalContentList.length; i++) {
								result.push({
									"tagName" : "tr",
									"className" : "layui-table-tr",
									"children" : [ {
										"tagName" : "td",
										"className" : "tright",
										"attr" : {
											"colspan" : "1"
										},
										"innerText" : approvalContentList[i].name
									}, {
										"tagName" : "td",
										"className" : "tleft",
										"attr" : {
											"colspan" : "3"
										},
										"innerHTML" : approvalContentList[i].value
									} ]
								})
							}
							
							result.push({
								"tagName" : "tr",
								"className" : "layui-table-tr",
								"children" : [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerHTML" : "附件信息"
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"children" : (function() {
										var tdChildren = [];
										$.ajax({
											url : basePath + "frame/fileUpload/getAttachments.spring",
											data : {
												belongToCode : approvalBelongFlowNodeRecord.ApprovalBelongFlowNodeRecordCode
											},
											async : false,
											skipDataCheck : true,
											success : function(data) {
												if (data.result == "success") {
													if (data.attachmentsList.length > 0) {
														for (var i = 0; i < data.attachmentsList.length; i++) {
															// 拼装内容
															tdChildren.push({
																"tagName" : "div",
																"attr" : {
																	"title" : data.attachmentsList[i].attachmentName,
																	"onclick" : "approvalFlow.downLoadAttaPreview('" + data.attachmentsList[i].attachmentName + "','" + data.attachmentsList[i].attachmentURL + "');"
																},
																"style" : {
																	"cursor" : "pointer",
																	"padding" : "5px"
																},
																"children" : [ {
																	"tagName" : "i",
																	"className" : "layui-icon layui-icon-download-circle i_icon",
																	"attr" : {
																		"title" : "点击下载"
																	}
																}, {
																	"tagName" : "span",
																	"innerHTML" : data.attachmentsList[i].attachmentName
																} ]
															});
														}
													} else {
														tdChildren.push({
															"tagName" : "div",
															"innerHTML" : "无"
														});
													}
												}
											}
										})
										return tdChildren;
									})()
								} ]
							});
							
							return result;
						})()
					} ]
				} ]
			}
		},
		downLoadAttaPreview : function(eifAttaName, eifAttaUrl) {
			$("#downloadFileForm").remove();
			var form = "<form id='downloadFileForm' style='display:none;' ></form>";
			$("body").append(form);
			var url = basePath + "/frame/fileUpload/downloadFile.spring?eifAttaUrl=" + eifAttaUrl + "&eifAttaName=" + encodeURIComponent(eifAttaName);
			document.getElementById("downloadFileForm").action = url;
			document.getElementById("downloadFileForm").method = "post";
			document.getElementById("downloadFileForm").submit();
		},
		toApproval : function(approvalType, approvalBelongFlowNodeRecordCode) {
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				area : [ '90%', '90%' ],
				title : approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeName + " - 审批",
				maxmin : true,
				scrollbar : false,
				content : approvalFlow.basePath + "/frame/customApprovalFlow/approval/defaultApproval.html?funCode=" + approvalFlow.funCode + "&appCode=" + approvalFlow.appCode + "&approvalBelongCode=" + approvalFlow.approvalBelongCode + "&approvalBelongFlowNodeCode=" + approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode + "&approvalBelongFlowNodeRecordDraftCode=" + (approvalBelongFlowNodeRecordCode || "") + "&inLoop="
						+ (approvalFlow.beginIndex > -1 && approvalFlow.endIndex > -1 && approvalFlow.currentApprovalBelongFlowNode.approvalIndex >= approvalFlow.beginIndex && approvalFlow.currentApprovalBelongFlowNode.approvalIndex <= approvalFlow.endIndex ? 1 : 0) + (approvalType == 1 ? "&approvalType=1" : "") + "&compNo=" + approvalFlow.compNo
			});
		},
		toRollback : function() {
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				closeBtn : 0,
				area : [ '90%', '90%' ],
				title : approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeName + " - 回退",
				maxmin : true,
				scrollbar : false,
				content : approvalFlow.basePath + "/frame/customApprovalFlow/approval/rollback.html?appCode=" + approvalFlow.appCode + "&approvalBelongCode=" + approvalFlow.approvalBelongCode + "&approvalBelongFlowNodeCode=" + approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode + "&compNo=" + approvalFlow.compNo + "&funCode=" + approvalFlow.funCode
			});
		},
	}
	window.approvalFlow = approvalFlow;
})(window);