var defaultApproval = {
	// 文件访问地址
	baseImgPath : "",
	// 富文本框对象
	ue : null,
	// 本公司
	compNo : "",
	// 初始化
	defaultApprovalInit : function() {
		var approvalBelongFlowNodeRecordDraftCode = param.get("approvalBelongFlowNodeRecordDraftCode");
		$.ajax({
			url : basePath + "frame/approvalFlowRecord/defaultApprovalInit.spring",
			dataType : "json",
			data : {
				approvalBelongCode : param.get("approvalBelongCode"),
				funCode : param.get("funCode"),
				appCode : param.get("appCode"),
				approvalBelongFlowNodeCode : param.get("approvalBelongFlowNodeCode"),
				approvalBelongFlowNodeRecordDraftCode : approvalBelongFlowNodeRecordDraftCode,
			},
			success : function(data) {
				var customFormCode = data.editApprovalBelongFlowNode ? data.editApprovalBelongFlowNode.approvalCustomFormCode : data.approvalBelongFlowNode.approvalCustomFormCode;
				var approvalBelongFlowNodeCode = data.editApprovalBelongFlowNode ? data.editApprovalBelongFlowNode.approvalBelongFlowNodeCode : data.approvalBelongFlowNode.approvalBelongFlowNodeCode;
				if ((approvalBelongFlowNodeRecordDraftCode ? data.approvalBelongFlowNodeRecord.approvalFormBelongCode : data.approvalBelongFlowNode.approvalCustomFormCode) && param.get("approvalType") != 1) {
					location.href = basePath + "frame/customForm/customFormApprovalTemplate.html?submitType=1&customFormCode=" + customFormCode + "&appCode=" + param.get("appCode") + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&approvalBelongCode=" + param.get("approvalBelongCode") + "&approvalBelongFlowNodeCode=" + approvalBelongFlowNodeCode + "&inLoop=" + param.get("inLoop") + "&approvalBelongFlowNodeRecordDraftCode=" + approvalBelongFlowNodeRecordDraftCode;
					return;
				} else {
					$("body").removeClass("layui-hide");
				}
			
				//一键结束执行限 && 协助不显示按钮
				if(data.finishExecRight && param.get("approvalType") == 0){
					$("[btn=finish]").removeClass("layui-hide");
				}
				
				if (parent.approvalFlow.hasStateFlow && data.approvalBelongFlowNode.state != 2 && param.get("approvalType") == 0) {// 协助和回退审批不显示按钮
					$("#stateStatusNo").removeClass("layui-hide");
				}
				
				if (data.approvalBelongFlowNode.approvalFlowNodeType == 3 && param.get("approvalType") == 0) {// 协助不显示按钮
					$("[btn=countersign]").removeClass("layui-hide");
				}
				
				if (param.get("inLoop") == 1 && param.get("approvalType") != 1 && data.approvalBelongFlowNode.state != 2) {// 协助不显示按钮
					$("[btn=inLoop]").removeClass("layui-hide");
				}
				
				defaultApproval.baseImgPath = data.baseImgPath;
				defaultApproval.loadEditor();
				
				defaultApproval.initStatus(data);
				if (data.approvalBelongFlowNode.state != 2 && data.nextApprovalBelongFlowNode) {
					for (var i = 0; i < data.customFormTypeStateList.length; i++) {
						if (data.customFormTypeStateList[i].customFormTypeStateNo == data.nextApprovalBelongFlowNode.approvalNodeState) {
							param.set("stateStatusNo", data.nextApprovalBelongFlowNode.approvalNodeState || -999);
							break;
						}
					}
				}
				
				if (data.approvalBelongFlowNodeRecord) {
					param.set("copyUserNames", data.approvalBelongFlowNodeRecord.copyUserNames);
					param.set("copyUserCodes", data.approvalBelongFlowNodeRecord.copyUserCodes);
					if (data.isDefault) {
						param.set("approvalBelongFlowNodeRecordDraftCode", data.approvalBelongFlowNodeRecord.approvalBelongFlowNodeRecordCode);
					}
					defaultApproval.ue.ready(function() {
						defaultApproval.ue.setContent(JSON.parse(data.approvalBelongFlowNodeRecord.approvalContent)[0].value);
					});
				}
				
				if (data.attachments) {
					defaultApproval.attaCallback(data.attachments);
				}
			}
		});
	},
	initStatus : function(data) {
		var $select = $("select[name=stateStatusNo]");
		var html = "";
		for (var i = 0; i < data.customFormTypeStateList.length; i++) {
			html += '<option value="' + data.customFormTypeStateList[i].customFormTypeStateNo + '">' + data.customFormTypeStateList[i].customFormTypeStateName + '</option>';
		}
		$select.append(html);
		layui.form.render("select", "stateStatusNoDiv");
	},
	// 加载富文本信息
	loadEditor : function() {
		defaultApproval.ue = pubUploader.initEditor("approvalContent", true, 1);
	},
	// 文件回调 - 可回显复用
	attaCallback : function(data) {
		if($("#ueditorFileDiv").find("p").length > 0){
			$("#ueditorFileDiv").empty();
		}
		pubUploader.setFileList(data, "#ueditorFileDiv", defaultApproval.baseImgPath);
	},
	/**
	 * 必填校验
	 */
	loadChecks : function() {
		var form = layui.form;
		// 自定义表单校验
		form.verify({
			// 校验富文本
			editorRequired : function(value, item) { // value：表单的值、item：表单的DOM对象
				value = $.trim(value);
				if (!value) {
					$(item).parent().prev().children("span").text("");
					var text = $(item).parent().prev().text();
					$(item).parent().prev().children("span").text("*");
					return "请填写" + text;
				}
			},
			checkHasTable : function(value, item) { // value：表单的值、item：表单的DOM对象
				if (value && value.indexOf("</table>") != -1) {
					return "意见不能包含表格";
				}
			},
		});
		// 监听保存
		form.on("submit(save)", function(data) {
			if (param.get("approvalType") != 1) {
				defaultApproval.getNextApprovalBelongFlowNode();
			} else {
				defaultApproval.save();
			}
			return false;
		});
		
		// 监听结束循环
		form.on("submit(saveLoop)", function(data) {
			defaultApproval.getLoopEndNextNode();
			return false;
		});
		
		// 监听结束会签
		form.on("submit(saveCountersign)", function(data) {
			defaultApproval.getNextApprovalBelongFlowNode(1);
			return false;
		});
		
		// 监听一键结束
		form.on("submit(saveFinish)", function(data) {
			assemblys.confirm("确定一键结束审批吗？", function() {
				defaultApproval.save(3);
			});
			return false;
		});
		
		// 渲染
		form.render();
	},
	// 保存
	save : function(type) {
		if (type == 2) {
			param.set("isDefault", 1);
		}
		var url = basePath + "frame/approvalFlowRecord/saveApprovalBelongFlowNodeRecord.spring";
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		$.ajax({
			url : url,
			type : "post",
			data : param.__form() + "&type=" + (type || 0),
			dataType : "json",
			success : function(data) {
				assemblys.msg(type != 2 ? '提交成功' : "保存成功", function() {
					if (type != 2) {
						parent.location.reload();
						assemblys.closeWindow();
					} else {
						param.set("approvalBelongFlowNodeRecordDraftCode", data.approvalBelongFlowNodeRecordDraftCode);
					}
				});
			}
		});
		param.set("isDefault", 0);
	},
	edit : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		var url = basePath + "frame/approvalFlowRecord/editApprovalBelongFlowNodeRecord.spring";
		$.ajax({
			url : url,
			type : "post",
			data : param.__form(),
			dataType : "json",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					parent.location.reload();
					assemblys.closeWindow();
				});
			}
		});
	},
	inLoopOnClick : function(obj) {
		var layVerify = $("input[name=approvalFlowNodeData]").prev().attr("lay-verify");
		$("input[name=approvalFlowNodeData]").prev().removeAttr("lay-verify");
		$(obj).next().click();
		$("input[name=approvalFlowNodeData]").prev().attr("lay-verify", layVerify);
	},
	getLoopEndNextNode : function() {
		assemblys.confirm("确定结束循环吗？", function() {
			$.ajax({
				url : basePath + "frame/approvalFlowRecord/getLoopEndNextNode.spring",
				data : {
					approvalBelongCode : param.get("approvalBelongCode"),
					funCode : param.get("funCode"),
					appCode : param.get("appCode"),
				},
				dataType : "json",
				success : function(data) {
					var $input = $("input[name=approvalFlowNodeData]");
					if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
						defaultApproval.toSelectApprovalUser(function(data) {
							var values = [];
							for (var i = 0; i < data.length; i++) {
								values.push(data[i].value);
							}
							
							var approvalUIDs = values.join(",");
							var approvalFlowNodeData = {
								approvalRight : 3,
								approvalUIDs : approvalUIDs
							}

							var oldValue = $input.val();
							$input.val(JSON.stringify(approvalFlowNodeData));
							defaultApproval.saveLoop();
						});
					} else {
						$input.val("{}");
						defaultApproval.saveLoop();
					}
				}
			});
		}, function() {
		})
	},
	getNextApprovalBelongFlowNode : function(type) {
		$.ajax({
			url : basePath + "frame/approvalFlow/getNextApprovalBelongFlowNode.spring",
			data : {
				approvalBelongCode : param.get("approvalBelongCode"),
				appCode : param.get("appCode"),
			},
			dataType : "json",
			success : function(data) {
				var $input = $("input[name=approvalFlowNodeData]");
				if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
					defaultApproval.toSelectApprovalUser(function(data) {
						var values = [];
						for (var i = 0; i < data.length; i++) {
							values.push(data[i].value);
						}
						
						var approvalUIDs = values.join(",");
						var approvalFlowNodeData = {
							approvalRight : 3,
							approvalUIDs : approvalUIDs
						}

						var oldValue = $input.val();
						$input.val(JSON.stringify(approvalFlowNodeData));
						defaultApproval.save(type);
					});
				} else {
					$input.val("{}");
					defaultApproval.save(type);
				}
			}
		});
	},
	// 结束循环
	saveLoop : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		var url = basePath + "frame/approvalFlowRecord/saveLoopApprovalBelongFlowNodeRecord.spring";
		return $.ajax({
			url : url,
			type : "post",
			data : param.__form(),
			dataType : "json",
			success : function(data) {
				assemblys.msg('提交成功', function() {
					parent.location.reload();
					assemblys.closeWindow();
				});
			}
		});
	},
	toSelectApprovalUser : function(obj) {
		window.__multipleSelectParam = {
			placeholder : "用户名称",
			URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
			param : {
				funCode : param.get("funCode"),
				rightPoint : 1,
			},
			field : {
				name : "userName",
				value : "uID"
			},
			parseData : function(data) {
				return data.users;
			},
			callback : typeof obj === "function" ? obj : function(data) {
				var names = [];
				var values = [];
				for (var i = 0; i < data.length; i++) {
					names.push(data[i].name);
					values.push(data[i].value);
				}
				var n = names.join(",");
				obj.value = n;
				obj.title = n;
				
				var approvalUIDs = values.join(",");
				var approvalFlowNodeData = {
					approvalRight : 3,
					approvalUIDs : approvalUIDs
				}
				$(obj).next().val(JSON.stringify(approvalFlowNodeData));
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toSelectApprovalCustomFormCode",
			area : [ '800px', '800px' ],
			title : "选择用户",
			scrollbar : false,
			content : basePath + "plugins/components/multipleSelect/multipleSelect.html",
		});
		
	},
	toSelectCopyUser : function(obj) {
		
		window.__multipleSelectParam = {
			parentName : "科室",
			parentURL : basePath + "frame/common/getDeptListNew.spring",
			parentField : {
				name : "DeptName",
				value : "DeptID"
			},
			parentParam : {
				compNo : param.get("compNo"),
			},
			parentParseData : function(data) {
				return data.deptList;
			},
			placeholder : "用户名称",
			URL : basePath + "frame/common/getUserList.spring",
			param : {},
			field : {
				name : "userName",
				value : "uid"
			},
			parseData : function(data) {
				return data.userList;
			},
			values : (function(){
				var values = [];
				if(param.get("copyUserCodes").length !=0){
					var nameArr = param.get("copyUserNames").split(",");
					var valueArr = param.get("copyUserCodes").split(",");
					for(var i = 0 ;i < valueArr.length;i++){
						var rightObj = {
								name : nameArr[i],
								value: valueArr[i]
						}
						values.push(rightObj)
					}
				}
				return values;
			})(),
			callback : function(data) {
				var names = [];
				var values = [];
				for (var i = 0; i < data.length; i++) {
					names.push(data[i].name);
					values.push(data[i].value);
				}
				obj.value = names.join(",");
				$(obj).next().val(values.join(","));
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '800px', '800px' ],
			title : "选择用户",
			scrollbar : false,
			content : basePath + "plugins/components/multipleSelect/multipleSelect.html"
		});
		
	},
	init : function() {
		if (param.get("approvalBelongFlowNodeRecordDraftCode")) {
			$("input[value='保存草稿']").val("保存").attr("onclick", "defaultApproval.edit();").siblings("input").not("input[value='关闭']").remove();
			$("#stateStatusNo,#copy").hide();// 避免与其他地方冲突,不使用addClass("layui-hide")
		} /*else {
			if (param.get("approvalType") == 0) {// 协助不显示按钮
				$("[btn=finish]").removeClass("layui-hide");
			}
		}*/
		defaultApproval.defaultApprovalInit();
		defaultApproval.loadChecks();
		pubUploader.fileSpace = param.get("appCode");
	}
}