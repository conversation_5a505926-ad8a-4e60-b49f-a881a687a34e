var rollback = {
	// 文件访问地址
	baseImgPath : "",
	// 本公司
	compNo : "",
	// 初始化
	rollbackInit : function() {
		$.ajax({
			url : basePath + "frame/approvalFlowRecord/rollbackInit.spring",
			dataType : "json",
			data : {
				approvalBelongCode : param.get("approvalBelongCode"),
				appCode : param.get("appCode"),
				approvalBelongFlowNodeCode : param.get("approvalBelongFlowNodeCode"),
			},
			success : function(data) {
				rollback.baseImgPath = data.baseImgPath;
				rollback.loadEditor();
				
				/*if (data.attachments && data.attachments.length > 0) {
					for ( var i in data.attachments) {
						data.attachments[i].title = data.attachments[i].attachmentName;
						data.attachments[i].url = data.attachments[i].attachmentURL;
						data.attachments[i].size = data.attachments[i].attachmentSize;
						data.attachments[i].type = data.attachments[i].attachmentType;
					}
					$("#ueditorFileDiv").empty();
					pubUploader.setFileList(data.attachments, "#ueditorFileDiv");
				}*/
			}
		});
	},
	// 加载富文本信息
	loadEditor : function() {
		pubUploader.initEditor("approvalContent", true, 2);
	},
	// 文件回调 - 可回显复用
	attaCallback : function(data) {
		//$("#ueditorFileDiv").empty();
		pubUploader.setFileList(data, "#ueditorFileDiv", rollback.baseImgPath);
	},
	/**
	 * 必填校验
	 */
	loadChecks : function() {
		var form = layui.form;
		// 自定义表单校验
		form.verify({
			// 校验富文本
			editorRequired : function(value, item) { // value：表单的值、item：表单的DOM对象
				value = $.trim(value);
				if (!value) {
					$(item).parent().prev().children("span").text("");
					var text = $(item).parent().prev().text();
					$(item).parent().prev().children("span").text("*");
					return "请填写" + text;
				}
			},
			checkHasTable : function(value, item) { // value：表单的值、item：表单的DOM对象
				if (value && value.indexOf("</table>") != -1) {
					return "意见不能包含表格";
				}
			},
		});
		// 监听保存
		form.on("submit(save)", function(data) {
			rollback.save();
			return false;
		});
		// 渲染
		form.render();
	},
	// 保存
	save : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		var url = basePath + "frame/approvalFlowRecord/saveRollbackApprovalBelongFlowNodeRecord.spring";
		$.ajax({
			url : url,
			type : "post",
			data : param.__form(),
			dataType : "json",
			success : function(data) {
				assemblys.msg('回退成功', function() {
					parent.location.reload();
					assemblys.closeWindow();
				});
			}
		});
	},
	initRollbackNode : function() {
		var elementData = [];
		for (var i = 0; i < parent.approvalFlow.approvalBelongFlowNodeList.length; i++) {
			if (parent.approvalFlow.approvalBelongFlowNodeList[i].current == 1) {
				break;
			}
			
			if (parent.approvalFlow.approvalBelongFlowNodeList[i].state < 0 || parent.approvalFlow.approvalBelongFlowNodeList[i].approvalFlowNodeType == 2) {
				continue;
			}
			
			if (parent.approvalFlow.approvalBelongFlowNodeList[i].approvedRecordList) {
				var approvedRecordList = parent.approvalFlow.approvalBelongFlowNodeList[i].approvedRecordList[0];
				elementData.push({
					"attr" : {
						"lay-skin" : "primary"
					},
					"tagName" : "input",
					"name" : "rollbackApprovalBelongFlowNodeCode",
					"type" : "radio",
					"value" : parent.approvalFlow.approvalBelongFlowNodeList[i].approvalBelongFlowNodeCode,
					"title" : parent.approvalFlow.approvalBelongFlowNodeList[i].approvalBelongFlowNodeName + "&nbsp;&nbsp;&nbsp;---&nbsp;&nbsp;&nbsp;" + approvedRecordList.deptName + " - " + approvedRecordList.userName
				});
				
				elementData.push({
					"tagName" : "br",
				});
			}
		}
		
		assemblys.createElement(elementData, $("div[rollbackApprovalBelongFlowNodeCodeDiv]")[0]);
	},
	init : function() {
		rollback.initRollbackNode();
		rollback.rollbackInit();
		rollback.loadChecks();
		pubUploader.fileSpace = param.get("appCode");
	}
}