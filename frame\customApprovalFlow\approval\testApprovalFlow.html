<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>审批流程</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="css/approvalFlow.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
		</div>
		<div class="bodys">
			<input type="button" value="生成流程" class="layui-btn layui-btn-sm" onclick="execToBeginFlow()"/>
			<div id="list" style="position: absolute;top: 100px;left: 10px;right: 350px;"></div>
			<div class="flow-container"></div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/approvalFlow.js"></script>
<script type="text/javascript">
	function execToBeginFlow() {
		$.ajax({
			url : basePath + "frame/approvalFlow/execToBeginFlow.spring",
			data : {
				appCode : "APP",
				customApprovalFlowCode : "111",
				compNo : "CF148",
				approvalBelongCode : "222"
			},
			type:"post"
		}).then(function(data) {
			console.log(data)
		});
	}


	$(function() {
		approvalFlow.initFlow({
			appCode : "APP",
			basePath : basePath,
			approvalBelongCode : "20220225155256946",
			funCode : "eventassgintest",
			selector : "div.flow-container",
			hasApproval : true
		});
		
		approvalFlow.getApprovalBelongFlowNodeRecordList({
			appCode : "APP",
			basePath : basePath,
			approvalBelongCode : "20220225155256946",
			funCode : "eventassgintest",
			selector : "#list"
		});
	})
</script>