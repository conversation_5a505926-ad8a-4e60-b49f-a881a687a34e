.flow-move {
	position: relative;
	width: 100%;
	z-index: 999;
}

.approval_module_0 .approval_module_item {
	width: 200px;
}

.flow-move-style {
	border: 2px dashed;
	padding-top: 5px;
	margin-bottom: 5px;
	border-color: inherit;
}

.approval_content {
	bottom: -28px;
	height: 24px;
}

.bodys {
	overflow-x: hidden;
}

.loop-node {
	border-left: 1px solid;
	border-top: 1px solid;
	border-bottom: 1px solid;
	width: 20px;
	position: absolute;
	left: -20px;
	top: 10px;
	color: #BEBEBE;
}

.loop-node:before {
	content: '→';
	position: absolute;
	top: -13px;
	font-size: 20px;
}

.loop-node:after {
	content: '循环';
	position: absolute;
	top: 20px;
	left: -4px;
	font-size: 12px;
	background-color: white;
}

.add_box .layui-btn {
	background: #fff !important;
}

.item_title, .i_edit, .i_file, .i_delete, .approval_module_0 .add_btn .i_add, .i_circle, .head_text, .head {
	color: #6e6969;
}

.item_title{
	overflow: hidden;
}

.head_border1, .head_border2 {
	border: 1px solid #6e6969;
}

.sortable-background{
	background-color: #F2F2F2;
}