<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑审批流程</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode" />
		<input type="hidden" name="state" value="1" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="customApprovalFlowID" />
		<input type="hidden" name="customApprovalFlowCode" />
		<input type="hidden" name="createUID" />
		<input type="hidden" name="createUserName" />
		<input type="hidden" name="createDate" />
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input id="toCustomApprovalFlowNode" type="button" class="layui-btn layui-btn-sm layui-bg-blue layui-hide" value="编辑流程" onclick="customApprovalFlowEdit.toCustomApprovalFlowNode()" />
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
				<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table" style="margin-bottom: 0;">
				<div class="layui-form-item">
					<label class="layui-form-label"> 应用 </label>
					<div class="layui-input-inline">
						<select disabled appCode></select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						流程名称
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required|limit|specialCharacters" limit="100" name="customApprovalFlowName" value="" class="layui-input" />
					</div>
					<label class="layui-form-label"> 业务编号 </label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="businessCode|specialCharacters" name="businessCode" value="" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						流程分类
					</label>
					<div class="layui-input-inline">
						<select name="customApprovalFlowTypeCode" lay-verify="required"></select>
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						顺序号
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required|integer|limit" limit="5" maxlength="5" name="seqNo" value="0" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 备注 </label>
					<div class="layui-input-block">
						<textarea name="remark" class="layui-textarea" lay-verify="limit" limit="2000" maxlength="2000"></textarea>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/customApprovalFlowEdit.js"></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		customApprovalFlowEdit.init();
	});
</script>