<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>编辑流程模块</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/process-edit.css">
<link rel="stylesheet" type="text/css" href="css/customApprovalFlowNode.css">
</head>
<body>
	<form class="layui-form" action="" lay-filter="param">
		<input type="hidden" name="funCode"/>
		<input type="hidden" name="appCode"/>
		<input type="hidden" name="customApprovalFlowCode"/>
		<div class="head0" style="height: 38px;">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span customApprovalFlowName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" style="margin-top: 4px;"/>
			</div>
		</div>
		<div class="bodys">
			<div class="main">
				<div class="head_box">
					<i class="layui-icon layui-icon-file head"></i>
					<span class="head_text">流程设置</span>
					<div class="head_border1"></div>
					<div class="head_border2"></div>
				</div>
				<div class="approval_box">
					<div class="flow-move">
						<div class="approval_module approval_module_0" id="approval_module_0">
							<div class="add_box">
								<div class="layui-btn add_btn">
									<div class="i_shuxian"></div>
									<i class="layui-icon layui-icon-add-1 fw700 i_add" onclick="customApprovalFlowNode.toEdit({customApprovalFlowNodeID : '',seqNo : 0});"></i>
								</div>
							</div>
							<div class="i_shuxian2">↓</div>
						</div>
					</div>
				</div>
				<div class="end">
					<div>
						<i class="layui-icon layui-icon-circle i_circle"></i>
						<span>结束</span>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="../../plugins/Sortable/Sortable.min.js"></script>
<script type="text/javascript" src="js/customApprovalFlowNode.js"></script>
<script type="text/javascript">
	$(function() {
		//flowMove.init(null, customApprovalFlowNode.updateCustomApprovalFlowNodeSeqNo);
		var sortable = Sortable.create($(".approval_box")[0],{
			animation: 150,
		    ghostClass: 'sortable-background',
		    onMove: function (/**Event*/evt, /**Event*/originalEvent) {
		    	var fromNotFirst = evt.dragged.getAttribute("notFirst");
		    	var fromIndex = $(evt.dragged).index();
				var toIndex = $(evt.related).index();
		    	return !((fromIndex == 0 && $("div.flow-move:eq(1)").attr("notFirst") == 1) || (toIndex == 0 && fromNotFirst == 1));
				// Example: https://jsbin.com/nawahef/edit?js,output
				// evt.dragged; // dragged HTMLElement
				// evt.draggedRect; // DOMRect {left, top, right, bottom}
				// evt.related; // HTMLElement on which have guided
				// evt.relatedRect; // DOMRect
				// evt.willInsertAfter; // Boolean that is true if Sortable will insert drag element after target by default
				// originalEvent.clientY; // mouse position
				// return false; — for cancel
				// return -1; — insert before target
				// return 1; — insert after target
				// return true; — keep default insertion point based on the direction
				// return void; — keep default insertion point based on the direction
			},
			onEnd: function(/**Event*/evt) {
				customApprovalFlowNode.updateCustomApprovalFlowNodeSeqNo();
				// var itemEl = evt.item;  // dragged HTMLElement
				// evt.to;    // target list
				// evt.from;  // previous list
				// evt.oldIndex;  // element's old index within old parent
				// evt.newIndex;  // element's new index within new parent
				// evt.oldDraggableIndex; // element's old index within old parent, only counting draggable elements
				// evt.newDraggableIndex; // element's new index within new parent, only counting draggable elements
				// evt.clone // the clone element
				// evt.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
			}
		});
		customApprovalFlowNode.init();
	});
</script>