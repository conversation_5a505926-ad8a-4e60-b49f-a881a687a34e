<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑审批流程节点</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/customApprovalFlowNodeEdit.css">
</head>
<body>
	<div class="head0">
		<span class="head1_text fw700">
			<i menuIcon class="layui-icon2"></i>
			<span approvalFlowName>节点设置</span>
		</span>
		<div class="head0_right fr">
			<input type="button" class="layui-btn layui-btn-sm" value="保存" onclick="customApprovalFlowNodeEdit.submit();" />
			<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" />
		</div>
	</div>
	<div class="bodys">
		<div class="layui-table main_table" style="margin-bottom: 0;">
			<form class="layui-form" lay-filter="param">
				<input id="paramSaveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="" lay-submit lay-filter="saveParam" />
				<input type="hidden" name="funCode" />
				<input type="hidden" name="appCode" />
				<input type="hidden" name="customApprovalFlowNodeID" />
				<input type="hidden" name="customApprovalFlowNodeCode" />
				<input type="hidden" name="customApprovalFlowCode" />
				<input type="hidden" name="seqNo" />
				<input type="hidden" name="createUID" />
				<input type="hidden" name="createUserName" />
				<input type="hidden" name="createDate" />
				<fieldset>
                    <legend>基础信息</legend>
                </fieldset>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						节点名称
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required|limit|specialCharacters" limit="200" name="customApprovalFlowNodeName" class="layui-input" />
					</div>
					<label class="layui-form-label"> 业务编号 </label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="businessCode|limit|specialCharacters" limit="200" name="businessCode" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 审批表单 </label>
					<div class="layui-input-inline">
						<input type="text" name="approvalCustomFormName" class="layui-input" placeholder="点击选择" readonly="readonly" onclick="customApprovalFlowNodeEdit.toSelectApprovalCustomFormCode(this);" />
						<input type="hidden" name="approvalCustomFormCode" />
						<i class="layui-icon2 layui-select-right" onclick="customApprovalFlowNodeEdit.clearApprovalCustomFormValue()" title="清空">&#xe68d;</i>
					</div>
					<label class="layui-form-label"> 状态流转<i class="layui-icon2" approvalNodeStateTips>&#xe890;</i> </label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="num" limit="200" name="approvalNodeState" class="layui-input" />
					</div>
				</div>
				<fieldset>
                    <legend>审批条件</legend>
                </fieldset>
				<div class="layui-form-item">
					<label class="layui-form-label"> 审批条件 </label>
					<div class="layui-input-inline">
						<select name="approvalCondition" lay-filter="approvalCondition">
							<option value="">无</option>
							<option value="into">进入审批</option>
							<option value="skip">跳过审批</option>
						</select>
					</div>
					<label approvalCondition class="layui-form-label layui-hide"> 条件类型 </label>
					<div approvalCondition class="layui-input-inline layui-hide">
						<select name="approvalConditionType" lay-filter="approvalConditionType">
							<option value="0">单条件</option>
							<option value="1" disabled="disabled">表达式</option>
							<option value="2">接口</option>
						</select>
					</div> 
				</div>
				<div approvalCondition class="layui-form-item  layui-hide">
					<label approvalConditionType="0" class="layui-form-label"> 字段归属<i class="layui-icon2" approvalFieldBelongTips>&#xe890;</i> </label>
					<div approvalConditionType="0" class="layui-input-inline">
						<select name="approvalFieldBelong" lay-filter="approvalFieldBelong">
							<option value="form">表单</option>
							<option value="approval">审批内容</option>
						</select>
					</div>
					<label approvalFieldBelong="approval" class="layui-form-label layui-hide"> 节点 </label>
					<div approvalFieldBelong="approval" class="layui-input-inline layui-hide">
						<select name="approvalFieldBelongNode" lay-filter="approvalFieldBelongNode"></select>
					</div>
				</div>
				<div approvalCondition class="layui-form-item layui-hide">
					<label approvalConditionType="0" class="layui-form-label">
						<span style="color: red;">*</span>
						字段业务编号
					</label>
					<div approvalConditionType="0" class="layui-input-inline" style="width: 150px;">
						<input type="text" lay-verify="required|limit|specialCharacters" limit="200" approvalConditionBusinessCode class="layui-input" lay-filter="approvalConditionSingle" />
					</div>
					<div approvalConditionType="0" class="layui-input-inline" style="width: 100px;">
						<select approvalConditionSymbol lay-filter="approvalConditionSingle">
							<option index="1" value="=">等于</option>
							<option index="2" value="<>">不等于</option>
							<option index="3" value="LIKE">包含</option>
							<option index="4" value="NOT LIKE">不包含</option>
							<option index="5" value=">">大于</option>
							<option index="6" value=">=">大于等于</option>
							<option index="7" value="<">小于</option>
							<option index="8" value="<=">小于等于</option>
						</select>
					</div>
					<label approvalConditionType="0" class="layui-form-label">
						<span style="color: red;">*</span>
						字段值
					</label>
					<div approvalConditionType="0" class="layui-input-inline">
						<input type="text" lay-verify="required|limit" limit="1000" approvalConditionBusinessValue class="layui-input" lay-filter="approvalConditionSingle" />
					</div>
					<label approvalConditionType="1" class="layui-form-label layui-hide"> 表达式 </label>
					<div approvalConditionType="1" class="layui-input-block layui-hide">
						<textarea name="approvalConditionConfig" class="layui-textarea"></textarea>
					</div>
				</div>
				<fieldset>
                    <legend>审批权限</legend>
                </fieldset>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						节点类型
					</label>
					<div class="layui-input-block layui-form" lay-filter="approvalFlowNodeTypeDiv" lay-verify="checkboxRequired" title="节点类型">
						<input type="radio" title="普通节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="0" class="layui-input" checked />
						<input type="radio" title="会签节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="3" class="layui-input" />
						<input type="radio" title="循环节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="2" class="layui-input" />
						<input type="radio" title="指定审批节点" name="approvalFlowNodeType" lay-filter="approvalFlowNodeType" value="1" class="layui-input" />
					</div>
				</div>
			</form>
			<form class="layui-form" lay-filter="approvalFlowNodeData">
				<input id="approvalFlowNodeDataSaveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="" lay-submit lay-filter="saveApprovalFlowNodeData" />
				<div approvalFlowNodeType="0" class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						审批权限
					</label>
					<div class="layui-input-inline">
						<select name="approvalRight" lay-filter="approvalRight">
							<option value="1">审批科室</option>
							<option value="2">功能组织架构</option>
							<option value="3">指定审批人</option>
							<option value="4">接口</option>
						</select>
					</div>
					<label approvalRight="1" class="layui-form-label">
						<span style="color: red;">*</span>
						审批科室
					</label>
					<div approvalRight="1" class="layui-input-inline">
						<select name="approvalDeptID" lay-search lay-verify="required"></select>
					</div>
					<label approvalRight="3" class="layui-form-label layui-hide">
						<span style="color: red;">*</span>
						指定审批人
					</label>
					<div approvalRight="3" class="layui-input-inline layui-hide">
						<input type="text"  name="approvalNames" class="layui-input" readonly="readonly" onclick="customApprovalFlowNodeEdit.toSelectApprovalUser(this);" />
						<input type="hidden" name="approvalUIDs" lay-verify="required" />
					</div>
				</div>
				<div approvalFlowNodeType="0" approvalRight="2" class="layui-form-item layui-hide">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						功能点
					</label>
					<div class="layui-input-inline">
						<select name="approvalFunCode" lay-verify="required"></select>
					</div>
					<label class="layui-form-label" style="width: 128px;">科室业务编号<i class="layui-icon2" approvalDeptOwnershipTips>&#xe890;</i></label>
					<div class="layui-input-inline">
						<input type="text" name="approvalDeptOwnership" lay-verify="limit|specialCharacters" limit="200" class="layui-input" />
					</div>
				</div>
				<div approvalFlowNodeType="2" class="layui-form-item layui-hide">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						开始节点
					</label>
					<div class="layui-input-inline">
						<select name="beginNode" lay-verify="required"></select>
					</div>
				</div>
				<div approvalFlowNodeType="3" class="layui-form-item layui-hide">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						方式
					</label>
					<div class="layui-input-block layui-form" lay-filter="countersignMethodDiv">
						<input type="radio" title="固定" name="countersignMethod" lay-filter="countersignMethod" value="0" class="layui-input" checked />
						<input type="radio" title="动态指定" name="countersignMethod" lay-filter="countersignMethod" value="1" class="layui-input" />
						<input type="radio" title="接口" name="countersignMethod" lay-filter="countersignMethod" value="2" class="layui-input" />
					</div>
				</div>
				<div approvalFlowNodeType="3" class="layui-form-item layui-hide">
					<label countersignMethod="0" class="layui-form-label">
						<span style="color: red;">*</span>
						指定会签人员
					</label>
					<div countersignMethod="0" class="layui-input-inline">
						<input type="text"  name="approvalNames" class="layui-input" readonly="readonly" onclick="customApprovalFlowNodeEdit.toSelectApprovalUser(this);" />
						<input type="hidden" name="approvalUIDs" lay-verify="required" />
					</div>
				</div>
			</form>
		</div>
	</div>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/customApprovalFlowNodeEdit.js"></script>
<script type="text/javascript">
	$(function() {
		customApprovalFlowNodeEdit.init();
	});
</script>