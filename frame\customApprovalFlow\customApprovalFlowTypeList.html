<!DOCTYPE html>
<html>
<head>
<title>自定义审批流程列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="css/customApprovalFlowTypeList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" target="exportIframe" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="currAppCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<label class="layui-form-label2 layui-hide hide-appCode">应用系统</label>
				<div class="layui-input-inline h28 lh28 layui-hide hide-appCode">
					<select name="appCode" lay-filter="appCode"></select>
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="customApprovalFlowTypeList.toEdit({customApprovalFlowTypeID:''})" value="新增" />
			</div>
		</div>
		<div class="bodys layui-form">
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">关键字</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" name="keyword" value="" autocomplete="off" placeholder="流程分类名称" title="流程分类名称" class="layui-input" onkeyup="if(timeout) clearTimeout(timeout);timeout = setTimeout(function(){customApprovalFlowTypeList.getCustomApprovalFlowTypeList();},500);" />
				</div>
				
				<label class="layui-form-label2">状态</label>
				<div class="layui-input-inline h28 lh28">
					<select name="state" lay-filter="state">
						<option value="99">全部</option>
						<option value="1" selected>有效</option>
						<option value="0">无效</option>
					</select>
				</div>
			</div>
			<div class="layui-row">
				<div class="tableDiv table_noTree" style="top: 45px;">
					<div id="list" lay-filter="list"></div>
				</div>
			</div>
		</div>
	</form>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
	<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
	<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
	<script type="text/javascript" src="js/customApprovalFlowTypeList.js"></script>
	<script type="text/javascript">
		var timeout = null;
		$(function() {
			customApprovalFlowTypeList.init();
		});
	</script>
</body>
</html>