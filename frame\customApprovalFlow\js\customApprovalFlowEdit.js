var customApprovalFlowEdit = {
	init : function() {
		customApprovalFlowEdit.getCustomApprovalFlowTypeList().then(function() {
			return customApprovalFlowEdit.getCustomApprovalFlow();
		}).then(function() {
			return assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		}).then(function() {
			return customApprovalFlowEdit.getExceAppList();
		}).then(function() {
			customApprovalFlowEdit.initLayui();
		});
		
	},
	initLayui : function() {
		layui.form.verify({
			businessCode : function(value, item) {
				if (value && customApprovalFlowEdit.businessCodeCheck(value)) {
					return "业务编号已存在";
				}
			}
		});
		layui.form.on("submit(save)", function() {
			customApprovalFlowEdit.saveCustomApprovalFlow();
			return false;
		});
		layui.form.render();
	},
	businessCodeCheck : function(businessCode) {
		var flag = false;
		$.ajax({
			url : basePath + "frame/customApprovalFlow/businessCodeCheck.spring",
			data : {
				"compNo" : param.get("compNo"),
				"appCode" : param.get("appCode"),
				"businessCode" : businessCode,
				"customApprovalFlowCode" : param.get("customApprovalFlowCode")
			},
			dataType : "json",
			async : false,
			success : function(data) {
				flag = data.has;
			}
		});
		return flag;
	},
	getCustomApprovalFlowTypeList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/getCustomApprovalFlowTypeList.spring",
			data : {
				compNo : param.get("compNo"),
				appCode : param.get("appCode"),
				state : 1
			},
			success : function(data) {
				var customApprovalFlowTypeCode = assemblys.getParam("customApprovalFlowTypeCode");
				var customApprovalFlowTypeList = data.customApprovalFlowTypeList;
				var $select = $("select[name=customApprovalFlowTypeCode]");
				var html = "";
				for (var i = 0; i < customApprovalFlowTypeList.length; i++) {
					html += '<option value="' + customApprovalFlowTypeList[i].customApprovalFlowTypeCode + '" ' + (customApprovalFlowTypeCode && customApprovalFlowTypeCode == customApprovalFlowTypeList[i].customApprovalFlowTypeCode ? "selected" : "") + '>'
							+ customApprovalFlowTypeList[i].customApprovalFlowTypeName + '</option>';
				}
				$select.append(html);
			}
		});
	},
	getCustomApprovalFlow : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/getCustomApprovalFlow.spring",
			data : {
				customApprovalFlowID : param.get("customApprovalFlowID"),
				appCode : param.get("appCode")
			},
			success : function(data) {
				if (data.customApprovalFlow) {
					param.set(null, data.customApprovalFlow);
					$("#toCustomApprovalFlowNode").removeClass("layui-hide");
				}
			}
		});
	},
	saveCustomApprovalFlow : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/saveCustomApprovalFlow.spring",
			data : param.__form(),
			type : "post",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					parent.page.set("curPageNum", 1);
					parent.customApprovalFlowList.getCustomApprovalFlowList();
					assemblys.closeWindow();
				});
			}
		});
	},
	getExceAppList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/getExceAppList.spring",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var appList = data.appList;
				var $select = $("select[appCode]");
				var html = "";
				var appCode = param.get("appCode");
				for (var i = 0; i < appList.length; i++) {
					if (appList[i].compAppID != 0) {
						html += '<option value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
					}
				}
				$select.append(html);
			}
		});
	},
	toCustomApprovalFlowNode : function() {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			closeBtn : 0,
			area : [ '900px', '600px' ],
			title : false,
			scrollbar : false,
			content : "customApprovalFlowNode.html?funCode=" + param.get("funCode") + "&customApprovalFlowName=" + encodeURIComponent(param.get("customApprovalFlowName")) + "&appCode=" + param.get("appCode") + "&compNo=" + param.get("compNo") + "&customApprovalFlowCode="
					+ param.get("customApprovalFlowCode")
		});
		
	}
}