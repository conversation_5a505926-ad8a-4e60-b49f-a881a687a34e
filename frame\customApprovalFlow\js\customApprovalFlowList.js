var customApprovalFlowList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			return customApprovalFlowList.getExceAppList();
		}).then(function() {
			assemblys.initSessionStoragePram();
			return customApprovalFlowList.getCustomApprovalFlowTypeList();
		}).then(function() {
			customApprovalFlowList.getCustomApprovalFlowList();
			customApprovalFlowList.initLayui();
		});
	},
	initLayui : function() {
		layui.form.on("switch(state)", function(data) {
			customApprovalFlowList.updateCustomApprovalFlowState(data)
		});
		
		layui.form.on("select(state)", function(data) {
			customApprovalFlowList.getCustomApprovalFlowList();
		});
		
		layui.form.on("select(appCode)", function(data) {
			customApprovalFlowList.getCustomApprovalFlowTypeList().then(function() {
				customApprovalFlowList.getCustomApprovalFlowList();
			});
			localStorage.setItem("CUSTOM_APPCODE_VALUE", data.value);
		});
	},
	search : function() {
		customApprovalFlowList.getCustomApprovalFlowTypeList().then(function() {
			customApprovalFlowList.getCustomApprovalFlowList();
		})
	},
	updateCustomApprovalFlowState : function(data) {
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/updateCustomApprovalFlowState.spring",
			data : {
				state : data.elem.checked ? 1 : 0,
				customApprovalFlowID : data.elem.getAttribute("customApprovalFlowID"),
				appCode : param.get("appCode")
			},
			type : "post",
			success : function(data) {
				assemblys.msg("修改状态成功", function() {
					customApprovalFlowList.getCustomApprovalFlowList();
				});
			}
		});
	},
	getCustomApprovalFlowTypeList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/getCustomApprovalFlowTypeList.spring",
			data : {
				compNo : param.get("compNo"),
				appCode : param.get("appCode"),
				state : 1
			},
			success : function(data) {
				customApprovalFlowList.customApprovalFlowTypeList = data.customApprovalFlowTypeList;
				var treeData = [ {
					title : "全部"
				}, {
					title : "未分类",
					customApprovalFlowTypeCode : "none_type"
				} ];
				for (var i = 0; i < data.customApprovalFlowTypeList.length; i++) {
					if (data.customApprovalFlowTypeList[i].customApprovalFlowTypeCode == "none_type") {
						continue;
					}
					data.customApprovalFlowTypeList[i].title = data.customApprovalFlowTypeList[i].customApprovalFlowTypeName;
					treeData.push(data.customApprovalFlowTypeList[i]);
				}
				layui.tree.render({
					elem : '#tree', //绑定元素
					data : treeData,
					click : function(item) {
						param.set("customApprovalFlowTypeCode", item.data.customApprovalFlowTypeCode || "");
						customApprovalFlowList.getCustomApprovalFlowList();
					}
				});
			}
		});
	},
	getCustomApprovalFlowList : function() {
		assemblys.tableRender({
			elem : '#list',
			url : basePath + 'frame/customApprovalFlow/getCustomApprovalFlowPager.spring?' + param.__form(),
			cols : [ [ {
				title : '序号',
				align : "center",
				type : 'numbers'
			}, {
				title : '操作',
				width : 100,
				align : "center",
				templet : function(d) {
					var html = '';
					html += '<i class="layui-icon layui-icon-edit i_icon" title="编辑" lay-event="toEdit"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" style="cursor: pointer" lay-event="del"></i>';
					html += '<i class="layui-icon2 i_icon" title="流程" lay-event="toCustomApprovalFlowNode">&#xea64;</i>';
					html += '<i class="layui-icon2 i_icon" title="导出SQL" style="cursor: pointer" lay-event="exportSQL">&#xea80;</i>';
					return html;
				}
			}, {
				title : '流程分类名称',
				width : 150,
				align : "left",
				templet : function(d) {
					return assemblys.htmlEncode(d.customApprovalFlowTypeName);
				}
			}, {
				title : '审批流程名称',
				width : 150,
				align : "left",
				templet : function(d) {
					return '<a lay-event="toEdit">' + assemblys.htmlEncode(d.customApprovalFlowName) + '</a>';
				}
			}, {
				title : '流程',
				align : "left",
				minWidth : 200,
				templet : function(d) {
					return assemblys.htmlEncode(d.customApprovalFlowNodeNames);
				}
			}, {
				title : '创建人',
				align : "center",
				width : 160,
				templet : function(d) {
					return d.createUserName + "<br>" + assemblys.dateToStr(d.createDate);
				}
			}, {
				title : '顺序号',
				align : "center",
				width : 80,
				field : "seqNo"
			}, {
				title : '状态',
				align : "center",
				width : 100,
				templet : function(d) {
					return '<input type="checkbox" customApprovalFlowID="' + d.customApprovalFlowID + '" lay-filter="state" value="1" lay-skin="switch" ' + (d.state == 1 ? "checked" : "") + ' lay-text="有效|无效"/>';
				}
			} ] ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
			},
			events : {
				toEdit : customApprovalFlowList.toEdit,
				toCustomApprovalFlowNode : customApprovalFlowList.toCustomApprovalFlowNode,
				exportSQL : customApprovalFlowList.exportSQL,
				del : customApprovalFlowList.deleteCustomApprovalFlow,
			}
		});
		
	},
	deleteCustomApprovalFlow : function(d) {
		assemblys.confirm("确定删除『" + assemblys.htmlEncode(d.customApprovalFlowName) + "』吗？", function() {
			$.ajax({
				url : basePath + "frame/customApprovalFlow/deleteCustomApprovalFlow.spring",
				data : {
					"customApprovalFlowCode" : d.customApprovalFlowCode,
					"appCode" : param.get("appCode")
				},
				dataType : "json",
				type : "post",
				success : function(data) {
					assemblys.msg("删除成功", function() {
						customApprovalFlowList.getCustomApprovalFlowList();
					});
				}
			});
		});
	},
	getExceAppList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/getExceAppList.spring",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var appList = data.appList;
				var $select = $("select[name='appCode']");
				var html = "";
				var customAppCode = localStorage.getItem("CUSTOM_APPCODE_VALUE");
				
				var currAppCode = param.get("currAppCode");
				if (!currAppCode) {
					$('.hide-appCode').removeClass("layui-hide");
					var appCode = param.get("appCode");
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0) {
							var selected = customAppCode == appList[i].appCode ? "selected" : "";
							html += '<option ' + selected + ' value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
						}
					}
				} else {
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0 && currAppCode == appList[i].appCode) {
							html += '<option value="' + appList[i].appCode + '" selected>' + appList[i].appName + '</option>';
						}
					}
				}
				$select.append(html);
				layui.form.render();
			}
		});
	},
	toEdit : function(d) {
		if (customApprovalFlowList.customApprovalFlowTypeList.length > 0) {
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				closeBtn : 0,
				area : [ '950px', '700px' ],
				title : false,
				scrollbar : false,
				content : "customApprovalFlowEdit.html?funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&compNo=" + param.get("compNo") + "&customApprovalFlowID=" + d.customApprovalFlowID + "&customApprovalFlowTypeCode=" + param.get("customApprovalFlowTypeCode"),
				end : function() {
					customApprovalFlowList.getCustomApprovalFlowList();
				}
			});
		} else {
			assemblys.msg("请先新增流程分类");
		}
	},
	toCustomApprovalFlowTypeList : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "customApprovalFlowTypeList",
			area : [ '100%', '100%' ],
			title : "流程分类管理",
			scrollbar : false,
			content : "customApprovalFlowTypeList.html?funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&compNo=" + param.get("compNo"),
			end : function() {
				customApprovalFlowList.getCustomApprovalFlowTypeList().then(function() {
					customApprovalFlowList.getCustomApprovalFlowList();
				});
			}
		});
	},
	toCustomApprovalFlowNode : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			closeBtn : 0,
			area : [ '900px', '600px' ],
			title : false,
			scrollbar : false,
			content : "customApprovalFlowNode.html?funCode=" + param.get("funCode") + "&customApprovalFlowName=" + encodeURIComponent(d.customApprovalFlowName) + "&appCode=" + param.get("appCode") + "&compNo=" + param.get("compNo") + "&customApprovalFlowCode=" + d.customApprovalFlowCode,
			end : function() {
				customApprovalFlowList.getCustomApprovalFlowList();
			}
		});
	},
	exportSQL : function(d) {
		location.href = basePath + "frame/customApprovalFlow/exportFlow.spring?appCode=" + param.get("appCode") + "&customApprovalFlowCode=" + d.customApprovalFlowCode;
	}
}