var customApprovalFlowNode = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			return customApprovalFlowNode.getCustomApprovalFlowNodeList();
		}).then(function() {
			$("span[customApprovalFlowName]").text(param.get("customApprovalFlowName"));
		});
	},
	toEdit : function(node) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			closeBtn : 0,
			area : [ '100%', '100%' ],
			title : false,
			scrollbar : false,
			content : "customApprovalFlowNodeEdit.html?funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&compNo=" + param.get("compNo") + "&customApprovalFlowName=" + encodeURIComponent(param.get("customApprovalFlowName")) + "&customApprovalFlowCode="
					+ encodeURIComponent(param.get("customApprovalFlowCode")) + "&customApprovalFlowNodeID=" + node.customApprovalFlowNodeID + "&seqNo=" + node.seqNo
		});
	},
	getCustomApprovalFlowNodeList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/getCustomApprovalFlowNodeList.spring",
			data : {
				appCode : param.get("appCode"),
				customApprovalFlowCode : param.get("customApprovalFlowCode"),
			},
			success : function(data) {
				var approvalFlowNodeTypeMap = data.approvalFlowNodeTypeMap;
				var doms = [];
				var loopNode = null;
				for (var i = 0; i < data.customApprovalFlowNodeList.length; i++) {
					// 当前节点
					let node = data.customApprovalFlowNodeList[i];
					if (node.approvalFlowNodeType == 2) {
						loopNode = node;
					}
					
					// 下一节点
					let nextNode = data.customApprovalFlowNodeList[i + 1];
					
					doms.push({
						attr : {
							customApprovalFlowNodeCode : data.customApprovalFlowNodeList[i].customApprovalFlowNodeCode,
							notFirst : (node.approvalFlowNodeType == 1 || node.approvalFlowNodeType == 2 || (node.approvalFlowNodeType == 3 && JSON.parse(node.approvalFlowNodeData).countersignMethod == 1)) ? "1" : "0"
						},
						tagName : "div",
						className : "flow-move",
						children : [ {
							tagName : "div",
							className : "approval_module approval_module_0",
							children : [ {
								tagName : "div",
								className : "add_box",
								children : [ {
									tagName : "div",
									className : "layui-btn add_btn",
									children : [ {
										tagName : "div",
										className : "i_shuxian"
									}, {
										tagName : "i",
										className : "layui-icon layui-icon-add-1 fw700 i_add",
										onclick : function() {
											customApprovalFlowNode.toEdit({
												customApprovalFlowNodeID : "",
												seqNo : (node.seqNo || node.seqNo == 0) ? node.seqNo + 1 : 0
											});
										}
									} ]
								} ]
							}, {
								tagName : "div",
								className : "i_shuxian2",
								innerText : "↓"
							}, {
								tagName : "div",
								className : "approval_module_item",
								children : [ {
									tagName : "div",
									className : "item_title skin-div-css",
									innerText : data.customApprovalFlowNodeList[i].customApprovalFlowNodeName
								}, {
									tagName : "input",
									type : "hidden",
									name : "customApprovalFlowNodeCode",
									value : data.customApprovalFlowNodeList[i].customApprovalFlowNodeCode
								}, {
									tagName : "div",
									className : "approval_edit skin-div-border",
									children : [ {
										tagName : "i",
										className : "layui-icon layui-icon-edit i_edit",
										onclick : function(e) {
											e.stopPropagation();
											customApprovalFlowNode.toEdit(node);
										},
										onmousedown : function(e) {
											e.stopPropagation();
										}
									}, {
										tagName : "div",
										className : "edborder",
									}, {
										tagName : "i",
										className : "layui-icon layui-icon-delete i_delete",
										onclick : i == 0 && nextNode && (nextNode.approvalFlowNodeType == 1 || (nextNode.approvalFlowNodeType == 3 && JSON.parse(nextNode.approvalFlowNodeData).countersignMethod == 1)) ? function(e) {
											assemblys.msg("第一个节点的下一个节点是指定审核节点或动态会签节点时，不允许删除");
										} : function(e) {
											e.stopPropagation();
											customApprovalFlowNode.deleteCustomApprovalFlowNode(node);
										},
										onmousedown : function(e) {
											e.stopPropagation();
										}
									} ]
								}, {
									tagName : "div",
									className : "aproval_dashed",
									children : []
								}, {
									tagName : "div",
									className : "approval_content",
									children : [ {
										tagName : "div",
										className : "approval_content_0 approval_content_first",
										children : [ {
											tagName : "div",
											className : "approval_content_left",
											innerText : "节点类型：" + approvalFlowNodeTypeMap[node.approvalFlowNodeType]
										} ]
									}, {
										tagName : "div",
										className : "approval_content_0",
										children : [ {
											tagName : "div",
											className : "approval_content_left",
											innerText : "审批表单：" + (node.approvalCustomFormName || "默认")
										} ]
									}, {
										tagName : "div",
										className : "approval_content_0",
										children : [ {
											tagName : "div",
											className : "approval_content_left",
											onmouseover : function(e) {
												assemblys.tips(this, "<div style='word-break: break-all;'>审批条件：" + node.approvalConditionConfig + "</div><br><div style='word-break: break-all;'>审批权限：" + node.approvalFlowNodeData + "</div>", 0, "left");
												
											},
											innerText : "审批条件：" + (node.approvalConditionConfig ? "有" : "无")
										} ]
									} ]
								} ]
							} ]
						} ]
					});
					
				}
				
				if (doms.length > 0) {
					assemblys.createElement(doms, $("div.approval_box").empty()[0]);
				}else{
					 $("div.approval_module_item").empty()[0];
				}
				
				// 循环节点处理
				if (loopNode) {
					var $beginNode = null;
					var beginIndex = -1;
					var approvalFlowNodeData = JSON.parse(loopNode.approvalFlowNodeData);
					for (var i = 0; i < data.customApprovalFlowNodeList.length; i++) {
						// 找出开始节点
						if (approvalFlowNodeData.beginNode == data.customApprovalFlowNodeList[i].customApprovalFlowNodeCode) {
							$beginNode = $("div.flow-move").eq(i);
							$beginNode.append('<div class="loop-node"></div>');
							beginIndex = i;
						}
						
						// 开始节点后的节点全部放到开始节点div.flow-move内
						if (i > beginIndex && beginIndex > -1) {
							var $thisFlowMove = $("div.flow-move[customApprovalFlowNodeCode='" + data.customApprovalFlowNodeList[i].customApprovalFlowNodeCode + "']");
							$beginNode.append($thisFlowMove.children());
							$thisFlowMove.remove();
						}
						
						if (loopNode == data.customApprovalFlowNodeList[i]) {
							break;
						}
					}
					
					$beginNode.find("div.loop-node").height(($beginNode.height() - 50) + "px");
					
					// 开始节点不能删除
					$beginNode.children().eq(0).find("i.i_delete")[0].onclick = function() {
						assemblys.msg("循环起始节点不允许删除");
					};
				}
			}
		});
	},
	updateCustomApprovalFlowNodeSeqNo : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/updateCustomApprovalFlowNodeSeqNo.spring",
			data : param.__form(),
			type : "post",
		}).then(function() {
			customApprovalFlowNode.getCustomApprovalFlowNodeList();
		});
	},
	deleteCustomApprovalFlowNode : function(node) {
		assemblys.confirm("确定删除『" + node.customApprovalFlowNodeName + "』节点吗？", function() {
			$.ajax({
				url : basePath + "frame/customApprovalFlow/deleteCustomApprovalFlowNode.spring",
				data : {
					customApprovalFlowNodeCode : node.customApprovalFlowNodeCode,
					appCode : param.get("appCode")
				},
				type : "post",
				success : function(data) {
					customApprovalFlowNode.getCustomApprovalFlowNodeList();
					assemblys.msg("删除成功");
				}
			});
		})
	}
}