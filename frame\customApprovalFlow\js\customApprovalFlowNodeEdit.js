var customApprovalFlowNodeEdit = {
	approvalFlowNodeType : null,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			return customApprovalFlowNodeEdit.getDeptList();
		}).then(function() {
			return customApprovalFlowNodeEdit.getCustomApprovalRightFunList();
		}).then(function() {
			return customApprovalFlowNodeEdit.getCustomApprovalFlowNodeList();
		}).then(function() {
			$("span[approvalFlowName]").text("节点设置");
			// 先绑定方法再回显
			customApprovalFlowNodeEdit.initLayui();
			customApprovalFlowNodeEdit.initApprovalCondition();
			return customApprovalFlowNodeEdit.getCustomApprovalFlowNode();
		}).then(function() {
			if (param.get("seqNo") == 0) {
				$("input[name=approvalFlowNodeType][value=1],input[name=approvalFlowNodeType][value=2]").attr("disabled", "disabled");
				layui.form.render("radio", "approvalFlowNodeTypeDiv");
				
				// 会签指定审批人员方式,第一个节点不能动态指定
				$("input[name=countersignMethod][value=1]").attr("disabled", "disabled").prop("checked", false);
				//$("input[name=countersignMethod][value=0]").prop("checked", true);
				layui.form.render("radio", "countersignMethodDiv");
				
			}
			
			$("i[approvalFieldBelongTips]").hover(function(e) {
				assemblys.tips(this, "审批条件表达式字段归属哪个表单(表单:流程归属的表单,审批内容:上一个节点的审批表单内容)", 0, "bottom");
			});
			
			$("i[approvalNodeStateTips]").hover(function(e) {
				assemblys.tips(this, "流转到当前节点时，关联的上报表单状态，必须是表单分类设置的状态列表中包含的状态编码，没有找到状态保持不变", 0, "bottom");
			});
			
			$("i[approvalDeptOwnershipTips]").hover(function(e) {
				assemblys.tips(this, "<strong>表单流程</strong>：关联上报表单的科室组件，填写科室组件的业务编号，例如事发科室。为空默认上报科室；<br><hr><strong>单独流程</strong>：如果流程是单独使用时，那么需要重写某些方法来达到节点条件判断的目的", 0, "top");
			});
		});
	},
	initLayui : function() {
		
		layui.form.on("select(approvalRight)", function(data) {
			$("[approvalRight]").addClass("layui-hide");
			$("[approvalRight='" + data.value + "']").removeClass("layui-hide");
		});
		
		layui.form.on("select(approvalCondition)", function(data) {
			
			if (data.value) {
				$("[approvalCondition]").removeClass("layui-hide");
			} else {
				$("[approvalCondition]").addClass("layui-hide");
				$("[approvalCondition]").find("input[approvalconditionbusinesscode],input[approvalconditionbusinessvalue],textarea[name=approvalConditionConfig]").val("");
			}
			
		});
		
		layui.form.on("select(approvalConditionType)", function(data) {
			$("[approvalConditionType]").addClass("layui-hide");
			$("[approvalConditionType='" + data.value + "']").removeClass("layui-hide");
		});
		
		layui.form.on("select(approvalFieldBelong)", function(data) {
			$("[approvalFieldBelong]").addClass("layui-hide");
			$("[approvalFieldBelong='" + data.value + "']").removeClass("layui-hide");
		});
		
		layui.form.on("radio(approvalFlowNodeType)", function(data) {
			$("[approvalFlowNodeType]").addClass("layui-hide");
			$("[approvalFlowNodeType='" + data.value + "']").removeClass("layui-hide");
			
			// 点击普通节点时，会显示错误
			if (data.value == 0) {
				$("[approvalRight]").addClass("layui-hide");
				$("[approvalRight='" + $("select[name='approvalRight']").val() + "']").removeClass("layui-hide");
			}
		});
		
		layui.form.on("radio(countersignMethod)", function(data) {
			$("[countersignMethod]").addClass("layui-hide");
			$("[countersignMethod='" + data.value + "']").removeClass("layui-hide");
			
			if (data.value == 1) {
				$("[countersignMethod]").find("input").val("");
			}
		});
		
		layui.form.on("submit(saveParam)", function() {
			$("#approvalFlowNodeDataSaveBtn").click();
			return false;
		});
		
		layui.form.on("submit(saveApprovalFlowNodeData)", function() {
			customApprovalFlowNodeEdit.saveCustomApprovalFlowNode();
			return false;
		});
		
		layui.form.on("select(approvalConditionSingle)", function(data) {
			if($(data.elem).find("option:selected").attr("index") > 4){
				$("input[approvalConditionBusinessValue]").attr("lay-verify","required|number");
			}else{
				$("input[approvalConditionBusinessValue]").attr("lay-verify","required|limit");
			}
			customApprovalFlowNodeEdit.handleApprovalConditionConfig();
		});
		
		layui.form.render();
	},
	initApprovalCondition : function() {
		$("input[lay-filter='approvalConditionSingle']").change(function(e) {
			customApprovalFlowNodeEdit.handleApprovalConditionConfig();
		});
	},
	handleApprovalConditionConfig : function() {
		var approvalConditionBusinessCode = $("input[approvalConditionBusinessCode]").val();
		var approvalConditionSymbol = $("select[approvalConditionSymbol]").val();
		var approvalConditionBusinessValue = $("input[approvalConditionBusinessValue]").val();
		param.set("approvalConditionConfig", "$[" + approvalConditionBusinessCode + "] " + approvalConditionSymbol + " ${" + approvalConditionBusinessValue + "}");
	},
	getDeptList : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			data : {
				compNo : param.get("compNo")
			},
			skipDataCheck : true,
			success : function(data) {
				var deptList = data.deptList;
				var $select = $("select[name='approvalDeptID']");
				var html = "";
				for (var i = 0; i < deptList.length; i++) {
					html += '<option value="' + deptList[i].DeptID + '">' + deptList[i].DeptName + '</option>';
				}
				$select.append(html);
			}
		});
	},
	getCustomApprovalRightFunList : function() {
		return $.ajax({
			url : basePath + "frame/appFunsSet/getCustomApprovalRightFunList.spring",
			data : {
				appCode : param.get("appCode")
			},
			success : function(data) {
				var funList = data.funList;
				var $select = $("select[name='approvalFunCode']");
				var html = "";
				var funCode = param.get("appCode") + "_CUSTOM_APPROVER";
				for (var i = 0; i < funList.length; i++) {
					if (funCode == funList[i].funCode) {
						continue;
					}
					html += '<option value="' + funList[i].funCode + '">' + funList[i].funName + '</option>';
				}
				$select.append(html);
			}
		});
	},
	submit : function() {
		// 处理隐藏的表单数据，不提交到后台
		$("input[type=hidden],input[type=text],textarea,select").parents("div.layui-input-inline:hidden").find("input[type=hidden],input[type=text],textarea,select").each(function(i, e) {
			$(this).attr("lay-verify-temp", $(this).attr("lay-verify")).removeAttr("lay-verify");
		}).attr("disabled", "disabled");
		
		// 1秒后还原，处理数据应该不会超过一秒，因为有很多情况需要还原，例如必填项校验没有通过，这种情况没有回调不能手动还原数据，所以这里用了定时执行
		setTimeout(function() {
			// 已经提交了,可以还原回来，不管是否成功提交
			$("input[type=hidden],input[type=text],textarea,select").parents("div.layui-input-inline:hidden").find("input[type=hidden],input[type=text],textarea,select").each(function(i, e) {
				$(this).attr("lay-verify", $(this).attr("lay-verify-temp")).removeAttr("lay-verify-temp");
			}).removeAttr("disabled");
		}, 1000);
		$('#paramSaveBtn').click();
	},
	getCustomApprovalFlowNode : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/getCustomApprovalFlowNode.spring",
			data : {
				customApprovalFlowNodeID : param.get("customApprovalFlowNodeID"),
				appCode : param.get("appCode")
			},
			success : function(data) {
				if (data.customApprovalFlowNode) {
					
					if (customApprovalFlowNodeEdit.hasLoop && customApprovalFlowNodeEdit.hasLoop != data.customApprovalFlowNode.customApprovalFlowNodeCode) {
						$("input[name=approvalFlowNodeType][value=2]").attr("disabled", "disabled");
						layui.form.render("radio", "approvalFlowNodeTypeDiv");
					}
					
					param.set(null, data.customApprovalFlowNode);
					customApprovalFlowNodeEdit.getCustomForm(data.customApprovalFlowNode.approvalCustomFormCode);
					
					// 回显单条件的字段业务编号和字段值，这些数据是自动合成表达式保存的，数据库没有相应字段保存
					if (data.customApprovalFlowNode.approvalConditionType == 0 && data.customApprovalFlowNode.approvalConditionConfig) {
						var approvalConditionBusinessCodeAry = data.customApprovalFlowNode.approvalConditionConfig.match(/\$\[.+\]/);
						if (approvalConditionBusinessCodeAry[0]) {
							$("input[approvalConditionBusinessCode]").val(approvalConditionBusinessCodeAry[0].substr(2, approvalConditionBusinessCodeAry[0].length - 3));
						}
						
						var approvalConditionBusinessValueAry = data.customApprovalFlowNode.approvalConditionConfig.match(/\$\{.+\}/);
						if (approvalConditionBusinessValueAry[0]) {
							$("input[approvalConditionBusinessValue]").val(approvalConditionBusinessValueAry[0].substr(2, approvalConditionBusinessValueAry[0].length - 3));
						}
						
						var approvalConditionSymbol = data.customApprovalFlowNode.approvalConditionConfig.replace(approvalConditionBusinessCodeAry[0], "").replace(approvalConditionBusinessValueAry[0], "").trim();
						$("select[approvalConditionSymbol]").next().find("dd[lay-value='" + approvalConditionSymbol + "']").click();
					}
					
					// 这里如果approvalFlowNodeData数据不是正确的json格式可能会出错，但不影响功能，只是不会回显，暂时不做特殊处理
					var approvalFlowNodeDataObj = JSON.parse(data.customApprovalFlowNode.approvalFlowNodeData);
					approvalFlowNodeData.set(null, approvalFlowNodeDataObj);
					customApprovalFlowNodeEdit.approvalFlowNodeType = data.customApprovalFlowNode.approvalFlowNodeType;
					if (approvalFlowNodeDataObj.approvalUIDs) {
						var approvalUIDs = [];
						if (data.customApprovalFlowNode.approvalFlowNodeType == 0) {
							approvalUIDs.push(approvalFlowNodeDataObj.approvalUIDs);
						} else {
							approvalUIDs.push("");// 存在两个approvalUIDs，回显需要处理一下，如果是会签节点，需要回显到第二个approvalUIDs
							approvalUIDs.push(approvalFlowNodeDataObj.approvalUIDs);
						}
						approvalFlowNodeData.set("approvalUIDs", approvalUIDs);
						customApprovalFlowNodeEdit.getApprovalUsers(approvalFlowNodeDataObj.approvalUIDs);
					}
				} else {
					if (customApprovalFlowNodeEdit.hasLoop) {
						$("input[name=approvalFlowNodeType][value=2]").attr("disabled", "disabled");
						layui.form.render("radio", "approvalFlowNodeTypeDiv");
					}
				}
			}
		});
	},
	getCustomApprovalFlowNodeList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/getCustomApprovalFlowNodeList.spring",
			data : {
				appCode : param.get("appCode"),
				customApprovalFlowCode : param.get("customApprovalFlowCode"),
			},
			success : function(data) {
				var seqNo = param.get("seqNo");
				var $select = $("select[name='beginNode']");
				var $approvalFieldBelongNode = $("select[name='approvalFieldBelongNode']");
				var html = "";
				for (var i = 0; i < data.customApprovalFlowNodeList.length; i++) {
					
					if (data.customApprovalFlowNodeList[i].approvalFlowNodeType == 2) {// 循环节点
						customApprovalFlowNodeEdit.hasLoop = data.customApprovalFlowNodeList[i].customApprovalFlowNodeCode;
					}
					
					if (data.customApprovalFlowNodeList[i].seqNo >= seqNo) {
						continue;
					}
					html += '<option value="' + data.customApprovalFlowNodeList[i].customApprovalFlowNodeCode + '">' + data.customApprovalFlowNodeList[i].customApprovalFlowNodeName + '</option>';
				}
				$select.append(html);
				$approvalFieldBelongNode.append(html);
			}
		});
	},
	saveCustomApprovalFlowNode : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlow/saveCustomApprovalFlowNode.spring",
			data : param.__form() + "&approvalFlowNodeData=" + encodeURIComponent(JSON.stringify(approvalFlowNodeData.__json())),
			type : "post",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					parent.customApprovalFlowNode.getCustomApprovalFlowNodeList();
					assemblys.closeWindow();
				});
			}
		});
	},
	getCustomForm : function(customFormCode) {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomForm.spring",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : param.get("appCode")
			},
			success : function(data) {
				if (!data.customForm) {
					data.customForm = {
						customFormName : ""
					}
				}
				param.set("approvalCustomFormName", data.customForm.customFormName);
			}
		});
	},
	getApprovalUsers : function(approvalUIDs) {
		return $.ajax({
			url : basePath + "frame/useraction/getUserListByUIDs.spring",
			data : {
				"UIDs" : approvalUIDs
			},
			success : function(data) {
				var names = [];
				var uidArr = approvalUIDs.split(",");
				for (var i = 0; i < uidArr.length; i++) {
					for (var j = 0; j < data.userList.length; j++) {
						if(uidArr[i] == data.userList[j].uID){
							names.push(data.userList[j].userCode+"-"+data.userList[j].userName);
						}
					}
				}
				$("input[name=approvalUIDs]").prev("input:visible").val(names.join(","));
			}
		});
	},
	toSelectApprovalUser : function(obj) {
		window.__multipleSelectParam = {
			placeholder : "用户名称",
			URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
			param : {
				funCode : param.get("appCode") + "_CUSTOM_APPROVER",
				rightPoint : 5,
			},
			field : {
				name : "userName",
				value : "uID"
			},
			parseData : function(data) {
				return data.users;
			},
			values : (function() {
				var values = [];
				var nameArr = null;
				var valueArr = null;
				if (customApprovalFlowNodeEdit.approvalFlowNodeType == 0) {
					if (approvalFlowNodeData.get("approvalUIDs")[0]) {
						nameArr = approvalFlowNodeData.get("approvalNames")[0].split(",");
						valueArr = approvalFlowNodeData.get("approvalUIDs")[0].split(",");
					}
				} else {
					if (approvalFlowNodeData.get("approvalUIDs")[1]) {
						nameArr = approvalFlowNodeData.get("approvalNames")[1].split(",");
						valueArr = approvalFlowNodeData.get("approvalUIDs")[1].split(",");
					}
				}
				if(valueArr){
					for (var i = 0; i < valueArr.length; i++) {
						var rightObj = {
							name : nameArr[i],
							value : valueArr[i]
						}
						values.push(rightObj)
					}
				}
				
				return values;
			})(),
			callback : function(data) {
				var names = [];
				var values = [];
				for (var i = 0; i < data.length; i++) {
					names.push(data[i].name);
					values.push(data[i].value);
				}
				obj.value = names.join(",");
				$(obj).next().val(values.join(","));
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toSelectApprovalCustomFormCode",
			area : [ '800px', '800px' ],
			title : "选择用户",
			scrollbar : false,
			content : basePath + "plugins/components/multipleSelect/multipleSelect.html"
		});
		
	},
	toSelectApprovalCustomFormCode : function(obj) {
		window.__singleSelectParam = {
			parentName : "表单分类",
			parentURL : basePath + "frame/customFormType/getCustomFormTypeList.spring",
			parentField : {
				name : "customFormTypeName",
				value : "customFormTypeCode"
			},
			parentParam : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
			},
			parentParseData : function(data) {
				return data.customFormTypeList;
			},
			placeholder : "表单名称",
			URL : basePath + "frame/newCustomForm/getCustomFormList.spring",
			param : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
				customFormClass : "1"
			},
			field : {
				name : "customFormName",
				value : "customFormCode"
			},
			parseData : function(data) {
				return data.customFormList;
			},
			callback : function(name, value) {
				obj.value = name;
				$(obj).next().val(value);
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toSelectApprovalCustomFormCode",
			area : [ '500px', '900px' ],
			title : "选择表单",
			scrollbar : false,
			content : basePath + "plugins/components/singleSelect/singleSelect.html"
		});
		
	},
	clearApprovalCustomFormValue : function() {
		param.set("approvalCustomFormCode", "");
		param.set("approvalCustomFormName", "");
	}
}