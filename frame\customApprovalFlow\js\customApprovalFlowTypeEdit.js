var customApprovalFlowTypeEdit = {
	init : function() {
		customApprovalFlowTypeEdit.getCustomApprovalFlowType().then(function() {
			return assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		}).then(function() {
			return customApprovalFlowTypeEdit.getExceAppList();
		}).then(function() {
			$("span[titleName]").text("流程分类");
			customApprovalFlowTypeEdit.initLayui();
		});
	},
	initLayui : function() {
		layui.form.verify({
			businessCode : function(value, item) {
				if (value && customApprovalFlowTypeEdit.businessCodeCheck(value)) {
					return "业务编号已存在";
				}
			}
		});
		layui.form.on("submit(save)", function() {
			customApprovalFlowTypeEdit.saveCustomApprovalFlowType();
			return false;
		});
		layui.form.render();
	},
	businessCodeCheck : function(businessCode) {
		var flag = false;
		$.ajax({
			url : basePath + "frame/customApprovalFlowType/businessCodeCheck.spring",
			data : {
				"compNo" : param.get("compNo"),
				"appCode" : param.get("appCode"),
				"businessCode" : businessCode,
				"customApprovalFlowTypeCode" : param.get("customApprovalFlowTypeCode")
			},
			dataType : "json",
			async : false,
			success : function(data) {
				flag = data.has;
			}
		});
		return flag;
	},
	getCustomApprovalFlowType : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/getCustomApprovalFlowType.spring",
			data : {
				customApprovalFlowTypeID : param.get("customApprovalFlowTypeID"),
				appCode : param.get("appCode")
			},
			success : function(data) {
				if (data.customApprovalFlowType) {
					param.set(null, data.customApprovalFlowType);
				}
			}
		});
	},
	saveCustomApprovalFlowType : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/saveCustomApprovalFlowType.spring",
			data : param.__form(),
			type : "post",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					parent.page.set("curPageNum", 1);
					parent.customApprovalFlowTypeList.getCustomApprovalFlowTypeList();
					assemblys.closeWindow();
				});
			}
		});
	},
	getExceAppList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/getExceAppList.spring",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var appList = data.appList;
				var $select = $("select[appCode]");
				var html = "";
				var appCode = param.get("appCode");
				for (var i = 0; i < appList.length; i++) {
					if (appList[i].compAppID != 0) {
						html += '<option value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
					}
				}
				$select.append(html);
			}
		});
	},
}