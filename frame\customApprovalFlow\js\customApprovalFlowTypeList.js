var customApprovalFlowTypeList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			return customApprovalFlowTypeList.getExceAppList();
		}).then(function() {
			assemblys.initSessionStoragePram();
			$("span[titleName]").text("流程分类");
			customApprovalFlowTypeList.getCustomApprovalFlowTypeList();
			customApprovalFlowTypeList.initLayui();
		});
	},
	initLayui : function() {
		layui.form.on("switch(state)", function(data) {
			customApprovalFlowTypeList.updateCustomApprovalFlowTypeState(data)
		});
		
		layui.form.on("select(state)", function(data) {
			customApprovalFlowTypeList.getCustomApprovalFlowTypeList();
		});
		
		layui.form.on("select(appCode)", function(data) {
			customApprovalFlowTypeList.getCustomApprovalFlowTypeList();
			localStorage.setItem("CUSTOM_APPCODE_VALUE", data.value);
		});
	},
	updateCustomApprovalFlowTypeState : function(data) {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/updateCustomApprovalFlowTypeState.spring",
			data : {
				state : data.elem.checked ? 1 : 0,
				customApprovalFlowTypeID : data.elem.getAttribute("customApprovalFlowTypeID"),
				appCode : param.get("appCode")
			},
			type : "post",
			success : function(data) {
				assemblys.msg("修改状态成功", function() {
					customApprovalFlowTypeList.getCustomApprovalFlowTypeList();
				});
			}
		});
	},
	getCustomApprovalFlowTypeList : function() {
		assemblys.tableRender({
			elem : '#list',
			url : basePath + 'frame/customApprovalFlowType/getCustomApprovalFlowTypePager.spring?' + param.__form(),
			cols : [ [ {
				title : '序号',
				align : "center",
				type : 'numbers'
			}, {
				title : '操作',
				width : 100,
				align : "center",
				templet : function(d) {
					var html = '';
					html += '<i class="layui-icon layui-icon-edit i_icon" title="编辑" lay-event="toEdit"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" style="cursor: pointer" lay-event="del"></i>';
					html += '<i class="layui-icon2 i_icon i_icon" title="导出SQL" style="cursor: pointer" lay-event="exportSQL">&#xea80;</i>';
					return html;
				}
			}, {
				title : '流程分类名称',
				align : "left",
				minWidth : 200,
				templet : function(d) {
					return '<a lay-event="toEdit">' + assemblys.htmlEncode(d.customApprovalFlowTypeName) + '</a>';
				}
			}, {
				title : '业务编号',
				width : 150,
				align : "left",
				templet : function(d) {
					return assemblys.htmlEncode(d.businessCode);
				}
			}, {
				title : '创建人',
				align : "center",
				width : 160,
				templet : function(d) {
					return d.createUserName + "<br>" + assemblys.dateToStr(d.createDate);
				}
			}, {
				title : '顺序号',
				align : "center",
				width : 80,
				field : "seqNo"
			}, {
				title : '状态',
				align : "center",
				width : 100,
				templet : function(d) {
					return '<input type="checkbox" customApprovalFlowTypeID="' + d.customApprovalFlowTypeID + '" lay-filter="state" value="1" lay-skin="switch" ' + (d.state == 1 ? "checked" : "") + ' lay-text="有效|无效"/>';
				}
			} ] ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
			},
			events : {
				toEdit : customApprovalFlowTypeList.toEdit,
				exportSQL : customApprovalFlowTypeList.exportSQL,
				del : customApprovalFlowTypeList.deleteCustomApprovalFlowType
			}
		});
		
	},
	deleteCustomApprovalFlowType : function(d) {
		assemblys.confirm("确定删除『" + assemblys.htmlEncode(d.customApprovalFlowTypeName) + "』吗？", function() {
			$.ajax({
				url : basePath + "frame/customApprovalFlowType/deleteCustomApprovalFlowType.spring",
				data : {
					"customApprovalFlowTypeCode" : d.customApprovalFlowTypeCode,
					"appCode" : param.get("appCode")
				},
				dataType : "json",
				type : "post",
				success : function(data) {
					assemblys.msg("删除成功", function() {
						customApprovalFlowTypeList.getCustomApprovalFlowTypeList();
					});
				}
			});
		});
	},
	getExceAppList : function() {
		return $.ajax({
			url : basePath + "frame/customApprovalFlowType/getExceAppList.spring",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var appList = data.appList;
				var $select = $("select[name='appCode']");
				var html = "";
				var customAppCode = localStorage.getItem("CUSTOM_APPCODE_VALUE");
				
				var currAppCode = param.get("currAppCode");
				if (!currAppCode) {
					$('.hide-appCode').removeClass('layui-hide');
					var appCode = param.get("appCode");
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0) {
							var selected = customAppCode == appList[i].appCode ? "selected" : "";
							html += '<option ' + selected + ' value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
						}
					}
				} else {
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0 && currAppCode == appList[i].appCode) {
							html += '<option value="' + appList[i].appCode + '" selected>' + appList[i].appName + '</option>';
						}
					}
				}
				$select.append(html);
				layui.form.render();
			}
		});
	},
	toEdit : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			closeBtn : 0,
			area : [ '900px', '450px' ],
			title : false,
			scrollbar : false,
			content : "customApprovalFlowTypeEdit.html?funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&compNo=" + param.get("compNo") + "&customApprovalFlowTypeID=" + d.customApprovalFlowTypeID
		});
	},
	exportSQL : function(d) {
		location.href = basePath + "frame/customApprovalFlowType/exportFlowType.spring?appCode=" + param.get("appCode") + "&customApprovalFlowTypeCode=" + d.customApprovalFlowTypeCode
	}
}