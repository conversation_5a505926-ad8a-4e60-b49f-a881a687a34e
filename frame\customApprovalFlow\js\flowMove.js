var flowMove = {
	init : function(beforeMove, moved) {
		flowMove.on(beforeMove, moved);
	},
	on : function(beforeMove, moved) {
		$(document).on("mousedown", "div.flow-move", function(e) {
			e.preventDefault();
			var that = this;
			if (window.event.button == 1 || e.button == 0) {
				flowMove.timeout = setTimeout(function() {
					flowMove.beforeMove();
					beforeMove && beforeMove();
					flowMove.move(e, that, moved);
				}, 200);
			} else {
				return;
			}
		});
		
		$(document).on("mouseup", "div.flow-move", function(e) {
			clearInterval(flowMove.timeout);
		})

	},
	move : function(e, moveElement, moved) {
		
		var this_item = $(moveElement);
		var clone = this_item.clone().appendTo(this_item.parent());
		
		var $bodys = $("div.bodys");
		var height = $bodys.outerHeight();
		var scrollHeight = $bodys[0].scrollHeight;
		var scrollTop = $bodys.scrollTop();
		scrollTop = (scrollHeight - height) / 2 - scrollTop;
		
		// 鼠标点击时的X坐标
		var offsetX = e.offsetX;
		var offsetY = e.offsetY;
		var pageX = e.pageX;
		var pageY = e.pageY;
		// 当前元素相对于父元素的水平偏移量
		var x, y;
		clone.css({
			'position' : 'absolute',
			'left' : (e.pageX - offsetX) + 'px',
			'top' : (e.pageY - offsetY - scrollTop) + 'px',
			'opacity' : '.65',
			'z-index' : 1000
		});
		
		$(document).mousemove(function(e) {
			clone.css({
				'left' : (e.pageX - offsetX) + 'px',
				'top' : (e.pageY - offsetY - scrollTop) + 'px'
			});
			
			return;
		});
		
		var i = 0;
		$(document).one('mouseup', function(e) { // 鼠标放开时判断移动的目标类名含有的字段，进行接下来的操作
			clone.remove();
			$(document).off('mousemove');
			
			var e = e || window.event;
			
			var pageX = e.pageX;
			var pageY = e.pageY;
			var element = document.elementFromPoint(pageX, pageY);
			// IE8下需要执行2次才有效果
			if (layui.device().ie == 8) {
				element = document.elementFromPoint(pageX, pageY);
			}
			
			var $flowMove = $(element).hasClass("flow-move") ? $(element) : $(element).parents("div.flow-move");
			// notFirst == 1 不能是第一节点
			if ($flowMove.length > 0 && $flowMove != this_item && (this_item.attr("notFirst") == 0 || (this_item.attr("notFirst") == 1 && $flowMove.index() > 0))) {
				$flowMove.before(this_item);
				moved && moved();
			}
			
			flowMove.moved();
		});
		
	},
	moved : function() {
		$(".flow-move").removeClass("flow-move-style");
		$(".approval_module, .approval_module_0").css("margin-bottom", "0px");
		$(".i_shuxian2,.i_shuxian,.loop-node").removeClass("layui-hide");
	},
	beforeMove : function() {
		$(".flow-move").addClass("flow-move-style");
		$(".approval_module, .approval_module_0").css("margin-bottom", "40px");
		$(".i_shuxian2,.i_shuxian,.loop-node").addClass("layui-hide");
	}
}