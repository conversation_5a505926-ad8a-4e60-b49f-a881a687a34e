SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'CustomApprovalFlow' 

-- sqlSplit

DROP TABLE IF EXISTS `CustomApprovalFlowType`;

-- sqlSplit

CREATE TABLE `CustomApprovalFlowType`  (
  `CustomApprovalFlowTypeID` int NOT NULL AUTO_INCREMENT,
  `CustomApprovalFlowTypeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '流程分类编号',
  `CustomApprovalFlowTypeName` varchar(200) NULL DEFAULT '' COMMENT '流程分类名称',
  `AppCode` varchar(200) NULL DEFAULT '' COMMENT '应用编号',
  `BusinessCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '业务编号',
  `CompNo` varchar(200) NULL DEFAULT '' COMMENT '公司编号',
  `State` int NULL DEFAULT 1 COMMENT '状态（0：无效，1：有效，-1：删除）',
  `Remark` text NULL COMMENT '备注',
  `SeqNo` int(11) NULL DEFAULT 0 COMMENT '顺序号',
  `CreateUID` varchar(200) NULL DEFAULT '' COMMENT '创建人工号（唯一）',
  `CreateUserName` varchar(200) NULL DEFAULT '' COMMENT '创建人姓名',
  `CreateDate` datetime NULL COMMENT '创建时间',
  `OptUID` varchar(200) NULL DEFAULT '' COMMENT '操作人工号（唯一）',
  `OptUserName` varchar(200) NULL DEFAULT '' COMMENT '操作人姓名',
  `OptDate` datetime NULL COMMENT '操作时间',
  PRIMARY KEY (`CustomApprovalFlowTypeID`),
  UNIQUE INDEX `CustomApprovalFlowType_CustomApprovalFlowTypeCode_INDEX`(`CustomApprovalFlowTypeCode`)
);

-- sqlSplit

DROP TABLE IF EXISTS `CustomApprovalFlow`;

-- sqlSplit

CREATE TABLE `CustomApprovalFlow`  (
  `CustomApprovalFlowID` int NOT NULL AUTO_INCREMENT,
  `CustomApprovalFlowCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '流程编号',
  `CustomApprovalFlowName` varchar(200) NULL DEFAULT '' COMMENT '流程名称',
  `CustomApprovalFlowTypeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '流程分类编号',
  `Remark` text NULL COMMENT '备注',
  `AppCode` varchar(200) NULL DEFAULT '' COMMENT '应用编号',
  `State` int NULL DEFAULT 1 COMMENT '状态（0：无效，1：有效，-1：删除）',
  `BusinessCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '业务编号',
  `CompNo` varchar(200) NULL DEFAULT '' COMMENT '公司编号',
  `SeqNo` int(11) NULL DEFAULT 0 COMMENT '顺序号',
  `CreateUID` varchar(200) NULL DEFAULT '' COMMENT '创建人工号（唯一）',
  `CreateUserName` varchar(200) NULL DEFAULT '' COMMENT '创建人姓名',
  `CreateDate` datetime NULL COMMENT '创建时间',
  `OptUID` varchar(200) NULL DEFAULT '' COMMENT '操作人工号（唯一）',
  `OptUserName` varchar(200) NULL DEFAULT '' COMMENT '操作人姓名',
  `OptDate` datetime NULL COMMENT '操作时间',
  PRIMARY KEY (`CustomApprovalFlowID`),
  UNIQUE INDEX `CustomApprovalFlow_CustomApprovalFlowCode_INDEX`(`CustomApprovalFlowCode`)
);

-- sqlSplit

DROP TABLE IF EXISTS `CustomApprovalFlowNode`;

-- sqlSplit

CREATE TABLE `CustomApprovalFlowNode`  (
  `CustomApprovalFlowNodeID` int NOT NULL AUTO_INCREMENT,
  `CustomApprovalFlowNodeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '节点编号',
  `CustomApprovalFlowNodeName` varchar(200) NULL DEFAULT '' COMMENT '节点名称',
  `CustomApprovalFlowCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '流程编号',
  `ApprovalCustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '审批表单编号',
  `ApprovalCondition` varchar(200) NULL DEFAULT '' COMMENT '审批条件（skip：跳过审批，into：进入审批，空串： 无，默认空串）',
  `ApprovalConditionType` int NULL DEFAULT 0 COMMENT '条件类型（0：单条件，1：表达式，默认0）',
  `ApprovalConditionConfig` text NULL COMMENT '表达式',
  `ApprovalFlowNodeType` int NULL DEFAULT 0 COMMENT '节点类型（0：普通，1：动态，2：循环，3：会签）',
  `ApprovalFlowNodeData` longtext NULL COMMENT '节点审核人数据',
  `BusinessCode` varchar(200) NULL DEFAULT '' COMMENT '业务编号',
  `CompNo` varchar(200) NULL DEFAULT '' COMMENT '公司编号',
  `SeqNo` int(11) NULL DEFAULT 0 COMMENT '顺序号',
  `CreateUID` varchar(200) NULL DEFAULT '' COMMENT '创建人工号（唯一）',
  `CreateUserName` varchar(200) NULL DEFAULT '' COMMENT '创建人姓名',
  `CreateDate` datetime NULL COMMENT '创建时间',
  `OptUID` varchar(200) NULL DEFAULT '' COMMENT '操作人工号（唯一）',
  `OptUserName` varchar(200) NULL DEFAULT '' COMMENT '操作人姓名',
  `OptDate` datetime NULL COMMENT '操作时间',
  PRIMARY KEY (`CustomApprovalFlowNodeID`),
  UNIQUE INDEX `CustomApprovalFlowNode_CustomApprovalFlowNodeCode_INDEX`(`CustomApprovalFlowNodeCode`)
);

-- sqlSplit

DROP TABLE IF EXISTS `ApprovalBelongFlowNode`;

-- sqlSplit

CREATE TABLE `ApprovalBelongFlowNode`  (
  `ApprovalBelongFlowNodeID` int NOT NULL AUTO_INCREMENT,
  `ApprovalBelongFlowNodeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '审批归属流程节点编号',
  `ApprovalBelongFlowNodeName` varchar(200) NULL DEFAULT '' COMMENT '节点名称',
  `CustomApprovalFlowCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '流程编号',
  `CustomApprovalFlowNodeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '节点编号',
  `ApprovalBelongCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '审批归属编号',
  `ApprovalCustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '审批表单编号',
  `ApprovalCondition` varchar(200) NULL DEFAULT '' COMMENT '审批条件（Skip：跳过审批，Into：进入审批，空串： 无，默认空串）',
  `ApprovalConditionType` int NULL DEFAULT 0 COMMENT '条件类型（0：单条件，1：表达式，默认0）',
  `ApprovalConditionConfig` text NULL COMMENT '表达式',
  `ApprovalFlowNodeType` int NULL DEFAULT 0 COMMENT '节点类型（0：普通，1：循环，2：动态，3：会签）',
  `ApprovalFlowNodeData` longtext NULL COMMENT '审批数据',
  `FinalApprovalUIDs` text NULL COMMENT '最终审核人',
  `OptUID` varchar(200) NULL DEFAULT '' COMMENT '操作人UID',
  `OptUserName` varchar(200) NULL DEFAULT '' COMMENT '操作人姓名',
  `OptDate` datetime NULL COMMENT '操作时间',
  `BusinessCode` varchar(200) NULL DEFAULT '' COMMENT '业务编号',
  `ApprovalIndex` int NOT NULL COMMENT '审批流程下标（第几节点）',
  `Current` int NOT NULL DEFAULT 0 COMMENT '是否当前节点（1：是，0不是，默认0）',
  `AppCode` varchar(200) NULL DEFAULT '' COMMENT '应用编号',
  `State` int NULL DEFAULT 0 COMMENT '节点状态（0：待审批，1：已审批，2：回退）',
  `Index` int NULL DEFAULT 0 COMMENT '第几次流转到当前节点，每次回退到节点计数+1',
  `CompNo` varchar(200) NULL COMMENT '公司编号',
  PRIMARY KEY (`ApprovalBelongFlowNodeID`),
  UNIQUE INDEX `ApprovalBelongFlowNode_ApprovalBelongFlowNodeCode_INDEX`(`ApprovalBelongFlowNodeCode`),
  INDEX `ApprovalBelongFlowNode_ApprovalBelongCode_INDEX`(`ApprovalBelongCode`)
);

-- sqlSplit

DROP TABLE IF EXISTS `ApprovalBelongFlowNodeApprover`;

-- sqlSplit

CREATE TABLE `ApprovalBelongFlowNodeApprover`  (
  `ApprovalBelongFlowNodeApproverID` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `ApprovalBelongFlowNodeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '审批节点编号',
  `FunCode` varchar(200) NULL COMMENT '功能编号',
  `FinalApprovalUIDs` text NULL COMMENT '审批人UID, \",\"隔开',
  PRIMARY KEY (`ApprovalBelongFlowNodeApproverID`),
  UNIQUE INDEX `ApprovalBelongFlowNodeApprover_FunCode_INDEX`(`ApprovalBelongFlowNodeCode`, `FunCode`)
);

-- sqlSplit

DROP TABLE IF EXISTS `ApprovalBelongFlowNodeRecord`;

-- sqlSplit

CREATE TABLE `ApprovalBelongFlowNodeRecord`  (
  `ApprovalBelongFlowNodeRecordID` int NOT NULL AUTO_INCREMENT,
  `ApprovalBelongFlowNodeRecordCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '审批记录编号',
  `ApprovalBelongFlowNodeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '审批归属流程节点编号',
  `ApprovalBelongFlowNodeState` int NULL DEFAULT 0 COMMENT '审批归属对象当前状态',
  `ApprovalBelongCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '审批归属编号',
  `ApprovalIndex` int NULL DEFAULT 0 COMMENT '审批节点下标',
  `ApprovalContent` text NULL COMMENT '审批内容',
  `ApprovalUID` varchar(200) NULL COMMENT '审批人工号（唯一）',
  `ApprovalUserName` varchar(200) NULL COMMENT '审批人姓名',
  `ApprovalDeptID` int NULL COMMENT '审批科室',
  `CreateDate` datetime NULL COMMENT '创建时间',
  `CompNo` varchar(200) NULL COMMENT '公司编号',
  `ApprovalType` int NULL DEFAULT 0 COMMENT '审批类型（0：正常审批，1：协助审批）',
  `Index` int NULL DEFAULT 0 COMMENT '对应ApprovalBelongFlowNode.Index',
  PRIMARY KEY (`ApprovalBelongFlowNodeRecordID`),
  UNIQUE INDEX `UNIQUE_ApprovalBelongFlowNodeRecordCode_INDEX`(`ApprovalBelongFlowNodeRecordCode`),
  INDEX `ApprovalBelongFlowNodeRecord_ApprovalBelongFlowNodeCode_INDEX`(`ApprovalBelongFlowNodeCode`),
  INDEX `ApprovalBelongFlowNodeRecord_ApprovalBelongCode_INDEX`(`ApprovalBelongCode`)
);

-- sqlSplit

CREATE TABLE `ApprovalBelongFlowNodeAssist`  (
  `ApprovalBelongFlowNodeAssistID` int NOT NULL AUTO_INCREMENT,
  `ApprovalBelongFlowNodeAssistCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '审批协助编号',
  `ApprovalBelongFlowNodeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '审批归属流程节点编号',
  `AssistContent` text NULL COMMENT '协助原因',
  `AssistUID` varchar(200) NULL DEFAULT '' COMMENT '协助人UID',
  `AssistUserName` varchar(200) NULL DEFAULT '' COMMENT '协助人姓名',
  `AssistDate` datetime NULL COMMENT '申请协助时间',
  `Index` int NULL DEFAULT 0 COMMENT '第几次流转到当前节点，每次回退到节点计数+1',
  PRIMARY KEY (`ApprovalBelongFlowNodeAssistID`),
  UNIQUE INDEX `ApprovalBelongFlowNodeAssistCode_INDEX`(`ApprovalBelongFlowNodeAssistCode`)
);

