SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'approvalbelongflownode' AND COLUMN_NAME = 'CustomApprovalFlowBusinessCode'

-- sqlSplit

ALTER TABLE `approvalbelongflownode` 
ADD COLUMN `CustomApprovalFlowBusinessCode` varchar(255) NULL COMMENT '流程或者流程分类业务编号';

-- sqlSplit

UPDATE approvalbelongflownode abfn 
INNER JOIN customapprovalflow caf ON caf.customapprovalflowCode = abfn.customapprovalflowCode AND caf.BusinessCode IS NOT NULL AND caf.BusinessCode <> ''
SET abfn.CustomApprovalFlowBusinessCode = caf.BusinessCode;

-- sqlSplit

UPDATE approvalbelongflownode abfn 
LEFT JOIN customapprovalflow caf ON caf.customapprovalflowCode = abfn.customapprovalflowCode 
INNER JOIN customapprovalflowtype caft ON caft.customapprovalflowtypeCode = caf.customapprovalflowtypeCode AND caft.BusinessCode IS NOT NULL AND caft.BusinessCode <> ''
SET abfn.CustomApprovalFlowBusinessCode = caft.BusinessCode WHERE abfn.CustomApprovalFlowBusinessCode IS NULL;