SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'approvalbelongflownode' AND COLUMN_NAME = 'ApprovalFieldBelongNode'

-- sqlSplit

ALTER TABLE `approvalbelongflownode` 
ADD COLUMN `ApprovalFieldBelongNode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin default '' COMMENT '审批内容归属节点';

-- sqlSplit

ALTER TABLE `customapprovalflownode` 
ADD COLUMN `ApprovalFieldBelongNode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin default '' COMMENT '审批内容归属节点';

-- sqlSplit

ALTER TABLE `approvalbelongflownoderecord` 
ADD COLUMN `CopyUserCodes` longtext NULL COMMENT '抄送人,userCode逗号隔开',
ADD COLUMN `CopyUserNames` longtext NULL COMMENT '抄送人,userName逗号隔开';

-- sqlSplit

ALTER TABLE `approvalbelongflownoderecorddraft` 
ADD COLUMN `CopyUserCodes` longtext NULL COMMENT '抄送人,userCode逗号隔开',
ADD COLUMN `CopyUserNames` longtext NULL COMMENT '抄送人,userName逗号隔开';