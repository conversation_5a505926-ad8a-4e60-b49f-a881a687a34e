SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'approvalbelongflownode' AND COLUMN_NAME = 'ApprovalNodeState'

-- sqlSplit

ALTER TABLE `approvalbelongflownode` 
ADD COLUMN `ApprovalNodeState` int(11) NULL COMMENT '关联表单的状态编码';

-- sqlSplit

ALTER TABLE `customapprovalflownode` 
ADD COLUMN `ApprovalNodeState` int(11) NULL COMMENT '关联表单的状态编码';

-- sqlSplit

ALTER TABLE `approvalbelongflownodeapprover` 
MODIFY COLUMN `ApprovalBelongFlowNodeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '审批节点编号';

-- sqlSplit

ALTER TABLE `approvalbelongflownodeapprover` 
ADD COLUMN `FinalApprovalUserNames` text NULL COMMENT '审批人userName,\",\"隔开';

-- sqlSplit

UPDATE approvalbelongflownodeapprover a SET FinalApprovalUserNames = (SELECT GROUP_CONCAT(UserName) FROM 【FtnAppCommon】.pubuser pu WHERE FIND_IN_SET(pu.UID,a.FinalApprovalUIDs));