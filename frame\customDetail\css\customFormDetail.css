body {
	width: 100%;
	min-width: 805px;
	min-height: 100%;
	padding: 48px 10px 10px;
	background: #eee;
	font-size: 0px;
	margin: 0;
	color: #484848;
	overflow: visible;
	font: 14px Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
}

.head0 {
	width: 100%;
	background: #fff;
	position: absolute;
	top: 0;
	left: 0;
	line-height: 38px;
	padding: 0 10px;
}

.bodys {
	min-width: 840px;
	min-height: auto;
	padding: 10px 15px 15px;
	overflow: auto;
	position: absolute;
	top: 48px;
	bottom: 10px;
	right: 10px;
	left: 10px;
}

html {
	height: 100%;
}

* {
	box-sizing: border-box;
}

.inblock {
	display: inline-block;
}

.h13 {
	height: 13px;
}

.lh13 {
	line-height: 13px;
}

.h28 {
	height: 28px;
}

.lh28 {
	line-height: 28px;
}

.h31 {
	height: 31px;
}

.lh31 {
	line-height: 31px;
}

.layui-table-cell {
	height: inherit;
	line-height: inherit;
}

th .layui-table-cell {
	height: 28px;
	line-height: 28px;
}

.head2_tab {
	border: none;
	width: 100%;
	white-space: normal;
	line-height: 28px;
	height: auto;
}

.head2_tab li {
	height: 28px;
	line-height: 28px;
	padding: 0 20px;
}

.layui-tab-title .layui-this {
	display: inline-block;
	padding: 0 20px;
	white-space: nowrap;
	text-align: center;
	font-size: 14px;
	border: none;
	cursor: pointer;
	opacity: .9;
	color: #009688;
	border-bottom: 2px solid #009688;
}

.layui-tab-title .layui-this:after {
	border: none;
}

.layui-nav-item .main_table_title {
	line-height: 40px;
	height: 40px;
	background: #dbf0e6;
	color: #000 !important;
	border: 1px solid #d0d0d0;
}

.layui-nav-item  .main_table_title {
	line-height: 40px;
	height: 40px;
	background: #dbf0e6;
	color: #000 !important;
	border: 1px solid #d0d0d0;
}

.custom-left-content {
	position: absolute;
	top: 0px;
	left: 00px;
	right: 0px;
	bottom: 00px;
	background-color: white;
	overflow: auto;
	padding: 10px 15px 15px;
}

.custom-right-content {
	width: 300px;
	position: absolute;
	top: 0px;
	right: 0px;
	bottom: 0px;
	background-color: white;
	overflow: auto;
	padding: 10px 15px 15px;
}

.tleft[colspan='3'] {
	width: 75% !important;
}

.layui-tab-content {
	padding: 10px 0 0 0;
}

.left {
	display: inline-block;
	width: 100%;
	vertical-align: top;
	background: none;
}

.left .layui-nav-item {
	margin-bottom: 10px;
	line-height: 40px;
}

.fr {
	float: right;
}

.main_table td {
	width: 25%;
	height: 40px;
	color: #222;
	word-break: break-all;
	word-wrap: break-word;
	white-space: pre-wrap;
}

.layui-nav-child td {
	white-space: normal;
}

.layui-nav-itemed>a .layui-nav-more {
	border-color: transparent transparent #4e4e4e;
}

.left .layui-nav-more {
	border-color: #4e4e4e transparent transparent;
}

.head0 .layui-btn-sm {
	height: 30px;
	line-height: 30px;
	padding: 0 20px;
	font-size: 14px;
	color: rgba(255, 255, 255, .7);
	margin-left: 10px;
}

.void {
	position: absolute;
	left: 50%;
	top: 37%;
	margin: 0 -104px;
	width: 210px;
	height: 90px;
	line-height: 86px;
	font-weight: 700;
	font-size: 45px;
	border: 5px solid #ff3f3f;
	color: #ff3f3f;
	text-align: center;
	z-index: 100;
}

.layui-table td {
	word-break: break-all;
	word-wrap: break-word;
	white-space: pre-wrap;
}