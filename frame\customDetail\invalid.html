<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>作废</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css" />
<link rel="stylesheet" type="text/css" href="css/invalid.css?version=1.0.1.0" />
</head>
<body>
	<form id="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="approvalBelongCode" />
		<input type="hidden" name="appCode" />
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2">&#xe8af;</i>
				<span>作废</span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="作废" lay-submit="" lay-filter="save">
				<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()">
			</div>
		</div>
		<div class="bodys">
			<div class="layui-form-item" >
				<label id="approvalContentTitle" class="layui-form-label">
					<span style="color: #ff0000">*</span>
					作废原因
				</label>
				<div class="layui-input-block" >
					<textarea class="layui-textarea" name="invalidContent" lay-verify="required|limit" limit="200"></textarea>
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" charset="utf-8" src="js/invalid.js?version=*******"></script>
<script type="text/javascript" charset="utf-8">
	$(function() {
		invalid.init();
	});
</script>
</html>
