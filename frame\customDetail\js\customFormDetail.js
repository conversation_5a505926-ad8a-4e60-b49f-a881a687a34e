var customFormDetail = {
	init : function() {
		
		// 图标
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			return initCustomDetail.getCustomDetailRight(param.get("funCode"));
		}).then(function(data) {
			data = data.data;
			initCustomDetail.rightMap = data;
			param.set(null, {
				"customFormCode" : data.customForm.customFormCode,
				"customFormTypeCode" : data.customForm.customFormTypeCode,
				"customFormBusinessCode" : data.customForm.businessCode,
				"approvalBelongCode" : data.customFormFilled.customFormFilledCode
			});
			$("span[titleName]").html(param.get("titleName") + " - " + data.customFormFilled.customFormFilledCode);
			
			// 初始化选项卡
			customFormDetail.intTab();
			
			// 初始化自定义流程
			customFormDetail.initFlowTable();
			
		});
		
	},
	intTab : function() {
		
		// 定义标签选项卡
		var tabList = [ {
			contentID : "formDetail",
			data : {
				state : "1",
			},
			className : "customClass",
			style : {
				"color" : "red"
			},
			title : "表单详情",
			callback : function(data) {
				customFormDetail.initFormDetail(data.contentID);
			}
		}, {
			contentID : "attaList",
			title : "附件信息",
			hasExport : "0"
		}, {
			contentID : "approvalList",
			className : "layui-hide",
			title : "审核记录",
			data : {
				state : "2",
			},
			callback : function(data) {
				customFormDetail.initApprovalList(data.contentID);
			}
		}, {
			contentID : "optLogList",
			title : "操作日志",
			hasExport : "1",
			data : {
				state : "3",
			},
			callback : function(data) {
				customFormDetail.initOptLogList(data.contentID);
			}
		} ]
		initCustomDetail.intTab(".tab", tabList);
		
		// 定义操作按钮
		var buttonList = [ {
			"title" : "编辑表单",
			"className" : "skin-btn-minor",
			// 是否显示
			"show" : function() {
				var isInvalid = initCustomDetail.rightMap.hasInvalidStatus != initCustomDetail.rightMap.status;
				return initCustomDetail.rightMap.hasEditRight && (!initCustomDetail.rightMap.hasInvalidStatus || isInvalid);
			},
			"onclick" : function() {
				// 编辑表单
				getCustomFormDetail.editCustomForm({
					"customFormCode" : param.get("customFormCode"),
					"appCode" : param.get("appCode"),
					"customFormFilledCode" : param.get("customFormFilledCode"),
					"compNo" : param.get("compNo")
				}, function(win) {
					assemblys.msg("提交成功！", function() {
						window.location.reload();
					});
				});
			}
		}, {
			"title" : "导出",
			"className" : "skin-btn-minor",
			"show" : function() {
				var isInvalid = initCustomDetail.rightMap.hasInvalidStatus != initCustomDetail.rightMap.status;
				return !initCustomDetail.rightMap.hasInvalidStatus || isInvalid;
			},
			"onclick" : function() {
				// 调用导出工具
				commonExportUtil.exportWord({
					"data" : initCustomDetail.handleTableOrListExportData("表单详情 - " + param.get("customFormFilledCode")),
					"tabName" : "导出预览",
					"fileName" : "事件报告"
				});
			}
		}, {
			"title" : "作废",
			"className" : "skin-btn-minor",
			"show" : function() {
				var isInvalid = initCustomDetail.rightMap.hasInvalidStatus != initCustomDetail.rightMap.status;
				if (initCustomDetail.rightMap.hasInvalidStatus && !isInvalid) {
					$("body").append("<div class='void'>已作废</div>");
				}
				
				return initCustomDetail.rightMap.hasExecRight && initCustomDetail.rightMap.hasInvalidStatus && isInvalid;
			},
			"onclick" : function() {
				layer.open({
					type : 2,
					skin : 'layui-layer-aems',
					closeBtn : 0,
					area : [ '850px', '200px' ],
					title : "选择用户",
					scrollbar : false,
					title : false,
					content : "invalid.html?appCode=" + param.get("appCode") + "&approvalBelongCode=" + param.get("customFormFilledCode")
				});
			}
		}, param.get("noBack") == "true" ? null : {
			"title" : "返回",
			"className" : "skin-btn-normal",
			"onclick" : function() {
				history.back();
			}
		} ]
		initCustomDetail.initButton(".head0_right", buttonList);
		
	},
	// 表单详情 - 来自【应用接口管理 - getCustomFormHTML 接口】
	initFormDetail : function(contentID) {
		getCustomFormDetail.getCustomFormData({
			"appCode" : param.get("appCode"),
			"compNo" : param.get("compNo"),
			"customFormCode" : param.get("customFormCode"),
			"customFormFilledCode" : param.get("customFormFilledCode"),
			"customFormBusinessCode" : param.get("customFormBusinessCode"),
			// 附件回调 - 自定义附件显示方式  - 和hasAtta参数二选一
			"attaCallback" : function(list) {
				customFormDetail.initAttaList(list);
			},
			// 是否显示附件 - prev 在前、 after在后，不配置或者为0不显示
			"hasAtta" : "prev",
			"dom" : contentID,
			"showLabel" : false
		});
		
	},
	// 审核记录 - 来自【应用接口管理 - getApprovalBelongFlowNodeRecordList 接口】
	initApprovalList : function(contentID) {
		approvalFlow.getApprovalBelongFlowNodeRecordList({
			"appCode" : param.get("appCode"),
			"approvalBelongCode" : param.get("approvalBelongCode"),
			"selector" : "#" + contentID
		});
	},
	/**
	 * 附件列表
	 */
	initAttaList : function(list) {
		var mapping = [ {
			name : "操作",
			opt : [ {
				"icon" : "&#xe7b3;",
				"title" : "查看",
				"show" : function(data) {
					var suffix = data.type.toUpperCase();
					return suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF";
				},
				"onclick" : function(data) {
					pubUploader.preview(data.title, data.url);
				}
			}, {
				"icon" : "&#xe72e;",
				"onclick" : function(data) {
					pubUploader.downLoadAttaPreview(data.title, data.url);
				}
			} ]
		}, {
			name : "附件名称",
			value : "title"
		}, {
			name : "附件大小",
			value : "size"
		}, {
			name : "附件类型",
			value : "type"
		}, {
			name : "操作人",
			value : "createUserName"
		}, {
			name : "操作时间",
			value : "createDate"
		} ]
		// 渲染
		initCustomDetail.initTableList("#attaList", mapping, list);
	},
	// 业务日志记录 - 来自【应用接口管理 - getBizLogList 接口】
	initOptLogList : function(contentID) {
		pubBizsysLog.getBizLogListHtml({
			"appCode" : param.get("appCode"),
			"bizRelationCode" : param.get("approvalBelongCode"),
			"selector" : "#" + contentID,
			// 操作栏
			"opt" : [ {
				"icon" : "&#xe8bb;",
				"show" : function(data) {
					return data.signName == "表单修改";
				},
				"onclick" : function(data) {
					getCustomFormDetail.showCustomFormLog(data.signCode, param.get("appCode"));
				}
			} ]
		});
	},
	/**
	 * 获取当前菜单类型
	 */
	// 菜单信息- 来自【应用接口管理 - getCustomFormTypeMenu 接口】
	getCustomFormTypeMenu : function() {
		return $.ajax({
			url : basePath + "frame/customFormType/getCustomFormTypeMenu.spring",
			type : "get",
			data : {
				"customFormTypeCode" : param.get("customFormTypeCode"),
				"customFormTypeMenuNo" : param.get("funCode"),
				"appCode" : param.get("appCode")
			},
			dataType : "json"
		});
	},
	
	// 流程图 - 来自【应用接口管理 - getApprovalBelongFlowNodeList 接口】
	initFlowTable : function() {
		customFormDetail.getCustomFormTypeMenu().then(function(data) {
			approvalFlow.initFlow({
				"appCode" : param.get("appCode"),
				"funCode" : param.get("funCode"),
				"compNo" : param.get("compNo"),
				"approvalBelongCode" : param.get("approvalBelongCode"),
				"hasApproval" : (data.customFormTypeMenu.customFormTypeMenuType == 0 && (initCustomDetail.rightMap.hasInvalidStatus && initCustomDetail.rightMap.hasInvalidStatus != initCustomDetail.rightMap.status || !initCustomDetail.rightMap.hasInvalidStatus) ? true : false),
				"selector" : "#flowContainer"
			}).then(function(div) {
				if (div) {
					$("div.custom-right-content").removeClass("layui-hide").prev().css("right", "310px");
					//审核记录
					$(".tab li[index='2']").removeClass("layui-hide");
				}
			});
		})

	}
}

customFormDetail.init();