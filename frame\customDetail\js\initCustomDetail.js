;
(function(window) {
	var layui = window.layui;
	var assemblys = window.assemblys;
	if (!layui || !assemblys) {
		alert("缺少layui相关组件（assemblys2.js）");
		return;
	}
	window.initCustomDetail = {
		/**
		 * 初始化选项卡
		 */
		intTab : function(dom, list) {
			// 数据项
			var tabs = []
			var tabContents = []
			for (var i = 0; i < list.length; i++) {
				var temp = list[i];
				tabs.push({
					"tagName" : "li",
					"className" : (temp.className || ""),
					"style" : (temp.style || {}),
					"attr" : {
						"index" : i,
						"hasExport" : !temp.hasExport || temp.hasExport == "1" ? true : false
					},
					"innerHTML" : temp.title,
					"onclick" : function() {
						var _index = $(this).attr("index");
						var _temp = list[_index];
						var _hasExport = _temp["hasExport"] || 0;
						// 样式
						var $contentLI = $(this).parents(dom).find(".layui-tab-content").children("ul").children();
						$contentLI.siblings().addClass("layui-hide");
						$contentLI.eq(_index).removeClass("layui-hide");
						if (_temp.callback && typeof _temp.callback == 'function') {
							var _callbackData = _temp.data || {};
							_callbackData["title"] = _temp.title;
							_callbackData["contentID"] = _temp.contentID;
							_temp.callback(_callbackData);
							if (_hasExport != 1) {
								list[_index].callback = null;
							}
						}
					}
				});
				tabContents.push({
					"id" : temp.contentID || "",
					"tagName" : "li",
					"className" : "layui-nav-item layui-nav-itemed layui-hide",
				});
			}
			var tab = [ {
				"tagName" : "ul",
				"className" : "layui-tab-title head2_tab h28",
				"children" : tabs
			}, {
				"tagName" : "div",
				"className" : "layui-tab-content lr_box",
				"children" : [ {
					"tagName" : "ul",
					"className" : "layui-nav layui-nav-tree left",
					"children" : tabContents
				} ]
			} ];
			assemblys.createElement(tab, $(dom)[0]);
			
			// 触发第一次
			$(dom).find(".layui-tab-title").children(":eq(0)").click();
			
			// 触发回调
			for (var i = 0; i < list.length; i++) {
				var temp = list[i];
				// 回调
				if (temp.callback && typeof temp.callback == 'function' && temp.hasExport == "1") {
					var _callbackData = temp.data || {};
					_callbackData["title"] = temp.title;
					_callbackData["contentID"] = temp.contentID;
					temp.callback(_callbackData);
					list[i].callback = null;
				}
			}
			
		},
		// 处理导出数据
		handleTableOrListExportData : function(fileTitle, leftTitle, rightTitle) {
			var modulers = [];
			// 找出符合条件的模块
			$("li[index][hasExport='true']").each(function() {
				var index = $(this).attr("index");
				var bigTitle = $.trim($(this).text());
				var $content = $(".layui-tab-content").children("ul").children("li:eq(" + index + ")");
				if ($content.children().length == 0) {
					return true;
				}
				var $item = $content.find("li.layui-nav-item");
				if ($item.length > 0) {
					$item.each(function() {
						modulers.push({
							title : bigTitle,
							dom : $(this)
						});
					});
				} else {
					modulers.push({
						title : bigTitle,
						dom : $content
					});
				}
			});
			
			// 动态识别数据
			if (modulers.length > 0) {
				var tempList = [];
				for (var i = 0; i < modulers.length; i++) {
					var temp = modulers[i];
					var bigTitle = temp.title;
					var $content = temp.dom;
					var type = $content.find("thead").length > 0 ? "list" : "table";
					// 数据集合
					var data = [];
					// 表格
					if (type == "table") {
						var $divs = $content.find("div.layui-colla-item");
						if ($divs.length > 0) {
							$divs.each(function() {
								var $h2 = $(this).find("h2:eq(0)").clone();
								$h2.find("i").remove();
								var title = $h2.text();
								var trs = [];
								$(this).find("table.layui-table tr.layui-table-tr").each(function() {
									var tds = [];
									$(this).children("td").each(function() {
										tds.push($.trim($(this).html()));
									});
									trs.push(tds);
								});
								data.push({
									title : title,
									list : trs
								});
							});
						} else if ($content.hasClass("layui-nav-item")) {
							$content.each(function() {
								var title = $(this).find(".main_table_title:eq(0)").text();
								var trs = [];
								$(this).find("table.layui-table tr.layui-table-tr").each(function() {
									var tds = [];
									$(this).children("td").each(function() {
										tds.push($.trim($(this).html()));
									});
									trs.push(tds);
								});
								data.push({
									title : title,
									list : trs
								});
							});
						}
					} else
					// 列表
					if (type == "list") {
						
						var title = "";
						var $title = $content.find(".main_table_title");
						if ($title.length > 0) {
							$title = $title.clone();
							$title.find("span").remove();
							title = $title.text();
						}
						
						var $table = $content.find("table");
						var heads = [];
						var hasFilterOpt = false;
						$table.find("thead th").each(function() {
							var text = $.trim($(this).text());
							if (text == "操作") {
								hasFilterOpt = true;
								return true;
							}
							heads.push(text);
						});
						var trs = [];
						$table.find("tbody tr.layui-table-tr").each(function() {
							var tds = [];
							$(this).children("td").each(function(tdIndex, td) {
								if (tdIndex == 0 && hasFilterOpt) {
									return true;
								}
								tds.push($.trim($(this).html()));
							});
							trs.push(tds);
						});
						data.push(heads);
						data.push(trs);
					}
					tempList.push({
						bigTitle : bigTitle,
						title : title,
						show : "1",
						type : type,
						data : data
					});
					
				}
				var newData = {
					title : fileTitle || "",
					leftTitle : leftTitle || "",
					rightTitle : rightTitle || "",
					list : tempList
				}
			}
			
			return newData;
		},
		/**
		 * 初始化按钮
		 */
		initButton : function(dom, list) {
			for (var i = 0; i < list.length; i++) {
				var temp = list[i];
				if (!temp) {
					continue;
				}
				// 显示条件
				var hasShow = true;
				if (temp.show && typeof temp.show == 'function') {
					hasShow = temp.show(temp);
				}
				if (!hasShow) {
					continue;
				}
				temp["tagName"] = "input";
				temp["attr"] = {
					"type" : "button",
					"value" : temp.title
				};
				temp["className"] = "layui-btn layui-btn-sm h28 lh28 " + (temp.className || "");
			}
			assemblys.createElement(list, $(dom)[0]);
			
		},
		/**
		 * 初始化表单详情的选项卡
		 */
		initModuleList : function(selector, dataList) {
			if (!dataList) {
				dataList = [ {
					name : "分類1",
					id : "module1",
					children : [ {
						"tagName" : "span",
						"innerText" : "dsajfkldsajf"
					} ],
				}, {
					name : "分類2",
					id : "module2",
					text : "<div>123123123</div>",
					children : []
				} ]
			}
			var lis = []
			if (dataList && dataList.length > 0) {
				for (var i = 0; i < dataList.length; i++) {
					var temp = dataList[i];
					var div = {
						"tagName" : "div",
						"id" : temp.id || "",
						"style" : {
							"background" : "#fff"
						}
					}
					if (temp.children instanceof Array) {
						div["children"] = temp.children;
					}
					
					if (temp.text) {
						div["innerHTML"] = temp.text;
					}
					
					var li = [ {
						"tagName" : "li",
						"className" : "layui-nav-item layui-nav-itemed subject eventDetail",
						"style" : {
							"display" : "block"
						},
						children : [ {
							"tagName" : "a",
							"attr" : {
								"href" : "javascript:void(0)"
							},
							"className" : "main_table_title skin-div-css",
							"innerHTML" : temp.name + '<span class="layui-nav-more"></span>'
						}, {
							"tagName" : "dl",
							"className" : "layui-nav-child main_table_box",
							"children" : [ {
								"tagName" : "dd",
								"children" : [ div ]
							
							} ]
						} ]
					} ]
					lis.push(li);
				}
			}
			
			var table = {
				"tagName" : "div",
				"className" : "layui-nav-item layui-nav-itemed",
				"children" : [ {
					"tagName" : "ul",
					"className" : "layui-nav layui-nav-tree left",
					"attr" : {
						"lay-filter" : selector + "UL"
					},
					"children" : lis
				} ]
			}
			assemblys.createElement(table, $(selector)[0]);
			layui.element.render("nav", selector + "UL");
			
		},
		/**
		 * 初始化详情
		 */
		initDetailList : function(selector, dataList) {
			if (!dataList) {
				dataList = [ {
					name : "分类1",
					list : [ {
						title : "独立一行",
						value : "值",
						one : 1
					}, {
						title : "测试数据1",
						value : "值1"
					}, {
						title : "测试数据2",
						value : "值2"
					}, {
						title : "最后一行自动合并",
						value : "值"
					} ]
				}, {
					name : "分类2",
					list : [ {
						title : "测试数据1",
						value : "值1"
					}, {
						title : "独立一行",
						value : "值",
						one : 1
					}, {
						title : "测试数据2",
						value : "值2"
					}, {
						title : "测试数据3",
						value : "值3"
					}, {
						title : "最后一行自动合并",
						value : "值",
						one : 1
					}, {
						title : "测试数据4",
						value : "值4"
					}, {
						title : "测试数据5",
						value : "值5"
					} ]
				} ]
			}
			
			var lis = []
			if (dataList.length > 0) {
				for (var i = 0; i < dataList.length; i++) {
					var temp = dataList[i];
					var trs = [];
					var childrens = temp.list || [];
					if (childrens.length > 0) {
						for (var j = 0; j < childrens.length; j++) {
							var temp2 = childrens[j];
							var nextTemp2 = childrens[j + 1] || null;
							var tds = []
							if (temp2.one && temp2.one == 1 || nextTemp2 == null || nextTemp2.one && nextTemp2.one == 1) {
								tds = [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerText" : temp2.title
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"innerText" : temp2.value
								} ]
							} else {
								tds = [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerText" : temp2.title
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "1"
									},
									"innerText" : temp2.value
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "1"
									},
									"innerText" : nextTemp2.title
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "1"
									},
									"innerText" : nextTemp2.value
								} ];
								j++;
							}
							
							trs.push({
								"tagName" : "tr",
								"className" : "layui-table-tr",
								"children" : tds
							});
							
						}
					}
					var li = [ {
						"tagName" : "li",
						"className" : "layui-nav-item layui-nav-itemed subject eventDetail",
						"style" : {
							"display" : "block"
						},
						children : [ {
							"tagName" : "a",
							"attr" : {
								"href" : "javascript:void(0)"
							},
							"className" : "main_table_title skin-div-css",
							"innerHTML" : temp.name + '<span class="layui-nav-more"></span>'
						}, {
							"tagName" : "dl",
							"className" : "layui-nav-child main_table_box",
							"children" : [ {
								"tagName" : "dd",
								"children" : [ {
									"tagName" : "table",
									"className" : "layui-table main_table detail_table",
									"children" : [ {
										"tagName" : "tbody",
										"children" : trs
									} ]
								} ]
							
							} ]
						} ]
					} ]
					lis.push(li);
				}
			} else {
				lis.push({
					"tagName" : "li",
					"className" : "layui-nav-item layui-nav-itemed subject eventDetail",
					"style" : {
						"display" : "block"
					},
					children : [ {
						"tagName" : "a",
						"attr" : {
							"href" : "javascript:void(0)"
						},
						"className" : "main_table_title skin-div-css",
						"innerHTML" : '<span class="layui-nav-more"></span>'
					}, {
						"tagName" : "dl",
						"className" : "layui-nav-child main_table_box",
						"children" : [ {
							"tagName" : "dd",
							"children" : [ {
								"tagName" : "table",
								"className" : "layui-table main_table detail_table",
								"children" : [ {
									"tagName" : "tbody",
									"children" : [ {
										"tagName" : "tr",
										"className" : "layui-table-tr",
										"children" : [ {
											"tagName" : "td",
											"className" : "tright",
											"style" : {
												"text-align" : "center"
											},
											"attr" : {
												"colspan" : "4"
											},
											"innerText" : "暂无数据"
										} ]
									} ]
								} ]
							} ]
						} ]
					} ]
				});
			}
			var table = {
				"tagName" : "div",
				"className" : "layui-nav-item layui-nav-itemed",
				"children" : [ {
					"tagName" : "ul",
					"className" : "layui-nav layui-nav-tree left",
					"attr" : {
						"lay-filter" : selector + "UL"
					},
					"children" : lis
				} ]
			}
			assemblys.createElement(table, $(selector)[0]);
			layui.element.render("nav", selector + "UL");
		},
		/**
		 * 初始化表格
		 */
		initTableList : function(selector, mapping, list) {
			var ths = [];
			var tbs = [];
			// 有数据
			if (list.length > 0) {
				for (var i = 0; i < list.length; i++) {
					var temp = list[i];
					var trs = {
						"tagName" : "tr",
						"className" : "layui-table-tr",
						"children" : []
					};
					for (var j = 0; j < mapping.length; j++) {
						var map = mapping[j];
						// 头
						if (i == 0) {
							ths.push({
								"tagName" : "th",
								"style" : {
									"text-align" : "center"
								},
								"innerHTML" : map.name
							})
						}
						// 特殊处理
						var value = temp[map.value] || "";
						if ((typeof value === 'number') && value > 1000000000000) {
							value = assemblys.dateToStr(value);
						} else if (value instanceof Date) {
							value = assemblys.dateToStr(value.getTime());
						} else if (value instanceof Object && value.time) {
							value = assemblys.dateToStr(value.time);
						}
						// 操作按钮
						var opts = []
						var optList = map.opt || [];
						if (map.opt instanceof Array) {
							value = "";
							for (var w = 0; w < optList.length; w++) {
								var opt = optList[w];
								var hasShow = true;
								if (opt.show && typeof opt.show == 'function') {
									hasShow = opt.show(temp);
								}
								if (hasShow) {
									opts.push({
										"tagName" : "i",
										"className" : "layui-icon2 " + (opt.className || ""),
										"innerHTML" : opt.icon + " ",
										"title" : opt.title || "",
										"attr" : {
											"row1" : i,
											"row2" : j,
											"index" : w
										},
										"onclick" : function() {
											var _optList = mapping[$(this).attr("row2")].opt;
											var _onclick = _optList[$(this).attr("index")].onclick;
											if (_onclick && typeof _onclick == 'function') {
												var _temp = list[$(this).attr("row1")];
												_onclick(_temp);
											}
										}
									})
								}
							}
						}
						// 自定义
						var templet = map.templet;
						if (templet && typeof templet == 'function') {
							value = map.templet(temp);
						}
						// 内容
						trs["children"].push({
							"tagName" : "td",
							"style" : (j == 0 ? {
								"text-align" : "center",
								"min-width" : "100px"
							} : {
								"min-width" : "100px"
							}),
							"innerHTML" : value,
							"children" : opts
						})
					}
					tbs.push(trs);
				}
			} else {
				
				var index = 0;
				// 无数据
				for (var j = 0; j < mapping.length; j++) {
					var map = mapping[j];
					ths.push({
						"tagName" : "th",
						"style" : {
							"text-align" : "center"
						},
						"innerHTML" : map.name
					});
					index++;
				}
				tbs.push({
					"tagName" : "tr",
					"className" : "layui-table-tr",
					"children" : [ {
						"tagName" : "td",
						"attr" : {
							"colspan" : index
						},
						"style" : {
							"text-align" : "center"
						},
						"innerHTML" : "无相关数据",
						"children" : []
					} ]
				})
			}
			
			// 组装
			var table = {
				"tagName" : "table",
				"className" : "layui-table",
				"children" : [ {
					"tagName" : "thead",
					"children" : [ {
						"tagName" : "tr",
						"className" : "layui-table-tr",
						"children" : ths
					} ]
				}, {
					"tagName" : "tbody",
					"children" : tbs
				} ]
			
			}
			assemblys.createElement(table, $(selector)[0]);
		},
		/**
		 * 打开一个窗口并显示表格
		 */
		initTableListWin : function(title, mapping, list, width, height) {
			if (!width) {
				width = "850px";
			}
			if (!height) {
				height = "500px";
			}
			var id = "SHOW_NEW_WINDOW_" + new Date().getTime();
			layer.open({
				type : 1,
				skin : 'layui-layer-aems',
				title : title,
				scrollbar : false,
				area : [ width, height ],
				content : "<div id='" + id + "' style='padding: 10px;'></div>"
			});
			// 渲染
			initCustomDetail.initTableList("#" + id, mapping, list);
		},
		/**
		 * 获取当前功能权限
		 */
		getCustomDetailRight : function(funCode) {
			return $.ajax({
				url : basePath + "frame/customDetail/getCustomDetailRight.spring",
				type : "get",
				data : {
					"funCode" : funCode,
					"appCode" : param.get("appCode"),
					"customFormFilledCode" : param.get("customFormFilledCode"),
				},
				dataType : "json",
			});
		}
	}

})(window)