var invalid = {
	filterValue : [],
	init : function() {
		invalid.initLayui();
	},
	initLayui : function() {
		layui.form.on("submit(save)", function() {
			invalid.saveInvalid();
			return false;
		});
	},
	saveInvalid : function() {
		assemblys.confirm("确定作废『" + param.get("approvalBelongCode") + "』吗？", function() {
			var url = basePath + "frame/customDetail/saveInvalid.spring";
			$.ajax({
				url : url,
				type : "post",
				data : param.__form(),
				dataType : "json",
				success : function(data) {
					assemblys.msg('已作废', function() {
						parent.location.reload();
						assemblys.closeWindow();
					});
				}
			});
		});
	},
}