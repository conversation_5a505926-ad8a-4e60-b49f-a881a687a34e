.table_right_main td {
	height: 40px;
	position: relative;
}

tr[customFieldRowCode] td[colspan='12'] {
	width: 100%;
}

tr[customFieldRowCode] td[colspan='6'] {
	width: 50%;
}

tr[customFieldRowCode] td[colspan='4'] {
	width: 33.33%;
}

tr[customFieldRowCode] td[colspan='3'] {
	width: 25%;
}

.head0 {
	background: #fff;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	line-height: 38px;
	padding: 0 10px;
}

.bodys td {
	padding: 10px;
	vertical-align: top;
}

.bodys .table_left_box {
	padding-right: 0px;
}

.bodys input[button] {
	width: 100px;
	display: inline-block;
}

.bodys input[text] {
	width: 170px;
	display: inline-block;
}

.bodys label {
	color: #000;
}

.table_right_title {
	font-size: 16px;
	height: 40px;
	line-height: 40px;
	padding-left: 20px;
	background: #dbf0e6;
	color: #000 !important;
	border: 1px dashed #d0d0d0;
}

.table_right_main {
	width: 100%;
	table-layout: fixed;
}

.bodys .layui-form-label {
	font-size: 15px;
	background: none;
	color: #000;
	width: auto;
	padding: 0 0 5px;
	margin: 0;
	float: none;
	text-align: left;
	display: inline-block;
}

.bodys .layui-form-radio {
	margin-top: 0;
	width: 100%;
}

.bodys .layui-form-checkbox {
	height: 31px;
}

.bodys .layui-form-checkbox i {
	margin-top: 6px;
}

.item_label_checkbox_field {
	max-width: 210px;
	line-height: 30px;
	cursor: pointer;
	width: auto;
	white-space: normal;
	vertical-align: top;
	word-break: break-all;
}

.item_label_radio_field {
	width: auto;
	white-space: normal;
	vertical-align: top;
	cursor: pointer;
	white-space: normal;
	word-break: break-all;
}

.second {
	margin-left: 250px;
	/*margin-top: -30px;*/
	position: relative;
	top: 0px;
	line-height: 30px;
}

.second .item_label_checkbox_field {
	white-space: normal;
	word-break: break-all;
}

.second .item_label_radio_field {
	min-width: 50px;
	white-space: normal;
	word-break: break-all;
	word-spacing: normal;
}

.second label {
	font-weight: 500;
}

.item_label_content {
	display: inline-block;
}

.inline {
	display: inline-block;
}

.inline .layui-form-switch {
	margin-top: 0px;
}

.item_label_content_opt {
	color: #000;
	display: inline-block;
	font-weight: 500;
	margin-left: 20px;
	cursor: pointer;
}

.layui-form-span-interface span {
	color: #000;
	margin-right: 20px;
}

.third {
	width: calc(100% - 30px);
	vertical-align: top;
}

.box1 div {
	display: inline-block;
	position: relative;
	margin: 1px 0;
}

label.layui-form-label.item_label2.block {
	display: inline-block;
	width: 70px;
	text-align: right;
	padding: 0 15px;
}

.item0 {
	padding: 5px 0px 5px 0;
	min-width: 300px;
}

.item2 {
	padding: 5px 0px 5px 0;
	min-width: 48%;
}

.item1 {
	background: #f2f2f2;
}

.i_time {
	position: absolute;
	right: -21px;
	top: 8px;
	z-index: 100;
}

.layui-table tbody tr:hover {
	background: #ffffff;
}

.layui-hove:hover {
	background: #e0e0e0;
}

.layui-hove {
	padding: 0px 8px;
	margin-top: 4px;
}

.third label {
	cursor: pointer;
}

.item0_title {
	color: #000;
	font-weight: 700;
	border-bottom: 1px solid #d0d0d0;
	padding: 5px 0;
	margin-bottom: 10px;
}

.second .layui-form {
	display: inline-block;
	vertical-align: top;
}

.table_right_title i {
	margin-right: 5px;
}

.layui-form-input-text {
	margin-left: 10px;
}

.input_set {
	display: none;
	position: absolute;
	top: 25px;
	left: 23px;
	line-height: 24px;
	text-align: center;
	border: 1px solid #ccc;
	background: #efefef;
	border-left: none;
	border-radius: 2px;
	font-size: 14px;
	padding: 4px 4px;
}

.input_set_edit, .input_set_delete {
	width: 60px;
	border-left: 1px solid #dedede;
	cursor: pointer;
}

.col_set {
	position: absolute;
	width: 80px;
	line-height: 24px;
	border: 1px solid #ccc;
	text-align: right;
	font-size: 16px;
	background: #efefef;
	cursor: pointer;
	border-radius: 2px;
	font-size: 14px;
	padding: 4px 4px;
}

.col_set li, .input_set li {
	border-top: 1px solid #ccc;
	padding: 4px 6px;
}

.col_set li:first-child, .input_set li:first-child {
	border-top: none;
}

.input_one {
	height: 20px;
	margin-top: 0px !important;
	padding: 0;
	margin-left: 3px;
}

.layui-form-mid {
	width: 100%;
	float: none;
	margin-left: 20px;
}

.input_item {
	display: inline-block;
	width: 200px;
	height: 26px;
	line-height: 26px;
	margin-top: 4px;
	border: none;
	border-bottom: 1px solid #A0A0A0;
	background: #ffffff;
}

.main_table {
	margin-bottom: 0;
	background: #f2f2f2;
	min-height: 465px;
	height: -moz-calc(100% -  19px);
	height: -webkit-calc(100% -   19px);
	height: calc(100% -  19px);
}

.table_right .table_right_main td {
	text-align: left;
	border: 1.5px dashed #ccc;
}

.table-edit .table_right .table_right_main td:hover {
	background-color: rgb(222, 225, 230);
}

.main_title {
	height: 42px;
	background: #000;
	color: #fff;
	font-size: 14px;
	padding: 0 5px;
}

.main_table .table_left, .table_right {
	border: 1px solid #dedede;
	background: #fff;
}

.table_right {
	margin-bottom: 10px;
}

.table_right_title {
	height: 38px;
	line-height: 38px;
	cursor: pointer;
}

.table_left_title {
	height: 38px;
	line-height: 38px;
	font-size: 15px;
	font-weight: 700;
	border-bottom: 1px solid #d0d0d0;
	background: #dbf0e6;
	cursor: pointer;
}

.table_left_main {
	border: none;
	overflow: auto;
	position: absolute;
	top: 39px;
	bottom: 190px;
	left: 0px;
	right: 0px;
}

.layui-colla-title {
	background: #fff;
	border-bottom: 1px solid #ededed;
	text-align: left;
	padding-left: 40px;
	font-weight: 700;
}

.layui-colla-item {
	background: #fff;
}

.layui-colla-content {
	border-top: none;
	padding: 0 15px 0px;
}

.left_item {
	width: 140px;
	line-height: 30px;
	height: 32px;
	overflow: hidden;
	border: 1px solid #dedede;
	background: #f2f2f2;
	margin: 6px 0px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	padding: 2px 10px;
	border-radius: 4px;
	cursor: pointer;
	white-space: nowrap;
	text-overflow: ellipsis;
	text-align: left;
}

.left_item:hover {
	background: #BEBEBE;
	color: #fff;
}

.table_right_all {
	overflow-y: auto;
	overflow-x: hidden;
	position: absolute;
	left: 210px;
	top: 0;
	right: 0;
	bottom: 0;
}

.table_left_box {
	text-align: center;
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	width: 200px;
	border: 1px solid #ccc;
	background-color: #fff;
}

.ps {
	text-align: left;
	margin-top: 10px;
	padding: 5px;
	border: 1px solid #dedede;
	background: #eee;
	font-weight: 700;
	font-size: 12px;
	position: absolute;
	bottom: 0;
}

.ps p:first-child {
	font-size: 14px;
}

.bodys {
	min-width: 840px;
	min-height: auto;
	background: #eee;
	overflow: auto;
	position: absolute;
	top: 48px;
	bottom: 10px;
	right: 10px;
	left: 10px;
}

body {
	width: 100%;
	height: 100%;
	padding: 15px;
	margin: 0;
	color: #484848;
	font: 14px Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1, Tahoma, Arial, sans-serif;
}

body {
	width: 100%;
	height: 100%;
	padding: 48px 10px 10px;
	margin: 0;
	background: #eee;
	color: #484848;
	font: 14px Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1, Tahoma, Arial, sans-serif;
	overflow: hidden;
}

.layui-btn {
	margin-left: 10px;
}

.pubHide {
	display: none;
}

.head_text {
	height: 36px;
	line-height: 36px;
	padding: 5px;
}

.head_text i {
	display: inline-block;
	border: none;
	text-align: center;
	width: 14px;
	height: 14px;
	position: relative;
	top: 1px;
}

.row_icon {
	position: absolute;
	width: 20px;
	top: 5px;
	right: 5px;
	font-size: 20px;
	cursor: pointer;
}

.col_set li:HOVER, .input_set li:HOVER {
	background-color: rgb(222, 225, 230);
}

body .layui-layer-aems .layui-layer-title {
	background-color: #DBEFE6;
}

.second {
	padding: 0px 8px;
}

.layui-select-title .layui-unselect {
	padding-right: 16px;
}

.win_head {
	padding: 5px 10px;
	border-bottom: 1px solid #d0d0d0;
}

.win_head span {
	font-weight: 700;
	color: #666;
}

.win_head img {
	position: absolute;
	right: 13px;
	top: 12px;
	cursor: pointer;
}

.win_li {
	padding: 5px 15px;
	cursor: pointer;
	position: relative;
}

.win_text {
	display: inline-block;
	width: 103px;
	border-right: 1px solid #d0d0d0;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	vertical-align: middle;
	padding-right: 3px;
}

.win_edit {
	position: absolute;
	right: 35px;
	display: inline-block;
	width: 16px;
	height: 16px;
}

.win_del {
	position: absolute;
	right: 10px;
	top: 7px;
	display: inline-block;
	width: 16px;
	height: 16px;
}

.module:hover .type_text {
	color: #000;
}

.module:hover .module_win {
	display: block;
}

.win_li:hover {
	background: #eff0f2;
}

.win_li:hover .win_text {
	color: #000;
}

.mergeWithNextRow td {
	border-style: none !important;
}

.mergeWithNextRow td:first-child {
	border-left: 1.5px dashed #ccc !important;
}

.mergeWithNextRow td:last-child {
	border-right: 1.5px dashed #ccc !important;
}

.mergeWithNextRow+tr td {
	border-style: none !important;
}

.mergeWithNextRow+tr td:first-child {
	border-left: 1.5px dashed #ccc !important;
}

.mergeWithNextRow+tr td:last-child {
	border-right: 1.5px dashed #ccc !important;
}

.left_item i {
	display: inline-block;
	border: none;
	text-align: center;
	background-image: url('../../../plugins/static/image/icon4.png');
	background-repeat: no-repeat;
	width: 16px;
	height: 15px;
	position: relative;
	top: 3px;
	margin-right: 10px;
}

.left_item:hover i {
	background-image: url('../../../plugins/static/image/icon5.png');
}

.i_1 {
	background-position: 0px 0px;
}

.i_2 {
	background-position: -16px 0px;
}

.i_3 {
	background-position: -32px 0px;
}

.i_4 {
	background-position: -48px 0px;
}

.i_5 {
	background-position: 0px -16px;
}

.i_6 {
	background-position: -16px -16px;
}

.i_7 {
	background-position: -32px -16px;
}

.i_8 {
	background-position: -48px -16px;
}

.i_9 {
	background-position: 0px -32px;
}

.i_10 {
	background-position: -16px -32px;
}

.i_11 {
	background-position: -32px -32px;
}

.i_12 {
	background-position: -48px -32px;
}

.i_13 {
	background-position: 0px -48px;
}

.i_14 {
	background-position: -16px -48px;
}

.i_15 {
	background-position: -32px -48px;
}

.i_16 {
	background-position: -48px -48px;
}

.i_17 {
	background-position: 0px -64px;
}

.i_18 {
	background-position: -16px -64px;
}

.i_19 {
	background-position: -32px -64px;
}

.layui-table th {
	position: static;
}

.left_item i.layui-icon2 {
	background-image: none;
	top: 0px;
}

.layui-profile {
	width: 220px;
	height: 220px;
}

html {
	background: none !important;
}

.sortable-background{
	background-color: #F2F2F2;
}

.move-div-div{
	width: fit-content;
}

.high-light{
	background-color: #D2D2D2;
}