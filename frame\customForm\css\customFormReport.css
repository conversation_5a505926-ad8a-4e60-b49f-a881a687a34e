p img {
	display: block;
	margin: 0 auto;
	float: right;
	margin-top: 12px;
}

.head3 {
	background: #f2f2f2;
	padding-top: 15px;
	border: 1px solid #e2e2e2;
}

.typeBoxWrap {
	width: 100%;
	border: none;
	padding: 0px;
}

.typeBox {
	min-width: 130px;
	min-height: 130px;
	width: 100%;
	border: 1px solid #e6e6e6;
	border-radius: 5px;
}

.typeName {
	font-size: 14px;
	text-align: center;
	padding: 0px 10px;
	padding-bottom: 10%;
    width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.panel p {
	padding: 0px 10px !important;
	margin-right: 0px !important;
}

.eventDataValue {
	background-color: #F2F2F2;
}

.rightCon {
    padding: 1px;
    border: 1px solid rgb(208, 208, 208);
    inset: 10px 10px 10px 265px;
    background: none;
}

.rightCon {
    position: absolute;
    inset: 45px 0px 0px 1px;
    background-color: rgb(247, 246, 246);
    padding: 10px;
    overflow-y: auto;
}
.enventShowTabs {
    width: 100%;
}
.panels {
    box-sizing: border-box;
    float: left;
    clear: both;
    width: 100%;
    background: rgb(255, 255, 255);
    border-radius: 0px 10px 10px;
    position: relative;
    z-index: 1;
}
.panel {
    box-sizing: border-box;
    width: 100%;
    position: absolute;
    opacity: 1;
    background: rgb(255, 255, 255);
    border-radius: 10px;
    border-top: 1px solid rgb(221, 221, 221);
    padding: 1%;
}
.panel p {
    line-height: 40px;
    margin: 0px 8px 0px 0px;
    padding: 0px 4px;
    border-bottom: 1px solid rgb(221, 221, 221);
}
.eventDataValue {
    background-color: #F2F2F2;
}
.panel p a {
    color: rgb(6, 165, 159);
    padding-left: 5px;
}
body a {
    cursor: pointer;
}