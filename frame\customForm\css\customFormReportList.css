.bodys {
	position: absolute;
	top: 48px;
	left: 10px;
	right: 10px;
	bottom: 10px;
	padding: 0 4px;
}

.bodys {
	padding: 10px 15px 15px;
	background: #fff;
	position: absolute;
	top: 48px;
	bottom: 10px;
	right: 10px;
	left: 10px;
}

.leftSum {
	top: 10px;
	bottom: 10px;
	left: 10px;
	border: 1px solid #d0d0d0;
}

.leftSum {
	position: absolute;
	width: 250px;
	border-right: 1px solid #3aa9e8;
	background-color: #fff;
	overflow-y: auto;
}

.leftSum ul, li.sumItem {
	list-style: none;
	padding: 0px;
	margin: 0px;
}

li.sumItem {
	padding: 0px 10px;
}

li.sumItem {
	border-bottom: 1px solid #e8e8e8;
	height: 40px;
	line-height: 40px;
	cursor: pointer;
}

li.sumItem:hover {
	background-color: #dcefe6 !important;
	color: black !important;
}

.sumItem .leftIco {
	float: left;
	width: 30px;
	height: 30px;
	margin-top: 5px;
	margin-right: 10px;
	border-radius: 100%;
	background-color: #fff;
	line-height: 30px;
	text-align: center;
}

.sumItem .rightText {
	font-size: 14px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.rightCon {
	padding: 1px;
	border: 1px solid #d0d0d0;
	top: 10px;
	left: 265px;
	bottom: 10px;
	right: 10px;
	background: none;
}

.rightCon {
	position: absolute;
	background-color: #f7f6f6;
	overflow-y: auto;
}

.enventShowTabs {
	width: 100%;
}

.panels {
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	float: left;
	clear: both;
	width: 100%;
	background: #fff;
	border-radius: 0 10px 10px 10px;
	position: relative;
	z-index: 1;
}

.panel p {
	padding: 0px 10px !important;
	margin-right: 0px !important;
}

.panel p {
	line-height: 40px;
	margin: 0px;
	margin-right: 8px;
	padding: 0px 4px;
	border-bottom: 1px solid #DDDDDD;
}

.eventDataValue {
	background-color: #F2F2F2;
}

.panel {
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	width: 100%;
	position: absolute;
	opacity: 1;
	background: #fff;
	border-radius: 10px 10px 10px 10px;
	border-top: 1px solid #ddd;
	padding: 1%;
}

.panel p a {
	color: #06a59f;
	padding-left: 5px;
}

.icon_lists .icon {
	font-size: 42px;
	line-height: 100px;
	margin: 10px 0px;
	color: rgb(153, 153, 153);
	cursor: pointer;
	transition: font-size 0.25s ease-out 0s;
}

.iconfont {
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	font-family: iconfont !important;
}
/** 大图标列表 **********************************************************/
.typeBoxWrap {
	width: 100%;
	border: none;
	padding: 0px;
}

.typeBoxWrap td {
	width: 20%;
	border: none;
	padding: 20px 16px;
}

.typeBox {
	min-width: 130px;
	min-height: 130px;
	width: 85%;
	border: 1px solid #e6e6e6;
	border-radius: 5px;
	height: 134px;
}

.typeBox:hover {
	border-color: #009688;
	box-shadow: 0px 0px 10px #93d3cc;
}

.typeBox i {
	text-align: center;
	vertical-align: middle;
	/* background-image: url(images/bgLogo.png); */
	font-size: 80px;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	display: inline-block;
	border: none;
	vertical-align: middle;
}

.typeBox i.layui-icon3 {
	font-size: 75px !important;
	padding-bottom: 5px;
}

.typeIco {
	margin: 16px auto 10px;
	text-align: center;
	vertical-align: middle;
	/* background-image: url(images/bgLogo.png); */
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
}

.typeName {
	font-size: 14px;
	text-align: center;
	padding-bottom: 10%;
	width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.slideBar .layui-icon2 {
	font-size: 14px !important;
}