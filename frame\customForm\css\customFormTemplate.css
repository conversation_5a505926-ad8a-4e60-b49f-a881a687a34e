.table_right .table_right_main td {
	border: 1px solid #ccc;
}

.item_label_content_template {
	position: absolute;
	top: 20px;
	left: 20px;
	z-index: 9999999;
	padding: 5px;
	width: 180px;
	background: #fff;
	border: 1px solid #d0d0d0;
	box-shadow: 1px 1px 5px 0px #aaa;
	height: 180px;
	overflow-x: auto;
}

.item_label_content_template li:HOVER {
	cursor: pointer;
}

.item_label_content_template li {
	position: relative;
	height: 20px;
}

.auditMode-left {
	display: inline-block;
	position: absolute;
	right: 35px;
	left: 0px;
	overflow: hidden;
}

.auditMode-left a {
	white-space: nowrap;
}

.auditMode-right {
	display: inline-block;
	width: 30px;
	position: absolute;
	right: 0px;
}

.mergeWithNextRow td {
	border-style: none !important;
}

.mergeWithNextRow td:first-child {
	border-left: 1px solid #ccc !important;
}

.mergeWithNextRow td:last-child {
	border-right: 1px solid #ccc !important;
}

.mergeWithNextRow+tr td {
	border-style: none !important;
}

.mergeWithNextRow+tr td:first-child {
	border-left: 1px solid #ccc !important;
}

.mergeWithNextRow+tr td:last-child {
	border-right: 1px solid #ccc !important;
}

.switchFish {
	height: 20px;
	width: 20px;
	margin-left: 5px;
	cursor: pointer;
}

.layui-form-span-interface {
	height: 30px;
}

.interface {
	width: 200px;
	height: auto !important;
}

.interface li {
	position: relative;
	height: 30px;
	line-height: 30px;
	padding: 2px 5px;
	cursor: pointer;
	border-bottom: 1px solid #d0d0d0;
}

.interface li span {
	float: right;
	margin-right: 0px;
}

.interface li:hover {
	background: #eff0f2;
}

.layui-table th, .layui-table td {
	position: static;
}

.field_fieldSet_label {
	margin-right: 10px;
}

.layui-radio-disbaled>i {
	color: #a0a0a0 !important;
}

.relationField-hide {
	display: none;
}

.second {
	padding: 0px 8px;
	display: none;
}

.second_show {
	display: block;
}

.bodys .layui-form-label {
	display: block;
}

.layui-profilePhoto {
	width: 90px;
	height: 100px;
}

input[readOnly], textarea[readOnly] {
	background: #F2F2F2;
}

.edui-editor-bottombar, .edui-editor-wordcount {
	display: none;
}

.table_right_main th {
	height: 40px;
	line-height: 40px;
	background-color: #f2f2f2 !important;
}

.table_right[isTable='1'] .item_label_hide {
	display: none;
}

.table_right[isTable='1'] .table_right_main_container {
	width: auto;
	overflow: auto;
}

.table_right[isTable='1'] .second {
	margin-left: 60px;
}

.table_right[isTable='1'] .table_right_main_container {
	background: #f2f2f2;
}

.table_right[isTable='1'] .table_right_main_container table.table_right_main {
	background: #fff;
}

.input_item[customregex] {
	width: 90%;
}

.custom_file .edui-editor {
	display: none !important;
}