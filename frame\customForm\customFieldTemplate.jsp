<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title></title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width">
<link rel="stylesheet" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" href="${basePath}plugins/static/css/style.css">
<link rel="stylesheet" type="text/css" href="${basePath}frame/customForm/css/customForm.css">
<link rel="stylesheet" type="text/css" href="${basePath}frame/customForm/css/customFormTemplate.css">
<link rel="stylesheet" type="text/css" href="${basePath}frame/customForm/css/customFieldTemplate.css">
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var baseImgPath = "${baseImgPath}";
	var hasSubmit = false;
	var compNo = "${customFormFilled.compNo}" || "${compNo}";
	
	$(function() {
		layui.use('form', function() {
			var form = layui.form;
			
			// 先处理单选多选
			$("div[customFieldCode] input[level='2']:checked").each(function(i, e) {
				// 二级选中,一级也要选中
				$("input[name='" + this.name.substr(1) + "'][value='" + $(this).attr("parentCustomOptionSetCode") + "']").prop("checked", true);
			});
			
			$("div[customFieldCode] div[divLevel='2']").each(function(i, e) {
				var customModularIndex = $(this).find("input:radio,input:checkbox")[0].name.split("_")[1];
				$(this).appendTo($("div.second[customOptionSetCode='" + $(this).attr("customOptionSetCode") + "'][customModularIndex='" + customModularIndex + "']"));
			});
			
			obj.render(form);
			
			form.verify(formVerify);
			
			form.on("submit(save)", function(data) {
				obj.saveCustomFormFilled();
				return false;
			});
			
			$("input[customModularCode]:hidden").each(function(i, e) {
				var customModularCode = $(this).attr("customModularCode");
				if ($("input[name='"+customModularCode+"']").length == 0) {
					$("#headTool").append('<input type="hidden" name="' + customModularCode + '" value="1"/>');
				} 
			}).remove();
			
			obj.radioOnClick(form);
			obj.checkboxOnClick(form);
			obj.selectOnChange(form);
			
			if(parent.initObj){
				$.each(parent.initObj, function(name, value) {
					if (typeof value == "function") {
						value.call(parent.window);
					}
				});
			}
		});
		
	});
	
</script>
</head>
<body>
	<form method="post" id="form1" name="form1" class="layui-form">
		<input type="hidden" name="customFormCode" value="${customForm.customFormCode}" />
		<input type="hidden" name="appCode" value="${customForm.appCode}" />
		<input type="hidden" name="customFormFilledCode" value="${customFormFilled.customFormFilledCode}" />
		<input type="hidden" name="customFormFilledID" value="${customFormFilled.customFormFilledID}" />
		<input type="hidden" name="status" value="${customFormFilled.status}" />
		<input type="hidden" name="createUserCode" value="${customFormFilled.createUserCode}" />
		<input type="hidden" name="createUserName" value="${customFormFilled.createUserName}" />
		<input type="hidden" name="createDate" value="<fmt:formatDate value="${customFormFilled.createDate}" pattern="yyyy-MM-dd HH:mm:ss" />" />
		<input type="hidden" name="compNo" value="${empty customFormFilled.compNo ? compNo : customFormFilled.compNo }" />
		<input type="hidden" name="deptID" value="${empty customFormFilled.deptID ? deptID : customFormFilled.deptID }" />
		<c:forEach items="${customFieldBusinessCodeAry}" var="customFieldBusinessCode">
			<input type="hidden" name="customFieldBusinessCode" value="${customFieldBusinessCode}" />
		</c:forEach>
		<div id="headTool" class="head0 layui-form">
			<div class="head0_right fr">
				<button type="button" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
			</div>
		</div>
		<div id="bodys" class="bodys">
			<div class="table_box" style="background-color: #fff;padding-top: 10px;padding-left: 10px;">
				<c:forEach items="${customFieldList}" var="customField">
					<input type="hidden" customModularCode="${customField.customModularCode}" value="0"/>
					<c:if test="${customField.customFieldSet == 'radio' || customField.customFieldSet == 'checkbox'}">
						<%@include file="customField/checkboxOrRadio.jsp"%>
					</c:if>
					<c:if test="${customField.customFieldSet == 'select'}">
						<%@include file="customField/select.jsp"%>
					</c:if>
					<c:if test="${customField.customFieldSet == 'label'}">
						<%@include file="customField/label.jsp"%>
					</c:if>
					<c:if test="${customField.customFieldSet == 'img'}">
						<%@include file="customField/img.jsp"%>
					</c:if>
					<c:if test="${customField.customFieldSet == 'text'}">
						<%@include file="customField/text.jsp"%>
					</c:if>
					<c:if test="${customField.customFieldSet == 'textarea'}">
						<%@include file="customField/textarea.jsp"%>
					</c:if>
					<c:if test="${customField.customFieldSet == 'datetime'}">
						<%@include file="customField/datetime.jsp"%>
					</c:if>
				</c:forEach>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/customForm/js/customFormTemplate.js"></script>
<script type="text/javascript" src="${basePath}frame/customForm/js/customFieldTemplate.js"></script>