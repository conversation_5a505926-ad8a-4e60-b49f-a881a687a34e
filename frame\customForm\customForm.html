<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>公共模块</title>
<link rel="stylesheet" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" href="../../plugins/static/css/style.css">
<link rel="stylesheet" type="text/css" href="css/customForm.css">
</head>
<body>
	<div style="left: 0px; right: 0px; position: absolute; color: blue; height: 24px; line-height: 24px; text-align: center; background: #fdeea9; z-index: 99999; width: 100%; font-size: 12px;">
		已配置【业务编号】的组件，请勿随意改动，否则会导致其他引用模块功能失效，造成严重的后果
		<i title="关闭此提示" style="float: right; margin-right: 15px; cursor: pointer;" class="layui-icon2" onclick="$(this).parent().remove();$('.table-edit').removeAttr('style')">&#xe68d; </i>
	</div>
	<form id="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="customFormCode" />
		<input type="hidden" name="customFormName" />
		<input type="hidden" name="customFormTypeCode" />
		<input type="hidden" name="customFormClass" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="funCode" />
		<input type="hidden" name="compNo" />
		<div id="headTool" class="head0 layui-form">
			<span class="head_text">
				<i id="menuIcon" class="layui-icon2"></i>
				
			</span>
			<span id="customFormName"></span>
			<div class="head0_right fr">
				<input type="button" value="模板下载" class="layui-btn layui-btn-sm skin-btn-minor layui-hide" onclick="customForm.downloadTemplate()" />
				<button type="button" class="layui-btn layui-btn-sm skin-btn-minor layui-hide" id="selectExportFile">导入</button>
				<button type="button" class="layui-btn layui-btn-sm" onclick="customForm.toCustomFormTemplate()">预览</button>
				<button type="button" class="layui-btn layui-btn-sm" onclick="customForm.openOrRetract(this)">全部收起</button>
				<button type="button" id="back" class="layui-btn layui-btn-sm skin-btn-normal" onclick="history.back();">返回</button>
			</div>
		</div>
		<div id="bodys" class="bodys table-edit" style="margin-top: 30px;">
			<div class="table_left_box">
				<div class="table_left">
					<div class="table_left_title skin-div-css">组件库</div>
					<div class="layui-collapse table_left_main">
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">布局组件</h2>
							<div class="layui-colla-content">
								<div class="left_item input table setCustomFormClass" unselectable="on">
									<i class="layui-icon i_18"></i>
									分类
								</div>
								<div class="left_item column column_1" cols="1" unselectable="on">
									<i class="layui-icon i_1"></i>
									一行一列
								</div>
								<div class="left_item column column_2 setCustomFormClass" cols="2" unselectable="on">
									<i class="layui-icon i_2"></i>
									一行二列
								</div>
								<div class="left_item column column_3 setCustomFormClass" cols="3" unselectable="on">
									<i class="layui-icon i_3"></i>
									一行三列
								</div>
							</div>
						</div>
						<div class="layui-colla-item">
							<h2 class="layui-colla-title">通用组件</h2>
							<div class="layui-colla-content layui-show" id="customFieldSetList">
								<div class="left_item input text" unselectable="on" customFieldSet="text">
									<i class="layui-icon i_5"></i>
									文本框
								</div>
								<div class="left_item input textarea" unselectable="on" customFieldSet="textarea">
									<i class="layui-icon i_6"></i>
									文本域
								</div>
								<div class="left_item input datetime" unselectable="on" customFieldSet="datetime">
									<i class="layui-icon i_7"></i>
									日期
								</div>
								<div class="left_item input radio  " unselectable="on" customFieldSet="radio">
									<i class="layui-icon i_8"></i>
									单选框
								</div>
								<div class="left_item input checkbox " unselectable="on" customFieldSet="checkbox">
									<i class="layui-icon i_9"></i>
									多选框
								</div>
								<div class="left_item input label " unselectable="on" customFieldSet="label">
									<i class="layui-icon i_10"></i>
									标签
								</div>
								<div class="left_item input select " unselectable="on" customFieldSet="select">
									<i class="layui-icon i_11"></i>
									下拉框
								</div>
								<!-- <div class="left_item input img setCustomFormClass" unselectable="on" customFieldSet="img">
									<i class="layui-icon i_16"></i>
									图片
								</div> -->
								<div class="left_item input org " unselectable="on" customFieldSet="org">
									<i class="layui-icon i_17"></i>
									组织架构
								</div>
								<div class="left_item input interface " unselectable="on" customFieldSet="interface">
									<i class="layui-icon2"></i>
									接口
								</div>
								<div class="left_item input profile setCustomFormClass layui-hide" unselectable="on" customFieldSet="profile">
									<i class="layui-icon i_16"></i>
									头像框
								</div>
								<div class="left_item input file setCustomFormClass layui-hide" unselectable="on" customFieldSet="file">
									<i class="layui-icon2">&#xe7af;</i>
									附件
								</div>
							</div>
						</div>
						<div id="systemClassificationsDiv" class="layui-colla-item setCustomFormClass">
							<h2 class="layui-colla-title">公用分类</h2>
							<div class="layui-colla-content" id="systemClassifications"></div>
						</div>
						<div id="systemComponentsDiv" class="layui-colla-item ">
							<h2 class="layui-colla-title">公用组件</h2>
							<div class="layui-colla-content" id="systemComponents"></div>
						</div>
						<div id="deletedClassificationsDiv" class="layui-colla-item setCustomFormClass">
							<h2 class="layui-colla-title">未启用分类</h2>
							<div class="layui-colla-content" id="deletedClassifications"></div>
						</div>
						<div id="deletedCustomFieldsDiv" class="layui-colla-item">
							<h2 class="layui-colla-title">未启用组件</h2>
							<div class="layui-colla-content" id="deletedCustomFields"></div>
						</div>
					</div>
				</div>
				<div class="ps">
					<p>说明：</p>
					<p>1、分类操作：鼠标左键拖拽分类可上下移动位置，右键点击分类可编辑和删除。</p>
					<p>2、组件操作：鼠标左键拖拽控件名称，与表单内组件互换位置；拖拽组件名称下方区域，可换行或横移。鼠标左键拖拽组件空白区域可上下换行。行鼠标右键点击组件可编辑和删除。</p>
				</div>
			</div>
			<div class="table_box table_right_all"></div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="../../plugins/components/moveUtil/js/moveUtil.js"></script>
<script type="text/javascript" src="../../plugins/Sortable/Sortable.min.js"></script>
<script type="text/javascript" src="../../plugins/Sortable/Sortable.Swap.js"></script>
<script type="text/javascript" src="js/initCustomForm.js?version=*******"></script>
<script type="text/javascript" src="js/customForm.js?version=*******"></script>
<script type="text/javascript">
	$(function() {
		window.customFormCode = param.get("customFormCode");
		window.customFormName = param.get("customFormName");
		window.customFormTypeCode = param.get("customFormTypeCode");
		window.customFormClass = param.get("customFormClass");
		window.compNo = param.get("compNo");
		window.appCode = param.get("appCode");
		window.funCode = param.get("funCode");
		customForm.init();
	});
</script>
</html>