<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>审核</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css" />
<link rel="stylesheet" type="text/css" href="css/customFormApprovalTemplate.css" />
</head>
<body>
	<form lay-filter="param" class="layui-form ">
		<input type="hidden" name="appCode" />
		<input type="hidden" name="funCode" />
		<input type="hidden" name="submitType" />
		<input type="hidden" name="customFormCode" />
		<input type="hidden" name="customFormBusinessCode" />
		<input type="hidden" name="approvalBelongCode" />
		<input type="hidden" name="approvalBelongFlowNodeCode" />
		<input type="hidden" name="inLoop" />
		<input type="hidden" name="compNo" />
		<input type="hidden" name="customFormFilledID" />
		<input type="hidden" name="customFormFilledCode" />
		<input type="hidden" name="status" />
		<input type="hidden" name="createUserCode" />
		<input type="hidden" name="createUserName" />
		<input type="hidden" name="createDate" />
		<input type="hidden" name="deptID" />
		<input type="hidden" name="approvalMethod" />
		<input type="hidden" name="saveState" value="1" />
		<input type="hidden" name="approvalBelongFlowNodeRecordDraftCode" />
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<label dynamic class="layui-form-label layui-hide">
					<span style="color: #ff0000">*</span>
					指定审批人
				</label>
				<div dynamic class="layui-input-inline layui-hide">
					<input type="text" class="layui-input" readonly="readonly" onclick="defaultApproval.toSelectApprovalUser(this);" />
					<input type="hidden" name="approvalFlowNodeData" />
				</div>
				<input btn="saveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="保存草稿" onclick="customFormApprovalTemplate.saveCustomFormFilled(2);"/>
				<input btn="saveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="提交" lay-submit lay-filter="save" />
				<input btn="inLoop" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束循环" onclick="customFormApprovalTemplate.inLoopOnClick(this);">
				<input type="button" class="layui-btn layui-btn-sm layui-hide" lay-submit="" lay-filter="saveLoop">
				<input btn="countersign" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束会签" lay-submit="" lay-filter="saveCountersign">
				<input btn="finish" type="button" class="layui-btn layui-btn-sm layui-hide" value="一键结束" lay-submit="" lay-filter="saveFinish">
				<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" />
			</div>
		</div>
		<div class="bodys">
			<input type="hidden" name="customModularCode" />
			<div customModular class="table_right"></div>
			<div id="stateStatusNo" class="layui-form-item layui-hide">
				<label class="layui-form-label">
					表单状态流转
				</label>
				<div class="layui-input-inline layui-form" lay-filter="stateStatusNoDiv">
					<select name="stateStatusNo">
						<option value="-999">自动</option>
					</select>
				</div>
			</div>
			<div id="copy" class="layui-form-item">
				<label class="layui-form-label">
					抄送
				</label>
				<div class="layui-input-block" >
					<input type="text" name="copyUserNames" class="layui-input"  readonly="readonly" onclick="defaultApproval.toSelectCopyUser(this);" />
					<input type="hidden" name="copyUserCodes" />
				</div>
			</div>
			<div id="table_box_upload" class="layui-hide">
				<div class="layui-form-item">
					<label class="layui-form-label"> 附件上传 </label>
					<div class="layui-input-inline" style="width: 520px;">
						<button type="button" class="layui-btn layui-btn-sm" onclick="pubUploader.openFiles(customFormApprovalTemplate.attaCallback)">点击上传</button>
						<textarea id="uploadEditor" style="display: none;"></textarea>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label"> 附件列表 </label>
					<div class="layui-input-inline" style="width: 520px;">
						<blockquote id="ueditorFileDiv" class="layui-elem-quote layui-quote-nm" style="min-width: 200px; width: auto; margin-bottom: 0px;">
							<p>无</p>
						</blockquote>
					</div>
				</div>
			</div>
			<div class="layui-form-item layui-hide">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline" style="width: 400px;">
					<input btn="saveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="保存草稿" onclick="customFormApprovalTemplate.saveCustomFormFilled(2);"/>
					<input btn="saveBtn" type="button" class="layui-btn layui-btn-sm layui-hide" value="提交" lay-submit lay-filter="save" />
					<input btn="inLoop" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束循环" onclick="customFormApprovalTemplate.inLoopOnClick(this);">
					<input type="button" class="layui-btn layui-btn-sm layui-hide" lay-submit="" lay-filter="saveLoop">
					<input btn="countersign" type="button" class="layui-btn layui-btn-sm layui-hide" value="结束会签" lay-submit="" lay-filter="saveCountersign">
					<input btn="finish" type="button" class="layui-btn layui-btn-sm layui-hide" value="一键结束" lay-submit="" lay-filter="saveFinish">
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" />
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/pubUploader.js"></script>
<script type="text/javascript" src="js/customFormApprovalTemplate.js"></script>
<script type="text/javascript" src="js/customFormTemplate.js"></script>
<script type="text/javascript" src="js/pubInterface.js?version=*******"></script>
<script type="text/javascript">
	$(function() {
		customFormApprovalTemplate.init();
	});
</script>
</html>

