<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="java.util.List"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>表单编辑</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<link rel="stylesheet" href="${basePath}/plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/edit.css">
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var network_err_tips = "网站服务器繁忙，请稍后提交";
	var isSubmit = false;
	var customFormCode = "";
	var customFormTypeCode = "";
	var currBusinessCode = "";
	var appCode = "";
	var funCode = "";
	
	$(function() {
		customFormCode = $("input[param='customFormCode']").val();
		customFormTypeCode = $("input[param='customFormTypeCode']").val();
		appCode = $("input[param='appCode']").val();
		funCode = $("input[param='funCode']").val();
	})
</script>
<style type="text/css">
.lay-readonly[readonly] {
	color: #BEBEBE;
}
</style>
</head>
<body class="body_noTop">
	<form method="post" id="form1" name="form1" class="layui-form" lay-filter="example">
		<!-- 入参 -->
		<input type="hidden" name="oldCustomFormCode" value="<c:out value='${param.customFormCode}'/>" name="customFormCode" />
		<input type="hidden" param="customFormCode" value="<c:out value='${param.customFormCode}'/>" name="customFormCode" />
		<input type="hidden" param="customFormTypeCode" value="<c:out value='${param.customFormTypeCode}'/>" />
		<input type="hidden" param="appCode" value="<c:out value='${param.appCode}'/>" />
		<input type="hidden" param="funCode" value="<c:out value='${param.funCode}'/>" />
		<!-- 后台参数 -->
		<input type="hidden" name="customFormID" value="" lay-filter="customFormID" />
		<input type="hidden" name="appCode" value="<c:out value='${param.appCode}'/>" />
		<input type="hidden" name="deptID" value="" lay-filter="deptID" />
		<input type="hidden" name="createUserCode" value="" lay-filter="createUserCode" />
		<input type="hidden" name="createUserName" value="" lay-filter="createUserName" />
		<input type="hidden" name="createDate" value="" lay-filter="createDate" />
		<input type="hidden" name="status" value="1">
		<input type="hidden" name="copy" value="<c:out value='${param.copy}'/>" />
		<!--  新增还是编辑 -->
		<c:set var="hasAdd" value="${empty param.customFormCode}"></c:set>
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
			
				<label class="layui-form-label"> 应用 </label>
				<div class="layui-input-inline">
					<select disabled appCode></select>
				</div>
				
				<label class="layui-form-label">
					<span style="color: red">*</span>
					表单分类
				</label>
				<div class="layui-input-inline">
					<select id="customFormTypeCode" name="customFormTypeCode" lay-verify="required" lay-filter="customFormTypeCode">
					</select>
				</div>
				
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 业务编号 </label>
				<div class="layui-input-inline">
					<input type="text" name="businessCode" maxlength="200" lay-verify="limit|businessCode|limit" limit="200" limit="200" value="" placeholder="请输入表单业务编号" autocomplete="off" class="layui-input lay-readonly" lay-filter="businessCode">
				</div>
				<label class="layui-form-label">
					<span style="color: red">*</span>
					顺序号
				</label>
				<div class="layui-input-inline">
					<input type="text" name="seqNo" maxlength="5" value="1" placeholder="请输入顺序号" lay-verify="required|integer|limit" limit="5" autocomplete="off" class="layui-input" lay-filter="seqNo">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					表单名称
				</label>
				<div class="layui-input-inline">
					<input type="text" name="customFormName" value="" maxlength="200" lay-verify="required|limit" limit="200" placeholder="请输入表单名称" autocomplete="off" class="layui-input" lay-filter="customFormName">
				</div>
				<span class="flowDirectionView  layui-hide">
					<label class="layui-form-label">
						<span style="color: red">*</span>
						流程走向
					</label>
					<div id="flowDirectionDiv" class="layui-input-inline"  lay-verify="" title="流程走向">
						<input type="radio" value="0" title="固定"  lay-skin="primary"  name="flowDirection" lay-filter="flowDirection" />
						<input type="radio" value="1" title="接口" lay-skin="primary"  name="flowDirection" lay-filter="flowDirection" />
					</div>
				</span>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					表单类型
				</label>
				<div  class="layui-input-inline" lay-verify="checkboxRequired" title="表单类型">
					<input type="radio" value="0" title="上报" lay-skin="primary" <c:out value="${hasAdd ? '' : 'disabled'}" /> name="customFormClass" lay-filter="customFormClass" />
					<input type="radio" value="1" title="审批" lay-skin="primary" <c:out value="${hasAdd ? '' : 'disabled'}" /> name="customFormClass" lay-filter="customFormClass" />
				</div>
				<span class="customApprovalView layui-hide" >
					<label class="layui-form-label"> 
						流程设置
					</label>
					<div class="layui-input-inline">
						<input type="text" name="customApprovalFlowName" placeholder="点击选择" readonly="readonly" value="" class="layui-input" onclick="customFormEdit.toSelectCustomApprovalCode(this);" />
						<input type="hidden" name="customApprovalFlowCode" value="" />
						<i class="layui-icon2 layui-select-right" onclick="customFormEdit.clearCustomApprovalFlowValue()" title="清空">&#xe68d;</i>
					</div>
				</span>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					支持附件上传
				</label>
				<div  class="layui-input-inline" lay-verify="checkboxRequired" title="支持附件上传">
					<input type="radio" value="1" title="是" lay-skin="primary" name="hasFileUpload" lay-filter="hasFileUpload" checked/>
					<input type="radio" value="0" title="否" lay-skin="primary" name="hasFileUpload" lay-filter="hasFileUpload" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/customForm/js/customFormEdit.js"></script>