<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title></title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/detail.css">
<link rel="stylesheet" type="text/css" href="${basePath}frame/customDetail/css/customFormDetail.css">
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
</head>
<body style="padding: 0;">
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="customFormCode" value="${param.customFormCode}">
		<input type="hidden" name="customFormFilledCode" value="${param.customFormFilledCode}">
		<input type="hidden" name="appCode" value="${param.appCode}">
		<div class="bodys bodys_noTop" style="display: inline-block; margin-right: 0.5%; top: 10px;">
			<div class="layui-tab-content lr_box">
				<ul class="layui-nav layui-nav-tree left" id="eventDetail">
				</ul>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}frame/customForm/js/getCustomFormDetail.js"></script>
<script>
	$(function() {
		getCustomFormDetail.getCustomFormData({
			"appCode" : $("input[name='appCode']").val(),
			"customFormCode" : $("input[name='customFormCode']").val(),
			"customFormFilledCode" : $("input[name='customFormFilledCode']").val(),
			"hasAtta" : "prev",
			"dom" : "eventDetail"
		});
	})
</script>
</html>
