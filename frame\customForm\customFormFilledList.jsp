<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
	request.setAttribute("funCode", BaseConstant.FUN_CODE_CUSTOM_FORM_FILLED);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>已填表单查询</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var network_err = "网站服务器繁忙，请稍后提交";
	var isSubmit = false;// 防止重复提交
	var funCode = "${funCode}";// 防止重复提交
	var appCode = "${param.appCode}";// 防止重复提交
</script>
</head>
<body>
	<form id="form1" name="form1" class="layui-form" onsubmit="return false;">
		<input type="hidden" id="compNo" name="compNo" value="${compNo}">
		<div class="head0">
			<span class="head1_text fw700">
				<b id="menuIcon"></b>
			</span>
			<div class="head0_right fr">
				
				<label class="layui-form-label2">应用系统</label>
				<div class="layui-input-inline h28 lh28">
					<select name="appCode" lay-filter="appCode"></select>
				</div>
			</div>
		</div>
		<div class="bodys">
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">关键字</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" name="keyword" value="" autocomplete="off" placeholder="表单名称" title="表单名称" class="layui-input">
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="查询" onclick="customFormFilledList.getCustomFormFilledListData();">
				<c:if test="${fn:length(compList) >= 2}">
					<label class="layui-form-label2">医院</label>
					<div class="layui-input-inline">
						<select lay-filter="compNo">
							<c:forEach items="${compList}" var="comp">
								<option <c:if test="${compNo == comp.compNo}">selected="selected"</c:if> value="${comp.compNo}"><c:out value="${comp.compName}"></c:out></option>
							</c:forEach>
						</select>
					</div>
				</c:if>
				<label class="layui-form-label2">状态</label>
				<div class="layui-input-inline h28 lh28">
					<select name="status" lay-filter="status">
						<option value="99" <c:if test="${param.status == 99}">selected</c:if>>全部</option>
						<option value="1" <c:if test="${param.status == 1}">selected</c:if>>有效</option>
						<option value="0" <c:if test="${param.status == 0}">selected</c:if>>无效</option>
					</select>
				</div>
			</div>
			<div class="tableDiv table_noTree">
				<table class="layui-table main_table" cellpadding="0" cellspacing="0">
					<tr class="layui-hide">
						<td align="center">
							<i class="layui-icon layui-icon-search i_check" title="详情" style="cursor: pointer"></i>
						</td>
						<td></td>
						<td></td>
						<td>
							<a href="javascript:;" class="layui-a-hasClick"></a>
						</td>
						<td align="center"></td>
						<td align="center"></td>
						<td align="center"></td>
						<td align="center"></td>
					</tr>
					<tr class="main_title">
						<td width="80">操作</td>
						<td width="130">应用/功能编号</td>
						<td width="230">表单名称</td>
						<td>已填编号</td>
						<td width="130">操作人</td>
						<td width="130">操作时间</td>
						<td width="60">顺序号</td>
						<td width="60">状态</td>
					</tr>
				</table>
				<jsp:include page="/plugins/common/jsp/comLayuiPage.jsp"></jsp:include>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/customForm/js/customFormFilledList.js"></script>