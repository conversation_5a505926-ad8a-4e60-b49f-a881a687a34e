<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>自定义表单</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/search.css" />
<link rel="stylesheet" type="text/css" href="css/customFormList.css">
</head>
<body>
	<form id="form1" name="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="funCode" />
		<input type="hidden" name="customFormTypeCode" />
		
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<label class="layui-form-label2 layui-hide hide-appCode">应用系统</label>
				<div class="layui-input-inline h28 lh28 layui-hide hide-appCode">
					<select name="appCode" lay-filter="appCode"></select>
				</div>
				<input type="button" style="display: none;" id="jsonExportButton" onclick="customFormList.jsonExport()" value="JSON导入" class="layui-btn layui-btn-sm  h28 lh28">
<!-- 				<input type="button" class="layui-btn layui-btn-sm h28 lh28 skin-btn-minor" onclick="customFormList.toCustomFormTypeList();" value="自定义表单分类" /> -->
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="customFormList.customFormEdit({customFormCode:''});" value="新增" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">关键字</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" name="keyword" value="" autocomplete="off" placeholder="表单名称/流程名称" title="表单名称/流程名称" class="layui-input">
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="查询" onclick="customFormList.getCustomFormListData();">
				<label class="layui-form-label2">状态</label>
				<input type="radio" name="status" value="99" title="全部" lay-filter="status" />
				<input type="radio" name="status" value="1" title="有效" lay-filter="status" checked="checked" />
				<input type="radio" name="status" value="0" title="无效" lay-filter="status" />
				<label class="layui-form-label2">类型</label>
				<input type="radio" name="customFormClass" value="99" title="全部" lay-filter="customFormClass" checked="checked" />
				<input type="radio" name="customFormClass" value="0" title="上报" lay-filter="customFormClass" />
				<input type="radio" name="customFormClass" value="1" title="审批" lay-filter="customFormClass" />
			</div>
			<div class="layui-row">
				<div class="treeDiv">
					<div class="treeHead">表单分类</div>
					<!-- tree -->
					<ul id="tree" class="tree-table-tree-box"></ul>
				</div>
				<div class="tableDiv">
					<div id="list" lay-filter="list"></div>
				</div>
			</div>
		</div>
	</form>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
	<form id="exportCustomForm" name="exportCustomForm" action="../../../frame/customForm/exportCustomForm.spring"></form>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/customFormList.js"></script>
<script type="text/javascript">
	$(function() {
		param.appCode = param.appCode || top.pubApp.appCode.replace("APP,", "");
		customFormList.init();
	});
</script>