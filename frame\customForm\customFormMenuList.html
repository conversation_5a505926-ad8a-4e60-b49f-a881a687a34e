<!DOCTYPE html>
<html>
<head>
<title>菜单列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="appCode">
		<input type="hidden" name="customFormTypeCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span class="layui-hide"></span>
				<span>菜单列表</span>
			</span>
		</div>
		<div class="bodys layui-form">
			<div class="layui-row">
				<div class="tableDiv table_noTree table_noSearch">
					<div id="list" lay-filter="list"></div>
					<br>
					<br>
					<br>
					<div class="comTab_Sn">
						<div>【操作说明】</div>
						<br>
						<div class="comTab_SnTxt">
							<li class="comTab_SnLi">
								<strong>
									<i class="layui-icon2" title="生成应用功能">&#xe80c;</i>
									：
								</strong>
								对当前菜单在子系统【
								<span class="appCodeRemark"></span>_CUSTOM_FORM_MENU / 自定义表单菜单】生成对应的【应用功能】、【功能点】
							</li>
							<li class="comTab_SnLi">
								<strong>
									<i class="layui-icon2" title="刷新菜单">&#xe967;</i>
									：
								</strong>
								刷新当前菜单在子系统【
								<span class="appCodeRemark"></span>_CUSTOM_FORM_MENU / 自定义表单菜单】对应的【应用功能（浏览）权限点】的【URL】。
							</li>
							<li class="comTab_SnLi">
								<strong>
									<i class="layui-icon layui-icon-delete" title="删除分类菜单"></i>
									：
								</strong>
								删除当前分类菜单，和其对应在子系统【
								<span class="appCodeRemark"></span>_CUSTOM_FORM_MENU / 自定义表单菜单】的【应用功能与功能点】
							</li>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/customFormMenuList.js"></script>
</html>