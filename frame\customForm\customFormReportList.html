<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>自定义表单上报</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css" />
<link rel="stylesheet" type="text/css" href="../../../frame/leftMenu/css/iconfont.css">
<link rel="stylesheet" type="text/css" href="../../../frame/customForm/css/customFormReportList.css?version=1.1" />
</head>
<body>
	<form id="form1" name="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="funCode" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="customFormTypeCode" />
		<input type="hidden" name="customFormTypeBusinessCode">
		<input type="hidden" name="customFormClass" value="0">
		<input type="hidden" name="status" value="1">
		<input type="hidden" name="type" value="1">
		<input type="hidden" name="hasClose" value="1">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
				<label class="layui-form-label2">关键字</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" name="keyword" value="" autocomplete="off" placeholder="表单名称" title="表单名称" class="layui-input" />
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="查询" onclick="customFormReportList.getFormList();" />
			</span>
			<div class="head0_right fr">
				<label id="checkButtom" style="display: inline; margin-right: 30px; cursor: pointer;" dataValue="1" dataName="列表查看" onclick="customFormReportList.changeIcon(this,true)">
					<i class="icon iconfont" style="margin-bottom: 0px; margin-right: 5px;">&#xe6a6;</i>
					<!--  &#xe6a7; -->
					<span class="list_text" style="font-size: 12px; font-weight: bold; height: 20px; line-height: 22px;">图标查看</span>
				</label>
				<div class="layui-input-inline h28 lh28 layui-hide">
					<select name="compNo" lay-filter="compNo"></select>
					<input type="hidden" name="compNo" />
				</div>
			</div>
		</div>
		<div class="bodys" id="slideTabWrap">
			<div class="leftSum showListType layui-hide"></div>
			<div class="rightCon showListType layui-hide" id="docs-body"></div>
			<div class="showBigIconType layui-hide" style="overflow: auto; position: absolute; top: 10px; width: 100%; bottom: 10px;"></div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="../../../frame/customForm/js/initTypeAndTable.js?version=1.1"></script>
<script type="text/javascript" src="../../../frame/customForm/js/customFormReportList.js?version=1.1"></script>
<script type="text/javascript">
	$(function() {
		param.appCode = param.appCode || "APP";
		customFormReportList.init();
	});
</script>