<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>公共模块</title>
<link rel="stylesheet" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" href="../../plugins/static/css/style.css">
<link rel="stylesheet" type="text/css" href="css/customForm.css?version=*******">
<link rel="stylesheet" type="text/css" href="css/customFormTemplate.css?version=1.2.2.3">
<link rel="stylesheet" type="text/css" href="../../plugins/cropperAvatar/static/css/cropper.min.css">
<link rel="stylesheet" type="text/css" href="../../plugins/cropperAvatar/static/css/ImgCropping.css">
</head>
<body>
	<form id="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<div id="headTool" class="head0 layui-form">
			<input type="hidden" name="type" />
			<input type="hidden" name="customFormCode" />
			<input type="hidden" name="customFormBusinessCode" />
			<input type="hidden" name="customFormFilledCode" />
			<input type="hidden" name="customFormFilledID" />
			<input type="hidden" name="appCode" />
			<input type="hidden" name="status" />
			<input type="hidden" name="createUserCode" />
			<input type="hidden" name="createUserName" />
			<input type="hidden" name="createDate" />
			<input type="hidden" name="compNo" />
			<input type="hidden" name="deptID" />
			<input type="hidden" name="hasBack" value="0" />
			<input type="hidden" name="hasClose" value="0" />
			<input type="hidden" name="funCode"/>
			<input type="hidden" name="hasSave" value="0"/>
			<input type="hidden" name="limitTag" value="0"/>
			<span class="head_text">
				<i id="menuIcon" class="layui-icon2"></i>
				<span id="customFormName"></span>
			</span>
			<div class="head0_right fr">
				<span id="showSaveTimer" class="layui-hide"></span>
				<button type="button" id="draftButton" class="layui-btn layui-btn-sm layui-hide" onclick="customFormTemplate.saveCustomFormFilled(0);">保存草稿</button>
				<button type="button" id="commitButton" class="layui-btn layui-btn-sm layui-hide" lay-submit="" lay-filter="save">提交</button>
				<button type="button" class="layui-btn layui-btn-sm" onclick="customFormTemplate.openOrRetract(this)">全部收起</button>
				<button type="button" id="backButton" class="layui-btn layui-btn-sm layui-hide skin-btn-normal " onclick="history.back()">返回</button>
				<button type="button" id="closeButton" class="layui-btn layui-btn-sm layui-hide skin-btn-normal " onclick="assemblys.closeWindow()">关闭</button>
			</div>
		</div>
		<div id="bodys" class="bodys">
			<div class="table_box"></div>
			<div id="table_box_upload" class="layui-hide">
				<div class="table_right">
					<div class="table_right_title skin-div-css">
						<span class="tableName">附件</span>
					</div>
					<table class="table_right_main">
						<tbody>
							<tr id="upload_td">
								<td colspan="12">
									<div class="layui-form moveInput">
										<label class="layui-form-label item_label">附件上传</label>
										<div class="layui-form-content">
											<div class="layui-form" style="display: inline-block;">
												<button type="button" class="layui-btn layui-btn-sm" onclick="pubUploader.openFiles(customFormTemplate.afterUpfile);">点击上传</button>
											</div>
										</div>
									</div>
								</td>
							</tr>
							<tr>
								<td colspan="12">
									<div class="layui-form moveInput">
										<label class="layui-form-label item_label">附件列表</label>
										<div class="layui-form-content">
											<div class="layui-form" style="display: inline-block;">
												<blockquote id="ueditorFileDiv" class="layui-elem-quote layui-quote-nm" style="min-width: 200px; width: auto; margin-bottom: 0px;"></blockquote>
											</div>
										</div>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</form>
	<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
	<script type="text/javascript" src="../../plugins/layui/assemblys2.js?version=*******"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/ueditor.config.js"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/ueditor.all.min.js"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/lang/zh-cn/zh-cn.js"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/pubUploader.js"></script>
	<script type="text/javascript" src="js/initCustomFormTemplate.js?version=*******"></script>
	<script type="text/javascript" src="js/customFormTemplate.js?version=*******"></script>
	<script type="text/javascript" src="js/pubInterface.js?version=*******"></script>
	<script type="text/javascript" src="js/pubProfile.js?version=*******"></script>
	<script type="text/javascript">
		var isSubmit = false;
		var form = layui.form;
		var type = param.get("type");
		var customFormBusinessCode = param.get("customFormBusinessCode");
		var customFormCode = param.get("customFormCode");
		var customFormFilledCode = param.get("customFormFilledCode");
		var appCode = param.get("appCode");
		var compNo = param.get("compNo");
		var baseImgPath = "";
		$(function() {
			customFormTemplate.start();
		});
	</script>
</body>
</html>