<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<title>新增/编辑表单分类</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/customFormTypeEdit.css?version=2.0.2.1">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="customFormTypeID" value="0">
		<input type="hidden" name="appCode">
		<input type="hidden" name="state" value="1" />
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
				<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table" style="margin-bottom: 0;">
				<fieldset class="layui-elem-field container1 " style="display: block;">
					<legend>分类设置</legend>
					<div class="layui-field-box">
						<div class="layui-form-item">
							<label class="layui-form-label"> 应用 </label>
							<div class="layui-input-inline">
								<select disabled appCode></select>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">
								<span style="color: red;">*</span>
								表单分类名称
							</label>
							<div class="layui-input-inline">
								<input type="text" name="customFormTypeName" value="" class="layui-input" lay-verify="required|limit" limit="200" maxlength="200" />
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">图标风格</label>
							<div class="layui-input-inline">
								<input type="radio" name="customFormTypeIconType" value="2" title="通用型" lay-filter="customFormTypeIconType" checked="checked">
								<input type="radio" name="customFormTypeIconType" value="3" title="医疗风" lay-filter="customFormTypeIconType">
							</div>
							<label class="layui-form-label"> 图标 </label>
							<div class="layui-input-inline">
								<input type="text" id="customFormTypeIcon" name="customFormTypeIcon" value="" readonly="readonly" onclick="customFormTypeEdit.toSelectIcon()" class="layui-input" />
								<i class="layui-select-right"></i>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label"> 业务编号 </label>
							<div class="layui-input-inline">
								<input type="text" name="businessCode" value="" class="layui-input" lay-verify="limit" limit="200" maxlength="200" />
							</div>
							<label class="layui-form-label"> 流程设置</label>
							<div class="layui-input-inline">
								<input type="text" name="customApprovalFlowName" placeholder="点击选择" readonly="readonly" value="" class="layui-input" onclick="customFormTypeEdit.toSelectCustomApprovalCode(this);" />
								<input type="hidden" name="customApprovalFlowCode" value="" />
								<i class="layui-icon2 layui-select-right" onclick="customFormTypeEdit.clearCustomApprovalFlowValue()" title="清空">&#xe68d;</i>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">
								<span style="color: red;">*</span>
								顺序号
							</label>
							<div class="layui-input-inline">
								<input type="text" name="seqNo" value="0" class="layui-input" lay-verify="required|integer|limit" limit="5" maxlength="5" />
							</div>
						</div>
					</div>
				</fieldset>
				<fieldset class="layui-elem-field container1 " style="display: block;">
					<legend>列表配置</legend>
					<div class="layui-field-box">
						<div class="layui-form-item">
							<table class="layui-table">
								<thead>
									<tr>
										<td colspan="6">
											<span style="display: inline-block; height: 40px; line-height: 40px;">
												<strong>表单分类状态</strong>
												<font style="color: blue;">（修改状态编码，需确认【表单状态】是否有作关联设置，否则关联关系会自动失效）</font>
											</span>
											<input type="button" onclick="customFormTypeEdit.addStateItem();" value="新增项" class="layui-btn layui-btn-sm layui-btn-xs fr" style="margin-top: 8px;">
										</td>
									</tr>
									<tr class="main_title">
										<td style="text-align: center" width="60">操作</td>
										<td style="text-align: center">
											<span style="color: red;">*</span>
											状态编码
										</td>
										<td style="text-align: center">
											<span style="color: red;">*</span>
											状态名称
										</td>
										<td style="text-align: center" width="120">状态颜色</td>
										<td style="text-align: center" width="120">
											触发动作
											<i class="layui-icon2" style="color: #6064c8;" onmouseover="assemblys.tips(this,'触发动作是唯一的，用于审批自动流转时触发的必定条件，配置相同会自动取消上一个')"> </i>
										</td>
										<td style="text-align: center" width="100">顺序</td>
									</tr>
								</thead>
								<tbody id="itemsStateView"></tbody>
							</table>
						</div>
						<div class="layui-form-item">
							<table class="layui-table">
								<thead>
									<tr>
										<td colspan="6">
											<span style="display: inline-block; height: 40px; line-height: 40px;">
												<strong> 分类菜单管理 </strong>
												<font style="color: blue;">（配置菜单管理，会根据菜单编码生成【应用功能】，请谨慎定义）</font>
											</span>
											<input type="button" onclick="customFormTypeEdit.addMenuItem();" value="新增项" class="layui-btn layui-btn-sm layui-btn-xs fr" style="margin-top: 8px;">
											<input type="button" onclick="customFormTypeEdit.refreshSelect();" value="刷新状态" class="layui-btn layui-btn-sm layui-btn-xs skin-btn-minor fr" style="margin: 8px 5px 0 0;">
										</td>
									</tr>
									<tr class="main_title">
										<td style="text-align: center" width="60">操作</td>
										<td style="text-align: center">
											<span style="color: red;">*</span>
											菜单编码
										</td>
										<td style="text-align: center">
											<span style="color: red;">*</span>
											菜单名称
										</td>
										<td style="text-align: center" width="340">菜单类型</td>
										<td style="text-align: center">表单状态</td>
									</tr>
								</thead>
								<tbody id="itemsMenuView"></tbody>
							</table>
						</div>
					</div>
				</fieldset>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/customFormTypeEdit.js"></script>
<!-- 颜色选择器 -->
<link rel="stylesheet" href="../../../plugins/bigcolorpicker/css/jquery.bigcolorpicker.css" />
<script type="text/javascript" src="../../../plugins/bigcolorpicker/jquery.bigcolorpicker.js"></script>
<!-- 下拉框 -->
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
<script type="text/javascript" src="../../../plugins/formSelects/formSelects-v4.min.js"></script>
<script type="text/javascript">
	var hasSubmit = false;
	$(function() {
		customFormTypeEdit.init();
	});
</script>