<!DOCTYPE html>
<html>
<head>
<title>自定义表单分类列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="css/customFormTypeList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="currAppCode">
		
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				
				<label class="layui-form-label2 layui-hide hide-appCode">应用系统</label>
				<div class="layui-input-inline h28 lh28 layui-hide hide-appCode">
					<select name="appCode" lay-filter="appCode"></select>
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="customFormTypeList.toEdit()" value="新增" />
<!-- 				<input type="button" class="layui-btn layui-btn-sm h28 lh28 skin-btn-normal" onclick="history.back()" value="返回" /> -->
			</div>
		</div>
		<div class="bodys layui-form">
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">关键字</label>
				<div class="layui-input-inline h28 lh28">
					<input type="text" name="keyword" value="" autocomplete="off" placeholder="表单分类名称" title="表单分类名称" class="layui-input">
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="查询" onclick="customFormTypeList.getCustomFormTypeList();">
				
				<label class="layui-form-label2">状态</label>
				<input type="radio" name="state" value="" title="全部" lay-filter="state" />
				<input type="radio" name="state" value="1" title="有效" lay-filter="state" checked="checked" />
				<input type="radio" name="state" value="0" title="无效" lay-filter="state" />
			</div>
			<div class="layui-row">
				<div class="tableDiv table_noTree" style="top: 45px;">
					<div id="list" lay-filter="list"></div>
				</div>
			</div>
		</div>
	</form>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/customFormTypeList.js"></script>
<script type="text/javascript">
	var hasSubmit = false;
	$(function() {
		customFormTypeList.init();
	});
</script>
</html>