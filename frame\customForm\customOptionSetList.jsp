<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>选项内容填写</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<link rel="stylesheet" type="text/css" href="${basePath}frame/customForm/css/customOptionSetList.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var customFieldCode = "${param.customFieldCode}";
	var customModularCode = "${param.customModularCode}";
	var customFormCode = "${param.customFormCode}";
	var customFieldSet = "${param.customFieldSet}";
	var title = "${param.title}";
	var formUrl = "${param.fromUrl}";
	var appCode = "${param.appCode}";
	var compNo = "${param.compNo}";
</script>
</head>
<body class="body_noTop">
	<form id="form1" name="form1" class="layui-form">
		<input type="hidden" name="customFormCode" value="${param.customFormCode}" />
		<input type="hidden" name="customFieldCode" value="${param.customFieldCode}" />
		<input type="hidden" name="appCode" value="${param.appCode}" />
		<input type="hidden" name="compNo" value="${param.compNo}" />
		<input type="hidden" name="parentCustomOptionSetCode" value="${param.parentCustomOptionSetCode}" />
		<input type="hidden" name="customOptionSetLevel" value="1" />
		<div class="bodys bodys_noTop">
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">无效选项</label>
				<div class="layui-input-inline h28 lh28">
					<input type="checkbox" lay-filter="optStat" lay-skin="switch" lay-text="显示|隐藏">
				</div>
				<span class="setCustomFormClass">
					<c:if test="${param.customFieldSet != 'label'}">
						<label class="layui-form-label2">父级</label>
						<i id="leftIcon" class="layui-icon2" style="position: absolute; left: 195px; top: 15px; cursor: not-allowed; color: #ccc;">&#xe71c;</i>
						<i id="leftIcon2" class="layui-icon2 layui-hide" style="position: absolute; left: 195px; top: 15px; cursor: pointer; color: green;">&#xe71c;</i>
						<div class="inblock h28 lh28" style="width: 362px; height: 28px; position: absolute; left: 207px; top: 10px; overflow: hidden;">
							<div id="mousewheel" style="position: absolute;">
								<div style="position: relative; white-space: nowrap;">
									<a href="javascript:;" style="text-decoration: none; margin-left: 5px;" class="layui-btn layui-btn-sm skin-btn-minor" customOptionSetContent="${param.title}" customOptionSetLevel="1" onclick="customOptionSetList.jumpTo(this);">
										<c:out value="${param.title}" />
									</a>
								</div>
							</div>
						</div>
						<i id="rightIcon" class="layui-icon2" style="position: absolute; right: 280px; top: 15px; cursor: not-allowed; color: #ccc;">&#xe730;</i>
						<i id="rightIcon2" class="layui-icon2 layui-hide" style="position: absolute; right: 280px; top: 15px; cursor: pointer; color: green;">&#xe730;</i>
					</c:if>
				</span>
			</div>
			<div class="head0_right fr">
				<input type="button" value="模板下载" class="layui-btn layui-btn-sm skin-btn-minor setCustomFormClass" onclick="customOptionSetList.downloadTemplate()" />
				<button type="button" class="layui-btn layui-btn-sm skin-btn-minor setCustomFormClass" id="selectExportFile">导入</button>
				<input type="button" class="layui-btn layui-btn-sm  h28 lh28" value="增加" lay-submit="" lay-filter="add" />
				<input type="button" class="layui-btn layui-btn-sm  h28 lh28" value="保存" lay-submit="" lay-filter="save" />
				<input type="button" class="layui-btn layui-btn-sm  skin-btn-normal  h28 lh28" value="返回" onclick="customOptionSetList.upward();" />
			</div>
			<div class="tableDiv table_noTree">
				<table class="layui-table main_table">
					<tr class="main_title">
						<td class="setCustomFormClass" width="50">操作</td>
						<td width="120">选项内容</td>
						<td width="100">业务编号</td>
						<td width="50">业务值</td>
						<td>备注</td>
						<td width="45">顺序号</td>
						<c:if test="${param.customFieldSet == 'radio' || param.customFieldSet == 'checkbox'}">
							<td width="100">控件类型</td>
						</c:if>
						<c:if test="${param.customFieldSet != 'label'}">
							<td width="60">默认选中</td>
						</c:if>
						<td width="150">状态</td>
					</tr>
					<%-- <tr class="comTab_R2"><td align="center" colspan="">没有相关的表单信息</td></tr> --%>
				</table>
				<%-- <jsp:include page="/plugins/common/jsp/comLayuiPage.jsp"></jsp:include> --%>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/customForm/js/customOptionSetList.js?version=*******"></script>