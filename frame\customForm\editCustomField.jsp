<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);

	//图片路径
	String readUrl = application.getInitParameter("BaseRealUrl");
	String baseImgPath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ "/" + readUrl;
	request.setAttribute("baseImgPath", baseImgPath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>编辑组件</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<link rel="stylesheet" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" href="${basePath}plugins/static/css/edit.css">
<link rel="stylesheet" href="${basePath}frame/customForm/css/editCustomField.css">
</head>
<body class="body_noTop">
	<div class="bodys bodys_noTop">
		<form class="layui-form" action="" lay-filter="param">
			<input type="hidden" id="customFormCode" name="customFormCode" value="${param.customFormCode}" />
			<input type="hidden" id="customFieldID" name="customFieldID" value="" />
			<input type="hidden" id="customFieldCode" name="customFieldCode" value="" />
			<input type="hidden" id="customModularCode" name="customModularCode" value="${param.customModularCode}" />
			<input type="hidden" name="appCode" value="${param.appCode}" />
			<input type="hidden" name="compNo" value="${param.compNo}" />
			<input type="hidden" name="seqNo" value="${param.seqNo}" />
			<input type="hidden" name="status" value="1" />
			<input type="hidden" name="customFieldRowCode" value="${param.customFieldRowCode}" />
			<input type="hidden" name="createUserCode" value="" />
			<input type="hidden" name="createUserName" value="" />
			<input type="hidden" name="createDate" value="" />
			<input type="hidden" name="isCommon" value="0" />
			<input type="hidden" name="isRichText" value="0" />
			<input type="hidden" name="commonCustomFieldID" />
			<input type="hidden" name="customFormTypeCode" value="${param.customFormTypeCode}" />
			<input type="hidden" name="customFormClass" value="${param.customFormClass}" />
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					组件名称
				</label>
				<div class="layui-input-inline">
					<input type="text" id="customFieldName" name="customFieldName" lay-verify="required|limit" limit="200" autocomplete="off" class="layui-input">
				</div>
				<label class="layui-form-label">控件</label>
				<div class="layui-input-inline">
					<input type="hidden" id="customFieldSet" name="customFieldSet" value="">
					<input type="text" id="customFieldSetText" class="layui-input" readonly="readonly" value="">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">是否必填</label>
				<div class="layui-input-inline">
					<input type="checkbox" name="isNecessField" lay-skin="switch" value="1" lay-text="是|否">
				</div>
				<label class="layui-form-label">移动端上报</label>
				<div class="layui-input-inline">
					<input type="checkbox" name="isMobile" lay-skin="switch" value="1" lay-text="是|否" checked="checked">
				</div>
			</div>
			<c:if test="${param.customFieldSet != 'img'}">
				<div class="layui-form-item">
					<label class="layui-form-label">业务编号</label>
					<div class="layui-input-inline">
						<input type="text" name="businessCode" lay-verify="businessCode|limit" limit="100" class="layui-input" value="">
					</div>
					<label class="layui-form-label">
					业务值
					<i class="layui-icon2" onmouseover="assemblys.tips(this,'普通文本组件配置以‘total_’为前缀的业务值将自动计算该组件的不支持新增的分类下所有单选组件多选组件的选项业务值（必须是整数）到该组件下。')"> </i>
					</label>
					<div class="layui-input-inline">
						<input type="text" name="businessValue" class="layui-input" value="" lay-verify="limit" limit="100">
					</div>
				</div>
			</c:if>
			<div class="specialConfig">
				<c:if test="${param.customFieldSet == 'org' }">
					<div class="layui-form-item">
						<label class="layui-form-label"> 功能点（组织架构） </label>
						<div class="layui-input-inline layui-form" lay-filter="funCodeDiv">
							<select name="funCode" lay-filter="funCode">
								<option></option>
							</select>
						</div>
						<label class="layui-form-label"> 选择对象 </label>
						<div class="layui-input-inline layui-form" lay-filter="relationFieldDiv">
							<select name="relationField" lay-filter="relationField">
								<option value="dept">科室</option>
								<option value="user">人员</option>
							</select>
						</div>
					</div>
				</c:if>
				<c:if test="${param.customFieldSet == 'datetime'}">
					<div class="layui-form-item">
						<label class="layui-form-label"> 时间范围 </label>
						<div class="layui-input-inline">
							<select name="dateRange" lay-filter="dateRange">
								<option value="-1">请选择</option>
								<option value="0">自定义</option>
								<option value="1">小于等于填写日期</option>
								<option value="2">大于等于填写日期</option>
							</select>
						</div>
						<label dateRange class="layui-form-label layui-hide">
							<span style="color: red">*</span>
							自定义区间
						</label>
						<div dateRange class="layui-input-inline layui-hide">
							<input readonly="readonly" class="layui-input" type="text" id="dateRange" />
							<input readonly="readonly" class="layui-input" type="hidden" id="beginDate" name="beginDate" />
							<input readonly="readonly" class="layui-input" type="hidden" id="endDate" name="endDate" />
						</div>
					</div>
				</c:if>
				<div class="layui-form-item">
					<c:if test="${param.customFieldSet == 'text' || param.customFieldSet == 'textarea' }">
						<span class="readOrRemind layui-hide">
							<!-- 默认值 -->
							<c:set var="defaultLength" value="${param.customFieldSet == 'text' ? 200 : 2000}" />
							<label class="layui-form-label">
								<span style="color: red">*</span>
								文本长度
							</label>
							<div class="layui-input-inline">
								<input type="text" name="customFieldLength" class="layui-input" value="${defaultLength }" lay-verify="required|isNum">
							</div>
						</span>
					</c:if>
					<c:if test="${param.customFieldSet == 'interface'}">
						<label class="layui-form-label">
							接口开关
							<i class="layui-icon2" onmouseover="assemblys.tips(this,'用于控制【移动端】是否启用接口功能',3000,'right')"> </i>
						</label>
						<div class="layui-input-inline">
							<input type="checkbox" name="interfaceSwitch" lay-skin="switch" value="1" lay-text="是|否">
						</div>
					</c:if>
					<span class="readOrRemind layui-hide">
						<div class="setCustomFormClass">
							<c:choose>
								<c:when test="${param.customFieldSet == 'text' || param.customFieldSet == 'textarea' || param.customFieldSet == 'datetime' || param.customFieldSet == 'interface'}">
									<label class="layui-form-label">
										<c:if test="${param.fieldSet == 'interface' || param.fieldSet == 'datetime'}">
											<span style="color: red">*</span>
										</c:if>
										${param.customFieldSet == 'text' || param.customFieldSet == 'textarea' ? '校验' : ''} ${param.customFieldSet == 'datetime' ? '时间格式' : ''} ${param.customFieldSet == 'interface' ? '接口' : ''}
									</label>
									<div class="layui-input-inline">
										<c:if test="${param.customFieldSet == 'datetime' || param.customFieldSet == 'text' || param.customFieldSet == 'textarea'}">
											<select name="fieldVerifyType" lay-verify="${param.customFieldSet == 'datetime'  ? 'required' : ''}" lay-filter="fieldVerifyType">
												<c:if test="${param.customFieldSet == 'text' || param.customFieldSet == 'textarea'}">
													<option value="">不需要校验</option>
												</c:if>
												<c:if test="${param.customFieldSet == 'datetime'}">
													<option value="date">yyyy-MM-dd</option>
													<option value="datetime">yyyy-MM-dd HH:mm</option>
												</c:if>
											</select>
										</c:if>
										<c:if test="${param.customFieldSet == 'interface'}">
											<input type="text" name="fieldVerifyType" class="layui-input" value="" lay-verify="limit" limit="100">
										</c:if>
									</div>
								</c:when>
								<c:otherwise>
									<input type="hidden" name="fieldVerifyType" value="" />
								</c:otherwise>
							</c:choose>
						</div>
					</span>
				</div>
			</div>
			<c:if test="${param.customFieldSet == 'text' || param.customFieldSet == 'textarea' }">
				<div class="layui-form-item readOrRemind layui-hide">
					<label class="layui-form-label"> 是否只读</label>
					<div class="layui-input-inline">
						<input type="checkbox" name="isRead" lay-skin="switch" value="1" lay-filter="isRead" lay-text="是|否">
					</div>
				</div>
				<div class="layui-form-item">
					<span class="readOrRemind layui-hide">
						<label class="layui-form-label"> 提示语 </label>
						<div class="layui-input-block">
							<textarea name="remindText" autocomplete="off" lay-verify="limit" limit="200" class="layui-textarea"></textarea>
						</div>
					</span>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span class="must layui-hide" style="color: red">*</span>
						默认值
						<i class="layui-icon2" onmouseover="assemblys.tips(this,'首次填报应用默认值时，如果超过【文本长度】会自动截取',3000,'right')"> </i>
					</label>
					<div class="layui-input-block">
						<textarea id="defaultValueTextarea" name="defaultValue" autocomplete="off" class="layui-textarea"></textarea>
					</div>
				</div>
			</c:if>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline" style="width: 400px;">
					<button type="button" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
					<button type="button" id="addSub" class="layui-btn layui-btn-sm layui-hide" onclick="editCustomField.addSub();">添加子选项</button>
					<button type="button" id="addCommon" class="layui-btn layui-btn-sm layui-hide setCustomFormClass" onclick="editCustomField.addCommon();">升级为公用组件</button>
					<c:if test="${param.customFieldSet == 'textarea' }">
						<button type="button" id="upgradeRichTextButton" class="layui-btn layui-btn-sm layui-hide" onclick="editCustomField.upgradeRichText();">升级为富文本域</button>
					</c:if>
					<button type="button" class="layui-btn layui-btn-sm layui-hide setCustomFormClass" onclick="editCustomField.preview();">预览组件</button>
				</div>
			</div>
		</form>
		<iframe id="uploadFrame" name="uploadFrame" style="display: none;"></iframe>
		<form id="fieldForm" name="fieldForm" action="${basePath}frame/customForm/saveImg.spring" method="post" enctype="multipart/form-data" class="layui-form" target="uploadFrame" lay-filter="fieldForm">
			<input type="hidden" id="customFieldCode" name="customFieldCode" value="" />
			<div class="layui-form-item <c:if test="${empty param.customFieldCode || param.customFieldSet != 'img'}">layui-hide</c:if>">
				<label class="layui-form-label">预览</label>
				<div class="layui-input-inline">
					<div id="preImgDiv" style="margin: 0 auto; height: 100px;">
						<img id="imgShow" name="imgURL" src="" width="100" height="100" onclick="editCustomField.previewImg()">
					</div>
				</div>
				<label class="layui-form-label">上传图片</label>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-sm">
						<i class="layui-icon">&#xe681;</i>
						选择图片
					</button>
					<button type="button" class="layui-btn layui-btn-sm" onclick="editCustomField.saveImg();">
						<i class="layui-icon">&#xe67c;</i>
						保存图片
					</button>
					<input type="file" id="imageurl" name="imageurl" title=" " accept="image/jpg, image/png, image/jpeg, image/gif" onchange="editCustomField.chooseImg(this);">
					<br />
					<font color="red">建议大小:</font>
					少于1M
					<br />
					<font color="red">支持类型:</font>
					gif、jpg、png、jpeg
				</div>
			</div>
		</form>
	</div>
	<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
	<script type="text/javascript" src="${basePath}plugins/layui/assemblys2.js"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/ueditor.config.js"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/ueditor.all.min.js"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/lang/zh-cn/zh-cn.js"></script>
	<script type="text/javascript" charset="utf-8" src="../../plugins/fileUpload/pubUploader.js"></script>
	<script script type="text/javascript" src="${basePath}frame/customForm/js/editCustomField.js?version=4.0"></script>
	<script type="text/javascript">
		var basePath = "${basePath}";
		var baseImgPath = "${baseImgPath}";
		var isSubmit = false;
		var customFieldCode = "${param.customFieldCode}" || "";
		var customFieldSet = "${param.customFieldSet}";
		var customModularCode = "${param.customModularCode}";
		var customFormCode = "${param.customFormCode}";
		var appCode = "${param.appCode}";
		var compNo = "${param.compNo}";
	</script>
</body>
</html>