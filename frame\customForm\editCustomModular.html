<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>编辑分类</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<link rel="stylesheet" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" href="../../plugins/static/css/edit.css">
</head>
<body class="body_noTop">
	<form class="layui-form" action="" lay-filter="param">
		<input type="hidden" name="commonCustomModularID" />
		<input type="hidden" name="customFormCode" />
		<input type="hidden" name="customModularID" />
		<input type="hidden" name="customModularCode" />
		<input type="hidden" name="status" value="1" />
		<input type="hidden" name="createUserCode" />
		<input type="hidden" name="createUserName" />
		<input type="hidden" name="createDate" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="compNo" />
		<input type="hidden" name="isCommon" value="0" />
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					分类名称
				</label>
				<div class="layui-input-inline">
					<input type="text" name="customModularName" lay-verify="required|limit" limit="50" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 业务编号 </label>
				<div class="layui-input-inline">
					<input type="text" name="businessCode" lay-verify="businessCode|limit" limit="100" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"> 业务值 </label>
				<div class="layui-input-inline">
					<input type="text" name="businessValue" lay-verify="limit" limit="100" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item setCustomFormClass">
				<label class="layui-form-label"> 是否支持新增 </label>
				<div class="layui-input-inline">
					<input type="checkbox" name="hasAdd" autocomplete="off" class="layui-input" lay-skin="switch" value="1" lay-text="是|否">
				</div>
			</div>
			<div class="layui-form-item setCustomFormClass">
				<label class="layui-form-label"> 表格风填报 </label>
				<div class="layui-input-inline">
					<input type="checkbox" name="isTable" autocomplete="off" class="layui-input" lay-skin="switch" value="1" lay-text="是|否">
				</div>
			</div>
			<label class="layui-form-label"></label>
			<div class="layui-input-inline" style="width: 300px;">
				<button type="button" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
				<button type="button" class="layui-btn layui-btn-sm layui-bg-black" onclick="assemblys.closeWindow()">关闭</button>
				<button type="button" id="addCommon" class="layui-btn layui-btn-sm layui-hide setCustomFormClass" onclick="editCustomModular.setCommon()">升级为公用分类</button>
			</div>
		</div>
		</div>
	</form>
	<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
	<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
	<script script type="text/javascript" src="js/editCustomModular.js"></script>
	<script type="text/javascript">
		var isSubmit = false;
		$(function() {
			editCustomModular.init();
		});
	</script>
</body>
</html>