<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>审核意见</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" href="${basePath}plugins/static/css/edit.css">
<script type="text/javascript">
	var hasSubmit = false;
</script>
</head>
<body class="body_noTop">
	<form id="form1" class="layui-form" lay-filter="param">
		<input type="hidden" name="customTextareaTemplateID" value='<c:out value="${param.customTextareaTemplateID}"/>' />
		<input type="hidden" name="customFieldCode" value='<c:out value="${param.customFieldCode}"/>' />
		<input type="hidden" name="compNo" value='<c:out value="${param.compNo}"/>' />
		<input type="hidden" name="appCode" value='<c:out value="${param.appCode}"/>' />
		<input type="hidden" name="createUserCode" value="" />
		<input type="hidden" name="createUserName" value="" />
		<input type="hidden" name="createDate" value="" />
		<div class="bodys bodys_noTop">

			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					模板标题
				</label>
				<div class="layui-input-inline">
					<input type="text" name="customTextareaTemplateTitle" lay-verify="required|limit" limit="200" autocomplete="off" class="layui-input" value="">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					共享范围
				</label>
				<div class="layui-input-inline">
					<input type="radio" name="multi" lay-verify="required|limit" checked="checked" value="1" title="个人"  />
					<input type="radio" name="multi" lay-verify="required|limit" value="2" title="本科室"  />
					<input type="radio" name="multi" lay-verify="required|limit" value="3" title="全院"  />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					模板内容
				</label>
				<div class="layui-input-inline">
					<textarea name="customTextareaTemplateContent" class="layui-textarea" lay-verify="required|limit" limit="2000"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
		</div>
	</form>
	<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
	<script type="text/javascript" src="${basePath}plugins/layui/assemblys2.js"></script>
	<script type="text/javascript" src="${basePath}frame/customForm/js/eidtCustomTextareaTemplate.js"></script>
</body>
</html>