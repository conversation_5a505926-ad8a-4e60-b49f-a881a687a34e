<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>接口内容</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/search.css" />
<link rel="stylesheet" type="text/css" href="css/interfaceDataList.css" />
</head>
<body class="body_noTop">
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="customFieldCode" />
		<input type="hidden" name="interfaceCode" />
		<input type="hidden" name="selectInputType" />
		<input type="hidden" name="index" />
		<div class="bodys bodys_noTop">
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm  h28 lh28" value="确定" onclick="interfaceDataList.selected();" />
				<input id="back" type="button" class="layui-btn layui-btn-sm h28 lh28 layui-bg-black" value="关闭" onclick="interfaceDataList.back();">
			</div>
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label2">高级查询</label>
				<div class="layui-input-inline h28 lh28 layui-form" lay-filter="conditionDiv">
					<select name="condition" lay-filter="condition"></select>
				</div>
				<div type="string" class="layui-input-inline h28 lh28" style="display: none;">
					<input type="text" value="" autocomplete="off" placeholder="关键字" title="关键字" class="layui-input">
				</div>
				<div type="date" class="layui-input-inline h28 lh28" style="display: none;">
					<input type="text" id="dateTime" value="" autocomplete="off" placeholder="时间范围" title="时间范围" class="layui-input">
				</div>
				<input type="button" class="layui-btn layui-btn-sm h28 lh28" value="查询" onclick="page.set('curPageNum','1');interfaceDataList.getInterfaceQueryList();">
				<span id="conditionDate" style="display: none;">
					<label class="layui-form-label2"></label>
					<div class="layui-input-inline h28 lh28">
						<input type="text" id="dateTime2" name="" value="" autocomplete="off" placeholder="时间范围" title="时间范围" class="layui-input">
					</div>
				</span>
			</div>
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
			<div id="tablePage" class="tablePage"></div>
		</div>
	</form>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" />
		<input type="hidden" name="pageSize" />
	</form>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/interfaceDataList.js?version=*******"></script>
<script type="text/javascript">
	$(function() {
		interfaceDataList.init();
	});
</script>