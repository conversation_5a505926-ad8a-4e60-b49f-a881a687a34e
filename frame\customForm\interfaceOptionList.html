<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Language" content="zh-cn" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>选项内容填写</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/search.css" />
<link rel="stylesheet" type="text/css" href="css/interfaceOptionList.css" />
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="title" />
		<input type="hidden" name="customModularCode" />
		<input type="hidden" name="interfaceCode" />
		<input type="hidden" name="customFieldCode" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="compNo" />
		<input type="hidden" name="fromUrl" />
		<div class="head0">
			<span class="head_text"> 注：当前组件绑定主接口的字段才能选择是否参数，其他字段来自关联接口，只供回显</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm  h28 lh28" value="保存" onclick="interfaceOptionList.doFieldSave();" />
				<input type="button" class="layui-btn layui-btn-sm  skin-btn-normal  h28 lh28" value="返回" onclick="location.href = param.get('fromUrl');" />
			</div>
		</div>
		<div class="bodys layui-form">
			<div class="layui-row">
				<div class="tableDiv table_noTree" style="top: 5px;">
					<div id="list"></div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/interfaceOptionList.js?version=*******"></script>
<script type="text/javascript">
	var isSubmit = false;
	var interfaceCode = "";
	var customModularCode = "";
	var customFieldCode = "";
	var compNo = "";
	var appCode = "";
	var date = "";
	$(function() {
		parent.$("#layer-editField").prev().html(assemblys.escape(param.get("title")));
		interfaceCode = assemblys.escape(param.get("interfaceCode"));
		customModularCode = assemblys.escape(param.get("customModularCode"));
		customFieldCode = assemblys.escape(param.get("customFieldCode"));
		compNo = assemblys.escape(param.get("compNo"));
		appCode = assemblys.escape(param.get("appCode"));
		
		interfaceOptionList.getInterfaceOptionList().then(function() {
			return interfaceOptionList.getInterfaceItemList();
		});
		
		var form = layui.form;
		form.on("select(interfaceItemCode)", function(data) {
			var level = $(data.elem).find("option:selected").attr("level");
			var $switch = $(data.elem).parents("td").next().find(".keywordTemp");
			if (level && level == 1) {
				$switch.removeClass("layui-hide");
			} else {
				$switch.addClass("layui-hide");
				$switch.find("input").removeAttr('checked');
				form.render('checkbox');
			}
			
		});
		form.render();
		
	});
</script>