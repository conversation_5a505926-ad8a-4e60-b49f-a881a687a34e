var customFieldTemplate = {
	save : function() {
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		$.ajax({
			url : basePath + "frame/customForm/saveCustomFormFilled.spring",
			dataType : "json",
			type : "post",
			data : $("#form1").serialize(),
			success : function(data) {
				if (data.result == "success") {
					assemblys.msg("保存成功", function() {
						if (parent.saveCallback) {
							$.each(parent.saveCallback, function(name, value) {
								if (typeof value == "function") {
									value.call(parent.window, data);
								}
							});
						}
					});
				} else {
					assemblys.alert("保存出错");
					isSubmit = false;
				}
			},
			error : function(a, b, c) {
				assemblys.alert("保存出错！错误信息：" + c);
				isSubmit = false;
			}
		});
		
	}
}