var customForm = {
	init : function() {
		document.onselectstart = function() {
			return false;
		};
		assemblys.getMenuIcon("CUSTOM_FORM", false, $("#menuIcon")[0]);
		customForm.addCustomFormClassStyle(document.getElementsByTagName("head")[0]);
		customForm.initCustomFieldSet();
		customForm.getDeletedCustomField();
		customForm.getDeletedCustomModular();
		customForm.getOptionRelation().then(function() {
			return customForm.getCommonCustomModular();
		}).then(function() {
			return customForm.getCommonCustomField();
		}).then(function() {
			if (customFormCode) {
				return initCustomForm.getCustomFormData();
			}
		}).then(function() {
			$("#systemComponents div").each(function(i, element) {
				if ($("label[customFieldCode=" + this.getAttribute("customFieldCode") + "]").length > 0) {
					$(this).addClass("layui-hide");
				}
			});
			
			$("#systemClassifications div").each(function(i, element) {
				if ($("div.table_box div[customModularCode=" + this.getAttribute("customModularCode") + "]").length > 0) {
					$(this).addClass("layui-hide");
				}
			});
			layui.element.render();
			
			customForm.events();
		});
		
	},
	// 动态添加隐藏样式
	addCustomFormClassStyle : function(head) {
		// 如果是审批表单
		if (customFormClass == "1") {
			// 追加样式
			var style = document.createElement("style");
			style.type = "text/css";
			var classStyle = ".setCustomFormClass{display:none;}";
			try {
				style.appendChild(document.createTextNode(classStyle));
			} catch (ex) {
				style.styleSheet.cssText = classStyle;
			}
			head.appendChild(style);
		} else {// 不是审批表单才显示模板下载和导入
			$("#selectExportFile").removeClass("layui-hide").prev().removeClass("layui-hide");
		}
	},
	openOrRetract : function(obj) {
		if (obj.num == 1) {
			var $siblings = $(".table_box").find(".table_right").find(".table_right_title");
			$siblings.next().removeClass("layui-hide");
			$siblings.children("i").html("&#xe920;");
			
			$(obj).text("全部收起");
			obj.num = 0;
		} else {
			var $siblings = $(".table_box").find(".table_right").find(".table_right_title");
			$siblings.next().addClass("layui-hide");
			$siblings.children("i").html("&#xe922;");
			
			$(obj).text("全部展开");
			obj.num = 1;
		}
	},
	toCustomFormTemplate : function() {
		
		if (customFormClass == "0") {
			var url = "customFormTemplate.html";
			location.url({
				"url" : url,
				"param" : {
					"customFormCode" : customFormCode,
					"type" : "0",
					"compNo" : compNo,
					"appCode" : appCode,
					"hasBack" : "1"
				}
			});
		} else {
			layer.open({
				type : 2,
				closeBtn : 0,
				skin : 'layui-layer-aems',
				area : [ '850px', '90%' ],
				title : false,
				scrollbar : false,
				content : "customFormApprovalTemplate.html?funCode=" + funCode + "&appCode=" + appCode + "&customFormCode=" + customFormCode + "&submitType=0&inLoop=0"
			});
		}
		
	},
	initCustomFieldSet : function() {
		$("div#customFieldSetList div[customFieldSet='text']")[0].dict = {
			dictCode : "text",
			dictName : "文本框",
			remark : '<i class="layui-icon i_5"></i>',
			dictContent : '<input type="text"  readonly="readonly"  name="text"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input input_item layui-input-draft">'
		};
		$("div#customFieldSetList div[customFieldSet='textarea']")[0].dict = {
			dictCode : "textarea",
			dictName : "文本域",
			remark : '<i class="layui-icon i_6"></i>',
			dictContent : '<textarea  readonly="readonly" name="desc" placeholder="请输入内容" class="layui-textarea"></textarea>'
		};
		$("div#customFieldSetList div[customFieldSet='datetime']")[0].dict = {
			dictCode : "datetime",
			dictName : "日期",
			remark : '<i class="layui-icon i_7"></i>',
			dictContent : '<i class="layui-icon layui-icon-draft">&#xe637;</i> 日期'
		};
		$("div#customFieldSetList div[customFieldSet='radio']")[0].dict = {
			dictCode : "radio",
			dictName : "单选框",
			remark : '<i class="layui-icon i_8"></i>',
			dictContent : '<div class="layui-unselect layui-form-radio layui-form-radioed   layui-form-draft "><i class="layui-anim layui-icon">&#xe643;</i><div>单选框</div></div>'
		};
		$("div#customFieldSetList div[customFieldSet='checkbox']")[0].dict = {
			dictCode : "checkbox",
			dictName : "多选框",
			remark : '<i class="layui-icon i_9"></i>',
			dictContent : '<div class="layui-unselect layui-form-checkbox layui-form-checked layui-form-draft " lay-skin="primary" style="display:inline-block;width:auto;"><span>多选框</span><i class="layui-icon" style="margin-top: 0px" >&#xe605;</i></div>'
		};
		$("div#customFieldSetList div[customFieldSet='label']")[0].dict = {
			dictCode : "label",
			dictName : "标签",
			remark : '<i class="layui-icon i_10"></i>',
			dictContent : '<i class="layui-icon2 layui-icon-draft ">&#xe714;</i> 标签'
		};
		$("div#customFieldSetList div[customFieldSet='select']")[0].dict = {
			dictCode : "select",
			dictName : "下拉框",
			remark : '<i class="layui-icon i_11"></i>',
			dictContent : '<div class="layui-input-inline" ><div class="layui-unselect layui-form-select"><div class="layui-select-title"><input type="text" placeholder="下拉框" value="" readonly="" class="layui-input layui-unselect"><i class="layui-edge"></i></div></div></div>'
		};
		/*$("div#customFieldSetList div[customFieldSet='img']")[0].dict = {
			dictCode : "img",
			dictName : "图片",
			remark : '<i class="layui-icon i_16"></i>',
			dictContent : '<i class="layui-icon layui-icon-draft ">&#xe64a;</i>'
		};*/
		$("div#customFieldSetList div[customFieldSet='org']")[0].dict = {
			dictCode : "org",
			dictName : "组织架构",
			remark : '<i class="layui-icon i_17"></i>',
			dictContent : '<div class="layui-input-inline" ><div class="layui-unselect layui-form-select"><div class="layui-select-title"><input type="text"  readonly="readonly" name="text" required="" lay-verify="required" placeholder="" autocomplete="off" class="layui-input input_item layui-input-draft ">  <i class="layui-icon">&#xe615;</i></div></div></div>'
		};
		$("div#customFieldSetList div[customFieldSet='interface']")[0].dict = {
			dictCode : "interface",
			dictName : "接口",
			remark : '<i class="layui-icon"></i>',
			dictContent : '<div class="layui-input-inline" ><div class="layui-unselect layui-form-select"><div class="layui-select-title"><input type="text" placeholder="接口" value="" readonly="" class="layui-input layui-unselect"><i class="layui-edge"></i></div></div></div>'
		};
		$("div#customFieldSetList div[customFieldSet='profile']")[0].dict = {
			dictCode : "profile",
			dictName : "头像框",
			remark : '<i class="layui-icon i_16"></i>',
			dictContent : '<div class="layui-input-inline" ><div class="layui-unselect layui-form-select"><div class="layui-select-title"><input type="text" placeholder="头像框" value="" readonly="" class="layui-input layui-unselect"><i class="layui-edge"></i></div></div></div>'
		};
		$("div#customFieldSetList div[customFieldSet='file']")[0].dict = {
			dictCode : "file",
			dictName : "附件",
			remark : '<i class="layui-icon2">&#xe7af;</i>',
			dictContent : '<div class="layui-input-inline" ><div class="layui-unselect layui-form-select"><div class="layui-select-title"><input type="text" placeholder="附件" value="" readonly="" class="layui-input layui-unselect"><i class="layui-edge"></i></div></div></div>'
		};
	},
	getOptionRelation : function() {
		var url = basePath + "frame/newCustomForm/getCustomRelatedList.spring";
		return $.ajax({
			url : url,
			dataType : "json",
			data : {
				"customFormCode" : customFormCode,
				"compNo" : compNo,
				"appCode" : appCode
			},
			success : function(data) {
				customForm.relationCodeMap = data.relationCodeMap;
				customForm.customOptionSetCodeMap = data.customOptionSetCodeMap;
			}
		});
	},
	getCommonCustomField : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCommonCustomField.spring",
			dataType : "json",
			data : {
				"appCode" : appCode,
				"customFormTypeCode" : customFormTypeCode,
				"customFormClass" : customFormClass
			},
			success : function(data) {
				var commonCustomFieldList = data.commonCustomFieldList
				for (var i = 0; i < commonCustomFieldList.length; i++) {
					var dict = $("#customFieldSetList").children("div." + commonCustomFieldList[i].customFieldSet)[0].dict;
					$("#systemComponents").append("<div class='left_item common' unselectable='on'  title='" + commonCustomFieldList[i].customFieldName + "' customFieldCode='" + commonCustomFieldList[i].customFieldCode + "' businessCode='" + commonCustomFieldList[i].businessCode + "' customFieldSet='" + commonCustomFieldList[i].customFieldSet + "'>" + dict.remark + commonCustomFieldList[i].customFieldName + "</div>");
					$("#systemComponents").children(":last")[0].customField = commonCustomFieldList[i];
				}
			}
		});
	},
	getDeletedCustomField : function() {
		$.ajax({
			url : basePath + "frame/newCustomForm/getDeletedCustomField.spring",
			dataType : "json",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : appCode
			},
			success : function(data) {
				$("#deletedCustomFields").empty();
				var deletedCustomFieldList = data.deletedCustomFieldList
				for (var i = 0; i < deletedCustomFieldList.length; i++) {
					var dict = $("#customFieldSetList").children("div." + deletedCustomFieldList[i].customFieldSet)[0].dict;
					
					$("#deletedCustomFields").append("<div class='left_item deletedCustomField' unselectable='on'  title='" + deletedCustomFieldList[i].customFieldName + "' customFieldCode='" + deletedCustomFieldList[i].customFieldCode + "' businessCode='" + deletedCustomFieldList[i].businessCode + "' customFieldSet='" + deletedCustomFieldList[i].customFieldSet + "'>" + dict.remark + deletedCustomFieldList[i].customFieldName + "</div>");
					$("#deletedCustomFields").children(":last")[0].customField = deletedCustomFieldList[i];
				}
				
			}
		});
	},
	getCommonCustomModular : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCommonCustomModular.spring",
			dataType : "json",
			data : {
				"appCode" : appCode,
				"compNo" : compNo
			},
			success : function(data) {
				var commonCustomModularList = data.commonCustomModularList;
				var commonCustomModularAry = [];
				for (var i = 0; i < commonCustomModularList.length; i++) {
					var div = {
						"attr" : {
							"unselectable" : "on",
							"customModularCode" : commonCustomModularList[i].customModularCode
						},
						"tagName" : "div",
						"className" : "left_item commonModular",
						"title" : commonCustomModularList[i].customModularName,
						"customModular" : commonCustomModularList[i],
						"children" : [ {
							"tagName" : "i",
							"className" : "layui-icon i_18"
						}, {
							"tagName" : "span",
							"innerText" : commonCustomModularList[i].customModularName
						} ]
					};
					commonCustomModularAry.push(div);
				}
				assemblys.createElement(commonCustomModularAry, $("#systemClassifications")[0]);
			}
		});
	},
	getDeletedCustomModular : function() {
		$.ajax({
			url : basePath + "frame/newCustomForm/getDeletedCustomModular.spring",
			dataType : "json",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : appCode,
				"compNo" : compNo
			},
			success : function(data) {
				$("#deletedClassifications").empty();
				var deletedCustomModularList = data.deletedCustomModularList;
				var customModularAry = [];
				for (var i = 0; i < deletedCustomModularList.length; i++) {
					var div = {
						"attr" : {
							"unselectable" : "on",
							"customModularCode" : deletedCustomModularList[i].customModularCode
						},
						"tagName" : "div",
						"className" : "left_item deletedModular",
						"title" : deletedCustomModularList[i].customModularName,
						"customModular" : deletedCustomModularList[i],
						"children" : [ {
							"tagName" : "i",
							"className" : "layui-icon i_18"
						}, {
							"tagName" : "span",
							"innerText" : deletedCustomModularList[i].customModularName
						} ]
					};
					customModularAry.push(div);
				}
				assemblys.createElement(customModularAry, $("#deletedClassifications")[0]);
				
			}
		});
	},
	merge : function(tr) {
		$.ajax({
			url : basePath + "frame/newCustomForm/updateMergeWithNextRowStatus.spring",
			dataType : "json",
			data : {
				"customFieldRowCode" : tr.getAttribute("customFieldRowCode"),
				"appCode" : appCode
			},
			success : function(data) {
				assemblys.msg("修改合并状态成功");
				var mergeWithNextRow = data.mergeWithNextRow;
				// 更新对象
				tr.customFieldRow.mergeWithNextRow = mergeWithNextRow;
				if (mergeWithNextRow == 1) {
					$(tr).addClass("mergeWithNextRow");
				} else {
					$(tr).removeClass("mergeWithNextRow");
				}
			}
		});
	},
	deleteRow : function(dom, cols) {
		var deleteCustomFieldRow = function() {
			var customFieldRowCode = $(dom).attr("customFieldRowCode");
			var customModularCode = $(dom).parents("[customModularCode]").attr("customModularCode");
			$.ajax({
				url : basePath + "frame/newCustomForm/deleteCustomFieldRow.spring",
				dataType : "json",
				type : "post",
				data : {
					"customModularCode" : customModularCode,
					"customFieldRowCode" : customFieldRowCode,
					"customFormCode" : customFormCode,
					"appCode" : appCode
				},
				success : function(data) {
					
					$(dom).find("label[customFieldSet]").each(function(i, e) {
						$("#systemComponents div[customFieldCode='" + $(this).attr("customFieldCode") + "']").show();
					});
					
					if (cols) {
						var table_right_main = $(dom).parents("table.table_right_main")[0];
						var customFieldRow = {
							"customFieldRowCode" : "tr_tempCode",
							"cols" : cols
						};
						
						// 创建行
						var tr = initCustomForm.createTr(null, customFieldRow);
						var trDom = assemblys.createElement(tr);
						$(dom).after(trDom);
						
						var $trDom = $(dom).next();
						$(dom).remove();
						
						// 监听行事件
						$trDom.find("td").each(function(i, e) {
							customForm.sortableTd(e);
						});
						
						return customForm.saveRowInfo(table_right_main, cols);
						
					} else {
						assemblys.msg("删除成功");
						$(dom).remove();
					}
				}
			}).then(function() {
				customForm.getDeletedCustomField();
			});
		};
		var fields = $(dom).find("[customFieldCode]");
		if (fields.length > 0) {
			var msg = "当前行存在表单属性,确定删除该行吗?(表单属性也将删除)";
			if (cols) {
				msg = "当前行存在表单属性,确定更改当前行的列数吗?(表单属性将删除)";
			}
			assemblys.confirm(msg, deleteCustomFieldRow);
		} else {
			deleteCustomFieldRow();
		}
		
	},
	editCustomModular : function(dom) {
		
		customForm.dom = dom;
		var customModularCode = $(dom).attr("customModularCode");
		
		var url = basePath + "frame/customForm/editCustomModular.html?customModularCode=" + customModularCode + "&customFormCode=" + customFormCode;
		url += "&appCode=" + appCode + "&compNo=" + compNo;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "layer-editCustomModular",
			title : '编辑分类',
			scrollbar : false,
			area : [ '500px', '400px' ],
			content : url,
			end : function() {
				// 如果没添加成功，就需要在页面移除
				customForm.cancelEditTableName(dom);
				// 更新顺序
				customForm.updateTableSeqNo();
			}
		});
	},
	deleteTable : function(dom) {
		var customModularCode = $(dom).parent().attr("customModularCode");
		
		/**
		 * 這裡刪除被版塊刪除和屬性刪除監聽
		 */
		var title = $(dom).children(".tableName") ? $(dom).children(".tableName").text() : $(dom).text();
		
		if (customModularCode != "0") {
			
			assemblys.confirm("确认要删除「" + title + "」模块吗?", function() {
				var url = basePath + "frame/newCustomForm/deleteCustomModular.spring";
				$.ajax({
					type : "post",
					url : url,
					dataType : "json",
					data : {
						"customModularCode" : customModularCode,
						"customFormCode" : customFormCode,
						"appCode" : appCode
					},
					success : function(data) {
						assemblys.msg("删除成功");
						if (data.isCommon == 1) {
							$("#systemClassifications div[customModularCode=" + customModularCode + "]").removeClass("layui-hide");
						} else {
							customForm.getDeletedCustomModular();
							$("#systemComponents div").each(function(i, element) {
								if ($(dom).parent().find("label[customFieldCode=" + this.getAttribute("customFieldCode") + "]").length > 0) {
									$(this).removeClass("layui-hide");
								}
							});
						}
						$(dom).parent().remove();
					}
				});
			});
		} else {
			$(dom).parent().remove();
		}
	},
	updateTableSeqNo : function() {
		var customModularCodeAry = new Array();
		$("div[customModularCode]").each(function(i, element) {
			customModularCodeAry.push($(this).attr("customModularCode"));
		});
		$.ajax({
			url : basePath + "frame/newCustomForm/updateCustomModularSeqNo.spring",
			dataType : "json",
			type : "post",
			data : {
				"customModularCode" : customModularCodeAry,
				"appCode" : appCode,
				"customFormCode" : customFormCode
			}
		});
	},
	updateTableTrSeqNo : function(table_right_main) {
		var customFieldRowCodeAry = new Array();
		$(table_right_main).find("tr[customFieldRowCode]").each(function(i, element) {
			customFieldRowCodeAry.push($(this).attr("customFieldRowCode"));
		});
		$.ajax({
			url : basePath + "frame/newCustomForm/updateCustomFieldRowSeqNo.spring",
			dataType : "json",
			type : "post",
			data : {
				"customFieldRowCode" : customFieldRowCodeAry,
				"appCode" : appCode,
				"customFormCode" : customFormCode
			}
		});
	},
	saveTable : function(dom) {
		
		var $table_right = $(dom).parents(".table_right");
		var $customModularNameDom = $(dom).prev().children(":eq(0)");
		var customModularName = $.trim($customModularNameDom.val());
		if (customModularName.length == 0) {
			assemblys.msg("请填写 “模块名称”");
			$customModularNameDom.focus();
			return false;
		}
		
		if (customModularName.length > 50) {
			assemblys.msg("“模块名称”长度不能超过50");
			$customModularNameDom.focus();
			return false;
		}
		
		var seqNo = $table_right.index();
		
		var customModular = $table_right[0].customModular || {};
		customModular.seqNo = seqNo;
		customModular.customModularName = customModularName;
		customModular.customFormCode = customFormCode;
		customModular.appCode = appCode;
		
		var url = basePath + "frame/customForm/saveCustomModular.spring";
		$.ajax({
			type : "post",
			url : url,
			data : customModular,
			dataType : "json",
			success : function(data) {
				assemblys.msg("保存成功");
				$(dom).parents(".table_right_title").children("span.tableName").text(customModularName);
				if (!customModular.customModularCode) {
					obj.updateTableSeqNo();
					$table_right.attr("customModularCode", data.customModularCode);
					$table_right.attr("customModularID", data.customModularID);
					var table_right_main = $table_right.find("table.table_right_main")[0];
					var customFieldRow = {
						cols : 1,
						customFieldRowCode : "",
						customFieldRowID : 0
					}
					obj.addColFun(table_right_main, customFieldRow);
				}
				customModular.status = 1;
				customModular.customModularCode = data.customModularCode
				customModular.customModularID = data.customModularID;
			},
			error : function(a, b, c) {
				assemblys.alert("保存模块出错!错误信息:" + c);
			}
		});
	},
	cancelEditTableName : function(dom) {
		var $table_right = $(dom);
		var customModularCode = $table_right.attr("customModularCode");
		if (!customModularCode) {
			$table_right.remove();
		}
	},
	addTable : function(e, element) {
		
		// 如果是盒子
		var $table_box = $(element).hasClass("table_box") ? $(element) : $(element).parents("div.table_box");
		
		// 新添加出来的
		var $table_right;
		
		// 如果是分类
		var $current_table_right = $(element).hasClass("table_right") ? $(element) : $(element).parents(".table_right");
		if ($current_table_right.length > 0) {
			var tableHtml = assemblys.createElement(initCustomForm.createTable({
				customModularCodeTemp : "table_tempCode",
				customModularName : ""
			}));
			// 当前后面追加
			$current_table_right.after(tableHtml);
			$table_right = $table_box.children("div[customModularCodeTemp='table_tempCode']");
		} else {
			assemblys.createElement(initCustomForm.createTable({
				customModularCodeTemp : "table_tempCode",
				customModularName : ""
			}), $table_box[0]);
			$table_right = $table_box.children("div:last");
		}
		
		if ($table_right.length > 0) {
			
			$table_box.animate({
				scrollTop : $table_box.scrollTop() + $table_right.offset().top - 74
			}, 0);
			
			customForm.sortableTr($table_right.find("table.table_right_main")[0]);
			
			customForm.editCustomModular($table_right[0]);
		}
	},
	createRow : function(table_right_main, cols, table_right_main_tr) {
		
		// 如果存在行
		if (table_right_main_tr && table_right_main_tr.length > 0) {
			// 行后面添加
			var trHtml = assemblys.createElement(initCustomForm.createTr(null, {
				"cols" : cols,
				"customFieldRowCode" : "tr_tempCode"
			}, 0));
			$(table_right_main_tr).after(trHtml);
		} else {
			// 父类后面添加
			assemblys.createElement(initCustomForm.createTr(null, {
				"cols" : cols,
				"customFieldRowCode" : "tr_tempCode"
			}, 0), table_right_main);
		}
		
		// 新加出来的
		var $newTr = $("tr[customFieldRowCode='tr_tempCode']");
		// 拖动效果
		$newTr.find("td").each(function(i, e) {
			customForm.sortableTd(e);
		});
		// 保存数据
		customForm.saveRowInfo(table_right_main, cols).fail(function() {
			$newTr.remove();
		});
		
	},
	addRow : function(e, element) {
		var $table_right_main = $(element).hasClass("table_right_main") ? $(element) : $(element).parents("div.table_right").children("table.table_right_main");
		if ($table_right_main.length > 0) {
			customForm.createRow($table_right_main[0], parseInt(e.getAttribute("cols")), $(element).parents("tr[customFieldRowCode]"));
		}
	},
	addCustomField : function(e, element) {
		if (element.tagName == "TD" && $(element).children().not("i.row_icon").length == 0) {
			var customField = {
				"customFieldName" : e.innerText,
				"customFieldCode" : "",
				"customFieldSet" : e.getAttribute("customFieldSet")
			};
			assemblys.createElement(initCustomForm.getFieldSet(null, null, customField, 0), element);
			
			customForm.sortableSwapTd($(element).find(".moveInput"));
			
			customForm.editCustomField($(element).find("label")[0]);
		}
	},
	addCommonCustomField : function(e, element) {
		if (element.tagName == "TD" && $(element).children().not("i.row_icon").length == 0) {
			customForm.saveCommonCustomField(element, e.customField).then(function() {
				assemblys.createElement(initCustomForm.getFieldSet(null, null, e.customField, 0), element);
				customForm.sortableSwapTd($(element).find(".moveInput"));
				$(e).addClass("layui-hide");
				$(e).siblings().css("margin", "6px 0px;");
				
				customForm.getDeletedCustomModular();
			});
		}
	},
	saveCommonCustomField : function(dom, customField) {
		return $.ajax({
			url : basePath + "frame/newCustomForm/saveCommonCustomField.spring",
			dataType : "json",
			type : "post",
			data : {
				"customModularCode" : $(dom).parents(".table_right").attr("customModularCode"),
				"customFieldCode" : customField.customFieldCode,
				"customFormCode" : customFormCode,
				"seqNo" : $(dom).index(),
				"customFieldRowCode" : $(dom).parent().attr("customFieldRowCode"),
				"appCode" : appCode
			}
		});
	},
	addCommonCustomModular : function(e, element) {
		
		var $table_box = $(element).hasClass("table_box") ? $(element) : $(element).parents("div.table_box");
		if ($table_box.length > 0) {
			
			// 增加关联分类
			customForm.saveCommonCustomModular($table_box, e.customModular).then(function() {
				
				// 新添加出来的
				var $table_right;
				
				// 如果定位当前
				var $current_table_right = $(element).hasClass("table_right") ? $(element) : $(element).parents(".table_right");
				if ($current_table_right.length > 0) {
					var tableHtml = assemblys.createElement(initCustomForm.createTable(e.customModular));
					$current_table_right.after(tableHtml);
					$table_right = $table_box.children("div[customModularCode='" + e.customModular.customModularCode + "']");
				} else {
					assemblys.createElement(initCustomForm.createTable(e.customModular), $table_box[0]);
					$table_right = $table_box.children("div:last");
				}
				
				// 定位
				$table_box.animate({
					scrollTop : $table_box.scrollTop() + $table_right.offset().top - 74
				}, 0);
				
				$(e).addClass("layui-hide");
				$(e).siblings().css("margin", "6px 0px;");
				
				// 隐藏组件库
				$("#systemComponents div").each(function(i, element) {
					if ($table_right.find("label[customFieldCode=" + this.getAttribute("customFieldCode") + "]").length > 0) {
						$(this).addClass("layui-hide");
					}
				});
				
				// 更新顺序
				customForm.updateTableSeqNo();
				
			});
			
		}
	},
	saveCommonCustomModular : function($table_box, customModular) {
		return $.ajax({
			url : basePath + "frame/newCustomForm/saveCommonCustomModular.spring",
			dataType : "json",
			type : "post",
			data : {
				"customModularCode" : customModular.customModularCode,
				"customFormCode" : customFormCode,
				"seqNo" : $table_box.children().length,
				"appCode" : appCode
			}
		});
	},
	editCustomField : function(dom) {
		customForm.label = dom;
		var customFieldCode = $(dom).attr("customFieldCode");
		var customFieldSet = $(dom).attr("customFieldSet");
		var customModularCode = $(dom).parents(".table_right").attr("customModularCode");
		var $tr = $(dom).parents("tr");
		var customFieldRowCode = $tr.attr("customFieldRowCode");
		var seqNo = $(dom).parents("td").index();
		var url = basePath + "frame/customForm/editCustomField.jsp?customFieldCode=" + customFieldCode + "&customModularCode=" + customModularCode + "&customFormCode=" + customFormCode + "&customFieldSet=" + customFieldSet;
		url += "&appCode=" + appCode + "&compNo=" + compNo + "&seqNo=" + seqNo + "&customFieldRowCode=" + customFieldRowCode;
		url += "&customFormTypeCode=" + customFormTypeCode + "&customFormClass=" + customFormClass;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "layer-editField",
			title : '编辑组件',
			scrollbar : false,
			area : [ '900px', '500px' ],
			content : url,
			end : function() {
				if (!$(dom).attr("customFieldCode")) {
					$(dom).parents("form.moveInput").remove();
					var customFieldSet = $(dom).attr("customFieldSet");
					$("#customFieldSetList [customFieldSet='" + customFieldSet + "']").show();
				}
				customForm.getOptionRelation();
			}
		});
	},
	saveRowInfo : function(table_right_main, cols) {
		var $table_right_main = $(table_right_main);
		var $table_right = $table_right_main.parent();
		var customModularCode = $table_right.attr("customModularCode");
		return $.ajax({
			url : basePath + "frame/newCustomForm/saveCustomFieldRow.spring",
			dataType : "json",
			type : "post",
			data : {
				"customModularCode" : customModularCode,
				"cols" : cols,
				"customFormCode" : customFormCode,
				"appCode" : appCode
			},
			success : function(data) {
				// 回填新加的行
				$table_right_main.children("tr[customFieldRowCode='tr_tempCode']").attr("customFieldRowCode", data.customFieldRow.customFieldRowCode)[0].customFieldRow = data.customFieldRow;
				// 更新排序
				customForm.updateTableTrSeqNo(table_right_main);
			}
		});
	},
	deleteCustomField : function(dom) {
		var prompt = '确定删除「' + dom.children[0].innerText + '」吗?</br>同时删除业务编号&nbsp;&nbsp;&nbsp;<input type="checkbox" checked="true" name="delBusinessCode" >';
		var options = {
				btn : [ '确定', '取消' ],
				title : '提示',
				btnAlign : 'c',
				closeBtn : 0
			};
		layer.confirm(prompt,options, function() {
			delBasenicode();
			return false;
			
		});
		function delBasenicode(){
			var delBusinessCode = 0;
			if($("input[name='delBusinessCode']").prop('checked')){
				delBusinessCode = 1;
			}
			var customFieldCode = $(dom).attr("customFieldCode");
			$.ajax({
				url : basePath + "frame/newCustomForm/deleteCustomField.spring",
				dataType : "json",
				data : {
					"customFormCode" : customFormCode,
					"customFieldCode" : customFieldCode,
					"appCode" : appCode,
					"delBusinessCode" : delBusinessCode,
					"customModularCode" : $(dom).parents("div.table_right").attr("customModularCode")
				},
				success : function(data) {
					assemblys.msg("删除成功");
					$(dom).closest("form").remove();
					if (data.isCommon == 1) {
						$("#systemComponents div[customFieldCode='" + customFieldCode + "']").removeClass("layui-hide");
					} else {
						customForm.getDeletedCustomField();
					}
					layer.closeAll();
				}
			});
		}
	},
	beforeMoveTable : function() {
		$(".table_right_main").hide();
	},
	moveTable : function() {
		$(".table_right_main").show();
		customForm.updateTableSeqNo();
	},
	moveRow : function(evt) {
		
		var fromElem = evt.from;
		var toElem = evt.to;
		var fromCustomModularCode = $(fromElem).parents("div[custommodularcode]").attr("custommodularcode");
		var fromCustomFieldRowCodes = [];
		$(fromElem).find("tr[customfieldrowcode]").each(function(i, e) {
			fromCustomFieldRowCodes.push(e.getAttribute("customfieldrowcode"));
		});
		
		var toCustomModularCode = "";
		var toCustomFieldRowCodes = [];
		if (fromElem != toElem) {
			toCustomModularCode = $(toElem).parents("div[custommodularcode]").attr("custommodularcode");
			$(toElem).find("tr[customfieldrowcode]").each(function(i, e) {
				toCustomFieldRowCodes.push(e.getAttribute("customfieldrowcode"));
			});
		}
		
		$.ajax({
			url : basePath + "frame/newCustomForm/updateRowSeqNo.spring",
			dataType : "json",
			type : "post",
			traditional : true,
			data : {
				"fromCustomModularCode" : fromCustomModularCode,
				"fromCustomFieldRowCodes" : fromCustomFieldRowCodes,
				"toCustomModularCode" : toCustomModularCode,
				"toCustomFieldRowCodes" : toCustomFieldRowCodes,
				"appCode" : appCode
			},
		});
		
	},
	beforeMoveField : function(e, clone) {
		layui.stope(e);
	},
	exchangeField : function(evt) {
		if (evt.from === evt.to) {
			return;
		}
		var $form = $(evt.from);
		var $to = $(evt.to);
		var fromCustomFieldRowCode = $form.parents("tr[customFieldRowCode]").attr("customFieldRowCode");
		var toCustomFieldRowCode = $to.parents("tr[customFieldRowCode]").attr("customFieldRowCode")
		var fromCustomModularCode = $form.parents("div[custommodularcode]").attr("custommodularcode");
		var toCustomModularCode = $to.parents("div[custommodularcode]").attr("custommodularcode");
		var fromCustomFieldCode = $form.find("label[customfieldcode]").attr("customfieldcode");
		var toCustomFieldCode = $to.find("label[customfieldcode]").attr("customfieldcode");
		var fromSeq = $form.parents("td[colspan]").index();
		var toSeq = $to.parents("td[colspan]").index();
		fromSeq = fromSeq >= 0 ? fromSeq : $form.index();
		toSeq = toSeq >= 0 ? toSeq : $to.index();
		return $.ajax({
			url : basePath + "frame/newCustomForm/updateCustomFieldSeqNo.spring",
			dataType : "json",
			type : "post",
			data : {
				"fromCustomModularCode" : fromCustomModularCode,
				"fromCustomFieldRowCode" : fromCustomFieldRowCode,
				"fromCustomFieldCode" : fromCustomFieldCode,
				"fromSeq" : fromSeq,
				"toCustomModularCode" : toCustomModularCode,
				"toCustomFieldRowCode" : toCustomFieldRowCode,
				"toCustomFieldCode" : toCustomFieldCode,
				"toSeq" : toSeq,
				"appCode" : appCode,
				"customFormCode" : customFormCode
			}
		});
		
	},
	downloadTemplate : function() {
		// 判断如果是Ipad则不支持导出功能功能
		var ua = navigator.userAgent.toLowerCase();
		var s = ua.match(/iPad/i);
		if (s == "ipad") {
			assemblys.msg("Ipad不支持该处导出功能，请更换设备导出");
			return false;
		}
		var url = basePath + "frame/common/downloadTemplate.spring?fileName=" + encodeURIComponent("导入表单模版.xls");
		location.href = url;
	},
	exportCustomFormInfo : function() {
		var uploadInst = layui.upload.render({
			elem : '#selectExportFile',
			url : basePath + "frame/newCustomForm/exportCustomFormInfo.spring",
			accept : 'file',
			exts : 'xls',
			data : {
				"customFormCode" : param.get("customFormCode"),
				"appCode" : param.get("appCode"),
				"compNo" : param.get("compNo")
			},
			done : function(res) {
				//上传完毕回调
				assemblys.msg("导入成功", function() {
					location.reload();
				});
			}
		});
	},
	sortableSwapTd : function($formInput) {
		Sortable.create($formInput[0], {
			group : 'moveInput', // set both lists to same group
			animation : 150,
			handle : '.item_label', // handle's class
			filter : ".field-has-relation",
			ghostClass : 'sortable-background',
			swap : true, // Enable swap plugin
			swapClass : 'high-light', // The class applied to the hovered swap item
			onMove : function(evt) {
				var fromCustomFieldRowCode = $(evt.from).parent().attr("customFieldRowCode");
				var toCustomFieldRowCode = $(evt.to).parent().attr("customFieldRowCode")
				var elementHasRelation = !!customForm.relationCodeMap[fromCustomFieldRowCode];
				var moveElementHasRelation = !!customForm.relationCodeMap[toCustomFieldRowCode];
				var fromCustomModularCode = $(evt.from).parents("div[custommodularcode]").attr("custommodularcode");
				var toCustomModularCode = $(evt.to).parents("div[custommodularcode]").attr("custommodularcode");
				if (fromCustomModularCode != toCustomModularCode && (elementHasRelation || moveElementHasRelation)) {
					assemblys.msg("组件被关联不能移动位置");
					return false;
				}
				
				var fromCustomModular = $(evt.from).parents("div.table_right")[0].customModular;
				var toCustomModular = $(evt.to).parents("div.table_right")[0].customModular;
				var fromCustomField = evt.from.customField;
				var toCustomField = evt.to.customField;
				if ((fromCustomModular.isCommon == 1 && toCustomField.isCommon == 1) || (toCustomModular.isCommon == 1 && fromCustomField.isCommon == 1)) {
					assemblys.msg("公用分类不能有公用组件");
					return false;
				}
			},
			onEnd : customForm.exchangeField,
		});
	},
	sortableTd : function(e) {
		
		var $formInput = $(e).find("form.moveInput");
		if ($formInput.length > 0) {
			customForm.sortableSwapTd($formInput);
		}
		
		Sortable.create(e, {
			group : 'moveDiv', // set both lists to same group
			animation : 150,
			handle : '.move-div-div', // handle's class
			filter : ".field-has-relation",
			ghostClass : 'sortable-background',
			onMove : function(evt) {
				var fromCustomFieldRowCode = $(evt.from).parent().attr("customFieldRowCode");
				var toCustomFieldRowCode = $(evt.to).parent().attr("customFieldRowCode")
				var elementHasRelation = !!customForm.relationCodeMap[fromCustomFieldRowCode];
				var moveElementHasRelation = !!customForm.relationCodeMap[toCustomFieldRowCode];
				var fromCustomModularCode = $(evt.from).parents("div[custommodularcode]").attr("custommodularcode");
				var toCustomModularCode = $(evt.to).parents("div[custommodularcode]").attr("custommodularcode");
				if (fromCustomModularCode != toCustomModularCode && (elementHasRelation || moveElementHasRelation)) {
					assemblys.msg("组件被关联不能移动位置");
					return false;
				}
				
				if ($(evt.to).find("form.moveInput").length > 0) {
					return false;
				}
				
			},
			onEnd : customForm.exchangeField,
		});
		
	},
	sortableTr : function(e) {
		Sortable.create(e, {
			group : 'col', // set both lists to same group
			animation : 150,
			filter : ".row-has-relation",
			ghostClass : 'sortable-background',
			onEnd : customForm.moveRow,
		});
	},
	events : function() {
		// 拖动事件
		moveUtil.onmove("div.left_item.table", customForm.addTable);// 新分类
		moveUtil.onmove("div.left_item.column", customForm.addRow);// 新行
		moveUtil.onmove("#customFieldSetList div", customForm.addCustomField);// 基础组件
		moveUtil.onmove("#systemComponents div", customForm.addCommonCustomField);// 公用组件
		moveUtil.onmove("#deletedCustomFields div", customForm.addCommonCustomField);// 已删除组件
		moveUtil.onmove("#systemClassifications div", customForm.addCommonCustomModular);// 公用分类
		moveUtil.onmove("#deletedClassifications div", customForm.addCommonCustomModular);// 已删除分类
//		moveUtil.onmove("label[customFieldCode]", customForm.exchangeField, customForm.beforeMoveField);// 移动组件
		
		var sortable = Sortable.create($(".table_right_all")[0], {
			animation : 150,
			handle : 'div.table_right_title', // handle's class
			ghostClass : 'sortable-background',
			onStart : customForm.beforeMoveTable,
			onEnd : customForm.moveTable,
		});
		
		$("table.table_right_main").each(function(i, e) {
			customForm.sortableTr(e);
		});
		
		$("tr[customfieldrowcode] td").each(function(i, e) {
			customForm.sortableTd(e);
		});
		
		// 左键点击分类标题隐藏/显示分类内容
		$(document).on("click", "div.table_right_title", function(e) {
			var $table_right_main = $(this).next();
			if ($table_right_main.hasClass("layui-hide")) {
				$table_right_main.removeClass("layui-hide");
				$(this).children("i.fr").html("&#xe920;");
			} else {
				$table_right_main.addClass("layui-hide");
				$(this).children("i.fr").html("&#xe922;");
			}
		});
		
		// 点击其他位置隐藏工具栏
		$(document).on("click", function(e) {
			var e = window.event;
			var target = e.target || e.srcElement;
			if (target != initCustomForm.trUtil && $(initCustomForm.rowUL).is(":visible")) {
				$(initCustomForm.rowUL).addClass("layui-hide")
			}
			
			if (target != initCustomForm.titleUL && $(initCustomForm.titleUL).is(":visible")) {
				$(initCustomForm.titleUL).addClass("layui-hide")
			}
		});
		
		// 行工具栏点击事件
		$(document).on("click", "i.row_icon", function(e) {
			var tr = $(this).parents("tr[customFieldRowCode]")[0];
			
			// 控制是否合并
			var rowUL = initCustomForm.rowUL;
			if (tr.customFieldRow.mergeWithNextRow != 1) {
				$(rowUL).find(".merge i").addClass("layui-hide");
			} else {
				$(rowUL).find(".merge i").removeClass("layui-hide");
			}
			
			$(rowUL).removeClass("layui-hide").css({
				'position' : 'fixed',
				'top' : e.pageY + 6 + (document.documentElement.clientHeight - e.pageY - $('.col_set').height() < 0 ? -$('.col_set').height() - 16 : 0),
				'left' : e.pageX - 85,
				'z-index' : '100000'
			}).off("click").on("click", "li", function() {
				if (this.className.indexOf("col_delete") != -1) {
					customForm.deleteRow(tr);
				} else if (this.className.indexOf("merge") != -1) {
					customForm.merge(tr);
				} else {
					var colValue = this.className.split("_")[1];
					colValue = $.trim(colValue.replace("setCustomFormClass", ""));
					customForm.deleteRow(tr, colValue);
				}
			}).children().removeClass("layui-hide").filter(".col_" + $(tr).children("td").length).addClass("layui-hide");
		});
		
		// 分类标题右键菜单
		$(document).on("contextmenu", "div.table_right_title", function(e) {
			var that = this;
			$(initCustomForm.titleUL).removeClass("layui-hide").css({
				'position' : 'fixed',
				'display' : 'block',
				'z-index' : '100',
				'top' : e.pageY - 10,
				'left' : e.pageX - 5
			}).off("click").on("click", "li", function() {
				if (this.className.indexOf("input_set_edit") != -1) {
					customForm.editCustomModular($(that).parent()[0]);
				} else if (this.className.indexOf("input_set_delete") != -1) {
					customForm.deleteTable(that);
				}
			});
			return false;
		});
		
		// 组件右键菜单
		$(document).on("contextmenu", "form.moveInput", function(e) {
			var that = this;
			$(initCustomForm.titleUL).removeClass("layui-hide").css({
				'position' : 'fixed',
				'display' : 'block',
				'z-index' : '100',
				'top' : e.pageY - 10,
				'left' : e.pageX - 5
			}).off("click").on("click", "li", function() {
				if (this.className.indexOf("input_set_edit") != -1) {
					customForm.editCustomField($(that).find("label")[0]);
				} else if (this.className.indexOf("input_set_delete") != -1) {
					customForm.deleteCustomField($(that).find("label")[0]);
				}
			});
			
			// 控件右键菜单不需要隐藏
			$(initCustomForm.titleUL).find(".setCustomFormClass").removeClass("setCustomFormClass");
			
			return false;
		});
		
		customForm.exportCustomFormInfo();
	}
}