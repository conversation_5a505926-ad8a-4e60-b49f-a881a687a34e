var customFormApprovalTemplate = {
	defaultApproval: false,
	init: function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("[menuIcon]")[0]).then(function() {
			return customFormApprovalTemplate.defaultApprovalInit();
		}).then(function(data) {
			return customFormApprovalTemplate.getCustomFormData(data);
		}).then(function() {

			if (customFormApprovalTemplate.customForm.hasFileUpload == 1) {
				$("#table_box_upload").removeClass("layui-hide");
			}

			window.compNo = param.get("compNo");
			customFormApprovalTemplate.initLayui();
		});

	},
	initLayui: function() {
		layui.form.render();
		layui.form.verify(customFormApprovalTemplate.formVerify);

		layui.form.on("submit(save)", function(data) {
			param.set("approvalMethod", "");
			customFormApprovalTemplate.getNextApprovalBelongFlowNode();
			return false;
		});

		// 监听结束循环
		layui.form.on("submit(saveLoop)", function(data) {
			customFormApprovalTemplate.getLoopEndNextNode();
			return false;
		});

		// 监听结束会签
		layui.form.on("submit(saveCountersign)", function(data) {
			param.set("approvalMethod", "");
			customFormApprovalTemplate.getNextApprovalBelongFlowNode(1);
			return false;
		});

		// 监听一键结束
		layui.form.on("submit(saveFinish)", function(data) {
			assemblys.confirm("确定一键结束审批吗？", function() {
				customFormApprovalTemplate.saveCustomFormFilled(3);
			});
			return false;
		});

		// 监听编辑
		layui.form.on("submit(edit)", function(data) {
			customFormApprovalTemplate.saveCustomFormFilled(4);
			return false;
		});

		// 监听单选radio一级
		layui.form.on("radio(oneLevelRadio)", function(data) {
			var $oneLeveDiv = $(this).closest("div").next("div");
			if (data.elem.checked) {
				$oneLeveDiv.removeClass("layui-hide");
			} else {
				$oneLeveDiv.addClass("layui-hide");
				$(this).next().next().val("");
				$oneLeveDiv.find(":radio").prop("checked", false);
				$oneLeveDiv.find("input[type='text']").val("");
			}
			//$(this).closest("div[checkboxgroup]").siblings().find("div[divlevel='2']").each(function() {})
			$(this).closest("div[checkboxgroup]").siblings().find("div[divlevel='2']").addClass("layui-hide");
			$(this).closest("div[checkboxgroup]").siblings().find("div[divlevel='2']").find(":radio").prop("checked", false);
			$(this).closest("div[checkboxgroup]").siblings().find("div[divlevel='2']").find("input[type='text']").val("");
			layui.form.render();
			return false;
		});

		//监听单选radio二级多选
		layui.form.on("radio(twoLevelRadio)", function(data) {
			if (!data.elem.checked) {
				$(this).next().next("input[type='text']").val("");
			}
			$(this).closest("div").find("input[type='text']").val("");
			layui.form.render();
		});

		// 监听多选checkbox一级
		layui.form.on("checkbox(oneLevelCheckbox)", function(data) {
			var $oneLeveDiv = $(this).closest("div").next("div");
			if (data.elem.checked) {
				$oneLeveDiv.removeClass("layui-hide");
			} else {
				$oneLeveDiv.addClass("layui-hide");
				$(this).next().next().val("");
				$oneLeveDiv.find(":checkbox").prop("checked", false);
				$oneLeveDiv.find("input[type='text']").val("");
			}
			layui.form.render();
			return false;
		});

		//监听多选checkbox多选
		layui.form.on("checkbox(twoLevelCheckbox)", function(data) {
			if (!data.elem.checked) {
				$(this).next().next("input[type='text']").val("");
			}
			layui.form.render();
		});

	},
	getCustomFormFilled: function(customFormFilledCode) {
		return $.ajax({
			url: basePath + "frame/newCustomForm/getCustomFormFilled.spring",
			data: {
				"customFormFilledCode": customFormFilledCode,
				"appCode": param.get("appCode")
			},
			dataType: "json",
			success: function(data) {
				param.set(null, data.customFormFilled);
			}
		});
	},
	defaultApprovalInit: function() {
		var approvalBelongFlowNodeCode = param.get("approvalBelongFlowNodeCode");
		if (approvalBelongFlowNodeCode) {
			return $.ajax({
				url: basePath + "frame/approvalFlowRecord/defaultApprovalInit.spring",
				dataType: "json",
				data: {
					approvalBelongCode: param.get("approvalBelongCode"),
					funCode: param.get("funCode"),
					appCode: param.get("appCode"),
					approvalBelongFlowNodeCode: approvalBelongFlowNodeCode,
					approvalBelongFlowNodeRecordDraftCode: param.get("approvalBelongFlowNodeRecordDraftCode"),
				},
				success: function(data) {
					if (param.get("submitType") == 1) {
						$("[btn=saveBtn]").removeClass("layui-hide");

						if (param.get("inLoop") == 1) {
							$("[btn=inLoop]").removeClass("layui-hide");
						}
						if (param.get("approvalBelongFlowNodeRecordDraftCode")) {
							$("input[value='保存草稿']").val("保存").attr("onclick", "").attr("lay-submit", "").attr("lay-filter", "edit").siblings("input").not("input[value='关闭']").remove();
							$("#stateStatusNo,#copy").hide();// 避免与其他地方冲突,不使用addClass("layui-hide")
						} else {
							if (data.finishExecRight) {
								$("[btn=finish]").removeClass("layui-hide");
							}
						}
					}

					if (parent.approvalFlow.hasStateFlow && data.approvalBelongFlowNode.state != 2 && param.get("approvalType") == 0) {// 协助和回退审批不显示按钮
						$("#stateStatusNo").removeClass("layui-hide");
					}

					if (data.approvalBelongFlowNode.approvalFlowNodeType == 3) {// 协助不显示按钮
						$("[btn=countersign]").removeClass("layui-hide");
					}

					if (param.get("inLoop") == 1 && data.approvalBelongFlowNode.state != 2) {// 协助不显示按钮
						$("[btn=inLoop]").removeClass("layui-hide");
					}

					customFormApprovalTemplate.initStatus(data);
					if (data.approvalBelongFlowNode.state != 2 && data.nextApprovalBelongFlowNode) {
						for (var i = 0; i < data.customFormTypeStateList.length; i++) {
							if (data.customFormTypeStateList[i].customFormTypeStateNo == data.nextApprovalBelongFlowNode.approvalNodeState) {
								param.set("stateStatusNo", data.nextApprovalBelongFlowNode.approvalNodeState || -999);
								break;
							}
						}
					}
					
					if (data.approvalBelongFlowNodeRecord) {
						param.set("copyUserNames", data.approvalBelongFlowNodeRecord.copyUserNames);
						param.set("copyUserCodes", data.approvalBelongFlowNodeRecord.copyUserCodes);
						if (data.isDefault) {
							param.set("approvalBelongFlowNodeRecordDraftCode", data.approvalBelongFlowNodeRecord.approvalBelongFlowNodeRecordCode);
						}
					}

					if (data.attachments) {
						customFormApprovalTemplate.attaCallback(data.attachments);
					}

					// 加载富文本
					customFormTemplate.loadRichText();

				}
			});
		} else {
			// 加载富文本
			customFormTemplate.loadRichText();
		}
	},
	initStatus: function(data) {
		var $select = $("select[name=stateStatusNo]");
		var html = "";
		for (var i = 0; i < data.customFormTypeStateList.length; i++) {
			html += '<option value="' + data.customFormTypeStateList[i].customFormTypeStateNo + '">' + data.customFormTypeStateList[i].customFormTypeStateName + '</option>';
		}
		$select.append(html);
		layui.form.render("select", "stateStatusNoDiv");
	},
	saveCustomFormFilled: function(type, showMsg) {
		if (type == 2) {
			param.set("saveState", 0);
		} else if (type == 4) {// 编辑
			param.set("saveState", 2);
		}

		if (isSubmit) {
			return;
		}
		isSubmit = true;
		return $.ajax({
			url: basePath + "frame/newCustomForm/saveCustomFormFilled.spring",
			dataType: "json",
			type: "post",
			data: param.__form() + "&type=" + (type || 0),
			success: function(data) {
				if (!showMsg) {
					assemblys.msg(type != 2 ? '提交成功' : "保存成功", function() {
						if (type != 2) {
							parent.location.reload();
							assemblys.closeWindow();
						}
					});
				}
				//审批表单保存后回调
				if (parent.saveApprovalCallback) {
					$.each(parent.saveApprovalCallback, function(name, value) {
						if (typeof value == "function") {
							data.saveState = param.get("saveState");
							// 父窗口、当前表单窗口、保存表单后的Data
							value.call(parent.window, window, data);
						}
					});
				}
				param.set("approvalBelongFlowNodeRecordDraftCode", data.approvalBelongFlowNodeRecordDraftCode);
				param.set("saveState", 1);
			}
		});

	},
	getNextApprovalBelongFlowNode: function(type) {
		//customFormApprovalTemplate.saveCustomFormFilled(2, true).then(function() {
		$.ajax({
			url: basePath + "frame/approvalFlow/getNextApprovalBelongFlowNode.spring",
			data: {
				approvalBelongCode: param.get("approvalBelongCode"),
				appCode: param.get("appCode"),
			},
			dataType: "json",
			success: function(data) {
				var $input = $("input[name=approvalFlowNodeData]");
				if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
					defaultApproval.toSelectApprovalUser(function(data) {
						var values = [];
						for (var i = 0; i < data.length; i++) {
							values.push(data[i].value);
						}

						var approvalUIDs = values.join(",");
						var approvalFlowNodeData = {
							approvalRight: 3,
							approvalUIDs: approvalUIDs
						}

						var oldValue = $input.val();
						$input.val(JSON.stringify(approvalFlowNodeData));
						customFormApprovalTemplate.saveCustomFormFilled(type);
					});
				} else {
					$input.val("{}");
					customFormApprovalTemplate.saveCustomFormFilled(type);
				}
			}
		});
		//});
	},
	inLoopOnClick: function(obj) {
		var layVerify = $("input[name=approvalFlowNodeData]").prev().attr("lay-verify");
		$("input[name=approvalFlowNodeData]").prev().removeAttr("lay-verify");
		$(obj).next().click();
		$("input[name=approvalFlowNodeData]").prev().attr("lay-verify", layVerify);
	},
	getLoopEndNextNode: function() {
		assemblys.confirm("确定结束循环吗？", function() {

			$.ajax({
				url: basePath + "frame/approvalFlowRecord/getLoopEndNextNode.spring",
				data: {
					approvalBelongCode: param.get("approvalBelongCode"),
					funCode: param.get("funCode"),
					appCode: param.get("appCode"),
				},
				dataType: "json",
				success: function(data) {
					var $input = $("input[name=approvalFlowNodeData]");
					if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
						defaultApproval.toSelectApprovalUser(function(data) {
							var values = [];
							for (var i = 0; i < data.length; i++) {
								values.push(data[i].value);
							}

							var approvalUIDs = values.join(",");
							var approvalFlowNodeData = {
								approvalRight: 3,
								approvalUIDs: approvalUIDs
							}

							var oldValue = $input.val();
							$input.val(JSON.stringify(approvalFlowNodeData));
							param.set("approvalMethod", "loop");// 结束循环
							customFormApprovalTemplate.saveCustomFormFilled();
						});
					} else {
						param.set("approvalMethod", "loop");// 结束循环
						if (!$input.val()) {
							$input.val("{}");
						}
						customFormApprovalTemplate.saveCustomFormFilled();
					}
				}
			});
		})
	},

	getCustomFormData: function(approvalData) {
		var customFormFilledCode = "";
		if (approvalData) {
			if (approvalData.data.approvalBelongFlowNodeRecord) {
				customFormApprovalTemplate.defaultApproval = true;
				customFormFilledCode = approvalData.data.approvalBelongFlowNodeRecord.approvalFormBelongCode;
				param.set("customFormFilledCode", customFormFilledCode)
			}
		}

		return customFormApprovalTemplate.getCustomFormFilled(customFormFilledCode).then(function() {
			return $.ajax({
				url: basePath + "frame/newCustomForm/getCustomFormData.spring",
				dataType: "json",
				data: param.__form(),
				success: function(data) {

					customFormApprovalTemplate.customForm = data.customForm;

					customFormApprovalTemplate.baseImgPath = data.baseImgPath;

					var customModular = data.customModularList[0];

					param.set("customModularCode", customModular.customModularCode);

					var customFieldMap = {};
					for (var i = 0; i < customModular.customFieldList.length; i++) {
						customFieldMap[customModular.customFieldList[i].customFieldRowCode] = customModular.customFieldList[i];
					}

					var divs = [];
					for (var i = 0; i < customModular.customFieldRowList.length; i++) {
						divs.push(customFormApprovalTemplate.createRow(customModular, customFieldMap[customModular.customFieldRowList[i].customFieldRowCode]));
					}

					var $table_right = $("div.table_right");
					$table_right[0].customModular = customModular;
					var formDoms = {};
					assemblys.createElement(divs, $table_right[0], formDoms);

					var customModularList = data.customModularList;

					for (var i = 0; i < customModularList.length; i++) {
						initCustomFormTemplate.setValue(null, customModularList[i].reportValues, null, formDoms);
					}
					//回显选中一级的显示二级
					$("input:checkbox[lay-filter='oneLevelCheckbox']:checked").each(function() {
						$(this).closest("div").next("div").removeClass("layui-hide");
					});

					//回显选中一级的显示二级
					$("input:radio[lay-filter='oneLevelRadio']:checked").each(function() {
						$(this).closest("div").next("div").removeClass("layui-hide");
					});
					customFormApprovalTemplate.initDatetime($table_right, 0);
				}
			});
		})

	},
	createRow: function(customModular, customField) {
		if (!customField) {
			return {};
		}

		return {
			attr: {
				"customFieldSet": customField.customFieldSet,
				//"customFieldCode": customField.customFieldCode,
				//"customFieldBusinessCode": customField.businessCode,
				//"customFieldBusinessValue": customField.businessValue
			},
			tagName: "div",
			className: "layui-form-item",
			customField: customField,
			children: [{
				tagName: "label",
				className: "layui-form-label",
				children: [{
					tagName: "span",
					innerText: customField.customFieldName
				}, customField.isNecessField == 1 ? {
					tagName: "span",
					innerText: " *",
					style: {
						color: "#ff0000"
					}
				} : null,]
			}, {
				attr: customField.customFieldSet == "radio" || customField.customFieldSet == "checkbox" || customField.customFieldSet == "label" ? {
					"lay-verify": customField.isNecessField == 1 ? "checkboxRequired" : "",
					"title": customField.customFieldName,
					"customFieldBusinessCode": customField.businessCode,
					"customFieldBusinessValue": customField.businessValue
				} : {},
				tagName: "div",
				className: "layui-input-block",
				children: customFormApprovalTemplate[customField.customFieldSet](customModular, customField)
			}]
		}
	},
	text: function(customModular, customField) {

		var defaultValue = customField.defaultValue;
		if (defaultValue && defaultValue.length > customField.customFieldLength) {
			defaultValue = defaultValue.substring(0, customField.customFieldLength);
		}
		return [{
			attr: {
				autocomplete: "off",
				placeholder: "请输入" + customField.customFieldName,
				"lay-verify": (customField.isNecessField == 1 ? "required|" : "") + "limit" + (customField.fieldVerifyType ? "|customRegex" : ""),
				customRegex: customField.fieldVerifyType,
				customFieldBusinessCode: customField.businessCode,
				customFieldBusinessValue: customField.businessValue,
			},
			tagName: "input",
			className: "layui-input",
			type: "text",
			name: customField.customFieldCode + "-0",
			"placeholder": customField.remindText || "",
			"readOnly": customField.isRead == 1 ? true : false,
			"value": defaultValue || ""
		}];
	},
	textarea: function(customModular, customField) {

		var defaultValue = customField.defaultValue;
		if (defaultValue && defaultValue.length > customField.customFieldLength) {
			defaultValue = defaultValue.substring(0, customField.customFieldLength);
		}

		// 验证
		var attrParam = {
			autocomplete: "off",
			placeholder: "请输入" + customField.customFieldName,
			"lay-verify": (customField.isNecessField == 1 ? "required|" : "") + "limit" + (customField.fieldVerifyType ? "|customRegex" : ""),
			customRegex: customField.fieldVerifyType,
			customFieldBusinessCode: customField.businessCode,
			customFieldBusinessValue: customField.businessValue,
		};

		// 富文本时
		if (customField.isRichText == 1) {
			attrParam["lay-verify"] = customField.isNecessField == 1 ? ("required") : "";
			attrParam["isRichText"] = "true";
			attrParam["customFieldBusinessCode"] = customField.businessCode;
			attrParam["customFieldBusinessValue"] = customField.businessValue;
		}

		return [{
			attr: attrParam,
			tagName: "textarea",
			className: "layui-textarea",
			name: customField.customFieldCode + "-0",
			"placeholder": customField.remindText || "",
			"readOnly": customField.isRead == 1 ? true : false,
			"value": defaultValue || ""
		}];
	},
	datetime: function(customModular, customField) {
		return [{
			attr: {
				"lay-verify": customField.isNecessField == 1 ? "required" : "",
				readonly: "readonly",
				"prevEditTime": 0,
				customFieldBusinessCode: customField.businessCode,
				customFieldBusinessValue: customField.businessValue,
			},
			tagName: "input",
			className: "layui-input",

			type: "text",
			name: customField.customFieldCode + "-0",
			"customField": customField
		}, {
			"tagName": "i",
			"className": "layui-icon layui-icon-date layui-icon-approval"
		}];
	},
	//单选框一级
	radio: function(customModular, customField) {
		var radios = [];
		var index = 1;

		// 是否所有一级选项都没有二级选项,如果都没有,一级选项横向排列
		var hasSecond = false;
		for (var i in customField.fieldData) {
			if (customField.fieldData[i].childOptionList.length > 0) {
				hasSecond = true;
				break;
			}
		}

		for (var i = 0; i < customField.fieldData.length; i++) {
			var oneOption = customField.fieldData[i];
			var customFieldSet = oneOption.customFieldSet.replace("Other", "");
			if ("label" == oneOption.customFieldSet) {
				radios.push({
					"tagName": "div",
					"className": "layui-word-aux",
					"innerText": oneOption.customOptionSetContent
				});
			} else {
				var childrenOne = [];
				var childrenTwo = [];
				var radioObj = {
					attr: {
						"lay-skin": "primary",
						"lay-filter": "oneLevelRadio",
						"customOptionSetBusinessCode": oneOption.businessCode,
						"customOptionSetBusinessValue": oneOption.businessValue,
					},
					tagName: "input",
					type: "radio",
					title: oneOption.customOptionSetContent,
					name: oneOption.customFieldCode + "-0",
					value: oneOption.customOptionSetCode,

				};

				if (oneOption.hasDefault == "1" && !customFormApprovalTemplate.defaultApproval) {
					radioObj.attr["checked"] = "";
				}
				childrenTwo.push(radioObj);

				if ("radioOther" == oneOption.customFieldSet) {
					childrenTwo.push({
						"attr": {
							"lay-verify": (customField.isNecessField == 1 ? "hasRadioTextRequired|limit" : "limit"),
							"customOptionSetBusinessCode": oneOption.businessCode + "_text",
							"customOptionSetBusinessValue": oneOption.businessValue,
							"limit": "200",
							"checked": (oneOption.hasDefault == "1" ? "true" : "false")
						},
						"tagName": "input",
						"className": "layui-input input_item input_one item1",
						"type": "text",
						"name": oneOption.customOptionSetCode + "_text-0",
						"onclick": function() {
							if (!$(this).prev().hasClass("layui-form-checked")) {
								$(this).prev().click();
							}
						}
					});
				}

				childrenOne.push({
					"tagName": "div",
					"style": {
						"width": hasSecond ? null : "auto",
						"display": hasSecond ? null : "inline-block"
					},
					"children": childrenTwo
				});

				if (oneOption.childOptionList.length > 0) {
					childrenOne.push({
						"tagName": "div",
						"className": "twoLevelRadio " + ((oneOption.hasDefault == "1" && !customFormApprovalTemplate.defaultApproval) ? "" : "layui-hide"),
						"attr": {
							"divlevel": "2",
							"customOptionSetCode": oneOption.customOptionSetCode
						},
						"children": customFormApprovalTemplate.handleSecondOptions(customModular, oneOption.childOptionList, oneOption)
					});
				}

				radios.push({
					"tagName": "div",
					attr: {
						"checkboxGroup": ""
					},
					"children": childrenOne

				});
			}
		}

		return radios;
	},

	//checkbox一级选项
	checkbox: function(customModular, customField) {
		var checkboxs = [];
		var index = 1;

		// 是否所有一级选项都没有二级选项,如果都没有,一级选项横向排列
		var hasSecond = false;
		for (var i in customField.fieldData) {
			if (customField.fieldData[i].childOptionList.length > 0) {
				hasSecond = true;
				break;
			}
		}

		for (var i = 0; i < customField.fieldData.length; i++) {
			var oneOption = customField.fieldData[i];
			var customFieldSet = oneOption.customFieldSet.replace("Other", "");

			if ("label" == oneOption.customFieldSet) {
				checkboxs.push({
					"tagName": "div",
					"className": "layui-word-aux",
					"innerText": oneOption.customOptionSetContent
				});
			} else {
				var childrenOne = [];
				var childrenTwo = [];
				var checkboxObj = {
					attr: {
						"lay-skin": "primary",
						"lay-filter": "oneLevelCheckbox",
						"customOptionSetBusinessCode": oneOption.businessCode,
						"customOptionSetBusinessValue": oneOption.businessValue,
					},
					tagName: "input",
					type: "checkbox",
					title: oneOption.customOptionSetContent,
					name: oneOption.customFieldCode + "-0",
					value: oneOption.customOptionSetCode,

				};
				if (oneOption.hasDefault == "1" && !customFormApprovalTemplate.defaultApproval) {
					checkboxObj.attr["checked"] = "";
				}
				childrenTwo.push(checkboxObj);

				if ("checkboxOther" == oneOption.customFieldSet) {
					childrenTwo.push({
						"attr": {
							"lay-verify": (customField.isNecessField == 1 ? "hasCheckboxTextRequired|limit" : "limit"),
							"customOptionSetBusinessCode": oneOption.businessCode + "_text",
							"customOptionSetBusinessValue": oneOption.businessValue,
							"limit": "200",
							"checked": ((oneOption.hasDefault == "1" && !customFormApprovalTemplate.defaultApproval) ? "true" : "false")
						},
						"tagName": "input",
						"className": "layui-input input_item input_one item1",
						"type": "text",
						"name": oneOption.customOptionSetCode + "_text-0",
						"onclick": function() {
							if (!$(this).prev().hasClass("layui-form-checked")) {
								$(this).prev().click();
							}
						}
					});
				}
				childrenOne.push({
					"tagName": "div",
					"style": {
						"width": hasSecond ? null : "auto",
						"display": hasSecond ? null : "inline-block"
					},
					"children": childrenTwo
				});

				if (oneOption.childOptionList.length > 0) {
					childrenOne.push({
						"tagName": "div",
						"className": "twoLevelCheckbox " + ((oneOption.hasDefault == "1" && !customFormApprovalTemplate.defaultApproval) ? "" : "layui-hide"),
						"attr": {
							"divlevel": "2",
							"customOptionSetCode": oneOption.customOptionSetCode
						},
						"children": customFormApprovalTemplate.handleSecondOptions(customModular, oneOption.childOptionList, oneOption)
					});
				}

				checkboxs.push({
					"tagName": "div",
					attr: {
						"checkboxGroup": ""
					},
					"children": childrenOne

				});
			}
		}
		return checkboxs;
	},
	//二级选项
	handleSecondOptions: function(customModular, customFieldList, parentCustomField) {
		var secondOptions = [];

		for (var i = 0; i < customFieldList.length; i++) {
			var twoOption = customFieldList[i];
			if ("label" == twoOption.customFieldSet) {
				secondOptions.push({
					"tagName": "div",
					"className": "layui-word-aux",
					"innerText": twoOption.customOptionSetContent
				});
			} else {
				if ("checkbox" == twoOption.customFieldSet || "checkboxOther" == twoOption.customFieldSet) {
					var twoObj = {
						attr: {
							"lay-skin": "primary",
							"lay-filter": "twoLevelCheckbox",
							"customOptionSetBusinessCode": twoOption.businessCode,
							"customOptionSetBusinessValue": twoOption.businessValue,
						},
						tagName: "input",
						type: "checkbox",
						title: twoOption.customOptionSetContent,
						name: parentCustomField.customOptionSetCode + "-0",
						value: twoOption.customOptionSetCode,
					}
					if (twoOption.hasDefault == "1" && !customFormApprovalTemplate.defaultApproval) {
						twoObj.attr["checked"] = ""
					}
					secondOptions.push(twoObj);
					if ("checkboxOther" == twoOption.customFieldSet) {
						secondOptions.push({
							"attr": {
								"lay-verify": (twoOption.hasDefault == "1" ? "hasCheckboxTextRequired|limit" : "limit"),
								"customOptionSetBusinessValue": twoOption.businessValue,
								"customOptionSetBusinessCode": twoOption.businessCode + "_text",
								"limit": "200"
							},
							"tagName": "input",
							"className": "layui-input input_item input_one item1",
							"type": "text",
							"name": twoOption.customOptionSetCode + "_text-0",
							"onclick": function() {
								if (!$(this).prev().hasClass("layui-form-checked")) {
									$(this).prev().click();
								}
							}
						});
					}
				}
				if ("radio" == twoOption.customFieldSet || "radioOther" == twoOption.customFieldSet) {
					var twoObj = {
						attr: {
							"lay-skin": "primary",
							"lay-filter": "twoLevelRadio",
							"customOptionSetBusinessCode": twoOption.businessCode,
							"customOptionSetBusinessValue": twoOption.businessValue,
						},
						tagName: "input",
						type: "radio",
						title: twoOption.customOptionSetContent,
						name: parentCustomField.customOptionSetCode + "-0",
						value: twoOption.customOptionSetCode,

					};
					if (twoOption.hasDefault == "1" && !customFormApprovalTemplate.defaultApproval) {
						twoObj.attr["checked"] = ""
					}
					secondOptions.push(twoObj);
					if ("radioOther" == twoOption.customFieldSet) {
						secondOptions.push({
							"attr": {
								"lay-verify": (twoOption.hasDefault == "1" ? "hasRadioTextRequired|limit" : "limit"),
								"customOptionSetBusinessValue": twoOption.businessValue,
								"customOptionSetBusinessCode": twoOption.businessCode + "_text",
								"limit": "200",
								"checked": (twoOption.hasDefault == "1" ? "true" : "false")
							},
							"tagName": "input",
							"className": "layui-input input_item input_one item1",
							"type": "text",
							"name": twoOption.customOptionSetCode + "_text-0",
							"onclick": function() {
								if (!$(this).prev().hasClass("layui-form-checked")) {
									$(this).prev().click();
								}
							}
						});
					}
				}

			}
		}
		return secondOptions;
	},
	label: function(customModular, customField) {
		var labels = [];
		for (var i = 0; i < customField.fieldData.length; i++) {
			labels.push({
				attr: {
					"customFieldCode": customField.customFieldCode,
					"customOptionSetBusinessCode": customField.fieldData[i].businessCode,
					"customOptionSetBusinessValue": customField.fieldData[i].businessValue,
				},
				"tagName": "div",
				"className": "layui-word-aux",
				"innerText": (i + 1) + "、" + customField.fieldData[i].customOptionSetContent,

			});

			labels.push({
				"tagName": "br",
			});
		}

		return labels;
	},
	select: function(customModular, customField) {
		var options = [];
		options.push({
			"tagName": "option",
			"innerText": "请选择",
			"value": ""
		});
		for (var i = 0; i < customField.fieldData.length; i++) {
			options.push({
				"tagName": "option",
				"attr": {
					"customOptionSetBusinessCode": customField.fieldData[i].businessCode,
					"customOptionSetBusinessValue": customField.fieldData[i].businessValue,
				},
				"innerText": customField.fieldData[i].customOptionSetContent,
				"value": customField.fieldData[i].customOptionSetCode,
				
				"selected": !param.get("approvalBelongFlowNodeRecordDraftCode") && customField.fieldData[i].hasDefault == 1
			});
		}
		return [{
			attr: {
				"lay-verify": customField.isNecessField == 1 ? "required" : "",
				"customFieldCode": customField.customFieldCode,
				"customFieldBusinessCode": customField.businessCode,
				"customFieldBusinessValue": customField.businessValue,
			},
			"tagName": "select",
			name: customField.customFieldCode + "-0",
			children: options
		}];
	},
	org: function(customModular, customField) {
		return [{
			"attr": {
				"org": customField.customFieldSet,
			},
			"tagName": "input",
			"type": "hidden",
			"name": customField.customFieldCode + "-0"
		}, {
			"attr": {
				"lay-verify": (customField.isNecessField == 1 ? "limit|required" : "limit"),
				"limit": customField.customFieldLength || 2000,
				"readonly": "readonly",
				"customFieldCode": customField.customFieldCode,
				"customFieldBusinessCode": customField.businessCode,
				"customFieldBusinessValue": customField.businessValue,
			},
			"tagName": "input",
			"className": "layui-input",
			"name": "remark--" + customField.customFieldCode + "-0",
			"maxLength": customField.customFieldLength || 2000,
			"onclick": function() {
				customFormApprovalTemplate.orgSelect(customField.funCode, customField.relationField, this);
			}
		}, {
			"tagName": "span",
			"innerHTML": "&nbsp;"
		}, {
			"tagName": "i",
			"className": "layui-icon layui-icon-approval",
			"innerHTML": "&#xe615;",
			"style": {
				"cursor": "pointer"
			},
			"onclick": function() {
				customFormApprovalTemplate.orgSelect(customField.funCode, customField.relationField, $(this).prev().prev()[0]);
			}
		}];
	},
	"interface": function(customModular, customField) {
		return [{
			"attr": {
				"lay-verify": (customField.isNecessField == 1 ? "limit|required" : "limit"),
				"limit": customField.customFieldLength || 2000,
				"customFieldBusinessCode": customField.businessCode,
				"customFieldBusinessValue": customField.businessValue,
			},
			"tagName": "input",
			"className": "layui-input",
			"name": customField.customFieldCode + "-0",
			"maxLength": customField.customFieldLength || 2000,
			"autocomplete": "off",
			"onkeyup": function(event) {
				if (event.keyCode == '13') {
					pubInterface.findInterfaceInfo(this);
				}
			}
		}, {
			"tagName": "span",
			"innerText": " "
		}, {
			"tagName": "i",
			"className": "layui-icon2 layui-icon-approval",
			"title": "查询",
			"innerHTML": "",
			"style": {
				"cursor": "pointer"
			},
			"onclick": function() {
				pubInterface.findInterfaceInfo($(this).prev().prev()[0]);
			}
		}];
	},
	profilePhoto: function(customModular, customField) {
		return [{

		}];
	},
	orgCallback: function(result) {
		$(window.orgSelectDom).prev().val(result.value + "/" + result.name);
		window.orgSelectDom.value = result.name;
	},
	orgSelect: function(funCode, type, dom) {
		window.orgSelectDom = dom;
		layer.open({
			type: 2,
			title: "选择组织架构",
			scrollbar: false,
			area: ['800px', '95%'],
			// 带条件
			content: basePath + "plugins/udSelector/selectUserPage.jsp?type=" + type + "&callback=customFormApprovalTemplate.orgCallback&model=radio&" + (funCode ? "funCode=" + funCode : "&compNo=" + compNo)
		});
	},
	// 文件回调 - 可回显复用
	attaCallback: function(data) {
		$("#ueditorFileDiv").empty();
		pubUploader.setFileList(data, "#ueditorFileDiv", customFormApprovalTemplate.baseImgPath);
	},
	initDatetime: function($dom, index) {
		$dom.find("input[prevEditTime" + (index || index == 0 ? "=" + index : "") + "]").each(function(i, e) {
			var customField = e.customField;
			var fieldVerifyType = customField.fieldVerifyType || "";
			var dateType = fieldVerifyType || "date";
			var max = "9999-12-31 23:59:59";
			var min = "1900-01-01 00:00:00";
			if (customField.dateRange == 0) {
				customField.beginDate = customField.beginDate ? assemblys.dateToStr(customField.beginDate) : "";
				customField.endDate = customField.endDate ? assemblys.dateToStr(customField.endDate) : "";
				min = customField.beginDate || min;
				max = customField.endDate || max;
				if (dateType == "date") {
					min = min.split(" ")[0];
					max = max.split(" ")[0];
				}
			} else if (customField.dateRange == 1) {
				max = new Date().getTime();
			} else if (customField.dateRange == 2) {
				min = new Date().getTime();
			}
			layui.laydate.render({
				elem: e,
				trigger: "click",
				type: dateType,
				"max": max,
				"min": min,
				format: dateType == "date" ? "yyyy-MM-dd" : "yyyy-MM-dd HH:mm",
				btns: ['clear', 'confirm'],
				ready: function(date) {
					// 可以自定义时分秒
					var now = new Date();
					this.dateTime.hours = now.getHours();
					this.dateTime.minutes = now.getMinutes();
				}
			});
		});
	},
	initFormVerify: function() {
		$.ajax({
			url: basePath + "frame/dict/getDictListByCode.spring",
			data: {
				"dictTypeCode": "CUSTOMFORM_VERIFY",
				"appCode": "APP"
			},
			dataType: "json",
			skipDataCheck: true,
			success: function(data) {
				if (data.result == "success") {
					for (var i = 0; i < data.dictList.length; i++) {
						customFormApprovalTemplate.formVerify.customRegexDictList[data.dictList[i].dictContent] = data.dictList[i];
					}
				}
			}
		});
	},
	formVerify: {
		customRegexDictList: {},
		customRegex: function(value, item) {
			var dict = customFormTemplate.formVerify.customRegexDictList[$(item).attr("customRegex")];
			if (dict && value != "") {
				var reg = new RegExp(dict.remark);
				if (!reg.test(value)) {
					// 展开选项卡,如果当前是隐藏的
					if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
						$(item).parents(".table_right").find(".table_right_title").click();
					}

					$(item).parents(".layui-form-item").css(customFormTemplate.boderStyle).focus();

					$(".bodys").stop(true, true).animate({
						scrollTop: ($(".bodys").scrollTop() + $(item).parents(".layui-form-item").offset().top) - 90
					}, 1000);

					setTimeout(function() {
						$(item).parents(".layui-form-item").css(customFormTemplate.noneBoderStyle);
					}, 3000);

					return "「" + $(item).parents(".layui-form-item").attr("customFieldName") + "」" + "请输入正确的" + dict.dictName + "";
				}
			}
		},
	},
}

var customFormTemplate = {
	boderStyle: {
		"border": "2px double #FF0000",
		"border-radius": "4px"
	},
	//#FF0000:红色     #5FB878
	noneBoderStyle: {
		"border": "none",
		"border-radius": ""
	},
	clearInput: function(dom) {
		// 文本
		$(dom).find("input:text,textarea").val("");
		// 单选多选
		$(dom).find("input:radio,input:checkbox").prop("checked", false);
		// 下拉
		$(dom).find("select").each(function() {
			var $option = $(this).children(":eq(0)");
			$option[0].selected = true;
		});

		layui.form.render();
	},
	// 富文本下标
	richTextIndex: 0,
	// 加载富文本
	loadRichText: function() {
		var toolbars = [['bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'removeformat', 'formatmatch', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|', 'rowspacingtop', 'rowspacingbottom', 'lineheight', '|', 'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'insertimage', '|', 'horizontal', 'date', 'time', '|', 'customstyle', 'paragraph', 'fontfamily', 'fontsize']];
		// 富文本渲染
		$("textarea[isRichText='true']").each(function() {
			var id = "richText" + customFormTemplate.richTextIndex;
			$(this).attr({
				"id": id,
				"isRichText": "false"
			});
			pubUploader.initEditor(id, true, toolbars);
			customFormTemplate.richTextIndex++;
		});
	}
}

var initCustomFormTemplate = {
	setValue: function(key, newVal, dom, formDoms) {

		var valObject = newVal;
		if (key) {
			valObject = {};
			valObject[key] = newVal;
		} else if (!(Object.prototype.toString.call(newVal) === '[object Object]')) {
			throw "设置的值必须是一个对象！";
		}

		if (!formDoms) {
			formDoms = {};
		}
		var $form = dom ? $(dom) : $("form");
		for (var k in valObject) {
			key = k;
			newVal = valObject[k];
			var $elem;
			if (formDoms[key]) {
				$elem = $(formDoms[key]);
			} else {
				$elem = $form.find("input[name='" + key + "']");
				if ($elem.length == 0) {
					$elem = $form.find("textarea[name='" + key + "']");

					if ($elem.length == 0) {
						$elem = $form.find("select[name='" + key + "']");
					}
				}

				formDoms[key] = [];
				$elem.each(function(i, element) {
					formDoms[key].push(this);
				});
			}

			if ($elem.length > 0) {
				/*if (Object.prototype.toString.call(newVal) != '[object Array]') {
					newVal = [ newVal ];
				}*/
				var customFieldSet = $elem.parents("div[customFieldSet]").attr("customFieldSet");

				if ($elem.is(":checkbox")) {
					$elem.filter(":checked").each(function() {
						this.checked = false;
					});
					for (var k in newVal) {
						var $e = $elem.filter("[value=" + newVal[k] + "]");
						if ($e.length == 0) {
							$e = $elem.filter("[optionRemark=" + newVal[k] + "]");
							if ($e.length == 0) {
								continue;
							}
						}
						$e.prop("checked", true);
						$e.parents("div.second").addClass("second_show");
					}

					var $p = $($elem[0].parentNode.parentNode.previousElementSibling).find("input[value=" + key.split("-")[0] + "]");
					if ($p.length > 0) {
						$p.prop("checked", true);
					}

				} else if ($elem.is(":radio")) {

					$elem.filter(":checked").each(function() {
						this.checked = false;
					}).removeAttr("radio-checked");

					for (var k in newVal) {
						var $e = $elem.filter("[value=" + newVal[k] + "]");
						if ($e.length == 0) {
							$e = $elem.filter("[optionRemark=" + newVal[k] + "]");
							if ($e.length == 0) {
								continue;
							}
						}
						$e.prop("checked", true).attr("radio-checked", "true");
						$e.parent().next().addClass("second_show");
					}

				} else if (customFieldSet == "select" && $elem.is("select")) {
					for (var m in newVal) {
						$elem = $(formDoms[key]);
						var $select = $elem.eq(m);
						if ($select.length > 0) {
							$select.val($select.find("option[value='" + newVal[m] + "'],option[optionRemark='" + newVal[m] + "']").val()).change();
						}
					}
				} else {
					for (var k in newVal) {
						if ((typeof newVal[k] === 'number') && newVal[k] > 1000000000000) {
							newVal[k] = assemblys.dateToStr(newVal[k]);
						} else if (newVal[k] instanceof Date) {
							newVal[k] = assemblys.dateToStr(newVal[k].getTime());
						} else if (newVal[k] instanceof Object && newVal[k].time) {
							newVal[k] = assemblys.dateToStr(newVal[k].time);
						}

						var fieldVerifyType = $elem.eq(k).attr("fieldVerifyType");
						if (fieldVerifyType == "datetime" && newVal[k]) {
							newVal[k] = (newVal[k] + "").substr(0, 16);
						} else if (fieldVerifyType == "date" && newVal[k]) {
							newVal[k] = (newVal[k] + "").substr(0, 10);
						}

						$elem.eq(k).val(newVal[k]);
					}
				}
			}
		}

		layui.form.render();
	}
}

var defaultApproval = {
	toSelectApprovalUser: function(obj) {
		window.__multipleSelectParam = {
			placeholder: "用户名称",
			URL: basePath + "frame/useraction/getHasFunRightUsers.spring",
			param: {
				funCode: param.get("funCode"),
				rightPoint: 1,
			},
			field: {
				name: "userName",
				value: "uID"
			},
			parseData: function(data) {
				return data.users;
			},
			callback: typeof obj === "function" ? obj : function(data) {
				var names = [];
				var values = [];
				for (var i = 0; i < data.length; i++) {
					names.push(data[i].name);
					values.push(data[i].value);
				}
				var n = names.join(",");
				obj.value = n;
				obj.title = n;

				var approvalUIDs = values.join(",");
				var approvalFlowNodeData = {
					approvalRight: 3,
					approvalUIDs: approvalUIDs
				}
				$(obj).next().val(JSON.stringify(approvalFlowNodeData));
			}
		};

		layer.open({
			type: 2,
			skin: 'layui-layer-aems',
			id: "toSelectApprovalCustomFormCode",
			area: ['800px', '800px'],
			title: "选择用户",
			scrollbar: false,
			content: basePath + "plugins/components/multipleSelect/multipleSelect.html",
		});

	},
	toSelectCopyUser: function(obj) {
		window.__multipleSelectParam = {
			parentName: "科室",
			parentURL: basePath + "frame/common/getDeptListNew.spring",
			parentField: {
				name: "DeptName",
				value: "DeptID"
			},
			parentParam: {
				compNo: param.get("compNo"),
			},
			parentParseData: function(data) {
				return data.deptList;
			},
			values: (function() {
				var values = [];
				if (param.get("copyUserCodes").length != 0) {
					var nameArr = param.get("copyUserNames").split(",");
					var valueArr = param.get("copyUserCodes").split(",");
					for (var i = 0; i < valueArr.length; i++) {
						var rightObj = {
							name: nameArr[i],
							value: valueArr[i]
						}
						values.push(rightObj)
					}
				}
				return values;
			})(),
			placeholder: "用户名称",
			URL: basePath + "frame/common/getUserList.spring",
			param: {},
			field: {
				name: "userName",
				value: "uid"
			},
			parseData: function(data) {
				return data.userList;
			},
			callback: function(data) {
				var names = [];
				var values = [];
				for (var i = 0; i < data.length; i++) {
					names.push(data[i].name);
					values.push(data[i].value);
				}
				obj.value = names.join(",");
				$(obj).next().val(values.join(","));

			}
		};

		layer.open({
			type: 2,
			skin: 'layui-layer-aems',
			area: ['800px', '800px'],
			title: "选择用户",
			scrollbar: false,
			content: basePath + "plugins/components/multipleSelect/multipleSelect.html"
		});

	}
}