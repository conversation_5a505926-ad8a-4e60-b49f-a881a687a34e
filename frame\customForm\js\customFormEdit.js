$(function() {
	
	customFormEdit.getExceAppList().then(function() {
		return customFormEdit.getCustomFormTypeList();
	}).then(function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			integer : function(value, item) {
				if (!(/^[0-9]*$/.test(value))) {
					return '只能填写整数';
				}
			},
			businessCode : function(value, item) {
				if (value && customFormEdit.checkCustomFormBusinessCode(value)) {
					return '表单编号已存在！';
				}
			}
		});
		form.on("submit(save)", function(data) {
			if (isSubmit) {
				return;
			}
			isSubmit = true;
			customFormEdit.saveCustomForm();
			return false;
		});
		
		form.on("radio(customFormClass)", function(data) {
			customFormEdit.setFlowDirectionView(data.value);
			customFormEdit.setCustomApprovalView(data.value);
		});
		
		form.on("radio(flowDirection)", function(data) {
			customFormEdit.setCustomApprovalView(data.value);
		});
		
		if (customFormCode) {
			customFormEdit.getCustomForm(form);
		} else {
			form.val('example', {
				"appCode" : appCode
			});
		}
	})
});
var customFormEdit = {
	// 修改前表单分类Code
	editBeforeCustomFormTypeCode : "",
	// 获取分类信息
	getCustomFormTypeList : function() {
		return $.ajax({
			url : basePath + "frame/customFormType/getCustomFormTypeList.spring",
			data : {
				"appCode" : appCode,
			},
			success : function(data) {
				if (data.result == "success") {
					var $select = $("#customFormTypeCode");
					$select.append("<option value=''>请选择</option>")
					var customFormTypeList = data.data.customFormTypeList;
					// 处理数据
					for (var i = 0; i < customFormTypeList.length; i++) {
						var temp = customFormTypeList[i];
						var selected = ""
						if (customFormTypeCode == temp.customFormTypeCode) {
							selected = "selected";
						}
						$select.append("<option " + selected + " value='" + temp.customFormTypeCode + "'>" + temp.customFormTypeName + "</option>")
					}
				}
			}
		});
	},
	// 获取表单
	getCustomForm : function(form) {
		$.ajax({
			url : basePath + "frame/customForm/getCustomForm.spring",
			dataType : "json",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : appCode
			},
			success : function(data) {
				if (data.result == "success") {
					var customForm = data.customForm;
					if ($("input[name=copy]").val() == 1) {
						customForm.customFormCode = "";
						customForm.customFormID = "";
						customForm.businessCode = "";
					}
					form.val('example', {
						"customFormName" : customForm.customFormName + "",
						"customFormCode" : customForm.customFormCode + "",
						"customFormTypeCode" : customForm.customFormTypeCode == "none_type" ? "" : customForm.customFormTypeCode + "",
						"customFormClass" : customForm.customFormClass + "",
						"flowDirection" : customForm.flowDirection + "",
						"customApprovalFlowCode" : customForm.customApprovalFlowCode + "",
						"customApprovalFlowName" : customForm.customApprovalFlowName + "",
						"customFormID" : customForm.customFormID,
						"appCode" : customForm.appCode,
						"deptID" : customForm.deptID,
						"createUserCode" : customForm.createUserCode,
						"createUserName" : customForm.createUserName,
						"createDate" : assemblys.dateToStr(customForm.createDate.time),
						"seqNo" : customForm.seqNo,
						"status" : customForm.status == 0 ? '0' : '1',
						"businessCode" : customForm.businessCode,
						"hasFileUpload" : customForm.hasFileUpload
					});
					currBusinessCode = customForm.businessCode;
					customFormEdit.editBeforeCustomFormTypeCode = customForm.customFormTypeCode;
					
					customFormEdit.setCustomApprovalView(customForm.customFormClass);
					customFormEdit.setFlowDirectionView(customForm.customFormClass);
					if (customForm.flowDirection == 1) {
						customFormEdit.setCustomApprovalView(customForm.flowDirection);
					}
					
					form.render();
					
				}
			}
		});
	},
	setCustomApprovalView : function(value) {
		if (value == "1") {
			$(".customApprovalView").addClass("layui-hide");
			customFormEdit.clearCustomApprovalFlowValue();
		} else {
			$(".customApprovalView").removeClass("layui-hide");
		}
	},
	setFlowDirectionView : function(value) {
		if (value == "1") {
			$(".flowDirectionView").addClass("layui-hide");
			$("#flowDirectionDiv").attr("lay-verify", "");
		} else {
			$(".flowDirectionView").removeClass("layui-hide");
			$("#flowDirectionDiv").attr("lay-verify", "checkboxRequired");
		}
	},
	saveCustomForm : function() {
		//获取数据之前先取消按钮禁用
		$("input[name='customFormClass']").attr("disabled", false);
		var datas =  $("#form1").serialize();
		// 保存回调
		var callback = function() {
			$.ajax({
				url : basePath + "frame/customForm/saveCustomForm.spring",
				dataType : "json",
				data : datas,
				success : function(data) {
					if (data.result == "success") {
						assemblys.msg($("input[name=copy]").val() == 1 ? "复制成功" : "保存成功", function() {
							assemblys.closeWindow();
						});
					} else {
						assemblys.alert("保存失败");
						isSubmit = false;
					}
				}
			});
		}

		// 判断是否修改过分类
		var customFormTypeCode = $("#customFormTypeCode").val() || "";
		if (customFormEdit.editBeforeCustomFormTypeCode != "" && customFormTypeCode != customFormEdit.editBeforeCustomFormTypeCode) {
			assemblys.confirm("更改【表单分类】会取消该表单属于原【表单分类】的【公用组件】关联关系，确定继续要保存吗？", function() {
				callback();
			}, function() {
				isSubmit = false;
			});
		} else {
			callback();
		}
		
	},
	getExceAppList : function() {
		return $.ajax({
			url : basePath + "frame/customForm/getExceAppList.spring",
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					var appList = data.appList;
					var $select = $("select[appCode]");
					var html = "";
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0) {
							html += '<option value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
						}
					}
					$select.append(html);
				} else {
					assemblys.alert("获取应用出错，请联系管理员");
				}
			},
			error : function() {
				assemblys.alert("获取应用数据出错，请联系管理员");
			}
		});
	},
	checkCustomFormBusinessCode : function(businessCode) {
		if (businessCode == currBusinessCode) {
			return false;
		}
		var flag = true;
		$.ajax({
			url : basePath + "frame/customForm/checkCustomFormBusinessCode.spring",
			data : {
				"businessCode" : businessCode,
				"customFormCode" : customFormCode,
				"appCode" : appCode,
			},
			dataType : "json",
			async : false,
			success : function(data) {
				if (data.result == "success") {
					flag = false;
				}
			}
		});
		return flag;
	},
	/**
	 * 选择流程
	 */
	toSelectCustomApprovalCode : function(obj) {
		window.__singleSelectParam = {
			parentName : "流程分类",
			parentURL : basePath + "frame/customApprovalFlowType/getCustomApprovalFlowTypeList.spring",
			parentField : {
				name : "customApprovalFlowTypeName",
				value : "customApprovalFlowTypeCode"
			},
			parentParam : {
				"appCode" : appCode,
			},
			parentParseData : function(data) {
				return data.customApprovalFlowTypeList;
			},
			placeholder : "流程名称",
			URL : basePath + "frame/customApprovalFlow/getCustomApprovalFlowList.spring",
			param : {
				"appCode" : appCode,
			},
			field : {
				name : "customApprovalFlowName",
				value : "customApprovalFlowCode"
			},
			parseData : function(data) {
				return data.customApprovalFlowList;
			},
			callback : function(name, value) {
				obj.value = name;
				$(obj).next().val(value);
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '500px', '90%' ],
			title : "选择流程",
			scrollbar : false,
			content : basePath + "plugins/components/singleSelect/singleSelect.html"
		});
		
	},
	clearCustomApprovalFlowValue : function() {
		$("input[name='customApprovalFlowCode']").val("");
		$("input[name='customApprovalFlowName']").val("");
	}
};