var fieldSet = {
	getFieldSet : function(customModular, customField, index) {
		return {
			"tagName" : "form",
			"className" : "moveInput",
			"children" : [ {
				
				"attr" : {
					"customFieldSet" : customField.customFieldSet,
					"customFieldCode" : customField.customFieldCode
				},
				"tagName" : "label",
				"className" : "layui-form-label item_label",
				"innerText" : customField.customFieldName
			}, fieldSet[customField.customFieldSet](customModular, customField, index) ]
		};
	},
	radio : function(customModular, customField, index) {
		return {
			"tagName" : "div",
			"className" : "layui-unselect layui-form-radio layui-form-radioed layui-form-draft",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-anim layui-icon",
				"innerHTML" : "&#xe643;"
			}, {
				"tagName" : "div",
				"innerText" : "单选框"
			} ]
		};
	},
	checkbox : function(customModular, customField, index) {
		return {
			"attr" : {
				"lay-skin" : "primary"
			},
			"tagName" : "div",
			"className" : "layui-unselect layui-form-checkbox layui-form-checked layui-form-draft",
			"style" : {
				"display" : "inline-block",
				"width" : "auto"
			},
			"children" : [ {
				"tagName" : "span",
				"innerText" : "多选框"
			}, {
				"tagName" : "i",
				"className" : "layui-icon",
				"style" : {
					"margin-top" : "0px"
				},
				"innerHTML" : "&#xe605;"
			} ]
		};
	},
	label : function(customModular, customField, index) {
		return {
			"tagName" : "span",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon2 layui-icon-draft",
				"innerHTML" : "&#xe714;"
			}, {
				"tagName" : "span",
				"innerText" : " 标签"
			} ]
		};
	},
	select : function(customModular, customField, index) {
		return {
			"tagName" : "div",
			"className" : "layui-input-inline",
			"children" : [ {
				"tagName" : "div",
				"className" : "layui-unselect layui-form-select",
				"children" : [ {
					"tagName" : "div",
					"className" : "layui-select-title",
					"children" : [ {
						"attr" : {
							"placeholder" : "下拉框",
							"readonly" : "readonly"
						},
						"tagName" : "input",
						"className" : "layui-input layui-unselect",
						"type" : "text"
					}, {
						"tagName" : "i",
						"className" : "layui-edge"
					} ]
				} ]
			} ]
		};
		
	},
	"interface" : function(customModular, customField, index) {
		return {
			"attr" : {
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input input_item layui-input-draft",
			"type" : "text"
		};
	},
	img : function(customModular, customField, index) {
		return {
			"tagName" : "i",
			"className" : "layui-icon layui-icon-draft",
			"innerHTML" : "&#xe64a;"
		};
	},
	textarea : function(customModular, customField, index) {
		return {
			"attr" : {
				"readonly" : "readonly",
				"placeholder" : "请输入内容"
			},
			"tagName" : "textarea",
			"className" : "layui-textarea"
		};
	},
	text : function(customModular, customField, index) {
		return {
			"attr" : {
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input input_item layui-input-draft",
			"type" : "text"
		};
		
	},
	datetime : function(customModular, customField, index) {
		return {
			"tagName" : "span",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-icon-draft",
				"innerHTML" : "&#xe637;"
			}, {
				"tagName" : "span",
				"innerText" : " 日期"
			} ]
		};
	},
}