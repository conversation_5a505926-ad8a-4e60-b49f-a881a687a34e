$(function() {
	
	assemblys.getMenuIcon({
		funCode : funCode,
		hasOrg : false,
		dom : $("b#menuIcon"),
		menuName : "已填表单查询"
	});
	
	customFormFilledList.getExceAppList();
	
	layui.use([ "form", "laypage" ], function() {
		var form = layui.form;
		form.render("select");
		var $compNo = $("select[lay-filter='compNo']");
		if ($compNo.length > 0) {
			$("#compNo").val($compNo.val());
		}
		form.on("select(compNo)", function(data) {
			customFormFilledList.changeCompNo(data.value);
		});
		form.on("select(appCode)", function(data) {
			customFormFilledList.changeApp(data.value);
			localStorage.setItem("CUSTOM_APPCODE_VALUE", data.value);
		});
		form.on("select(status)", function(data) {
			customFormFilledList.changeStatus(data.value);
		});
		customFormFilledList.getCustomFormFilledListData();
	});
});
var customFormFilledList = {
	changeCompNo : function(compNo) {
		location.href = basePath + "frame/customForm/customFormFilledList.spring?compNo=" + compNo + "&appCode=" + $("select[name='appCode']").val() + "&status=" + $("select[name='status']").val();
	},
	changeApp : function(appCode) {
		location.href = basePath + "frame/customForm/customFormFilledList.spring?compNo=" + $("#compNo").val() + "&appCode=" + appCode + "&status=" + $("select[name='status']").val();
	},
	changeStatus : function(status) {
		location.href = basePath + "frame/customForm/customFormFilledList.spring?compNo=" + $("#compNo").val() + "&appCode=" + $("select[name='appCode']").val() + "&status=" + status;
	},
	getCustomFormFilledListData : function() {
		$.ajax({
			url : basePath + "frame/customForm/getCustomFormFilledListData.spring",
			dataType : "json",
			data : $("#form1").serialize(),
			success : function(data) {
				if (data.result == "success") {
					$("tr.main_title").nextAll().remove();
					var pager = data.pager;
					
					layui.use("laypage", function() {
						var laypage = layui.laypage;
						laypage.render({
							elem : 'layui-table-page1',
							count : pager.totalCount,
							limit : pager.pageSize,
							curr : pager.curPageNum,
							layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
							jump : function(obj, first) {
								if (!first) {
									document.getElementById("curPageNum").value = obj.curr;
									document.getElementById("pageSize").value = obj.limit;
									customFormFilledList.getCustomFormFilledListData();
								}
							}
						});
					});
					
					var items = pager.items;
					if (items.length == 0) {
						$(".main_title").after('<tr><td colspan="8" align="center">没有相关数据！</td></tr>');
					} else {
						var $tr;
						for (var i = items.length - 1; i >= 0; i--) {
							$tr = $(".main_title").prev().clone();
							var $tds = $tr.children();
							var $is = $tds.eq(0).children();
							$is.eq(0).attr("customFormCode", items[i].customFormCode).attr("customFormFilledCode", items[i].customFormFilledCode).attr("appCode", items[i].appCode).click(function(e) {
								var url = basePath + "frame/customForm/customFormFilledDetail.jsp?customFormCode=" + $(this).attr("customFormCode") + "&customFormFilledCode=" + $(this).attr("customFormFilledCode") + "&appCode=" + $("select[name='appCode']").val();
								layer.open({
									type : 2,
									skin : 'layui-layer-aems',
									title : '表单详情',
									scrollbar : false,
									area : [ '98%', '98%' ],
									content : url
								});
							});
							$tds.eq(1).text(items[i].appCode);
							$tds.eq(2).text(items[i].customFormName);
							$tds.eq(3).children().attr("customFormCode", items[i].customFormCode).attr("customFormFilledCode", items[i].customFormFilledCode).attr("appCode", items[i].appCode).click(function(e) {
								var url = basePath + "frame/customForm/customFormTemplate.html?customFormCode=" + $(this).attr("customFormCode") + "&customFormFilledCode=" + $(this).attr("customFormFilledCode") + "&appCode=" + $(this).attr("appCode") + "&type=1";
								url += "&appCode=" + $("select[name='appCode']").val();
								layer.open({
									type : 2,
									skin : 'layui-layer-aems',
									title : '表单预览',
									scrollbar : false,
									area : [ '98%', '98%' ],
									content : url
								});
							}).text(items[i].customFormFilledCode);
							$tds.eq(4).text(items[i].optUserName);
							$tds.eq(5).text(assemblys.dateToStr(items[i].optDate.time));
							$tds.eq(6).text(items[i].seqNo);
							$tds.eq(7).html('<font style="color: ' + (items[i].status == 0 ? 'red' : 'green') + ';">' + (items[i].status == 0 ? '无效' : '有效') + '</font>');
							$(".main_title").after($tr.removeClass("layui-hide"));
						}
					}
				} else {
					assemblys.alert("获取表单列表数据出错");
				}
			}
		});
	},
	getExceAppList : function() {
		$.ajax({
			url : basePath + "frame/customForm/getExceAppList.spring",
			data : {
				"compNo" : $("#compNo").val()
			},
			dataType : "json",
			async : false,
			success : function(data) {
				if (data.result == "success") {
					var appList = data.appList;
					var customAppCode = localStorage.getItem("CUSTOM_APPCODE_VALUE");
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0) {
							var selected = customAppCode == appList[i].appCode ? "selected" : "";
							$("select[name='appCode']").append('<option ' + selected + ' value="' + appList[i].appCode + '" ' + (appCode == appList[i].appCode ? "selected" : "") + '>' + appList[i].appName + '</option>');
						}
					}
				} else {
					assemblys.alert("获取应用出错，请联系管理员");
				}
			},
			error : function() {
				assemblys.alert("获取应用数据出错，请联系管理员");
			}
		});
	}
};