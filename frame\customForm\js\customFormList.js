var customFormList = {
	// 分类长度
	typeLength : 0,
	// 初始化
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			return customFormList.getExceAppList();
		}).then(function() {
			assemblys.initSessionStoragePram();
			return customFormList.getCustomFormTypeList();
		}).then(function() {
			customFormList.getCustomFormListData();
			customFormList.initLayui();
		});
	},
	getCustomFormTypeList : function() {
		customFormList.typeLength = 0;
		return $.ajax({
			url : basePath + "frame/customFormType/getCustomFormTypeList.spring",
			data : {
				"appCode" : param.get("appCode"),
				"compNo" : param.get("compNo")
			},
			success : function(data) {
				var treeData = [ {
					title : "全部"
				}, {
					title : "未分类",
					customFormTypeCode : "none_type"
				} ];
				// 处理数据
				for (var i = 0; i < data.customFormTypeList.length; i++) {
					var customFormType = data.customFormTypeList[i];
					if (customFormType.customFormTypeCode == "none_type") {
						continue;
					}
					customFormType.title = customFormType.customFormTypeName;
					treeData.push(customFormType);
					customFormList.typeLength++;
				}
				layui.tree.render({
					elem : '#tree', //绑定元素
					data : treeData,
					click : function(item) {
						param.set("customFormTypeCode", item.data.customFormTypeCode || "");
						customFormList.getCustomFormListData();
					}
				});
			}
		});
	},
	getCustomFormListData : function() {
		
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "frame/newCustomForm/getCustomFormListData.spring?" + param.__form(),
			cols : [ [ {
				title : '序号',
				align : "center",
				type : 'numbers'
			}, {
				title : "操作",
				width : 115,
				align : "center",
				templet : function(d) {
					var html = "";
					html += '<i class="layui-icon layui-icon-edit i_check" title="编辑" style="cursor: pointer" lay-event="edit"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" style="cursor: pointer" lay-event="del"></i>';
					html += '<i class="layui-icon2 i_icon" title="自定义表单" style="cursor: pointer" lay-event="set">&#xe7e5;</i>';
					html += '<i class="layui-icon2 i_icon" title="导出SQL" style="cursor: pointer" lay-event="exportSQL">&#xea80;</i>';
					html += '<i class="layui-icon2 i_icon" title="复制" style="cursor: pointer" lay-event="copy">&#xe77e;</i>';
					return html;
				}
			}, {
				title : '表单名称',
				align : "left",
				minWidth : 150,
				templet : function(d) {
					return assemblys.htmlEncode(d.customFormName);
				}
			}, {
				title : '表单分类',
				align : "left",
				minWidth : 100,
				templet : function(d) {
					return assemblys.htmlEncode(d.customFormTypeName);
				}
			}, {
				title : '流程名称',
				align : "left",
				minWidth : 150,
				templet : function(d) {
					return assemblys.htmlEncode(d.customApprovalFlowName);
				}
			}, {
				title : '表单类型',
				align : "center",
				width : 90,
				templet : function(d) {
					return d.customFormClass == 0 ? "<font style='color:green;'>上报</font>" : "<font style='color:blue;'>审批</font>";
				}
			}, {
				title : '业务编号',
				align : "left",
				minWidth : 100,
				templet : function(d) {
					return assemblys.htmlEncode(d.businessCode);
				}
			}, {
				title : '操作人',
				align : "center",
				width : 145,
				templet : function(d) {
					var html = "";
					html += assemblys.htmlEncode(d.optUserName);
					html += "<br>";
					html += assemblys.dateToStr(d.optDate);
					return html;
				}
			}, {
				title : '顺序号',
				align : "center",
				width : 75,
				templet : function(d) {
					return d.seqNo;
				}
			}, {
				title : '状态',
				align : "center",
				width : 90,
				templet : function(d) {
					return '<input type="checkbox"  lay-filter="status"  lay-skin="switch"  customFormCode="' + d.customFormCode + '"  ' + (d.status == "1" ? "checked" : "") + ' lay-text="启用|停用">';
				}
			} ] ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
			},
			// 绑定事件
			events : {
				edit : customFormList.customFormEdit,
				del : customFormList.deleteCustomForm,
				set : customFormList.set,
				exportSQL : customFormList.exportSQL,
				copy : customFormList.copy,
			}
		});
	},
	customFormEdit : function(d) {
		
		if (customFormList.typeLength == 0) {
			assemblys.msg("请先新增表单分类");
			return;
		}
		
		var url = basePath + "frame/customForm/customFormEdit.jsp?customFormCode=" + d.customFormCode + "&compNo=" + param.get("compNo") + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&customFormTypeCode=" + param.get("customFormTypeCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '表单编辑',
			scrollbar : false,
			area : [ '860px', '500px' ],
			content : url,
			end : function() {
				customFormList.getCustomFormListData();
			}
		});
	},
	copy : function(d) {
		
		var url = basePath + "frame/customForm/customFormEdit.jsp?customFormCode=" + d.customFormCode + "&copy=1&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&customFormTypeCode=" + (param.get("customFormTypeCode") || "");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '表单复制',
			scrollbar : false,
			area : [ '860px', '500px' ],
			content : url,
			end : function() {
				customFormList.getCustomFormListData();
			}
		});
	},
	set : function(d) {
		location.url({
			url : basePath + "frame/customForm/customForm.html",
			param : {
				"customFormCode" : d.customFormCode,
				"customFormName" : d.customFormName,
				"customFormTypeCode" : d.customFormTypeCode,
				"customFormClass" : d.customFormClass,
				"compNo" : param.get("compNo"),
				"appCode" : param.get("appCode"),
				"funCode" : param.get("funCode")
			}
		});
	},
	exportSQL : function(d) {
		var $exportCustomForm = $("#exportCustomForm");
		$exportCustomForm.empty();
		$exportCustomForm.append('<input type="hidden" name="customFormCode" value="' + d.customFormCode + '"/>');
		$exportCustomForm.append('<input type="hidden" name="customFormName" value="' + d.customFormName + '"/>');
		$exportCustomForm.append('<input type="hidden" name="appCode" value="' + d.appCode + '"/>');
		$exportCustomForm.prop("action", basePath + "frame/customForm/exportCustomForm.spring");
		$exportCustomForm.prop("onsubmit", "");
		$exportCustomForm.submit();
	},
	deleteCustomForm : function(d) {
		assemblys.confirm("确定删除【" + d.customFormName + "】表单吗?<br><div style='text-align:center;color:red;'>（此操作不可恢复）</div>", function() {
			$.ajax({
				url : basePath + "frame/newCustomForm/deleteCustomForm.spring",
				dataType : "json",
				data : {
					"customFormCode" : d.customFormCode,
					"appCode" : param.get("appCode")
				},
				type : "POST",
				success : function(data) {
					assemblys.msg("删除成功", function() {
						customFormList.getCustomFormListData();
					});
				}
			});
		});
	},
	updateCustomFormState : function(obj) {
		var status = obj.elem.checked ? 1 : 0;
		var customFormCode = $(obj.elem).attr("customFormCode");
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		$.ajax({
			url : basePath + "frame/newCustomForm/updateCustomFormState.spring",
			type : "post",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : param.get("appCode"),
				"status" : status
			},
			dataType : "json",
			success : function(data) {
				assemblys.msg("更改成功", function() {
					hasSubmit = false;
					customFormList.getCustomFormListData();
				}, 200);
			}
		});
		
	},
	getExceAppList : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getExceAppList.spring",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var appList = data.appList;
				var $select = $("select[name='appCode']");
				var html = "";
				var customAppCode = localStorage.getItem("CUSTOM_APPCODE_VALUE");
				
				var currAppCode = param.get("currAppCode");
				if (!currAppCode) {
					$('.hide-appCode').removeClass('layui-hide');
					var appCode = param.get("appCode");
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0) {
							var selected = customAppCode == appList[i].appCode ? "selected" : "";
							html += '<option ' + selected + ' value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
						}
					}
				} else {
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0 && currAppCode == appList[i].appCode) {
							html += '<option value="' + appList[i].appCode + '" selected>' + appList[i].appName + '</option>';
						}
					}
				}
				$select.append(html);
				layui.form.render();
			}
		});
	},
	toCustomFormTypeList : function() {
		var url = "customFormTypeList.html";
		location.url({
			url : url,
			param : {
				"funCode" : param.get("funCode"),
				"appCode" : param.get("appCode"),
				"compNo" : param.get("compNo")
			}
		})
	},
	jsonExport : function() {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "layer-editField",
			title : 'JSON导入',
			scrollbar : false,
			area : [ '900px', '430px' ],
			content : "jsonExport.html?compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
		});
	},
	initLayui : function() {
		var form = layui.form;
		form.on("select(compNo)", function(data) {
			param.set("customFormTypeCode", "");
			customFormList.getCustomFormTypeList().then(function() {
				customFormList.getCustomFormListData();
			});
		});
		form.on("select(appCode)", function(data) {
			param.set("customFormTypeCode", "");
			customFormList.getCustomFormTypeList().then(function() {
				customFormList.getCustomFormListData();
			});
			localStorage.setItem("CUSTOM_APPCODE_VALUE", data.value);
		});
		form.on("radio(status)", function(data) {
			customFormList.getCustomFormListData();
		});
		form.on("radio(customFormClass)", function(data) {
			customFormList.getCustomFormListData();
		});
		form.on("switch(status)", function(obj) {
			customFormList.updateCustomFormState(obj);
		});
		form.render();
		
		// 监听json导入开关
		$(document).keyup(function(e) {
			if (e.keyCode == 192) {
				$("#jsonExportButton").toggle();
			}
		});
	}
};