var hasSubmit = false;

var customFormMenuList = {
	init : function() {
		// 图标 + 组织架构
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			$(".appCodeRemark").html(param.get("appCode").toUpperCase());
			// 数据
			customFormMenuList.getCustomFormMenuList();
		});
	},
	getCustomFormMenuList : function() {
		layui.table.render({
			elem : '#list',
			url : basePath + 'frame/customFormType/getCustomFormTypeMenuList.spring?' + param.__form(),
			page : false,
			parseData : function(res) {
				var list = res.customFormTypeMenuList;
				return {
					"code" : "0",
					"data" : list
				};
			},
			cols : [ [ {
				title : '序号',
				align : "center",
				type : 'numbers'
			}, {
				title : '操作',
				width : 90,
				align : "center",
				templet : function(d) {
					var html = '';
					html += '<i class="layui-icon2 i_delete" title="生成应用功能" lay-event="toGenerate" >&#xe80c;</i>';
					html += '<i class="layui-icon2 i_delete" title="刷新菜单" lay-event="toUpdateMenu" >&#xe967;</i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除分类" lay-event="toDel" ></i>';
					return html;
				}
			}, {
				title : '菜单编码',
				align : "left",
				minWidth : 150,
				templet : function(d) {
					return assemblys.htmlDecode(d.customFormTypeMenuNo);
				}
			}, {
				title : '菜单名称',
				align : "left",
				minWidth : 150,
				templet : function(d) {
					return assemblys.htmlDecode(d.customFormTypeMenuName);
				}
			}, {
				title : '菜单类型',
				align : "center",
				width : 120,
				templet : function(d) {
					if (d.customFormTypeMenuType == 0) {
						return "审批";
					} else if (d.customFormTypeMenuType == 1) {
						return "查阅";
					} else if (d.customFormTypeMenuType == 2) {
						return "我的";
					} else if (d.customFormTypeMenuType == 3) {
						return "上报";
					}
				}
			}, {
				title : '表单状态',
				align : "left",
				minWidth : 200,
				templet : function(d) {
					var stateText = new Array();
					var menuStateList = d.menuStateList;
					for (var i = 0; i < menuStateList.length; i++) {
						var state = menuStateList[i];
						stateText.push(assemblys.htmlDecode(state.customFormTypeStateName));
					}
					return stateText.join("、");
				}
			} ] ]
		});
		
		layui.table.on("tool(list)", function(obj) {
			if (obj.event == "toDel") {
				customFormMenuList.toDel(obj.data);
			}
			if (obj.event == "toGenerate") {
				customFormMenuList.toGenerate(obj.data);
			}
			if (obj.event == "toUpdateMenu") {
				customFormMenuList.toUpdateMenu(obj.data);
			}
			return;
		});
		
	},
	toDel : function(d) {
		assemblys.confirm("确定删除当前菜单吗？<br><font style='color:red;'>将同时删除对应的应用功能与功能点</font>", function() {
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			$.ajax({
				url : basePath + "frame/customFormType/deleteCustomFormTypeMenu.spring",
				type : "post",
				data : {
					"customFormTypeMenuCode" : d.customFormTypeMenuCode,
					"appCode" : param.get("appCode"),
				},
				dataType : "json",
				success : function(data) {
					assemblys.msg("删除成功", function() {
						hasSubmit = false;
						parent.customFormTypeList.getCustomFormTypeList();
						customFormMenuList.getCustomFormMenuList();
					});
				}
			});
		});
	},
	toGenerate : function(d) {
		assemblys.confirm("确定对当前菜单生成应用功能吗？", function() {
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			$.ajax({
				url : basePath + "frame/customFormType/savaCustomFormTypeMenuFun.spring",
				type : "post",
				data : {
					"customFormTypeMenuCode" : d.customFormTypeMenuCode,
					"appCode" : param.get("appCode"),
				},
				dataType : "json",
				success : function(data) {
					assemblys.msg("已生成应用功能", function() {
						hasSubmit = false;
						customFormMenuList.getCustomFormMenuList();
					});
				}
			});
		});
	},
	toUpdateMenu : function(d) {
		assemblys.confirm("确定刷新当前菜单对应的应用功能点吗？", function() {
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			$.ajax({
				url : basePath + "frame/customFormType/updateCustomFormTypeMenuUrl.spring",
				type : "post",
				data : {
					"customFormTypeMenuCode" : d.customFormTypeMenuCode,
					"appCode" : param.get("appCode"),
				},
				dataType : "json",
				success : function(data) {
					assemblys.msg("已重置URL", function() {
						hasSubmit = false;
						customFormMenuList.getCustomFormMenuList();
					});
				}
			});
		});
	}
}

$(function() {
	customFormMenuList.init();
});