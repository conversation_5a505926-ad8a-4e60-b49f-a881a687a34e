var customFormReportList = {
	// 入参
	paramCustomFormTypeCode : "",
	// 初始化
	init : function() {
		
		var paramCustomFormTypeCode = getQueryAll("customFormTypeCode") || "";
		if (paramCustomFormTypeCode) {
			customFormReportList.paramCustomFormTypeCode = paramCustomFormTypeCode.join(",");
			param.set("customFormTypeCode", customFormReportList.paramCustomFormTypeCode);
		}
		
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			customFormReportList.getCustomFormTypeList().then(function(data) {
				typeAndTable.loadType(customFormReportList.getTypeArray(data.customFormTypeList), "0", ".leftSum", customFormReportList.showList);
				return customFormReportList.getFormList();
			}).then(function() {
				customFormReportList.initLayui();
			});
		});
	},
	initLayui : function() {
		layui.form.on("select(compNo)", function(data) {
			customFormReportList.getCustomFormTypeList().then(function(data) {
				typeAndTable.loadType(customFormReportList.getTypeArray(data.customFormTypeList), "0", ".leftSum", customFormReportList.showList);
				customFormReportList.getFormList();
			});
		});
		layui.form.render();
	},
	getCustomFormTypeList : function() {
		
		return $.ajax({
			url : basePath + "frame/customFormType/getCustomFormTypeList.spring",
			data : {
				"appCode" : param.get("appCode"),
				"compNo" : param.get("compNo"),
				"customFormTypeCode" : param.get("customFormTypeCode")
			},
			success : function(data) {
				return data.customFormTypeList;
			}
		});
	},
	//获取表单列表
	getFormList : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomFormList.spring",
			data : param.__form(),
			success : function(data) {
				//渲染加载界面
				typeAndTable.loadTable(customFormReportList.getFromArray(data.customFormList), ".rightCon", customFormReportList.toFromView);
				return data.customFormList;
			}
		});
	},
	toFromView : function(customForm) {
		// 返回HTML形式 
		var url = basePath + "frame/customForm/customFormTemplate.html?" + param.__form() + "&customFormCode=" + customForm.customFormCode;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : false,
			scrollbar : false,
			closeBtn : 0,//右上角的×按钮
			area : [ '100%', '100%' ],
			content : url
		});
		
	},
	// 切换分类
	showList : function(customFormType) {
		
		//隐藏分类
		var customFormTypeCode = customFormType.customFormTypeCode || "All";
		$("li[customFormType=" + customFormTypeCode + "]").addClass('skin-btn-main').siblings("li").removeClass("skin-btn-main");
		
		// 如果是全部，默认给空
		if (customFormTypeCode == "All") {
			customFormType.customFormTypeCode = "";
		}
		
		// 写入并查询
		param.set("customFormTypeCode", customFormType.customFormTypeCode || customFormReportList.paramCustomFormTypeCode);
		customFormReportList.getFormList();
		
	},
	changeIcon : function(obj, hasFind) {
		
		// 互换值
		var dataValue = $(obj).attr("dataValue");
		$(obj).attr("dataValue", dataValue == "0" ? "1" : "0");
		
		var showTypeDom = dataValue == "0" ? ".leftSum" : ".showBigIconType";
		
		var callBack = dataValue == "0" ? customFormReportList.showList : customFormReportList.showCustomFormType;
		
		var dataName = $(obj).attr("dataName");
		var $span = $(obj).find("span:eq(0)");
		$(obj).attr("dataName", $span.html());
		$span.html(dataName);
		
		if (dataValue == "0") {
			param.set("customFormTypeCode", customFormReportList.paramCustomFormTypeCode);
		}
		
		var liIocn = dataValue == "0" ? "&#xe6a6;" : "&#xe6a7;";
		var $li = $(obj).find("li:eq(0)");
		$li.html(liIocn);
		
		// 写入全局
		param.checkIcon = dataValue;
		if (hasFind) {
			// 加载数据
			customFormReportList.getCustomFormTypeList().then(function(data) {
				typeAndTable.loadType(customFormReportList.getTypeArray(data.customFormTypeList), dataValue, showTypeDom, callBack);
				customFormReportList.getFormList();
			});
		}
	},
	showCustomFormType : function(customFormType) {
		param.set("customFormTypeCode", customFormType.customFormTypeCode || getQueryAll("customFormTypeCode").join());
		// 返回HTML形式 
		var url = basePath + "/frame/customForm/iconToReport.html?" + param.__form();
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : "表单",
			scrollbar : false,
			closeBtn : 1,//右上角的×按钮
			area : [ '100%', '100%' ],
			content : url
		});
	},
	getTypeArray : function(customFormTypeList) {
		if (customFormTypeList.length > 0) {
			for (var i = 0; i < customFormTypeList.length; i++) {
				customFormTypeList[i].typeName = customFormTypeList[i].customFormTypeName;
				customFormTypeList[i].typeCode = customFormTypeList[i].customFormTypeCode;
				customFormTypeList[i].typeIcon = customFormTypeList[i].customFormTypeIcon || null;
				customFormTypeList[i].typeIconType = customFormTypeList[i].customFormTypeIconType || null;
				
			}
		}
		return customFormTypeList;
	},
	getFromArray : function(customFormList) {
		if (customFormList.length > 0) {
			for (var i = 0; i < customFormList.length; i++) {
				customFormList[i].fromName = customFormList[i].customFormName;
				customFormList[i].fromCode = customFormList[i].customFormCode;
				customFormList[i].typeCode = customFormList[i].customFormTypeCode;
			}
		}
		return customFormList;
	}
};

//上报完成后回调
var saveCallback = {
	init : function(win) {
		assemblys.msg("提交成功！", function() {
			win.assemblys.closeWindow();
		});
	}
}

function getQueryString(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
	var r = window.location.search.substr(1).match(reg);
	if (r != null)
		return unescape(r[2]);
	return null;
}

function getQueryAll(name) {
	var reg = new RegExp('(^|&)' + name + "=([^&]*)", "g");
	var r = decodeURI(window.location.search.substr(1)).match(reg);
	if (r == null || r.length == 0)
		return null;
	var result = [];
	for (var i = 0; i < r.length; i++) {
		var value = r[i];
		result.push(value.split('=')[1])
	}
	return result;
}
