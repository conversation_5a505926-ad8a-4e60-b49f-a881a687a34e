var customFormTemplate = {
	boderStyle : {
		"border" : "2px double #FF0000",
		"border-radius" : "4px"
	},
	//#FF0000:红色     #5FB878
	noneBoderStyle : {
		"border" : "none",
		"border-radius" : ""
	},
	optionRelation : [],
	initButton : function(data) {
		var type = param.get("type") // 0预览,1新增,2编辑;
		
		if (type == 1 || type == 2) {
			$("#draftButton").removeClass("layui-hide");
		}
		
		var hasBack = param.get("hasBack") || "0";
		if (hasBack == "1") {
			$("#backButton").removeClass("layui-hide");
		}
		
		if (type == 2) {
			$("#draftButton").text("保存");
		}
		
		//hwx 2022-09-28 医师档案去除保存按钮
		var hasSave = param.get("hasSave") || "0";
		//hwx 2022-09-28 医师档案去除保存按钮
		if (hasSave == "1") {
			$("#draftButton").addClass("layui-hide");
		}
		//hwx 2022-09-28 医师档案显示提交按钮
		if (hasSave == "2") {
			$("#commitButton").removeClass("layui-hide");
		}
		
		if (type == 1) {
			$("#commitButton").removeClass("layui-hide");
		}
		
		if (type == 2 && param.get("hasRollbackStatus") == param.get("status") && data.approvalBelongFlowNode.approvalIndex == 0) {
			$("#commitButton").removeClass("layui-hide");
		}
		
		var hasClose = param.get("hasClose") || "0";
		if (hasClose == "1") {
			$("#closeButton").removeClass("layui-hide");
		}
		
	},
	initFormVerify : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			data : {
				"dictTypeCode" : "CUSTOMFORM_VERIFY",
				"appCode" : "APP"
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					for (var i = 0; i < data.dictList.length; i++) {
						customFormTemplate.formVerify.customRegexDictList[data.dictList[i].dictContent] = data.dictList[i];
					}
				}
			}
		});
	},
	formVerify : {
		customRegexDictList : {},
		customRegex : function(value, item) {
			var dict = customFormTemplate.formVerify.customRegexDictList[$(item).attr("customRegex")];
			if (dict && value != "") {
				var reg = new RegExp(dict.remark);
				if (!reg.test(value)) {
					// 展开选项卡,如果当前是隐藏的
					if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
						$(item).parents(".table_right").find(".table_right_title").click();
					}
					
					$(item).parents(".layui-form-content").css(customFormTemplate.boderStyle).focus();
					
					$(".bodys").stop(true, true).animate({
						scrollTop : ($(".bodys").scrollTop() + $(item).parents(".layui-form-content").offset().top) - 90
					}, 1000);
					
					setTimeout(function() {
						$(item).parents(".layui-form-content").css(customFormTemplate.noneBoderStyle);
					}, 3000);
					
					return "「" + $(item).parents(".layui-form-content").attr("customFieldName") + "」" + "请输入正确的" + dict.dictName + "";
				}
			}
		},
		// 长度校验
		limit : function(value, item) { // value：表单的值、item：表单的DOM对象
		
			/**
			 * 如果该模块已经隐藏，不做校验
			 */
			if ($(item).parents(".table_right").is(":hidden") || $(item).parents("[customFieldRowCode]").css("display") == "none") {
				return;
			}
			
			var limit = $(item).attr("limit");
			
			if (value.replace(/\n/g, "aaaa").replace(/\n/g, "aaaa").length > limit) {
				return "「" + $(item).parents(".layui-form-content").attr("customFieldName") + "」" + "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
			}
		},
		// 校验文本框，文本域
		required : function(value, item) {
			value = $.trim(value);
			/**
			 * 如果该模块已经隐藏，不做校验
			 */
			if ($(item).parents(".table_right").is(":hidden") || $(item).parents("[customFieldRowCode]").css("display") == "none") {
				return;
			}
			
			if (!value) {
				
				// 展开选项卡,如果当前是隐藏的
				if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
					$(item).parents(".table_right").find(".table_right_title").click();
				}
				
				$(item).parents(".layui-form-content").css(customFormTemplate.boderStyle).focus();
				
				$(".bodys").stop(true, true).animate({
					scrollTop : ($(".bodys").scrollTop() + $(item).parents(".layui-form-content").offset().top) - 90
				}, 1000);
				
				setTimeout(function() {
					$(item).parents(".layui-form-content").css(customFormTemplate.noneBoderStyle);
				}, 5000);
				
				var msg = "请选择";
				if (item.type == "text" || item.type == "textarea") {
					msg = "请填写";
				}
				return msg + "「" + $(item).parents(".layui-form-content").attr("customFieldName") + "」";
			}
			
		},
		// 校验单选多选
		checkboxRequired : function(value, item) { // value：表单的值、item：表单的DOM对象
		
			/**
			 * 如果该模块已经隐藏，不做校验
			 */
			if ($(item).parents(".table_right").is(":hidden") || $(item).parents("[customFieldRowCode]").css("display") == "none") {
				return;
			}
			
			var flag = false;
			if ($(item).find("input:checked").length > 0) {
				flag = true;
			}
			
			// 如果有未勾选
			if (!flag) {
				
				// 展开选项卡,如果当前是隐藏的
				if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
					$(item).parents(".table_right").find(".table_right_title").click();
				}
				
				$(item).css(customFormTemplate.boderStyle).focus();
				
				$(".bodys").stop(true, true).animate({
					scrollTop : ($(".bodys").scrollTop() + $(item).offset().top) - 90
				}, 1000);
				
				setTimeout(function() {
					$(item).css(customFormTemplate.noneBoderStyle);
				}, 5000);
				
				return "请选择「" + $(item).attr("customFieldName") + "」";
			}
		},
		// 校验单选多选的二级选项
		hasChildrenRequired : function(value, item) {
			
			/**
			 * 如果该模块已经隐藏，不做校验
			 */
			if ($(item).parents(".table_right").is(":hidden") || $(item).parents("[customFieldRowCode]").css("display") == "none") {
				return;
			}
			
			if ($(item).parent().next().length > 0 && $(item).prop("checked")) {
				
				var $second = $(item).parent().next();
				
				var flag = false;
				if ($second.find("input:checked").length > 0) {
					flag = true;
				}
				
				if (!flag) {
					
					// 展开选项卡,如果当前是隐藏的
					if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
						$(item).parents(".table_right").find(".table_right_title").click();
					}
					
					$second.css(customFormTemplate.boderStyle).focus();
					
					$(".bodys").stop(true, true).animate({
						scrollTop : ($(".bodys").scrollTop() + $second.offset().top) - 90
					}, 1000);
					
					setTimeout(function() {
						$second.css(customFormTemplate.noneBoderStyle);
					}, 5000);
					
					return "「" + $(item).parents(".layui-form-content").attr("customFieldName") + "」" + "请选择勾选二级";
				}
			}
			
		},
		// 校验单选+多选的文本框
		hasCheckboxTextRequired : function(value, item) {
			
			/**
			 * 如果该模块已经隐藏，不做校验
			 */
			if ($(item).parents(".table_right").is(":hidden") || $(item).parents("[customFieldRowCode]").css("display") == "none") {
				return;
			}
			
			value = $.trim(value);
			
			var hasType = "";
			if ($(item).parents(".layui-form-radio").length > 0) {
				hasType = "radio";
			}
			if ($(item).parents(".layui-form-checkbox").length > 0) {
				hasType = "checkbox";
			}
			
			// 不是单选多选带文本框的
			if (hasType == "") {
				return;
			} else {
				if (hasType == "radio") {
					if ($(item).parents(".layui-form-radio").hasClass("layui-form-radioed")) {
						if (!value) {
							// 展开选项卡,如果当前是隐藏的
							if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
								$(item).parents(".table_right").find(".table_right_title").click();
							}
							$(item).css(customFormTemplate.boderStyle).focus();
							$(".bodys").stop(true, true).animate({
								scrollTop : ($(".bodys").scrollTop() + $(item).offset().top) - 90
							}, 1000);
							setTimeout(function() {
								$(item).css(customFormTemplate.noneBoderStyle);
							}, 5000);
							return $(item).parents(".layui-form-content").attr("customFieldName") + "请输入单选框文本信息";
						}
					}
				}
				
				if (hasType == "checkbox") {
					if ($(item).parents(".layui-form-checkbox").hasClass("layui-form-checked")) {
						if (!value) {
							// 展开选项卡,如果当前是隐藏的
							if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
								$(item).parents(".table_right").find(".table_right_title").click();
							}
							$(item).css(customFormTemplate.boderStyle).focus();
							$(".bodys").stop(true, true).animate({
								scrollTop : ($(".bodys").scrollTop() + $(item).offset().top) - 90
							}, 1000);
							setTimeout(function() {
								$(item).css(customFormTemplate.noneBoderStyle);
							}, 5000);
							return "「" + $(item).parents(".layui-form-content").attr("customFieldName") + "」" + "请输入多选框文本信息";
						}
					}
				}
			}
			
		},
		// 校验下拉框
		selectRequired : function(value, item) { // value：表单的值、item：表单的DOM对象
			value = $.trim(value);
			/**
			 * 如果该模块已经隐藏，不做校验
			 */
			if ($(item).parents(".table_right").is(":hidden") || $(item).parents("[customFieldRowCode]").css("display") == "none") {
				return;
			}
			
			if (!value || value == '-1') {
				// 展开选项卡,如果当前是隐藏的
				if ($(item).parents(".table_right_main").hasClass("layui-hide")) {
					$(item).parents(".table_right").find(".table_right_title").click();
				}
				
				$(".bodys").stop(true, true).animate({
					scrollTop : ($(".bodys").scrollTop() + $(item).parents(".layui-form-content").offset().top) - 90
				}, 2000);
				
				return "请选择「" + $(item).parents(".layui-form-content").attr("customFieldName") + "」";
			}
		},
		// 校验附件
		fileRequired : function(value, item) {
			if ($(item).children().length == 0) {
				$(item).parent().css(customFormTemplate.boderStyle).focus();
				$(".bodys").stop(true, true).animate({
					scrollTop : ($(".bodys").scrollTop() + $(item).parent().offset().top) - 90
				}, 1000);
				setTimeout(function() {
					$(item).parent().css(customFormTemplate.noneBoderStyle);
				}, 5000);
				return "请上传附件";
			}
			
		}
	},
	initLayuiForm : function() {
		form.verify(customFormTemplate.formVerify);
		
		form.on("submit(save)", function(data) {
			customFormTemplate.saveCustomFormFilled(1);
			return false;
		});
	},
	saveCustomFormFilled : function(saveState) {
		
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		
		// 提交操作
		var submitFun = function() {
			// 保存前回调
			var saveBack = "";
			if (parent.saveBeforeCallback) {
				$.each(parent.saveBeforeCallback, function(name, value) {
					if (typeof value == "function") {
						// 父窗口、当前表单窗口、提交状态
						saveBack = value.call(parent.window, window, saveState);
					}
				});
			}
			// 如果有提示语
			if (saveBack && saveBack != "") {
				isSubmit = false;
				assemblys.msg(saveBack);
				return;
			}
			// 触发保存
			$.ajax({
				url : basePath + "frame/newCustomForm/saveCustomFormFilled.spring",
				dataType : "json",
				type : "post",
				data : $("#form1").serialize() + "&saveState=" + saveState,
				success : function(data) {
					data.saveState = saveState;
					// 保存后回调
					if (parent.saveCallback) {
						$.each(parent.saveCallback, function(name, value) {
							if (typeof value == "function") {
								// 父窗口、当前表单窗口、保存表单后的Data
								value.call(parent.window, window, data);
							}
						});
					}
				}
			});
		}
		// 是否提交
		if (saveState == 1) {
			assemblys.confirm("确认提交吗？", submitFun, function() {
				isSubmit = false;
			});
		} else {
			submitFun();
		}
	},
	// table的添加与事件绑定
	showOrHideRelation : function(dom) {// 级联
		var customOptionSetCode = dom.value;
		var customOptionSetCodeMap = customFormTemplate.customOptionSetCodeMap[customOptionSetCode];
		if ((dom.checked || dom.selected) && customOptionSetCode && customOptionSetCodeMap) {
			customFormTemplate.showRelation(dom, customOptionSetCodeMap);
		} else {
			customFormTemplate.hideRelation(dom, customOptionSetCodeMap);
		}
		if (window.showOrHideTimeout) {
			clearTimeout(window.showOrHideTimeout);
		}
		window.showOrHideTimeout = setTimeout(function() {
			$(":animated").finish();
			$("div[customModularCode],[lay-filter^='customFieldRow_']").each(function() {
				var $elem = $(this);
				var code = "";
				if ($elem.is("div")) {
					code = $elem.attr("customModularCode");
				} else {
					code = $elem.attr("customFieldRowCode");
				}
				
				if (!customFormTemplate.relationCodeMap[code]) {
					return true;
				}
				
				var relationNum = parseInt($elem.attr("relationNum") || 0);
				if (relationNum > 0) {
					if ($elem.is(":hidden")) {
						$elem.show(300);
						// 隐藏分类的内容显示时解除禁用
						$elem.find("input,textarea,select").removeAttr("disabled");
						
						var isTable = $elem.hasClass("table_right");
						if (isTable && $elem.children(".table_right_main").hasClass("layui-hide")) {
							$elem.children(".table_right_main").removeClass("layui-hide");
						}
					}
				} else {
					if ($elem.is(":visible")) {
						$elem.hide(300, function() {
							customFormTemplate.clearInput($elem);
							// 隐藏分类的内容禁用
							$elem.find("input,textarea,select").attr("disabled", "disabled");
						});
					}
				}
			});
		}, 100);
	},
	showRelation : function(dom, customOptionSetCodeMap) {
		var customOptionSetCode = dom.value;
		var name;
		if (dom.tagName == "OPTION") {
			name = $(dom).parent().prop("name");
		} else {
			name = dom.name;
		}
		var index = name.split("-")[1];
		
		for ( var i in customOptionSetCodeMap) {
			var relationCode = customOptionSetCodeMap[i];
			var $table_right = $("div[customModularCode='" + relationCode + "'],[lay-filter='customFieldRow_" + relationCode + "-" + index + "']");
			
			if ($table_right.length > 0) {
				// 2021.01.13 暂时不再让关联内容出现在选项下方
				/*if (isTable) {
					$(dom).parents("div.table_right").after($table_right[0]);
				} else {
					$(dom).parents("tr[customFieldRowCode]").after($table_right[0]);
				}*/
				var relationNum = parseInt($table_right.attr("relationNum"));
				relationNum++;
				$table_right.attr("relationNum", relationNum);
				
			}
			
		}
		
	},
	hideRelation : function(dom, customOptionSetCodeMap) {
		var customOptionSetCode = dom.value;
		var name;
		if (dom.tagName == "OPTION") {
			name = $(dom).parent().prop("name");
		} else {
			name = dom.name;
		}
		var index = name.split("-")[1];
		
		if (customOptionSetCodeMap) {
			for ( var i in customOptionSetCodeMap) {
				var relationCode = customOptionSetCodeMap[i];
				$("div[customModularCode='" + relationCode + "']:visible,[lay-filter='customFieldRow_" + relationCode + "-" + index + "']:visible").each(function() {
					var $elem = $(this);
					if ($elem.length > 0) {
						var relationNum = parseInt($elem.attr("relationNum"));
						relationNum--;
						$elem.attr("relationNum", relationNum);
					}
				});
			}
		}
	},
	clearInput : function(dom) {
		// 文本
		$(dom).find("input:text,textarea").val("");
		// 清空富文本
		$(dom).find("textarea").each(function() {
			var id = $(this).prev().prop("id");
			if (id && pubUploader["__" + id]) {
				pubUploader["__" + id].setContent("");
			}
		});
		// 单选多选
		$(dom).find("input:radio,input:checkbox").prop("checked", false).each(function() {
			customFormTemplate.onChecked($(this));
			customFormTemplate.showOrHideRelation(this);
		});
		// 下拉
		$(dom).find("select").each(function() {
			var $option = $(this).children(":eq(0)");
			$option[0].selected = true;
			customFormTemplate.selectOnSelected($(this));
		});
	},
	previewShow : function(url) {
		var html = "<div id='zoom' style='overflow-y:auto;'><a class='close' onclick='$(\'#zoom\').fadeOut();'></a><img class='content' src='" + url + "'/></div>";
		var body = $("body");
		body.append(html);
		var img = new Image();
		img.src = url;
		img.onload = function() {
			var w = body.width();
			var imgW = img.width;
			if (imgW - w * 0.8 > 0) {
				imgW = w * 0.8;
			}
			var fbW = (w - imgW) * 0.5;
			var $ct = $("#zoom .content");
			$ct.attr("src", url);
			$ct.css("width", imgW + "px");
			$ct.css("margin-left", fbW + "px");
			$ct.css("margin-right", fbW + "px");
			$("#zoom .close").css("left", fbW + imgW + "px");
			$("#zoom").fadeIn();
		};
		
	},
	openOrRetract : function(obj) {
		if (obj.num == 1) {
			var $siblings = $(".table_box").find(".table_right").find(".table_right_title");
			$siblings.nextAll().removeClass("layui-hide");
			$siblings.children("i").html("&#xe920;");
			
			$(obj).text("全部收起");
			obj.num = 0;
		} else {
			var $siblings = $(".table_box").find(".table_right").find(".table_right_title");
			$siblings.nextAll().addClass("layui-hide");
			$siblings.children("i").html("&#xe922;");
			
			$(obj).text("全部展开");
			obj.num = 1;
		}
	},
	// 审核模板编辑
	eidtCustomTextareaTemplate : function(customTextareaTemplateID, customFieldCode) {
		var url = basePath + "frame/customForm/eidtCustomTextareaTemplate.jsp?customTextareaTemplateID=" + customTextareaTemplateID + "&customFieldCode=" + customFieldCode + "&compNo=" + compNo + "&appCode=" + appCode;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '编辑文本框模板',
			scrollbar : false,
			area : [ '460px', '370px' ],
			content : url,
			end : function() {
				customFormTemplate.getCustomTextareaTemplateList(customFieldCode);
			}
		});
	},
	deleteCustomTextareaTemplate : function(customTextareaTemplateID, dom) {
		assemblys.confirm("确认删除该模板？", function() {
			var url = basePath + "frame/newCustomForm/deleteCustomTextareaTemplate.spring";
			$.ajax({
				url : url,
				dataType : "json",
				data : {
					"customTextareaTemplateID" : customTextareaTemplateID,
					"appCode" : appCode
				},
				type : "post",
				success : function(data) {
					$(dom).remove();
					assemblys.msg("删除成功");
				}
			});
		});
	},
	showTextareaTemplate : function(o, customFieldCode) {
		$(".item_label_content_template").addClass("layui-hide");
		$(o).next().removeClass("layui-hide");
		customFormTemplate.getCustomTextareaTemplateList(customFieldCode);
	},
	getCustomTextareaTemplateList : function(customFieldCode) {
		var url = basePath + "frame/newCustomForm/getCustomTextareaTemplateList.spring?customFieldCode=" + customFieldCode + "&compNo=" + compNo + "&appCode=" + appCode;
		$.ajax({
			url : url,
			async : false,
			dataType : "json",
			success : function(data) {
				var $ul = $("div[customFieldCode='" + customFieldCode + "']").find(".item_label_content_template").find(".win_ul");
				$ul.children().remove();
				$.each(data.customTextareaTemplateList, function(i, customTextareaTemplate) {
					$ul.each(function(j, e) {
						var li = "<li class=\"win_li\">";
						li += "	<span class=\"win_text\">" + (i + 1) + "、" + customTextareaTemplate.customTextareaTemplateTitle + "</span>	";
						if (customTextareaTemplate.createUserCode == data.user.uID) {
							li += "	<span class=\"win_edit\"><i class=\"layui-icon2\">&#xe628;</i></span>	";
							li += "	<span class=\"win_del\"><img title=\"删除模板\"  src=\"" + basePath + "/frame/images/del.png\" style=\"position: absolute; left: 2px; cursor: pointer; font-size: 14px;\" /></span>	";
							
						}
						li += "</li>";
						$(this).append(li);
						$(this).children("li:last").children(".win_text").click(function() {
							$(this).parents(".layui-form-label").next().find("textarea").val(customTextareaTemplate.customTextareaTemplateContent);
						});
						$(this).children("li:last").children(".win_edit").click(function() {
							customFormTemplate.eidtCustomTextareaTemplate(customTextareaTemplate.customTextareaTemplateID, customFieldCode);
						});
						$(this).children("li:last").children(".win_del").click(function() {
							customFormTemplate.deleteCustomTextareaTemplate(customTextareaTemplate.customTextareaTemplateID, $(e).children(":last"));
						});
					})
				});
			}
		});
	},
	getOptionRelation : function() {
		var url = basePath + "frame/newCustomForm/getCustomRelatedList.spring";
		return $.ajax({
			url : url,
			dataType : "json",
			data : {
				"customFormCode" : customFormCode,
				"customFormBusinessCode" : customFormBusinessCode,
				"compNo" : compNo,
				"appCode" : appCode
			},
			success : function(data) {
				customFormTemplate.relationCodeMap = data.relationCodeMap;
				customFormTemplate.customOptionSetCodeMap = data.customOptionSetCodeMap;
			}
		});
	},
	hasOptionRelation : function(relationCode) {
		return customFormTemplate.relationCodeMap[relationCode];
	},
	hasRelationModular : function(customOptionSetCode, value, index) {
		var customOptionSetCodeMap = customFormTemplate.customOptionSetCodeMap[customOptionSetCode];
		for ( var i in customOptionSetCodeMap) {
			var relationCode = customOptionSetCodeMap[i];
			var $elem = $("div[customModularCode='" + relationCode + "']:visible,[lay-filter='customFieldRow_" + relationCode + "-" + index + "']:visible");
			if ($elem.attr("relationNum") == 1) {
				if (value) {
					var relationCodeMap = customFormTemplate.relationCodeMap[$elem.attr("customModularCode") || $elem.attr("customFieldRowCode")];
					for ( var j in relationCodeMap) {
						if (relationCodeMap[j] == value) {
							return false;
						}
					}
				}
				return true;
			}
		}
		return false;
	},
	onChecked : function($input) {
		
		var level = $input.attr("level");
		if ($input[0].type == "checkbox") {
			if ($input[0].checked) {
				$input.next().addClass("layui-form-checked");
			} else {
				$input.next().removeClass("layui-form-checked");
				$input.next().find("input:text").val("");
			}
			
			if (level == "1") {
				if ($input[0].checked) {
					$input.parent().next().addClass("second_show");
				} else {
					$input.parent().next().removeClass("second_show");
				}
			}
		} else {
			if ($input[0].checked) {
				$("input[name='" + $input[0].name + "']").prop("checked", false).next().removeClass("layui-form-radioed").children("i").removeClass("layui-icon-radio").addClass("layui-icon-circle");
				$input[0].checked = true;
				$input.next().addClass("layui-form-radioed").children("i").removeClass("layui-icon-circle").addClass("layui-icon-radio");
				
				if (level == "1") {
					$input.parents(".layui-hove").siblings().find(".second").removeClass("second_show");
					$input.parent().next().addClass("second_show");
				}
			} else {
				$input.next().removeClass("layui-form-radioed").children("i").removeClass("layui-icon-radio").addClass("layui-icon-circle");
				$input.parent().next().removeClass("second_show");
			}
			
		}
		
	},
	radioOnClick : function($input) {
		
		if ($input.next().hasClass("layui-disabled")) {
			return;
		}
		
		var e = window.event;
		var target = e.target || e.srcElement;
		// 判断点击的是否是输入框,如果是,则不取消选中
		var elem = $input[0];
		if (target.type == "text" && elem.checked) {
			return;
		}
		
		elem.checked = !elem.checked;
		customFormTemplate.onChecked($input);
		
		var level = $input.attr("level");
		var index = elem.name.split("-")[1];
		var name = elem.name;
		var value = elem.value;
		var $div = $input.parents("div[customFieldCode]");
		var customFieldCode = $div.attr("customFieldCode");
		var flag = true;
		var that = null;
		
		$("input[name='" + name + "'][radio-checked=true]").each(function(i, e) {
			that = this;
			if (customFormTemplate.hasRelationModular(this.value, elem.value, index)) {
				if ($input.next().is(":hidden")) {
					customFormTemplate.showOrHideRelation(that);
					customFormTemplate.radioOnClickRelationFun($input, $div);
				} else {
					assemblys.confirm("取消『" + $(this).next().find(".item_label_radio_field").text() + "』选项会把它所关联的选项内容清空，确定要取消吗？", function() {
						customFormTemplate.showOrHideRelation(that);
						customFormTemplate.radioOnClickRelationFun($input, $div);
					}, function() {
						e.checked = true;
						customFormTemplate.onChecked($(e));
						$input[0].checked = false;
						customFormTemplate.onChecked($input);
					});
				}
				flag = false;
				return false;
			}
		});
		
		if (flag) {
			customFormTemplate.radioOnClickRelationFun($input, $div);
			if (that && that != $input[0]) {// 之前有被选中的,需要调用关联方法让relationNum减1
				customFormTemplate.showOrHideRelation(that);
			}
		}
		
	},
	radioOnClickRelationFun : function($input, $div) {
		var elem = $input[0];
		var level = $input.attr("level");
		var value = elem.value;
		var index = elem.name.split("-")[1];
		var name = elem.name;
		
		customFormTemplate.showOrHideRelation(elem);
		if (level == "1") {
			$div.find("input[level='2']:checked").not("input[name='" + value + "-" + index + "']").prop("checked", false).each(function(i, e) {
				customFormTemplate.onChecked($(this));
				$(this).removeAttr("radio-checked");
			});
		}
		
		/*else {
			elem.checked = true;
			var $parent = $input.parents("div.second").prev().find("input[value='" + name.split("-")[0] + "']").prop("checked", true);
			if ($parent.prop("type") == "radio") {
				$parent.next().click();
			} else {
				customFormTemplate.showOrHideRelation($parent[0]);
				customFormTemplate.onChecked($parent);
			}
		}*/

		var $clearTextDiv = $input.parent().parent();
		if (level == "1") {
			$clearTextDiv = $clearTextDiv.parent();
		}
		$clearTextDiv.find("input:text").val("");
		
		// 用于获取当前选项被点击之前的选项
		$("input[name='" + name + "']").not(":checked").removeAttr("radio-checked");
		if (elem.checked) {
			$input.attr("radio-checked", "true");
		}
		
	},
	checkboxOnClick : function($input) {
		
		if ($input.next().hasClass("layui-disabled")) {
			return;
		}
		
		var elem = $input[0];
		
		var e = window.event;
		var target = e.target || e.srcElement;
		
		// 判断点击的是否是输入框,如果是,则不取消选中
		if (target.type == "text" && elem.checked) {
			return;
		}
		
		elem.checked = !elem.checked;
		customFormTemplate.onChecked($input);
		
		var level = $input.attr("level");
		var index = elem.name.split("-")[1];
		var name = elem.name;
		var value = elem.value;
		var $div = $input.parents("div[customFieldCode]");
		var customFieldCode = $div.attr("customFieldCode");
		
		// 处理谷歌下选择文本取消多选框选中的文本
		var t = $(elem).next().find("input:text");
		if (t.length > 0 && target.type != "text") {
			var text = customFormTemplate.getSelectText(t[0]);
			if (text) {
				$input.next().click();
				return;
			}
		}
		
		var relationFun = function() {
			customFormTemplate.showOrHideRelation(elem);
			
			if (level == "2") {// 当前选中二级,自动把一级选上
				var $parentCheckbox = $input.parents("div.second").prev().find("input[value='" + name.split("-")[0] + "']");
				if (elem.checked && !$parentCheckbox.prop("checked")) {
					if ($parentCheckbox.prop("type") == "radio") {
						$parentCheckbox.next().click();
					} else {
						$parentCheckbox.prop("checked", true);
						customFormTemplate.showOrHideRelation($parentCheckbox[0]);
						customFormTemplate.onChecked($parentCheckbox);
					}
				}
				
			} else {
				
				if (!elem.checked) {// 当取消一级选中,自动把所有二级取消选中
					$("div[customFieldCode='" + customFieldCode + "']").find("input[name='" + value + "-" + index + "']").prop("checked", false).removeAttr("radio-checked").each(function(i, e) {
						customFormTemplate.showOrHideRelation(this);
						customFormTemplate.onChecked($(this));
					});
				}
			}
			if (!elem.checked) {
				$(elem).parent().next("div.second").find("input:text").val("");
			}
		};
		
		var flag = true;
		
		if (!elem.checked) {
			if (customFormTemplate.hasRelationModular(value, null, index)) {
				if ($input.next().is(":hidden")) {
					relationFun();
				} else {
					assemblys.confirm("取消『" + $(elem).next().find("label.item_label_checkbox_field").text() + "』选项会把它所关联的选项内容清空，确定要取消吗？", function() {
						relationFun();
					}, function() {
						elem.checked = true;
						customFormTemplate.onChecked($input);
					});
				}
				flag = false;
			}
		}
		
		if (flag) {
			relationFun();
		}
		
	},
	calculatedScore : function($input) {
		var custommodularcode = $input.closest("div[custommodularcode]").attr("custommodularcode");
		var sum = 0;
		$('div[custommodularcode="' + custommodularcode + '"]').find("input:checked").each(function() {
			var numberValue = $(this).attr("customoptionsetbusinessvalue");
			sum = sum + Number(numberValue);
		});
		if (initCustomFormTemplate.numberModular.length > 0) {
			for (var i = 0; i < initCustomFormTemplate.numberModular.length; i++) {
				var codes = initCustomFormTemplate.numberModular[i].split("_");
				if (custommodularcode == codes[0]) {
					$('input[name="' + codes[1] + '-0"]').val(sum);
				}
			}
		}
	},
	selectOnChange : function(dom, $select) {
		$select.val($(dom).attr("lay-value")).change();
		customFormTemplate.selectOnSelected($select);
	},
	selectOnSelected : function($select) {
		var elem = $select[0];
		var index = elem.name.split("-")[1];
		var value = $select.val() || "";
		var $dl = $select.next().children("dl");
		var $dd = $dl.children("dd[lay-value='" + value + "']");
		if (!value && $dd.length == 0) {
			$dd = $dl.children().eq(0);
		}
		var $beforeSelected = $dl.children(".layui-this");
		var beforeSelectedText = $beforeSelected.text();
		var beforeSelectedValue = $beforeSelected.attr("lay-value");
		var childSelected = [ beforeSelectedValue ];
		// 把下级选中的关联都取消
		$select.parent().nextAll().find("option:selected").each(function(i, e) {
			childSelected.push(this.value);
		});
		
		var flag = true;
		for ( var i in childSelected) {
			if (customFormTemplate.hasRelationModular(childSelected[i], value, index)) {
				if ($select.next().is(":hidden")) {
					customFormTemplate.selectOnSelectedRelationFun($select, $dl, $dd, elem, value, index);
				} else {
					assemblys.confirm("取消『" + beforeSelectedText + "』选项会把它及它的子级选项所关联的选项内容清空，确定要取消吗？", function() {
						customFormTemplate.selectOnSelectedRelationFun($select, $dl, $dd, elem, value, index);
					});
				}
				flag = false;
				break;
			}
		}
		
		if (flag) {
			return customFormTemplate.selectOnSelectedRelationFun($select, $dl, $dd, elem, value, index);
		}
		
	},
	selectOnSelectedRelationFun : function($select, $dl, $dd, elem, value, index) {
		// 关联关系处理
		// 先隐藏
		var $beforeSelectedOption = $select.children("option[option-selected=true]");
		if ($beforeSelectedOption.length > 0) {
			customFormTemplate.showOrHideRelation($beforeSelectedOption[0]);
			$beforeSelectedOption.removeAttr("option-selected");
		}
		// 再显示
		var $afterSelectedOption = $select.children("option:selected");
		customFormTemplate.showOrHideRelation($afterSelectedOption[0]);
		$afterSelectedOption.attr("option-selected", "true");
		
		// 把下级选中的关联都取消
		$select.parent().nextAll().find("option:selected").each(function(i, e) {
			this.selected = false;
			customFormTemplate.showOrHideRelation(this);
		});
		
		var customField = elem.customField;
		var customModular = $(elem).parents("div.table_right")[0].customModular;
		$dl.prev().children("input").val($dd.eq(0).text());
		$dd.addClass("layui-this").siblings().removeClass("layui-this");
		
		$select.parent().nextAll().remove();
		if (!value) {
			return;
		}
		var selectHove = initCustomFormTemplate.getSelectHove(customModular, customField, index, value);
		if (selectHove.children[0].children.length > 1) {
			var selectParent = $select.parent().parent()[0];
			assemblys.createElement(selectHove, selectParent);
			return selectParent.children[selectParent.children.length - 1].children[0];
		}
	},
	getSelectText : function(t) {// 获取文本框选择的文本,为了防止死循环,使用selected变量判断是否已经返回选中值,为true则返回空串并重置selected
		if (t.selected) {
			t.selected = false;
			return "";
		}
		var text = "";
		if (window.getSelection) {
			if (t.selectionStart != undefined && t.selectionEnd != undefined) {
				text = t.value.substring(t.selectionStart, t.selectionEnd);
			}
		} else {
			text = document.selection.createRange().text;
		}
		t.selected = true;
		return text;
	},
	preview : function() { // 预览
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "layer-preview",
			title : '预览',
			scrollbar : false,
			area : [ '900px', '500px' ],
			content : basePath + "aers/customEventForm/previewTemplate.jsp"
		});
	},
	getInterfaceData : function(dom) {
		var customField = $(dom).parents("[fieldset='interface']")[0].customField;
		var openTitle = $(dom).parent().attr("fieldcontent");
		var $table_right = $(dom).parents("div.table_right");
		var index = $(dom).prop("name").split("-")[1];
		var interfaceItem = customField.fieldData;
		var values = {};
		
		customFormTemplate.interfaceParam = customField.interfaceParam;
		customFormTemplate.interfaceText = $(dom).parent().find("input").val();
		customFormTemplate.interfaceCode = customField.fieldVerifyType;
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : openTitle,
			scrollbar : false,
			area : [ '900px', '500px' ],
			content : "interfaceDataList.html",
			end : function() {
				if (customFormTemplate.interfaceDataIndex != -1) {
					$.each(customFormTemplate.interfaceData[customFormTemplate.interfaceDataIndex], function(name, value) {
						values[interfaceItem[name] + "-" + index] = value;
					});
					var filterValues = {};
					for ( var k in values) {
						var valueTemp = values[k];
						var fieldcomment = $("input[name='" + k + "']").attr("fieldcomment");
						if ($("input[name='" + k + "']").length > 1) {
							valueTemp = $("input[name='" + k + "'][optionremark='" + valueTemp + "']").attr("value");
						} else if ($("select[name='" + k + "']").length > 0) {
							valueTemp = $("select[name='" + k + "'] [optionremark='" + valueTemp + "']").attr("value");
						}
						if (valueTemp) {
							// 如果存在，要转格式
							if (fieldcomment) {
								if (fieldcomment == "date") {
									valueTemp = valueTemp.substring(0, 10);
								}
								if (fieldcomment == "datetime") {
									valueTemp = valueTemp.substring(0, 16);
								}
							}
							filterValues[k] = valueTemp;
						}
					}
					initCustomFormTemplate.setValue(null, filterValues, $table_right);
				}
			}
		});
	},
	getInterfaceDataSon : function(dom, interfaceData) {
		var $table_right = $(dom).parents("div.table_right");
		var values = {};
		$.each(interfaceData, function(name, value) {
			values[interfaceItem[name] + "-" + index] = value;
		});
		initCustomFormTemplate.setValue(null, values, $table_right);
	},
	getCustomFormFilled : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomFormFilled.spring",
			data : {
				"customFormFilledCode" : customFormFilledCode,
				"appCode" : appCode
			},
			dataType : "json",
			success : function(data) {
				param.set(null, data.customFormFilled);
				param.set("hasRollbackStatus", data.hasRollbackStatus);
			}
		});
	},
	// 文件回调
	afterUpfile : function(list) {
		pubUploader.setFileList(list, "#ueditorFileDiv");
	},
	// 获取附件
	getAttachments : function() {
		if (customFormFilledCode) {
			return $.ajax({
				url : basePath + "frame/fileUpload/getAttachments.spring",
				data : {
					"belongToCode" : customFormFilledCode
				},
				dataType : "json",
				skipDataCheck : true,
				success : function(data) {
					// 底部附件
					var list = [];
					for ( var i in data.attachmentsList) {
						var atta = data.attachmentsList[i];
						list.push({
							title : atta.attachmentName,
							url : atta.attachmentURL,
							size : atta.attachmentSize,
							type : atta.attachmentType
						});
					}
					pubUploader.setFileList(list, "#ueditorFileDiv");
				}
			});
		}
	},
	/**
	 * 获取组件附件
	 */
	getFileAttachments : function() {
		if (customFormFilledCode) {
			return $.ajax({
				url : basePath + "frame/fileUpload/getAttachments.spring",
				data : {
					"belongToCode" : customFormFilledCode,
					"belongToSubCode" : "all"
				},
				dataType : "json",
				skipDataCheck : true,
				success : function(data) {
					var fieldList = data.attachmentsList;
					// 回显组件附件
					for (var i = 0; i < fieldList.length; i++) {
						var file = fieldList[i];
						customFormTemplate.setFileList([ file ], file.belongToSubCode);
					}
				}
			});
		}
		
	},
	orgCallback : function(result) {
		$(window.orgSelectDom).prev().val(result.value + "/" + result.name);
		window.orgSelectDom.value = result.name;
	},
	orgSelect : function(funCode, type, dom) {
		window.orgSelectDom = dom;
		layer.open({
			type : 2,
			title : "选择组织架构",
			scrollbar : false,
			area : [ '800px', '95%' ],
			// 带条件
			content : basePath + "plugins/udSelector/selectUserPage.jsp?type=" + type + "&callback=customFormTemplate.orgCallback&model=radio&" + (funCode ? "funCode=" + funCode : "&compNo=" + compNo)
		});
	},
	// 富文本下标
	richTextIndex : 0,
	// 加载富文本
	loadRichText : function() {
		var toolbars = [ [ 'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'removeformat', 'formatmatch', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|', 'rowspacingtop', 'rowspacingbottom', 'lineheight', '|', 'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'insertimage', '|', 'horizontal', 'date', 'time', '|', 'customstyle', 'paragraph', 'fontfamily', 'fontsize' ] ];
		// 富文本渲染
		$("textarea[isRichText='true']").each(function() {
			var id = "richText" + customFormTemplate.richTextIndex;
			$(this).attr({
				"id" : id,
				"isRichText" : "false"
			});
			pubUploader.initEditor(id, true, toolbars);
			customFormTemplate.richTextIndex++;
		});
	},
	//hwx 2022-4-26增加定时器保存
	initSaveTimer : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			data : {
				"dictTypeCode" : "Switch",
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					if (data.dictList) {
						for (var i = 0; i < data.dictList.length; i++) {
							var dictList = data.dictList[i];
							if (dictList.dictCode == "FormSaveTimer") {
								$("#showSaveTimer").removeClass("layui-hide");
								SysSecond = 60 * (Number(dictList.dictContent) || 3);//如果没有设置时间。默认3分钟
								InterValObj = window.setInterval(customFormTemplate.setRemainTime, 1000);
							}
						}
					}
					
				}
			}
		});
	},
	//hwx 2022-4-26增加定时器保存
	setRemainTime : function() {
		if (SysSecond > 0) {
			SysSecond = SysSecond - 1;
			var second = Math.floor(SysSecond % 60);
			var minite = Math.floor((SysSecond / 60) % 60);
			$("#showSaveTimer").html("<font color='red' size='5'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + minite + "</font>&nbsp;分&nbsp;" + "<font color='red' size='5'>" + second + "</font>&nbsp;秒&nbsp;后自动保存");
		} else {
			window.clearInterval(InterValObj);
			//到计时完后保存草稿
			customFormTemplate.saveCustomFormFilled(0);
		}
	},
	initFileUpload : function() {
		$(".custom_file[hasLoad='0']").each(function() {
			$(this).attr("hasLoad", "1");
			var id = $(this).attr("id");
			// 文件
			pubUploader.initCustomUpload(id, {
				type : "file",
			// 限制格式和数量
			// fileAllow : [ ".png", ".jpg", ".xml" ],
			// fileAllowNum : 1
			}, function(list) {
				customFormTemplate.setFileList(list, id);
			});
		});
	},
	// 回显文件列表
	setFileList : function(result, code) {
		var fileHtml = "";
		for (var i = 0; i < result.length; i++) {
			var title = result[i].title || result[i].attaName;
			var url = result[i].url || result[i].attaUrl;
			var size = result[i].size || result[i].attaSize;
			var type = result[i].type || result[i].attaType;
			fileHtml += "<li>";
			fileHtml += "	<em title=\"" + title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + title + "&nbsp;&nbsp;" + size + "</em>";
			var suffix = type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "<a style='margin-left:5px;cursor: pointer;color:blue;'  class=\"cattachqueue-remove\"  onclick=\"pubUploader.preview('" + title + "','" + url + "');\"  >预览图片</a>";
			}
			fileHtml += "	<a style='margin-left:5px;cursor: pointer;color:blue;'  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + title + "','" + url + "');\">下载</a>";
			fileHtml += "	<a style='margin-left:5px;cursor: pointer;color:blue;'  class=\"cattachqueue-remove\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a>";
			fileHtml += "	<input type=\"hidden\" name=\"attaCode\"  value=\"" + code + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + type + "\"/>";
			fileHtml += "</li>";
		}
		$("#" + code + "_custom_file_div").append(fileHtml);
	},
	start : function() {
		assemblys.getMenuIcon("CUSTOM_FORM", false, $("#menuIcon")[0]);
		//hwx 2022-4-26增加定时器保存
		if (type == 1) {
			customFormTemplate.initSaveTimer();
		}
		customFormTemplate.initFormVerify();
		customFormTemplate.initLayuiForm();
		customFormTemplate.getCustomFormFilled().then(function(data) {
			customFormTemplate.initButton(data);
		});
		customFormTemplate.getOptionRelation().then(function() {
			return initCustomFormTemplate.getCustomFormData();
		}).then(function() {
			// 加载文件上传
			customFormTemplate.initFileUpload();
			customFormTemplate.getAttachments();
			customFormTemplate.getFileAttachments();
			initCustomFormTemplate.events();
			
			if (customFormTemplate.customForm.hasFileUpload == 1) {
				$("#table_box_upload").removeClass("layui-hide");
			}
			if (parent.initObj) {
				$.each(parent.initObj, function(name, value) {
					if (typeof value == "function") {
						value.call(parent.window, window);
					}
				});
			}
		}).then(function() {
			$("div.table_right:hidden").find("input,textarea,select").attr("disabled", "disabled");
		});
	}
};
