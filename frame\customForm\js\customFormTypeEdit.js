var customFormTypeEdit = {
	// 表单状态
	formTypeStateList : [],
	menuIndex : 0,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			$("span[titleName]").text("自定义表单分类");
			return customFormTypeEdit.getExceAppList();
		}).then(function() {
			return customFormTypeEdit.getFormTypeState();
		}).then(function() {
			var customFormTypeID = param.get("customFormTypeID");
			if (customFormTypeID == 0) {
				customFormTypeEdit.addNoneItem("#itemsStateView");
				customFormTypeEdit.addNoneItem("#itemsMenuView");
				customFormTypeEdit.initLayui();
			} else {
				customFormTypeEdit.getCustomFormType(customFormTypeID);
			}
		});
	},
	getFormTypeState : function() {
		return $.ajax({
			url : basePath + "frame/customFormType/getFormTypeState.spring",
			type : "get",
			dataType : "json",
			success : function(data) {
				customFormTypeEdit.formTypeStateList = data.formTypeStateList;
			}
		});
	},
	getCustomFormType : function(customFormTypeID) {
		$.ajax({
			url : basePath + "frame/customFormType/getCustomFormType.spring",
			type : "get",
			data : {
				"appCode" : param.get("appCode"),
				"customFormTypeID" : customFormTypeID
			},
			dataType : "json",
			success : function(data) {
				// 回显基础数据
				param.set(null, data.customFormType);
				// 状态
				var customFormTypeStateList = data.customFormTypeStateList;
				for (var i = 0; i < customFormTypeStateList.length; i++) {
					customFormTypeEdit.addStateItem(customFormTypeStateList[i]);
				}
				// 菜单
				var customFormTypeMenuList = data.customFormTypeMenuList;
				for (var i = 0; i < customFormTypeMenuList.length; i++) {
					customFormTypeEdit.addMenuItem(customFormTypeMenuList[i]);
				}
				if (customFormTypeStateList.length == 0) {
					customFormTypeEdit.addNoneItem("#itemsStateView");
				}
				if (customFormTypeMenuList.length == 0) {
					customFormTypeEdit.addNoneItem("#itemsMenuView");
				}
				
				// 图标风格
				$("#customFormTypeIcon").next().html(data.customFormType.customFormTypeIcon).addClass("layui-icon" + data.customFormType.customFormTypeIconType);
				
				customFormTypeEdit.initLayui();
			}
		});
	},
	getExceAppList : function() {
		var appCode = param.get("appCode");
		return $.ajax({
			url : basePath + "frame/customFormType/getExceAppList.spring",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var appList = data.appList;
				var $select = $("select[appCode]");
				var html = "";
				for (var i = 0; i < appList.length; i++) {
					if (appList[i].compAppID != 0) {
						html += '<option value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
					}
				}
				$select.append(html);
			}
		});
	},
	addStateItem : function(data) {
		var id = "#itemsStateView";
		if (!data) {
			data = {
				customFormTypeStateCode : "",
				customFormTypeStateNo : "",
				customFormTypeStateName : "",
				customFormTypeStateColor : ""
			};
		}
		var tr = '';
		tr += '<tr>';
		tr += '		<td class="comTab_Td" style="text-align: center">';
		tr += '			<i class="layui-icon layui-icon-delete i_icon" onclick="customFormTypeEdit.removeItem(this, 1);" title="删除"></i>';
		tr += '			<input type="hidden" name="customFormTypeStateCode" value = "' + data.customFormTypeStateCode + '" />';
		tr += '		</td>';
		tr += '		<td class="comTab_Td">';
		tr += '			<input type="text" name="customFormTypeStateNo" ' + (data.customFormTypeStateCode ? "readonly" : "") + ' value="' + data.customFormTypeStateNo
				+ '" maxLength="5" lay-verify="required|num"  onblur="customFormTypeEdit.checkName(this);"   class="layui-input lay-readonly" autocomplete="off">';
		tr += '		</td>';
		tr += '			<td class="comTab_Td">';
		tr += '			<input type="text" name="customFormTypeStateName" value="' + data.customFormTypeStateName + '"  maxLength="100" lay-verify="required"  onblur="customFormTypeEdit.checkName(this);"  class="layui-input" autocomplete="off">';
		tr += '		</td>';
		tr += '			<td class="comTab_Td">';
		tr += '			<input type="text" name="customFormTypeStateColor" value="' + data.customFormTypeStateColor + '" class="layui-input backColor" autocomplete="off">';
		tr += '		</td>';
		tr += '		</td>';
		tr += '			<td class="comTab_Td">';
		tr += '			<select name="customFormTypeStateStatus"  lay-filter="customFormTypeStateStatus" >';
		tr += '				<option value="" >无</option>   ';
		var formTypeStateList = customFormTypeEdit.formTypeStateList || [];
		for (var i = 0; i < formTypeStateList.length; i++) {
			var state = formTypeStateList[i];
			tr += '			<option value="' + state.code + '"  ' + (data.customFormTypeStateStatus == state.code ? "selected" : "") + ' >' + state.name + '</option>   ';
		}
		tr += '			</select>';
		tr += '		</td>';
		tr += '		<td style="text-align: center;">';
		tr += '			<i class="layui-icon2  seqnoButton " style="color: blue" onclick="customFormTypeEdit.up(this);">&#xe683; </i>';
		tr += '			<i class="layui-icon2  seqnoButton " style="color: green" onclick="customFormTypeEdit.down(this);">&#xe680; </i>';
		tr += '		</td>';
		tr += '	</tr>';
		$(id).append(tr);
		
		// 加载颜色选择器
		$(".backColor").bigColorpicker();
		
		// 聚焦
		$("input[name='customFormTypeStateNo']:last").focus();
		
		// 渲染
		layui.form.render();
		
		customFormTypeEdit.removeNoneItem(id);
		
	},
	addMenuItem : function(data) {
		var id = "#itemsMenuView";
		if (!data) {
			data = {
				customFormTypeMenuCode : "",
				customFormTypeMenuNo : "",
				customFormTypeMenuName : "",
				customFormTypeMenuType : "0",
				menuStateList : []
			};
		}
		var setRadiChecked = function(_value, _checkValue) {
			if (_value == _checkValue || _value == "0") {
				return 'checked="checked"';
			}
		}

		var tr = '';
		tr += '<tr>';
		tr += '		<td class="comTab_Td" style="text-align: center">';
		tr += '			<i class="layui-icon layui-icon-delete i_icon" onclick="customFormTypeEdit.removeItem(this, 2);" title="删除"></i>';
		tr += '			<input type="hidden" name="customFormTypeMenuCode" value="' + data.customFormTypeMenuCode + '" />';
		tr += '		</td>';
		tr += '		<td class="comTab_Td">';
		tr += '			<input type="text"  name="customFormTypeMenuNo" ' + (data.customFormTypeMenuNo ? "readonly" : "") + '   value="' + data.customFormTypeMenuNo
				+ '" onblur="customFormTypeEdit.checkName(this);" lay-verify="required|specialCharacters" maxLength="100"  class="layui-input lay-readonly" autocomplete="off">';
		tr += '		</td>';
		tr += '		<td class="comTab_Td">';
		tr += '			<input type="text"  name="customFormTypeMenuName" value="' + data.customFormTypeMenuName + '" onblur="customFormTypeEdit.checkName(this);" lay-verify="required" maxLength="100" class="layui-input" autocomplete="off">';
		tr += '		</td>';
		tr += '		<td class="comTab_Td">';
		tr += '			<input type="radio" name="customFormTypeMenuType' + customFormTypeEdit.menuIndex + '"  lay-filter="customFormTypeMenuType" value="0" title="审批" ' + setRadiChecked(0, data.customFormTypeMenuType) + ' />';
		tr += '			<input type="radio" name="customFormTypeMenuType' + customFormTypeEdit.menuIndex + '"  lay-filter="customFormTypeMenuType" value="1" title="查阅" ' + setRadiChecked(1, data.customFormTypeMenuType) + ' />';
		tr += '			<input type="radio" name="customFormTypeMenuType' + customFormTypeEdit.menuIndex + '"  lay-filter="customFormTypeMenuType" value="2" title="我的" ' + setRadiChecked(2, data.customFormTypeMenuType) + ' />';
		tr += '			<input type="radio" name="customFormTypeMenuType' + customFormTypeEdit.menuIndex + '"  lay-filter="customFormTypeMenuType" value="3" title="上报" ' + setRadiChecked(3, data.customFormTypeMenuType) + ' />';
		tr += '			<input type="hidden" name="customFormTypeMenuType" value="' + data.customFormTypeMenuType + '" />';
		tr += '		</td>';
		tr += '		<td style="text-align: center;">';
		tr += '			<div class="selectContent" style="display:' + (data.customFormTypeMenuType == "3" ? "none" : "block") + '" >';
		tr += '				<select  name="customFormTypeMenuStateName" xm-select="customFormTypeMenuState' + customFormTypeEdit.menuIndex + '" >';
		tr += '				</select>';
		var values = new Array();
		var menuStateList = data.menuStateList;
		if (menuStateList.length > 0) {
			for (var j = 0; j < menuStateList.length; j++) {
				var menuState = menuStateList[j];
				values.push(menuState.customFormTypeStateNo);
			}
		}
		tr += '				<input type="hidden" name="customFormTypeMenuState" value = "' + values.join("|") + '" />';
		tr += '			</div>';
		tr += '		</td>';
		tr += '</tr>';
		$(id).append(tr);
		
		// 渲染
		layui.form.render();
		
		// 下标追加
		customFormTypeEdit.menuIndex++;
		
		// 刷新下拉框
		customFormTypeEdit.refreshSelect();
		
		// 聚焦
		$("input[name='customFormTypeMenuNo']:last").focus();
		
		customFormTypeEdit.removeNoneItem(id);
		
	},
	addNoneItem : function(id) {
		var tr = '';
		tr += '<tr class="noneTr">';
		tr += '		<td class="comTab_Td" style="text-align: center" colspan="' + (id == "#itemsStateView" ? 6 : 5) + '" >';
		tr += '		暂无数据';
		tr += '		</td>';
		tr += '	</tr>';
		$(id).append(tr);
	},
	removeNoneItem : function(id) {
		$(id).find(".noneTr").remove();
	},
	getStateList : function() {
		var list = new Array();
		$("#itemsStateView").children("tr").each(function() {
			var value = $(this).find("input[name='customFormTypeStateNo']").val();
			var name = $(this).find("input[name='customFormTypeStateName']").val();
			if (name && value) {
				list.push({
					"value" : value,
					"name" : name
				});
			}
		});
		return list;
	},
	refreshSelect : function() {
		var formSelects = layui.formSelects;
		// 动态写入
		var stateList = customFormTypeEdit.getStateList();
		
		// 针对多少个多选 下拉框进行刷新
		$("select[xm-select^='customFormTypeMenuState']").each(function() {
			var tag = $(this).attr("xm-select");
			var values = [];
			var obj = this;
			
			var $stateValue = $(obj).parent().find("input[name='customFormTypeMenuState']");
			
			// 如果渲染过 - 获取原来选择的内容
			if ($(obj).attr("_name")) {
				values = formSelects.value(tag, 'val');
			} else {
				// 回显
				var preValue = $stateValue.val();
				values = preValue.split("|")
			}
			// 清空
			$(obj).empty();
			// 写入项
			for (var i = 0; i < stateList.length; i++) {
				var state = stateList[i];
				$(obj).append('<option value="' + state.value + '">' + state.name + '</option>');
			}
			// 渲染
			formSelects.render(tag);
			// 回显
			formSelects.value(tag, values);
			// 隐藏域回显
			var setValue = function() {
				var valueTemp = formSelects.value(tag, 'val');
				$stateValue.val(valueTemp.join("|"));
			}
			setValue();
			// 监听切换
			formSelects.on(tag, function() {
				setTimeout(function() {
					setValue();
				}, 200);
			});
			
		});
	},
	removeItem : function(obj, num) {
		var noneCallback = function() {
			if ($(obj).parents("tbody").children().length == 1) {
				customFormTypeEdit.addNoneItem(num == 1 ? "#itemsStateView" : "#itemsMenuView");
			}
			$(obj).parents("tr").remove();
		}
		// 删除
		if (num == 1) {
			assemblys.confirm("确定删除当前状态吗？<br><font style='color:red;'>同时会解除菜单的关联关系</font>", function() {
				noneCallback();
				// 渲染下拉框
				customFormTypeEdit.refreshSelect();
			});
		} else {
			// 数据库对象
			var customFormTypeMenuCode = $(obj).next().val();
			if (customFormTypeMenuCode == "") {
				assemblys.confirm("确定删除当前菜单吗？", function() {
					noneCallback();
				});
			} else {
				assemblys.confirm("确定删除当前菜单吗？<br><font style='color:red;'>将同时删除对应的应用功能与功能点</font>", function() {
					// 删除功能点
					customFormTypeEdit.deleteCustomFormTypeMenu(customFormTypeMenuCode).then(function() {
						noneCallback();
					})
				});
			}
		}
		
	},
	// 验证重名
	checkName : function(obj) {
		
		var inputValue = $.trim($(obj).val());
		if (!inputValue) {
			return;
		}
		// 值写入属性
		$(obj).attr("value", inputValue);
		// 去空格
		$(obj).val(inputValue);
		
		// 匹配
		var value = $(obj).attr("value");
		var name = $(obj).attr("name");
		
		if (name == "customFormTypeStateNo") {
			if ($("input[name='" + name + "'][value='" + value + "']").length > 1 || inputValue.search('^[0]*$') != -1) {
				assemblys.msg("该输入值已重复或不可定义 0，请重新定义");
				$(obj).val("").attr("value", "");
				$(obj).focus();
			}
		}
		if (name == "customFormTypeMenuNo") {
			if ($("input[name='" + name + "'][value='" + value + "']").length > 1) {
				assemblys.msg("该输入值已重复，请重新定义");
				$(obj).val("").attr("value", "");
				$(obj).focus();
			}
		}
		
	},
	up : function(obj) {
		var $this = $(obj).parents("tr");
		var $prev = $this.prev("tr");
		if ($prev.length == 0) {
			assemblys.msg("到顶了，不能移动了");
		} else {
			$prev.insertAfter($this);
		}
	},
	down : function(obj) {
		var $this = $(obj).parents("tr");
		var $next = $this.next("tr");
		if ($next.length == 0) {
			assemblys.msg("到底了，不能移动了");
		} else {
			$next.insertBefore($this);
		}
	},
	initLayui : function() {
		var form = layui.form;
		form.on("radio(customFormTypeMenuType)", function(data) {
			$(data.elem).parent().find("input[name='customFormTypeMenuType']").val(data.value);
			
			var $selectContent = $(data.elem).parents("tr").find(".selectContent");
			if (data.value == "3") {
				$selectContent.hide();
			} else {
				$selectContent.show();
			}
			
		});
		form.on("select(customFormTypeStateStatus)", function(data) {
			if (data.value != "") {
				$(data.elem).parents("tr").siblings().find("select[name='customFormTypeStateStatus']").each(function() {
					if (data.value == $(this).val()) {
						$(this).val("");
						layui.form.render();
						return false;
					}
				});
			}
		});
		// 监听图标风格
		form.on('radio(customFormTypeIconType)', function(data) {
			$("#customFormTypeIcon").val("").next().removeClass("layui-icon2 layui-icon3").addClass("layui-icon" + data.value);
			customFormTypeEdit.toSelectIcon();
			return false;
		});
		form.on("submit(save)", function(data) {
			customFormTypeEdit.saveCustomFormType();
		});
		layui.form.render();
	},
	deleteCustomFormTypeMenu : function(customFormTypeMenuCode) {
		return $.ajax({
			url : basePath + "frame/customFormType/deleteCustomFormTypeMenu.spring",
			type : "post",
			data : {
				"customFormTypeMenuCode" : customFormTypeMenuCode,
				"appCode" : param.get("appCode"),
			},
			dataType : "json",
			success : function(data) {
				assemblys.msg("删除成功", function() {
					parent.customFormTypeList.getCustomFormTypeListData();
				});
			}
		});
	},
	/**
	 * 保存配置
	 */
	saveCustomFormType : function() {
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		$.ajax({
			url : basePath + "frame/customFormType/saveCustomFormType.spring",
			type : "post",
			data : param.__form(),
			dataType : "json",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					parent.customFormTypeList.getCustomFormTypeListData();
					assemblys.closeWindow();
				});
			}
		});
		
	},
	/**
	 * 选择流程
	 */
	toSelectCustomApprovalCode : function(obj) {
		window.__singleSelectParam = {
			parentName : "流程分类",
			parentURL : basePath + "frame/customApprovalFlowType/getCustomApprovalFlowTypeList.spring",
			parentField : {
				name : "customApprovalFlowTypeName",
				value : "customApprovalFlowTypeCode"
			},
			parentParam : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
			},
			parentParseData : function(data) {
				return data.customApprovalFlowTypeList;
			},
			placeholder : "流程名称",
			URL : basePath + "frame/customApprovalFlow/getCustomApprovalFlowList.spring",
			param : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
			},
			field : {
				name : "customApprovalFlowName",
				value : "customApprovalFlowCode"
			},
			parseData : function(data) {
				return data.customApprovalFlowList;
			},
			callback : function(name, value) {
				obj.value = name;
				$(obj).next().val(value);
			}
		};
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '500px', '500px' ],
			title : "选择流程",
			scrollbar : false,
			content : basePath + "plugins/components/singleSelect/singleSelect.html"
		});
		
	},
	/**
	 * 选择菜单图标
	 */
	toSelectIcon : function() {
		// 图标
		var map = {
			"2" : "selectIcon.html",
			"3" : "selectIcon2.html"
		}
		var customFormTypeIconType = param.get("customFormTypeIconType");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			closeBtn : 0,
			area : [ '100%', '100%' ],
			title : false,
			scrollbar : false,
			content : basePath + "frame/leftMenu/" + map[customFormTypeIconType] + "?id=customFormTypeIcon"
		});
	},
	clearCustomApprovalFlowValue : function() {
		param.set("customApprovalFlowCode", "");
		param.set("customApprovalFlowName", "");
	}
}