var customFormTypeList = {
	// 公司长度
	compLength : 1,
	// 公司
	compList : [],
	// 初始化
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			$("span[titleName]").text("自定义表单分类");
			customFormTypeList.getCompList();
			return customFormTypeList.getExceAppList();
		}).then(function() {
			assemblys.initSessionStoragePram();
			customFormTypeList.getCustomFormTypeList();
			customFormTypeList.initLayui();
		})
	},
	getCompList : function() {
		var compList = []
		var length = 0;
		$("select[name='compNo']").find("option").each(function() {
			compList.push({
				"compNo" : $(this).attr("value"),
				"compName" : $.trim($(this).text())
			})
			length++;
		});
		customFormTypeList.compList = compList;
		customFormTypeList.compLength = length || 1;
	},
	getCustomFormTypeListData : function() {
		page.set("curPageNum", 1);
		customFormTypeList.getCustomFormTypeList();
	},
	getCustomFormTypeList : function() {
		assemblys.tableRender({
			elem : '#list',
			url : basePath + 'frame/customFormType/getCustomFormTypePager.spring?' + param.__form(),
			cols : [ [ {
				title : '序号',
				align : "center",
				type : 'numbers'
			}, {
				title : '操作',
				width : 110,
				align : "center",
				templet : function(d) {
					var html = '';
					html += '<i class="layui-icon layui-icon-edit i_icon" title="编辑分类" lay-event="toEdit"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除分类" lay-event="toDel" ></i>';
					if (customFormTypeList.compLength > 1) {
						html += '<i class="layui-icon2 i_icon" title="复制分类" lay-event="toCopy" >&#xe77e;</i>';
					}
					html += '<i class="layui-icon2" title="导出SQL" lay-event="toSql" >&#xea80;</i>';
					return html;
				}
			}, {
				title : '分类图标',
				width : 90,
				align : "center",
				templet : function(d) {
					return d.customFormTypeIcon ? "<i style='font-size:20px;'  class='layui-icon" + d.customFormTypeIconType + "' > " + d.customFormTypeIcon + "</i>" : "";
				}
			}, {
				title : '表单分类名称',
				align : "left",
				minWidth : 150,
				templet : function(d) {
					return assemblys.htmlDecode(d.customFormTypeName);
				}
			}, {
				title : '流程名称',
				minWidth : 150,
				align : "left",
				templet : function(d) {
					return assemblys.htmlDecode(d.customApprovalFlowName);
				}
			}, {
				title : '菜单数',
				width : 80,
				align : "center",
				templet : function(d) {
					if (d.menuNum > 0) {
						return '<a style="color:blue;" lay-event="toMenu"  style="text-decoration: underline;">' + d.menuNum + '</i>';
					}
					return 0;
				}
			}, {
				title : '状态数',
				width : 80,
				align : "center",
				field : "stateNum"
			}, {
				title : '业务编号',
				width : 160,
				align : "left",
				templet : function(d) {
					return assemblys.htmlDecode(d.businessCode);
				}
			}, {
				title : '创建人',
				align : "center",
				width : 160,
				templet : function(d) {
					return d.createUserName + "<br>" + assemblys.dateToStr(d.createDate);
				}
			}, {
				title : '操作人',
				align : "center",
				width : 160,
				templet : function(d) {
					return d.optUserName + "<br>" + assemblys.dateToStr(d.optDate);
				}
			}, {
				title : '排序号',
				align : "center",
				width : 80,
				field : "seqNo"
			}, {
				title : '状态',
				align : "center",
				width : 100,
				templet : function(d) {
					return '<input type="checkbox"  lay-filter="state"  lay-skin="switch"  customFormTypeID="' + d.customFormTypeID + '"  ' + (d.state == "1" ? "checked" : "") + ' lay-text="启用|停用">';
				}
			} ] ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
			},
			events : {
				toEdit : customFormTypeList.toEdit,
				toDel : customFormTypeList.toDel,
				toMenu : customFormTypeList.toMenu,
				toCopy : customFormTypeList.toCopy,
				toSql : customFormTypeList.toSql,
			}
		});
	},
	getExceAppList : function() {
		return $.ajax({
			url : basePath + "frame/customFormType/getExceAppList.spring",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var appList = data.appList;
				var $select = $("select[name='appCode']");
				var html = "";
				var customAppCode = localStorage.getItem("CUSTOM_APPCODE_VALUE");
				
				var currAppCode = param.get("currAppCode");
				if (!currAppCode) {
					$('.hide-appCode').removeClass('layui-hide');
					var appCode = param.get("appCode");
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0) {
							var selected = customAppCode == appList[i].appCode ? "selected" : "";
							html += '<option ' + selected + ' value="' + appList[i].appCode + '" ' + (appList[i].appCode == appCode || (!appCode && i == 0) ? "selected" : "") + '>' + appList[i].appName + '</option>';
						}
					}
				} else {
					for (var i = 0; i < appList.length; i++) {
						if (appList[i].compAppID != 0 && currAppCode == appList[i].appCode) {
							html += '<option value="' + appList[i].appCode + '" selected>' + appList[i].appName + '</option>';
						}
					}
				}
				$select.append(html);
				layui.form.render();		
			}
		});
	},
	initLayui : function() {
		var form = layui.form;
		form.on("select(compNo)", function(data) {
			customFormTypeList.getCustomFormTypeListData();
		});
		form.on("select(appCode)", function(data) {
			customFormTypeList.getCustomFormTypeListData();
			localStorage.setItem("CUSTOM_APPCODE_VALUE", data.value);
		});
		form.on("radio(state)", function(data) {
			customFormTypeList.getCustomFormTypeListData();
		});
		form.on("switch(state)", function(obj) {
			customFormTypeList.updateCustomFormTypeState(obj);
		});
		layui.form.render();
	},
	updateCustomFormTypeState : function(obj) {
		var state = obj.elem.checked ? 1 : 0;
		var customFormTypeID = $(obj.elem).attr("customFormTypeID");
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		$.ajax({
			url : basePath + "frame/customFormType/updateCustomFormTypeState.spring",
			type : "post",
			data : {
				"customFormTypeID" : customFormTypeID,
				"appCode" : param.get("appCode"),
				"state" : state
			},
			dataType : "json",
			success : function(data) {
				assemblys.msg("更改成功", function() {
					hasSubmit = false;
					customFormTypeList.getCustomFormTypeListData();
				});
			}
		});
		
	},
	toEdit : function(data) {
		var customFormTypeID = data && data.customFormTypeID || 0;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '100%', '100%' ],
			title : customFormTypeID == 0 ? "新增自定义表单分类" : "编辑自定义表单分类",
			scrollbar : false,
			content : "customFormTypeEdit.html?customFormTypeID=" + customFormTypeID + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&compNo=" + param.get("compNo")
		});
	},
	toDel : function(data) {
		assemblys.confirm("确定删除当前表单分类吗？", function() {
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			$.ajax({
				url : basePath + "frame/customFormType/deleteCustomFormType.spring",
				type : "post",
				data : {
					"customFormTypeID" : data.customFormTypeID,
					"appCode" : param.get("appCode")
				},
				dataType : "json",
				success : function(data) {
					assemblys.msg("删除成功", function() {
						hasSubmit = false;
						customFormTypeList.getCustomFormTypeListData();
					});
				}
			});
		}, function() {
			hasSubmit = false;
		});
		
	},
	toSql : function(data) {
		location.href = basePath + "frame/customFormType/exportCustomFormType.spring?appCode=" + param.get("appCode") + "&customFormTypeCode=" + data.customFormTypeCode;
	},
	toMenu : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '80%', '80%' ],
			title : false,
			scrollbar : false,
			content : "customFormMenuList.html?customFormTypeCode=" + d.customFormTypeCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode")
		});
	},
	toCopy : function(d) {
		var compNo = param.get("compNo");
		var compList = customFormTypeList.compList;
		var html = "";
		for (var i = 0; i < compList.length; i++) {
			var comp = compList[i];
			if (comp.compNo == compNo) {
				continue;
			}
			html += '<div style="float:left;width:200px;"><input type="radio" name="copyCompNo" value="' + comp.compNo + '" title="' + comp.compName + '"  /></div>';
		}
		html += '<div style="clear:both;"></div>';
		html += '<div class="layui-form-item" style="margin-top:15px;">';
		html += '	<label class="layui-form-label"> </label>';
		html += '	<div class="layui-input-inline">';
		html += '		<input type="button" class="layui-btn layui-btn-sm" value="确认复制"  onclick="customFormTypeList.copyCustomData(\'' + d.customFormTypeCode + '\')"  >';
		html += '	</div>';
		html += '</div>';
		layer.open({
			type : 1,
			skin : 'layui-layer-aems',
			title : "将当前自定义分类【" + d.customFormTypeName + "】复制给（包含表单、流程等）",
			scrollbar : false,
			area : [ "650px", "300px" ],
			content : "<div style='padding: 10px;' lay-filter='copyCompNo' class='layui-form' >" + html + "</div>"
		});
		layui.form.render("radio", "copyCompNo");
	},
	copyCustomData : function(customFormTypeName) {
		var compNo = param.get("compNo");
		var copyCompNo = $("input[name='copyCompNo']:checked").val() || "";
		if (!copyCompNo) {
			assemblys.msg("请选择需要复制的医院");
			return;
		}
		
		$.ajax({
			url : basePath + "frame/customFormType/saveCopyCustomFormType.spring",
			type : "post",
			data : {
				"customFormTypeCode" : customFormTypeName,
				"appCode" : param.get("appCode"),
				"compNo" : copyCompNo
			},
			dataType : "json",
			success : function(data) {
				assemblys.msg("复制成功", function() {
					$(".layui-layer-close").click();
				});
			}
		});
	}
}