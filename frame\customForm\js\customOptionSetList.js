$(function() {
	parent.customForm.addCustomFormClassStyle(document.getElementsByTagName("head")[0]);
	layui.use([ "form", "laypage" ], function() {
		var form = layui.form;
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			customOptionSetContent : function(value, item) { //value：表单的值、item：表单的DOM对象
				var $inputs = $("input[name='customOptionSetContent'][value='" + value + "']");
				if ($inputs.length > 1) {
					return "选项内容不能重复";
				}
			},
			businessCode : function(value, item) { //value：表单的值、item：表单的DOM对象
				if (value) {
					var count = 0;
					$("input[name='businessCode']").each(function(i, e) {
						if (this.value == value) {
							count++;
						}
					});
					if (count > 1) {
						return "业务编号不能重复";
					}
					
					if (customOptionSetList.checkCustomOptionSetBusinessCode($(item).parents("tr").find("input[name='customOptionSetCode']").val(), value)) {
						return "已存在相同的业务编号";
					}
				}
			},
			isNum : function(value, item) {
				if (!/^[0-9]+$/.test(value) || value <= 0 || !(value <= 9999)) {
					return "必须是大于0且小于等于9999的正整数";
				}
			},
			select : function(value, item) {
				if (!value) {
					var border = $(item).next().css("border");
					$(item).next().css("border", "1px solid red");
					setTimeout(function() {
						$(item).next().css("border", border);
					}, 3000);
					return "请选择" + $(item).attr("selectName") + "";
				}
			}
		});
		
		form.on("switch(optStat)", function(data) {
			customOptionSetList.reload();
		});
		
		form.on("switch(hasDefault)", function(data) {
			$(data.elem).prev().val(data.elem.checked ? 1 : 0);
			var fieldSet = customFieldSet == "select" ? customFieldSet : $(data.elem).parents("tr").find("select[name='customFieldSet']").val();
			if (data.elem.checked) {
				if ("radio" == fieldSet || "radioOther" == fieldSet || "select" == fieldSet) {
					$("input[name='hasDefaultTemp']:checked").not(data.elem).prop("checked", false).each(function(i, element) {
						var layFilter = $(this).parents("tr[lay-filter]").attr("lay-filter");
						layui.form.render("checkbox", layFilter);
					});
					$("input[name='hasDefault']").not($(data.elem).prev()).val("0");
				}
			}
		});
		
		form.on("submit(save)", function(data) {
			customOptionSetList.saveCustomOptionSets();
			return false;
		});
		
		form.on("submit(add)", function(data) {
			$("tr.noResult").remove();
			var $prevTr = $("tr.main_title").nextAll().last();
			var index = $prevTr.index();
			var seqNo = (parseInt($prevTr.find("input[name='seqNo']").val()) || 0) + 1;
			var firstFieldSet = customOptionSetList.getFirstFieldSet2($("tr.main_title").next());
			var element = customOptionSetList.addCustomOptionSet({
				"customOptionSetID" : 0,
				"customFieldSet" : firstFieldSet ? firstFieldSet : customFieldSet
			}, false, $prevTr.length > 0, seqNo, firstFieldSet, index);
			$("table.main_table").append(assemblys.createElement(element));
			$("tr.main_title").nextAll().last().find("input[name=customOptionSetContent]").focus();
			layui.form.render(null, "tr_" + index);
			layui.form.on("radio", function(data) {
				$(data.elem).siblings("input[name='status']").val(data.value);
			});
			return false;
		});
		customOptionSetList.getCustomOptionSetListData(null, title);
		
		// 获取隐藏的class
		parent.customForm.addCustomFormClassStyle(document.getElementsByTagName("head")[0]);
		
		if(customFieldSet == "checkbox" || customFieldSet == "radio"){
			$("table").find("tr").find("td:eq(0)").removeClass("setCustomFormClass");
		}
		
		form.render();
	});
});
var customOptionSetList = {
	getFirstFieldSet2 : function($tr) {
		if ($tr.length == 0) {
			return "";
		}
		var firstFieldSet = ($tr.find("select[name='customFieldSet']").val() || "").replace("Other", "");
		if (firstFieldSet != "label") {
			$tr.children("td:eq(7)").find("dd[lay-value]").not("[lay-value^='" + firstFieldSet + "'],[lay-value='label']").addClass("layui-disabled");
			return firstFieldSet;
		} else {
			return customOptionSetList.getFirstFieldSet2($tr.next());
		}
	},
	addCustomOptionSet : function(customOptionSet, hasAdd, prevTr, seqNo, firstFieldSet, index) {
		var parentCustomOptionSetCode = $("input[name='parentCustomOptionSetCode']").val();
		return {
			"attr" : {
				"lay-filter" : "tr_" + index
			},
			"tagName" : "tr",
			"className" : "layui-form",
			"children" : [
			// 操作
			{
				"tagName" : "td",
				"align" : "center",
				"className" : ((customFieldSet != "checkbox"  && customFieldSet != "radio")   ? "setCustomFormClass" : ""),
				"children" : [ (customOptionSet.customOptionSetCode && customFieldSet != "label" && customOptionSet.customFieldSet != "label" && customOptionSet.parentCustomOptionSetCode == "") && (!parentCustomOptionSetCode || customFieldSet != "checkbox" || customFieldSet != "radio") ? {
					"tagName" : "i",
					"className" : "layui-icon layui-icon-add-circle i_icon " + ((customFieldSet != "checkbox"  && customFieldSet != "radio") ? "setCustomFormClass" : ""),
					"title" : "添加子选项",
					"customOptionSet" : customOptionSet,
					"onclick" : function() {
						var $customOptionSetLevel = $("input[name='customOptionSetLevel']");
						$customOptionSetLevel.val(parseInt($customOptionSetLevel.val()) + 1);
						customOptionSetList.getCustomOptionSetListData(this.customOptionSet.customOptionSetCode, this.customOptionSet.customOptionSetContent);
					}
				} : null, (customOptionSet.customOptionSetCode && customFieldSet != "label" && customOptionSet.customFieldSet != "label") && !hasAdd ? {
					"tagName" : "i",
					"className" : "layui-icon layui-icon-share i_icon setCustomFormClass",
					"title" : "关联选项",
					"customOptionSet" : customOptionSet,
					"onclick" : function() {
						customOptionSetList.toRelatedAttribute(this);
					}
				} : null, !customOptionSet.customOptionSetCode ? {
					"tagName" : "i",
					"className" : "layui-icon layui-icon-delete i_delete",
					"title" : "删除",
					"onclick" : function() {
						customOptionSetList.deleteCustomOptionSet(this);
					},
					"style" : {
						"cursor" : "pointer"
					}
				} : null, {
					"tagName" : "input",
					"type" : "hidden",
					"name" : "customOptionSetID",
					"value" : customOptionSet.customOptionSetID
				}, {
					"tagName" : "input",
					"type" : "hidden",
					"name" : "customOptionSetCode",
					"value" : customOptionSet.customOptionSetCode || ""
				} ]
			}
			// 选项内容
			, {
				"tagName" : "td",
				"align" : "center",
				"children" : [ {
					"attr" : {
						"limit" : "100",
						"lay-verify" : "required|limit|customOptionSetContent",
						"value" : customOptionSet.customOptionSetContent || ""
					},
					"tagName" : "input",
					"className" : "layui-input",
					"type" : "text",
					"name" : "customOptionSetContent",
					"maxlength" : "100",
					"value" : customOptionSet.customOptionSetContent || "",
					"onchange" : function() {
						$(this).attr("value", this.value);
					}
				} ]
			}
			// 业务编号
			, {
				"tagName" : "td",
				"align" : "center",
				"children" : [ {
					"attr" : {
						"limit" : "100",
						"lay-verify" : "limit|businessCode"
					},
					"tagName" : "input",
					"className" : "layui-input",
					"type" : "text",
					"name" : "businessCode",
					"maxlength" : "100",
					"value" : customOptionSet.businessCode || ""
				} ]
			}
			// 业务值
			, {
				"tagName" : "td",
				"align" : "center",
				"children" : [ {
					"attr" : {
						"limit" : "100",
						"lay-verify" : "limit"
					},
					"tagName" : "input",
					"className" : "layui-input",
					"type" : "text",
					"name" : "businessValue",
					"maxlength" : "100",
					"value" : customOptionSet.businessValue || ""
				} ]
			}
			// 备注
			, {
				"tagName" : "td",
				"align" : "center",
				"children" : [ {
					"attr" : {
						"limit" : "1000",
						"lay-verify" : "limit"
					},
					"tagName" : "input",
					"className" : "layui-input",
					"type" : "text",
					"name" : "remark",
					"maxlength" : "1000",
					"value" : customOptionSet.remark || ""
				} ]
			}
			// 排序号
			, {
				"tagName" : "td",
				"align" : "center",
				"children" : [ {
					"attr" : {
						"lay-verify" : "required|isNum"
					},
					"tagName" : "input",
					"className" : "layui-input",
					"type" : "text",
					"name" : "seqNo",
					"value" : customOptionSet.seqNo || (!prevTr ? 1 : seqNo)
				} ]
			}
			// 控件类型
			, {
				"tagName" : "td",
				"align" : "center",
				"className" : customFieldSet == "select" || customFieldSet == "label" ? "layui-hide" : "",
				"children" : [ customFieldSet == "select" || customFieldSet == "label" ? {
					"tagName" : "input",
					"type" : "hidden",
					"name" : "customFieldSet",
					"value" : customFieldSet
				} : ({
					"attr" : {
						"selectName" : "控件类型",
						"lay-verify" : "select"
					},
					"tagName" : "select",
					"name" : "customFieldSet",
					"children" : [ {
						"tagName" : "option",
						"value" : "",
						"innerText" : "请选择",
						"selected" : customOptionSet.customFieldSet == ""
					}, !firstFieldSet || firstFieldSet.indexOf("radio") > -1 ? {
						"tagName" : "option",
						"value" : "radio",
						"innerText" : "单选框",
						"selected" : customOptionSet.customFieldSet == "radio"
					} : null, !firstFieldSet || firstFieldSet.indexOf("radio") > -1 ? {
						"tagName" : "option",
						"value" : "radioOther",
						"innerText" : "单选输入框",
						"selected" : customOptionSet.customFieldSet == "radioOther"
					} : null, !firstFieldSet || firstFieldSet.indexOf("checkbox") > -1 ? {
						"tagName" : "option",
						"value" : "checkbox",
						"innerText" : "多选框",
						"selected" : customOptionSet.customFieldSet == "checkbox"
					} : null, !firstFieldSet || firstFieldSet.indexOf("checkbox") > -1 ? {
						"tagName" : "option",
						"value" : "checkboxOther",
						"innerText" : "多选输入框",
						"selected" : customOptionSet.customFieldSet == "checkboxOther"
					} : null, {
						"tagName" : "option",
						"value" : "label",
						"innerText" : "标签",
						"selected" : customOptionSet.customFieldSet == "label"
					} ]
				}) ]
			}
			// 默认选中
			, {
				"tagName" : "td",
				"className" : customFieldSet == "label" ? "layui-hide" : "",
				"align" : "center",
				"children" : [ {
					"tagName" : "input",
					"type" : "hidden",
					"name" : "hasDefault",
					"value" : customOptionSet.hasDefault || 0,
				}, {
					"attr" : {
						"lay-skin" : "switch",
						"lay-text" : "是|否",
						"lay-filter" : "hasDefault"
					},
					"tagName" : "input",
					"className" : "layui-input",
					"type" : "checkBox",
					"name" : "hasDefaultTemp",
					"value" : "1",
					"checked" : customOptionSet.hasDefault == 1
				} ]
			}
			// 状态
			, {
				"tagName" : "td",
				"align" : "center",
				"children" : [ {
					"tagName" : "input",
					"type" : "hidden",
					"name" : "status",
					"value" : customOptionSet.status == 0 ? 0 : 1
				}, {
					"tagName" : "input",
					"title" : "<font style='color:green'>有效</font>",
					"type" : "radio",
					"value" : "1",
					"name" : "status_" + index,
					"checked" : customOptionSet.status != 0
				}, {
					"tagName" : "input",
					"title" : "<font style='color:red'>无效</font>",
					"type" : "radio",
					"value" : "0",
					"name" : "status_" + index,
					"checked" : customOptionSet.status == 0
				} ]
			} ]
		};
	},
	upward : function(){
		if($("input[name='parentCustomOptionSetCode']").val()){
			customOptionSetList.getCustomOptionSetListData("","");
		}else{
			location.href = formUrl;
		}
	},
	getCustomOptionSetListData : function(parentCustomOptionSetCode, customOptionSetContent) {
		
		$("input[name='parentCustomOptionSetCode']").val(parentCustomOptionSetCode);
		$("div#mousewheel div").find("a").eq(1).remove();
		$("div#mousewheel div").find("span").eq(0).remove();
		if (parentCustomOptionSetCode) {
			$("div#mousewheel div").append('<span>→</span><a href="javascript:;" style="text-decoration:none;margin-left:0px;" class="layui-btn layui-btn-sm" parentCustomOptionSetCode="' + parentCustomOptionSetCode + '" customOptionSetContent="' + customOptionSetContent + '" customOptionSetLevel="' + $("input[name='customOptionSetLevel']").val() + '" onclick="customOptionSetList.jumpTo(this);">' + $('<div>').text(customOptionSetContent).html() + '</a>');
			customOptionSetList.renderMousewheel();
		}
		parent.$("#layer-editField").prev().text(customOptionSetContent);
		
		var optStat = $("input[lay-filter='optStat']").is(":checked");
		
		$.ajax({
			url : basePath + "frame/newCustomForm/getCustomOptionSetListData.spring",
			dataType : "json",
			data : {
				"customFieldCode" : customFieldCode,
				"parentCustomOptionSetCode" : parentCustomOptionSetCode,
				"status" : optStat ? 99 : 1,
				"appCode" : appCode,
				"compNo" : compNo,
				"pageSize" : 99999
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var layerIndex = layer.load(1);
					setTimeout(function() {
						data = data.data;
						$("tr.main_title").nextAll().remove();
						var pager = data.pager;
						var items = pager.items;
						if (items.length == 0) {
							$(".main_title").after('<tr class="noResult"><td colspan="10" align="center">没有相关数据！</td></tr>');
						} else {
							$("tr.noResult").remove();
							var elementAry = [];
							var firstFieldSet = customOptionSetList.getFirstFieldSet(items);
							var time1 = new Date().getTime();
							for (var i = 0; i < items.length; i++) {
								var element = customOptionSetList.addCustomOptionSet(items[i], data.hasAdd, i > 0, i > 0 ? items[i - 1].seqNo : 1, firstFieldSet, i);
								elementAry.push(element);
							}
							$("table.main_table").append(assemblys.createElement(elementAry));
							layui.form.render();
							layui.form.on("radio", function(data) {
								$(data.elem).siblings("input[name='status']").val(data.value);
							});
						}
						
						layer.close(layerIndex);
					}, 50);
				} else {
					assemblys.alert("获取选项列表数据出错!");
				}
			}
		});
		
		customOptionSetList.exportCustomFormInfo();
		
	},
	jumpTo : function(obj) {
		$("input[name='customOptionSetLevel']").val($(obj).attr("customOptionSetLevel"));
		var parentCustomOptionSetCode = $(obj).attr("parentCustomOptionSetCode");
		var customOptionSetContent = $(obj).attr("customOptionSetContent");
		$(obj).nextAll().remove();
		if (parentCustomOptionSetCode) {
			$(obj).prev().remove();
			$(obj).remove();
		}
		customOptionSetList.getCustomOptionSetListData(parentCustomOptionSetCode, customOptionSetContent);
	},
	saveCustomOptionSets : function() {
		assemblys.confirm("确认保存吗?", function() {
			$.ajax({
				url : basePath + "frame/newCustomForm/saveCustomOptionSets.spring",
				dataType : "json",
				data : $("#form1").serialize(),
				type : "POST",
				success : function(data) {
					assemblys.msg("保存成功!", function() {
						customOptionSetList.reload();
					});
				}
			});
		});
	},
	renderMousewheel : function() {
		
		var changeLeft = function(obj, wheelDelta) {
			var left = $(obj).children().css("left");
			if (left == "auto") {
				left = 0;
			} else {
				left = parseInt(left.replace("px", ""));
			}
			left += wheelDelta;
			$("#leftIcon").addClass("layui-hide");
			$("#leftIcon2").removeClass("layui-hide");
			$("#rightIcon").addClass("layui-hide");
			$("#rightIcon2").removeClass("layui-hide");
			
			if (left > 0) {
				left = 0;
				$("#leftIcon2").addClass("layui-hide");
				$("#leftIcon").removeClass("layui-hide");
			} else if (left < 362 - $(obj).width()) {
				left = 362 - $(obj).width();
				$("#rightIcon2").addClass("layui-hide");
				$("#rightIcon").removeClass("layui-hide");
			}
			$(obj).children().css("left", left);
		}

		if ($("#mousewheel").children().width() > 362) {
			$("#rightIcon").addClass("layui-hide");
			$("#rightIcon2").removeClass("layui-hide");
			$("#mousewheel").on("mousewheel", function(e) {
				if (e.preventDefault) {
					e.preventDefault();
				} else {
					e.returnValue = false;
				}
				
				var wheelDelta = 30;
				if (e.originalEvent.wheelDelta < 0) {
					wheelDelta = wheelDelta * -1
				}
				changeLeft(this, wheelDelta);
			});
		}
		$("#rightIcon2").click(function(e) {
			changeLeft($("#mousewheel")[0], -30);
		});
		$("#leftIcon2").click(function(e) {
			changeLeft($("#mousewheel")[0], 30);
		});
	},
	reload : function() {
		if (customFieldSet == "label") {
			customOptionSetList.getCustomOptionSetListData(null, title);
		} else {
			$("div#mousewheel a:last").click();
		}
	},
	//到关联属性或项
	toRelatedAttribute : function(obj) {
		var customOptionSetContent = obj.customOptionSet.customOptionSetContent;
		var customOptionSetCode = obj.customOptionSet.customOptionSetCode;
		var url = basePath + "frame/customForm/getRelatedCustomModularList.spring?customFieldCode=" + customFieldCode + "&customModularCode=" + customModularCode + "&title=" + encodeURIComponent(customOptionSetContent);
		url += "&customFormCode=" + customFormCode + "&relationOptionCode=" + customOptionSetCode + "&appCode=" + appCode;
		url += "&fromUrl=" + encodeURIComponent(location.href);
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			closeBtn : 0,
			area : [ '100%', '100%' ],
			title : false,
			scrollbar : false,
			content : url
		});
	},
	getFirstFieldSet : function(items) {
		if (items.length == 0) {
			return "";
		}
		for (var i = 0; i < items.length; i++) {
			var firstFieldSet = (items[0].customFieldSet || "").replace("Other", "");
			if (firstFieldSet != "label") {
				return firstFieldSet;
			}
		}
		return "";
	},
	deleteCustomOptionSet : function(obj) {
		$(obj).parents("tr").remove();
		if ($("tr.main_title").next().length == 0) {
			$("tr.main_title").after('<tr class="noResult"><td colspan="10" align="center">没有相关数据！</td></tr>');
		}
	},
	checkCustomOptionSetBusinessCode : function(customOptionSetCode, value) {
		var result = false;
		$.ajax({
			url : basePath + "frame/customForm/checkCustomOptionSetBusinessCode.spring",
			data : {
				"businessCode" : value,
				"customOptionSetCode" : customOptionSetCode,
				"appCode" : appCode,
				"compNo" : compNo
			},
			dataType : "json",
			async : false,
			success : function(data) {
				result = data.result == "has";
			}
		});
		return result;
	},
	/**
	 * 下载模板
	 */
	downloadTemplate : function() {
		// 判断如果是Ipad则不支持导出功能功能
		var ua = navigator.userAgent.toLowerCase();
		var s = ua.match(/iPad/i);
		if (s == "ipad") {
			assemblys.msg("Ipad不支持该处导出功能，请更换设备导出");
			return false;
		}
		var url = basePath + "frame/common/downloadTemplate.spring?fileName=" + encodeURIComponent("导入表单子项模版.xls");
		location.href = url;
	},
	/**
	 * 导入实例
	 */
	uploadInst : null,
	/**
	 * 导入
	 */
	exportCustomFormInfo : function() {
		if (customOptionSetList.uploadInst == null) {
			// 初始化
			customOptionSetList.uploadInst = layui.upload.render({
				elem : '#selectExportFile',
				url : basePath + "frame/newCustomForm/exportCustomOptionSet.spring",
				accept : 'file',
				exts : 'xls',
				data : {
					"customFieldCode" : customFieldCode,
					"parentCustomOptionSetCode" : $("input[name='parentCustomOptionSetCode']").val() || "",
					"appCode" : appCode,
					"compNo" : compNo
				},
				done : function(res) {
					//上传完毕回调
					if (res.result == "success") {
						assemblys.msg("导入成功", function() {
							location.reload();
						});
					} else {
						assemblys.alert(res.resultMsg);
					}
				}
			});
		} else {
			// 重新载入
			customOptionSetList.uploadInst.reload({
				data : {
					"customFieldCode" : customFieldCode,
					"parentCustomOptionSetCode" : $("input[name='parentCustomOptionSetCode']").val() || "",
					"appCode" : appCode,
					"compNo" : compNo
				}
			});
		}
	}
};