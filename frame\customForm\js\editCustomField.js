$(function() {
	layui.use([ 'form' ], function() {
		var form = layui.form;
		
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			},
			customFieldName : function(value, item) { //value：表单的值、item：表单的DOM对象
				if (editCustomField.checkCustomFieldName(customFieldCode, customModularCode, value)) {
					return "当前模块存在相同的属性名称";
				}
			},
			isNum : function(value, item) {
				if (!/^[0-9]+$/.test(value) || value <= 0 || !(value <= 2000)) {
					return "必须是大于0且小于等于2000的正整数";
				}
			},
			businessCode : function(value, item) { //value：表单的值、item：表单的DOM对象
				if (value && editCustomField.checkCustomFieldBusinessCode(value)) {
					return "已存在相同的业务编号";
				}
			}
		});
		
		form.on('select(dateRange)', function(data) {
			var $dateRange = $("[dateRange]");
			if (data.value === "0") {
				$dateRange.removeClass("layui-hide");
				$("#dateRange,#beginDate,#endDate").attr("lay-verify", "required");
			} else {
				$dateRange.addClass("layui-hide");
				$("#dateRange,#beginDate,#endDate").val("").removeAttr("lay-verify");
			}
			return false;
		});
		
		// 监听开关
		form.on("switch(isRead)", function(data) {
			editCustomField.setIsReadHideOrShow(data.elem.checked);
		});
		
		form.on('submit(save)', function(data) {
			editCustomField.saveEditCustomField(data.field);
			return false;
		});
		
		editCustomField.initFieldSet(form);
		editCustomField.initForm();
		// 获取隐藏的class
		parent.customForm.addCustomFormClassStyle(document.getElementsByTagName("head")[0]);
	});
	
	// 回显数据
});

var editCustomField = {
	imgUrl : $("#preImgDiv img").prop("src"),
	// 条件控制
	setIsReadHideOrShow : function(flag) {
		var $must = $(".must");
		var $defaultValue = $("textarea[name='defaultValue']");
		// 默认值必填
		if (flag) {
			$must.removeClass("layui-hide");
			$defaultValue.attr("lay-verify", "required");
		} else {
			$must.addClass("layui-hide");
			$defaultValue.attr("lay-verify", "");
		}
	},
	chooseImg : function(imgFile) {
		var fileType = imgFile.value.substr(imgFile.value.lastIndexOf("."));
		var accept = ".jpg,.png,.jpeg,.gif";
		if (accept.indexOf(fileType) == -1) {
			assemblys.alert("文件格式不支持");
			$(imgFile).after('<input type="file" id="imageurl" name="imageurl" accept="image/jpg, image/png, image/jpeg, image/gif" onchange="chooseImg(this);" style="">').remove();
			$("#preImgDiv img").prop("src", imgUrl);
			return;
		}
		
		var imgWidth = $("#imgWidth").val();
		if (document.all) {// ie
			if (window.FileReader) {// 判断是否支持HTML5
				var fr = new FileReader();
				fr.onload = (function(file) {
					return function(e) {
						$("#preImgDiv").html("<img src='" + this.result + "' width='100' height='100'/>");
					};
				})(imgFile.files[0]);
				fr.readAsDataURL(imgFile.files[0]);
			} else {
				imgFile.select();
				$("#preImgDiv").focus();
				var img = document.selection.createRange().text;
				if (img.indexOf("(") != -1 || img.indexOf(")") != -1) {
					assemblys.alert("文件名或路径不能包含括号");
					$(imgFile).after('<input type="file" id="imageurl" name="imageurl" accept="image/jpg, image/png, image/jpeg, image/gif" onchange="chooseImg(this);" style="">').remove();
					$("#preImgDiv img").prop("src", imgUrl);
					return;
				}
				$("#preImgDiv").html("").append("<img id='preImg' width='100'></img>");
				$("#preImg").css("filter", "progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale',src=\'" + img + "'\)").css({
					"width" : "100px",
					"height" : "100px"
				});
			}
		} else {
			$("#preImgDiv").html("").append("<img id='preImg' width='100'></img>");
			$("#preImg").attr("src", window.URL.createObjectURL(imgFile.files[0]));
		}
	},
	saveImg : function() {
		$("#fieldForm").submit();
	},
	previewImg : function() {
		layer.photos({
			photos : '#preImgDiv',
			anim : 5
		});
	},
	addSub : function() {
		var customFieldCode = $("input[name='customFieldCode']").val();
		var customFieldID = $("#customFieldID").val();
		var url = basePath + "frame/customForm/customOptionSetList.jsp?1=1";
		if (customFieldSet == "interface") {
			url = basePath + "frame/customForm/interfaceOptionList.html?interfaceCode=" + $("select[name='fieldVerifyType']").val();
		}
		url += "&customFieldCode=" + customFieldCode + "&customFieldSet=" + customFieldSet + "&title=" + encodeURIComponent($("#customFieldName").val());
		url += "&customFormCode=" + $("#customFormCode").val() + "&customModularCode=" + $("#customModularCode").val() + "&appCode=" + appCode + "&compNo=" + compNo;
		url += "&fromUrl=" + encodeURIComponent(location.href.replace("customFieldCode=&", "customFieldCode=" + customFieldCode + "&").replace("&customFieldID=0&", "&customFieldID=" + customFieldID + "&"));
		location.href = url;
	},
	addCommon : function() {
		assemblys.confirm("确认把『" + $("#customFieldName").val() + "』升级为公用组件吗？<br><span style='color:red;'>(该操作不可逆)</span>", function() {
			$.ajax({
				"url" : basePath + "frame/newCustomForm/upgradeCommonCustomField.spring",
				"dataType" : "json",
				"data" : {
					"customFieldCode" : $("input[name='customFieldCode']").val(),
					"customFormCode" : $("input[name='customFormCode']").val(),
					"appCode" : appCode
				},
				success : function(data) {
					if (data.hasField == "success") {
						assemblys.msg("升级成功", function() {
							parent.location.reload();
						});
					} else if (data.hasField == "error") {
						assemblys.alert("升级公用组件出错");
					} else {
						assemblys.alert(data.hasField);
					}
				}
			});
		});
	},
	upgradeRichText : function() {
		assemblys.confirm("确认把『" + $("#customFieldName").val() + "』升级为富文本组件吗？<br><span style='color:red;'>(该操作不可逆，同时【是否只读】、【提示语】条件将会重置，【文本长度】、【校验】将失去约束)</span>", function() {
			$.ajax({
				"url" : basePath + "frame/newCustomForm/upgradeRichText.spring",
				"dataType" : "json",
				"data" : {
					"customFieldCode" : $("input[name='customFieldCode']").val(),
					"customFormCode" : $("input[name='customFormCode']").val(),
					"appCode" : appCode
				},
				success : function(data) {
					if (data.hasField == "success") {
						assemblys.msg("升级成功", function() {
							parent.location.reload();
						});
					} else if (data.hasField == "error") {
						assemblys.alert("升级富文本组件出错");
					} else {
						assemblys.alert(data.hasField);
					}
				}
			});
		});
	},
	showAddSubButton : function(customFieldSet, flag) {
		
		if (param.get("isRichText") != 1) {
			$("#upgradeRichTextButton").removeClass("layui-hide");
		}
		if (param.get("isCommon") != 1) {
			$("#addCommon").removeClass("layui-hide");
		}
		if (customFieldSet == "checkbox" || customFieldSet == "select" || customFieldSet == "radio" || customFieldSet == "label" || customFieldSet == "interface") {
			$('#addSub').removeClass("layui-hide");
		} else if (customFieldSet == "img") {
			// 这里不能删
		} else {
			if (flag) {
				assemblys.closeWindow();
			}
		}
	},
	checkCustomFieldName : function(customFieldCode, customModularCode, customFieldName) {
		var result = false;
		$.ajax({
			url : basePath + "frame/newCustomForm/checkCustomFieldName.spring",
			data : {
				"customFieldCode" : customFieldCode,
				"customModularCode" : customModularCode,
				"customFieldName" : customFieldName,
				"appCode" : appCode
			},
			dataType : "json",
			async : false,
			success : function(data) {
				result = data.has;
			}
		});
		return result;
	},
	checkCustomFieldBusinessCode : function(value) {
		var result = false;
		$.ajax({
			url : basePath + "frame/newCustomForm/checkCustomFieldBusinessCode.spring",
			data : {
				"customFieldCode" : customFieldCode,
				"businessCode" : value,
				"compNo" : compNo,
				"appCode" : appCode
			},
			dataType : "json",
			async : false,
			success : function(data) {
				result = data.has;
			}
		});
		return result;
	},
	initForm : function() {
		layui.form.render();
		
		// 存在ID时
		if (customFieldCode) {
			$.ajax({
				url : basePath + "frame/newCustomForm/getCustomField.spring",
				data : {
					customFieldCode : customFieldCode,
					"appCode" : appCode
				},
				dataType : "json",
				success : function(data) {
					layui.use([ 'form' ], function() {
						var form = layui.form;
						var customField = data.customField;
						customField.beginDate = assemblys.dateToStr(customField.beginDate, customField.fieldVerifyType == "date" ? "yyyy-MM-dd" : "");
						customField.endDate = assemblys.dateToStr(customField.endDate, customField.fieldVerifyType == "date" ? "yyyy-MM-dd" : "");
						form.val('param', {
							"customFieldID" : customField.customFieldID,
							"customFieldCode" : customField.customFieldCode,
							"customFieldName" : customField.customFieldName,
							"customFieldSet" : customField.customFieldSet,
							"status" : customField.status,
							"isNecessField" : customField.isNecessField,
							"businessCode" : customField.businessCode,
							"customFieldLength" : customField.customFieldLength,
							"createUserCode" : customField.createUserCode,
							"createUserName" : customField.createUserName,
							"createDate" : assemblys.dateToStr(customField.createDate),
							"funCode" : customField.funCode,
							"relationField" : customField.relationField,
							"fieldVerifyType" : customField.fieldVerifyType ? customField.fieldVerifyType : (customField.customFieldSet == "datetime" ? "date" : ""),
							"isCommon" : customField.isCommon,
							"compNo" : customField.compNo,
							"businessValue" : customField.businessValue,
							"beginDate" : customField.beginDate,
							"endDate" : customField.endDate,
							"dateRange" : customField.dateRange ? customField.dateRange : (customField.dateRange == 0 ? 0 : "-1"),
							"isMobile" : customField.isMobile,
							"interfaceSwitch" : customField.interfaceSwitch,
							"isRead" : customField.isRead,
							"isRichText" : customField.isRichText,
							"remindText" : customField.remindText,
							"defaultValue" : customField.defaultValue,
						});
						
						editCustomField.setIsReadHideOrShow(customField.isRead == 1);
						
						// 时间
						if (customField.customFieldSet == "datetime") {
							if (customField.dateRange == 0) {
								$("[dateRange]").removeClass("layui-hide");
								$("#dateRange").attr("lay-verify", "required");
							}
							
							editCustomField.initDatetime(customField.fieldVerifyType, customField.beginDate, customField.endDate);
						}
						
						// 图片
						if (customField.customFieldSet == "img") {
							form.val('fieldForm', {
								"customFieldCode" : customField.customFieldCode
							});
							
							$("#imgShow")[0].src = customField.imgURL ? baseImgPath + customField.imgURL : basePath + "frame/images/default.jpg";
							$("input[name='isNecessField']").parent().parent().addClass("layui-hide");
						}
						
						// 富文本
						if (customField.customFieldSet == "textarea" && customField.isRichText == 1) {
							$("#customFieldSetText").val("富文本域");
							var toolbars = [ [ 'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'removeformat', 'formatmatch', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|', 'rowspacingtop', 'rowspacingbottom', 'lineheight', '|', 'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'insertimage', '|', 'horizontal', 'date', 'time', '|', 'customstyle', 'paragraph', 'fontfamily', 'fontsize' ] ];
							pubUploader.initEditor("defaultValueTextarea", true, toolbars);
						} else {
							$(".readOrRemind").removeClass("layui-hide");
						}
						
						editCustomField.showAddSubButton(customField.customFieldSet);
						
						$("select[name=relationField]").attr("disabled", "disabled");
						layui.form.render("select", "relationFieldDiv");
						$("select[name=relationField]").removeAttr("disabled");
					});
				}
			});
		} else {
			// 显示隐藏项
			$(".readOrRemind").removeClass("layui-hide");
			if (customFieldSet == "datetime") {
				editCustomField.initDatetime("date", "", "");
			}
		}
		
	},
	initDatetime : function(fieldVerifyType, beginDate, endDate) {
		$("#dateRange").after('<input readonly="readonly" class="layui-input" type="text" id="dateRange" />').remove();
		layui.laydate.render({
			elem : '#dateRange',
			trigger : "click",
			type : fieldVerifyType || "date",
			range : "~",
			value : beginDate || endDate ? beginDate + " ~ " + endDate : "",
			done : function(value, data, endDate) {
				var values = value.split(" ~ ");
				$("#beginDate").val(values[0]);
				$("#endDate").val(values[1]);
			}
		});
	},
	initFieldSet : function(form) {
		var customFieldSetText = "";
		if (customFieldSet == "text") {
			customFieldSetText = "文本框";
		} else if (customFieldSet == "textarea") {
			customFieldSetText = "文本域";
		} else if (customFieldSet == "datetime") {
			customFieldSetText = "日期";
		} else if (customFieldSet == "radio") {
			customFieldSetText = "单选框";
		} else if (customFieldSet == "checkbox") {
			customFieldSetText = "多选框";
		} else if (customFieldSet == "label") {
			customFieldSetText = "标签";
		} else if (customFieldSet == "select") {
			customFieldSetText = "下拉框";
		} else if (customFieldSet == "img") {
			customFieldSetText = "图片";
		} else if (customFieldSet == "org") {
			customFieldSetText = "组织架构";
		} else if (customFieldSet == "interface") {
			customFieldSetText = "接口";
		} else if (customFieldSet == "profile") {
			customFieldSetText = "头像框";
		} else if (customFieldSet == "file") {
			customFieldSetText = "附件";
		}
		
		// 加载接口
		if (customFieldSet == "interface") {
			//改为手动输入接口编号
			//editCustomField.getInterfaceList();
		}
		
		// 特殊验证
		if (customFieldSet == "text" || customFieldSet == "textarea") {
			editCustomField.getFormVerifyTypeList();
		}
		
		// 组织架构
		if (customFieldSet == "org") {
			editCustomField.initAppFuns();
		}
		
		// 渲染
		form.render("select");
		
		// 监听
		form.on("select(fieldVerifyType)", function(data) {
			editCustomField.initDatetime(data.value, "", "");
		});
		
		$("#customFieldSet").val(customFieldSet);
		$("#customFieldSetText").val(customFieldSetText);
	},
	getFormVerifyTypeList : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			data : {
				"dictTypeCode" : "CUSTOMFORM_VERIFY",
				"appCode" : "APP"
			},
			dataType : "json",
			async: false,
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					for (var i = 0; i < data.dictList.length; i++) {
						$("select[name='fieldVerifyType']").append('<option value="' + data.dictList[i].dictContent + '">' + assemblys.htmlEncode(data.dictList[i].dictName) + '</option>');
					}
					layui.form.render();
				}
			}
		});
	},
	getInterfaceList : function() {
		return $.ajax({
			url : basePath + "frame/interface/getInterfaceSettingList.spring",
			dataType : "json",
			data : {
				"compNo" : compNo,
				t : (new Date()).getTime()
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var $select = $("select[name='fieldVerifyType']");
					var options = "";
					for (var i = 0; i < data.interfaceSettingList.length; i++) {
						options += '<option value="' + data.interfaceSettingList[i].interfaceCode + '">' + data.interfaceSettingList[i].interfaceName + '</option>';
					}
					$select.append(options);
					layui.form.render();
				} else {
					assemblys.alert("加载数据出错,请刷新重试");
				}
			}
		});
	},
	saveEditCustomField : function(pars) {
		// 必填项默认0
		if (!pars.isNecessField) {
			pars.isNecessField = 0;
		}
		
		if (!pars.isMobile) {
			pars.isMobile = 0;
		}
		
		if (!pars.interfaceSwitch) {
			pars.interfaceSwitch = 0;
		}
		
		// 没有状态即为无效 -1
		if (!pars.status) {
			pars.status = -1;
		}
		assemblys.confirm("确认保存？", function() {
			var dom = parent.customForm.label;
			pars.saveAppCode = appCode;
			if (!isSubmit) {
				isSubmit = true;
				var url = basePath + "frame/newCustomForm/saveCustomField.spring";
				$.ajax({
					type : "post",
					url : url,
					data : pars,
					dataType : "json",
					success : function(data) {
						
						isSubmit = false;
						assemblys.msg("保存成功", function() {
							
							var customField = data.customField;
							layui.form.val('param', {
								"customFieldID" : customField.customFieldID,
								"customFieldCode" : customField.customFieldCode,
								"createUserCode" : customField.createUserCode,
								"createUserName" : customField.createUserName,
								"createDate" : assemblys.dateToStr(customField.createDate)
							});
							
							$(dom).attr("customFieldCode", customField.customFieldCode);
							
							var customFieldName = assemblys.htmlEncode(pars.customFieldName);
							
							// 必填
							if (pars.isNecessField == "1") {
								customFieldName = '<span style="color: red;">* </span>' + customFieldName;
							}
							// 业务编号
							if (pars.businessCode) {
								customFieldName += " - <span style='color:#A0A0A0;' title='业务编号'>" + assemblys.htmlEncode(pars.businessCode) + "</span>";
							}
							// 公用
							if (pars.isCommon == "1") {
								customFieldName += ' <i class="layui-icon2" title="公用组件" style="font-size: 14px; color: red; margin-right:3px;">&#xe7b3;</i>';
							}
							// 关联
							var hasRelation = $(dom).attr("hasRelation");
							if (hasRelation == "1") {
								customFieldName += ' <i class="layui-icon layui-icon-share" title="被关联" style="font-size: 14px; color: blue; margin-right:3px;"></i>';
							}
							// 手机端
							if (pars.isMobile == "1") {
								customFieldName += ' <i class="layui-icon2" title="该组件已开启【移动端】上报" style="font-size: 14px; color: #818181; margin-right:3px;">&#xe77c;</i>';
							}
							// 只读
							if (pars.isRead == "1") {
								customFieldName += ' <i class="layui-icon2" title="该组件已开启【只读】" style="font-size: 16px; color: blue; margin-right:3px;">&#xe806;</i>';
							}
							// 只读
							if (pars.isRichText == "1") {
								customFieldName += ' <i class="layui-icon2" title="该组件已开启【富文本】" style="font-size: 15px; color: blue; margin-right:3px;">&#xe728;</i>';
							}
							// 更新显示
							$(dom).html(customFieldName);
							
							// 文本框和文本域
							if (customFieldSet == "text" || (customFieldSet == "textarea" && pars.isRichText == "0")) {
								// 提示语
								$(dom).parents(".move-div").find(".move-div-div input,textarea").attr("placeholder", pars.remindText || "未设置提示语");
							}
							
							// 其他操作
							editCustomField.showAddSubButton(pars.customFieldSet, true);
						});
						
					}
				});
			}
			
		});
	},
	preview : function() {
		location.href = basePath + "frame/customForm/toCustomFieldTemplate.spring?customFormCode=" + $("#customFormCode").val() + "&customFieldBusinessCode=" + $("input[name='businessCode']").val() + "&compNo=" + compNo + "&customFormFilledCode=CFFt4D9C2C1797824E6E86CD84FC123E56AC";
	},
	initAppFuns : function() {
		$.ajax({
			url : basePath + "frame/newCustomForm/getAppFunList.spring",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : appCode
			},
			dataType : "json",
			async : false,
			success : function(data) {
				var appFunList = data.appFunList;
				var $funCode = $("select[name='funCode']");
				for (var i = 0; i < appFunList.length; i++) {
					$funCode.append('<option value="' + appFunList[i].funCode + '">' + appFunList[i].funName + '</option>');
				}
				layui.form.render();
			}
		});
	}
}