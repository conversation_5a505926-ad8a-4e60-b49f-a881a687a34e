var hasSubmit = false;

var editCustomModular = {
	init : function() {
		// 获取隐藏的class
		parent.customForm.addCustomFormClassStyle(document.getElementsByTagName("head")[0]);
		editCustomModular.getCustomModular();
		editCustomModular.initForm();
	},
	getCustomModular : function() {
		if (param.get("customModularCode")) {
			$.ajax({
				url : basePath + "frame/newCustomForm/getCustomModular.spring",
				dataType : "json",
				data : {
					"customModularCode" : param.get("customModularCode"),
					"appCode" : param.get("appCode")
				},
				success : function(data) {
				
					var customFormCode = param.get("customFormCode");
					param.set(null, data.customModular);
					param.set(null, data.commonCustomModular);
					if (customFormCode) {
						param.set("customFormCode", customFormCode);
					}
					
					// 当前不是公用，激活按钮
					if (data.customModular.isCommon == 0) {
						$("#addCommon").removeClass("layui-hide");
					} else {
						param.set("compNo", "");
					}
					
				}
			});
		}
	},
	initForm : function() {
		
		layui.form.verify({
			businessCode : function(value, item) { //value：表单的值、item：表单的DOM对象
				if (value && editCustomModular.checkCustomModularBusinessCode(value)) {
					return "已存在相同的业务编号";
				}
			}
		});
		
		layui.form.on('submit(save)', function(data) {
			editCustomModular.saveCustomModular();
		});
	},
	checkCustomModularBusinessCode : function(value) {
		var result = false;
		$.ajax({
			url : basePath + "frame/newCustomForm/checkCustomModularBusinessCode.spring",
			data : {
				"compNo" : param.get("compNo"),
				"businessCode" : value,
				"customModularCode" : param.get("customModularCode"),
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			async : false,
			success : function(data) {
				result = data.has;
			}
		});
		return result;
	},
	saveCustomModular : function() {
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		var url = basePath + "frame/newCustomForm/saveCustomModular.spring";
		$.ajax({
			type : "post",
			url : url,
			data : param.__form(),
			dataType : "json",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					assemblys.closeWindow();
				});
				// 更新分类
				var $table_right = parent.$(parent.customForm.dom);
				
				// 分类名称
				var customModularName = assemblys.htmlEncode(param.get("customModularName"));
				// 业务编号
				var businessCode = param.get("businessCode");
				if (businessCode) {
					customModularName += "<span style='color:#A0A0A0;'> - " + businessCode + "</span>";
				}
				$table_right.find(".table_right_title").children("span.tableName").html(customModularName);
				
				var stateText = "";
				var $tableNameState = $table_right.find(".table_right_title").children("span.tableNameState");
				
				// 如果公用
				if (param.get("isCommon") == 1) {
					stateText += '<i class="layui-icon2" title="公用分类" style="font-size: 16px; color: red;">&#xe7b3;</i>';
				}
				// 如果新增
				if (param.get("hasAdd") == 1) {
					stateText += '<i class="layui-icon2" title="可以条件多个分类" style="font-size: 14px; color: green;">&#xe755;</i>';
				}
				// 如果表格风填报
				if (param.get("isTable") == 1) {
					stateText += '<i class="layui-icon2" title="表格风填报" style="font-size: 16px; color: #818181;">&#xe79f;</i>';
				}
				// 如果表格风填报
				if ($tableNameState.attr("hasRelation") == 1) {
					stateText += '<i class="layui-icon layui-icon-share" title="被关联" style="font-size: 12px; color: blue;"></i>';
				}
				$tableNameState.html(stateText);
				
				// 如果是新增
				if (!param.get("customModularCode")) {
					// 写入code
					$table_right.attr("customModularCode", data.customModularCode);
					$table_right.attr("customModularCodeTemp", "");
					// 创建行
					var table_right_main = $table_right.find("table.table_right_main")[0];
					parent.customForm.createRow(table_right_main, 1);
				}
				$table_right.attr("businessCode",businessCode);
			}
		});
	},
	setCommon : function() {
		assemblys.confirm("你确定要升级成公用分类吗？该操作不可逆", function() {
			$("input[name='isCommon']").val(1);
			param.set("compNo", "");
			$("#addCommon").addClass("layui-hide");
			assemblys.msg("保存后生效");
		});
	}
}