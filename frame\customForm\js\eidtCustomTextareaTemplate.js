$(function() {
	layui.use([ 'form' ], function() {
		var form = layui.form;
		//自定义表单校验
		form.verify({
			//长度校验
			limit : function(value, item) { //value：表单的值、item：表单的DOM对象
				var limit = $(item).attr("limit");
				if (value.replace(/\n/g, "aaaa").length > limit) {
					return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
				}
			}
		});
		
		form.on('submit(save)', function(data) {
			eidtCustomTextareaTemplate.saveCustomTextareaTemplate();
			return false;
		});
	});
	
	if (param.get("customTextareaTemplateID") != "") {
		eidtCustomTextareaTemplate.getCustomTextareaTemplate();
	}
});

var eidtCustomTextareaTemplate = {
	saveCustomTextareaTemplate : function() {
		if (hasSubmit) {
			return;
		}
		hasSubmit = true;
		var url = basePath + "frame/newCustomForm/saveCustomTextareaTemplate.spring";
		$.ajax({
			url : url,
			type : "post",
			data : $("#form1").serialize(),
			dataType : "json",
			success : function(data) {
				assemblys.msg("保存成功", function() {
					assemblys.closeWindow();
				});
			}
		});
		
	},
	getCustomTextareaTemplate : function() {
		$.ajax({
			url : basePath + "frame/newCustomForm/getCustomTextareaTemplate.spring",
			dataType : "json",
			data : {
				"customTextareaTemplateID" : param.get("customTextareaTemplateID"),
				"appCode" : param.get("appCode")
			},
			success : function(data) {
				param.set(null, data.customTextareaTemplate);
			}
		});
	}
}
