;
(function(window) {
	var layui = window.layui;
	var assemblys = window.assemblys;
	if (!layui || !assemblys) {
		alert("缺少layui相关组件（assemblys.js）");
		return;
	}
	window.getCustomFormDetail = {
		/**
		 * 获取表单详情
		 */
		getCustomFormData: function(path, customFormCode, customFormFilledCode, appCode, dom, customFormBusinessCode, compNo, showLabel) {

			var attaCallback = null;
			var hasAtta = "";
			if (Object.prototype.toString.call(path) == '[object Object]') {
				customFormCode = path.customFormCode;
				customFormFilledCode = path.customFormFilledCode;
				appCode = path.appCode;
				dom = path.dom;
				customFormBusinessCode = path.customFormBusinessCode;
				attaCallback = path.attaCallback;
				hasAtta = path.hasAtta || "0";
				compNo = path.compNo;
				showLabel = path.showLabel;
				path = path.path || window.basePath;
			}

			return $.ajax({
				url: path + "frame/newCustomForm/getCustomFormData.spring",
				dataType: "json",
				data: {
					"customFormCode": customFormCode,
					"customFormBusinessCode": customFormBusinessCode,
					"compNo": compNo,
					"customFormFilledCode": customFormFilledCode,
					"appCode": appCode,
					"isDetail": true,
					"showLabel": showLabel
				},
				skipDataCheck: true,
				success: function(data) {
					if (data.result == "success") {

						// 附件
						var attaList = getCustomFormDetail.getAttaList(customFormFilledCode);

						var table = "<ul class='layui-nav layui-nav-tree left'   lay-filter='" + dom + "UL' >";

						var pushAttaCallBack = function() {
							var li = '<li  class="layui-nav-item layui-nav-itemed subject eventDetail" style="display: block;">';
							li += '<a href="javascript:;" class="main_table_title skin-div-css">附件信息</a>';
							li += '<dl class="layui-nav-child main_table_box"><dd><table class="layui-table main_table detail_table"><tbody>';
							li += '<tr class="layui-table-tr" >';
							li += '	 <td class="tleft" colspan="1">附件列表</td>';
							li += '	 <td class="tleft" colspan="3" style="color: #009688;">';
							for (var i = 0; i < attaList.length; i++) {
								var atta = attaList[i];
								var title = atta.title;
								var url = atta.url;
								li += ' <p title="' + title + '" onclick="getCustomFormDetail.downLoadAttaPreview(\'' + title + '\',\'' + url + '\');" style="cursor: pointer;"><i class="layui-icon layui-icon-download-circle i_icon" title="点击下载"></i> <span>' + title + '</span></p> ';
							}
							li += '	 </td>';
							li += '</tr>';
							li += '</tbody></table></dd></dl></li>';
							table += li;
						}

						// 处理附件
						if (hasAtta == "prev" && attaList.length > 0) {
							pushAttaCallBack();
						}
						var customModularList = data.data.customModularList;
						for (var i = 0; i < customModularList.length; i++) {
							var customModular = customModularList[i];
							if (JSON.stringify(customModular.detailValues) != "{}") {
								var li = '<li  class="layui-nav-item layui-nav-itemed subject eventDetail" style="display: block;">';
								li += '<a href="javascript:;" class="main_table_title skin-div-css">' + customModular.customModularName + '</a>';
								li += '<dl class="layui-nav-child main_table_box"><dd style="width: 100%; overflow: auto;"><table class="layui-table main_table detail_table" style="table-layout: fixed;" >';

								var thWidths = {};
								var textAligns = {};
								var verticalAligns = {};

								for (var j = 0; j < customModular.maxIndex + 1; j++) {
									// 默认分类风格显示
									if (customModular.isTable == 0) {
										li += '<tbody>';
										if (customModular.hasAdd) {
											li += '<tr  class="layui-table-tr"  ><td class="tleft" colspan="4" style="color: #009688;">' + customModular.customModularName + '-' + (j + 1) + '</td>';
											li += '</tr>';
										}
										var customFieldList = customModular.customFieldList;
										for (var k = 0; k < customFieldList.length;) {
											var customField = customFieldList[k];
											var customFieldName = customField.customFieldCode + "-" + j;
											
											if (customField.customFieldSet == "label" && showLabel) {
												customFieldName = customField.customFieldCode;
											}
											var value = customModular.detailValues[customFieldName] || "";

											var isOneCol = customField.customFieldSet == "textarea";
											if (customField.customFieldSet != "label") {
												if (!customModular.hasAdd && !value) {
													k++;
													continue;
												}
											}
											var next, nextFieldName, nextValue, nextIsOneCol;
											while ((next = customFieldList[k + 1])) {
												nextFieldName = customFieldList[k + 1].customFieldCode + "-" + j;
												if (customFieldList[k + 1].customFieldSet == "label" && showLabel) {
													nextFieldName = customFieldList[k + 1].customFieldCode;
												}
												nextValue = customModular.detailValues[nextFieldName] || "";
												nextIsOneCol = next.customFieldSet == "textarea";
												if (nextIsOneCol) {
													break;
												}
												if (!customModular.hasAdd && !nextValue && customFieldList[k + 1].customFieldSet != "label") {
													k++;
													continue;
												} else {
													break;
												}

											}

											if (customField.customFieldSet == "label" && showLabel) {
												if (customField.customFieldName && !value) {
													li += '<tr class="layui-table-tr" ><td class="tleft" colspan="4" >' + customField.customFieldName + '</td></tr>';
												} else {
													li += '<tr class="layui-table-tr" ><td class="tright" colspan="1" >' + customField.customFieldName + '</td>';
													li += '<td class="tleft" colspan="3" >' + assemblys.htmlDecode(value) + '</td></tr>';
												}
												k++;
												continue;
											}

											li += '<tr  class="layui-table-tr" >';

											var colspan = !isOneCol && !nextIsOneCol && next ? 1 : 3;
											if(next){
												if(next.customFieldSet == "label" && !nextValue){
													colspan = 3;
												}
											}
											if (value || customModular.hasAdd) {
												li += '<td class="tright" colspan="1" title="' + customField.customFieldName + '">' + customField.customFieldName + '</td>';
												if (customField.isRichText == 1) {
													li += '<td class="tleft" colspan="' + colspan + '">' + value + '</td>';
												} else if (customField.customFieldSet == "profile") {
													li += '<td style="text-align: left;" colspan="' + colspan + '"><img style="max-width:60px;height:70px;" width="60" src="' + value + '" /></td>';
												} else {
													li += '<td class="tleft" colspan="' + colspan + '" id="' + customField.customFieldCode + "_" + j + '"  fieldbusinessCode="'+ customField.businessCode +'"   >' + assemblys.htmlDecode(value) + '</td>';
												}
											}
											if(next){
												if(next.customFieldSet == "label" && !nextValue){
												k++;
												continue;
											}
											}
											

											if (!isOneCol && !nextIsOneCol && next && (nextValue || customModular.hasAdd )) {
												li += '<td class="tright" colspan="1" title="' + next.customFieldName + '">' + next.customFieldName + '</td>';
												if (customField.isRichText == 1) {
													li += '<td class="tleft" colspan="1"  id="' + next.customFieldCode + "_" + j + '" fieldbusinessCode="'+ next.businessCode +'" >' + nextValue + '</td>';
												} else if (next.customFieldSet == "profile") {
													li += '<td style="text-align: left;" colspan="1"><img style="max-width:60px;height:70px;"  width="60" src="' + nextValue + '" /></td>';
												} else {
													li += '<td class="tleft" colspan="1"  id="' + next.customFieldCode + "_" + j + '"  fieldbusinessCode="'+ next.businessCode +'" >' + assemblys.htmlDecode(nextValue) + '</td>';
												}
												k = k + 2;
											} else if (isOneCol || nextIsOneCol) {
												k++;
											} else if (next) {
												if(next.customFieldSet == "label"){
													k++;
												}
											} else {
												k = customFieldList.length;
											}
											li += '</tr>';
										}
										li += '</tbody>';
									} else {
										var ths = '';
										var tds = '';
										// 表头
										if (j == 0) {
											ths += '<thead>';
											ths += '	<tr  class="layui-table-tr" >';
											if (customModular.hasAdd == 1) {
												ths += '<th style="background:#f2f2f2;text-align:center;" width="50" >序号</th>';
											}
											tds += '<tbody>';
										}
										// 表体
										tds += '	<tr  class="layui-table-tr" >';

										// 表格风显示
										var customFieldList = customModular.customFieldList;
										for (var k = 0; k < customFieldList.length; k++) {
											var customField = customFieldList[k];
											var customFieldName = customField.customFieldCode + "-" + j;
											if (customField.customFieldSet == "label" && showLabel) {
												customFieldName = customField.customFieldCode;
											}
											var value = customModular.detailValues[customFieldName] || "";
											// 表头
											if (j == 0) {

												// 宽度
												var thWidth = "160";
												if (customField.customFieldSet == "textarea" && customField.isRichText == 1) {
													thWidth = "240";
												} else if (customField.customFieldSet == "textarea" || customField.customFieldSet == "radio" || customField.customFieldSet == "checkbox") {
													thWidth = "200";
												}
												thWidths[k] = thWidth;

												// 左右居中
												var textAlign = "left";
												if (customField.customFieldSet == "datetime") {
													textAlign = "center";
												}
												textAligns[k] = textAlign;

												// 上下居中
												var verticalAlign = "middle";
												if (customField.customFieldSet == "radio" || customField.customFieldSet == "checkbox" || customField.customFieldSet == "label" || customField.customFieldSet == "select") {
													verticalAlign = "initial";
												}
												verticalAligns[k] = verticalAlign;

												ths += '<th style="background:#f2f2f2;;text-align:' + textAlign + ';" width="' + thWidth + '" >' + customField.customFieldName + '</th>';

											}

											// 如果开启了新增
											if (customModular.hasAdd == 1) {
												if (k == 0) {
													tds += '<td style="vertical-align:middle;text-align:center;" width="50" >' + (j + 1) + '</td>';
												}
											}

											var handleValue = "";
											if (customField.isRichText == 1) {
												handleValue = value;
											} else if (customField.customFieldSet == "profile") {
												handleValue = '<img style="max-width:60px;height:70px;" width="60" src="' + value + '" />';
											} else {
												handleValue = assemblys.htmlDecode(value);
											}
											tds += '<td style="vertical-align:' + verticalAligns[k] + ';text-align:' + textAligns[k] + ';" width="' + thWidths[k] + '" >' + handleValue + '</td>';
										}

										// 表头
										if (j == 0) {
											ths += '	</tr>';
											ths += '</thead>';
											li += ths;
										}
										// 表体
										tds += '</tr>';
										if (j == customModular.maxIndex) {
											tds += '</tbody>';
										}
										li += tds;

									}
								}
								li += '</table></dd></dl></li>';
								table += li;
							}
						}
						table += "</ul>";

						// 处理附件
						if (hasAtta == "after" && attaList.length > 0) {
							pushAttaCallBack();
						}

						$("#" + dom).html(table);
						layui.element.render("nav", dom + "UL");

						// 加载图片显示
						$("#" + dom).find("img").on("click", function() {
							var url = $(this).attr("src");
							var alt = $(this).attr("alt");
							layer.photos({
								photos: {
									"title": "", //相册标题
									"data": [{ //相册包含的图片，数组格式
										"alt": alt,
										"src": url
									}]
								}
							});
						});

						// 无数据
						if ($("#" + dom).find("li").length == 0) {
							table = "<ul class='layui-nav layui-nav-tree left'   lay-filter='" + dom + "UL' >";
							table += '	<li class="layui-nav-item subject eventDetail layui-nav-itemed" ><a href="javascript:;" class="main_table_title skin-div-css">表单信息<span class="layui-nav-more"></span></a><dl class="layui-nav-child main_table_box"><dd><table class="layui-table main_table detail_table"><tbody><tr><td style="text-align: center;" >暂无数据</td></tr></tbody></table></dd></dl></li>';
							table += "</ul>";
							$("#" + dom).html(table);
							layui.element.render("nav", dom + "UL");
						}
						$("ul[lay-filter='formDetailUL'] li").each(function(i, o) {
							if ($(this).find("tr").length == 0) {
								$(this).css("display", "none");
							}
						})
						if (attaCallback && typeof attaCallback == 'function') {
							//  加载附件
							attaCallback(attaList);
						}
						// 获取附件
						getCustomFormDetail.getAttachments(customFormFilledCode);

					} else {
						assemblys.alert("加载表单失败");
					}
				}
			});
		},
		getAttachments: function(customFormFilledCode) {
			if (customFormFilledCode) {
				return $.ajax({
					url: basePath + "frame/fileUpload/getAttachments.spring",
					data: {
						"belongToCode": customFormFilledCode,
						"belongToSubCode": "all"
					},
					dataType: "json",
					skipDataCheck: true,
					success: function(data) {
						var attaList = data.attachmentsList || [];

						var attaMap = {};
						if (attaList.length > 0) {
							for (var l = 0; l < attaList.length; l++) {
								var atta = attaList[l];
								if (!attaMap[atta.belongToSubCode]) {
									attaMap[atta.belongToSubCode] = [atta];
								} else {
									attaMap[atta.belongToSubCode].push(atta);
								}
							}
						}

						// 回显
						for (var key in attaMap) {
							getCustomFormDetail.setFileList(attaMap[key], key);
						}
					}
				});
			}
		},
		// 回显文件列表
		setFileList: function(result, id) {
			var fileHtml = "";
			for (var i = 0; i < result.length; i++) {
				var title = result[i].title || result[i].attaName;
				var url = result[i].url || result[i].attaUrl;
				var size = result[i].size || result[i].attaSize;
				var type = result[i].type || result[i].attaType;
				fileHtml += "<p style='display: flex;'>";
				fileHtml += "	<em title=\"" + title + "\"><img style='width:16px;' title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + title + "</em>";
				var suffix = type.toUpperCase();
				if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
					fileHtml += "	<span style='color:blue;cursor: pointer;margin-left: 5px;min-width: 33px;' onclick=\"getCustomFormDetail.preview('" + title + "','" + url + "');\"  >预览</span>";
				}
				fileHtml += "	<span style='color:blue;cursor: pointer;margin-left: 5px;min-width: 33px;' onclick=\"getCustomFormDetail.downLoadAttaPreview('" + title + "','" + url + "');\">下载</span>";
				fileHtml += "</p>";
			}
			$("#" + id).html(fileHtml);
		},
		// 预览图片
		preview: function(imgName, src) {
			var url = basePath + "/frame/fileUpload/downloadFile.spring?eifAttaUrl=" + src + "&eifAttaName=" + encodeURIComponent(imgName);
			layer.photos({
				photos: {
					"title": "", //相册标题
					"data": [ //相册包含的图片，数组格式
						{
							"alt": imgName,
							"src": url
						}]
				}
			});
		},
		downLoadAttaPreview: function(eifAttaName, eifAttaUrl) {
			$("#downloadFileForm").remove();
			var form = "<form id='downloadFileForm' style='display:none;' ></form>";
			$("body").append(form);
			var url = basePath + "/frame/fileUpload/downloadFile.spring?eifAttaUrl=" + eifAttaUrl + "&eifAttaName=" + encodeURIComponent(eifAttaName);
			document.getElementById("downloadFileForm").action = url;
			document.getElementById("downloadFileForm").method = "post";
			document.getElementById("downloadFileForm").submit();
		},
		getAttaList: function(customFormFilledCode) {
			var list = [];
			$.ajax({
				url: basePath + "frame/fileUpload/getAttachments.spring",
				type: "get",
				async: false,
				data: {
					"belongToCode": customFormFilledCode,
				},
				dataType: "json",
				skipDataCheck: true,
				success: function(data) {
					list = data.attachmentsList;
				}
			});
			return list;
		},
		/**
		 * 编辑表单
		 */
		editCustomForm: function(data, callback) {
			//上报完成后回调
			window.saveCallback = {
				save: function(win) {
					if (callback && typeof callback == 'function') {
						callback(win);
					}
				}
			}
			// 表单编辑 - 来自【应用接口管理 - toCustomFormTemplateHTML 接口】
			var url = basePath + "frame/customForm/customFormTemplate.html?hasClose=1&type=2";
			url += "&customFormCode=" + data.customFormCode;
			url += "&appCode=" + data.appCode;
			url += "&customFormFilledCode=" + data.customFormFilledCode;
			url += "&customFormBusinessCode=" + data.customFormBusinessCode;
			url += "&compNo=" + data.compNo;
			layer.open({
				type: 2,
				skin: 'layui-layer-aems',
				title: false,
				scrollbar: false,
				closeBtn: 0,
				area: ['98%', '98%'],
				content: url
			});

		},
		/**
		 * 显示表单修改日志
		 */
		showCustomFormLog: function(relationCode, appCode) {
			$.ajax({
				url: basePath + "frame/newCustomForm/getCustomFormLogList.spring",
				type: "get",
				data: {
					"relationCode": relationCode,
					"appCode": appCode
				},
				dataType: "json",
				success: function(data) {
					if (data.customFormLogList.length > 0) {
						getCustomFormDetail.initTableListWin(data.customFormLogList);
					} else {
						assemblys.msg("暂无表单修改记录");
					}
				}
			});
		},
		initTableListWin: function(list) {
			// 打开窗口
			var id = "SHOW_NEW_LOG_WINDOW_" + new Date().getTime();
			layer.open({
				type: 1,
				skin: 'layui-layer-aems',
				title: "表单修改详情",
				scrollbar: false,
				area: ["80%", "80%"],
				content: "<div id='" + id + "' style='padding: 10px;'></div>"
			});

			var ths = [];
			var tbs = [];
			var mapping = [{
				name: "修改字段",
				value: "customFieldName"
			}, {
				name: "修改前",
				value: "beforeValue",
				templet: function(data) {
					var showPoto = "";
					// 富文本
					if (data.isRichText == 1) {
						showPoto = data.beforeValue;
					} else if (data.customFieldSet == "profile") {
						showPoto = "<img src='" + data.beforeValue + "' />";
					} else {
						showPoto = assemblys.htmlDecode(data.beforeValue);
					}
					return showPoto;
				}
			}, {
				name: "修改后",
				value: "afterValue",
				templet: function(data) {
					var showPoto = "";
					// 富文本
					if (data.isRichText == 1) {
						showPoto = data.afterValue;
					} else if (data.customFieldSet == "profile") {
						showPoto = "<img src='" + data.afterValue + "' />";
					} else {
						showPoto = assemblys.htmlDecode(data.afterValue);
					}
					return showPoto;
				}
			}, {
				name: "修改人",
				value: "optUserName"
			}, {
				name: "修改时间",
				value: "optDate"
			}];
			// 有数据
			if (list.length > 0) {
				for (var i = 0; i < list.length; i++) {
					var temp = list[i];
					var trs = {
						"tagName": "tr",
						"children": []
					};
					for (var j = 0; j < mapping.length; j++) {
						var map = mapping[j];
						// 头
						if (i == 0) {
							ths.push({
								"tagName": "th",
								"style": {
									"text-align": "center"
								},
								"innerHTML": map.name
							})
						}
						// 特殊处理
						var value = temp[map.value] || "";
						if ((typeof value === 'number') && value > 1000000000000) {
							value = assemblys.dateToStr(value);
						} else if (value instanceof Date) {
							value = assemblys.dateToStr(value.getTime());
						} else if (value instanceof Object && value.time) {
							value = assemblys.dateToStr(value.time);
						}
						// 操作按钮
						var opts = []
						var optList = map.opt || [];
						if (map.opt instanceof Array) {
							value = "";
							for (var w = 0; w < optList.length; w++) {
								var opt = optList[w];
								var hasShow = true;
								if (opt.show && typeof opt.show == 'function') {
									hasShow = opt.show(temp);
								}
								if (hasShow) {
									opts.push({
										"tagName": "i",
										"className": "layui-icon2 " + (opt.className || ""),
										"innerHTML": opt.icon + " ",
										"attr": {
											"row1": i,
											"row2": j,
											"index": w
										},
										"onclick": function() {
											var _optList = mapping[$(this).attr("row2")].opt;
											var _onclick = _optList[$(this).attr("index")].onclick;
											if (_onclick && typeof _onclick == 'function') {
												var _temp = list[$(this).attr("row1")];
												_onclick(_temp);
											}
										}
									})
								}
							}
						}
						// 自定义
						var templet = map.templet;
						if (templet && typeof templet == 'function') {
							value = map.templet(temp);
						}
						// 内容
						trs["children"].push({
							"tagName": "td",
							"style": (j == 0 ? {
								"text-align": "center",
								"width": "100px"
							} : {}),
							"innerHTML": value,
							"children": opts
						})
					}
					tbs.push(trs);
				}
			} else {
				var index = 0;
				// 无数据
				for (var j = 0; j < mapping.length; j++) {
					var map = mapping[j];
					ths.push({
						"tagName": "th",
						"style": {
							"text-align": "center"
						},
						"innerHTML": map.name
					});
					index++;
				}
				tbs.push({
					"tagName": "tr",
					"children": [{
						"tagName": "td",
						"attr": {
							"colspan": index
						},
						"style": {
							"text-align": "center"
						},
						"innerHTML": "无相关数据",
						"children": []
					}]
				})
			}
			// 组装
			var table = {
				"tagName": "table",
				"className": "layui-table",
				"children": [{
					"tagName": "thead",
					"children": [{
						"tagName": "tr",
						"children": ths
					}]
				}, {
					"tagName": "tbody",
					"children": tbs
				}]
			}
			assemblys.createElement(table, $("#" + id)[0]);

			// 加载图片显示
			$("#" + id).find("img").on("click", function() {
				var url = $(this).attr("src");
				layer.photos({
					photos: {
						"title": "", //相册标题
						"data": [{ //相册包含的图片，数组格式
							"src": url
						}]
					}
				});
			});

		},
		downLoadAttaPreview: function(eifAttaName, eifAttaUrl) {
			$("#downloadFileForm").remove();
			var form = "<form id='downloadFileForm' style='display:none;' ></form>";
			$("body").append(form);
			var url = basePath + "/frame/fileUpload/downloadFile.spring?eifAttaUrl=" + eifAttaUrl + "&eifAttaName=" + encodeURIComponent(eifAttaName);
			document.getElementById("downloadFileForm").action = url;
			document.getElementById("downloadFileForm").method = "post";
			document.getElementById("downloadFileForm").submit();
		},
		getCustomFormFilled: function(customFormFilledCode, appCode) {
			return $.ajax({
				url: basePath + "frame/newCustomForm/getCustomFormFilled.spring",
				data: {
					"customFormFilledCode": customFormFilledCode,
					"appCode": appCode
				},
				dataType: "json",
				success: function(data) {
					param.set(null, data.customFormFilled);
				}
			});
		}
	};
})(window)
