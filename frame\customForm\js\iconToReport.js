var iconToReport = {
	init : function() {
		iconToReport.getFormList();
	},
	//获取表单列表
	getFormList : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomFormList.spring",
			data : param.__form(),
			success : function(data) {
				//渲染加载界面
				iconToReport.initTreeAndTable(data);
			}
		});
	},
	//渲染加载界面
	initTreeAndTable : function(data) {
		//列表显示
		$(".rightCon").removeClass("layui-hide").empty();
		// 表单信息
		var rightHtml = "<div class=\"table table-hover enventShowTabs\">";
		rightHtml += "<div class=\"panels\">";
		rightHtml += "<div class=\"panel\" >";
		if (!data.customFormList || data.customFormList.length == 0) {
			rightHtml += "<p>";
			rightHtml += "<i class=\"layui-icon layui-icon-file\" style=\"color: #009688;\"></i>";
			rightHtml += "<a href=\"javascript:void(0)\">";
			rightHtml += "<span style=\"font-size: 14px;\"> 无相关数据！ </span>";
			rightHtml += "</a>";
			rightHtml += "</p>";
		} else {
			for (var i = 0; i < data.customFormList.length; i++) {
				var item = data.customFormList[i];
				rightHtml += "<p ";
				if ((i + 1) % 2 == 0) {
					rightHtml += " class=\"eventDataValue\" ";
				}
				rightHtml += " eventDataValue=\"" + item.customFormTypeCode + "\">";
				rightHtml += "<i class=\"layui-icon layui-icon-file\" style=\"color: #009688;\"></i>";
				rightHtml += "<a href=\"javascript:void(0)\" onclick=\"iconToReport.toFromView('" + assemblys.escape(item.customFormCode) + "')\" title=\"" + assemblys.escape(item.customFormName) + "\">";
				rightHtml += "<i class=\"fa fa-fw fa-file\"></i>";
				rightHtml += "<span style=\"font-size: 14px;\">" + assemblys.escape(item.customFormName) + "</span>";
				rightHtml += "</a>";
				rightHtml += "</p>";
			}
		}
		rightHtml += "</div>";
		$(".rightCon").html(rightHtml);
	},
	toFromView : function(customFormCode) {
		// 返回HTML形式 
		var url = basePath + "frame/customForm/customFormTemplate.html?" + param.__form() + "&customFormCode=" + customFormCode;
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : false,
			scrollbar : false,
			closeBtn : 0,//右上角的×按钮
			area : [ '100%', '100%' ],
			content : url
		});
		
	}
}
//上报完成后回调
var saveCallback = {
	init : function(win) {
		assemblys.msg("提交成功！", function() {
			win.assemblys.closeWindow();
		});
	}
}
