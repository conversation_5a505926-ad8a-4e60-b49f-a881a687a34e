var initCustomForm = {
	isIE8 : layui.device().ie == 8,
	trUtil : assemblys.createElement({
		"tagName" : "i",
		"className" : "layui-icon2 row_icon layui-hide",
		"innerHTML" : "&#xe779;"
	}, document.createElement("div")).children[0],
	rowUL : assemblys.createElement({
		"tagName" : "ul",
		"className" : "col_set layui-hide",
		"onmouseleave" : function() {
			$(this).addClass("layui-hide");
		},
		"children" : [ {
			attr : {
				cols : "1"
			},
			"tagName" : "li",
			"className" : "col_1",
			"innerText" : "一行一列"
		}, {
			attr : {
				cols : "2"
			},
			"tagName" : "li",
			"className" : "col_2 setCustomFormClass",
			"innerText" : "一行二列"
		}, {
			attr : {
				cols : "3"
			},
			"tagName" : "li",
			"className" : "col_3 setCustomFormClass",
			"innerText" : "一行三列"
		}, {
			"tagName" : "li",
			"className" : "merge setCustomFormClass",
			"innerText" : "合并",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon2",
				"innerHTML" : "&#xe6fb;",
				"style" : {
					"color" : "green"
				}
			} ]
		}, {
			"tagName" : "li",
			"className" : "col_delete",
			"innerText" : "删除"
		} ]
	}, document.body).lastChild,
	titleUL : assemblys.createElement({
		"tagName" : "ul",
		"className" : "input_set layui-hide",
		"children" : [ {
			"tagName" : "li",
			"className" : "input_set_edit",
			"innerText" : "编辑"
		}, {
			"tagName" : "li",
			"className" : "input_set_delete setCustomFormClass",
			"innerText" : "删除"
		} ]
	}, document.body).lastChild,
	getCustomFormData : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomFormData.spring",
			dataType : "json",
			data : {
				"customFormCode" : customFormCode,
				"appCode" : appCode,
				"isCustomForm" : true
			},
			success : function(data) {
				$("#customFormName").text(data.customForm.customFormName);
				var customModularList = data.customModularList;
				var table_rightAry = [];
				for (var i = 0; i < customModularList.length; i++) {
					table_rightAry.push(initCustomForm.createTable(customModularList[i]));
				}
				
				assemblys.createElement(table_rightAry, $("div.table_box")[0]);
				
			}
		});
	},
	createTable : function(customModular) {
		// 使用key-value方式缓存customField,方便在生成row时同时生成组件
		customModular.customFieldMap = [];
		var customFieldList = customModular.customFieldList || [];
		for (var j = 0; j < customFieldList.length; j++) {
			customModular.customFieldMap[customFieldList[j].customFieldRowCode + "-" + customFieldList[j].seqNo] = customFieldList[j];
		}
		
		var customModularCode = customModular.customModularCode;
		var customModularCodeTemp = customModular.customModularCodeTemp;
		var businessCode = customModular.businessCode || "";
		var customModularName = customModular.customModularName;
		
		var checked = customModular.checked;
		return {
			"attr" : {
				"customModularCodeTemp" : (customModularCodeTemp ? customModularCodeTemp : ''),
				"customModularCode" : (customModularCode ? customModularCode : ''),
				"relationNum" : "0",
				"lay-filter" : "customModular_" + customModularCode + "-0"
			},
			"tagName" : "div",
			"className" : "table_right layui-form",
			"customModular" : customModular,
			"children" : [ {
				"tagName" : "div",
				"className" : "table_right_title skin-div-css",
				"children" : [ {
					"tagName" : "span",
					"className" : "tableName",
					"innerHTML" : assemblys.htmlEncode(customModularName) + (businessCode ? "<span style='color:#A0A0A0;'> - " + businessCode + "</span>" : "")
				}, {
					"tagName" : "span",
					"className" : "tableNameState",
					"attr" : {
						"hasRelation" : (customForm.relationCodeMap[customModular.customModularCode] ? 1 : 0)
					},
					"style" : {
						"margin-left" : "5px",
					},
					"children" : [ customModular.isCommon ? {
						"tagName" : "i",
						"className" : "layui-icon2",
						"title" : "公用分类",
						"innerHTML" : "&#xe7b3;",
						"style" : {
							"font-size" : "16px",
							"color" : "red"
						}
					} : null, customModular.hasAdd ? {
						"tagName" : "i",
						"className" : "layui-icon2",
						"title" : "可以条件多个分类",
						"innerHTML" : "&#xe755;",
						"style" : {
							"font-size" : "14px",
							"color" : "green"
						}
					} : null, customModular.isTable ? {
						"tagName" : "i",
						"className" : "layui-icon2",
						"title" : "表格风填报",
						"innerHTML" : "&#xe79f;",
						"style" : {
							"font-size" : "16px",
							"color" : "#818181"
						}
					} : null, customForm.relationCodeMap[customModular.customModularCode] ? {
						"tagName" : "i",
						"className" : "layui-icon layui-icon-share",
						"title" : "被关联",
						"style" : {
							"font-size" : "12px",
							"color" : "blue"
						}
					} : null ]
				}, {
					"tagName" : "i",
					"className" : "layui-icon2 fr",
					"innerHTML" : "&#xe920;"
				} ]
			}, {
				"tagName" : "table",
				"className" : "table_right_main",
				"children" : initCustomForm.createRow(customModular, 0)
			} ]
		};
	},
	createRow : function(customModular, index) {
		var rowAry = [];
		
		var customFieldRowList = customModular.customFieldRowList || [];
		for (var i = 0; i < customFieldRowList.length; i++) {
			rowAry.push(initCustomForm.createTr(customModular, customFieldRowList[i], i));
		}
		
		return rowAry;
	},
	createTr : function(customModular, customFieldRow, index) {
		var customFieldRowCode = customFieldRow.customFieldRowCode;
		var mergeWithNextRow = customFieldRow.mergeWithNextRow;
		
		var elementHasRelation = !!customForm.relationCodeMap[customFieldRowCode];
		return {
			"attr" : {
				"customFieldRowCode" : customFieldRowCode,
				"relationNum" : "0",
				"lay-filter" : "customFieldRow_" + customFieldRowCode + "-" + index
			},
			"tagName" : "tr",
			"customFieldRow" : customFieldRow,
			"className" : "layui-form" + (mergeWithNextRow == 1 ? " mergeWithNextRow" : "") + (elementHasRelation ? " row-has-relation" : ""),
			"onmouseenter" : function(e) {
				$(initCustomForm.trUtil).removeClass("layui-hide").appendTo($(this).children("td:last"))
			},
			"children" : initCustomForm.createTd(customModular, customFieldRow, index)
		};
	},
	createTd : function(customModular, customFieldRow, index) {
		var customFieldMap = customModular ? customModular.customFieldMap : {};
		var customFieldRowCode = customFieldRow.customFieldRowCode;
		var cols = customFieldRow.cols;
		var tdAry = [];
		for (var i = 0; i < cols; i++) {
			var customField = customFieldMap[customFieldRowCode + "-" + i];
			tdAry.push({
				"attr" : {
					"colspan" : 12 / cols
				},
				"tagName" : "td",
				"children" : [ customField ? initCustomForm.getFieldSet(customModular, customFieldRow, customField, index) : null ]
			});
		}
		return tdAry;
	},
	getFieldSet : function(customModular, customFieldRow, customField, index) {
		return {
			"tagName" : "form",
			"className" : "moveInput",
			"customField" : customField,
			"children" : [ {
				tagName : "div",
				className : "move-div",
				children : [ {
					"attr" : {
						"customFieldSet" : customField.customFieldSet,
						"customFieldCode" : customField.customFieldCode,
						"hasRelation" : customFieldRow && customFieldRow.customFieldRowCode && customForm.relationCodeMap[customFieldRow.customFieldRowCode] ? "1" : "0"
					},
					"tagName" : "label",
					"className" : "layui-form-label item_label",
					"children" : [ customField.isNecessField == 1 ? {
						"tagName" : "span",
						"innerText" : "* ",
						"style" : {
							"color" : "red"
						},
					} : null, {
						"tagName" : "span",
						"innerHTML" : customField.customFieldName + (customField.businessCode ? " - <span style='color:#A0A0A0;' title='业务编号'>" + assemblys.htmlEncode(customField.businessCode) + "</span>" : "")
					}, {
						"tagName" : "span",
						"innerHTML" : "&nbsp;"
					}, customField.isCommon ? {
						"tagName" : "i",
						"className" : "layui-icon2",
						"title" : "公用组件",
						"innerHTML" : "&#xe7b3;",
						"style" : {
							"font-size" : "14px",
							"color" : "red",
							"margin-right" : "3px"
						}
					} : null, customFieldRow && customFieldRow.customFieldRowCode && customForm.relationCodeMap[customFieldRow.customFieldRowCode] ? {
						"tagName" : "i",
						"className" : "layui-icon layui-icon-share",
						"title" : "被关联",
						"style" : {
							"font-size" : "14px",
							"color" : "blue",
							"margin-right" : "3px"
						}
					} : null, customField.isMobile == 1 ? {
						"tagName" : "i",
						"className" : "layui-icon2",
						"title" : "该组件已开启【移动端】上报",
						"innerHTML" : "&#xe77c;",
						"style" : {
							"font-size" : "14px",
							"color" : "#818181",
							"margin-right" : "3px"
						}
					} : null, customField.isRead == 1 ? {
						"tagName" : "i",
						"className" : "layui-icon2",
						"title" : "该组件已开启【只读】",
						"innerHTML" : "&#xe806;",
						"style" : {
							"font-size" : "16px",
							"color" : "blue",
							"margin-right" : "3px"
						}
					} : null, customField.isRichText == 1 ? {
						"tagName" : "i",
						"className" : "layui-icon2",
						"title" : "该组件已开启【富文本】",
						"innerHTML" : "&#xe728;",
						"style" : {
							"font-size" : "15px",
							"color" : "blue",
							"margin-right" : "3px"
						}
					} : null ]
				}, {
					"tagName" : "br"
				}, {
					tagName : "div",
					className : "move-div-div",
					children : [ initCustomForm[customField.customFieldSet](customModular, customField, index) ]
				} ]
			} ]
		};
	},
	radio : function(customModular, customField, index) {
		return {
			"tagName" : "div",
			"className" : "layui-unselect layui-form-radio layui-form-radioed layui-form-draft",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-anim layui-icon",
				"innerHTML" : "&#xe643;"
			}, {
				"tagName" : "div",
				"innerText" : "单选框"
			} ]
		};
	},
	checkbox : function(customModular, customField, index) {
		return {
			"attr" : {
				"lay-skin" : "primary"
			},
			"tagName" : "div",
			"className" : "layui-unselect layui-form-checkbox layui-form-checked layui-form-draft",
			"style" : {
				"display" : "inline-block",
				"width" : "auto"
			},
			"children" : [ {
				"tagName" : "span",
				"innerText" : "多选框"
			}, {
				"tagName" : "i",
				"className" : "layui-icon",
				"style" : {
					"margin-top" : "0px"
				},
				"innerHTML" : "&#xe605;"
			} ]
		};
	},
	label : function(customModular, customField, index) {
		return {
			"tagName" : "span",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon2 layui-icon-draft",
				"innerHTML" : "&#xe714;"
			}, {
				"tagName" : "span",
				"innerText" : " 标签"
			} ]
		};
	},
	select : function(customModular, customField, index) {
		return {
			"tagName" : "div",
			"className" : "layui-input-inline",
			"children" : [ {
				"tagName" : "div",
				"className" : "layui-unselect layui-form-select",
				"children" : [ {
					"tagName" : "div",
					"className" : "layui-select-title",
					"children" : [ {
						"attr" : {
							"placeholder" : "下拉框",
							"readonly" : "readonly"
						},
						"tagName" : "input",
						"className" : "layui-input layui-unselect",
						"type" : "text"
					}, {
						"tagName" : "i",
						"className" : "layui-edge"
					} ]
				} ]
			} ]
		};
		
	},
	org : function(customModular, customField, index) {
		return [ {
			"attr" : {
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input input_item layui-input-draft",
			"type" : "text"
		}, {
			"tagName" : "span",
			"innerHTML" : "&nbsp;"
		}, {
			"tagName" : "i",
			"className" : "layui-icon",
			"innerHTML" : "&#xe615;"
		} ];
		
	},
	"interface" : function(customModular, customField, index) {
		return [ {
			"attr" : {
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input input_item layui-input-draft",
			"type" : "text"
		}, {
			"tagName" : "i",
			"className" : "layui-icon2",
			"innerText" : ""
		} ];
	},
	img : function(customModular, customField, index) {
		return {
			"tagName" : "i",
			"className" : "layui-icon layui-icon-draft",
			"innerHTML" : "&#xe64a;"
		};
	},
	textarea : function(customModular, customField, index) {
		var remindText = "未设置提示语";
		if (customField.remindText) {
			remindText = customField.remindText;
		}
		if (customField.isRichText == 1) {
			remindText = "";
		}
		return {
			"attr" : {
				"readonly" : "readonly",
				"placeholder" : remindText
			},
			"tagName" : "textarea",
			"className" : "layui-textarea"
		};
	},
	text : function(customModular, customField, index) {
		return {
			"attr" : {
				"readonly" : "readonly",
				"placeholder" : customField.remindText || "未设置提示语"
			},
			"tagName" : "input",
			"className" : "layui-input input_item layui-input-draft",
			"type" : "text"
		};
		
	},
	datetime : function(customModular, customField, index) {
		return {
			"tagName" : "span",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-icon-draft",
				"innerHTML" : "&#xe637;"
			}, {
				"tagName" : "span",
				"innerText" : " 日期"
			} ]
		};
	},
	profile : function(customModular, customField, index) {
		return {
			"tagName" : "div",
			"children" : [ {
				"tagName" : "img",
				"style" : {
					"width" : "60px",
					"height" : "60px"
				},
				"src" : basePath + "frame/images/tx.png",
				"className" : "layui-profile"
			} ]
		};
	},
	file : function(customModular, customField, index) {
		return {
			"tagName" : "i",
			"className" : "layui-icon2",
			"innerHTML" : "&#xe7af;"
		};
	}
}