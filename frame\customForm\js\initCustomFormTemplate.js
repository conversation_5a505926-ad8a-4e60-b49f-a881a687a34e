var initCustomFormTemplate = {
	numberModular : [],
	// IE验证
	isIE8 : layui.device().ie == 8,
	// 获取数据
	getCustomFormData : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomFormData.spring",
			dataType : "json",
			data : {
				"customFormBusinessCode" : customFormBusinessCode,
				"compNo" : compNo,
				"customFormCode" : customFormCode,
				"customFormFilledCode" : customFormFilledCode,
				"appCode" : appCode
			},
			success : function(data) {
				customFormTemplate.customForm = data.customForm;
				$("#customFormName").text(data.customForm.customFormName);
				param.set("customFormCode", data.customForm.customFormCode);
				
				var customModularList = data.customModularList;
				var table_rightAry = [];
				for (var i = 0; i < customModularList.length; i++) {
					// 使用key-value方式缓存customField,方便在生成row时同时生成组件
					customModularList[i].customFieldMap = [];
					for (var j = 0; j < customModularList[i].customFieldList.length; j++) {
						customModularList[i].customFieldMap[customModularList[i].customFieldList[j].customFieldRowCode + "-" + customModularList[i].customFieldList[j].seqNo] = customModularList[i].customFieldList[j];
					}
					table_rightAry.push(initCustomFormTemplate.createTable(customModularList[i]));
				}
				
				var $tableBox = $("div.table_box");
				var formDoms = {};
				assemblys.createElement(table_rightAry, $tableBox[0], formDoms);
				for (var i = 0; i < customModularList.length; i++) {
					initCustomFormTemplate.setValue(null, customModularList[i].reportValues, null, formDoms);
				}
				
				initCustomFormTemplate.initDatetime($tableBox);
				
				baseImgPath = data.baseImgPath;
				
				// 加载模板
				customFormTemplate.getCustomTextareaTemplateList('');
				
				// 加载富文本
				customFormTemplate.loadRichText();
				
			}
		});
	},
	createTable : function(customModular) {
		var customModularCode = customModular.customModularCode;
		var customModularName = customModular.customModularName;
		var isTable = customModular.isTable;
		var checked = customModular.checked;
		return {
			"attr" : {
				"customModularCode" : (customModularCode ? customModularCode : ''),
				"relationNum" : "0",
				"lay-filter" : "customModular_" + customModularCode + "-0",
				"isTable" : isTable,
				"customModularBussinessCode" : customModular.businessCode || ""
			},
			"tagName" : "div",
			"className" : "table_right layui-form",
			"style" : {
				"display" : (customFormTemplate.hasOptionRelation(customModularCode) && !checked ? 'none' : null)
			},
			"customModular" : customModular,
			"children" : [ {
				"tagName" : "div",
				"className" : "table_right_title skin-div-css",
				"onclick" : function(e) {
					var $table_right_main = $(this).nextAll();
					if ($table_right_main.hasClass("layui-hide")) {
						$table_right_main.removeClass("layui-hide");
						$(this).children("i").html("&#xe920;");
						// 定位
						$(".bodys").animate({
							scrollTop : ($(".bodys").scrollTop() + $table_right_main.offset().top) - 90
						}, 300);
					} else {
						$table_right_main.addClass("layui-hide");
						$(this).children("i").html("&#xe922;");
					}
				},
				"children" : [ {
					"tagName" : "span",
					"className" : "tableName",
					"innerText" : customModularName
				}, {
					"tagName" : "i",
					"className" : "layui-icon2 fr",
					"innerHTML" : "&#xe920;"
				}, {
					"tagName" : "input",
					"type" : "hidden",
					"name" : "customModularCode",
					"value" : customModularCode
				} ]
			}, {
				"tagName" : "div",
				"className" : "table_right_main_container",
				"children" : [ {
					"tagName" : "table",
					"className" : "table_right_main",
					"children" : initCustomFormTemplate.createRow(customModular, 0)
				} ]
			}, !customModular.hasAdd ? null : {
				"tagName" : "table",
				"className" : "table_right_main",
				"style" : {
					"width" : "100%"
				},
				"children" : [ {
					"tagName" : "tr",
					"className" : "layui-form",
					"onclick" : function() {
						customModular.reportValues = {};
						var rowAry = initCustomFormTemplate.addCommonCustomModular(customModular);
						var $table = $(this).parents("table").prev().find("table");
						assemblys.createElement(rowAry, $table[0]);
						initCustomFormTemplate.initDatetime($table, customModular.count);
						initCustomFormTemplate.setValue(null, customModular.reportValues);
						// 加载富文本
						customFormTemplate.loadRichText();
						// 加载文件上传
						customFormTemplate.initFileUpload();
					},
					"children" : [ {
						"attr" : {
							"colspan" : 12
						},
						"tagName" : "td",
						"className" : "addCommoncustomModularTd",
						"style" : {
							"textAlign" : "center",
							"height" : "auto",
							"cursor" : "pointer",
							"fontWeight" : 600,
							"color" : "#009688",
							"position" : "relative"
						},
						"children" : [ {
							"tagName" : "i",
							"className" : "layui-icon layui-icon-add-circle"
						}, {
							"tagName" : "span",
							"innerText" : " 点击新增【" + customModular.customModularName + "】"
						} ]
					} ]
				} ]
			} ]
		};
	},
	addCommonCustomModular : function(customModular) {
		if (customModular.count) {
			customModular.count++;
		} else {
			customModular.count = 1;
		}
		
		return initCustomFormTemplate.createRow(customModular, customModular.count);
	},
	createRow : function(customModular, index) {
		var isTable = customModular.isTable;
		var rowAry = [];
		// 头部分类标识
		if (customModular.hasAdd && isTable == 0) {
			rowAry.push(initCustomFormTemplate.getCommonCustomModularFirstTr(customModular, index));
		}
		
		// 组件部分
		var customFieldRowList = customModular.customFieldRowList;
		// 表格风填报
		if (isTable == 1) {
			var trList = initCustomFormTemplate.createTableTd(customModular, customFieldRowList, index);
			for (var i = 0; i < trList.length; i++) {
				rowAry.push(trList[i]);
			}
		} else {
			for (var i = 0; i < customFieldRowList.length; i++) {
				rowAry.push(initCustomFormTemplate.createTr(customModular, customFieldRowList[i], index));
			}
		}
		
		if (customFormFilledCode && customModular.hasAdd && index == 0) {
			for (var i = 0; i < customModular.maxIndex; i++) {
				rowAry.push(initCustomFormTemplate.addCommonCustomModular(customModular));
			}
		}
		
		return rowAry;
	},
	createTr : function(customModular, customFieldRow, index) {
		var customFieldRowCode = customFieldRow.customFieldRowCode;
		var checked = customFieldRow.checked;
		var mergeWithNextRow = customFieldRow.mergeWithNextRow;
		return {
			"attr" : {
				"customFieldRowCode" : customFieldRowCode,
				"relationNum" : "0",
				"lay-filter" : "customFieldRow_" + customFieldRowCode + "-" + index
			},
			"tagName" : "tr",
			"className" : "layui-form" + (mergeWithNextRow == 1 ? " mergeWithNextRow" : ""),
			"style" : {
				"display" : customFormTemplate.hasOptionRelation(customFieldRowCode) && !checked ? "none" : null
			},
			"children" : initCustomFormTemplate.createTd(customModular, customFieldRow, index)
		};
	},
	createTd : function(customModular, customFieldRow, index) {
		var customFieldMap = customModular.customFieldMap;
		var customFieldRowCode = customFieldRow.customFieldRowCode;
		var cols = customFieldRow.cols;
		var tdAry = [];
		for (var i = 0; i < cols; i++) {
			var customField = customFieldMap[customFieldRowCode + "-" + i]
			tdAry.push({
				"attr" : {
					"colspan" : 12 / cols
				},
				"tagName" : "td",
				"children" : [ customField ? initCustomFormTemplate[customField.customFieldSet](customModular, customField, index) : null ]
			});
		}
		return tdAry;
	},
	// 表格风填报 - 身体
	createTableTd : function(customModular, customFieldRowList, index) {
		var hasAdd = customModular.hasAdd;
		// 所有行
		var trs = [];
		// 表头
		if (index == 0) {
			var ths = [ {
				"tagName" : "th",
				"style" : {
					"width" : "50px"
				},
				"innerText" : "操作"
			} ];
			
			// 新增时，才有操作栏
			if (hasAdd == 0) {
				ths = [];
			}
			
		}
		// 组件
		var tds = [ {
			"tagName" : "td",
			"style" : {
				"text-align" : "center",
				"vertical-align" : "middle"
			},
			"children" : [ {
				"tagName" : "i",
				"style" : {
					"font-size" : "15px",
					"cursor" : "pointer"
				},
				"className" : "layui-icon layui-icon-delete i_delete",
				"onclick" : function() {
					
					if ($("div.table_right[customModularCode='" + customModular.customModularCode + "']").find("[customFieldRowCode][lay-filter$='-1']").length == 0) {
						assemblys.msg("至少要保留一组信息！");
						return;
					}
					
					var $tr = $(this).parents("tr");
					
					var thisIndex = $tr.find("span[index]").attr("index") - 1;
					
					$tr.nextAll("tr[customFieldRowCode][lay-filter$='-" + thisIndex + "']").hide(500, function() {
						$(this).remove();
					});
					setTimeout(function() {
						var $trs = $tr.nextAll().each(function(i, e) {
							var $this = $(this);
							var filter = $this.attr("lay-filter");
							if (filter) {
								var layFilter = filter.split("-");
								$this.attr("lay-filter", layFilter[0] + "-" + (parseInt(layFilter[1]) - 1));
							}
						});
						
						$trs.find("[lay-filter]").each(function(i, e) {
							var layFilter = $(this).attr("lay-filter").split("-");
							$(this).attr("lay-filter", layFilter[0] + "-" + (parseInt(layFilter[1]) - 1));
						});
						
						$trs.find("[name]").each(function(i, e) {
							var name = this.name.split("-");
							this.name = name[0] + "-" + (parseInt(name[1]) - 1);
						});
						
						$trs.find("span[index]").each(function(j, e) {
							var $this = $(this);
							var text = $this.text();
							var i = parseInt(text) - 1;
							$this.text(i);
							$this.attr("index", i);
						});
						
						$tr.remove();
					}, 501);
					
					customModular.count--;
					
				}
			} ]
		} ];
		
		// 新增时，才有操作栏
		if (hasAdd == 0) {
			tds = [];
		}
		
		// 处理组件
		for (var i = 0; i < customFieldRowList.length; i++) {
			var customFieldRow = customFieldRowList[i];
			var customFieldMap = customModular.customFieldMap;
			var customFieldRowCode = customFieldRow.customFieldRowCode;
			var checked = customFieldRow.checked;
			var cols = customFieldRow.cols;
			for (var j = 0; j < cols; j++) {
				var customField = customFieldMap[customFieldRowCode + "-" + j]
				if (customField) {
					// 表头
					if (index == 0) {
						var thWidth = "260";
						if (customField.customFieldSet == "textarea" && customField.isRichText == 1) {
							thWidth = "500";
						} else if (customField.customFieldSet == "textarea" || customField.customFieldSet == "radio" || customField.customFieldSet == "checkbox") {
							thWidth = "360";
						}
						ths.push({
							"tagName" : "th",
							"width" : thWidth,
							"innerHTML" : (customField.isNecessField == 1 ? '<span style="color: red;">* </span>' : "") + customField.customFieldName
						});
					}
					// 左右居中
					var textAlign = "center";
					if (customField.customFieldSet == "radio" || customField.customFieldSet == "checkbox" || customField.customFieldSet == "label") {
						textAlign = "left";
					}
					// 上下居中
					var verticalAlign = "middle";
					if (customField.customFieldSet == "radio" || customField.customFieldSet == "checkbox" || customField.customFieldSet == "label" || customField.customFieldSet == "select") {
						verticalAlign = "";
					}
					// 组件
					tds.push({
						"tagName" : "td",
						"style" : {
							"text-align" : textAlign,
							"vertical-align" : verticalAlign
						},
						"children" : [ {
							"tagName" : "div",
							"attr" : {
								"customFieldRowCode" : customFieldRowCode,
								"relationNum" : "0",
								"lay-filter" : "customFieldRow_" + customFieldRowCode + "-" + index
							},
							"style" : {
								"display" : customFormTemplate.hasOptionRelation(customFieldRowCode) && !checked ? "none" : null
							},
							"children" : [ initCustomFormTemplate[customField.customFieldSet](customModular, customField, index) ]
						} ]
					})
				}
			}
		}
		// 第一个时才需要表头
		if (index == 0) {
			trs.push({
				"tagName" : "tr",
				"children" : ths
			});
		}
		// 组件
		trs.push({
			"tagName" : "tr",
			"children" : tds
		});
		return trs;
	},
	getCommonCustomModularFirstTr : function(customModular, index) {
		return {
			"tagName" : "tr",
			"className" : "layui-form",
			"children" : [ {
				"attr" : {
					"colspan" : 12
				},
				"tagName" : "td",
				"className" : "addCommonCustomModularTd",
				"style" : {
					"height" : "auto",
					"font-weight" : 600,
					"color" : "#009688"
				},
				"children" : [ {
					"tagName" : "i",
					"className" : "layui-icon2",
					"innerHTML" : "&#xe6fe;",
					"style" : {
						"cursor" : "pointer",
						"color" : "red"
					},
					"onclick" : function() {
						
						if ($("div.table_right[customModularCode='" + customModular.customModularCode + "']").find("tr[customFieldRowCode][lay-filter$='-1']").length == 0) {
							assemblys.msg("至少要保留一组信息！");
							return;
						}
						var $tr = $(this).parents("tr");
						
						var thisIndex = $tr.find("span[index]").attr("index") - 1;
						
						$tr.nextAll("tr[customFieldRowCode][lay-filter$='-" + thisIndex + "']").hide(500, function() {
							$(this).remove();
						});
						
						var $trs = $tr.nextAll().each(function(i, e) {
							var $this = $(this);
							var filter = $this.attr("lay-filter");
							if (filter) {
								var layFilter = filter.split("-");
								$this.attr("lay-filter", layFilter[0] + "-" + (parseInt(layFilter[1]) - 1));
							}
						});
						
						$trs.find("[lay-filter]").each(function(i, e) {
							var layFilter = $(this).attr("lay-filter").split("-");
							$(this).attr("lay-filter", layFilter[0] + "-" + (parseInt(layFilter[1]) - 1));
						});
						
						$trs.find("[name]").each(function(i, e) {
							var name = this.name.split("-");
							this.name = name[0] + "-" + (parseInt(name[1]) - 1);
						});
						
						$trs.find("span[index]").each(function(j, e) {
							var $this = $(this);
							var text = $this.text();
							var i = parseInt(text) - 1;
							$this.text(i);
							$this.attr("index", i);
						});
						
						$tr.remove();
						
						customModular.count--;
					}
				}, {
					"tagName" : "span",
					"innerText" : " " + customModular.customModularName + "-"
				}, {
					"attr" : {
						"index" : index + 1
					},
					"tagName" : "span",
					"innerText" : index + 1
				} ]
			} ]
		};
	},
	getHoveDiv : function(customModular, customField, index) {
		var hoveDivAry = [];
		
		// 是否所有一级选项都没有二级选项,如果都没有,一级选项横向排列
		var hasSecond = false;
		for ( var i in customField.fieldData) {
			if (customField.fieldData[i].childOptionList.length > 0) {
				hasSecond = true;
				break;
			}
		}
		
		for ( var i in customField.fieldData) {
			var oneOption = customField.fieldData[i];
			var customFieldSet = oneOption.customFieldSet.replace("Other", "");
			var hoveDiv = {
				"tagName" : "div",
				"className" : "layui-hove",
				"style" : {
					"width" : hasSecond ? null : "auto",
					"display" : hasSecond ? null : "inline-block"
				},
				"children" : [ {
					"attr" : {
						"divlevel" : "1"
					},
					"tagName" : "div",
					"children" : customFieldSet == "label" ? [ {
						"tagName" : "span",
						"className" : "field_customFieldSet_label",
						"innerText" : oneOption.customOptionSetContent
					} ] : [ {
						"attr" : {
							"level" : "1",
							"lay-ignore" : "true",
							"optionremark" : oneOption.remark,
							//"customFieldBusinessCode": customField.businessCode,
							//"customFieldBusinessValue": customField.businessValue,
							"customOptionSetBusinessCode" : oneOption.businessCode,
							"customOptionSetBusinessValue" : oneOption.businessValue,
							"lay-verify" : (customField.isNecessField == 1 ? "hasChildrenRequired" : ""),
							"customOptionSetContent" : oneOption.customOptionSetContent,
							"radio-checked" : (oneOption.hasDefault == 1 && !customFormFilledCode && customFieldSet == "radio" ? "true" : "")
						},
						"tagName" : "input",
						"className" : "layui-hide",
						"type" : customFieldSet,
						"value" : oneOption.customOptionSetCode,
						"name" : customField.customFieldCode + "-" + index,
						"checked" : (oneOption.hasDefault == 1 && !customFormFilledCode ? initCustomFormTemplate.addDefaultValue(customModular, customField.customFieldCode, oneOption.customOptionSetCode, index) : false)
					}, ("radio" == customFieldSet) ? initCustomFormTemplate.getRadio(customField, oneOption, index) : initCustomFormTemplate.getCheckbox(customField, oneOption, index) ]
				}, (oneOption.childOptionList.length > 0) ? {
					"tagName" : "div",
					"className" : "second" + (oneOption.hasDefault == 1 && !customFormFilledCode ? " second_show" : ""),
					"children" : initCustomFormTemplate.getSecondOptions(customModular, customField, oneOption, index)
				} : null ]
			};
			
			hoveDivAry.push(hoveDiv);
		}
		
		return hoveDivAry;
	},
	addDefaultValue : function(customModular, code, customOptionSetCode, index) {
		var name = code + "-" + index;
		var reportValues = customModular.reportValues[name];
		if (!reportValues) {
			reportValues = [];
		}
		reportValues.push(customOptionSetCode);
		customModular.reportValues[name] = reportValues;
	},
	getSecondOptions : function(customModular, customField, oneOption, index) {
		var secondDivAry = [];
		for ( var j in oneOption.childOptionList) {
			var twoOption = oneOption.childOptionList[j];
			customFieldSet = twoOption.customFieldSet.replace("Other", "");
			var childOption = {
				"customOptionSetContent" : twoOption.customOptionSetContent,
				"customFieldSet" : twoOption.customFieldSet,
				"customOptionSetCode" : twoOption.customOptionSetCode
			};
			
			var twoDiv = {
				"attr" : {
					"divlevel" : "2",
					"customOptionSetCode" : oneOption.customOptionSetCode
				},
				"tagName" : "div",
				"className" : "layui-form",
				"children" : (customFieldSet == "label") ? [ {
					"tagName" : "span",
					"className" : "field_customFieldSet_label",
					"innerText" : twoOption.customOptionSetContent
				} ] : [ {
					"attr" : {
						"level" : "2",
						"lay-ignore" : "true",
						"optionremark" : twoOption.remark,
						//"customFieldBusinessCode": customField.businessCode,
						//"customFieldBusinessValue": customField.businessValue,
						"customOptionSetBusinessCode" : twoOption.businessCode,
						"customOptionSetBusinessValue" : twoOption.businessValue,
						"radio-checked" : (twoOption.hasDefault == 1 && !customFormFilledCode && customFieldSet == "radio" ? "true" : "")
					},
					"tagName" : "input",
					"className" : "layui-hide",
					"type" : customFieldSet,
					"name" : twoOption.parentCustomOptionSetCode + "-" + index,
					"value" : twoOption.customOptionSetCode,
					"checked" : (twoOption.hasDefault == 1 && !customFormFilledCode ? initCustomFormTemplate.addDefaultValue(customModular, twoOption.parentCustomOptionSetCode, twoOption.customOptionSetCode, index) : false)
				}, ("radio" == customFieldSet) ? initCustomFormTemplate.getRadio(customField, childOption, index) : initCustomFormTemplate.getCheckbox(customField, childOption, index) ]
			};
			
			secondDivAry.push(twoDiv);
		}
		
		return secondDivAry;
	},
	// 单选
	getRadio : function(customField, oneOption, index) {
		
		return {
			"tagName" : "div",
			"className" : "layui-unselect layui-form-radio",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-anim layui-icon layui-icon-circle"
			}, {
				"tagName" : "div",
				"className" : "third",
				"children" : [ {
					"tagName" : "label",
					"className" : "item_label_radio_field",
					"innerText" : oneOption.customOptionSetContent
				}, ("radioOther" != oneOption.customFieldSet) ? null : {
					"tagName" : "div",
					"className" : "layui-form-input-text",
					"children" : [ {
						"attr" : {
							"lay-verify" : (customField.isNecessField == 1 ? "hasCheckboxTextRequired|limit" : "limit"),
							"customOptionSetBusinessCode" : oneOption.businessCode + "_text",
							"customOptionSetBusinessValue" : oneOption.businessValue,
							"limit" : "200"
						},
						"tagName" : "input",
						"className" : "layui-input input_item input_one item1",
						"type" : "text",
						"name" : oneOption.customOptionSetCode + "_text-" + index
					} ]
				} ]
			} ]
		};
	},
	// 多选
	getCheckbox : function(customField, oneOption, index) {
		return {
			"attr" : {
				"lay-skin" : "primary"
			},
			"tagName" : "div",
			"className" : "layui-unselect layui-form-checkbox",
			"children" : [ {
				"tagName" : "span",
				"children" : [ {
					"tagName" : "label",
					"className" : "item_label_checkbox_field",
					"innerText" : oneOption.customOptionSetContent,
					"style" : {
						"maxWidth" : "none"
					}
				}, ("checkboxOther" != oneOption.customFieldSet) ? null : {
					"attr" : {
						"lay-verify" : (customField.isNecessField == 1 ? "hasCheckboxTextRequired|limit" : "limit"),
						"customOptionSetBusinessCode" : oneOption.businessCode + "_text",
						"customOptionSetBusinessValue" : oneOption.businessValue,
						"limit" : "200"
					},
					"tagName" : "input",
					"className" : "layui-input input_item input_one item1",
					"type" : "text",
					"name" : oneOption.customOptionSetCode + "_text-" + index
				} ]
			}, {
				"tagName" : "i",
				"className" : "layui-icon layui-icon-ok"
			} ]
		};
	},
	radio : function(customModular, customField, index) {
		return initCustomFormTemplate.getCheckboxOrRadioDiv(customModular, customField, index);
	},
	checkbox : function(customModular, customField, index) {
		return initCustomFormTemplate.getCheckboxOrRadioDiv(customModular, customField, index);
	},
	getCheckboxOrRadioDiv : function(customModular, customField, index) {
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet,
				"customFieldBusinessCode" : customField.businessCode,
				"customFieldBusinessValue" : customField.businessValue,
			},
			"tagName" : "div",
			"className" : "layui-form moveInput",
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"children" : [ (customField.isNecessField == 1) ? {
					"tagName" : "span",
					"innerText" : "*",
					"title" : "请填写" + customField.customFieldName,
					"style" : {
						"color" : "red"
					}
				} : null, {
					"tagName" : "span",
					"className" : "customFieldName",
					"innerText" : " " + customField.customFieldName
				}, customField.hasFish == 1 && !initCustomFormTemplate.isIE8 ? {
					"tagName" : "img",
					"src" : basePath + "plugins/static/image/zh2.png",
					"className" : "switchFish",
					"onclick" : function() {
						specialTreatment.switchFish($(this).parent().next()[0]);
						$(this).addClass('layui-hide').next().removeClass('layui-hide');
					}
				} : null, customField.hasFish == 1 && !initCustomFormTemplate.isIE8 ? {
					"tagName" : "img",
					"src" : basePath + "plugins/static/image/zh.png",
					"className" : "switchFish layui-hide",
					"onclick" : function() {
						specialTreatment.switchNormal($(this).parent().next()[0]);
						$(this).addClass('layui-hide').prev().removeClass('layui-hide');
					}
				} : null ]
			}, {
				"attr" : {
					"customFieldName" : customField.customFieldName,
					"lay-verify" : (customField.isNecessField == 1 ? "checkboxRequired" : "")
				},
				"tagName" : "div",
				"className" : "layui-form-content",
				"children" : initCustomFormTemplate.getHoveDiv(customModular, customField, index)
			} ]
		};
	},
	getLabel : function(customModular, customField, index) {
		var divAry = [];
		var optionList = customField.fieldData;
		for ( var i in optionList) {
			var option = optionList[i];
			var div = {
				"tagName" : "div",
				"className" : "layui-hove",
				"attr" : {
					"customOptionSetBusinessCode" : option.businessCode,
					"customOptionSetBusinessValue" : option.businessValue
				},
				"children" : [ {
					"tagName" : "p",
					"className" : "layui-word-aux",
					"innerText" : option.customOptionSetContent
				} ]
			};
			divAry.push(div);
		}
		return divAry;
	},
	label : function(customModular, customField, index) {
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet,
				"customFieldBusinessCode" : customField.businessCode,
				"customFieldBusinessValue" : customField.businessValue
			},
			"tagName" : "div",
			"className" : "moveInput",
			"customField" : customField,
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"innerText" : " " + customField.customFieldName
			}, {
				"tagName" : "div",
				"className" : "item_label_tags",
				"children" : initCustomFormTemplate.getLabel(customModular, customField, index)
			} ]
		}
	},
	select : function(customModular, customField, index) {
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet,
				"relationField" : customField.relationField
			},
			"tagName" : "div",
			"className" : "layui-form moveInput",
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"children" : [ (customField.isNecessField != 1) ? null : {
					"tagName" : "span",
					"innerText" : "*",
					"style" : {
						"color" : "red"
					}
				}, {
					"tagName" : "span",
					"innerText" : " " + customField.customFieldName
				} ]
			}, initCustomFormTemplate.getSelectHove(customModular, customField, index, "") ]
		};
		
	},
	getSelectHove : function(customModular, customField, index, parentCustomOptionSetCode) {
		return {
			"attr" : {
				"customFieldName" : customField.customFieldName
			},
			"tagName" : "div",
			"className" : "layui-form-select-content layui-hove layui-form-content",
			"style" : {
				"styleFloat" : "left",// IE8
				"cssFloat" : "left",// 其他浏览器
				"display" : "block"
			},
			"children" : [ {
				"attr" : {
					"lay-verify" : (customField.isNecessField == 1 ? 'selectRequired' : ''),
					"customFieldBusinessCode" : customField.businessCode,
					"customFieldBusinessValue" : customField.businessValue,
					"lay-ignore" : "true"
				},
				"tagName" : "select",
				"className" : "layui-hide",
				"name" : customField.customFieldCode + "-" + index,
				"customField" : customField,
				"children" : initCustomFormTemplate.getSelectOption(customModular, customField, parentCustomOptionSetCode, index)
			}, {
				"tagName" : "div",
				"className" : "layui-unselect layui-form-select",
				"children" : [ {
					"tagName" : "div",
					"className" : "layui-select-title",
					"onclick" : function() {
						initCustomFormTemplate.cancelSelect();
						var $div = $(this).parent();
						var $input = $(this).children("input");
						if (!$div.hasClass("layui-form-selected")) {
							$div.addClass("layui-form-selected layui-form-selected-2");
						}
						$input.val("").keyup();
						
						// 扩展高度
						$(this).parents(".table_right[isTable='1']").find("table.table_right_main:eq(0)").css("margin-bottom", $(".layui-form-selected dl").height() - 30 + "px");
					},
					"children" : [ {
						"tagName" : "input",
						"className" : "layui-input layui-unselect",
						"type" : "text",
						"placeholder" : "请选择",
						"value" : "请选择",
						"onkeyup" : function() {
							var $dl = $(this).parent().next();
							var $dd = $dl.children("dd");
							var $p = $dl.children("p");
							
							if (this.value && this.value != this.getAttribute("placeholder")) {
								$dd.addClass("layui-hide").filter("dd[lay-content*='" + this.value.replace(/'/g, "\\'") + "']").removeClass("layui-hide");
							} else {
								$dd.removeClass("layui-hide");
							}
							
							if ($dd.filter("dd:visible").length == 0) {
								$p.removeClass("layui-hide");
							} else if (!$p.hasClass("layui-hide")) {
								$p.addClass("layui-hide");
							}
							
							if (!$dl.parent().hasClass("layui-form-selected")) {
								$dl.parent().addClass("layui-form-selected layui-form-selected-2");
							}
						}
					}, {
						"tagName" : "i",
						"className" : "layui-edge"
					} ]
				}, {
					"tagName" : "dl",
					"className" : "layui-anim layui-anim-upbit",
					"children" : initCustomFormTemplate.getSelectDD(customField, parentCustomOptionSetCode)
				} ]
			} ]
		};
	},
	getSelectOption : function(customModular, customField, parentCustomOptionSetCode, index) {
		var optionAry = [];
		optionAry.push({
			"tagName" : "option",
			"value" : "",
			"innerText" : "请选择"
		});
		var optionList = customField.fieldData;
		if (optionList.length > 0) {
			for ( var i in optionList) {
				var option = optionList[i];
				if (parentCustomOptionSetCode == option.parentCustomOptionSetCode) {
					var optionJson = {
						"attr" : {
							"optionremark" : option.remark,
							//"customFieldBusinessCode": customField.businessCode,
							//"customFieldBusinessValue": customField.businessValue,
							"customOptionSetBusinessCode" : option.businessCode,
							"customOptionSetBusinessValue" : option.businessValue
						},
						"tagName" : "option",
						"value" : option.customOptionSetCode,
						"innerText" : option.customOptionSetContent
					};
					optionAry.push(optionJson);
				}
				if (option.hasDefault == 1) {
					initCustomFormTemplate.addDefaultValue(customModular, customField.customFieldCode, option.customOptionSetCode, index);
					option.hasDefault = 0;
				}
			}
		}
		
		return optionAry;
	},
	getSelectDD : function(customField, parentCustomOptionSetCode) {
		var ddAry = [];
		ddAry.push({
			"attr" : {
				"lay-value" : "",
				"lay-content" : "请选择"
			},
			"tagName" : "dd",
			"innerText" : "请选择"
		});
		var optionList = customField.fieldData;
		if (optionList.length > 0) {
			for ( var i in optionList) {
				var option = optionList[i];
				if (parentCustomOptionSetCode == option.parentCustomOptionSetCode) {
					var dd = {
						"attr" : {
							"lay-value" : option.customOptionSetCode,
							"lay-content" : option.customOptionSetContent,
							"relationField" : option.relationField
						},
						"tagName" : "dd",
						"innerText" : option.customOptionSetContent
					};
					ddAry.push(dd);
				}
			}
		}
		
		ddAry.push({
			"tagName" : "p",
			"className" : "layui-select-none layui-hide",
			"innerText" : "无匹配项"
		});
		return ddAry;
	},
	// 取消选择
	cancelSelect : function() {
		$("div.layui-form-selected").each(function() {
			var $dl = $(this).children("dl");
			var $dds = $dl.children();
			$dl.prev().children("input").val($dds.filter("dd.layui-this").text() || $dds.first().text());
			$(this).removeClass("layui-form-selected");
		});
	},
	org : function(customModular, customField, index) {
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "div",
			"className" : "moveInput",
			"customField" : customField,
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"children" : [ (customField.isNecessField != 1) ? null : {
					"tagName" : "span",
					"title" : "请填写" + customField.customFieldName,
					"style" : {
						"color" : "red"
					},
					"innerText" : "*"
				}, {
					"tagName" : "span",
					"innerText" : " " + customField.customFieldName
				} ]
			}, {
				"attr" : {
					"customFieldName" : customField.customFieldName
				},
				"tagName" : "div",
				"className" : "layui-hove layui-form-content",
				"children" : [ {
					"attr" : {
						"customFieldBusinessCode" : customField.businessCode,
						"customFieldBusinessValue" : customField.businessValue
					},
					"tagName" : "input",
					"type" : "hidden",
					"name" : customField.customFieldCode + "-" + index
				}, {
					"attr" : {
						"lay-verify" : (customField.isNecessField == 1 ? "limit|required" : "limit"),
						"customFieldBusinessCode" : customField.businessCode,
						"customFieldBusinessValue" : customField.businessValue,
						"limit" : "200",
						"readonly" : "readonly"
					},
					"tagName" : "input",
					"className" : "layui-input input_item item1",
					"name" : "remark--" + customField.customFieldCode + "-" + index,
					"maxLength" : 200,
					"autocomplete" : "off",
					"onclick" : function() {
						customFormTemplate.orgSelect(customField.funCode, customField.relationField, this);
					}
				}, {
					"tagName" : "span",
					"innerHTML" : "&nbsp;"
				}, {
					"tagName" : "i",
					"className" : "layui-icon",
					"title" : "选择科室",
					"innerHTML" : "&#xe615;",
					"style" : {
						"cursor" : "pointer"
					},
					"onclick" : function() {
						customFormTemplate.orgSelect(customField.funCode, customField.relationField, $(this).prev().prev()[0]);
					}
				} ]
			} ]
		};
	},
	"interface" : function(customModular, customField, index) {
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "div",
			"className" : "moveInput",
			"customField" : customField,
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"children" : [ (customField.isNecessField != 1) ? null : {
					"tagName" : "span",
					"title" : "请填写" + customField.customFieldName,
					"style" : {
						"color" : "red"
					},
					"innerText" : "*"
				}, {
					"tagName" : "span",
					"innerText" : " " + customField.customFieldName
				} ]
			}, {
				"attr" : {
					"customFieldName" : customField.customFieldName
				},
				"tagName" : "div",
				"className" : "layui-hove layui-form-content",
				"children" : [ {
					"attr" : {
						"lay-verify" : (customField.isNecessField == 1 ? "limit|required" : "limit"),
						"customFieldBusinessCode" : customField.businessCode,
						"customFieldBusinessValue" : customField.businessValue,
						"limit" : "200"
					},
					"tagName" : "input",
					"className" : "layui-input input_item item1",
					"name" : customField.customFieldCode + "-" + index,
					"maxLength" : 200,
					"autocomplete" : "off",
					"onkeyup" : function(event) {
						if (event.keyCode == '13') {
							pubInterface.findInterfaceInfo(this);
						}
					}
				}, {
					"tagName" : "span",
					"innerText" : " "
				}, {
					"tagName" : "i",
					"className" : "layui-icon2",
					"title" : "查询",
					"innerHTML" : "",
					"style" : {
						"cursor" : "pointer"
					},
					"onclick" : function() {
						pubInterface.findInterfaceInfo($(this).prev().prev()[0]);
					}
				} ]
			} ]
		};
	},
	img : function(customModular, customField, index) {
		var imgWidth = customField.fieldData.imgWidth || 0;
		return {
			"attr" : {
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "form",
			"className" : "moveInput",
			"customField" : customField,
			"style" : {
				"position" : "static"
			},
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"innerText" : " " + customField.customFieldName
			}, {
				"tagName" : "div",
				"children" : [ (!customField.fieldData) ? null : {
					"tagName" : "img",
					"title" : customField.fieldData.imgName,
					"src" : baseImgPath + customField.fieldData.imgUrl,
					"style" : {
						"maxWidth" : imgWidth + "px",
						"width" : imgWidth + "px"
					},
				} ]
			} ]
		};
	},
	file : function(customModular, customField, index) {
		var customFieldCode = customField.customFieldCode;
		return {
			"attr" : {
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "div",
			"className" : "moveInput",
			"customField" : customField,
			"style" : {
				"position" : "static"
			},
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label",
				"children" : [ (customField.isNecessField != 1) ? null : {
					"tagName" : "span",
					"title" : "请填写" + customField.customFieldName,
					"style" : {
						"color" : "red"
					},
					"innerText" : "*"
				}, {
					"tagName" : "span",
					"innerText" : " " + customField.customFieldName
				} ]
			}, {
				"tagName" : "div",
				"children" : [ {
					"tagName" : "button",
					"className" : "layui-btn layui-btn-sm custom_file",
					"id" : customFieldCode + "_" + index,
					"attr" : {
						"hasLoad" : "0",
						"type" : "button"
					},
					"innerText" : "点击上传"
				}, {
					"tagName" : "div",
					"style" : {
						"margin-top" : "5px"
					},
					"attr" : {
						"lay-verify" : (customField.isNecessField == 1) ? "fileRequired" : ""
					},
					"id" : customFieldCode + "_" + index + "_custom_file_div",
				} ]
			} ]
		};
	},
	textarea : function(customModular, customField, index) {
		
		// 默认值
		if (param.get("customFormFilledCode") == "") {
			var defaultValue = customField.defaultValue;
			if (defaultValue && defaultValue.length > customField.customFieldLength) {
				defaultValue = defaultValue.substring(0, customField.customFieldLength);
			}
			initCustomFormTemplate.addDefaultValue(customModular, customField.customFieldCode, defaultValue, index);
		}
		
		// 验证
		var attrParam = {
			"lay-verify" : customField.isNecessField == 1 ? ("limit|required|customRegex") : ("limit|customRegex"),
			"limit" : customField.customFieldLength,
			"customFieldBusinessCode" : customField.businessCode,
			"customFieldBusinessValue" : customField.businessValue,
			"customRegex" : customField.fieldVerifyType || ""
		};
		
		// 富文本时
		if (customField.isRichText == 1) {
			attrParam["lay-verify"] = customField.isNecessField == 1 ? ("required") : "";
			attrParam["isRichText"] = "true";
		}
		
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "div",
			"className" : "moveInput layui-form",
			"customField" : customField,
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label",
				"children" : [ {
					"tagName" : "div",
					"className" : "item_label_content",
					"style" : {
						"width" : "90%"
					},
					"children" : [ (customField.isNecessField != 1) ? null : {
						"tagName" : "span",
						"className" : "item_label_hide",
						"title" : "请填写" + customField.customFieldName,
						"style" : {
							"color" : "red"
						},
						"innerText" : "*"
					}, {
						"tagName" : "span",
						"className" : "item_label_hide",
						"innerText" : " " + customField.customFieldName
					}, customField.isRead == 1 || customField.isRichText == 1 ? null : {
						"tagName" : "div",
						"style" : {
							"display" : "inline-block",
							"position" : "relative"
						},
						"children" : [ {
							"tagName" : "span",
							"className" : "item_label_content_opt",
							"onclick" : function(event) {
								layui.stope(event);
								customFormTemplate.showTextareaTemplate(this, customField.customFieldCode);
							},
							"children" : [ {
								"tagName" : "i",
								"className" : "layui-icon layui-icon-form"
							
							}, {
								"tagName" : "span",
								"innerText" : " 选择模板"
							} ]
						}, {
							"tagName" : "div",
							"className" : "module_win item_label_content_template layui-hide",
							"onmouseleave" : function(e) {
								$(this).addClass('layui-hide');
							},
							"children" : [ {
								"tagName" : "div",
								"className" : "win_head",
								"children" : [ {
									"tagName" : "span",
									"innerText" : "新建模板"
								}, {
									"tagName" : "img",
									"className" : "addMode",
									"title" : "新建模板",
									"src" : basePath + "frame/images/add.png",
									"style" : {
										"marginLeft" : "10px",
										"cursor" : "pointer"
									},
									"onclick" : function() {
										customFormTemplate.eidtCustomTextareaTemplate('', customField.customFieldCode);
									}
								} ]
							}, {
								"attr" : {
									"customFieldCode" : customField.customFieldCode
								},
								"tagName" : "ul",
								"className" : "win_ul",
							} ]
						} ]
					} ]
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-form-textarea",
				"children" : [ {
					"attr" : {
						"customFieldName" : customField.customFieldName
					},
					"tagName" : "div",
					"className" : "layui-form-div-textarea layui-form-content",
					"children" : [ {
						"attr" : attrParam,
						"style" : customField.isRichText == 1 ? {
							"height" : "300px"
						} : null,
						"tagName" : "textarea",
						"className" : "layui-textarea",
						"name" : customField.customFieldCode + "-" + index,
						"maxLength" : customField.customFieldLength,
						"autocomplete" : "off",
						"placeholder" : customField.remindText || "",
						"title" : customField.remindText || "",
						"readOnly" : customField.isRead == 1 ? true : false
					} ]
				} ]
			} ]
		};
	},
	text : function(customModular, customField, index) {
		
		// 默认值
		if (param.get("customFormFilledCode") == "") {
			var defaultValue = customField.defaultValue;
			if (defaultValue && defaultValue.length > customField.customFieldLength) {
				defaultValue = defaultValue.substring(0, customField.customFieldLength);
			}
			initCustomFormTemplate.addDefaultValue(customModular, customField.customFieldCode, defaultValue, index);
		}
		//分值自动计算的回显框,不支持新增的分类才有效果
		if (customField.businessValue) {
			if (customField.businessValue.indexOf("total_") > -1 && customModular.hasAdd == 0) {
				initCustomFormTemplate.numberModular.push(customModular.customModularCode + "_" + customField.customFieldCode)
			}
		}
		
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "div",
			"className" : "moveInput",
			"customField" : customField,
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"children" : [ (customField.isNecessField != 1) ? null : {
					"tagName" : "span",
					"title" : "请填写" + customField.customFieldName,
					"style" : {
						"color" : "red"
					},
					"innerText" : "*"
				}, {
					"tagName" : "span",
					"innerText" : " " + customField.customFieldName
				} ]
			}, {
				"attr" : {
					"customFieldName" : customField.customFieldName
				},
				"tagName" : "div",
				"className" : "layui-hove layui-form-content",
				"children" : [ {
					"attr" : {
						"lay-verify" : customField.isNecessField == 1 ? ("limit|required|customRegex") : ("limit|customRegex"),
						"limit" : customField.customFieldLength,
						"customFieldBusinessCode" : customField.businessCode,
						"customFieldBusinessValue" : customField.businessValue,
						"customRegex" : customField.fieldVerifyType || ""
					},
					"tagName" : "input",
					"className" : "layui-input input_item item1",
					"name" : customField.customFieldCode + "-" + index,
					"maxLength" : customField.customFieldLength,
					"autocomplete" : "off",
					"placeholder" : customField.remindText || "",
					"title" : customField.remindText || "",
					"readOnly" : customField.isRead == 1 ? true : false
				} ]
			} ]
		};
		
	},
	datetime : function(customModular, customField, index) {
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "div",
			"className" : "layui-form inline-block moveInput datetimeDiv",
			"customField" : customField,
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"children" : [ (customField.isNecessField != 1) ? null : {
					"tagName" : "span",
					"title" : "请填写" + customField.customFieldName,
					"style" : {
						"color" : "red"
					},
					"innerText" : "*"
				}, {
					"tagName" : "span",
					"innerText" : " " + customField.customFieldName
				} ]
			}, {
				"attr" : {
					"customFieldName" : customField.customFieldName
				},
				"tagName" : "div",
				"className" : "layui-hove layui-form-content",
				"children" : [ {
					"attr" : {
						"lay-verify" : customField.isNecessField == 1 ? "limit|required|" + customField.fieldVerifyType : "limit",
						"limit" : "200",
						"customFieldBusinessCode" : customField.businessCode,
						"customFieldBusinessValue" : customField.businessValue,
						"prevEditTime" : index,
						"fieldVerifyType" : customField.fieldVerifyType
					},
					"tagName" : "input",
					"className" : "layui-input input_item item1",
					"name" : customField.customFieldCode + "-" + index,
					"maxLength" : 200,
					"autocomplete" : "off",
					"customField" : customField
				}, {
					"tagName" : "span",
					"innerText" : " "
				}, {
					"tagName" : "i",
					"className" : "layui-icon layui-icon-date"
				} ]
			} ]
		};
		
	},
	initDatetime : function($dom, index) {
		$dom.find("input[prevEditTime" + (index || index == 0 ? "=" + index : "") + "]").each(function(i, e) {
			var customField = e.customField;
			var fieldVerifyType = customField.fieldVerifyType || "";
			var dateType = fieldVerifyType || "date";
			var max = "9999-12-31 23:59:59";
			var min = "1900-01-01 00:00:00";
			if (customField.dateRange == 0) {
				customField.beginDate = customField.beginDate ? assemblys.dateToStr(customField.beginDate) : "";
				customField.endDate = customField.endDate ? assemblys.dateToStr(customField.endDate) : "";
				min = customField.beginDate || min;
				max = customField.endDate || max;
				if (dateType == "date") {
					min = min.split(" ")[0];
					max = max.split(" ")[0];
				}
			} else if (customField.dateRange == 1) {
				max = new Date().getTime();
			} else if (customField.dateRange == 2) {
				min = new Date().getTime();
			}
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : dateType,
				"max" : max,
				"min" : min,
				format : dateType == "date" ? "yyyy-MM-dd" : "yyyy-MM-dd HH:mm",
				btns : [ 'clear', 'confirm' ],
				ready : function(date) {
					// 可以自定义时分秒
					var now = new Date();
					this.dateTime.hours = now.getHours();
					this.dateTime.minutes = now.getMinutes();
				}
			});
		});
	},
	profile : function(customModular, customField, index) {
		//hwx 2021-12-14 头像框
		var initProUrl = basePath + "frame/images/tx.png";
		var $key = customField.customFieldCode + "-" + index;
		var $porUrlTemp = customModular.reportValues[$key];
		if ($porUrlTemp) {
			initProUrl = $porUrlTemp;
		}
		return {
			"attr" : {
				"customFieldCode" : customField.customFieldCode,
				"customFieldSet" : customField.customFieldSet
			},
			"tagName" : "div",
			"className" : "layui-form inline-block moveInput datetimeDiv",
			"customField" : customField,
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label item_label item_label_hide",
				"innerText" : " " + customField.customFieldName
			}, {
				"tagName" : "div",
				"children" : [ (!customField.fieldData) ? null : {
					"attr" : {
						"profileCode" : customField.businessCode
					},
					"tagName" : "img",
					"title" : "点击更换头像",
					"src" : initProUrl,
					"onclick" : function(event) {
						layui.stope(event);
						pubProfile.initProfile(this);
					},
					"className" : "layui-profilePhoto"
				}, {
					"attr" : {
						"customFieldBusinessCode" : customField.businessCode,
						"customFieldBusinessValue" : customField.businessValue,
					},
					"tagName" : "input",
					"className" : "layui-input input_item item1 layui-hide profilePhotoData",
					"name" : customField.customFieldCode + "-" + index,
					"autocomplete" : "off",
					"customField" : customField,
					"value" : initProUrl
				} ]
			} ]
		};
	},
	setValue : function(key, newVal, dom, formDoms) {
		
		var valObject = newVal;
		if (key) {
			valObject = {};
			valObject[key] = newVal;
		} else if (!(Object.prototype.toString.call(newVal) === '[object Object]')) {
			throw "设置的值必须是一个对象！";
		}
		
		if (!formDoms) {
			formDoms = {};
		}
		
		var $form = dom ? $(dom) : $("form");
		for ( var k in valObject) {
			key = k;
			newVal = valObject[k];
			var $elem;
			if (formDoms[key]) {
				$elem = $(formDoms[key]);
			} else {
				$elem = $form.find("input[name='" + key + "']");
				if ($elem.length == 0) {
					$elem = $form.find("textarea[name='" + key + "']");
					
					if ($elem.length == 0) {
						$elem = $form.find("select[name='" + key + "']");
					}
				}
				
				formDoms[key] = [];
				$elem.each(function(i, element) {
					formDoms[key].push(this);
				});
			}
			
			if ($elem.length > 0) {
				if (Object.prototype.toString.call(newVal) != '[object Array]') {
					newVal = [ newVal ];
				}
				
				var customFieldSet = $elem.parents("div[customFieldSet]").attr("customFieldSet");
				if ($elem.is(":checkbox")) {
					$elem.filter(":checked").each(function() {
						this.checked = false;
						customFormTemplate.showOrHideRelation(this, true);
						customFormTemplate.onChecked($(this));
					});
					for ( var k in newVal) {
						var $e = $elem.filter("[value=" + newVal[k] + "]");
						if ($e.length == 0) {
							$e = $elem.filter("[optionRemark=" + newVal[k] + "]");
							if ($e.length == 0) {
								continue;
							}
						}
						$e.prop("checked", true);
						//						$e.parents("div.second").addClass("second_show");
						customFormTemplate.showOrHideRelation($e[0], true);
						customFormTemplate.onChecked($e);
					}
					
					var $p = $($elem[0].parentNode.parentNode.previousElementSibling).find("input[value=" + key.split("-")[0] + "]");
					if ($p.length > 0) {
						$p.prop("checked", true);
						customFormTemplate.showOrHideRelation($p[0], true);
						customFormTemplate.onChecked($p);
					}
				} else if ($elem.is(":radio")) {
					
					$elem.filter(":checked").each(function() {
						this.checked = false;
						customFormTemplate.showOrHideRelation(this, true);
						customFormTemplate.onChecked($(this));
					}).removeAttr("radio-checked");
					
					for ( var k in newVal) {
						var $e = $elem.filter("[value=" + newVal[k] + "]");
						if ($e.length == 0) {
							$e = $elem.filter("[optionRemark=" + newVal[k] + "]");
							if ($e.length == 0) {
								continue;
							}
						}
						$e.prop("checked", true).attr("radio-checked", "true");
						$e.parent().next().addClass("second_show");
						customFormTemplate.showOrHideRelation($e[0], true);
						customFormTemplate.onChecked($e);
					}
				} else if (customFieldSet == "select" && $elem.is("select")) {
					for ( var m in newVal) {
						$elem = $(formDoms[key]);
						var $select = $elem.eq(m);
						if ($select.length > 0) {
							$select.val($select.find("option[value='" + newVal[m] + "'],option[optionRemark='" + newVal[m] + "']").val()).change();
							var selectElement = customFormTemplate.selectOnSelected($select);
							if (formDoms && selectElement) {
								initCustomFormTemplate.setFormDoms(formDoms, selectElement);
							}
						}
					}
				} else {
					for ( var k in newVal) {
						if ((typeof newVal[k] === 'number') && newVal[k] > 1000000000000) {
							newVal[k] = assemblys.dateToStr(newVal[k]);
						} else if (newVal[k] instanceof Date) {
							newVal[k] = assemblys.dateToStr(newVal[k].getTime());
						} else if (newVal[k] instanceof Object && newVal[k].time) {
							newVal[k] = assemblys.dateToStr(newVal[k].time);
						}
						
						var fieldVerifyType = $elem.eq(k).attr("fieldVerifyType");
						if (fieldVerifyType == "datetime" && newVal[k]) {
							newVal[k] = assemblys.dateToStr(newVal[k]);
							newVal[k] = (newVal[k] + "").substr(0, 16);
						} else if (fieldVerifyType == "date" && newVal[k]) {
							newVal[k] = assemblys.dateToStr(newVal[k]);
							newVal[k] = (newVal[k] + "").substr(0, 10);
						}
						
						$elem.eq(k).val(newVal[k]);
					}
				}
			}
		}
	},
	setFormDoms : function(formDoms, element) {
		if (formDoms && element.name) {
			if (formDoms[element.name]) {
				if (Object.prototype.toString.call(formDoms[element.name]) != '[object Array]') {
					formDoms[element.name] = [ formDoms[element.name] ];
				}
				formDoms[element.name].push(element);
			} else {
				formDoms[element.name] = element;
			}
		}
	},
	events : function() {
		$("div.table_right").on("click", "div.layui-form-checkbox,div.layui-form-radio,dd", function(e) {
			if (this.className.indexOf("layui-form-radio") != -1) {
				var $input = $(this).prev();
				if ($input.attr("lay-ignore") == "true") {
					customFormTemplate.radioOnClick($input);
					customFormTemplate.calculatedScore($input);
				}
			} else if (this.className.indexOf("layui-form-checkbox") != -1) {
				var $input = $(this).prev();
				if ($input.attr("lay-ignore") == "true") {
					customFormTemplate.checkboxOnClick($input);
					customFormTemplate.calculatedScore($input);
				}
			} else if (this.tagName == "DD") {
				if ($(this).hasClass("layui-this")) {
					return;
				}
				
				var $select = $(this).parents("div.layui-form-select").prev();
				if ($select.attr("lay-ignore") == "true") {
					var customFieldSet = $(this).parents("div[customFieldCode]").attr("customFieldSet");
					customFormTemplate.selectOnChange(this, $select);
					customFormTemplate.calculatedScore($select);
				}
			}
		});
		
		// 点击隐藏下拉,同时处理输入框的值
		$(document).on("click", function(e, clear) {
			if (!$(e.target).parent().hasClass("layui-select-title") || clear) {
				initCustomFormTemplate.cancelSelect();
				// 取消扩展高度
				$(e.target).parents(".table_right[isTable='1']").find("table.table_right_main:eq(0)").css("margin-bottom", "");
			}
		});
	},
	/**
	 * 暂时提供给需要加载单一组件的页面使用
	 */
	getCheckboxOrRadioOptions : function(customField, eventID, eventInputID, dom, callback) {
		customField.eventInputID = eventInputID;
		customField.evnetID = eventID;
		$.ajax({
			url : basePath + "aers/customEventForm/getCheckboxOrRadioOptions.spring",
			dataType : "json",
			data : customField,
			success : function(data) {
				if (data.result == "success") {
					var div = initCustomFormTemplate.checkbox(data.customModular, data.customField, 0);
					var formDoms = {};
					var documentFragment = assemblys.createElement(div, null, formDoms);
					
					initCustomFormTemplate.setValue(null, data.customModular.reportValues, null, formDoms);
					$(dom).empty()[0].appendChild(documentFragment);
					
					initCustomFormTemplate.events();
					
					if (callback) {
						callback();
					}
				} else {
					assemblys.alert("获取单选或多选选项信息出错");
				}
				
			},
			error : function(a, b, c) {
				assemblys.alert("获取单选或多选选项信息出错!错误信息:" + c);
			}
		});
		
	},

};
