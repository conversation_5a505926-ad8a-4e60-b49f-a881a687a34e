var typeAndTable = {
	loadType : function(typeList, checkIcon, dom, callback) {
		if (!typeList) {
			return true;
		}
		typeList.unshift({
			typeName : "全部"
		})
		// 事件分类
		if (checkIcon == "1") {//大图标显示
			$(".showListType").addClass("layui-hide");
			$(".showBigIconType").removeClass("layui-hide").empty();
			var html = [];
			var tds = [];
			var trs = [];
			for (var i = 0; i < typeList.length; i++) {
				var type = typeList[i];
				tds.push({
					"tagName" : "td",
					"style" : "padding: 15px;",
					"children" : [ {
						"tagName" : "label",
						"attr" : {
							"typeCode" : (type.typeName == '全部' ? "" : type.typeCode),
							"typeName" : type.typeName,
							"index" : i
						},
						"onclick" : function() {
							if (callback && typeof callback == 'function') {
								var _index = $(this).attr("index");
								var _temp = typeList[_index];
								callback(_temp);
							}
						},
						"children" : [ {
							"tagName" : "div",
							"className" : "typeBox",
							"style" : "cursor: pointer;",
							"children" : [ {
								"tagName" : "div",
								"className" : "typeIco",
								"children" : [ {
									"tagName" : "i",
									"className" : "icon layui-icon" + (type.typeIconType || 2) + " skin-div-font",
									"innerHTML" : (type.typeIcon != null ? type.typeIcon : "&#xe779;"),
								}, {
									"tagName" : "div",
									"className" : "typeName",
									"innerHTML" : assemblys.escape(type.typeName)
								} ]
							} ]
						} ]
					} ]
				});
				if ((i + 0) % 5 == 4 || i == typeList.length - 1) {
					trs.push({
						"tagName" : "tr",
						"children" : tds
					});
					tds = [];
				}
			}
			html.push({
				"tagName" : "table",
				"className" : "typeBoxWrap",
				"attr" : {
					"cellspacing" : "20"
				},
				"children" : trs
			});
			assemblys.createElement(html, $(dom)[0]);
			//$(".showBigIconType").html(html);
		} else {
			//列表显示分类
			$(".showBigIconType").addClass("layui-hide");
			$(".showListType").removeClass("layui-hide").empty();
			var leftHtml = [];
			var lis = [];
			for (var i = 0; i < typeList.length; i++) {
				var type = typeList[i];
				lis.push({
					"tagName" : "li",
					"className" : (type.typeName == '全部' ? "sumItem skin-btn-main" : "sumItem"),
					"attr" : {
						"customFormType" : (type.typeName == '全部' ? "All" : type.typeCode),
						"index" : i
					},
					"onclick" : function() {
						if (callback && typeof callback == 'function') {
							var _index = $(this).attr("index");
							var _temp = typeList[_index];
							callback(_temp);
						}
					},
					"children" : [ {
						"tagName" : "div",
						"className" : "leftIco",
						"children" : [ {
							"tagName" : "i",
							"className" : "icon layui-icon" + (type.typeIconType || 2) + " skin-div-font",
							"innerHTML" : (type.typeIcon != null ? type.typeIcon : "&#xe779;"),
						} ]
					}, {
						"tagName" : "div",
						"className" : "rightText",
						"innerHTML" : type.typeName,
					} ]
				});
			}
			leftHtml.push({
				"tagName" : "ul",
				"className" : "slideBar",
				"children" : lis
			});
			assemblys.createElement(leftHtml, $(dom)[0]);
			//$(".leftSum").html(leftHtml);
		}
	},
	loadTable : function(customFormList, dom, callback) {
		$(dom).html("");
		var rightHtml = [];
		var ps = [];
		if (customFormList == null || customFormList.length == 0) {
			ps.push({
				"tagName" : "p",
				"children" : [ {
					"tagName" : "i",
					"className" : "layui-icon layui-icon-file",
					"style" : "color: #009688;",
				}, {
					"tagName" : "a",
					"attr" : {
						"href" : "javascript:void(0)"
					},
					"children" : [ {
						"tagName" : "span",
						"style" : "font-size: 14px;",
						"innerHTML" : " 无相关数据！",
					} ]
				} ]
			});
		} else {
			for (var i = 0; i < customFormList.length; i++) {
				var item = customFormList[i];
				ps.push({
					"tagName" : "p",
					"className" : ((i + 1) % 2 == 0) ? "eventDataValue" : "",
					"attr" : {
						"customFormValue" : item.typeCode
					},
					"children" : [ {
						"tagName" : "i",
						"className" : "layui-icon layui-icon-file",
						"style" : "color: #009688;",
					}, {
						"tagName" : "a",
						"attr" : {
							"href" : "javascript:void(0)",
							"title" : assemblys.escape(item.fromName),
							"index" : i
						},
						"onclick" : function() {
							if (callback && typeof callback == 'function') {
								var _index = $(this).attr("index");
								var _temp = customFormList[_index];
								callback(_temp);
							}
						},
						"children" : [ {
							"tagName" : "i",
							"className" : "fa fa-fw fa-file",
						}, {
							"tagName" : "span",
							"style" : "font-size: 14px;",
							"innerHTML" : assemblys.escape(item.fromName),
						} ]
					} ]
				});
			}
		}
		
		rightHtml.push({
			"tagName" : "div",
			"className" : "table table-hover enventShowTabs",
			"children" : [ {
				"tagName" : "div",
				"className" : "panels",
				"children" : [ {
					"tagName" : "div",
					"className" : "panel",
					"children" : ps
				} ]
			} ]
		});
		//$(".rightCon").html(rightHtml);
		assemblys.createElement(rightHtml, $(dom)[0]);
	}
}