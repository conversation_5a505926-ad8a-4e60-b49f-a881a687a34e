var interfaceDataList = {
	relationInterfaceList : {},
	parentInterfaceCode : "",
	parentInterfaceKeys : [],
	parentInterfaceValues : [],
	paramCache : [],
	resultAry : [],
	selectedData : [],
	queryList : [],
	init : function() {
		interfaceDataList.getRelationInterface().then(function() {
			interfaceDataList.getInterfaceQueryList(param.get("interfaceCode"), parent.interfaceKeys, parent.interfaceValues);
		});
	},
	getInitCondition : function(interfaceCode) {
		return $.ajax({
			url : basePath + "frame/interface/getInitCondition.spring",
			data : {
				"interfaceCode" : interfaceCode
			},
			skipDataCheck : true,
			dataType : "json"
		});
	},
	initSearch : function(interfaceCode, keyList) {
		
		if (interfaceDataList.parentInterfaceCode != interfaceCode) {
			
			// 加载初始化数据
			interfaceDataList.getInitCondition(interfaceCode).then(function(data) {
				
				var timeField = "";
				// 入参
				$("#conditionDate").hide();
				if (data.result == "success") {
					var paramMap = data.paramMap;
					timeField = paramMap.timeField;
					// 如果存在
					if (timeField) {
						$("#dateTime2").attr("name", timeField).val(paramMap.timeValue);
						$("#conditionDate").children("label").html(paramMap.timeName);
						$("#conditionDate").show();
						//执行一个laydate实例
						assemblys.dateRender({
							trigger : 'click',
							range : "~",
							max : 'today',
							elem : '#dateTime2' //指定元素
							,
							btns : [ 'confirm' ],
							ready : function(date) {
								//可以自定义时分秒
								var now = new Date();
								this.dateTime.hours = now.getHours();
								this.dateTime.minutes = now.getMinutes();
							}
						});
					}
				}
				
				// 组装下拉框
				var optionHtml = "<option value=''>无</option>";
				for ( var index in keyList) {
					var temp = keyList[index];
					// 过滤表和初始时间
					if (temp.itemType == "table" || temp.itemName == timeField) {
						continue;
					}
					optionHtml += "<option value='" + temp.itemName + "' type='" + temp.itemType + "' >" + temp.itemNameCHI + "</option>";
				}
				
				// 写入下拉框
				$("select[name='condition']").empty().append(optionHtml);
				layui.form.render("select", "conditionDiv");
				
				//执行一个laydate实例
				assemblys.dateRender({
					trigger : 'click',
					range : "~",
					max : 'today',
					elem : '#dateTime' //指定元素
					,
					ready : function(date) {
						//可以自定义时分秒
						var now = new Date();
						this.dateTime.hours = now.getHours();
						this.dateTime.minutes = now.getMinutes();
					}
				});
				
				// 初始化隐藏
				$(".layui-tab").find("div[type]").hide();
				// 监听下拉框
				layui.form.on("select(condition)", function(data) {
					$(this).parents(".layui-tab").find("div[type]").hide();
					if (data.value != "") {
						var type = $("select[name='condition']").find("option[value='" + data.value + "']").attr("type");
						var $div = $(this).parents(".layui-tab").find("div[type='" + type + "']");
						$div.show();
						$div.children("input").val("");
					}
				});
			})
		}
	},
	initLayui : function(queryList, cols, interfaceKeys, interfaceValues, interfaceCode, interfaceInfo, relationInterfaceCode, totalPage, curPageNum, pageSize) {
		layui.table.render({
			elem : '#list',
			data : queryList,
			height : window.$("#list").parent().height() - (window.$("#list").next().hasClass("layui-table-view") ? 0 : $("#tablePage").height()),
			limit : 100,
			cols : [ cols ]
		});
		
		layui.table.on("tool(list)", function(data) {
			if (data.event == "next") {
				
				// 缓存当前列表参数
				interfaceDataList.paramCache.push({
					"keys" : interfaceKeys,
					"values" : interfaceValues,
					"condition" : param.get("condition"),
					"keyword" : param.get("keyword"),
					"interfaceCode" : interfaceCode,
					"curPageNum" : page.get("curPageNum"),
					"pageSize" : page.get("pageSize")
				});
				
				var keys = interfaceInfo.keys;
				var values = [];
				for ( var i in keys) {
					values.push(data.data[keys[i]]);
				}
				
				interfaceDataList.resultAry.push(data.data);
				
				interfaceDataList.getInterfaceQueryList(relationInterfaceCode, keys, values);
				
				interfaceDataList.changeBackName();
			}
		});
		
		layui.table.on('checkbox(list)', function(obj) {
			var checkedData;
			if (obj.type == "all") {
				checkedData = interfaceDataList.queryList;
			} else {
				checkedData = [ obj.data ];
			}
			
			if (obj.checked) {
				for ( var i in checkedData) {
					interfaceDataList.selectedData[checkedData[i].index] = checkedData[i];
				}
			} else {
				for ( var i in checkedData) {
					interfaceDataList.selectedData[checkedData[i].index] = null;
				}
			}
		});
		
		layui.table.on('radio(list)', function(obj) {
			if (obj.checked) {
				obj.data.index = (curPageNum - 1) * pageSize + parseInt(obj.tr.attr("data-index")) + 1;
				interfaceDataList.selectedData[0] = obj.data;
			} else {
				interfaceDataList.selectedData[0] = null;
			}
		});
		
		layui.laypage.render({
			"elem" : "tablePage",
			"count" : totalPage,
			"curr" : curPageNum || 1,
			"limit" : pageSize || 20,
			"limits" : [ 10, 20, 50, 100 ],
			"layout" : [ 'count', 'prev', 'page', 'next', 'limit', 'skip' ],
			"jump" : function(obj, first) {
				page.set("curPageNum", obj.curr)
				page.set("pageSize", obj.limit);
				//首次不执行
				if (!first) {
					interfaceDataList.getInterfaceQueryList();
				}
			}
		});
	},
	getRelationInterface : function() {
		return $.ajax({
			url : basePath + "frame/interface/getRelationInterface.spring",
			data : {
				"interfaceCode" : param.get("interfaceCode"),
				"customFieldCode" : param.get("customFieldCode"),
				"appCode" : parent.param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					interfaceDataList.relationInterfaceList = data.relationInterfaceList;
				} else {
					assemblys.alert("获取接口信息出错");
				}
			},
			error : function(a, b, c) {
				assemblys.alert("获取接口信息出错!错误信息:" + c);
			}
		});
	},
	getInterfaceQueryList : function(interfaceCode, interfaceKeys, interfaceValues) {
		interfaceCode = interfaceCode || interfaceDataList.parentInterfaceCode;
		interfaceKeys = interfaceKeys || interfaceDataList.parentInterfaceKeys;
		interfaceValues = interfaceValues || interfaceDataList.parentInterfaceValues;
		
		if (!window.$("#list").next().hasClass("layui-table-view") || !window["page"]) {
			window["page"].set(null, {
				curPageNum : 1,
				pageSize : 20
			});
		}
		var curPageNum = page.get("curPageNum");
		var pageSize = page.get("pageSize");
		var selectInputType = param.get("selectInputType");
		$.ajax({
			url : basePath + "frame/interface/getInterfaceQueryList.spring",
			data : {
				// 固定关联参数
				"keys" : interfaceKeys,
				"values" : interfaceValues,
				// 高级搜索
				"condition" : param.get("condition"),
				"keyword" : param.get("keyword"),
				"interfaceCode" : interfaceCode,
				"curPageNum" : page.get("curPageNum"),
				"pageSize" : page.get("pageSize")
			},
			traditional : true, //默认false
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				interfaceDataList.queryList = data.queryList || [];
				var interfaceInfo = interfaceDataList.relationInterfaceList[interfaceCode];
				var relationInterfaceCode = interfaceInfo.relationInterfaceCode;
				
				var checkedSelector = "";
				var cols = [ {
					"title" : "操作",
					"align" : "center",
					"fixed" : "left",
					"type" : selectInputType
				}, {
					"title" : "下级",
					"align" : "center",
					"minWidth" : "70",
					"templet" : function(d) {
						var index = (curPageNum - 1) * pageSize + d.LAY_INDEX;
						data.queryList[d.LAY_INDEX - 1].index = index;
						if (interfaceDataList.selectedData[index]) {
							checkedSelector += ",tr[data-index='" + (d.LAY_INDEX - 1) + "'] input[name='layTableCheckbox']";
						}
						
						if (selectInputType == "radio" && interfaceDataList.selectedData[0] && interfaceDataList.selectedData[0].index == index) {
							checkedSelector = "tr[data-index='" + (d.LAY_INDEX - 1) + "'] input[lay-type='layTableRadio']";
						}
						
						if (relationInterfaceCode) {
							return "<button type='button' class='layui-btn layui-btn-xs' lay-event='next'>" + interfaceDataList.relationInterfaceList[relationInterfaceCode].interfaceName + "</button>";
						}
						return "";
					}
				} ];
				
				for ( var i in data.itemList) {
					cols.push({
						"title" : data.itemList[i],
						"align" : "left",
						"minWidth" : "120",
						"keyValue" : i,
						"templet" : function(d) {
							var v = d[this["keyValue"]];
							if (v) {
								if ((typeof v === 'number') && v > 1000000000000) {
									v = assemblys.dateToStr(v);
								} else if (v instanceof Date) {
									v = assemblys.dateToStr(v.getTime());
								} else if (v.time && Object.prototype.toString.call(v) === '[object Object]') {
									v = assemblys.dateToStr(v.time);
								}
							}
							return v || "";
						}
					});
				}
				
				interfaceDataList.initSearch(interfaceCode, data.keyList);
				
				interfaceDataList.initLayui(data.queryList || [], cols, interfaceKeys, interfaceValues, interfaceCode, interfaceInfo, relationInterfaceCode, data.totalPage, data.curPageNum, data.pageSize)

				// 回显checkbox
				if (selectInputType == "checkbox") {
					var $checkbox = $("div.layui-table-fixed").find(checkedSelector.substr(1)).prop("checked", true);
					$checkbox.next().addClass("layui-form-checked");
					if ($checkbox.length == pageSize) {
						$("input[lay-filter='layTableAllChoose']").prop("checked", true).next().addClass("layui-form-checked");
					}
				} else if (checkedSelector) {
					$("div.layui-table-fixed").find(checkedSelector).prop("checked", true).next().addClass("layui-form-radioed").children("i").addClass("layui-anim-scaleSpring").html("").parents("tr[data-index]").addClass("layui-table-click");
				}
				
				interfaceDataList.parentInterfaceCode = interfaceCode;
				interfaceDataList.parentInterfaceKeys = interfaceKeys;
				interfaceDataList.parentInterfaceValues = interfaceValues;
			}
		});
	},
	back : function() {
		if (interfaceDataList.paramCache.length == 0) {
			assemblys.closeWindow();
		} else {
			var paramCache = interfaceDataList.paramCache[interfaceDataList.paramCache.length - 1];
			var $select = $("select[name='condition']");
			$select.empty().append("<option value='" + paramCache.condition + "'></option>");
			
			page.set(null, {
				"condition" : paramCache.condition,
				"keyword" : paramCache.keyword
			});
			page.set(null, {
				"curPageNum" : paramCache.curPageNum,
				"pageSize" : paramCache.pageSize
			});
			interfaceDataList.getInterfaceQueryList(paramCache.interfaceCode, paramCache.keys, paramCache.values);
			
			interfaceDataList.paramCache.pop();
			
			interfaceDataList.resultAry.pop();
			
			interfaceDataList.changeBackName();
		}
	},
	changeBackName : function() {
		if (interfaceDataList.paramCache.length == 0) {
			$("#back").val("关闭");
		} else {
			$("#back").val("返回");
		}
	},
	selected : function() {
		var data = [];
		for ( var i in interfaceDataList.selectedData) {
			if (interfaceDataList.selectedData[i]) {
				data.push(interfaceDataList.selectedData[i]);
			}
		}
		
		if (data.length == 0) {
			assemblys.alert("请选择数据");
		} else {
			var result = {};
			var index = parseInt(param.get("index"));
			
			for ( var i in data) {
				i = parseInt(i);
				for ( var lastKey in data[i]) {
					var key = parent.interfaceFieldData[lastKey];
					if (key) {
						result[key + "-" + index] = data[i][lastKey];
					}
				}
				
				for ( var j in interfaceDataList.resultAry) {
					for ( var firstKey in interfaceDataList.resultAry[j]) {
						var key = parent.interfaceFieldData[firstKey];
						if (key) {
							result[key + "-" + index] = interfaceDataList.resultAry[j][firstKey];
						}
					}
				}
				
				index++;
			}
			index = index - 1;
			var $table_right = parent.$("[name^='" + param.get("customFieldCode") + "']").parents("div.table_right");
			var count = $table_right[0].customModular.count || 0;
			
			if (index > count) {
				var $tr = $table_right.find("td.addCommoncustomModularTd").parent();
				for (var i = 0; i < index - count; i++) {
					$tr.click();
				}
			}
			
			if (param.get("selectInputType") == "checkbox") {
				parent.customFormTemplate.clearInput($table_right);
			}
			
			parent.initCustomFormTemplate.setValue(null, result, $table_right[0]);
			
			assemblys.closeWindow();
		}
	}
}