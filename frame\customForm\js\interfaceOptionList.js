var interfaceOptionList = {
	getInterfaceItemList : function() {
		return $.ajax({
			url : basePath + "frame/interface/getInterfaceItemList.spring",
			data : {
				"interfaceCode" : interfaceCode,
				"compNo" : compNo
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				var options = "<option value=''></option>";
				var interfaceItemList = data.interfaceItemList;
				
				var interfaceName = "";
				
				for (var i = 0; i < interfaceItemList.length; i++) {
					var interfaceItem = interfaceItemList[i];
					if (interfaceItem.itemType == "date") {
						if (i != interfaceItemList.length - 1) {
							date += interfaceItem.itemName;
						} else {
							date += interfaceItem.itemName + ",";
						}
						
					}
					// 结尾
					if (interfaceName != "" && interfaceName != interfaceItem.interfaceName) {
						options += '</optgroup>';
					}
					// 第一次
					if (interfaceName == "" || interfaceName != interfaceItem.interfaceName) {
						options += '<optgroup label="' + interfaceItem.interfaceName + '">';
						interfaceName = interfaceItem.interfaceName;
					}
					options += '<option itemType="' + interfaceItem.itemType + '" level="' + interfaceItem.level + '"  value="' + interfaceItem.itemName + '">' + interfaceItem.itemNameCHI + '</option>';
				}
				// 回显选中的值
				$("select").append(options).val(function() {
					var value = $(this).prev().val();
					if ($(this).find("option[value='" + value + "']").length == 0) {
						value = "";
					}
					return value;
				});
				
				// 显示入参开关
				$("select").each(function() {
					var level = $(this).find("option:selected").attr("level");
					var $switch = $(this).parents("td").next().find(".keywordTemp");
					if (level && level == 1) {
						$switch.removeClass("layui-hide");
					} else {
						$switch.addClass("layui-hide");
					}
				});
				
				// 接口：显示入参开关
				$("input[name='interfaceItemCode']").each(function() {
					var $switch = $(this).parents("td").next().find(".keywordTemp");
					$switch.removeClass("layui-hide");
					/*if (level && level == 1) {
						$switch.removeClass("layui-hide");
					} else {
						$switch.addClass("layui-hide");
					}*/
				});
				
				layui.form.render("select");
			}
		});
	},
	getInterfaceOptionList : function() {
		return $.ajax({
			url : basePath + "frame/newCustomForm/getInterfaceOptionList.spring",
			data : {
				"customFieldCode" : customFieldCode,
				"customModularCode" : customModularCode,
				"appCode" : appCode
			},
			dataType : "json",
			success : function(data) {
				
				layui.table.render({
					elem : '#list',
					data : data.interfaceOptionList,
					limits : [ 9999 ],
					limit : 9999,
					height : window.$("#list").parent().height() - 10,
					cols : [ [ {
						title : '序号',
						align : "center",
						type : 'numbers'
					}, {
						title : '组件',
						align : "left",
						templet : function(d) {
							var html = '';
							html += '<input name="otherCustomFieldCode" type="hidden" value="' + d.customFieldCode + '" />' + d.customFieldName;
							return html;
						}
					}, {
						title : '接口字段',
						align : "center",
						width : 250,
						templet : function(d) {
							var html = '';
							html += '<input type="text" name="interfaceItemCode"  class="layui-input" value="' + (d.interfaceItemCode || "") + '" lay-verify="limit" limit="100" />';
							//html += '<select name="interfaceItemCode" style="width: 90px;" lay-filter="interfaceItemCode"></select>';
							return html;
						}
					}, {
						title : '是否参数',
						align : "center",
						width : 100,
						templet : function(d) {
							return '<div class="keywordTemp  layui-hide"><input type="checkBox" name="keywordTemp" lay-filter="keywordTemp" value="1" ' + (d.isKeyword == 1 ? 'checked' : '') + ' lay-skin="switch" lay-text="是|否" /></div>';
						}
					} ] ]
				});
			}
		});
	},
	//保存
	doFieldSave : function() {
		var is = false;
		if (isSubmit) {
			return;
		}
		isSubmit = true;
		
		if ($("input[name='keywordTemp']:checked").length == 0) {
			assemblys.alert("至少设置一个查询参数");
			isSubmit = false;
			return;
		}
		
		var pars = $("form").serialize();
		$("input[name='keywordTemp']").each(function() {
			if ($(this).is(":checked")) {
				$(this).val(1);
				var name = $(this).parents(".layui-form").prev().find(".layui-this").attr("lay-value");
				if (date.indexOf(name) != -1) {
					is = true;
				}
			} else {
				$(this).val(0);
			}
			var keyword = $(this).val();
			pars += "&keyword=" + keyword;
		});
		if (is) {
			assemblys.alert("不能选择date类型作为参数");
			isSubmit = false;
			return;
		}
		
		var url = basePath + "frame/newCustomForm/saveInterfaceOption.spring";
		$.ajax({
			type : "post",
			url : url,
			data : pars,
			dataType : "json",
			success : function(data) {
				assemblys.msg("操作成功", function() {
					window.location.reload();
				});
			}
		});
	}
}
