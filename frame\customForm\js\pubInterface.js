/**
 * 接口
 */
var pubInterface = {
	// 初始化函数
	initParam : null,
	// 回调
	callback : function(list) {
		if (list.length > 0) {
			pubInterface.selectData = list;
		}
	},
	// 返回的接口数据
	selectData : [ {} ],
	// 查询接口
	findInterfaceInfo : function(dom) {
		// 回显数据
		var $table_right = $(dom).parents("div.table_right");
		var customModular = $table_right[0].customModular;
		// 回显的name下标
		var index = $(dom).prop("name").split("-")[1];
		// 封装的数据
		var customField = $(dom).parents("[customFieldSet='interface']")[0].customField;
		// 选中的值
		var value = $.trim($(dom).parent().find("input").val());
		
		// 接口编号
		var interfaceCode = customField.fieldVerifyType;
		// 组件ID
		var customFieldCode = customField.customFieldCode;
		
		if (value == "") {
			assemblys.msg("接口【" + customField.customFieldName + "】的查询内容不能为空");
			return;
		}
		
		if (!customField.interfaceParam || JSON.stringify(customField.interfaceParam) === "{}") {
			assemblys.alert("请设置接口组件【" + customField.customFieldName + "】的查询参数");
			return;
		}
		
		window.interfaceKeys = [];
		window.interfaceValues = [];
		window.interfaceFieldData = customField.fieldData;
		for ( var i in customField.interfaceParam) {
			// 是入参时
			var fieldName = customField.interfaceParam[i].otherCustomFieldCode + "-" + index;
			var paramName = i;
			var paramValue = "";
			
			// 单选
			if ($table_right.find("input[type='radio'][name='" + fieldName + "']").length > 0) {
				paramValue = $table_right.find("input[type='radio'][name='" + fieldName + "']:checked").attr("optionRemark") || "";
			} else
			// 多选
			if ($table_right.find("input[type='checkbox'][name='" + fieldName + "']").length > 0) {
				$table_right.find("input[type='checkbox'][name='" + fieldName + "']").each(function() {
					if ($(this).is(":checked")) {
						paramValue = $(this).attr("optionRemark");
						return false;
					}
				});
			} else
			// 文本框、时间框
			if ($table_right.find("input[name='" + fieldName + "']").length > 0) {
				paramValue = $table_right.find("input[name='" + fieldName + "']").val();
			} else
			// 下拉框
			if ($table_right.find("select[name='" + fieldName + "']").length > 0) {
				paramValue = $table_right.find("select[name='" + fieldName + "']").children("option[value='" + $table_right.find("select[name='" + fieldName + "']").val() + "']").attr("optionRemark");
			} else
			// 文本域
			if ($table_right.find("textarea[name='" + fieldName + "']").length > 0) {
				paramValue = $table_right.find("textarea[name='" + fieldName + "']").val();
			}
			
			// 从控件检索值
			if (paramValue) {
				interfaceKeys.push(paramName);
				interfaceValues.push(paramValue);
			}
		}
		
		// 入参函数
		var selectInputType = (index > 0 || !customModular.hasAdd ? "radio" : "checkbox");
		pubInterface.initParam = function() {
			return {
				"selectInputType" : selectInputType,
				"compNo" : compNo,
				"interfaceCode" : interfaceCode,
				"keys" : interfaceKeys,
				"values" : interfaceValues
			};
		}

		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : customField.customFieldName,
			scrollbar : false,
			area : [ '90%', '90%' ],
			content : basePath + "plugins/interfaceInfo/interfaceDataFrame.html?initParam=pubInterface.initParam&callback=pubInterface.callback",
			end : function() {
				
				// 回显映射值
				var interfaceItem = customField.fieldData;
				
				var values = {};
				// 获取映射关系
				if (index == 0) {
					for (var i = 0; i < pubInterface.selectData.length; i++) {
						$.each(pubInterface.selectData[i], function(name, value) {
							values[interfaceItem[name] + "-" + i] = value;
						});
					}
				} else {
					$.each(pubInterface.selectData[0], function(name, value) {
						values[interfaceItem[name] + "-" + index] = value;
					});
				}
				
				if (JSON.stringify(values) == "{}") {
					return;
				}
				
				var filterValues = {};
				for ( var k in values) {
					var valueTemp = values[k];
					var fieldVerifyType = $("input[name='" + k + "']").attr("fieldVerifyType");
					if ($("input[name='" + k + "']").length > 1) {
						valueTemp = $("input[name='" + k + "'][optionremark='" + valueTemp + "']").val();
					} else if ($("select[name='" + k + "']").length > 0) {
						var $select = $("select[name='" + k + "']");
						var customFieldSet = $select.parents("div[customFieldCode='" + k.split("-")[0] + "']").attr("customFieldSet");
						if (customFieldSet != "select") {
							valueTemp = $("select[name='" + k + "'] option[value='" + valueTemp + "']").val();
						} else {
							valueTemp = $("select[name='" + k + "'] option[optionremark='" + valueTemp + "']").val();
						}
					}
					
					// 如果存在，要转格式
					if (valueTemp && fieldVerifyType) {
						if (fieldVerifyType == "date") {
							valueTemp = valueTemp.substring(0, 10);
						}
						if (fieldVerifyType == "datetime") {
							valueTemp = valueTemp.substring(0, 16);
						}
					}
					filterValues[k] = valueTemp;
					
				}
				
				var modularIndex = index;
				var i = pubInterface.selectData.length - 1;
				var $table_right = $("[name^='" + customField.customFieldCode + "']").parents("div.table_right");
				var count = $table_right[0].customModular.count || 0;
				if (i > count) {
					var $tr = $table_right.find("td.addCommoncustomModularTd").parent();
					for (var x = 0; x < i - count; x++) {
						$tr.click();
					}
				}
				
				/*if (selectInputType == "checkbox") {
					for (var i = 0; i < pubInterface.selectData.length; i++) {
						customFormTemplate.clearInput($table_right.find("[name$='-" + i + "']").parents("tr"));
					}
				} else {
					customFormTemplate.clearInput($table_right.find("[name$='-" + modularIndex + "']").parents("tr"));
				}*/

				// 下拉框无法回显
				initCustomFormTemplate.setValue(null, filterValues, $table_right);
				
				pubInterface.selectData.length = 0;
				
				// 如果有回调
				if (parent.commonCallback && parent.commonCallback["interfaceCallback"]) {
					parent.commonCallback["interfaceCallback"](window, interfaceItem, values, modularIndex);
				}
				
			}
		});
	}
}