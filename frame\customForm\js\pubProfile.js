/**
 * 头相框
 */
var pubProfile = {
	// 渲染组件
	initProfile : function(dom) {
		// 如果存在
		if (dom && dom.cropper) {
			dom.cropper.destroy();
		}
		var url = basePath + 'plugins/cropperAvatar/static/mod/';
		layui.config({
			base : url
		}).use('avatar', function() {
			var avatarObject = layui.avatar;
			avatarObject.render({
				success : function(base64, size) {
					$(".tailoring-container").remove();
					if (dom) {
						$(dom).attr("src", base64);
						//hwx 2022-03-01 增加保存图片路径base64
						$(dom).parent().find(".profilePhotoData").val(base64);
					}
				}
			});
		});
	}
}