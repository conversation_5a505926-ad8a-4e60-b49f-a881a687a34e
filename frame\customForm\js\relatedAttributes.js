$(function() {
	layui.use([ 'form', 'element' ], function() {
		var form = layui.form;
		form.render();
		form.on("switch(relate)", function(data) {
			relatedAttributes.relateOption(data.elem);
		});
		form.on("select(type)", function(data) {
			relatedAttributes.toRelatedAttribute(data.value);
		});
	});
});

var relatedAttributes = {
	//到关联属性或项
	toRelatedAttribute : function(type) {
		var url = basePath + "frame/customForm/getRelatedCustomModularList.spring?customFieldCode=" + customFieldCode + "&customModularCode=" + customModularCode + "&title=" + encodeURIComponent(title);
		url += "&customFormCode=" + customFormCode + "&type=" + type + "&relationOptionCode=" + relationOptionCode;
		url += "&appCode=" + appCode;
		url += "&fromUrl=" + encodeURIComponent(fromUrl);
		window.location.href = url;
	},
	goback : function() {
		
		if (fromUrl) {
			window.location.href = fromUrl;
		}
		
	},
	relateOption : function(obj) {
		$.ajax({
			url : basePath + "frame/customForm/relationCustomField.spring",
			data : {
				"targetCode" : obj.value,
				"relationOptionCode" : relationOptionCode,
				"customFormCode" : customFormCode,
				"appCode" : appCode,
				"checked" : obj.checked ? 1 : 0
			},
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					location.href = location.href;
				} else {
					obj.checked = !obj.checked;
					assemblys.alert("关联失败");
				}
			},
			error : function(a, b, c) {
				obj.checked = !obj.checked;
				assemblys.alert("关联失败!错误信息:" + c);
			}
		});
	},

}
