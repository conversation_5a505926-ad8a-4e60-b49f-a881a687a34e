<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>编辑属性</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<link rel="stylesheet" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" href="../../plugins/static/css/edit.css">
</head>
<body class="body_noTop">
	<form class="layui-form" action="" lay-filter="param">
		<input type="hidden" name="compNo" />
		<input type="hidden" name="appCode" />
		<div class="bodys bodys_noTop">
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red">*</span>
					JSON
				</label>
				<div class="layui-input-block">
					<textarea name="data" autocomplete="off" class="layui-textarea"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline" style="width: 300px;">
					<button type="button" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
					<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow()" />
				</div>
			</div>
			<pre class="layui-code">// 参考的JSON形式
businessCode = 业务编号 /  businessValue = 业务值 / type = 控件类型
{
    "customForm": {
        "customFormName": "事件名称",
        "businessCode": ""
    },
    "customModularList": [
        {
            "className": "分类1",
            "businessCode": "",
            "businessValue": "",
            "children": [
                {
                    "titleName": "控件名称",
                    "type": "text",
                    "businessCode": "",
                    "businessValue": "",
                    "optionList": []
                },
                {
                    "titleName": "控件名称",
                    "type": "select",
                    "businessCode": "",
                    "businessValue": "",
                    "optionList": [
                        {
                            "name": "选项1",
                            "businessCode": "",
                            "businessValue": "",
							"children" : [
								{
                                    "name": "二级选项1",
                                    "businessCode": "",
                                    "businessValue": ""
                                },
                                {
                                    "name": "二级选项2",
                                    "businessCode": "",
                                    "businessValue": ""
                                }
							]
                        },
                        {
                            "name": "选项2",
                            "businessCode": "",
                            "businessValue": ""
                        }
                    ]
                }
            ]
        },
        {
            "className": "分类2",
            "businessCode": "",
            "businessValue": "",
            "children": [
                {
                    "titleName": "控件名称",
                    "type": "datetime",
                    "businessCode": "",
                    "businessValue": "",
                    "optionList": []
                },
                {
                    "titleName": "控件名称",
                    "type": "radio",
                    "businessCode": "",
                    "businessValue": "",
                    "optionList": [
                        {
                            "name": "选项1",
                            "type": "radio",
                            "businessCode": "",
                            "businessValue": "",
                            "checked": false
                        },
                        {
                            "name": "选项2",
                            "type": "radioOther",
                            "businessCode": "",
                            "businessValue": "",
                            "checked": false
                        }
                    ]
                }
            ]
        },
        {
            "className": "分类3",
            "businessCode": "",
            "businessValue": "",
            "children": [
                {
                    "titleName": "控件名称",
                    "type": "checkbox",
                    "businessCode": "",
                    "businessValue": "",
                    "optionList": [
                        {
                            "name": "选项1",
                            "type": "checkbox",
                            "businessCode": "",
                            "businessValue": "",
                            "checked": false,
                            "children": [
                                {
                                    "name": "二级选项1",
                                    "type": "checkbox",
                                    "businessCode": "",
                                    "businessValue": "",
                                    "checked": true
                                },
                                {
                                    "name": "二级选项2",
                                    "type": "checkboxOther",
                                    "businessCode": "",
                                    "businessValue": "",
                                    "checked": false
                                }
                            ]
                        },
                        {
                            "name": "选项2",
                            "type": "checkboxOther",
                            "businessCode": "",
                            "businessValue": "",
                            "checked": false
                        }
                    ]
                }
            ]
        }
    ]
}			
			</pre>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript">
	layui.form.on("submit(save)", function() {
		$.ajax({
			type : "post",
			url : basePath + "frame/newCustomForm/generateCustomForm.spring",
			dataType : "json",
			data : {
				"compNo" : param.get("compNo"),
				"appCode" : param.get("appCode"),
				"data" : param.get("data")
			},
			success : function(data) {
				assemblys.msg("导入成功！", function() {
					parent.customFormList.getCustomFormListData();
					assemblys.closeWindow();
				});
			}
		});
		return false;
	});
</script>