<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;">
<title>关联属性</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/static/css/search.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var fromUrl = "${fromUrl}";
	var relationOptionCode = "";
	var title = "";
	var customFormCode = "";
	var customModularCode = "";
	var customFieldCode = "";
	var type = "";
	$(function() {
		fromUrl = $("input[param='fromUrl']").val();
		relationOptionCode = $("input[param='relationOptionCode']").val();
		title = $("input[param='title']").val();
		customFormCode = $("input[param='customFormCode']").val();
		customModularCode = $("input[param='customModularCode']").val();
		customFieldCode = $("input[param='customFieldCode']").val();
		type = $("input[param='type']").val();
	})
	var appCode = "${appCode}";
</script>
</head>
<body class="body_noTop">
	<form id="form1" action="" method="get" class="layui-form">
		<input type="hidden" param="fromUrl" value='<c:out value="${fromUrl}"/>' />
		<input type="hidden" param="relationOptionCode" value='<c:out value="${relationOptionCode}"/>' />
		<input type="hidden" param="title" value='<c:out value="${title}"/>' />
		<input type="hidden" param="customFormCode" value='<c:out value="${customFormCode}"/>' />
		<input type="hidden" param="customModularCode" value='<c:out value="${customModularCode}"/>' />
		<input type="hidden" param="customFieldCode" value='<c:out value="${customFieldCode}"/>' />
		<input type="hidden" param="type" value='<c:out value="${type}"/>' />
		<div class="bodys bodys_noTop">

			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<label class="layui-form-label">
					<c:choose>
						<c:when test="${fn:length(title)>20}">
							设置“<c:out value="${fn:substring(title, 0, 20)}" />...”选项的关联属性
						</c:when>
						<c:otherwise>
							设置“<c:out value="${title}" />”选项的关联属性
						</c:otherwise>
					</c:choose>
				</label>
				<div class="layui-input-inline h28 lh28">
					<select lay-filter="type">
						<option value="0" <c:if test="${type == 0}">selected</c:if>>组件</option>
						<option value="1" <c:if test="${type == 1}">selected</c:if>>模块</option>
					</select>
				</div>
				<c:if test="${type == 0}">
					<div style="color: red;max-width: 280px;" class="layui-input-inline h28 lh28">
						PS：只支持关联当前分类一行一列上的组件
					</div>
				</c:if>
			</div>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm  h28 lh28" value="关闭" onclick="assemblys.closeWindow();" />
			</div>
			<div class="tableDiv table_noTree">

				<table class="layui-table main_table">
					<tr class="main_title">
						<td width="40" align="center" nowrap="nowrap">序号</td>
						<td width="40" align="center" nowrap="nowrap">操作</td>
						<td align="center" nowrap="nowrap">表单模块</td>
						<td align="center" nowrap="nowrap">关联项名称</td>
					</tr>
					<c:choose>
						<c:when test="${fn:length(customModularList)>0}">
							<c:forEach items="${customModularList}" var="customModular" varStatus="vs">
								<tr id="${vs.count}" relationOptionCode="${customModular.relationOptionCode}">
									<td align="center">${vs.count}${customModular.relationOptionCode}</td>
									<td align="center">
										<input type="checkbox" <c:if test="${customModular.checked}">checked</c:if> value="${customModular.customModularCode}" lay-filter="relate" lay-skin="switch" lay-text="是|否" />
									</td>
									<td align="left">
										<c:out value="${customModular.customModularName}" />
									</td>
									<td align="left">
										<font color="red">
											<c:out value="${customModular.relationOptionName}" />
										</font>
									</td>
								</tr>
							</c:forEach>
						</c:when>
						<c:otherwise>
							<tr class="comTab_R2">
								<td align="center" colspan="4">没有相关的属性信息</td>
							</tr>
						</c:otherwise>
					</c:choose>
				</table>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}frame/customForm/js/relatedAttributes.js"></script>