SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'CustomForm' 

-- sqlSplit

DROP TABLE IF EXISTS `commoncustomfield`;

-- sqlSplit

CREATE TABLE `commoncustomfield`  (
  `CommonCustomFieldID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomModularCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '分类编号',
  `CustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '组件编号',
  `CustomFieldRowCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '行列信息编号',
  `SeqNo` int(11) NULL DEFAULT NULL COMMENT '顺序号',
  PRIMARY KEY (`CommonCustomFieldID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '组件中间表';

-- sqlSplit

DROP TABLE IF EXISTS `customfield`;

-- sqlSplit

CREATE TABLE `customfield`  (
  `CustomFieldID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFieldName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件名称',
  `CustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '组件编号',
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单编号，非公用组件被删除时保存归属表单编号',
  `CreateUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人工号',
  `CreateUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `CreateDate` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `IsNecessField` int(11) NULL DEFAULT NULL COMMENT '是否必填',
  `SeqNo` int(11) NULL DEFAULT NULL COMMENT '顺序号',
  `Status` int(11) NULL DEFAULT NULL COMMENT '状态,1有效,-1删除',
  `CustomFieldSet` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件类型,text文本框,textarea文本域,datetime日期,radio单选,checkbox多选,select下拉,label标签,user用户,dept科室,company医院,interdace接口',
  `CustomFieldLength` int(11) NULL DEFAULT NULL COMMENT '字段长度,目前好像没什么用',
  `BusinessCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务编号',
  `ImgURL` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片组件的图片路径,暂时没有开放',
  `RelationField` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户,科室,医院组件之间关联使用',
  `FunCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户,科室,医院组件的组织架构',
  `FieldVerifyType` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段校验类型',
  `IsCommon` int(0) NOT NULL DEFAULT 0 COMMENT '是否公用组件',
  `CompNo` varchar(200) NULL COMMENT '医院编号',
  PRIMARY KEY (`CustomFieldID`) USING BTREE,
  INDEX `index_businesscode_compno`(`BusinessCode`, `CompNo`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 228 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '组件表';

-- sqlSplit

DROP TABLE IF EXISTS `customfieldrow`;

-- sqlSplit

CREATE TABLE `customfieldrow`  (
  `CustomFieldRowID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFieldRowCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '行编号',
  `CustomModularCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '分类编号',
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单编号',
  `Cols` int(11) NULL DEFAULT NULL COMMENT '列数',
  `SeqNo` int(11) NULL DEFAULT NULL COMMENT '顺序号',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `Status` int(11) NULL DEFAULT NULL COMMENT '状态,1有效,-1删除',
  `MergeWithNextRow` int(11) NULL DEFAULT NULL COMMENT '是否合并下一行',
  PRIMARY KEY (`CustomFieldRowID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 168 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '行列信息表';

-- sqlSplit

DROP TABLE IF EXISTS `customform`;

-- sqlSplit

CREATE TABLE `customform`  (
  `CustomFormID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表单名称',
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单编号',
  `AppCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应用编号',
  `CompNo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医院编号',
  `DeptID` int(11) NULL DEFAULT NULL COMMENT '科室ID',
  `CreateUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人工号',
  `CreateUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `CreateDate` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `SeqNo` int(11) NULL DEFAULT NULL COMMENT '顺序号',
  `Status` int(11) NULL DEFAULT NULL COMMENT '状态,1有效,0无效,-1删除',
  `BusinessCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务编号',
  PRIMARY KEY (`CustomFormID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '表单';

-- sqlSplit

DROP TABLE IF EXISTS `customformfilled`;

-- sqlSplit

CREATE TABLE `customformfilled`  (
  `CustomFormFilledID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单编号',
  `CustomFormFilledCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '已提交编号',
  `Status` int(11) NULL DEFAULT NULL COMMENT '状态,默认0,需要业务系统自行修改',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `CreateUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人工号',
  `CreateUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `CreateDate` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `CompNo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医院编号',
  `DeptID` int(11) NULL DEFAULT NULL COMMENT '科室ID',
  PRIMARY KEY (`CustomFormFilledID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '已提交表单';

-- sqlSplit

DROP TABLE IF EXISTS `CommonCustomModular`;

-- sqlSplit

CREATE TABLE `CommonCustomModular`  (
  `CommonCustomModularID` int(0) NOT NULL AUTO_INCREMENT,
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '表单编号',
  `CustomModularCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '分类编号',
  `SeqNo` int(0) NOT NULL COMMENT '顺序号',
  PRIMARY KEY (`CommonCustomModularID`),
  INDEX `CommonCustomModular_CustomFormCode_INDEX`(`CustomFormCode`)
) COMMENT = '表单分类中间表';

-- sqlSplit

DROP TABLE IF EXISTS `custommodular`;

-- sqlSplit

CREATE TABLE `custommodular`  (
  `CustomModularID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomModularCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '分类编号',
  `CustomModularName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单编号',
  `SeqNo` int(11) NULL DEFAULT NULL COMMENT '顺序号',
  `Status` int(11) NULL DEFAULT NULL COMMENT '状态,1有效,-1删除(未启用)',
  `CreateUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人工号',
  `CreateUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `CreateDate` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `IsCommon` int(0) NOT NULL DEFAULT 0 COMMENT '是否公用组件',
  `HasAdd` int(11) NULL DEFAULT 0 COMMENT '是否支持新增',
  PRIMARY KEY (`CustomModularID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '分类表';

-- sqlSplit

DROP TABLE IF EXISTS `customoptionset`;

-- sqlSplit

CREATE TABLE `customoptionset`  (
  `CustomOptionSetID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomOptionSetCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '选项编号',
  `ParentCustomOptionSetCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '上级选项编号',
  `CustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '组件编号',
  `CustomOptionSetContent` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '选项内容',
  `Remark` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '选项备注',
  `CustomFieldSet` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '选项类型,radio单选,radioOther单选带输入框,checkbox多选,checkboxOther多选带输入框,label标签,select下拉',
  `SeqNo` int(11) NULL DEFAULT NULL COMMENT '顺序号',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `CreateUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人工号',
  `CreateUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `CreateDate` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `Status` int(11) NULL DEFAULT NULL COMMENT '状态,1有效,0无效,-1删除',
  `HasDefault` int(11) NULL DEFAULT NULL COMMENT '是否默认选中',
  `BusinessCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务编号',
  `CompNo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医院编号',
  `CustomOptionSetLevel` double NULL DEFAULT NULL COMMENT '选项等级',
  PRIMARY KEY (`CustomOptionSetID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 689 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '组件选项表';

-- sqlSplit

DROP TABLE IF EXISTS `customtextareatemplate`;

-- sqlSplit

CREATE TABLE `customtextareatemplate`  (
  `CustomTextareaTemplateID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomTextareaTemplateTitle` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模版标题',
  `CustomTextareaTemplateContent` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模板内容',
  `CustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '组件编号',
  `CompNo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '医院编号',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `CreateUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人工号',
  `CreateUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `CreateDate` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  PRIMARY KEY (`CustomTextareaTemplateID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '文本域模板表';

-- sqlSplit

DROP TABLE IF EXISTS `customvalue`;

-- sqlSplit

CREATE TABLE `customvalue`  (
  `CustomValueID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormFilledCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '已提交编号',
  `CustomModularCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '分类编号',
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单编号',
  `CustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '组件编号',
  `CustomOptionSetCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '选项编号,单选多选下拉选中值',
  `CustomTextValue` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '保存文本框,文本域,日期,用户,科室,医院,单选输入框,多选输入框等文本值',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `CopyIndex` int(11) NULL DEFAULT NULL COMMENT '分类副本序号,第一个副本为0,每增加一个副本+1（PS：支持新增的分类点击分类底部新增***就是增加副本）',
  PRIMARY KEY (`CustomValueID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '保存表单提交内容';

-- sqlSplit

DROP TABLE IF EXISTS `customvaluebackup`;

-- sqlSplit

CREATE TABLE `customvaluebackup`  (
  `CustomValueBackupID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormFilledCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '已提交编号',
  `CustomModularCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '分类编号',
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单编号',
  `CustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '组件编号',
  `CustomOptionSetCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '选项编号,单选多选下拉选中值',
  `CustomTextValue` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '保存文本框,文本域,日期,用户,科室,医院,单选输入框,多选输入框等文本值',
  `OptUserCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作日期',
  `CopyIndex` int(11) NULL DEFAULT NULL COMMENT '分类副本序号,第一个副本为0,每增加一个副本+1（PS：支持新增的分类点击分类底部新增***就是增加副本）',
  `RollbackCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '回滚编号',
  PRIMARY KEY (`CustomValueBackupID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- sqlSplit

DROP TABLE IF EXISTS `CustomInterfaceOption`;

-- sqlSplit

CREATE TABLE `custominterfaceoption`  (
  `InterfaceOptionID` int(11) NOT NULL AUTO_INCREMENT,
  `CurrCustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '接口组件编号',
  `OtherCustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '当前接口组件所在分类下其他组件的FieldName',
  `InterfaceItemCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '接口字段',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `IsKeyword` tinyint(255) NULL DEFAULT NULL COMMENT '是否关键字',
  `State` int(11) NULL DEFAULT NULL COMMENT '状态(1有效,0无效)',
  PRIMARY KEY (`InterfaceOptionID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '接口组件映射信息表';

-- sqlSplit

DROP TABLE IF EXISTS `CustomFormLog`;

-- sqlSplit

CREATE TABLE `CustomFormLog`  (
  `CustomFormLogID` bigint(0) NOT NULL AUTO_INCREMENT,
  `CustomFormLogCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '日志编号,UUID',
  `CustomFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '被修改的字段',
  `BeforeValue` text NULL COMMENT '修改前',
  `AfterValue` text NULL COMMENT '修改后',
  `OptUserCode` varchar(200) NULL COMMENT '修改人工号',
  `OptUserName` varchar(200) NULL COMMENT '修改人姓名',
  `OptDate` datetime(0) NULL COMMENT '修改时间',
  `CustomFormFilledCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '已填表单编号',
  `Status` int(0) NULL COMMENT '表单状态',
  `OptLogID` int(0) NULL COMMENT '操作记录表ID',
  PRIMARY KEY (`CustomFormLogID`)
) COMMENT = '表单修改记录表';

-- sqlSplit

DROP TABLE IF EXISTS `CustomRelation`;

-- sqlSplit

CREATE TABLE `CustomRelation`  (
  `CustomRelationID` int(0) NOT NULL AUTO_INCREMENT,
  `CustomFormCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '表单编号',
  `CustomOptionSetCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '选项编号',
  `RelationOptionCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '被关联模块编号',
  PRIMARY KEY (`CustomRelationID`)
)COMMENT = '选项与模块关联映射表';