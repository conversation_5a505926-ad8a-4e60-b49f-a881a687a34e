SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'CustomField' AND COLUMN_NAME = 'BusinessValue'

-- sqlSplit

ALTER TABLE `customfield` 
ADD COLUMN `BusinessValue` varchar(200) NULL COMMENT '业务值',
ADD COLUMN `BeginDate` datetime(0) NULL COMMENT '时间组件选择范围的开始时间',
ADD COLUMN `EndDate` datetime(0) NULL COMMENT '时间组件选择范围的结束时间',
ADD COLUMN `DateRange` int(0) NULL COMMENT '时间组件范围类型,0:自定义,1:小于等于今天,2:大于等于今天';

-- sqlSplit

ALTER TABLE `customoptionset` 
ADD COLUMN `BusinessValue` varchar(200) NULL COMMENT '业务值';

-- sqlSplit

ALTER TABLE `customvalue` 
ADD COLUMN `Remark` varchar(200) NULL COMMENT '备注';

-- sqlSplit

ALTER TABLE `customvaluebackup` 
ADD COLUMN `Remark` varchar(200) NULL COMMENT '备注';

-- sqlSplit

UPDATE CustomField SET RelationField = CustomFieldSet,CustomFieldSet = 'org' WHERE CustomFieldSet IN('department','user');

-- sqlSplit

UPDATE CustomField SET RelationField = 'dept' WHERE RelationField = 'department';

-- sqlSplit

UPDATE CustomValue  cv
INNER JOIN CustomField cf ON cf.CustomFieldCode = cv.CustomFieldCode AND cf.RelationField = 'user'
INNER JOIN 【FtnAppCommon】.PubUser pu ON pu.userCode = cv.CustomTextValue
SET cv.Remark = pu.userName; 

-- sqlSplit

UPDATE CustomValue  cv
INNER JOIN CustomField cf ON cf.CustomFieldCode = cv.CustomFieldCode AND cf.RelationField = 'dept'
INNER JOIN 【FtnAppCommon】.PubDept pd ON pd.deptID = cv.CustomTextValue
SET cv.Remark = pd.deptName; 

-- sqlSplit

UPDATE CustomValuebackup  cv
INNER JOIN CustomField cf ON cf.CustomFieldCode = cv.CustomFieldCode AND cf.RelationField = 'user'
INNER JOIN 【FtnAppCommon】.PubUser pu ON pu.userCode = cv.CustomTextValue
SET cv.Remark = pu.userName; 

-- sqlSplit

UPDATE CustomValuebackup  cv
INNER JOIN CustomField cf ON cf.CustomFieldCode = cv.CustomFieldCode AND cf.RelationField = 'dept'
INNER JOIN 【FtnAppCommon】.PubDept pd ON pd.deptID = cv.CustomTextValue
SET cv.Remark = pd.deptName; 

-- sqlSplit

ALTER TABLE `custommodular` 
ADD COLUMN `BusinessCode` varchar(200) NULL COMMENT '业务编号',
ADD COLUMN `BusinessValue` varchar(200) NULL COMMENT '业务值',
ADD COLUMN `CompNo` varchar(200) NULL COMMENT '医院编号',
ADD INDEX `CustomModular_INDEX_BusinessCode_CompNo`(`BusinessCode`, `CompNo`);

-- sqlSplit

UPDATE custommodular cm
INNER JOIN CustomForm cf ON cf.CustomFormCode = cm.CustomFormCode
SET cm.CompNo = cf.CompNo;

-- sqlSplit

UPDATE custommodular cm
INNER JOIN commoncustommodular ccm ON ccm.custommodularCode = cm.custommodularCode AND cm.IsCommon <> 1
INNER JOIN CustomForm cf ON cf.CustomFormCode = ccm.CustomFormCode
SET cm.CompNo = cf.CompNo;

-- sqlSplit

UPDATE custommodular SET CompNo = '' WHERE IsCommon = 1;

-- sqlSplit

UPDATE customField SET CompNo = '' WHERE IsCommon = 1;

-- sqlSplit

UPDATE customoptionset cos
INNER JOIN CustomField cf ON cf.CustomFieldCode = cos.CustomFieldCode AND cf.IsCommon = 1
SET cos.CompNo = '';