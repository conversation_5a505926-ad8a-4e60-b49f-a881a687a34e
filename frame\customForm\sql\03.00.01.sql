SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'customoptionset' AND COLUMN_NAME = 'CustomOptionSetLevel' AND DATA_TYPE = 'int'

-- sqlSplit

UPDATE CustomOptionSet c1 
INNER JOIN (SELECT
	@Level := @Level + 1 AS level,@ids AS _ids,(
	SELECT
		@ids := GROUP_CONCAT( CustomOptionSetCode SEPARATOR ',') 
	FROM
		CustomOptionSet 
	WHERE
	FIND_IN_SET( parentCustomOptionSetCode, @ids )) AS cids 
FROM
	CustomOptionSet cos,
	( SELECT @ids := (SELECT GROUP_CONCAT(CustomOptionSetCode SEPARATOR ',') FROM CustomOptionSet WHERE parentCustomOptionSetCode = ''),@Level := 0 ) b 
WHERE
	@ids IS NOT NULL) T ON FIND_IN_SET(c1.CustomOptionSetCode,T._ids)
	SET c1.CustomOptionSetLevel = T.level ;
	
-- sqlSplit

ALTER TABLE `customoptionset` 
MODIFY COLUMN `CustomOptionSetLevel` int(0) NULL DEFAULT 1 COMMENT '选项等级';