SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'CustomFormType' 

-- sqlSplit

CREATE TABLE `CustomFormType`  (
  `CustomFormTypeID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormTypeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单分类编号',
  `CustomFormTypeName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表单分类名称',
  `CustomApprovalFlowCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程编号',
  `CustomApprovalFlowName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程名称',
  `AppCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应用编号',
  `CompNo` varchar(200) NULL COMMENT '医院编号',
  `CreateUID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人工号（唯一）',
  `CreateUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `CreateDate` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `OptUID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人工号（唯一）',
  `OptUserName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人名称',
  `OptDate` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
  `SeqNo` decimal(11,0) NULL DEFAULT NULL COMMENT '顺序号',
  `State` int(11) NULL DEFAULT NULL COMMENT '状态,0无效,1有效,-1删除',
  `BusinessCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务编号',
  PRIMARY KEY (`CustomFormTypeID`) USING BTREE,
  INDEX `index_businesscode_compno`(`BusinessCode`, `CompNo`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '自定义表单分类表';

-- sqlSplit

CREATE TABLE `CustomFormTypeState`  (
  `CustomFormTypeStateID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormTypeStateCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '状态编号',
  `CustomFormTypeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单分类编号',
  `CustomFormTypeStateNo` int(11) NULL DEFAULT NULL COMMENT '表单状态编码',
  `CustomFormTypeStateName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表单状态名称',
  `CustomFormTypeStateColor` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表单状态颜色',
  `SeqNo` int(11) NULL DEFAULT NULL COMMENT '顺序号',
  PRIMARY KEY (`CustomFormTypeStateID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '自定义表单分类状态表';

-- sqlSplit

CREATE TABLE `CustomFormTypeMenu`  (
  `CustomFormTypeMenuID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormTypeMenuCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '菜单编号',
  `CustomFormTypeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单分类编号',
  `CustomFormTypeMenuNo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '菜单编码',
  `CustomFormTypeMenuName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '菜单名称',
  `CustomFormTypeMenuType` int(11) NULL DEFAULT NULL COMMENT '菜单类型，0审批，1查阅，2我的，3上报',
  PRIMARY KEY (`CustomFormTypeMenuID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '自定义表单分类菜单表';

-- sqlSplit

CREATE TABLE `CustomFormTypeMenuState`  (
  `CustomFormTypeMenuStateID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomFormTypeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单分类编号',
  `CustomFormTypeStateCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '状态编号',
  `CustomFormTypeMenuCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '菜单编号',
  PRIMARY KEY (`CustomFormTypeMenuStateID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT '自定义表单分类菜单状态中间表';

-- sqlSplit

ALTER TABLE `customform`
ADD COLUMN `CustomFormTypeCode`  varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '表单分类编号' AFTER `CustomFormCode`,
ADD COLUMN `CustomFormClass`  int(11) NOT NULL DEFAULT 0 COMMENT '表单类型' AFTER `CustomFormTypeCode`,
ADD COLUMN `CustomApprovalFlowCode`  varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '流程编号' AFTER `CustomFormClass`,
ADD COLUMN `CustomApprovalFlowName`  varchar(200) NULL COMMENT '流程名称' AFTER `CustomApprovalFlowCode`;

-- sqlSplit

ALTER TABLE `customfield`
ADD COLUMN `CustomFormTypeCode`  varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '表单分类编号' AFTER `CustomFormCode`,
ADD COLUMN `CustomFormClass`  Int(11) NULL DEFAULT 0 COMMENT '表单类型' AFTER `CustomFormTypeCode`;

-- sqlSplit

INSERT INTO `customformtype` (`CustomFormTypeCode`, `CustomFormTypeName`, `CustomApprovalFlowCode`, `CustomApprovalFlowName`, `AppCode`, `CompNo`, `CreateUID`, `CreateUserName`, `CreateDate`, `OptUID`, `OptUserName`, `OptDate`, `SeqNo`, `State`, `BusinessCode`) VALUES ( 'none_type', '未分类', '', '', '【appCode】', 'CF148', 'CF148_ADMIN', '初始化管理员', '2022-01-22 10:14:24', 'CF148_ADMIN', '初始化管理员', now(), '0.0', '1', '');

-- sqlSplit

UPDATE customfield SET CustomFormTypeCode = 'none_type', CustomFormClass = 0;

-- sqlSplit

UPDATE customform SET CustomFormTypeCode = 'none_type' WHERE (CustomFormTypeCode IS NULL OR CustomFormTypeCode = '');

-- sqlSplit

ALTER TABLE `customfieldrow` 
ADD INDEX `CustomFieldRow_CustomModularCode_INDEX`(`CustomModularCode`);

