SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'customfield' AND COLUMN_NAME = 'IsRead' 

-- sqlSplit

ALTER TABLE `customfield`
ADD COLUMN `DefaultValue`  text NULL  COMMENT '默认值'  AFTER `DateRange`,
ADD COLUMN `IsRead`  int(11) NULL DEFAULT 0 COMMENT '是否只读'  AFTER `DefaultValue`,
ADD COLUMN `RemindText`  varchar(200) NULL DEFAULT '' COMMENT '提示语' AFTER `IsRead`,
ADD COLUMN `IsRichText`  int(11) NULL DEFAULT 0  COMMENT '是否富文本' AFTER `RemindText`;

-- sqlSplit

ALTER TABLE `custommodular`
ADD COLUMN `IsTable`  int(11) NULL DEFAULT 0 COMMENT '是否表格风填报' AFTER `CompNo`;

-- sqlSplit

ALTER TABLE `customvalue`
MODIFY COLUMN `CustomTextValue`  longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL AFTER `CustomOptionSetCode`;

-- sqlSplit

ALTER TABLE `customformlog`
MODIFY COLUMN `BeforeValue`  longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修改前' AFTER `CustomFieldCode`,
MODIFY COLUMN `AfterValue`  longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修改后' AFTER `BeforeValue`;

