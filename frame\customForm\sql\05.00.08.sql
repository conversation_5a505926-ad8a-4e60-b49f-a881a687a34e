SELECT 1 FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'customvalue' AND INDEX_NAME = 'CustomValue_CustomFormFilledCode_CustomFieldCode_INDEX' 

-- sqlSplit

ALTER TABLE `customoptionset` 
ADD UNIQUE INDEX `CustomOptionSet_Code_INDEX`(`CustomOptionSetCode`);

-- sqlSplit

ALTER TABLE `customfield` 
ADD UNIQUE INDEX `CustomField_Code_INDEX`(`CustomFieldCode`);

-- sqlSplit

ALTER TABLE `customvalue` 
ADD INDEX `CustomValue_CustomFormFilledCode_CustomFieldCode_INDEX`(`CustomFormFilledCode`, `CustomFieldCode`);

-- sqlSplit

ALTER TABLE `customformfilled` 
ADD UNIQUE INDEX `CustomFormFilled_Code_INDEX`(`CustomFormFilledCode`);

-- sqlSplit

ALTER TABLE `customform` 
ADD UNIQUE INDEX `CustomForm_Code_INDEX`(`CustomFormCode`);

-- sqlSplit

ALTER TABLE `customform` 
ADD INDEX `CustomForm_TypeCode_INDEX`(`CustomFormTypeCode`);

-- sqlSplit

ALTER TABLE `customformtypemenu` 
ADD INDEX `CustomFormTypeMenu_TypeCode_INDEX`(`CustomFormTypeCode`);