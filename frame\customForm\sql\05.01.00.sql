SELECT 1 FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'commoncustommodular' AND INDEX_NAME = 'CommonCustomModular_FormCode_ModularCode_INDEX' 

-- sqlSplit

DELETE FROM commoncustommodular WHERE commoncustommodularID IN(SELECT commoncustommodularID FROM 
(SELECT MAX(commoncustommodularID) AS commoncustommodularID FROM commoncustommodular GROUP BY `CustomFormCode`, `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM commoncustommodular WHERE commoncustommodularID IN(SELECT commoncustommodularID FROM 
(SELECT MAX(commoncustommodularID) AS commoncustommodularID FROM commoncustommodular GROUP BY `CustomFormCode`, `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM commoncustommodular WHERE commoncustommodularID IN(SELECT commoncustommodularID FROM 
(SELECT MAX(commoncustommodularID) AS commoncustommodularID FROM commoncustommodular GROUP BY `CustomFormCode`, `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM commoncustommodular WHERE commoncustommodularID IN(SELECT commoncustommodularID FROM 
(SELECT MAX(commoncustommodularID) AS commoncustommodularID FROM commoncustommodular GROUP BY `CustomFormCode`, `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM commoncustommodular WHERE commoncustommodularID IN(SELECT commoncustommodularID FROM 
(SELECT MAX(commoncustommodularID) AS commoncustommodularID FROM commoncustommodular GROUP BY `CustomFormCode`, `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

ALTER TABLE `commoncustommodular` 
DROP INDEX `CommonCustomModular_CustomFormCode_INDEX`,
ADD UNIQUE INDEX `CommonCustomModular_FormCode_ModularCode_INDEX`(`CustomFormCode`, `CustomModularCode`) USING BTREE;

-- sqlSplit

DELETE FROM CommonCustomField WHERE CommonCustomFieldID IN(SELECT CommonCustomFieldID FROM 
(SELECT MAX(CommonCustomFieldID) AS CommonCustomFieldID FROM CommonCustomField GROUP BY CustomModularCode,CustomFieldCode HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM CommonCustomField WHERE CommonCustomFieldID IN(SELECT CommonCustomFieldID FROM 
(SELECT MAX(CommonCustomFieldID) AS CommonCustomFieldID FROM CommonCustomField GROUP BY CustomModularCode,CustomFieldCode HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM CommonCustomField WHERE CommonCustomFieldID IN(SELECT CommonCustomFieldID FROM 
(SELECT MAX(CommonCustomFieldID) AS CommonCustomFieldID FROM CommonCustomField GROUP BY CustomModularCode,CustomFieldCode HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM CommonCustomField WHERE CommonCustomFieldID IN(SELECT CommonCustomFieldID FROM 
(SELECT MAX(CommonCustomFieldID) AS CommonCustomFieldID FROM CommonCustomField GROUP BY CustomModularCode,CustomFieldCode HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM CommonCustomField WHERE CommonCustomFieldID IN(SELECT CommonCustomFieldID FROM 
(SELECT MAX(CommonCustomFieldID) AS CommonCustomFieldID FROM CommonCustomField GROUP BY CustomModularCode,CustomFieldCode HAVING COUNT(1) > 1) T);

-- sqlSplit

ALTER TABLE `commoncustomfield` 
ADD UNIQUE INDEX `CommonCustomField_Modular_Field_INDEX`(`CustomModularCode`, `CustomFieldCode`) USING BTREE;

-- sqlSplit

DELETE FROM customrelation WHERE customrelationID IN(SELECT customrelationID FROM 
(SELECT MAX(customrelationID) AS customrelationID FROM customrelation GROUP BY `CustomFormCode`, `CustomOptionSetCode`, `RelationOptionCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customrelation WHERE customrelationID IN(SELECT customrelationID FROM 
(SELECT MAX(customrelationID) AS customrelationID FROM customrelation GROUP BY `CustomFormCode`, `CustomOptionSetCode`, `RelationOptionCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customrelation WHERE customrelationID IN(SELECT customrelationID FROM 
(SELECT MAX(customrelationID) AS customrelationID FROM customrelation GROUP BY `CustomFormCode`, `CustomOptionSetCode`, `RelationOptionCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customrelation WHERE customrelationID IN(SELECT customrelationID FROM 
(SELECT MAX(customrelationID) AS customrelationID FROM customrelation GROUP BY `CustomFormCode`, `CustomOptionSetCode`, `RelationOptionCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customrelation WHERE customrelationID IN(SELECT customrelationID FROM 
(SELECT MAX(customrelationID) AS customrelationID FROM customrelation GROUP BY `CustomFormCode`, `CustomOptionSetCode`, `RelationOptionCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

ALTER TABLE `customrelation` 
ADD UNIQUE INDEX `UNIQUE_CustomRelationCode_INDEX`(`CustomFormCode`, `CustomOptionSetCode`, `RelationOptionCode`);

-- sqlSplit

DELETE FROM custommodular WHERE custommodularID IN(SELECT custommodularID FROM 
(SELECT MAX(custommodularID) AS custommodularID FROM custommodular GROUP BY `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM custommodular WHERE custommodularID IN(SELECT custommodularID FROM 
(SELECT MAX(custommodularID) AS custommodularID FROM custommodular GROUP BY `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM custommodular WHERE custommodularID IN(SELECT custommodularID FROM 
(SELECT MAX(custommodularID) AS custommodularID FROM custommodular GROUP BY `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM custommodular WHERE custommodularID IN(SELECT custommodularID FROM 
(SELECT MAX(custommodularID) AS custommodularID FROM custommodular GROUP BY `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM custommodular WHERE custommodularID IN(SELECT custommodularID FROM 
(SELECT MAX(custommodularID) AS custommodularID FROM custommodular GROUP BY `CustomModularCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

ALTER TABLE `custommodular` 
ADD UNIQUE INDEX `CustomModular_Code_INDEX`(`CustomModularCode`);

-- sqlSplit

DELETE FROM customfieldrow WHERE customfieldrowID IN(SELECT customfieldrowID FROM 
(SELECT MAX(customfieldrowID) AS customfieldrowID FROM customfieldrow GROUP BY `CustomFieldRowCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customfieldrow WHERE customfieldrowID IN(SELECT customfieldrowID FROM 
(SELECT MAX(customfieldrowID) AS customfieldrowID FROM customfieldrow GROUP BY `CustomFieldRowCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customfieldrow WHERE customfieldrowID IN(SELECT customfieldrowID FROM 
(SELECT MAX(customfieldrowID) AS customfieldrowID FROM customfieldrow GROUP BY `CustomFieldRowCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customfieldrow WHERE customfieldrowID IN(SELECT customfieldrowID FROM 
(SELECT MAX(customfieldrowID) AS customfieldrowID FROM customfieldrow GROUP BY `CustomFieldRowCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

DELETE FROM customfieldrow WHERE customfieldrowID IN(SELECT customfieldrowID FROM 
(SELECT MAX(customfieldrowID) AS customfieldrowID FROM customfieldrow GROUP BY `CustomFieldRowCode` HAVING COUNT(1) > 1) T);

-- sqlSplit

ALTER TABLE `customfieldrow` 
ADD UNIQUE INDEX `CustomFieldRow_Code_INDEX`(`CustomFieldRowCode`);