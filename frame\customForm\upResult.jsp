<%@ page contentType="text/html; charset=utf-8" language="java"  %>
<%
	String baseURL = request.getContextPath();
%>
<html>
<script type="text/javascript">
	var result = "${result}";
	
	switch (result){
		case "success" :
			try{
				parent.assemblys.msg("保存成功",function(){
					parent.isSubmit =false;
					window.parent.location.reload();
				});
			}catch(e){
			}
			break;
		case "overSize" :
			try{
				parent.assemblys.alert("图片大小不能超过1M");
				parent.isSubmit =false;
				//window.parent.location.reload();
			}catch(e){
			}
			break;
		case "errorType" :
			try{
				parent.assemblys.alert("附件类型不匹配，请检查上传附件类型");
				parent.isSubmit =false;
				//window.parent.location.reload();
			}catch(e){
			}
			break;
		case "error" :
			try{
				parent.assemblys.alert("保存失败");
				parent.isSubmit =false;
				//window.parent.location.reload();
			}catch(e){
			}
			break;
		case "hasRepeatName" :
			try{
				parent.assemblys.alert("模块名称相同，请重新输入");
				parent.isSubmit =false;
				//window.parent.location.reload();
			}catch(e){
			}
			break;
		default :
			try{
				parent.assemblys.alert("上传附件出错，请联系管理员");
				parent.isSubmit =false;
				//window.parent.location.reload();
			}catch(e){
			}
	}
</script>
</html>
