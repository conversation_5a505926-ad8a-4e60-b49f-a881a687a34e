<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<title>字段显示设置</title>
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css">
<style type="text/css">
div.layui-form-radio {
	padding-top: 8px;
}

* {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
</style>
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="appCode">
		<input type="hidden" name="compNo">
		<input type="hidden" name="customFormCode">
		<input type="hidden" name="customFormTypeCode">
		<input type="hidden" param="callback">
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2" menuIcon>&#xe62c;</i>
				字段显示设置
			</span>
			<div id="settingType" class="layui-input-inline layui-hide" style="max-width: 300px;">
				<label class="layui-form-label" style="width: 80px; display: inline-block; padding: 14px 9px 9px 0px;">对象</label>
				<input type="radio" name="customFieldSettingType" value="1" title="个人" checked="checked" lay-filter="customFieldSettingType" />
				<input type="radio" name="customFieldSettingType" value="0" title="组织" lay-filter="customFieldSettingType" />
			</div>
			<div class="head0_right fr">
				<input type="button" value="保存" lay-submit lay-filter="save" class="layui-btn layui-btn-sm  h28 lh28">
			</div>
		</div>
		<div class="bodys"></div>
	</form>
	<script type="text/javascript" src="../../plugins/Sortable/Sortable.min.js"></script>
	<script type="text/javascript" src="../../plugins/Sortable/Sortable.Swap.js"></script>
	<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
	<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
	<script type="text/javascript" src="js/customFieldSetting.js"></script>
	<script type="text/javascript">
		$(function() {
			customFieldSetting.init();
		});
	</script>
</body>
</html>