<!DOCTYPE html>
<html>
<head>
<title>customList列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../plugins/filterSearch/filterSearch.css">
<link rel="stylesheet" type="text/css" href="css/customList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="appCode">
		<input type="hidden" name="status">
		<input type="hidden" name="state">
		<input type="hidden" name="customFormCode">
		<input type="hidden" name="customFormTypeCode">
		<input type="hidden" name="customFormTypeMenuCode">
		<input type="hidden" name="customFormTypeBusinessCode">
		<input type="hidden" name="customFormTypeMenuNo">
		<input type="hidden" name="customFormTypeMenuType">
		<input type="hidden" name="myApproval" >
		<input type="hidden" name="hasTree" >
<!-- 	<input type="hidden" name="userCodeShow" > -->
<!-- 	<input type="hidden" name="businessCode" value="abc,test"> -->
		
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<div class="layui-input-inline h28 lh28 ">
					<input type="button" id="myApproval" class="layui-btn layui-btn-sm  h28 lh28 layui-hide" value="审核记录" lay-submit  lay-filter="approvalRecord" onclick="customList.approvalRecord()" >
				</div>
				<div class="layui-input-inline h28 lh28 ">
					<select name="compNo" lay-filter="compNo"></select>
					<input type="hidden" name="compNo" />
				</div>
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		<div class="layui-tab"  lay-filter="tabView"></div>
		<div class="layui-row">
			<div class="treeDiv layui-hide">
				<div class="treeHead">表单</div>
				<!-- tree -->
				<ul id="tree" class="tree-table-tree-box"></ul>
			</div>
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="../../plugins/filterSearch/filterSearch.js"></script>
<script type="text/javascript" src="js/customListModule.js"></script>
<script type="text/javascript" src="js/customList.js"></script>
<script type="text/javascript">
	$(function() {
		customList.init();
	});
</script>
</html>