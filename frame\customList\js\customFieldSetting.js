var customFieldSetting = {
	transfer : null,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function(data) {
			return customFieldSetting.getCustomFieldList(data);
		}).then(function(data) {
			customFieldSetting.initLayuiForm(data);
			return data;
		}).then(function() {
			return customFieldSetting.initRight();
		}).then(function() {
			if (customFieldSetting.hasSuperRight) {
				$("#settingType").removeClass("layui-hide");
			}
			// 默认个人
			param.set("customFieldSettingType", "1");
		});
		
	},
	//获取已选（字段）
	getCustomFieldSetting : function() {
		return $.ajax({
			url : basePath + "frame/customList/getCustomFieldSetting.spring",
			data : {
				appCode : param.get("appCode"),
				funCode : param.get("funCode"),
				compNo : param.get("compNo"),
				customFormCode : param.get("customFormCode"),
				customFormTypeCode : param.get("customFormTypeCode"),
				customFieldSettingType : param.get("customFieldSettingType")
			}
		}).then(function(data) {
			$("#ulRight li").dblclick();
			var isNumber = "60";
			var isOperate = "60";
			for (var i = 0; i < data.customFieldSetting.length; i++) {
				if(data.customFieldSetting[i].customFieldCode == "number"){
					isNumber = data.customFieldSetting[i].width;
					continue;
				}
				if(data.customFieldSetting[i].customFieldCode == "operate"){
					isOperate = data.customFieldSetting[i].width;
					continue;
				}
				$("#ulLeft").find("li[data-value='" + data.customFieldSetting[i].customFieldCode + "']").attr("width", data.customFieldSetting[i].width).dblclick();
			}
			$("#ulLeft").find("li[data-value='operate']").attr("width", isOperate).dblclick();
			$("#ulLeft").find("li[data-value='number']").attr("width",  isNumber).dblclick();
			$("#ulRight").find("li[data-value='number']").find("input[name='width']").val(isNumber);
			$("#ulRight").find("li[data-value='operate']").find("input[name='width']").val(isOperate);
			return data;
		});
	},
	initRight : function() {
		return $.ajax({
			url : basePath + "frame/customList/initRight.spring",
			data : {
				funCode : param.get("funCode"),
			},
			success : function(data) {
				customFieldSetting.hasSuperRight = data.hasSuperRight;
			}
		});
	},
	//获取全部组件字段
	getCustomFieldList : function() {
		return $.ajax({
			//url : basePath + "frame/newCustomForm/getAllCustomFieldList.spring",
			url : basePath + "frame/customList/getCustomFieldSettingAll.spring",
			data : {
				appCode : param.get("appCode"),
				funCode : param.get("funCode"),
				customFormTypeCode : param.get("customFormTypeCode"),
				customFormCode : param.get("customFormCode"),
				compNo : param.get("compNo")
			}
		}).then(function(data2) {
			data2.selectList = [];
			for ( var i in data2.customFieldList) {
				if (data2.customFieldList[i].customFieldSet == "label") {
					continue;
				}
				data2.selectList.push(data2.customFieldList[i]);
			}
			return data2;
		});
	},
	initLayuiForm : function(data) {
		var form = layui.form;
		
		form.on("radio(customFieldSettingType)", function(data) {
			customFieldSetting.getCustomFieldSetting();
			return false;
		});
		
		// 监听提交
		form.on("submit(save)", function(data) {
			customFieldSetting.saveCustomFieldSetting();
			return false;
		});
		
		customFieldSetting.transferRender(data.selectList);
		
		var sortable = Sortable.create($("ul.layui-transfer-data")[1], {
			animation : 150,
			handle : '.sortable-div', // handle's class
			ghostClass : 'sortable-background',
			filter: '.sortable-filtered', // 'filtered' class is not draggable
			onMove : function(evt, originalEvent) {
				var fromIndex = $(evt.dragged).index();
				var toIndex = $(evt.related).index();
				return fromIndex > 1 && (toIndex > 1 || $(evt.dragged).hasClass("layui-disabled"));
			},
		});
	},
	transferRender : function(selectList) {
		var fun = {
			toRight : function() {
				$(this).find("div").eq(0).css({
					"display" : "inline-block",
					"width" : "170px",
					"overflow" : "hidden"
				});
				if($(this).data("value") == "number"){
					$(this).append('<div class="field-width" style="display: inline-block; width: 50px;"><input name="customFieldName" type="hidden" value="' + $(this).attr("title") + '"><input name="customFieldCode" type="hidden" value="' + $(this).data("value") + '"><input name="width" class="layui-input" type="text" style="width: 100%;" value="' + ($(this).attr("width") || 200) + '" onclick="event.stopPropagation();"></div>').prependTo($("#ulRight"));
					this.ondblclick = null;
				}else if($(this).data("value") == "operate"){
					$(this).append('<div class="field-width" style="display: inline-block; width: 50px;"><input name="customFieldName" type="hidden" value="' + $(this).attr("title") + '"><input name="customFieldCode" type="hidden" value="' + $(this).data("value") + '"><input name="width" class="layui-input" type="text" style="width: 100%;" value="' + ($(this).attr("width") || 200) + '" onclick="event.stopPropagation();"></div>').prependTo($("#ulRight"));
					this.ondblclick = null;
				}else{
					$(this).append('<div class="field-width" style="display: inline-block; width: 50px;"><input name="customFieldName" type="hidden" value="' + $(this).attr("title") + '"><input name="customFieldCode" type="hidden" value="' + $(this).data("value") + '"><input name="width" class="layui-input" type="text" style="width: 100%;" value="' + ($(this).attr("width") || 200) + '" onclick="event.stopPropagation();"></div>').appendTo($("#ulRight"));
					this.ondblclick = fun.toLeft;
				}
			},
			toLeft : function() {
				
				$(this).find("div").eq(0).css({
					"display" : "",
					"width" : "",
				});
				$(this).find("div").eq(1).remove();
				$(this).appendTo($("#ulLeft"));
				
				this.ondblclick = fun.toRight;
			}
		
		}

		var dom = [ {
			tagName : "div",
			className : "layui-transfer-box",
			style : {
				"width" : "200px",
				"height" : "360px"
			},
			children : [ {
				tagName : "div",
				className : "layui-transfer-header",
				children : [ {
					attr : {
						"lay-skin" : "primary"
					},
					tagName : "div",
					className : "layui-unselect layui-form-checkbox",
					children : [ {
						tagName : "span",
						innerText : "未选"
					}, {
						tagName : "span",
						innerText : "（双击选择）",
						style : {
							"color" : "red"
						}
					} ]
				} ]
			}, {
				tagName : "div",
				className : "layui-transfer-search",
				children : [ {
					tagName : "i",
					className : "layui-icon layui-icon-search",
				}, {
					attr : {
						placeholder : "关键词搜索"
					},
					tagName : "input",
					className : "layui-input",
					type : "text",
					onkeyup : function(e) {
						var value = $.trim(this.value);
						var $ul = $("#ulLeft");
						if (value) {
							$ul.find("li").addClass("layui-hide");
							$ul.find("li[title*='" + assemblys.htmlEncode(this.value) + "']").removeClass("layui-hide");
						} else {
							$ul.find("li").removeClass("layui-hide");
						}
					}
				} ]
			}, {
				tagName : "ul",
				className : "layui-transfer-data",
				id : "ulLeft",
				style : {
					"height" : "267px"
				},
				children : (function() {
					var lis = [];
					for (var i = 0; i < selectList.length; i++) {
						lis.push({
							attr : {
								"data-value" : selectList[i].customFieldCode
							},
							tagName : "li",
							title : selectList[i].customFieldName,
							ondblclick : fun.toRight,
							children : [ {
								attr : {
									"lay-skin" : "primary"
								},
								tagName : "div",
								className : "layui-unselect layui-form-checkbox sortable-div "+((selectList[i].customFieldCode == "number" || selectList[i].customFieldCode == "operate" ) ? "layui-checkbox-disabled layui-disabled" :""),
								children : [ {
									tagName : "span",
									innerText : selectList[i].customFieldName
								} ]
							} ]
						})
					}
					return lis;
				})()
			} ]
		}, {
			tagName : "div",
			className : "layui-transfer-box",
			style : {
				"width" : "265px",
				"height" : "360px",
				"margin-left" : "10px"
			},
			children : [ {
				tagName : "div",
				className : "layui-transfer-header",
				children : [ {
					attr : {
						"lay-skin" : "primary"
					},
					tagName : "div",
					className : "layui-unselect layui-form-checkbox",
					children : [ {
						tagName : "span",
						innerText : "已选"
					}, {
						tagName : "span",
						innerText : "（双击选择）",
						style : {
							"color" : "red"
						}
					} ]
				} ]
			}, {
				tagName : "div",
				className : "layui-transfer-search",
				children : [ {
					tagName : "i",
					className : "layui-icon layui-icon-search",
				}, {
					attr : {
						placeholder : "关键词搜索"
					},
					tagName : "input",
					className : "layui-input",
					type : "text",
					style : {
						"width" : "177px",
						"display" : "inline-block"
					},
					onkeyup : function(e) {
						var value = $.trim(this.value);
						var $ul = $("#ulRight");
						if (value) {
							$ul.find("li").addClass("layui-hide");
							$ul.find("li[title*='" + assemblys.htmlEncode(this.value) + "']").removeClass("layui-hide");
						} else {
							$ul.find("li").removeClass("layui-hide");
						}
					}
				}, {
					tagName : "span",
					className : "",
					innerHTML : "&nbsp;&nbsp;&nbsp;列宽"
				} ]
			}, {
				tagName : "ul",
				className : "layui-transfer-data",
				id : "ulRight",
				style : {
					"height" : "267px"
				},
			} ]
		} ];
		assemblys.createElement(dom, $("div.bodys")[0]);
		
	},
	saveCustomFieldSetting : function() {
		$.ajax({
			url : basePath + "frame/customList/saveCustomFieldSetting.spring",
			data : param.__form(),
			type : "post",
			success : function(data) {
				assemblys.msg("保存成功！", function() {
					// 如果是方法回调
					if (param.callback) {
						parent.window.__returnData = data;
						parent.window.eval(param.callback + "(__returnData)");
						assemblys.closeWindow();
					} else {
						assemblys.alert("未设置callback回调");
					}
				});
			}
		});
	}
}
