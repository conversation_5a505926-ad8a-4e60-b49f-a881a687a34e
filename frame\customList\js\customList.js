var customList = {
	status : "",
	statusObjArr : {},
	statusColorArr : {},
	stateList : [],
	filterCodes : [],
	filterSets : [],
	specialStatus : [],
	first : true,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			return customList.getCustomFormTypeMenu();
		}).then(function() {
			return customList.initRight();
		}).then(function() {
			return customList.getCustomFormTypeStateList();
		}).then(function() {
			// 获取表单类型数量
			return customList.getCustomTypeCount();
		}).then(function() {
			// 过滤条件
			customList.initFilter();
			// 渲染
			customList.initLayuiForm();
			
			assemblys.initSessionStoragePram();
			
			// 加载tab
			customListModule.initTab(customList.handleStetsData(), ".layui-tab", function(data) {
				// 回调，提供点击当前tab值
				if (data.state) {
					param.set("status", data.state);
				} else {
					param.set("status", customList.status);
				}
				filterSearch.search(customList.initTable);
				customList.first = false;
			});
		});
	},
	initRight : function() {
		return $.ajax({
			url : basePath + "frame/customList/initRight.spring",
			data : {
				funCode : param.get("funCode"),
			},
			success : function(data) {
				customList.hasExecRight = data.hasExecRight;
			}
		});
	},
	// 获取表单类型数量
	getCustomTypeCount : function() {
		return $.ajax({
			url : basePath + "frame/customList/getCustomTypeCount.spring",
			data : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
				funCode : param.get("funCode"),
				status : param.get("status"),
				customFormTypeCode : param.get("customFormTypeCode"),
				customFormTypeMenuCode : param.get("customFormTypeMenuCode")
			},
			success : function(data) {
				if (data.customTypeCount && data.customTypeCount.length == 1) {
					param.set("customFormCode", data.customTypeCount[0].customFormCode);
				} else {
					if (param.get("hasTree") == 1) {
						$("div.treeDiv").removeClass("layui-hide");
						$("div.tableDiv").removeClass("table_noTree");
						customList.initTree(data.customTypeCount);
					} else if (data.customTypeCount.length == 1) {
						param.set("customFormCode", data.customTypeCount[0].customFormCode);
					}
				}
			}
		});
	},
	initTree : function(data) {
		layui.tree.render({
			id : "tree",
			elem : '#tree',
			click : function(item) {
				param.set("customFormCode", item.data.customFormCode);
				filterSearch.search(customList.initTable);
			},
			data : [ {
				title : "全部",
				customFormCode : "",
				spread : true,
				children : data
			} ],
			onlyIconControl : true
		});
	},
	initFilter : function() {
		var elemAry = customList.processingParams(customList.getParams());
		var customBtnDomArray = [ {
			title : "导出",
			className : "layui-icon layui-icon-export skin-div-font",
			onclick : function() {
				customListModule.exportList("列表");
			}
		} ];
		customBtnDomArray.push({
			title : "字段",
			className : "layui-icon layui-icon-set-fill skin-div-font",
			onclick : function() {
				customListModule.customFieldSetting("customList.colseWindow");
			}
		});
		// 过滤器
		filterSearch.init({
			"params" : elemAry,
			"searchFun" : function() {
				customList.initTable();
			},
			"customBtnDom" : customBtnDomArray
		})
		$("form.layui-form[lay-filter='filterParam']").find("#filterSearch").append('<input type="hidden" name="filterCodes" value="' + customList.filterCodes.join(",") + '">');
		$("form.layui-form[lay-filter='filterParam']").find("#filterSearch").append('<input type="hidden" name="filterSets" value="' + customList.filterSets.join(",") + '">');
	},
	// 初始化表单，界面渲染
	initLayuiForm : function() {
		layui.form.on("select(compNo)", function(data) {
			if (customList.first) {
				return;
			}
			filterSearch.search(customList.initTable);
		});
		layui.form.render();
	},
	getCustomFormTypeMenu : function() {
		return $.ajax({
			url : basePath + "frame/customFormType/getCustomFormTypeMenu.spring",
			data : {
				appCode : param.get("appCode"),
				compNo : param.get("compNo"),
				customFormTypeCode : param.get("customFormTypeCode"),
				customFormTypeMenuCode : param.get("customFormTypeMenuCode"),
				customFormTypeBusinessCode : param.get("customFormTypeBusinessCode"),
				customFormTypeMenuNo : param.get("customFormTypeMenuNo") || param.get("funCode") || "",
			}
		}).then(function(data) {
			var customFormType = data.customFormType;
			var customFormTypeMenu = data.customFormTypeMenu;
			var customFormTypeMenuType = customFormTypeMenu.customFormTypeMenuType || "0";
			param.set("customFormTypeCode", customFormType.customFormTypeCode);
			param.set("customFormTypeMenuCode", customFormTypeMenu.customFormTypeMenuCode);
			param.set("customFormTypeMenuType", customFormTypeMenuType);
			// 审核记录(我的上报不显示审核记录)
			if (param.get("myApproval") == "" && customFormTypeMenuType == "0") {
				$("#myApproval").removeClass("layui-hide");
			}
			return data;
		});
	},
	// 获取tab设置信息。
	getCustomFormTypeStateList : function() {
		return $.ajax({
			url : basePath + "frame/customFormType/getCustomFormTypeStateList.spring",
			data : {
				appCode : param.get("appCode"),
				customFormTypeCode : param.get("customFormTypeCode"),
				customFormTypeMenuCode : (param.get("myApproval") != "" ? "" : param.get("customFormTypeMenuCode"))
			}
		}).then(function(data) {
			customList.stateList = data.customFormTypeStateList;
			var stateArray = [];
			if (data.customFormTypeStateList) {
				for (var i = 0; i < data.customFormTypeStateList.length; i++) {
					var name = data.customFormTypeStateList[i].customFormTypeStateName;
					var no = data.customFormTypeStateList[i].customFormTypeStateNo;
					var color = data.customFormTypeStateList[i].customFormTypeStateColor;
					stateArray.push(data.customFormTypeStateList[i].customFormTypeStateNo);
					customList.statusObjArr[no] = name;
					customList.statusColorArr[no] = color;
					if (data.customFormTypeStateList[i].customFormTypeStateStatus && data.customFormTypeStateList[i].customFormTypeStateStatus != "STATE_COMMIT") {
						customList.specialStatus.push(data.customFormTypeStateList[i].customFormTypeStateNo);
					}
				}
				customList.statusObjArr["0"] = "草稿";
			}
			if (param.get("customFormTypeMenuType") == "2") {
				stateArray.push("0");
			}
			param.set("status", stateArray.join(","));
			customList.status = stateArray.join(",");
		});
	},
	// 获取字段设置信息
	getParams : function() {
		if (window.CUSTOM_FIELD_SETTING) {
			return window.CUSTOM_FIELD_SETTING;
		}
		$.ajax({
			url : basePath + "frame/customList/getCustomFieldSettingList.spring",
			async : false,
			data : {
				appCode : param.get("appCode"),
				funCode : param.get("funCode"),
				compNo : param.get("compNo"),
				customFormCode : param.get("customFormCode"),
				customFormTypeCode : param.get("customFormTypeCode")
			}
		}).then(function(data) {
			if (data.customFieldSetting) {
				window.CUSTOM_FIELD_SETTING = data.customFieldSetting;
			}
		});
		return window.CUSTOM_FIELD_SETTING;
	},
	// 清空参数
	clearParam : function() {
		window.CUSTOM_FIELD_SETTING = null;
	},
	initTable : function() {
		var events = {
			changeState : customListModule.changeState
		};
		// 列表
		var opts = [];
		// 我的上报，草稿状态可以编辑
		if (param.get("customFormTypeMenuType") == "2") {
			opts.push({
				"icon" : "layui-icon-search",
				"title" : "查看",
				"show" : function(d) {
					if (d.status != "0" && d.approvalIndex != "0") {
						return true;
					} else {
						return false;
					}
				},
				"onclick" : function(d) {
					customList.toDetail(d);
				}
			}, {
				"icon" : "layui-icon-edit",
				"title" : "编辑",
				"show" : function(d) {
					// 如果回退给上报人，可以编辑操作
					if (d.status == "0" || d.approvalIndex == "0") {
						return true;
					} else {
						return false;
					}
				},
				"onclick" : function(d) {
					customList.toEdit(d);
				}
			});
		} else {
			opts.push({
				"icon" : "layui-icon-search",
				"title" : "查看",
				"show" : function(d) {
					return true;
				},
				"onclick" : function(d) {
					customList.toDetail(d);
				}
			});
		}
		
		var customFieldSetting = customList.getParams();
		var cols = [];
		var isNumber = false;
		var isOperate = false;
		for ( var i in customFieldSetting) {
			var templet = function(d) {
				return layui.util.escape(d[this.fieldKey] || "");
			};
			if (customFieldSetting[i].templet) {
				eval(customFieldSetting[i].templet);
			}
			
			if ("customFormFilledCode" == customFieldSetting[i].customFieldCode) {
				cols.push({
					"title" : customFieldSetting[i].customFieldName,
					"width" : customFieldSetting[i].width || 170,
					"align" : customFieldSetting[i].align || "left",
					"fieldKey" : customFieldSetting[i].customFieldCode,
					"templet" : templet
				});
			} else if("number" == customFieldSetting[i].customFieldCode){
				isNumber = true;
				cols.push({
					"title" : customFieldSetting[i].customFieldName,
					"width" : customFieldSetting[i].width || 200,
					"align" : customFieldSetting[i].align || "left",
					"fieldKey" : customFieldSetting[i].customFieldCode,
					"templet" : templet
				});
			} else if("operate" == customFieldSetting[i].customFieldCode){
				isOperate = true;
				cols.push({
					"title" : customFieldSetting[i].customFieldName,
					"width" : customFieldSetting[i].width || 200,
					"align" : customFieldSetting[i].align || "left",
					"fieldKey" : customFieldSetting[i].customFieldCode,
					"templet" : templet
				});
			}else {
				cols.push({
					"title" : customFieldSetting[i].customFieldName,
					"width" : customFieldSetting[i].width || 200,
					"align" : customFieldSetting[i].align || "left",
					"fieldKey" : customFieldSetting[i].customFieldCode,
					"templet" : templet
				});
			}
		}
		if(!isOperate){
			cols.unshift({
				"title" : "操作",
				"width" :  60,
				"align" : "center",
				"fieldKey" : "operate",
			});
		}
		if(!isNumber){
			cols.unshift({
				"title" : "序号",
				"width" :  60,
				"align" : "center",
				"fieldKey" : "number",
			});
		}
		customListModule.tableRender({
			"elem" : '#list',
			"cols" : cols,
			"opt" : opts,
			"sta" : customList.statusObjArr,
			"color" : customList.statusColorArr
		});
		
	},
	/**
	 * 详情
	 */
	toDetail : function(data) {
		location.url({
			"url" : basePath + "/frame/customDetail/customFormDetail.html",
			"param" : {
				"titleName" : "表单详情",
				"appCode" : param.get("appCode"),
				"funCode" : param.get("funCode"),
				"customFormFilledCode" : data.customFormFilledCode,
				"approvalBelongCode" : data.customFormFilledCode,
				"customFormCode" : data.customFormCode,
				"customFormBusinessCode" : data.businessCode,
				"customFormTypeCode" : param.get("customFormTypeCode"),
				"customFormTypeMenuCode" : param.get("customFormTypeMenuCode"),
				"compNo" : param.get("compNo")
			},
			"page":{
				curPageNum : page.get("curPageNum"),
				pageSize : page.get("pageSize")
			}
		})
	},
	/**
	 * 草稿编辑
	 */
	toEdit : function(data) {
		// 返回HTML形式
		var url = basePath + "frame/customForm/customFormTemplate.html?1=1";
		url += "&appCode=" + param.get("appCode");
		url += "&customFormFilledCode=" + data.customFormFilledCode;
		url += "&customFormCode=" + data.customFormCode;
		url += "&compNo=" + param.get("compNo");
		url += "&type=" + (data.status == "0" ? "1" : "2");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : false,
			scrollbar : false,
			// closeBtn : 0,//取消右上角的×按钮
			area : [ '95%', '95%' ],
			content : url
		});
		
	},
	approvalRecord : function(data) {
		param.set("myApproval", "0");
		var url = basePath + "/frame/customList/customList.html?" + param.__form() + "&" + filterParam.__form();
		// url += "&myApproval=0";
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "layer-preview",
			title : "审批记录",
			scrollbar : false,
			area : [ '100%', '100%' ],
			content : url
		});
	},
	// 字段保存成功后回调
	colseWindow : function(data) {
		customList.clearParam();
		customList.initFilter();
		filterSearch.search(customList.initTable);
	},
	processingParams : function(filters) {
		var elemAry = new Array();
		if (filters) {
			for (var i = 0; i < filters.length; i++) {
				var fieldCode = filters[i].customFieldCode;
				var fieldSet = filters[i].customFieldSet;
				var fieldName = filters[i].customFieldName;
				var relationField = filters[i].relationField;
				if (customList.inBaseFilter(fieldCode)|| fieldCode == "operate"|| fieldCode == "number" || fieldCode == "status" || fieldCode == "approver" || fieldCode == "approvalBelongFlowNodeName") {
					continue;
				}
				var name = "";
				
				// （暂定）头像 、标签、 不作为过滤条件。（后期调整）
				if (fieldSet == "profile" || fieldSet == "label" || fieldSet == "file") {
					continue;
				}
				customList.filterCodes.push(fieldCode);
				customList.filterSets.push(fieldSet);
				// 接口
				if (fieldSet == "interface" || fieldSet == "text" || fieldSet == "textarea") {
					fieldSet = "text";
					name = "filterVlaues";
				}
				
				if (fieldSet == "datetime") {
					name = "filterVlaues";
				}
				// 
				if (fieldSet == "radio" || fieldSet == "checkbox" || fieldSet == "select") {
					fieldSet = "select";
					name = "filterVlaues";
				}
				// 组织架构
				if (fieldSet == "org") {
					fieldSet = "select";
					name = "filterVlaues";
				}
				
				elemAry.push({
					"type" : fieldSet,
					"title" : fieldName,
					"placeholder" : fieldName,
					"name" : name,
					"code" : fieldCode,
					"options" : customList.getSelectOption(filters[i].options, name, relationField)
				});
			}
		}
		return baseFitler.concat(elemAry);
	},
	inBaseFilter : function(code) {
		for (var i = 0; i < baseFitler.length; i++) {
			if (baseFitler[i].name == code) {
				return true;
			}
		}
	},
	getSelectOption : function(optionList, name, relationField) {
		var elemList = new Array();
		elemList.push({
			"value" : "",
			"text" : "全部",
		})
		if (optionList) {
			for (var i = 0; i < optionList.length; i++) {
				if (relationField && relationField == "user") {
					elemList.push({
						"value" : (optionList[i].userCode ? optionList[i].userCode : optionList[i].customOptionSetCode),
						"text" : (optionList[i].userName ? optionList[i].userName : optionList[i].customOptionSetContent)
					})
				} else {
					elemList.push({
						"value" : (optionList[i].deptId ? optionList[i].deptId : optionList[i].customOptionSetCode),
						"text" : (optionList[i].deptName ? optionList[i].deptName : optionList[i].customOptionSetContent)
					})
				}
			}
		}
		return elemList;
	},
	handleStetsData : function() {
		var dataList = customList.stateList;
		var stateArray = [];
		// 我的上报显示草稿
		if (param.get("customFormTypeMenuType") == "2") {
			stateArray.push({
				"name" : "草稿",
				"color" : "",
				"state" : "0",
				"code" : ""
			});
		}
		if (stateArray) {
			for (var i = 0; i < dataList.length; i++) {
				stateArray.push({
					"name" : dataList[i].customFormTypeStateName,
					"color" : dataList[i].customFormTypeStateColor,
					"state" : dataList[i].customFormTypeStateNo,
					"code" : dataList[i].customFormTypeMenuCode
				});
			}
		}
		return stateArray;
	}

}

var baseFitler = [ {
	"type" : "text",
	"title" : "编号",
	"placeholder" : "编号",
	"name" : "customFormFilledCode",
	"code" : "",
	"options" : []
}, {
	"type" : "text",
	"title" : "表单名称",
	"placeholder" : "表单名称",
	"name" : "customFormName",
	"code" : "",
	"options" : []
}, {
	"type" : "text",
	"title" : "创建人",
	"placeholder" : "创建人",
	"name" : "createUserName",
	"code" : "",
	"options" : []
}, {
	"type" : "datetime",
	"title" : "创建时间",
	"placeholder" : "创建时间",
	"name" : "createDate",
	"code" : "",
	"options" : []
} ]

// 上报完成后回调
var saveCallback = {
	init : function(win, d) {
		assemblys.msg(d.saveState == 0 ? "保存成功" : "提交成功", function() {
			win.assemblys.closeWindow();
		});
	}
}
