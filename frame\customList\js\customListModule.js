var customListModule = {
	/**
	 * 过滤器
	 */
	initFilter : function(param, customList, callback) {
		// 过滤条件
		filterSearch.init(pubData.getFilterParams(), callback, customList);
	},
	/**
	 * 初始化Tab
	 */
	initTab : function(list, dom, callback) {
		
		var listLength = list.length;
		// 写入结构
		var html = [];
		html.push({
			"tagName" : "li",
			"attr" : {
				"state" : ""
			},
			"innerText" : "全部",
			"onclick" : function() {
				if (callback && typeof callback == 'function') {
					callback({
						"state" : ""
					});
				}
			}
		});
		for (var i = 0; i < listLength; i++) {
			var stateName = list[i].name;
			var stateColor = list[i].color;
			var state = list[i].state;
			var stateCode = list[i].code;
			html.push({
				"tagName" : "li",
				"attr" : {
					"state" : state,
					"index" : i
				},
				"style" : {
					"color" : stateColor
				},
				"innerText" : stateName,
				"onclick" : function() {
					var _index = $(this).attr("index");
					var state = $(this).attr("state");
					param.set("state", state);
					var _temp = list[_index];
					if (callback && typeof callback == 'function') {
						callback(_temp);
					}
				}
			});
		}
		var tabdiv = [ {
			"tagName" : "li",
			"id" : "leftIcon",
			"className" : "layui-icon2",
		}, {
			"tagName" : "li",
			"id" : "leftIcon2",
			"className" : "layui-icon2 layui-hide",
		}, {
			"tagName" : "div",
			"className" : "layui-tab-div",
			"children" : [ {
				"tagName" : "ul",
				"id" : "CUSTOMLIST_TAB_VIEW",
				"className" : "layui-tab-title head2_tab h28",
				"style" : "left: 0px;",
				"children" : html
			} ]
		}, {
			"tagName" : "li",
			"id" : "rightIcon",
			"className" : "layui-icon2",
		}, {
			"tagName" : "li",
			"id" : "rightIcon2",
			"className" : "layui-icon2 layui-hide",
		} ];
		assemblys.createElement(tabdiv, $(dom)[0]);
		
		// 触发第一次点击
		$("#CUSTOMLIST_TAB_VIEW li[state='" + (param.get("state") || "") + "']").click();
		
		// 监听滚动
		var renderMousewheel = function() {
			var _changeLeft = function(_obj, _wheelDelta) {
				var left = $(_obj).children().css("left");
				if (left == "auto") {
					left = 0;
				} else {
					left = parseInt(left.replace("px", ""));
				}
				left += _wheelDelta;
				$("#leftIcon").addClass("layui-hide");
				$("#leftIcon2").removeClass("layui-hide");
				$("#rightIcon").addClass("layui-hide");
				$("#rightIcon2").removeClass("layui-hide");
				
				var width = $("div.layui-tab").width();
				
				if (left > 0) {
					left = 0;
					$("#leftIcon2").addClass("layui-hide");
					$("#leftIcon").removeClass("layui-hide");
				} else if (left < width - $(_obj).width()) {
					left = width - $(_obj).width();
					$("#rightIcon2").addClass("layui-hide");
					$("#rightIcon").removeClass("layui-hide");
				}
				$(_obj).children().css("left", left);
			}

			var $mousewheel = $("div.layui-tab-div");
			var width = $(window).width() - 450;//hwx 2022-4-6
//			var width = $("div.layui-tab").width();
			$("div.layui-tab").css("right", "auto").css("width", width);
			if ($mousewheel.width() > width) {
				$mousewheel.attr("title", "可鼠标滚轮查看更多");//hwx 2022-4-6
				$("#rightIcon").addClass("layui-hide");
				$("#rightIcon2").removeClass("layui-hide");
				$mousewheel.on("mousewheel", function(e) {
					if (e.preventDefault) {
						e.preventDefault();
					} else {
						e.returnValue = false;
					}
					
					var wheelDelta = 30;
					if (e.originalEvent.wheelDelta < 0) {
						wheelDelta = wheelDelta * -1
					}
					_changeLeft(this, wheelDelta);
				});
			}
			$("#rightIcon2").click(function(e) {
				_changeLeft($mousewheel[0], -30);
			});
			$("#leftIcon2").click(function(e) {
				_changeLeft($mousewheel[0], 30);
			});
			
		}
		renderMousewheel();
		
	},
	/**
	 * 字段
	 */
	customFieldSetting : function(callback) {
		var url = basePath + "frame/customList/customFieldSetting.html?";
		if (param) {
			url += param.__form();
		}
		if (callback) {
			url += "&callback=" + callback;
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "layer-preview",
			title : false,
			scrollbar : false,
			//closeBtn : 0,//取消右上角的×按钮
			area : [ '520px', '450px' ],
			content : url
		});
	},
	objMerge : function(target, source) {
		for ( var obj in source) {
			if (!target[obj]) {
				target[obj] = source[obj];
			} else {
				if (!target[obj] instanceof Array) {
					target[obj] = [ target[obj] ];
				} else {
					target[obj].push(source[obj]);
				}
			}
		}
		return target;
	},
	/**
	 * 表格列表
	 */
	tableRender : function(data) {
		var url = basePath + "frame/customList/getCustomListPager.spring?";
		var params = {};
		if (param) {
			params = customListModule.objMerge(params, param.__json());
		}

		if (filterParam) {
			params = customListModule.objMerge(params, filterParam.__json());
		}
		var events = {
			changeState : customListModule.changeState
		};
		
		for ( var i in data.cols) {
			if(data.cols[i].fieldKey == "number"){
				data.cols[i]["type"] = "numbers";
				//data.cols[i]["unresize"] = true //禁止拖拽列宽
			}
			if(data.cols[i].fieldKey == "operate"){
				data.cols[i]["templet"] = function(d) {
					var iDiv = ""
						for (var i = 0; i < data.opt.length; i++) {
							var temp = data.opt[i];
							var hasShow = true;
							if (temp.show && typeof temp.show == 'function') {
								hasShow = temp.show(d);
							}
							if (hasShow) {
								events[i + ""] = temp.onclick;
								iDiv += '<i class="layui-icon ' + temp.icon + ' i_delete" title="' + temp.title + '"  lay-event="' + i + '" ></i>';
							}
						}
						return iDiv;
					}
			}
		}
		
		var newCole = [];

		$.ajaxSetup({
			traditional : true
		});
		
		// 加载layui模板引擎
		assemblys.tableRender({
			"url" : url,
			"method" : "post",
			"where" : params,
			"elem" : data.elem || "#list",
			"cols" : [ newCole.concat(data.cols) ],
			"done" : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				if ($("#filterNum")) {
					$("#filterNum").html(count);
				}
			},
			"events" : events
		});
	},
	changeState : function(d) {
		var $a = $("a[customFormFilledCode=" + d.customFormFilledCode + "]");
		if ($a.attr("isDropdownRender")) {
			return;
		}
		var dataAry = [];
		for (var i = 0; i < customList.stateList.length; i++) {
			if (customList.stateList[i].customFormTypeStateStatus == "STATE_ROLLBACK" || customList.stateList[i].customFormTypeStateStatus == "STATE_END" || customList.stateList[i].customFormTypeStateStatus == "STATE_INVALID") {
				continue;
			}
			dataAry.push({
				title : customList.stateList[i].customFormTypeStateName,
				state : customList.stateList[i].customFormTypeStateNo,
				stateColor : customList.stateList[i].customFormTypeStateColor,
			});
		}
		
		layui.dropdown.render({
			elem : $a[0],
			data : dataAry,
			trigger : "click",
			click : function(data, obj) {
				$.ajax({
					url : basePath + "frame/newCustomForm/updateCustomFormFilledStatus.spring",
					data : {
						appCode : param.get("appCode"),
						customFormFilledCode : d.customFormFilledCode,
						status : data.state
					},
					success : function() {
						$a.css({
							border : "1px solid " + data.stateColor,
							color : data.stateColor
						}).text(data.title);
					}
				})
			}
		});
		
		$a.attr("isDropdownRender", true);
		$a.click();
	},
	// 导出列表
	exportList : function(fileName) {
		if (!fileName) {
			fileName = $.trim($("span[titleName]").text()) || "列表";
		}
		var url = basePath + "frame/customList/exportList.spring?" + param.__form() + "&" + filterParam.__form();
		url += "&fileName=" + encodeURIComponent(fileName);
		location.href = url;
	},
}
