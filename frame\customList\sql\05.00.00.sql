SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'customfieldsetting' 

-- sqlSplit

DROP TABLE IF EXISTS `customfieldsetting`;

-- sqlSplit

CREATE TABLE `customfieldsetting`  (
  `customFieldSettingID` int(11) NOT NULL AUTO_INCREMENT,
  `compNo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公司编号',
	`customFieldCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin   COMMENT '自定义表单组件编号',
	`customFormTypeCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin   COMMENT '自定义表单分类编号',
  PRIMARY KEY (`CustomFieldSettingID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '自定义列表字段设置表' ROW_FORMAT = Dynamic;
