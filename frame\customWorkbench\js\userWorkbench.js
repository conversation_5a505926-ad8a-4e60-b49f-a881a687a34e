var hasSubmit = false;
var userWorkbench = {
	// 卡片下标
	cardIndex : 0,
	tableIndex : 0,
	init : function() {
		userWorkbench.getUserWorkBench().then(function() {
			userWorkbench.getHideKejinMsgRule();
			userWorkbench.loadScrollMonitor();
		});
	},
	getHideKejinMsgRule : function() {
		return $.ajax({
			url : basePath + "frame/dict/getDictByCode.spring",
			type : "get",
			data : {
				"appCode" : "APP",
				"dictCode" : "hideKejinMsgRule"
			},
			async : false,
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.dict) {
					$(".container").css({
						"background-image" : "url(images/water_mark.png)",
						"background-color" : "#eee"
					})
				}
			}
		});
	},
	loadScrollMonitor : function() {
		$("iframe[data-src]").scrollLoading({
			// 识别标签
			"attr" : "data-src",
			// 指定父
			"container" : $(".container")
		})
	},
	getUserWorkBench : function() {
		return $.ajax({
			url : basePath + "frame/customWorkbench/getUserWorkBench.spring",
			type : "get",
			data : {
				"appCode" : param.get("appCode"),
				"isPcShow" : "1"
			},
			dataType : "json",
			success : function(data) {
				// 权限
				initUserWorkbench.initRight(data.hasExecRight);
				// 卡片显示
				var list = data.customWorkBenchList;
				if (list.length > 0) {
					// 父类方法
					var checkHandleUrl = parent.pubApp.checkHandleUrl;
					// 处理卡片
					for (var i = 0; i < list.length; i++) {
						var temp = list[i];
						var tempData = {
							"title" : temp.customWorkbenchModuleName,
							"dataUrl" : userWorkbench.handleUrl((checkHandleUrl(temp.dataUrl) || ""), temp.funCode),
							"url" : userWorkbench.handleUrl((checkHandleUrl(temp.url) || ""), temp.funCode),
							"icon" : temp.moduleIcon,
							"iconType" : temp.moduleIconType,
							"fontColor" : temp.moduleNameColor,
							"iconColor" : temp.moduleIconColor,
							"iconBgColor" : temp.iconBackGroundColor,
							"bgColor" : temp.moduleBackGroundColor,
							"click" : temp.isPcClick
						}
						// 按类型回显
						if (temp.moduleType == 0) {
							userWorkbench.addCard(tempData);
						} else {
							userWorkbench.addTable(tempData);
						}
					}
				}
				// 处理浮动
				initUserWorkbench.addLastDiv();
			}
		});
	},
	handleUrl : function(url, funCode) {
		if (url == "") {
			return url;
		}
		if (url.indexOf("&funCode=") == -1 && url.indexOf("?funCode=") == -1) {
			if (url.indexOf("?") != -1) {
				url += "&funCode=" + funCode;
			} else {
				url += "?funCode=" + funCode;
			}
		}
		if (url.indexOf("$appCode=") == -1 && url.indexOf("?appCode=") == -1 && url.indexOf("@appCode=") == -1) {
			if (url.indexOf("&appCode=") == -1) {
				url += "&appCode=" + param.get("appCode");
			}
		}
		return url;
	},
	// 加载卡片
	addCard : function(data) {
		
		var url = data.dataUrl;
		// 卡片ID
		var cardID = "card" + userWorkbench.cardIndex;
		var child = []
		// 如果有数据
		if (url) {
			child = [ {
				"tagName" : "div",
				"className" : "jur_num",
				"id" : cardID,
			}, {
				"tagName" : "div",
				"className" : data.url && data.click == "1" ? "jur_text  jur_text_sm cursor" : "jur_text jur_text_sm",
				"style" : {
					"color" : data.fontColor || "#000"
				},
				"title" : data.title,
				"innerText" : data.title
			} ];
		} else {
			child.push({
				"tagName" : "div",
				"className" : data.url && data.click == "1" ? "jur_text cursor" : "jur_text",
				"style" : {
					"color" : data.fontColor || "#000"
				},
				"title" : data.title,
				"innerText" : data.title
			});
		}
		var card = {
			"tagName" : "div",
			"className" : data.url && data.click == "1" ? "jurs cursor" : "jurs",
			"onclick" : function() {
				if (!data.url || data.click == "0") {
					return;
				}
				assemblys.top.addTab(null, data.title, data.url);
			},
			"children" : [ {
				"tagName" : "div",
				"className" : data.bgColor ? "jur_card jur" : "jur_card jur skin-div-css",
				"style" : {
					"background" : data.bgColor || ""
				},
				"children" : [ {
					"tagName" : "div",
					"className" : "jur_left",
					"style" : {
						"background" : data.iconBgColor || "#f2f2f2"
					},
					"children" : [ {
						"tagName" : "i",
						"className" : "layui-icon" + data.iconType,
						"style" : {
							"color" : data.iconColor || "#000",
							"cursor" : (data.url && data.click == "1" ? "pointer" : "default")
						},
						"innerHTML" : data.icon || "&#xe779;"
					} ]
				}, {
					"tagName" : "div",
					"className" : "jur_right",
					"children" : child
				} ],
			} ]
		}
		assemblys.createElement(card, $(".jur_box")[0]);
		// 加载数据
		userWorkbench.getDataNum(cardID, url);
		//hwx 2023-12-07 定时刷新工作台小卡片数据 
		/*if(url){
			window.setInterval(function(){
				userWorkbench.getDataNum(cardID, url);
			},3000)
		}*/
		// 追加
		userWorkbench.cardIndex++;
		
	},
	// 加载数字
	getDataNum : function(cardID, url) {
		if (!url) {
			return;
		}
		$.ajax({
			url : url,
			type : "get",
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				var count = data.count || data.data.count || data.totalCount || data.data.totalCount || 0;
				if (count && count > 0) {
					$("#" + cardID).html(count > 100000 ? "99999+" : count);
				}
			}
		});
	},
	// 加载表格
	addTable : function(data) {
		var id = "table" + userWorkbench.tableIndex;
		var table = {
			"tagName" : "div",
			"className" : "md2 layui-col-xs12 pd",
			"children" : [ {
				"tagName" : "div",
				"className" : "layui-card",
				"children" : [ {
					"tagName" : "div",
					"className" : "layui-colla-item",
					"children" : [ {
						"tagName" : "h2",
						"className" : "layui-colla-title",
						"children" : [ {
							"tagName" : "span",
							"className" : "titleFont",
							"children" : [ {
								"tagName" : "i",
								"className" : "layui-icon" + data.iconType,
								"style" : {
									"color" : data.iconColor || "#000"
								},
								"innerHTML" : data.icon || "&#xe779;"
							}, {
								"tagName" : "span",
								"style" : {
									"color" : data.fontColor || "#000"
								},
								"innerText" : " " + data.title
							} ]
						} ]
					}, {
						"tagName" : "div",
						"className" : "layui-colla-content table_Hight layui-show",
						"children" : [ {
							"tagName" : "iframe",
							"id" : id,
							"attr" : {
								"data-src" : data.url,
								"frameborder" : "0",
								"width" : "100%",
								"height" : "100%"
							}
						} ]
					} ]
				} ]
			} ]
		}
		assemblys.createElement(table, $(".jur_table")[0]);
		// 追加
		userWorkbench.tableIndex++;
	}
}
$(function() {
	userWorkbench.init();
});
