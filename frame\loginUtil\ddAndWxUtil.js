var ddAndWxUtil = {
	/**
	 * 扫码登录地址，即钉钉或企业微信扫码后，后台响应地址，通过这个地址获取当前扫码人信息
	 */
	loginUrl : "/frame/excludeUrl/scanCode/scanCodeLogin.spring?1=1",
	/**
	 * 钉钉二维码初始化
	 */
	ddQRCodeInit : function(data, id) {
		// 1、拼装扫码回调地址 begin
		var redirect_uri = data.redirectUri + ddAndWxUtil.loginUrl;
		redirect_uri += "&userCode=" +  (window.scanQRCodeBind ? window.scanQRCodeBind.userCode : "");
		redirect_uri += "&optType=" +  (window.scanQRCodeBind ? window.scanQRCodeBind.optType : "");
		// 在扫码回调地址后面拼接配置信息
		redirect_uri += "&appkey=" + data.appkey + "&appsecret=" + data.secret + "&compNo=" + data.compNo;
		redirect_uri += "&accessAppId=" + data.accessAppId + "&accessAppSecret=" + data.accessAppSecret;
		redirect_uri = encodeURIComponent(redirect_uri);
		
		var dd_uri = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=" + data.accessAppId + "&response_type=code&scope=snsapi_login&state=ddUserID&redirect_uri=" + redirect_uri;
		// 拼装扫码回调地址 end
		
		// 2、官方生成二维码 begin
		var obj = DDLogin({
		    id: id,// 二维码绑定的HTML标签ID
		    goto: encodeURIComponent(dd_uri), 
		    style: "border:none;background-color:#FFFFFF;margin-top:-10px;",
		    width : "365",
		    height: "400"
		});
		var handleMessage = function(event) {
			var origin = event.origin;
			if (origin == "https://login.dingtalk.com") { // 判断是否来自ddLogin扫码事件。
				var loginTmpCode = event.data; // 拿到loginTmpCode后就可以在这里构造跳转链接进行跳转了
				window.location.href = dd_uri + "&loginTmpCode=" + loginTmpCode;
			}
		};
		if (typeof window.addEventListener != 'undefined') {
			window.addEventListener('message', handleMessage, false);
		} else if (typeof window.attachEvent != 'undefined') {
			window.attachEvent('onmessage', handleMessage);
		}
		// 官方生成二维码 end
	},
	/**
	 * 企业微信二维码初始化
	 */
	wxQRCodeInit : function(data, id) {
		// 1、拼装扫码回调地址 begin
		var redirect_uri = data.redirectUri + ddAndWxUtil.loginUrl;
		redirect_uri += "&userCode=" +  (window.scanQRCodeBind ? window.scanQRCodeBind.userCode : "");
		redirect_uri += "&optType=" +  (window.scanQRCodeBind ? window.scanQRCodeBind.optType : "");
		// 在扫码回调地址后面拼接配置信息
		redirect_uri += "&appkey=" + data.corpID + "&appsecret=" + data.secret + "&compNo=" + data.compNo;

		redirect_uri = encodeURIComponent(redirect_uri);
		// 拼装扫码回调地址 end
		
		// 2、官方生成二维码 begin
		window.WwLogin({
			"id" : id, // 二维码绑定的HTML标签ID
			"appid" : data.corpID,
			"agentid" : data.agentID,
			"redirect_uri" : redirect_uri,
			"state" : "wxUserID",// 自定义参数值
			"href" : "",
		});
		// 官方生成二维码 end
	}
}