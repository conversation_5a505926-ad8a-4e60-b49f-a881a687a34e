(function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.dd=t():e.dd=t()})(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=478)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(2),o=n(143),i=n(2);t.ENV_ENUM=i.ENV_ENUM;var a=n(3);n(146),t.ddSdk=new a.Sdk(r.getENV(),o.log)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addWatchParamsDeal=function(e){var t=Object.assign({},e);return t.watch=!0,t},t.addDefaultCorpIdParamsDeal=function(e){var t=Object.assign({},e);return t.corpId="corpId",t},t.genDefaultParamsDealFn=function(e){var t=Object.assign({},e);return function(e){return Object.assign(t,e)}},t.forceChangeParamsDealFn=function(e){var t=Object.assign({},e);return function(e){return Object.assign(e,t)}},t.genBoolResultDealFn=function(e){return function(t){var n=Object.assign({},t);return e.forEach(function(e){void 0!==n[e]&&(n[e]=!!n[e])}),n}},t.genBizStoreParamsDealFn=function(e){var t=Object.assign({},e);return"string"!=typeof t.params?(t.params=JSON.stringify(t),t):t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(3),o=n(3);t.ENV_ENUM=o.ENV_ENUM,t.getENV=function(){var e="";"undefined"!=typeof navigator&&(e=navigator&&(navigator.userAgent||navigator.swuserAgent)||"");var t,n=-1!==e.indexOf("AliApp")&&-1!==e.indexOf("DingTalk"),o=/iPhone|iPad|iPod/i.test(e)&&n,i=/Android/i.test(e)&&n,a=(e.indexOf("Mac"),e.indexOf("Win"),e.indexOf("dingtalk-win"),/Nebula/i.test(e)&&n),s=a?r.APP_TYPE.MINI_APP:r.APP_TYPE.WEB,d="*",u=e.match(/AliApp\(\w+\/([a-zA-Z0-9.-]+)\)/);null===u&&(u=e.match(/DingTalk\/([a-zA-Z0-9.-]+)/));var c;u&&u[1]&&(c=u[1]);var l="";if("undefined"!=typeof name&&(l=name),l)try{var f=JSON.parse(l);f.hostVersion&&(c=f.hostVersion),d=f.language||"*",t=f.containerId}catch(e){}var v,p=!!t;return v=o?r.ENV_ENUM.ios:i?r.ENV_ENUM.android:p?r.ENV_ENUM.pc:r.ENV_ENUM.notInDingTalk,{platform:v,version:c,appType:s,language:d}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(149);t.APP_TYPE=r.APP_TYPE,t.LogLevel=r.LogLevel,t.isFunction=r.isFunction,t.compareVersion=r.compareVersion,t.ENV_ENUM=r.ENV_ENUM;var o=function(){function e(e,t){var n=this;this.configJsApiList=[],this.hadConfig=!1,this.p={},this.config$=new Promise(function(e,t){n.p.reject=t,n.p.resolve=e}),this.logQueue=[],this.devConfig={debug:!1},this.platformConfigMap={},this.invokeAPIConfigMapByMethod={},this.isBridgeDrity=!0,this.getExportSdk=function(){return n.exportSdk},this.setAPI=function(e,t){n.invokeAPIConfigMapByMethod[e]=t},this.setPlatform=function(e){n.isBridgeDrity=!0,n.platformConfigMap[e.platform]=e,e.platform===n.env.platform&&e.bridgeInit().catch(function(e){n.customLog(r.LogLevel.WARNING,["auto bridgeInit error",e||""])})},this.getPlatformConfigMap=function(){return n.platformConfigMap},this.deleteApiConfig=function(e,t){var r=n.invokeAPIConfigMapByMethod[e];r&&delete r[t]},this.invokeAPI=function(e,t,o){void 0===t&&(t={}),void 0===o&&(o=!0),n.customLog(r.LogLevel.INFO,['==> "'+e+'" params: ',t]);var i=+new Date,a=i+"_"+Math.floor(1e3*Math.random());return n.devConfig.onBeforeInvokeAPI&&n.devConfig.onBeforeInvokeAPI({invokeId:a,method:e,params:t,startTime:i}),!1===n.devConfig.isAuthApi&&(o=!1),n.bridgeInitFn().then(function(s){var d=n.invokeAPIConfigMapByMethod[e],u=!0===n.devConfig.isDisableDeal||n.devConfig.disbaleDealApiWhiteList&&-1!==n.devConfig.disbaleDealApiWhiteList.indexOf(e);if(d||!o){var c;if(d&&(c=d[n.env.platform]),c||!o){var l={};l=!u&&c&&c.paramsDeal&&r.isFunction(c.paramsDeal)?c.paramsDeal(t):Object.assign({},t);var f=function(e){return!u&&c&&c.resultDeal&&r.isFunction(c.resultDeal)?c.resultDeal(e):e};if(r.isFunction(l.onSuccess)){var v=l.onSuccess;l.onSuccess=function(e){v(f(e))}}return s(e,l).then(f,function(t){var i=n.hadConfig&&void 0===n.isReady&&-1!==n.configJsApiList.indexOf(e),a="object"==typeof t&&"string"==typeof t.errorCode&&t.errorCode===r.ERROR_CODE.no_permission,d="object"==typeof t&&"string"==typeof t.errorCode&&t.errorCode===r.ERROR_CODE.cancel,u=c&&c.vs&&n.env.version&&r.compareVersion(n.env.version,c.vs),v=(n.env.platform===r.ENV_ENUM.ios||n.env.platform===r.ENV_ENUM.android)&&i&&a,p=n.env.platform===r.ENV_ENUM.pc&&i&&(u&&!d&&o||a);return v||p?n.config$.then(function(){return s(e,l).then(f)}):Promise.reject(t)}).then(function(o){return n.devConfig.onAfterInvokeAPI&&n.devConfig.onAfterInvokeAPI({invokeId:a,method:e,params:t,payload:o,isSuccess:!0,startTime:i,duration:+new Date-i}),n.customLog(r.LogLevel.INFO,['<== "'+e+'" success result: ',o]),o},function(o){return n.devConfig.onAfterInvokeAPI&&n.devConfig.onAfterInvokeAPI({invokeId:a,method:e,params:t,payload:o,startTime:i,duration:+new Date-i,isSuccess:!1}),n.customLog(r.LogLevel.WARNING,['<== "'+e+'" fail result: ',o]),Promise.reject(o)})}var p='"'+e+'" do not support the current platform ('+n.env.platform+")";return n.customLog(r.LogLevel.ERROR,[p]),Promise.reject({errorCode:r.ERROR_CODE.jsapi_internal_error,errorMessage:p})}var p="This API method is not configured for the platform ("+n.env.platform+")";return n.customLog(r.LogLevel.ERROR,[p]),Promise.reject({errorCode:r.ERROR_CODE.jsapi_internal_error,errorMessage:p})})},this.customLog=function(e,t){var r={level:e,text:t,time:new Date};!0===n.devConfig.debug?n.customLogInstance(r):n.logQueue.push(r)},this.clearLogQueue=function(){n.logQueue.forEach(function(e){n.customLogInstance(e)}),n.logQueue=[]},this.customLogInstance=t,this.env=e,this.bridgeInitFn=function(){if(n.bridgeInitFnPromise&&!n.isBridgeDrity)return n.bridgeInitFnPromise;n.isBridgeDrity=!1;var t=n.platformConfigMap[e.platform];if(t)n.bridgeInitFnPromise=t.bridgeInit().catch(function(e){return n.customLog(r.LogLevel.ERROR,["\b\b\b\b\bJsBridge initialization fails, jsapi will not work"]),Promise.reject(e)});else{var o="Do not support the current environment："+e.platform;n.customLog(r.LogLevel.WARNING,[o]),n.bridgeInitFnPromise=Promise.reject(new Error(o))}return n.bridgeInitFnPromise};var o=function(e){void 0===e&&(e={}),n.devConfig=Object.assign(n.devConfig,e),!0===e.debug&&n.clearLogQueue(),e.extraPlatform&&n.setPlatform(e.extraPlatform)};this.exportSdk={config:function(t){void 0===t&&(t={});var i=!0;Object.keys(t).forEach(function(e){-1===["debug","usePromise"].indexOf(e)&&(i=!1)}),i?(n.customLog(r.LogLevel.WARNING,["This is a deprecated feature, recommend use dd.devConfig"]),o(t)):n.hadConfig?n.customLog(r.LogLevel.WARNING,["Config has been executed"]):(t.jsApiList&&(n.configJsApiList=t.jsApiList),n.hadConfig=!0,n.bridgeInitFn().then(function(r){var o=n.platformConfigMap[e.platform],i=t;o.authParamsDeal&&(i=o.authParamsDeal(i)),r(o.authMethod,i).then(function(e){n.isReady=!0,n.p.resolve(e)}).catch(function(e){n.isReady=!1,n.p.reject(e)})},function(){n.customLog(r.LogLevel.ERROR,['\b\b\b\b\bJsBridge initialization failed and "dd.config" failed to call'])}))},devConfig:o,ready:function(e){!1===n.hadConfig?(n.customLog(r.LogLevel.WARNING,["You don 't use a dd.config, so you don't need to wrap dd.ready, recommend remove dd.ready"]),n.bridgeInitFn().then(function(){e()})):n.config$.then(function(t){e()})},error:function(e){n.config$.catch(function(t){e(t)})},on:function(t,r){n.bridgeInitFn().then(function(){n.platformConfigMap[e.platform].event.on(t,r)})},off:function(t,r){n.bridgeInitFn().then(function(){n.platformConfigMap[e.platform].event.off(t,r)})},env:e,checkJsApi:function(t){void 0===t&&(t={});var o={};return t.jsApiList&&t.jsApiList.forEach(function(t){var i=n.invokeAPIConfigMapByMethod[t];if(i){var a=i[e.platform];a&&a.vs&&e.version&&r.compareVersion(e.version,a.vs)&&(o[t]=!0)}o[t]||(o[t]=!1)}),Promise.resolve(o)},_invoke:function(e,t){return void 0===t&&(t={}),n.invokeAPI(e,t,!1)}}}return e}();t.Sdk=o},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){(function(t,n){e.exports=n()})(0,function(){return function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=478)}({156:function(e,t,n){"use strict";var r=n(159);e.exports=r},158:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},159:function(e,t,n){"use strict";var r=n(161),o=n(162),i=n(160),a=n(163),s=new i,d=!1,u="",c=null,l={},f=/{.*}/;try{var v=window.name.match(f);if(v&&v[0])var l=JSON.parse(v[0])}catch(e){l={}}l.hostOrigin&&".dingtalk.com"===l.hostOrigin.split(":")[1].slice(0-".dingtalk.com".length)&&l.containerId&&(d=!0,u=l.hostOrigin,c=l.containerId);var p={},_=new Promise(function(e,t){p._resolve=e,p._reject=t}),E={},N=null;window.top!==window&&(N=window.top,p._resolve()),E[a.SYS_INIT]=function(e){N=e.frameWindow,p._resolve(),e.respond({})},window.addEventListener("message",function(e){var t=e.data,n=e.origin;if(n===u)if("response"===t.type&&t.msgId){var r=t.msgId,i=s.getMsyById(r);i&&i.receiveResponse(t.body,!t.success)}else if("event"===t.type&&t.msgId){var r=t.msgId,i=s.getMsyById(r);i&&i.receiveEvent(t.eventName,t.body)}else if("request"===t.type&&t.msgId){var i=new o(e.source,n,t);E[i.methodName]&&E[i.methodName](i)}}),t.invokeAPI=function(e,t){var n=new r(c,e,t);return d&&_.then(function(){N&&N.postMessage(n.getPayload(),u),s.addPending(n)}),n};var P=null;t.addEventListener=function(e,n){P||(P=t.invokeAPI(a.SYS_EVENT,{})),P.addEventListener(e,n)},t.removeEventListener=function(e,t){P&&P.removeEventListener(e,t)}},160:function(e,t,n){"use strict";var r=function(){this.pendingMsgs={}};r.prototype.addPending=function(e){this.pendingMsgs[e.id]=e;var t=function(){delete this.pendingMsgs[e.id],e.removeEventListener("_finish",t)}.bind(this);e.addEventListener("_finish",t)},r.prototype.getMsyById=function(e){return this.pendingMsgs[e]},e.exports=r},161:function(e,t,n){"use strict";var r=n(475),o=n(473),i=0,a=function(){return++i},s={code:408,reason:"timeout"},d={TIMEOUT:"_timeout",FINISH:"_finish"},u={timeout:-1},c=function(e,t,n,r){this.id=a(),this.methodName=t,this.containerId=e,this.option=o({},u,r);var n=n||{};this._p={},this.result=new Promise(function(e,t){this._p._resolve=e,this._p._reject=t}.bind(this)),this.callbacks={},this.plainMsg=this._handleMsg(n),this._eventsHandle={},this._timeoutTimer=null,this._initTimeout(),this.isFinish=!1};c.prototype._initTimeout=function(){this._clearTimeout(),this.option.timeout>0&&(this._timeoutTimer=setTimeout(function(){this.receiveEvent(d.TIMEOUT),this.receiveResponse(s,!0)}.bind(this),this.option.timeout))},c.prototype._clearTimeout=function(){clearTimeout(this._timeoutTimer)},c.prototype._handleMsg=function(e){var t={};return Object.keys(e).forEach(function(n){var o=e[n];"function"==typeof o&&"on"===n.slice(0,2)?this.callbacks[n]=o:t[n]=r(o)}.bind(this)),t},c.prototype.getPayload=function(){return{msgId:this.id,containerId:this.containerId,methodName:this.methodName,body:this.plainMsg,type:"request"}},c.prototype.receiveEvent=function(e,t){if(this.isFinish&&e!==d.FINISH)return!1;e!==d.FINISH&&e!==d.TIMEOUT&&this._initTimeout(),Array.isArray(this._eventsHandle[e])&&this._eventsHandle[e].forEach(function(e){try{e(t)}catch(e){console.error(t)}});var n="on"+e.charAt(0).toUpperCase()+e.slice(1);return this.callbacks[n]&&this.callbacks[n](t),!0},c.prototype.addEventListener=function(e,t){if(!e||"function"!=typeof t)throw"eventName is null or handle is not a function, addEventListener fail";Array.isArray(this._eventsHandle[e])||(this._eventsHandle[e]=[]),this._eventsHandle[e].push(t)},c.prototype.removeEventListener=function(e,t){if(!e||!t)throw"eventName is null or handle is null, invoke removeEventListener fail";if(Array.isArray(this._eventsHandle[e])){var n=this._eventsHandle[e].indexOf(t);-1!==n&&this._eventsHandle[e].splice(n,1)}},c.prototype.receiveResponse=function(e,t){if(!0===this.isFinish)return!1;this._clearTimeout();var t=!!t;return t?this._p._reject(e):this._p._resolve(e),setTimeout(function(){this.receiveEvent(d.FINISH)}.bind(this),0),this.isFinish=!0,!0},e.exports=c},162:function(e,t,n){"use strict";var r=function(e,t,n){if(this._msgId=n.msgId,this.frameWindow=e,this.methodName=n.methodName,this.clientOrigin=t,this.containerId=n.containerId,this.params=n.body,!this._msgId)throw"msgId not exist";if(!this.frameWindow)throw"frameWindow not exist";if(!this.methodName)throw"methodName not exits";if(!this.clientOrigin)throw"clientOrigin not exist";this.hasResponded=!1};r.prototype.respond=function(e,t){var t=!!t;if(!0!==this.hasResponded){var n={type:"response",success:!t,body:e,msgId:this._msgId};this.frameWindow.postMessage(n,this.clientOrigin),this.hasResponded=!0}},r.prototype.emit=function(e,t){var n={type:"event",eventName:e,body:t,msgId:this._msgId};this.frameWindow.postMessage(n,this.clientOrigin)},e.exports=r},163:function(e,t,n){"use strict";e.exports={SYS_EVENT:"SYS_openAPIContainerInitEvent",SYS_INIT:"SYS_openAPIContainerInit"}},4:function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},472:function(e,t){function n(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function r(e,t){return t=y(void 0===t?e.length-1:t,0),function(){for(var r=arguments,o=-1,i=y(r.length-t,0),a=Array(i);++o<i;)a[o]=r[t+o];o=-1;for(var s=Array(t+1);++o<t;)s[o]=r[o];return s[t]=a,n(e,this,s)}}function o(e,t){if("function"!=typeof e)throw new TypeError(l);return t=void 0===t?t:u(t),r(e,t)}function i(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function a(e){return!!e&&"object"==typeof e}function s(e){return"symbol"==typeof e||a(e)&&k.call(e)==_}function d(e){return e?(e=c(e))===f||e===-f?(e<0?-1:1)*v:e===e?e:0:0===e?e:0}function u(e){var t=d(e),n=t%1;return t===t?n?t-n:t:0}function c(e){if("number"==typeof e)return e;if(s(e))return p;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(E,"");var n=P.test(e);return n||h.test(e)?m(e.slice(2),n?2:8):N.test(e)?p:+e}var l="Expected a function",f=1/0,v=1.7976931348623157e308,p=NaN,_="[object Symbol]",E=/^\s+|\s+$/g,N=/^[-+]0x[0-9a-f]+$/i,P=/^0b[01]+$/i,h=/^0o[0-7]+$/i,m=parseInt,M=Object.prototype,k=M.toString,y=Math.max;e.exports=o},473:function(e,t,n){function r(e,t,n){var r=e[t];m.call(e,t)&&d(r,n)&&(void 0!==n||t in e)||(e[t]=n)}function o(e,t,n,o){n||(n={});for(var i=-1,a=t.length;++i<a;){var s=t[i];r(n,s,o?o(n[s],e[s],s,n,e):e[s])}return n}function i(e,t){return!!(t=null==t?_:t)&&("number"==typeof e||P.test(e))&&e>-1&&e%1==0&&e<t}function a(e,t,n){if(!f(n))return!1;var r=typeof t;return!!("number"==r?u(n)&&i(t,n.length):"string"==r&&t in n)&&d(n[t],e)}function s(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||h)}function d(e,t){return e===t||e!==e&&t!==t}function u(e){return null!=e&&l(b(e))&&!c(e)}function c(e){var t=f(e)?M.call(e):"";return t==E||t==N}function l(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=_}function f(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}var v=n(474),p=n(472),_=9007199254740991,E="[object Function]",N="[object GeneratorFunction]",P=/^(?:0|[1-9]\d*)$/,h=Object.prototype,m=h.hasOwnProperty,M=h.toString,k=h.propertyIsEnumerable,y=!k.call({valueOf:1},"valueOf"),b=function(e){return function(e){return null==e?void 0:e.length}}(),g=function(e){return p(function(t,n){var r=-1,o=n.length,i=o>1?n[o-1]:void 0,s=o>2?n[2]:void 0;for(i=e.length>3&&"function"==typeof i?(o--,i):void 0,s&&a(n[0],n[1],s)&&(i=o<3?void 0:i,o=1),t=Object(t);++r<o;){var d=n[r];d&&e(t,d)}return t})}(function(e,t){if(y||s(t)||u(t))return void o(t,v(t),e);for(var n in t)m.call(t,n)&&r(e,n,t[n])});e.exports=g},474:function(e,t){function n(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function r(e,t){var r=g(e)||s(e)?n(e.length,String):[],o=r.length,a=!!o;for(var d in e)!t&&!M.call(e,d)||a&&("length"==d||i(d,o))||r.push(d);return r}function o(e){if(!a(e))return b(e);var t=[];for(var n in Object(e))M.call(e,n)&&"constructor"!=n&&t.push(n);return t}function i(e,t){return!!(t=null==t?_:t)&&("number"==typeof e||h.test(e))&&e>-1&&e%1==0&&e<t}function a(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||m)}function s(e){return u(e)&&M.call(e,"callee")&&(!y.call(e,"callee")||k.call(e)==E)}function d(e){return null!=e&&l(e.length)&&!c(e)}function u(e){return v(e)&&d(e)}function c(e){var t=f(e)?k.call(e):"";return t==N||t==P}function l(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=_}function f(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function v(e){return!!e&&"object"==typeof e}function p(e){return d(e)?r(e):o(e)}var _=9007199254740991,E="[object Arguments]",N="[object Function]",P="[object GeneratorFunction]",h=/^(?:0|[1-9]\d*)$/,m=Object.prototype,M=m.hasOwnProperty,k=m.toString,y=m.propertyIsEnumerable,b=function(e,t){return function(n){return e(t(n))}}(Object.keys,Object),g=Array.isArray;e.exports=p},475:function(e,t,n){function r(e){return o(e,!0,!0)}var o=n(476);e.exports=r},476:function(e,t,n){(function(e,n){function r(e,t){return e.set(t[0],t[1]),e}function o(e,t){return e.add(t),e}function i(e,t){for(var n=-1,r=e.length;++n<r&&!1!==t(e[n],n,e););return e}function a(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function s(e,t,n,r){var o=-1,i=e.length;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}function d(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function u(e){return e&&e.Object===Object?e:null}function c(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}function l(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n}function f(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}function v(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function p(){this.__data__=$t?$t(null):{}}function _(e){return this.has(e)&&delete this.__data__[e]}function E(e){var t=this.__data__;if($t){var n=t[e];return n===Se?void 0:n}return Et.call(t,e)?t[e]:void 0}function N(e){var t=this.__data__;return $t?void 0!==t[e]:Et.call(t,e)}function P(e,t){return this.__data__[e]=$t&&void 0===t?Se:t,this}function h(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function m(){this.__data__=[]}function M(e){var t=this.__data__,n=L(t,e);return!(n<0||(n==t.length-1?t.pop():gt.call(t,n,1),0))}function k(e){var t=this.__data__,n=L(t,e);return n<0?void 0:t[n][1]}function y(e){return L(this.__data__,e)>-1}function b(e,t){var n=this.__data__,r=L(n,e);return r<0?n.push([e,t]):n[r][1]=t,this}function g(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function I(){this.__data__={hash:new v,map:new(Vt||h),string:new v}}function A(e){return re(this,e).delete(e)}function S(e){return re(this,e).get(e)}function V(e){return re(this,e).has(e)}function U(e,t){return re(this,e).set(e,t),this}function j(e){this.__data__=new h(e)}function O(){this.__data__=new h}function $(e){return this.__data__.delete(e)}function w(e){return this.__data__.get(e)}function D(e){return this.__data__.has(e)}function C(e,t){var n=this.__data__;return n instanceof h&&n.__data__.length==Ae&&(n=this.__data__=new g(n.__data__)),n.set(e,t),this}function F(e,t,n){var r=e[t];Et.call(e,t)&&Ee(r,n)&&(void 0!==n||t in e)||(e[t]=n)}function L(e,t){for(var n=e.length;n--;)if(Ee(e[n][0],t))return n;return-1}function x(e,t){return e&&ee(t,Ie(t),e)}function T(e,t,n,r,o,a,s){var d;if(r&&(d=a?r(e,o,a,s):r(e)),void 0!==d)return d;if(!ke(e))return e;var u=Rt(e);if(u){if(d=de(e),!t)return X(e,d)}else{var l=se(e),f=l==$e||l==we;if(Bt(e))return q(e,t);if(l==Fe||l==Ue||f&&!a){if(c(e))return a?e:{};if(d=ue(f?{}:e),!t)return te(e,x(d,e))}else{if(!rt[l])return a?e:{};d=ce(e,l,T,t)}}s||(s=new j);var v=s.get(e);if(v)return v;if(s.set(e,d),!u)var p=n?ne(e):Ie(e);return i(p||e,function(o,i){p&&(i=o,o=e[i]),F(d,i,T(o,t,n,r,i,e,s))}),d}function z(e){return ke(e)?yt(e):{}}function R(e,t,n){var r=t(e);return Rt(e)?r:a(r,n(e))}function B(e,t){return Et.call(e,t)||"object"==typeof e&&t in e&&null===ie(e)}function W(e){return At(Object(e))}function q(e,t){if(t)return e.slice();var n=new e.constructor(e.length);return e.copy(n),n}function J(e){var t=new e.constructor(e.byteLength);return new Mt(t).set(new Mt(e)),t}function H(e,t){var n=t?J(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}function G(e,t,n){return s(t?n(l(e),!0):l(e),r,new e.constructor)}function Y(e){var t=new e.constructor(e.source,et.exec(e));return t.lastIndex=e.lastIndex,t}function Q(e,t,n){return s(t?n(f(e),!0):f(e),o,new e.constructor)}function K(e){return Tt?Object(Tt.call(e)):{}}function Z(e,t){var n=t?J(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function X(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}function ee(e,t,n,r){n||(n={});for(var o=-1,i=t.length;++o<i;){var a=t[o];F(n,a,r?r(n[a],e[a],a,n,e):e[a])}return n}function te(e,t){return ee(e,ae(e),t)}function ne(e){return R(e,Ie,ae)}function re(e,t){var n=e.__data__;return ve(t)?n["string"==typeof t?"string":"hash"]:n.map}function oe(e,t){var n=e[t];return be(n)?n:void 0}function ie(e){return It(Object(e))}function ae(e){return kt(Object(e))}function se(e){return Nt.call(e)}function de(e){var t=e.length,n=e.constructor(t);return t&&"string"==typeof e[0]&&Et.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function ue(e){return"function"!=typeof e.constructor||pe(e)?{}:z(ie(e))}function ce(e,t,n,r){var o=e.constructor;switch(t){case Re:return J(e);case je:case Oe:return new o(+e);case Be:return H(e,r);case We:case qe:case Je:case He:case Ge:case Ye:case Qe:case Ke:case Ze:return Z(e,r);case De:return G(e,r,n);case Ce:case Te:return new o(e);case Le:return Y(e);case xe:return Q(e,r,n);case ze:return K(e)}}function le(e){var t=e?e.length:void 0;return Me(t)&&(Rt(e)||ge(e)||Ne(e))?d(t,String):null}function fe(e,t){return!!(t=null==t?Ve:t)&&("number"==typeof e||nt.test(e))&&e>-1&&e%1==0&&e<t}function ve(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function pe(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||pt)}function _e(e){if(null!=e){try{return _t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Ee(e,t){return e===t||e!==e&&t!==t}function Ne(e){return he(e)&&Et.call(e,"callee")&&(!bt.call(e,"callee")||Nt.call(e)==Ue)}function Pe(e){return null!=e&&Me(zt(e))&&!me(e)}function he(e){return ye(e)&&Pe(e)}function me(e){var t=ke(e)?Nt.call(e):"";return t==$e||t==we}function Me(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Ve}function ke(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function ye(e){return!!e&&"object"==typeof e}function be(e){return!!ke(e)&&(me(e)||c(e)?Pt:tt).test(_e(e))}function ge(e){return"string"==typeof e||!Rt(e)&&ye(e)&&Nt.call(e)==Te}function Ie(e){var t=pe(e);if(!t&&!Pe(e))return W(e);var n=le(e),r=!!n,o=n||[],i=o.length;for(var a in e)!B(e,a)||r&&("length"==a||fe(a,i))||t&&"constructor"==a||o.push(a);return o}var Ae=200,Se="__lodash_hash_undefined__",Ve=9007199254740991,Ue="[object Arguments]",je="[object Boolean]",Oe="[object Date]",$e="[object Function]",we="[object GeneratorFunction]",De="[object Map]",Ce="[object Number]",Fe="[object Object]",Le="[object RegExp]",xe="[object Set]",Te="[object String]",ze="[object Symbol]",Re="[object ArrayBuffer]",Be="[object DataView]",We="[object Float32Array]",qe="[object Float64Array]",Je="[object Int8Array]",He="[object Int16Array]",Ge="[object Int32Array]",Ye="[object Uint8Array]",Qe="[object Uint8ClampedArray]",Ke="[object Uint16Array]",Ze="[object Uint32Array]",Xe=/[\\^$.*+?()[\]{}|]/g,et=/\w*$/,tt=/^\[object .+?Constructor\]$/,nt=/^(?:0|[1-9]\d*)$/,rt={};rt[Ue]=rt["[object Array]"]=rt[Re]=rt[Be]=rt[je]=rt[Oe]=rt[We]=rt[qe]=rt[Je]=rt[He]=rt[Ge]=rt[De]=rt[Ce]=rt[Fe]=rt[Le]=rt[xe]=rt[Te]=rt[ze]=rt[Ye]=rt[Qe]=rt[Ke]=rt[Ze]=!0,rt["[object Error]"]=rt[$e]=rt["[object WeakMap]"]=!1;var ot={function:!0,object:!0},it=ot[typeof t]&&t&&!t.nodeType?t:void 0,at=ot[typeof e]&&e&&!e.nodeType?e:void 0,st=at&&at.exports===it?it:void 0,dt=u(it&&at&&"object"==typeof n&&n),ut=u(ot[typeof self]&&self),ct=u(ot[typeof window]&&window),lt=u(ot[typeof this]&&this),ft=dt||ct!==(lt&&lt.window)&&ct||ut||lt||Function("return this")(),vt=Array.prototype,pt=Object.prototype,_t=Function.prototype.toString,Et=pt.hasOwnProperty,Nt=pt.toString,Pt=RegExp("^"+_t.call(Et).replace(Xe,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ht=st?ft.Buffer:void 0,mt=ft.Symbol,Mt=ft.Uint8Array,kt=Object.getOwnPropertySymbols,yt=Object.create,bt=pt.propertyIsEnumerable,gt=vt.splice,It=Object.getPrototypeOf,At=Object.keys,St=oe(ft,"DataView"),Vt=oe(ft,"Map"),Ut=oe(ft,"Promise"),jt=oe(ft,"Set"),Ot=oe(ft,"WeakMap"),$t=oe(Object,"create"),wt=_e(St),Dt=_e(Vt),Ct=_e(Ut),Ft=_e(jt),Lt=_e(Ot),xt=mt?mt.prototype:void 0,Tt=xt?xt.valueOf:void 0;v.prototype.clear=p,v.prototype.delete=_,v.prototype.get=E,v.prototype.has=N,v.prototype.set=P,h.prototype.clear=m,h.prototype.delete=M,h.prototype.get=k,h.prototype.has=y,h.prototype.set=b,g.prototype.clear=I,g.prototype.delete=A,g.prototype.get=S,g.prototype.has=V,g.prototype.set=U,j.prototype.clear=O,j.prototype.delete=$,j.prototype.get=w,j.prototype.has=D,j.prototype.set=C;var zt=function(e){return function(e){return null==e?void 0:e.length}}();kt||(ae=function(){return[]}),(St&&se(new St(new ArrayBuffer(1)))!=Be||Vt&&se(new Vt)!=De||Ut&&"[object Promise]"!=se(Ut.resolve())||jt&&se(new jt)!=xe||Ot&&"[object WeakMap]"!=se(new Ot))&&(se=function(e){var t=Nt.call(e),n=t==Fe?e.constructor:void 0,r=n?_e(n):void 0;if(r)switch(r){case wt:return Be;case Dt:return De;case Ct:return"[object Promise]";case Ft:return xe;case Lt:return"[object WeakMap]"}return t});var Rt=Array.isArray,Bt=ht?function(e){return e instanceof ht}:function(e){return function(){return!1}}();e.exports=T}).call(t,n(158)(e),n(4))},478:function(e,t,n){e.exports=n(156)}})})},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.alipay.pay";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.pay$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.calendar.chooseDateTime";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o)),t.chooseDateTime$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.calendar.chooseHalfDay";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o)),t.chooseHalfDay$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.calendar.chooseInterval";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o)),t.chooseInterval$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.calendar.chooseOneDay";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o[i.ENV_ENUM.android]={vs:"3.5.0",paramsDeal:a.addDefaultCorpIdParamsDeal},o)),t.chooseOneDay$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.chat.chooseConversationByCorpId",d=a.genDefaultParamsDealFn({max:50});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.6.0",paramsDeal:d},o)),t.chooseConversationByCorpId$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.chat.locationChatMessage";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.7.6"},o[i.ENV_ENUM.android]={vs:"2.7.6"},o)),t.locationChatMessage$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.chat.openSingleChat";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.4.10"},o[i.ENV_ENUM.android]={vs:"3.4.10"},o)),t.openSingleChat$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.chat.pickConversation";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.2"},o[i.ENV_ENUM.android]={vs:"2.4.2"},o)),t.pickConversation$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.chat.toConversation";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.toConversation$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.clipboardData.setData";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.7.0"},o[i.ENV_ENUM.android]={vs:"2.7.0"},o)),t.setData$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.contact.choose",d=a.genDefaultParamsDealFn({multiple:!0,startWithDepartmentId:0,users:[]});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.choose$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.contact.chooseMobileContacts";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.1"},o[i.ENV_ENUM.android]={vs:"3.1"},o)),t.chooseMobileContacts$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.contact.complexPicker";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.9.0"},o[i.ENV_ENUM.android]={vs:"2.9.0"},o[i.ENV_ENUM.pc]={vs:"4.3.5"},o)),t.complexPicker$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.contact.createGroup";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.createGroup$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.contact.departmentsPicker";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"4.2.5"},o[i.ENV_ENUM.ios]={vs:"3.0"},o[i.ENV_ENUM.android]={vs:"3.0"},o)),t.departmentsPicker$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.contact.externalComplexPicker";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"3.0"},o[i.ENV_ENUM.android]={vs:"3.0"},o)),t.externalComplexPicker$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.contact.externalEditForm";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.0"},o[i.ENV_ENUM.android]={vs:"3.0"},o)),t.externalEditForm$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.contact.setRule";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.15"},o[i.ENV_ENUM.android]={vs:"2.15"},o)),t.setRule$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.cspace.chooseSpaceDir";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.6"},o[i.ENV_ENUM.android]={vs:"3.5.6"},o)),t.chooseSpaceDir$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.cspace.preview";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.7.0"},o[i.ENV_ENUM.android]={vs:"2.7.0"},o)),t.preview$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.cspace.saveFile";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.7.6"},o[i.ENV_ENUM.android]={vs:"2.7.6"},o)),t.saveFile$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.customContact.choose",d=a.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.5.2",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.5.2",paramsDeal:d},o)),t.choose$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.customContact.multipleChoose",d=a.genDefaultParamsDealFn({isShowCompanyName:!1,max:50});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.multipleChoose$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.ding.create";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.1"},o[i.ENV_ENUM.android]={vs:"3.5.1"},o[i.ENV_ENUM.pc]={vs:"4.5.9"},o)),t.create$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.ding.post";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.post$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.event.notifyWeex";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"4.5.0"},o)),t.notifyWeex$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.intent.fetchData";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.7.6"},o[i.ENV_ENUM.android]={vs:"2.7.6"},o)),t.fetchData$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.map.locate";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.locate$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.map.search",d=a.genDefaultParamsDealFn({scope:500});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.search$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.map.view";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.view$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.microApp.openApp";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"4.5.6"},o[i.ENV_ENUM.android]={vs:"4.5.6"},o)),t.openApp$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.navigation.close";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o[i.ENV_ENUM.pc]={vs:"4.3.5"},o)),t.close$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.navigation.goBack";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.goBack$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.navigation.hideBar";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.6"},o[i.ENV_ENUM.android]={vs:"3.5.6"},o)),t.hideBar$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.navigation.quit";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o)),t.quit$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.navigation.replace";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.4.6"},o[i.ENV_ENUM.android]={vs:"3.4.6"},o)),t.replace$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.navigation.setIcon",d=a.genDefaultParamsDealFn({watch:!0,showIcon:!0,iconIndex:1});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.setIcon$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.navigation.setLeft",d=a.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.setLeft$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.navigation.setMenu";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0",paramsDeal:a.addWatchParamsDeal},o[i.ENV_ENUM.android]={vs:"2.6.0",paramsDeal:a.addWatchParamsDeal},o)),t.setMenu$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.navigation.setRight",d=a.genDefaultParamsDealFn({watch:!0,show:!0,control:!1,showIcon:!0,text:""});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.setRight$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.navigation.setTitle";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.setTitle$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.store.closeUnpayOrder";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},o)),t.closeUnpayOrder$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.store.createOrder";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},o)),t.createOrder$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.store.getPayUrl";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},o)),t.getPayUrl$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.store.inquiry";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.android]={vs:"4.3.7",paramsDeal:a.genBizStoreParamsDealFn},o[i.ENV_ENUM.pc]={vs:"4.5.3",paramsDeal:a.genBizStoreParamsDealFn},o)),t.inquiry$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.telephone.call";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.call$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.telephone.checkBizCall";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"4.0.0"},o[i.ENV_ENUM.ios]={vs:"3.5.6"},o[i.ENV_ENUM.android]={vs:"3.5.6"},o)),t.checkBizCall$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.telephone.quickCallList";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.5.6"},o[i.ENV_ENUM.ios]={vs:"3.5.6"},o[i.ENV_ENUM.android]={vs:"3.5.6"},o)),t.quickCallList$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.telephone.showCallMenu";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.showCallMenu$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.user.checkPassword";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"4.5.8"},o[i.ENV_ENUM.android]={vs:"4.5.8"},o)),t.checkPassword$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.user.get";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.get$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.chosen";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.chosen$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.datepicker";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.datepicker$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.datetimepicker";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.datetimepicker$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.decrypt";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.9.1"},o[i.ENV_ENUM.android]={vs:"2.9.1"},o)),t.decrypt$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.downloadFile";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o)),t.downloadFile$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.encrypt";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.9.1"},o[i.ENV_ENUM.android]={vs:"2.9.1"},o)),t.encrypt$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.isLocalFileExist";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o)),t.isLocalFileExist$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.multiSelect";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.0.0"},o[i.ENV_ENUM.android]={vs:"3.0.0"},o)),t.multiSelect$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.open";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.7.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.open$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.util.openLink",d=a.genDefaultParamsDealFn({credible:!0,showMenuBar:!0});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.7.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.openLink$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.openLocalFile";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o)),t.openLocalFile$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.openModal";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o)),t.openModal$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.openSlidePanel";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o)),t.openSlidePanel$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.previewImage";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"2.7.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.previewImage$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.previewVideo";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"4.3.7"},o[i.ENV_ENUM.android]={vs:"4.3.7"},o)),t.previewVideo$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.util.scan",d=a.genDefaultParamsDealFn({type:"qrCode"});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.scan$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.scanCard";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.scanCard$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.setScreenBrightnessAndKeepOn";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"4.3.3"},o[i.ENV_ENUM.android]={vs:"4.3.3"},o)),t.setScreenBrightnessAndKeepOn$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.util.share",d=a.genDefaultParamsDealFn({title:"",buttonName:"确定"});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.share$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.systemShare";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"4.5.11"},o[i.ENV_ENUM.android]={vs:"4.5.11"},o)),t.systemShare$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.timepicker";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.timepicker$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.uploadAttachment";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.7.0"},o[i.ENV_ENUM.android]={vs:"2.7.0"},o)),t.uploadAttachment$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="biz.util.uploadImage",d=a.genDefaultParamsDealFn({multiple:!1});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.uploadImage$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.uploadImageFromCamera";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.uploadImageFromCamera$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="biz.util.ut",s=function(e){var t=Object.assign({},e),n=t.value,r=[];if(n&&"object"==typeof n){for(var o in n)n[o]&&r.push(o+"="+n[o]);n=r.join(",")}return t.value=n,t};i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.5.0",paramsDeal:s},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:function(e){var t=Object.assign({},e),n=t.value;return n&&"object"==typeof n&&(n=JSON.stringify(n)),t.value=n,t}},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:s},o)),t.ut$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="channel.permission.requestAuthCode";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.0.0"},o[i.ENV_ENUM.android]={vs:"3.0.0"},o)),t.requestAuthCode$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.accelerometer.clearShake";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.clearShake$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="device.accelerometer.watchShake";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:function(e){return a.forceChangeParamsDealFn({sensitivity:3.2})(a.addWatchParamsDeal(e))}},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:a.addWatchParamsDeal},o)),t.watchShake$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.download";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.download$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.onPlayEnd";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.onPlayEnd$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.onRecordEnd";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.onRecordEnd$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.pause";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.pause$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.play";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.play$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.resume";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.resume$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.startRecord";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.startRecord$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.stop";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.stop$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.stopRecord";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.stopRecord$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.audio.translateVoice";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.8.0"},o[i.ENV_ENUM.android]={vs:"2.8.0"},o)),t.translateVoice$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.base.getInterface";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.getInterface$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.base.getPhoneInfo";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.5.0"},o[i.ENV_ENUM.android]={vs:"3.5.0"},o)),t.getPhoneInfo$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.base.getUUID";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.getUUID$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.base.getWifiStatus";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.11.0"},o[i.ENV_ENUM.android]={vs:"2.11.0"},o)),t.getWifiStatus$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.connection.getNetworkType";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.getNetworkType$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.geolocation.get";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.get$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.geolocation.start";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.4.7"},o[i.ENV_ENUM.android]={vs:"3.4.7"},o)),t.start$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.geolocation.status";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.4.8"},o[i.ENV_ENUM.android]={vs:"3.4.8"},o)),t.status$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.geolocation.stop";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"3.4.7"},o[i.ENV_ENUM.android]={vs:"3.4.7"},o)),t.stop$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.launcher.checkInstalledApps";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.checkInstalledApps$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.launcher.launchApp";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.launchApp$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.nfc.nfcRead";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.11.0"},o[i.ENV_ENUM.android]={vs:"2.11.0"},o)),t.nfcRead$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.nfc.nfcStop";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"4.3.9"},o[i.ENV_ENUM.android]={vs:"4.3.9"},o)),t.nfcStop$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.nfc.nfcWrite";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.11.0"},o[i.ENV_ENUM.android]={vs:"2.11.0"},o)),t.nfcWrite$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.notification.actionSheet";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.actionSheet$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="device.notification.alert",d=a.genDefaultParamsDealFn({title:"",buttonName:"确定"});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.alert$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="device.notification.confirm",d=a.genDefaultParamsDealFn({title:"",buttonLabels:["确定","取消"]});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.confirm$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.notification.extendModal";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.5.0"},o[i.ENV_ENUM.android]={vs:"2.5.0"},o)),t.extendModal$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.notification.hidePreloader";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.hidePreloader$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.notification.modal";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"4.2.5"},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.modal$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="device.notification.prompt",d=a.genDefaultParamsDealFn({title:"",buttonLabels:["确定","取消"]});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.7.0"},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.prompt$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="device.notification.showPreloader",d=a.genDefaultParamsDealFn({text:"加载中...",showIcon:!0});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.showPreloader$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="device.notification.toast",d=a.genDefaultParamsDealFn({text:"toast",duration:3,delay:0});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.pc]={vs:"2.5.0",paramsDeal:function(e){return e.icon&&!e.type&&("success"===e.icon?e.type="success":"error"===e.icon&&(e.type="error")),e}},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.toast$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="device.notification.vibrate",d=a.genDefaultParamsDealFn({duration:300});i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:d},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:d},o)),t.vibrate$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.screen.resetView";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.android]={vs:"4.0.0"},o[i.ENV_ENUM.ios]={vs:"4.0.0"},o)),t.resetView$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="device.screen.rotateView";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.android]={vs:"4.0.0"},o[i.ENV_ENUM.ios]={vs:"4.0.0"},o)),t.rotateView$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="runtime.message.fetch";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.fetch$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="runtime.message.post";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.post$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="runtime.permission.requestAuthCode",s=function(e){return Object.assign(e,{url:location.href.split("#")[0]})};i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.0.0",paramsDeal:s},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.requestAuthCode$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="runtime.permission.requestOperateAuthCode",s=function(e){return Object.assign(e,{url:location.href.split("#")[0]})};i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.pc]={vs:"3.3.0",paramsDeal:s},o[i.ENV_ENUM.ios]={vs:"3.3.0"},o[i.ENV_ENUM.android]={vs:"3.3.0"},o)),t.requestOperateAuthCode$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.input.plain";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.plain$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.nav.close";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.close$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.nav.getCurrentId";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.getCurrentId$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.nav.go";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.go$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.nav.preload";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.preload$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.nav.recycle";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.6.0"},o[i.ENV_ENUM.android]={vs:"2.6.0"},o)),t.recycle$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.progressBar.setColors";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.setColors$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.pullToRefresh.disable";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.disable$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(s,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a=n(1),s="ui.pullToRefresh.enable";i.ddSdk.setAPI(s,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0",paramsDeal:a.addWatchParamsDeal},o[i.ENV_ENUM.android]={vs:"2.4.0",paramsDeal:a.addWatchParamsDeal},o)),t.enable$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.pullToRefresh.stop";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.stop$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.webViewBounce.disable";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.disable$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="ui.webViewBounce.enable";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.4.0"},o[i.ENV_ENUM.android]={vs:"2.4.0"},o)),t.enable$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="util.domainStorage.getItem";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.9.0"},o[i.ENV_ENUM.android]={vs:"2.9.0"},o)),t.getItem$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="util.domainStorage.removeItem";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.9.0"},o[i.ENV_ENUM.android]={vs:"2.9.0"},o)),t.removeItem$=r,t.default=r},function(e,t,n){"use strict";function r(e){return i.ddSdk.invokeAPI(a,e)}Object.defineProperty(t,"__esModule",{value:!0});var o,i=n(0),a="util.domainStorage.setItem";i.ddSdk.setAPI(a,(o={},o[i.ENV_ENUM.ios]={vs:"2.9.0"},o[i.ENV_ENUM.android]={vs:"2.9.0"},o)),t.setItem$=r,t.default=r},function(e,t,n){"use strict";var r=n(0),o=n(144),i=Object.assign({},o,r.ddSdk.getExportSdk());e.exports=i},function(e,t,n){"use strict";var r=n(141);n(151),e.exports=r},function(e,t,n){"use strict";function r(e){return e="00"+e,e.substring(e.length-2,e.length)}Object.defineProperty(t,"__esModule",{value:!0}),t.log=function(e){console.log.apply(console,[r(e.time.getHours())+":"+r(e.time.getMinutes())+":"+r(e.time.getSeconds())].concat(e.text))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(2),o=r.getENV();t.ios=o.platform===r.ENV_ENUM.ios,t.android=o.platform===r.ENV_ENUM.android,t.pc=o.platform===r.ENV_ENUM.pc,t.other=o.platform===r.ENV_ENUM.notInDingTalk,t.compareVersion=function(e,t,n){function r(e){return parseInt(e,10)||0}if("string"!=typeof e||"string"!=typeof t)return!1;for(var o,i,a=e.split("-")[0].split(".").map(r),s=t.split("-")[0].split(".").map(r);o===i&&s.length>0;)o=a.shift(),i=s.shift();return n?(i||0)>=(o||0):(i||0)>(o||0)},t.language=o.language,t.version=o.version},function(e,t,n){"function"!=typeof Promise&&n(155)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(145),n(147),n(148)},function(e,t){"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e,t){"use strict";if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(e),r=1;r<arguments.length;r++){var o=arguments[r];if(null!=o)for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n},writable:!0,configurable:!0})},function(e,t){Object.keys||(Object.keys=function(e){if(e!==Object(e))throw new TypeError("Object.keys called on a non-object");var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.push(t);return n})},function(e,t,n){"use strict";function r(e){return"function"==typeof e}function o(e,t){function n(e){return parseInt(e,10)||0}for(var r=e.split(".").map(n),o=t.split(".").map(n),i=0;i<r.length;i++){if(void 0===o[i])return!1;if(r[i]<o[i])return!1;if(r[i]>o[i])return!0}return!0}Object.defineProperty(t,"__esModule",{value:!0}),t.isFunction=r,t.compareVersion=o;(function(e){e.cancel="-1",e.not_exist="1",e.no_permission="7",e.jsapi_internal_error="22"})(t.ERROR_CODE||(t.ERROR_CODE={}));(function(e){e.pc="pc",e.android="android",e.ios="ios",e.notInDingTalk="notInDingTalk"})(t.ENV_ENUM||(t.ENV_ENUM={}));(function(e){e.WEB="WEB",e.MINI_APP="MINI_APP"})(t.APP_TYPE||(t.APP_TYPE={}));(function(e){e[e.INFO=1]="INFO",e[e.WARNING=2]="WARNING",e[e.ERROR=3]="ERROR"})(t.LogLevel||(t.LogLevel={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),o=n(2),i=n(3);r.ddSdk.setPlatform({platform:o.ENV_ENUM.android,bridgeInit:function(){if(o.getENV().appType===i.APP_TYPE.MINI_APP)return Promise.resolve(function(e,t){return new Promise(function(n,r){my.dtBridge({m:e,args:t,onSuccess:function(e){"function"==typeof t.onSuccess&&t.onSuccess(e),n(e)},onFail:function(e){"function"==typeof t.onFail&&t.onFail(e),r(e)}})})});var e=function(e,t){return new Promise(function(n,r){var o=e.split("."),i=o.pop()||"",a=o.join("."),s=function(e){"function"==typeof t.onSuccess&&t.onSuccess(e),n(e)},d=function(e){"function"==typeof t.onFail&&t.onFail(e),r(e)};"function"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid(s,d,a,i,t)})};return new Promise(function(t,n){var r=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),t(e)}catch(e){n(e)}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?r():document.addEventListener("runtimeready",function(){r()},!1)})},authMethod:"runtime.permission.requestJsApis",event:{on:function(e,t){document.addEventListener(e,t)},off:function(e,t){document.removeEventListener(e,t)}}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(153),n(150),n(152)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),o=n(2),i=n(3);r.ddSdk.setPlatform({platform:o.ENV_ENUM.ios,bridgeInit:function(){if(o.getENV().appType===i.APP_TYPE.MINI_APP)return Promise.resolve(function(e,t){return new Promise(function(n,r){my.dtBridge({m:e,args:t,onSuccess:function(e){"function"==typeof t.onSuccess&&t.onSuccess(e),n(e)},onFail:function(e){"function"==typeof t.onFail&&t.onFail(e),r(e)}})})});var e=function(e,t){var n=Object.assign({},t);return new Promise(function(t,r){if(!0===n.watch){var o=n.onSuccess;delete n.onSuccess,"undefined"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler(e,function(e,t){"function"==typeof o&&o.call(null,e),t&&t({errorCode:"0",errorMessage:"success"})})}void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler(e,Object.assign({},n),function(e){var o=e||{};"0"===o.errorCode?("function"==typeof n.onSuccess&&n.onSuccess.call(null,o.result),t(o.result)):("-1"===o.errorCode?"function"==typeof n.onCancel&&n.onCancel.call(null,o.result,o.errorCode):"function"==typeof n.onFail&&n.onFail.call(null,o.result,o.errorCode),r(o.result))})})};return new Promise(function(t,n){if("undefined"!=typeof WebViewJavascriptBridge){try{WebViewJavascriptBridge.init(function(e,t){})}catch(e){return n()}return t(e)}document.addEventListener("WebViewJavascriptBridgeReady",function(){if("undefined"==typeof WebViewJavascriptBridge)return n();try{WebViewJavascriptBridge.init(function(e,t){})}catch(e){return n()}return t(e)},!1)})},authMethod:"runtime.permission.requestJsApis",event:{on:function(e,t){document.addEventListener(e,t)},off:function(e,t){document.removeEventListener(e,t)}}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),o=n(2);r.ddSdk.setPlatform({platform:o.ENV_ENUM.pc,bridgeInit:function(){var e=n(5);return Promise.resolve(function(t,n){return e.invokeAPI(t,n).result.then(function(e){return"function"==typeof n.onSuccess&&n.onSuccess.call(null,e),Promise.resolve(e)},function(e){return"function"==typeof n.onFail&&n.onFail.call(null,e),Promise.reject(e)})})},authMethod:"config",authParamsDeal:function(e){var t=Object.assign({},e);return t.url=window.location.href.split("#")[0],t},event:{on:function(e,t){n(5).addEventListener(e,t)},off:function(e,t){n(5).removeEventListener(e,t)}}})},function(e,t){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function o(e){if(c===setTimeout)return setTimeout(e,0);if((c===n||!c)&&setTimeout)return c=setTimeout,setTimeout(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}function i(e){if(l===clearTimeout)return clearTimeout(e);if((l===r||!l)&&clearTimeout)return l=clearTimeout,clearTimeout(e);try{return l(e)}catch(t){try{return l.call(null,e)}catch(t){return l.call(this,e)}}}function a(){_&&v&&(_=!1,v.length?p=v.concat(p):E=-1,p.length&&s())}function s(){if(!_){var e=o(a);_=!0;for(var t=p.length;t;){for(v=p,p=[];++E<t;)v&&v[E].run();E=-1,t=p.length}v=null,_=!1,i(e)}}function d(e,t){this.fun=e,this.array=t}function u(){}var c,l,f=e.exports={};(function(){try{c="function"==typeof setTimeout?setTimeout:n}catch(e){c=n}try{l="function"==typeof clearTimeout?clearTimeout:r}catch(e){l=r}})();var v,p=[],_=!1,E=-1;f.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];p.push(new d(e,t)),1!==p.length||_||o(s)},d.prototype.run=function(){this.fun.apply(null,this.array)},f.title="browser",f.browser=!0,f.env={},f.argv=[],f.version="",f.versions={},f.on=u,f.addListener=u,f.once=u,f.off=u,f.removeListener=u,f.removeAllListeners=u,f.emit=u,f.prependListener=u,f.prependOnceListener=u,f.listeners=function(e){return[]},f.binding=function(e){throw new Error("process.binding is not supported")},f.cwd=function(){return"/"},f.chdir=function(e){throw new Error("process.chdir is not supported")},f.umask=function(){return 0}},function(e,t,n){(function(e,t){(function(e,t){t()})(0,function(){"use strict";function n(){}function r(e,t){return function(){e.apply(t,arguments)}}function o(e){if(!(this instanceof o))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],c(e,this)}function i(e,t){for(;3===e._state;)e=e._value;if(0===e._state)return void e._deferreds.push(t);e._handled=!0,o._immediateFn(function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null===n)return void(1===e._state?a:s)(t.promise,e._value);var r;try{r=n(e._value)}catch(e){return void s(t.promise,e)}a(t.promise,r)})}function a(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof o)return e._state=3,e._value=t,void d(e);if("function"==typeof n)return void c(r(n,t),e)}e._state=1,e._value=t,d(e)}catch(t){s(e,t)}}function s(e,t){e._state=2,e._value=t,d(e)}function d(e){2===e._state&&0===e._deferreds.length&&o._immediateFn(function(){e._handled||o._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)i(e,e._deferreds[t]);e._deferreds=null}function u(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function c(e,t){var n=!1;try{e(function(e){n||(n=!0,a(t,e))},function(e){n||(n=!0,s(t,e))})}catch(e){if(n)return;n=!0,s(t,e)}}var l=setTimeout;o.prototype.catch=function(e){return this.then(null,e)},o.prototype.then=function(e,t){var r=new this.constructor(n);return i(this,new u(e,t,r)),r},o.prototype.finally=function(e){var t=this.constructor;return this.then(function(n){return t.resolve(e()).then(function(){return n})},function(n){return t.resolve(e()).then(function(){return t.reject(n)})})},o.all=function(e){return new o(function(t,n){function r(e,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var s=a.then;if("function"==typeof s)return void s.call(a,function(t){r(e,t)},n)}o[e]=a,0==--i&&t(o)}catch(e){n(e)}}if(!e||void 0===e.length)throw new TypeError("Promise.all accepts an array");var o=Array.prototype.slice.call(e);if(0===o.length)return t([]);for(var i=o.length,a=0;a<o.length;a++)r(a,o[a])})},o.resolve=function(e){return e&&"object"==typeof e&&e.constructor===o?e:new o(function(t){t(e)})},o.reject=function(e){return new o(function(t,n){n(e)})},o.race=function(e){return new o(function(t,n){for(var r=0,o=e.length;r<o;r++)e[r].then(t,n)})},o._immediateFn="function"==typeof e&&function(t){e(t)}||function(e){l(e,0)},o._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)};var f=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==t)return t;throw new Error("unable to locate global object")}();f.Promise||(f.Promise=o)})}).call(t,n(157).setImmediate,n(4))},function(e,t,n){(function(e,t){(function(e,n){"use strict";function r(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var r={callback:e,args:t};return u[d]=r,s(d),d++}function o(e){delete u[e]}function i(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(n,r)}}function a(e){if(c)setTimeout(a,0,e);else{var t=u[e];if(t){c=!0;try{i(t)}finally{o(e),c=!1}}}}if(!e.setImmediate){var s,d=1,u={},c=!1,l=e.document,f=Object.getPrototypeOf&&Object.getPrototypeOf(e);f=f&&f.setTimeout?f:e,"[object process]"==={}.toString.call(e.process)?function(){s=function(e){t.nextTick(function(){a(e)})}}():!function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?e.MessageChannel?function(){var e=new MessageChannel;e.port1.onmessage=function(e){a(e.data)},s=function(t){e.port2.postMessage(t)}}():l&&"onreadystatechange"in l.createElement("script")?function(){var e=l.documentElement;s=function(t){var n=l.createElement("script");n.onreadystatechange=function(){a(t),n.onreadystatechange=null,e.removeChild(n),n=null},e.appendChild(n)}}():function(){s=function(e){setTimeout(a,0,e)}}():function(){var t="setImmediate$"+Math.random()+"$",n=function(n){n.source===e&&"string"==typeof n.data&&0===n.data.indexOf(t)&&a(+n.data.slice(t.length))};e.addEventListener?e.addEventListener("message",n,!1):e.attachEvent("onmessage",n),s=function(n){e.postMessage(t+n,"*")}}(),f.setImmediate=r,f.clearImmediate=o}})("undefined"==typeof self?void 0===e?this:e:self)}).call(t,n(4),n(154))},function(e,t,n){(function(e){function r(e,t){this._id=e,this._clearFn=t}var o=void 0!==e&&e||"undefined"!=typeof self&&self||window,i=Function.prototype.apply;t.setTimeout=function(){return new r(i.call(setTimeout,o,arguments),clearTimeout)},t.setInterval=function(){return new r(i.call(setInterval,o,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},r.prototype.unref=r.prototype.ref=function(){},r.prototype.close=function(){this._clearFn.call(o,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},n(156),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(t,n(4))},,function(e,t,n){"use strict";var r=n(142),o=n(437),i=Object.assign(r,o.apiObj);e.exports=i},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(6),o=n(7),i=n(8),a=n(9),s=n(10),d=n(11),u=n(12),c=n(13),l=n(14),f=n(15),v=n(16),p=n(17),_=n(18),E=n(19),N=n(20),P=n(21),h=n(22),m=n(23),M=n(24),k=n(25),y=n(26),b=n(27),g=n(28),I=n(29),A=n(30),S=n(31),V=n(32),U=n(33),j=n(34),O=n(35),$=n(36),w=n(37),D=n(38),C=n(39),F=n(40),L=n(41),x=n(42),T=n(43),z=n(44),R=n(45),B=n(46),W=n(47),q=n(48),J=n(49),H=n(50),G=n(51),Y=n(52),Q=n(53),K=n(54),Z=n(55),X=n(56),ee=n(57),te=n(58),ne=n(59),re=n(60),oe=n(61),ie=n(62),ae=n(63),se=n(64),de=n(65),ue=n(66),ce=n(67),le=n(68),fe=n(69),ve=n(70),pe=n(71),_e=n(72),Ee=n(73),Ne=n(74),Pe=n(75),he=n(76),me=n(77),Me=n(78),ke=n(79),ye=n(80),be=n(81),ge=n(82),Ie=n(83),Ae=n(84),Se=n(85),Ve=n(86),Ue=n(87),je=n(88),Oe=n(89),$e=n(90),we=n(91),De=n(92),Ce=n(93),Fe=n(94),Le=n(95),xe=n(96),Te=n(97),ze=n(98),Re=n(99),Be=n(100),We=n(101),qe=n(102),Je=n(103),He=n(104),Ge=n(105),Ye=n(106),Qe=n(107),Ke=n(108),Ze=n(109),Xe=n(110),et=n(111),tt=n(112),nt=n(113),rt=n(114),ot=n(115),it=n(116),at=n(117),st=n(118),dt=n(119),ut=n(120),ct=n(121),lt=n(122),ft=n(123),vt=n(124),pt=n(125),_t=n(126),Et=n(127),Nt=n(128),Pt=n(129),ht=n(130),mt=n(131),Mt=n(132),kt=n(133),yt=n(134),bt=n(135),gt=n(136),It=n(137),At=n(138),St=n(139),Vt=n(140);t.apiObj={biz:{alipay:{pay:r.pay$},calendar:{chooseDateTime:o.chooseDateTime$,chooseHalfDay:i.chooseHalfDay$,chooseInterval:a.chooseInterval$,chooseOneDay:s.chooseOneDay$},chat:{chooseConversationByCorpId:d.chooseConversationByCorpId$,locationChatMessage:u.locationChatMessage$,openSingleChat:c.openSingleChat$,pickConversation:l.pickConversation$,toConversation:f.toConversation$},clipboardData:{setData:v.setData$},contact:{choose:p.choose$,chooseMobileContacts:_.chooseMobileContacts$,complexPicker:E.complexPicker$,createGroup:N.createGroup$,departmentsPicker:P.departmentsPicker$,externalComplexPicker:h.externalComplexPicker$,externalEditForm:m.externalEditForm$,setRule:M.setRule$},cspace:{chooseSpaceDir:k.chooseSpaceDir$,preview:y.preview$,saveFile:b.saveFile$},customContact:{choose:g.choose$,multipleChoose:I.multipleChoose$},ding:{create:A.create$,post:S.post$},event:{notifyWeex:V.notifyWeex$},intent:{fetchData:U.fetchData$},map:{locate:j.locate$,search:O.search$,view:$.view$},microApp:{openApp:w.openApp$},navigation:{close:D.close$,goBack:C.goBack$,hideBar:F.hideBar$,quit:L.quit$,replace:x.replace$,setIcon:T.setIcon$,setLeft:z.setLeft$,setMenu:R.setMenu$,setRight:B.setRight$,setTitle:W.setTitle$},store:{closeUnpayOrder:q.closeUnpayOrder$,createOrder:J.createOrder$,getPayUrl:H.getPayUrl$,inquiry:G.inquiry$},telephone:{call:Y.call$,checkBizCall:Q.checkBizCall$,quickCallList:K.quickCallList$,showCallMenu:Z.showCallMenu$},user:{checkPassword:X.checkPassword$,get:ee.get$},util:{chosen:te.chosen$,datepicker:ne.datepicker$,datetimepicker:re.datetimepicker$,decrypt:oe.decrypt$,downloadFile:ie.downloadFile$,encrypt:ae.encrypt$,isLocalFileExist:se.isLocalFileExist$,multiSelect:de.multiSelect$,open:ue.open$,openLink:ce.openLink$,openLocalFile:le.openLocalFile$,openModal:fe.openModal$,openSlidePanel:ve.openSlidePanel$,previewImage:pe.previewImage$,previewVideo:_e.previewVideo$,scan:Ee.scan$,scanCard:Ne.scanCard$,setScreenBrightnessAndKeepOn:Pe.setScreenBrightnessAndKeepOn$,share:he.share$,systemShare:me.systemShare$,timepicker:Me.timepicker$,uploadAttachment:ke.uploadAttachment$,uploadImage:ye.uploadImage$,uploadImageFromCamera:be.uploadImageFromCamera$,ut:ge.ut$}},channel:{permission:{requestAuthCode:Ie.requestAuthCode$}},device:{accelerometer:{clearShake:Ae.clearShake$,watchShake:Se.watchShake$},audio:{download:Ve.download$,onPlayEnd:Ue.onPlayEnd$,onRecordEnd:je.onRecordEnd$,pause:Oe.pause$,play:$e.play$,resume:we.resume$,startRecord:De.startRecord$,stop:Ce.stop$,stopRecord:Fe.stopRecord$,translateVoice:Le.translateVoice$},base:{getInterface:xe.getInterface$,getPhoneInfo:Te.getPhoneInfo$,getUUID:ze.getUUID$,getWifiStatus:Re.getWifiStatus$},connection:{getNetworkType:Be.getNetworkType$},geolocation:{get:We.get$,start:qe.start$,status:Je.status$,stop:He.stop$},launcher:{checkInstalledApps:Ge.checkInstalledApps$,launchApp:Ye.launchApp$},nfc:{nfcRead:Qe.nfcRead$,nfcStop:Ke.nfcStop$,nfcWrite:Ze.nfcWrite$},notification:{actionSheet:Xe.actionSheet$,alert:et.alert$,confirm:tt.confirm$,extendModal:nt.extendModal$,hidePreloader:rt.hidePreloader$,modal:ot.modal$,prompt:it.prompt$,showPreloader:at.showPreloader$,toast:st.toast$,vibrate:dt.vibrate$},screen:{resetView:ut.resetView$,rotateView:ct.rotateView$}},runtime:{message:{fetch:lt.fetch$,post:ft.post$},permission:{requestAuthCode:vt.requestAuthCode$,requestOperateAuthCode:pt.requestOperateAuthCode$}},ui:{input:{plain:_t.plain$},nav:{close:Et.close$,getCurrentId:Nt.getCurrentId$,go:Pt.go$,preload:ht.preload$,recycle:mt.recycle$},progressBar:{setColors:Mt.setColors$},pullToRefresh:{disable:kt.disable$,enable:yt.enable$,stop:bt.stop$},webViewBounce:{disable:gt.disable$,enable:It.enable$}},util:{domainStorage:{getItem:At.getItem$,removeItem:St.removeItem$,setItem:Vt.setItem$}}}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){e.exports=n(159)}])});