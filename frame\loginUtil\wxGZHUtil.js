var wxGZHUtil = {
	queryTimer : false,//定时器
	curQueryNum : 0,//异步查询当前次数
	maxQueryCount : 40,//异步查询最大次数，每秒请求一次 
	wSocket : null,//webSocket对象
	callback : null,
	compNo : null,
	userCode : null,
	init : function(data, id, callback) {
		wxGZHUtil.getQRCode(data, id, callback);
	},
	getQRCode : function(data, id, callback) {
		wxGZHUtil.callback = callback;
		if (wxGZHUtil.queryTimer) {
			//刷新二维码时，清掉timer
			clearInterval(wxGZHUtil.queryTimer);
		}
		wxGZHUtil.compNo = data.compNo;
		wxGZHUtil.userCode = (window["scanQRCodeBind"] ? window.scanQRCodeBind.userCode : "");
		return $.ajax({
			type : "get",
			url : basePath + "frame/excludeUrl/scanCode/getQRCode.spring",
			dataType : "json",
			skipDataCheck : true,
			data : {
				"appId" : data.appId,
				"appsecret" : data.appsecret,
				"compNo" : wxGZHUtil.compNo,
				"userCode" : wxGZHUtil.userCode
			}
		}).then(function(data) {
			if (data.result == "success") {
				$("#" + id).attr("src", "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + data.ticket);
				//创建二维码成功后，建立WebSocket连接，用户等待扫码
				var wsUrl = ws_basePath + "/frame/excludeUrl/webSocket/" + data.sceneId;
				wxGZHUtil.createWebSocket(wsUrl, data.sceneId);
			} else {
				assemblys.alert(data.result);
			}
			return data;
		})
	},
	/**
	 * 创建WebSocket
	 * wsUrl：WebSocket后台地址，sceneId：场景值ID
	 */
	createWebSocket : function(wsUrl, sceneId) {

		var curQueryCallback = function(){
			wxGZHUtil.curQueryNum = 0;
			//改为一点五秒 by 2022.07.08
			wxGZHUtil.queryTimer = setInterval(function() {
				wxGZHUtil.getScanResult(sceneId, wxGZHUtil.maxQueryCount)
			}, 1500);
		}
		
		if ("WebSocket" in window) {
			try {
				wxGZHUtil.wSocket = new WebSocket(wsUrl);
				wxGZHUtil.wSocket.onopen = function() {
					//连接建立事件
				};
				wxGZHUtil.wSocket.onerror = function() {
					curQueryCallback();
				}
				wxGZHUtil.wSocket.onmessage = function(evt) {
					//客户端接收消息的回调事件
					var received_msg = evt.data;
					if (received_msg) {
						var msg = $.parseJSON(received_msg);
						if (wxGZHUtil.callback) {
							//扫码登录，跳转后台登录
							wxGZHUtil.callback(msg);
						}else{
							assemblys.alert("扫码成功");
						}
					}
				};
				wxGZHUtil.wSocket.onclose = function() {
					//连接关闭事件
				};
			} catch (err) {
				//则使用ajax轮询方式，等待1分钟
				curQueryCallback();
			}
		} else {//浏览器不支持WebSocket，则使用ajax轮询方式，等待1分钟
			curQueryCallback();
		}
	},
	/**
	 * 通过ajax每1秒访问服务端，来获取扫码结果
	 * @returns
	 */
	getScanResult : function(sceneId, executeNum) {
		if (wxGZHUtil.curQueryNum > executeNum && wxGZHUtil.queryTimer) {
			clearInterval(wxGZHUtil.queryTimer);
		}
		try {
			var url = basePath + "frame/excludeUrl/scanCode/getScanResult.spring";
			return $.ajax({
				type : "get",
				url : url,
				dataType : "json",
				skipDataCheck : true,
				data : {
					sceneId : sceneId,
					compNo : wxGZHUtil.compNo
				}
			}).then(function(data) {
				if (data.result == "no_user") {
					wxGZHUtil.curQueryNum++;
				} else if (data.result == "success") {
					if (wxGZHUtil.callback) {
						wxGZHUtil.callback(data);
					} else {
						if (wxGZHUtil.queryTimer) {
							clearInterval(wxGZHUtil.queryTimer);
						}
						assemblys.alert("扫码成功");
					}
				} else {
					if (wxGZHUtil.queryTimer) {
						clearInterval(wxGZHUtil.queryTimer);
					}
					assemblys.alert(data.result);
				}
				return data;
			});
		} catch (err) {
			if (wxGZHUtil.queryTimer) {
				clearInterval(wxGZHUtil.queryTimer);
			}
		}
	},
	//关闭WebSocket连接
	closeWebSocket : function() {
		if (wxGZHUtil.wSocket) {
			wxGZHUtil.wSocket.close();
		}
	}
}
/**
 * 离开页面时关闭WebSocket连接
 */
window.onbeforeunload = function() {
	wxGZHUtil.closeWebSocket();
	if (wxGZHUtil.queryTimer) {
		clearInterval(wxGZHUtil.queryTimer);
	}
}