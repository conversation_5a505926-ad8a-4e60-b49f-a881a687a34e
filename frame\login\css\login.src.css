html, body {
	width: 100%;
	height: 100%;
	min-width: 1024px;
	min-height: 660px;
}

body {
	background: url('../images/login_bg.png') no-repeat;
	background-size: 100% 100%;
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(
    src='../images/logins_bg.png', sizingMethod='scale') \0;
	background-position: center center;
	margin: 0;
	position: relative;
}

.bg {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1000;
}

.main {
	text-align: center;
	display: inline-block;
	width: 99%;
	vertical-align: middle;
}

.login_box {
	position: absolute;
	top: 6px;
	border-radius: 10px;
	padding: 30px 15px;
	width: 544px;
	height: 300px;
	background: #357cd5;
	background: rgba(87, 119, 174, 0.9);
	background: -webkit-linear-gradient(rgba(70, 147, 227, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(70, 147, 227, 0.8));
	background: -o-linear-gradient(rgba(70, 147, 227, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(70, 147, 227, 0.8));
	background: -moz-linear-gradient(rgba(70, 147, 227, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(70, 147, 227, 0.8));
	background: linear-gradient(rgba(70, 147, 227, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(56, 130, 222, 0.8), rgba(70, 147, 227, 0.8));
	border: 2px solid #357cd5;
	border-image: -webkitlinear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, #affeff 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
	border-image: -o-linear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, #affeff 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
	border-image: -moz-linear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, #affeff 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
	border-image: linear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, #affeff 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
}

.login_box_cotent {
	margin: 2% auto 2%;
	border-radius: 10px;
	position: relative;
	width: 578px;
}

.login_ps {
	font-size: 23px;
	font-weight: 700;
	color: #000;
	text-align: left;
	margin-bottom: 20px;
}

.login_left {
	position: relative;
	width: 30%;
	text-align: center;
	display: inline-block;
	height: 100%;
}

.input_box {
	position: relative;
	width: 100%;
	height: 40px;
	margin-bottom: 19px;
	border-radius: 3px;
}

.i_user_psd {
	width: 16px;
	height: 18px;
	position: absolute;
	left: 12px;
	top: 10px;
	padding-right: 10px;
	z-index: 9999;
}

.input, .password {
	border: none;
	padding-left: 40px;
	width: 248px;
	height: 40px;
	color: #666666;
	font-size: 16px;
	padding: 0 0 0 40px;
}

.topPw {
	position: absolute;
	background: transparent;
	width: 85%;
}

.btn_login {
	width: 100px;
	height: 40px;
	line-height: 40px;
	font-size: 20px;
	background: #499ef0;
	background: -webkit-linear-gradient(#6df0ff, #3c97df, #86c7ff);
	background: -o-linear-gradient(#6df0ff, #3c97df, #86c7ff);
	background: -moz-linear-gradient(#6df0ff, #3c97df, #86c7ff);
	background: linear-gradient(#6df0ff, #3c97df, #86c7ff);
	font-family: SimHei;
}

.sao {
	position: absolute;
	right: 14.5%;
	top: 0;
	width: 115px;
	height: 30px;
	background: url(../images/sao1.png) no-repeat;
	cursor: pointer;
}

.sao:hover {
	background: url(../images/sao2.png) no-repeat;
}

.login_right {
	width: 30%;
	padding-left: 50px;
	display: inline-block;
	vertical-align: top;
	border-left: 1px solid #959fbb;
}

.link, .foot {
	text-align: center;
}

.link {
	margin-top: 70px;
}

.link_text, .link_text a {
	color: #fff;
	font-size: 16px;
}

.link_text a:hover {
	color: #1E9FFF;
}

.foot p {
	margin-top: 20px;
	color: #fff;
	font-size: 16px;
}

.foot p:last-child {
	margin-top: 10px;
	font-size: 12px;
}

.layui-form-checkbox[lay-skin=primary]:hover i {
	border-color: #009688;
	color: #fff;
}

.layui-form-checked[lay-skin=primary] i {
	border-color: #009688;
	background-color: #009688;
	color: #fff;
}

.layui-form-checkbox[lay-skin=primary] i {
	font-size: 16px;
}

.login_right {
	width: 65%;
	height: 100%;
	box-sizing: border-box;
}

.login_left .login_img {
	text-align: center;
	position: relative;
	margin: 0 auto;
}

.layui-tab {
	padding: 0px;
	position: relative;
	margin: 0 auto;
}

.layui-tab-content {
	padding: 0px;
}

.head2_tab {
	border: none;
	height: 28px;
	margin-bottom: 15px;
	text-align: center;
	width: 100%;
}

.head2_tab li {
	height: 28px;
	line-height: 28px;
	width: auto;
	font-size: 18px;
	padding: 0;
	color: #b4b4b4;
}

.layui-tab-title .layui-this {
	display: inline-block;
	padding: 0;
	color: #009688;
	white-space: nowrap;
	text-align: center;
	font-size: 22px;
	border: none;
	border-radius: 2px;
	cursor: pointer;
	opacity: .9;
	font-weight: bold;
	font-family: SimHei;
}

.layui-tab-title .layui-this:after {
	border: none;
}

.v_code_box {
	display: inline-block;
	width: 50%;
}

img.v_code {
	width: 98px;
	height: 40px;
	vertical-align: top;
	border: 1px solid #d0d0d0;
	border-radius: 3px;
}

.QR_code {
	margin: 5px 12px;
	display: inline-block;
}

@media screen and (min-width:1360px) {
	.QR_left {
		margin: 15px 0 0 20px;
	}
	.QR_right {
		float: right;
		margin: 15px 20px 0 0;
	}
}

.code1 {
	background: url(../images/dingding41.png) no-repeat center center;
	background-size: 100%;
}

.code2 {
	background: url(../images/qywx41.png) no-repeat center center;
	background-size: 100%;
}

.code3 {
	background: url(../images/gzh41.png) no-repeat center center;
	background-size: 100%;
}

.code1_enable {
	background: url(../images/dingding4.png) no-repeat center center;
	background-size: 100%;
}

.code2_enable {
	background: url(../images/qywx4.png) no-repeat center center;
	background-size: 100%;
}

.code3_enable {
	background: url(../images/gzh4.png) no-repeat center center;
	background-size: 100%;
}

.code1, .code2, .code3 {
	width: 50px;
	height: 50px;
	cursor: pointer;
}

.QR_code_content {
	width: 250px;
}

.head2_tab li {
	margin-right: 25px;
	float: left;
}

.ruler {
	display: inline-block;
	width: 0;
	height: 100%;
	vertical-align: middle;
}

.QR_window {
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 9999;
	display: none;
}

.QR_window_show {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	opacity: 0.3;
	filter: alpha(opacity = 30);
	background-color: #000;
	zoom: 1;
	display: none;
}

.QR_delete {
	font-size: 58px;
	color: #ffffff;
	position: absolute;
	margin-left: 213px;
	margin-bottom: 215px;
	left: 50%;
	bottom: 50%;
	cursor: pointer;
}

.QR_help {
	color: #fff;
	font-size: 15px;
	cursor: pointer;
	line-height: 18px;
}

#QRImageGZHDiv {
	position: fixed;
	inset: 0px;
	margin: auto;
	width: 300px;
	font-size: 50px;
	background: #fff;
	height: 350px;
	top: 0px;
	bottom: 0px;
	left: 0px;
	right: 0px;
	display: none;
}

#QRImageGZHDiv font {
	text-align: center;
	width: 100%;
	font-size: 13px;
	height: 40px;
	line-height: 40px;
	bottom: 0px;
	position: absolute;
	color: #737C84;
	margin: 0 auto;
}

.login_button_box {
	border: none;
}

.layui-field-custom-title {
	margin: 10px 0 5px;
	margin-top: 12px;
	width: 248px;
}

.layui-field-custom-title legend {
	font-family: SimHei;
	font-size: 14px !important;
	color: #fff;
	margin: 0 auto;
}

html .login_box .layui-tab-title .layui-this {
	color: #fff !important;
}

.layui-form-checkbox[lay-skin="primary"] span {
	color: #fff !important;
	font-family: SimHei;
	padding-right: 10px;
}

.layui-elem-field {
	border-color: #959fbb !important;
}

#link a, #link .link_text, .foot p, .link_text a {
	font-size: 12px;
	font-family: SimHei;
}

.layui-form-checked[lay-skin=primary] i {
	border-color: #235db1 !important;
	background-color: #235db1 !important;
	color: #fff;
}

body {
	display: block !important;
}

.login_bg_left {
	position: absolute;
	left: -17px;
	height: 100%;
	display: none;
	z-index: 9999;
}

.login_bg_right {
	position: absolute;
	right: -17px;
	height: 100%;
	display: none;
	z-index: 9999;
}