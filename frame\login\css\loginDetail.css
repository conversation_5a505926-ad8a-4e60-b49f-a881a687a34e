html{
	background: none!important;
}

html, body {
    width: 100%;
    height: 100%;
    min-width: 1024px;
    min-height: 660px;
}

body{
	background: url(../images/login_bg.png);
    background-size: 100% 100%;
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader( src='../images/logins_bg.png',sizingMethod='scale') \0;
    background-position: center center;
    margin: 0;
    position: relative;
}

.login_title{
}

.login_box {
	padding: 30px 15px;
	height: 300px;
	background: white;
	position: absolute;
	width: 250px;
	top: 78px;
	right: 50px;
}

.login_ps {
	font-size: 23px;
	font-weight: 700;
	color: #000;
	text-align: left;
	margin-bottom: 20px;
}

.login_left {
	position: relative;
	width: 30%;
	text-align: center;
	display: inline-block;
	height: 100%;
}

.input_box {
	position: relative;
	width: 100%;
	height: 30px;
	margin-bottom: 19px;
	border-radius: 5px;
	border: 1px solid #C0C0C0;
	overflow: auto;
}


/* Chrome、Safari等WebKit内核浏览器 */
:-webkit-input-placeholder {
    color: #C0C0C0; /* 设置为红色 */
}
 
/* Firefox浏览器 */
::-moz-placeholder {
    color: #C0C0C0; /* 设置为蓝色 */
}
 
/* IE10及其之后版本 */
:-ms-input-placeholder {
    color: #C0C0C0; /* 设置为绿色 */
}
 
/* 标准语法 */
::placeholder {
    color: #C0C0C0; /* 设置为紫色 */
}

.i_user_psd {
	width: 14px;
	height: 14px;
	position: absolute;
	left: 8px;
	top: 8px;
	padding-right: 10px;
	z-index: 9999;
}

.input, .password {
	border: none;
	width: 248px;
	height: 30px;
	color: #666666;
	font-size: 13px;
	padding: 0 0 0 30px;
	font-family: Microsoft YaHei;
}

.topPw {
	position: absolute;
	background: transparent;
	width: 85%;
}

.btn_login {
	width: 150px;
	height: 30px;
	line-height: 30px;
	font-size: 14px;
	background: #499ef0;
	font-family: Microsoft YaHei;
	border-radius: 5px;
	float: right;
}

.sao {
	position: absolute;
	right: 14.5%;
	top: 0;
	width: 115px;
	height: 30px;
	background: url(../images/sao1.png) no-repeat;
	cursor: pointer;
}

.sao:hover {
	background: url(../images/sao2.png) no-repeat;
}

.link, .foot {
	text-align: center;
}

.link {
	margin-top: 70px;
}

.link_text, .link_text a {
	color: #A0A0A0;
	font-size: 16px;
}

.link_text a:hover {
	color: #1E9FFF;
}

.foot p {
	margin-top: 20px;
	color: #A0A0A0;
	font-size: 16px;
}

.foot p:last-child {
	margin-top: 10px;
	font-size: 12px;
}

.layui-form-checkbox[lay-skin=primary]:hover i {
	border-color: #009688;
	color: #fff;
}

.layui-form-checked[lay-skin=primary] i {
	border-color: #009688;
	background-color: #009688;
	color: #fff;
}

.layui-form-checkbox[lay-skin=primary] i {
	font-size: 10px;
	width: 12px;
	height: 12px;
	line-height: 12px;
}

.login_right {
	width: 100%;
	height: 350PX;
	box-sizing: border-box;
	display: inline-block;
	vertical-align: top;
}

.login_left .login_img {
	text-align: center;
	position: relative;
	margin: 0 auto;
}

.layui-tab {
	padding: 0px;
	position: relative;
	margin: 0 auto;
}

.layui-tab-content {
	padding: 0px;
}

.head2_tab {
	border: none;
	height: 28px;
	margin-bottom: 15px;
	text-align: center;
	width: 100%;
}

.head2_tab li {
	height: 28px;
	line-height: 28px;
	width: auto;
	font-size: 18px;
	padding: 0;
	color: #b4b4b4;
}

.layui-tab-title .layui-this {
	display: inline-block;
	padding: 0;
	color: #000;
	white-space: nowrap;
	text-align: center;
	font-size: 22px;
	border: none;
	border-radius: 2px;
	cursor: pointer;
	opacity: .9;
	font-weight: 500;
	font-family: Microsoft YaHei;
}

.layui-tab-title .layui-this:after {
	border: none;
}

.v_code_box {
	display: inline-block;
	width: 236px;
}

img.v_code {
	width: 98px;
	height: 30px;
	vertical-align: top;
	border: 1px solid #d0d0d0;
	border-radius: 3px;
}

.QR_code {
	margin: 5px 12px;
	display: inline-block;
}

@media screen and (min-width:1360px) {
	.QR_left {
		margin: 15px 0 0 20px;
	}
	.QR_right {
		float: right;
		margin: 15px 20px 0 0;
	}
}

.code1 {
	background: url(../images/dingding41.png) no-repeat center center;
	background-size: 100%;
}

.code2 {
	background: url(../images/qywx41.png) no-repeat center center;
	background-size: 100%;
}

.code3 {
	background: url(../images/gzh41.png) no-repeat center center;
	background-size: 100%;
}

.code1_enable {
	background: url(../images/dingding4.png) no-repeat center center;
	background-size: 100%;
}

.code2_enable {
	background: url(../images/qywx4.png) no-repeat center center;
	background-size: 100%;
}

.code3_enable {
	background: url(../images/gzh4.png) no-repeat center center;
	background-size: 100%;
}

.code1, .code2, .code3 {
	width: 50px;
	height: 50px;
	cursor: pointer;
}

.QR_code_content {
	width: 250px;
}

.head2_tab li {
	margin-right: 25px;
	float: left;
}

.ruler {
	display: inline-block;
	width: 0;
	height: 100%;
	vertical-align: middle;
}

.QR_window {
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 9999;
	display: none;
}

.QR_window_show {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	opacity: 0.3;
	filter: alpha(opacity = 30);
	background-color: #000;
	zoom: 1;
	display: none;
}

.QR_delete {
	font-size: 58px;
	color: #ffffff;
	position: absolute;
	margin-left: 213px;
	margin-bottom: 215px;
	left: 50%;
	bottom: 50%;
	cursor: pointer;
}

.QR_help {
	color: #fff;
	font-size: 15px;
	cursor: pointer;
	line-height: 18px;
}

#QRImageGZHDiv {
	position: fixed;
	inset: 0px;
	margin: auto;
	width: 300px;
	font-size: 50px;
	background: #fff;
	height: 350px;
	top: 0px;
	bottom: 0px;
	left: 0px;
	right: 0px;
	display: none;
}

#QRImageGZHDiv font {
	text-align: center;
	width: 100%;
	font-size: 13px;
	height: 40px;
	line-height: 40px;
	bottom: 0px;
	position: absolute;
	color: #737C84;
	margin: 0 auto;
}

.login_button_box {
	border: none;
	height: 40px;
}

.layui-field-custom-title {
	margin: 10px 0 5px;
	margin-top: 12px;
	width: 248px;
}

.layui-field-custom-title legend {
	font-family: Microsoft YaHei;
	font-size: 14px !important;
	color: #fff;
	margin: 0 auto;
}

.layui-form-checkbox[lay-skin="primary"] span {
	color: #A0A0A0 !important;
	font-family: Microsoft YaHei;
	padding-right: 10px;
	font-size: 12px;
	line-height: 12px;
}

.layui-form-checkbox[lay-skin="primary"]{
	padding-left: 20px;
}

.layui-elem-field {
	border-color: #959fbb !important;
}

#link a, #link .link_text, .foot p, .link_text a {
	font-size: 12px;
	font-family: Microsoft YaHei;
}

.layui-form-checked[lay-skin=primary] i {
	border-color: #235db1 !important;
	background-color: #235db1 !important;
	color: #fff;
}

.login_bg_left {
	position: absolute;
	left: -17px;
	height: 100%;
	display: none;
	z-index: 9999;
}

.login_bg_right {
	position: absolute;
	right: -17px;
	height: 100%;
	display: none;
	z-index: 9999;
}

#scanQRCodeLogin{
	display: none;
}

.nav {
	position: absolute;
	left: 50px;
	top: 78px;
	right: 350px;
	padding-bottom: 50px;
}

.nav-foot{
	position: relative;
	top: 10px;
	color: white;
	font-size: 12px;
}

.title {
	height: 80px;
	background-color: white;
	padding: 10px 30px;
	position: relative;
}

.title .title-img {
	position: absolute;
	height: 100%;
	left: 30px;
	right: 30px;
}

.title img {
	width: 100%;
	height: 80px;
}

.nav-tab {
	background-color: white;
	overflow-x: auto;
    overflow-y: hidden;
    height: 67px;
}

.layui-breadcrumb{
	margin-left: 30px;
	white-space: nowrap;
}

.nav-tab-bottom{
	height: 7px;
    background: rgb(242,242,242);
}

.layui-breadcrumb a{
	padding: 22px;
	font-size: 16px;
	line-height: 60px;
	border-bottom: 7px solid rgb(242,242,242);
}

.layui-breadcrumb a.layui-this{
	background-color: rgb(53,136,222);
	color: #FFF!important;
	border-bottom: 7px solid rgb(7,71,145);
}

.content{
	width: 100%;
	padding: 20px 0px;
	background-color: white;
}

.content-title{
	padding: 10px 40px 0px 40px;
	font-size: 20px;
	font-weight: 700;
}

.content-date{
	padding: 10px 0px;
	margin: 10px 40px;
	font-size: 12px;
	border-bottom: 1px solid;
}

.content-text{
	padding: 20px 0px;
	margin: 10px 40px;
	font-size: 16px;
}

.content-card-row{
	display: flex;
    flex-direction: row;
}

.content-card{
	display: table-cell;
	background-color: white;
	display: inline-block;
	border: 1px solid #E0E0E0;
	padding: 20px;
	margin: 10px;
	flex: 1;
}

.content-card-title{
	font-size: 22px;
    font-weight: 600;
}

.content-card-title a{
	font-size: 14px;
	float: right;
	text-decoration: underline;
	color: rgb(105,158,181);
	line-height: 40px;
}

.content-card-ul{
	margin-top: 15px; 
}

.content-card-ul-li{
	border-bottom: 1px dashed #E0E0E0;
	height: 30px;
    line-height: 30px;
    position: relative;
}

.content-card-ul-li-left{
	position: absolute;
	left: 0px;
	top: 0px;
	bottom: 0px;
	right: 70px;
	overflow: hidden;
}

.content-card-ul-li-right{
	position: absolute;
	top: 0px;
	bottom: 0px;
	right: 0px;
	width: 60px;
	text-align: right;
}

.top-img{
	position: absolute;
	top: 0;
	left: 50px;
	font-size: 10px;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
	overflow: hidden;
}

.top-img-name{
	display: inline-block;
	padding: 10px;
	background-color: black;
	color: white;
	/* transform: skew(20deg); */ 
}

.top-img-content{
	display: inline-block;
	padding: 10px;
	background-color: #F59A23;
	color: black;
	/* transform: skew(20deg);  */
}

.platform-name{
	position: absolute;
	top: 40px;
	left: 50px;
	font-size: 22px;
	color: white;
}

.platform-name-company{
	font-weight: 700;
}

.platform-name-software{
	font-weight: 500;
}

/* .abc {
	position: absolute;
	top: 0;
	left: 50px;
	width: 0px;
	border: 17px solid transparent;
	border-left: 10.2px solid white;
	border-bottom: 17px solid white;
} */