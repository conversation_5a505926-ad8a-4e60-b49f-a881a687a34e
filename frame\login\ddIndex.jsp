<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String port = ":" + request.getServerPort();
	String serverPort = ((port).equals(":80") || (port).equals(":443")) ? "" : port;
	String basePath = request.getScheme() + "://" + request.getServerName() + serverPort + path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<title>授权登录</title>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<!-- 钉钉需要dingtalk.js 获取免登授权码-->
<script src="${basePath}/frame/loginUtil/dingtalk.open.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var corpId = "${param.corpId}";
	var compNo = "${param.compNo}";
	var singleCode = "${param.singleCode}";
	var appCode = "${param.appCode}";
	
	if (!corpId || !singleCode) {
		assemblys.alert("corpId和singleCode不能为空，请检查钉钉首页地址");
	} else {
		dd.ready(function() {
			dd.runtime.permission.requestAuthCode({
				corpId : corpId,
				onSuccess : function(info) {
					login(info.code);
				},
				onFail : function(err) {
					alert(JSON.stringify(err));
				}
			});
		});
	}
	function login(code) {
		var url = basePath + "/frame/excludeUrl/scanCode/mobileLoginByDd.spring";
		$.ajax({
			type : "get",
			url : url,
			data : {
				"code" : code,
				"compNo" : compNo,
				"appCode" : appCode,
				"loginKey" : "ddUserID",
			},
			skipDataCheck : true,
			dataType : "json",
			success : function(data) {
				var userCode = data.userCode;
				if (userCode) {
					url = basePath + "/frame/login/singleMobileLogin.spring";
					url += "?loginKey=ddUserID&compNo=" + compNo;
					url += "&singleCode=" + singleCode;
					url += "&userCode=" + userCode;
					location.href = url;
				} else {
					//没获取到用户userID，在正常情况，应该是移动配置不对导致
					url = basePath + "/errorOne.jsp";
					location.href = url;
				}
			},
			error : function() {
				alert("网路错误，请稍后重试");
			}
		});
	}
</script>
</head>
<body>
	<div>请稍后...</div>
</body>
</html>