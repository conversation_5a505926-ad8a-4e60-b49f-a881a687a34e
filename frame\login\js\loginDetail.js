var loginDetail = {
	init : function() {
		loginDetail.getKnowledge();
	},
	getKnowledge : function() {
		$.ajax({
			url : "http://fortune.clifford.cn:9091/P1936/knowledge/knowledge/getKnowledge.spring",
			type : "get",
			skipDataCheck : true,
			dataType : "json",
			data : {
				knowledgeId : knowledgeId
			},
			success : function(data) {
				if (data.result == "success") {
					data = data.data;
					$("#knowledge_title").text(data.knowledge.title);
					$("#knowledge_content").html(data.knowledge.content);
					$("#knowledge_datetime").text(assemblys.dateToStr(data.knowledge.crtDate));
					$("#knowledge_creater").html(data.knowledge.crtUserName);
				}
			}
		});
	}
}