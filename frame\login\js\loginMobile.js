var hasOpt = false;

function login() {
	
	var userCode = $("#userCode").val();
	if (userCode.length == 0) {
		assemblys.alert("请输入用户编号");
		document.getElementById("userCode").focus();
		return false;
	}
	var ppwwddValue = $("#ppwwddValue").val();
	if (ppwwddValue.length == 0) {
		assemblys.alert("请输入用户密码");
		document.getElementById("ppwwddValue").focus();
		return false;
	}
	
	var paramLoginKey = $("#paramLoginKey").val();
	var paramLoginValue = $("#paramLoginValue").val();
	var paramCompNo = $("#paramCompNo").val();
	var paramSingleCode = $("#paramSingleCode").val();
	
	if (hasOpt) {
		return;
	}
	hasOpt = true;
	var base = new Base64();
	var url = basePath + "/frame/login/bindUser.spring";
	$.ajax({
		type : "post",
		url : url,
		data : {
			"userCode" : userCode,
			"ppwwddValue" : base.encode(ppwwddValue),
			"compNo" : paramCompNo,
			"loginKey" : paramLoginKey,
			"loginValue" : paramLoginValue,
		},
		skipDataCheck : true,
		dataType : "json",
		success : function(data) {
			if (data.result == "success") {
				assemblys.msg("绑定成功", function() {
					// 绑定后，重新发起一次单点登录
					if (paramSingleCode) {
						url = basePath + "/frame/login/singleMobileLogin.spring?1=1";
						url += "&loginKey=" + paramLoginKey;
						url += "&userCode=" + paramLoginValue;
						url += "&singleCode=" + paramSingleCode;
						url += "&compNo=" + paramCompNo;
						window.location.href = url;
					} else {
						// 非单点登录绑定
						window.location.href = basePath + "/frame/login/login.jsp";
					}
				});
			} else {
				hasOpt = false;
				assemblys.alert(JSON.stringify(data.resultMsg));
			}
		},
		error : function() {
			hasOpt = false;
			assemblys.alert("网路错误，请稍后重试");
		}
	});
}