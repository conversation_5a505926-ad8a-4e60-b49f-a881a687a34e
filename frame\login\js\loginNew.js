var login = {
	// 是否显示机构
	hasShowCompNo : false,
	// 是否开启底部链接显示
	hasLinkOpen : false,
	// 是否开启验证码
	hasLoginAuthCodeOpen : false,
	// 是否开启扫码
	hasOpenScanQRCode : false,
	// 是否开启手机号
	hasOpenPhoneLogin : false,
	// 是否开启机构显示
	hasLoginCompNoRule : false,
	// 高度差
	autoHight : 0,
	getAuth : function() {
		return $.ajax({
			url : basePath + "frame/login/checkAuth.spring",
			type : "get",
			dataType : "json",
			success : function(data) {
				if (data.data && data.data.auth == "0") {
					location.href = basePath + "xerror.jsp";
				}
			}
		});
	},
	// 通过字典识别要加载什么套件
	getSafeRule : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictByCode.spring",
			type : "get",
			data : {
				"dictCode" : "loginMode,LoginAuthCode,LoginLinkOpen,LoginCompNoRule",
				"appCode" : "APP"
			},
			skipDataCheck : true,
			dataType : "json",
			success : function(data) {
				// 存在时
				if (data) {
					// 默认用户登录
					var dictList = data.dict;
					for ( var index in dictList) {
						var dict = dictList[index];
						// 如果是登录
						if (dict.dictCode == "loginMode") {
							// 内容值
							var dictContent = dict.dictContent || "1";
							var dictValues = dictContent.split(",")
							// 加载块
							for (var i = 0; i < dictValues.length; i++) {
								var tempValue = dictValues[i];
								if (tempValue == "2") {
									login.hasOpenPhoneLogin = true;
								} else if (tempValue == "3") {
									login.hasOpenScanQRCode = true;
								} 
							}
						} else
						// 底部链接
						if (dict.dictCode == "LoginLinkOpen") {
							login.hasLinkOpen = true;
						} else
						// 验证码
						if (dict.dictCode == "LoginAuthCode") {
							login.hasLoginAuthCodeOpen = true;
						} else
						// 机构
						if (dict.dictCode == "LoginCompNoRule") {
							login.hasLoginCompNoRule = true;
							// 如果存在取字典，不存在取web.xml
							$("input[name='compNo']").val(dict.dictContent || companyNo);
						}
					}
				}
			}
		});
	},
	/**
	 * 获取医院
	 */
	getCompList : function() {
		
		// 机构查询策略未开启时
		if (!login.hasLoginCompNoRule) {
			// 上一次输入的
			var loginCompNo = localStorage.getItem("loginCompNo") || "";
			if (loginCompNo) {
				companyNo = loginCompNo;
			}
			$("input[name='compNo']").val(companyNo);
		}
		
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/common/getCompList.spring",
			type : "get",
			skipDataCheck : true,
			dataType : "json",
			success : function(data) {
				if (data.result == "success") {
					// 多家医院，并且策略未开启
					if (data.compList.length > 1) {
						$("input[name='compNo']").val(data.compList[0].compNo);
						if (!login.hasLoginCompNoRule) {
							login.hasShowCompNo = true;
						}
					}
				}
			}
		});
	},
	// 加载手机号登录
	loadPhoneLogin : function() {
		$("input[name='userCode']").attr("placeholder", $("input[name='userCode']").attr("placeholder") + "/手机号");
		JPlaceHolder.refreshPlaceholder();
	},
	// 加载扫码登录
	loadScanQRCodeLogin : function() {
		$("input[name='compNo']").blur(function() {
			scanQRCodeLogin.loadQRCode();
		});
		$("#scanQRCodeLogin").load(basePath + "frame/login/modules/scanQRCodeLogin.jsp");
	},
	/**
	 * 登录数据
	 */
	loginCheck : function(formData) {
		// base加密
		var base = new Base64();
		var pwd = formData["ppwwddValue"];
		if (pwd) {
			formData["ppwwddValue"] = login.getChar(base.encode(pwd));
		}
		$.ajax({
			url : basePath + "frame/login/loginCheck.spring",
			type : "post",
			data : formData,
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				// 是否开启验证码
				if (login.hasLoginAuthCodeOpen) {
					$(".v_code").click();
				}
				
				if (data.result == "success") {
					// 缓存
					sessionStorage.setItem("CUSTOM_COMPLEX_RULE", pwd);
					// 记住切换的
					localStorage.setItem("loginCompNo", formData["compNo"]);
					
					$("#logining").addClass("layui-hide");
					$("#logined").removeClass("layui-hide");
					login.getUserInfo();
					
					window.remenber = (formData["remenber"] || "");
					assemblys.setCookie("remenber",window.remenber);
					// 进入框架
//					location.href = basePath + "frame/login/logining.spring?remenber=" + (formData["remenber"] || "");
				} else {
					assemblys.msg(data.resultMsg, function() {
						// 如果是验证码错误
						if (data.resultCode == "01122") {
							$(".layui-tab-item:eq(" + $(".layui-tab-title").find(".layui-this").index() + ")").find(".v_code").click();
						}
					}, 2500);
				}
			},
			error : function(e) {
				assemblys.alert("登录出错，请联系管理员");
			}
		});
	},
	loginIn : function(){
		location.href = basePath + "frame/login/logining.spring?remenber=" + window.remenber;
	},
	loginOut : function(){
		// 缓存
		sessionStorage.removeItem("CUSTOM_COMPLEX_RULE");
		// 记住切换的
		localStorage.removeItem("loginCompNo");
		
		$("input[name=userCode]").val("");
		$("input[name=ppwwddValue]").val("");
		
		$("#logining").removeClass("layui-hide");
		$("#logined").addClass("layui-hide");
		
		window.remenber = null;
		assemblys.setCookie("remenber","");
		assemblys.setCookie("JSESSIONID","");
	},
	getUserInfo : function(){
		$.ajax({
			url : basePath + "frame/useraction/getUserInfo.spring",
			dataType : "json",
			success : function(data) {
				$("#user_userName").text(data.data.user.userName);
				$("#user_userCode").text(data.data.user.userCode);
			},
			error : function(e) {
				assemblys.alert("登录出错，请联系管理员");
			}
		});
	},
	// 验证码
	chgImgCode : function(obj) {
		$(obj).attr("src", basePath + "/frame/excludeUrl/verificationCode/createCode.spring?createTypeFlag=n&" + Math.random());
		$("input[name='careCode']").val("");
	},
	/**
	 * 加载监听器
	 */
	loadMonitor : function() {
		
		//  医院机构
		if (login.hasShowCompNo) {
			if (login.hasOpenScanQRCode) {
				login.autoHight += 58;
			} else {
				login.autoHight += 28;
			}
			$(".compno_input").removeClass("layui-hide");
		}
		
		// 开启手机
		if (login.hasOpenPhoneLogin) {
			login.loadPhoneLogin();
		}
		
		// 开启扫码
		if (login.hasOpenScanQRCode) {
			login.loadScanQRCodeLogin();
		}
		
		// 打开链接
		if (login.hasLinkOpen) {
			$("#link").removeClass("layui-hide");
		} else {
			$("#link").remove();
		}
		
		// 是否开启验证码
		if (login.hasLoginAuthCodeOpen) {
			if (login.hasOpenScanQRCode) {
				login.autoHight += 58;
			} else {
				login.autoHight += 28;
			}
			$(".v_code").click();
			$(".loginAuthCode").removeClass("layui-hide");
		} else {
			$(".loginAuthCode").remove();
		}
		
		// 监听登录
		var $form = $("form[lay-filter='param']");
		// 是否有监听事件
		$form.find("input[name='compNo'],input[name='userCode'],input[name='ppwwddValue'],input[name='careCode']").keyup(function(e) {
			if (e.keyCode == 13) {
				var $this = $form.find("input[name='compNo']");
				if ($.trim($this.val()) == "") {
					$this.focus();
					return;
				}
				var $this = $form.find("input[name='userCode']");
				if ($.trim($this.val()) == "") {
					$this.focus();
					return;
				}
				$this = $form.find("input[name='ppwwddValue']");
				if ($.trim($this.val()) == "") {
					$this.focus();
					return;
				}
				if (login.hasLoginAuthCodeOpen) {
					$this = $form.find("input[name='careCode']");
					if ($.trim($this.val()) == "") {
						$this.focus();
						return;
					}
				}
				// 匹配登录
				$form.find("button:contains(登录)").filter(function() {
					return $(this).text() == "登录";
				}).click();
			}
		});
		
		//监听提交
		var form = layui.form;
		form.on('submit', function(data) {
			login.loginCheck(data.field);
			return false;
		});
		form.render();
		
	},
	/**
	 * 特殊处理
	 */
	specialHandle : function() {
		$(".main").fadeIn(800);
		// 动态识别容器
		$(".login_box").css("height", ($(".login_box").height() + login.autoHight + 62) + "px");
		$(".login_box_cotent").css("height", ($(".login_box").height() + 77) + "px");
		var value = 10;
		var layuiTabValue = parseInt(($(".login_left").height() - ($(".layui-tab").height() + (login.hasOpenScanQRCode ? 76 : 0))) / 2);
		$(".layui-tab").css("padding", (layuiTabValue - value) + "px 10px " + (layuiTabValue) + "px", " 10px");
		$(".login_img").css("padding", parseInt(($(".login_right").height() - ($(".login_img").height() || 270)) / 2) + "px 0");
		// 显示翅膀
		$(".login_bg_right,.login_bg_left").show();
	},
	getCategory:function(categoryId){
		$.ajax({
			url : "http://fortune.clifford.cn:9091/P1936/knowledge/category/getCategory.spring",
			type : "get",
			skipDataCheck : true,
			dataType : "json",
			data : {
				categoryId : categoryId
			},
			success : function(data) {
				if (data.result == "success") {
					data = data.data;
					$("#category_"+categoryId).text(data.category.name);
				}
			}
		});
	},
	getKnowledgeList:function(categoryId){
		$.ajax({
			url : "http://fortune.clifford.cn:9091/P1936/knowledge/knowledge/getKnowledgeList.spring",
			type : "get",
			skipDataCheck : true,
			dataType : "json",
			data : {
				categoryId : categoryId
			},
			success : function(data) {
				if (data.result == "success") {
					data = data.data;
					var html = "";
					var length = data.knowledgeList.length > 5 ? 5:data.knowledgeList.length;
					for (var i = 0; i < length; i++) {
						html += '<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp?knowledgeId='+data.knowledgeList[i].KnowledgeId+'" target="_blank">'+data.knowledgeList[i].Title+'</a><div class="content-card-ul-li-right">'+data.knowledgeList[i].CrtUserName+'</div></li>';
					}
					
					if(!html){
						html += '<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="javascript:;">暂无文章</a><div class="content-card-ul-li-right"></div></li>';
					}
					$("#category_"+categoryId).parent().next().empty().append(html);
				}
			}
		});
	},
	initKnowledge:function(){
		for (var i = -998; i < -994; i++) {
			login.getCategory(i);
			login.getKnowledgeList(i);
		}
	},
	/**
	 * 初始化
	 */
	init : function() {
		login.initKnowledge();
		login.getAuth().then(function() {
			// 动态修改发布年份
			$("#currentYear").html(new Date().getFullYear());
			// 如果已登录
			/*if (curUser && window.remenber) {
				location.href = basePath + "frame/login/logining.spring?remenber=" + remenber || "";
			}*/
			// 加载策略
			login.getSafeRule().then(function() {
				// 加载策略
				return login.getCompList();
			}).then(function() {
				// 如果记住登录
				/*if (remenber && remenber == 1) {
					login.loginCheck({
						"userCode" : username,
						"ppwwddValue" : "",
						"impersonateEmpNo" : impersonateEmpNo,
						"remenber" : remenber,
						"compNo" : $("input[name='compNo']").val()
					});
				}*/
				// 监听器
				login.loadMonitor();
				// 特殊处理
				login.specialHandle();
			});
		});
	},
	getChar : function(str){
		var baseChar = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
		str = str.substring(0,1) + baseChar.charAt(Math.floor(Math.random()*46)) + str.substring(1,str.length);
		str = baseChar.charAt(Math.floor(Math.random()*46)) + str + baseChar.charAt(Math.floor(Math.random()*46));
		return str;
	},
	toDetail : function(){
		
	}
}
login.init();