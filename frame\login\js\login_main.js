var login = {
	getAuth : function() {
		return $.ajax({
			url : basePath + "frame/login/checkAuth.spring",
			type : "get",
			dataType : "json",
			success : function(data) {
				if (data.data && data.data.auth == "0") {
					location.href = basePath + "xerror.jsp";
				}
			}
		});
	},
	// 通过字典识别要加载什么套件
	getSafeRule : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictByCode.spring",
			type : "get",
			data : {
				"dictCode" : "loginMode,LoginCompNoRule",
				"appCode" : "APP"
			},
			skipDataCheck : true,
			dataType : "json",
			success : function(data) {
				// 存在时
				if (data) {
					// 默认
					$("input[name='compNo']").val(companyNo);
					var dictList = data.dict;
					if (dictList) {
						for ( var index in dictList) {
							var dict = dictList[index];
							// 机构
							if (dict.dictCode == "LoginCompNoRule") {
								$("input[name='compNo']").val(dict.dictContent || companyNo);
							} else if (dict.dictCode == "loginMode") {
								// 不是访客登录时
								if (("," + dict.dictContent + ",").indexOf(",4,") == -1) {
									location.href = basePath + "frame/login/login.jsp";
								}
							}
						}
					}
				}
			}
		});
	},
	// 获取配置
	getMoblieConfig : function(moblieCode, callback) {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/moblieSetting/getByCode.spring",
			type : "get",
			data : {
				"moblieCode" : moblieCode,
				"compNo" : $("input[name='compNo']").val()
			},
			skipDataCheck : true,
			dataType : "json",
			success : callback
		});
	},
	/**
	 * 加载监听器
	 */
	loadMonitor : function() {
		var form = layui.form;
		// 申请访问
		$(".help").on("click", function() {
			// 公众号扫码
			login.getMoblieConfig("MOBLIE_WECHAT", function(moblie) {
				// 加载二维码
				wxGZHUtil.getQRCode(moblie, "QRImageGZH", function(data) {
					login.checkApply(data);
				});
				$('.QR_window_show,.QR_window,#QRImageGZHDiv').show();
			});
		});
		// 关闭
		$(".QR_delete").on("click", function() {
			$("#QRImageGZH").attr("src", "").removeAttr("style");
			$('.QR_window_show,.QR_window,#QRImageGZHDiv').hide();
		});
		form.render();
	},
	/**
	 * 登录数据
	 */
	loginCheck : function(formData) {
		// base加密
		var base = new Base64();
		var pwd = formData["ppwwddValue"];
		if (pwd) {
			formData["ppwwddValue"] = base.encode(pwd);
		}
		$.ajax({
			url : basePath + "frame/login/loginCheck.spring",
			type : "post",
			data : formData,
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 进入框架
					location.href = basePath + "frame/login/logining.spring";
				} else {
					assemblys.alert("登录出错，请联系管理员");
				}
			},
			error : function(e) {
				assemblys.alert("登录出错，请联系管理员");
			}
		});
	},
	// 检查是否正在申请中
	checkApply : function(data) {
		var wxOpenID = data.wxOpenID;
		var compNo = $("input[name='compNo']").val();
		$.ajax({
			url : basePath + "frame/apply/checkApply.spring",
			type : "get",
			data : {
				"wxOpenID" : wxOpenID
			},
			dataType : "json",
			success : function(data) {
				$(".QR_delete").click();
				// 没有申请过
				if (data.result == "none") {
					// 申请页面
					var url = basePath + "frame/apply/visitorApply.html?wxOpenID=" + wxOpenID + "&compNo=" + compNo;
					layer.open({
						content : url,
						type : 2,
						skin : 'layui-layer-aems',
						title : "申请访问",
						scrollbar : false,
						area : [ '75%', '75%' ]
					});
				} else if (data.result == "exist") {
					// 提醒
					assemblys.alert(data.msg);
				} else {
					// 登录
					login.loginCheck({
						"singleDiscernKey" : "wxOpenID",
						"ppwwddValue" : "",
						"singleDiscernValue" : wxOpenID,
						"loginModel" : "scanQRCode",
						"compNo" : "FK9999"
					});
				}
			}
		});
	},
	/**
	 * 特殊处理
	 */
	specialHandle : function() {
		$(".main").fadeIn(800);
		// 动态识别容器
		$(".main").css("margin-top", ($(window).height() * 0.13) + "px");
	},
	/**
	 * 初始化
	 */
	init : function() {
		login.getAuth().then(function() {
			// 动态修改发布年份
			$("#currentYear").html(new Date().getFullYear());
			// 如果已登录
			if (curUser) {
				location.href = basePath + "frame/login/logining.spring";
			}
			// 加载策略
			login.getSafeRule().then(function() {
				// 监听器
				login.loadMonitor();
				// 特殊处理
				login.specialHandle();
				// 企业微信扫码
				login.getMoblieConfig("MOBLIE_QIYEWENXIN", function(moblie) {
					window.scanQRCodeBind = {
						optType : "checkUser"
					}
					ddAndWxUtil.wxQRCodeInit(moblie, "QRImage");
				});
			});
		});
	}
}
login.init();