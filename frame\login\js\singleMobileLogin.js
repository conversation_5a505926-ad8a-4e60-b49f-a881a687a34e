var singleMobileLogin = {
	init : function() {
		singleMobileLogin.singleMobileLogin();
	},
	singleMobileLogin : function() {
		return $.ajax({
					url : basePath + "frame/login/singleMobileLogins.spring",
					data : param.__form(),
					success : function(data) {
						var url = "";
						if (data.to == "FAILURE") {
							url = "/frame/login/login.jsp";
						} else if (data.to == "XERROR") {
							url = "xerror.jsp";
						} else if (data.to == "LOGINMOBILE") {
							url = "/frame/login/loginMobile.html?1=1";
							url += "&singleParam=" + encodeURIComponent(data.singleParam)
						} else if (data.to == "MOBILEPAGECONFIG") {
							url = "/frame/mobile/pageConfig/mobilePageConfig.html?1=1";
							url += "&appCode=" + encodeURIComponent(data.appCode)
							url += "&singleCode=" + encodeURIComponent(data.singleCode)
							if (data.singleParam) {
								url += "&singleParam=" + encodeURIComponent(data.singleParam)
							}
						} else if (data.to == "SUCCESS") {
							url = "/frame/userWindows.jsp?1=1";
							url += "&singleCode=" + encodeURIComponent(data.singleCode)
							if (data.singleParam) {
								url += "&singleParam=" + encodeURIComponent(data.singleParam)
							}
						} else if (data.to == "INDEX") {
							url = "/frame/mobile/index/index.html?1=1";
							url += "&appCode=" + encodeURIComponent(data.appCode)
							url += "&singleCode=" + encodeURIComponent(data.singleCode)
							if (data.singleParam) {
								url += "&singleParam=" + encodeURIComponent(data.singleParam)
							}
						} else {
							url = data.to;
						}
						location.href = basePath + url;
					}
				});
	},
}