<%@page language="Java" contentType="text/html;charset=UTF-8"%>
<%@page import="javax.servlet.http.Cookie"%>
<%@page import="org.hyena.frame.Globals"%>
<%@page import="java.util.Date"%>
<%@page import="org.hyena.frame.view.User"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);

	String ws_basePath = "ws://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
	request.setAttribute("ws_basePath", ws_basePath);

	//Cookie 用户名
	String username = "";
	//Cookie 密码
	String impersonateEmpNo = "";
	// 是否记住
	String remenber = "";
	/****************** Cookie设置 ******************/
	Cookie myCookie[] = request.getCookies();
	if (myCookie != null) {
		for (int n = 0; n <= myCookie.length - 1; ++n) {
			Cookie newCookie = myCookie[n];
			if (newCookie.getName().equals("username")) {
				username = newCookie.getValue();
			}
			if (newCookie.getName().equals("impersonateEmpNo")) {
				impersonateEmpNo = newCookie.getValue();
			}
			if (newCookie.getName().equals("remenber")) {
				remenber = newCookie.getValue();
			}
		}
	}
	request.setAttribute("username", username);
	request.setAttribute("impersonateEmpNo", impersonateEmpNo);
	request.setAttribute("remenber", remenber);

	User curUser = (User) session.getAttribute(Globals.KEY_USER);
	request.setAttribute("curUser", curUser == null ? "" : curUser.getUserId());

	ServletContext servletContext = request.getSession().getServletContext();
	String companyNo = servletContext.getInitParameter("companyNo") + "";
	request.setAttribute("companyNo", companyNo);
	
	long random = new Date().getTime();
	request.setAttribute("random", random);
	
%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, must-revalidate">
<meta http-equiv="expires" content="0">
<title>科进 | 医疗安全管控平台</title>
<link rel="icon" href="${basePath}favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}frame/css/default/common.css" />
<link rel="stylesheet" href="${basePath}/frame/login/css/login.css?ver=4.5">
<script>
	var basePath = "${basePath}";
	var curUser = "${curUser}";
	var username = "${username}";
	var impersonateEmpNo = "${impersonateEmpNo}";
	var remenber = "${remenber}";
	var ws_basePath = "${ws_basePath}";
	var companyNo = "${companyNo}";
	sessionStorage.removeItem("water_mark");
</script>
</head>
<body>
	<div class="main" style="display: none;">
		<img src="${basePath}frame/logo/images/login_top_logo.png?${random}" class="login_title" alt="">
		<div class="login_box_cotent">
			<img src="${basePath}/frame/login/images/loginLeft.png" class="login_bg_left">
			<img src="${basePath}/frame/login/images/loginRight.png" class="login_bg_right">
			<div class="login_box">
				<div class="login_left">
					<div class="login_img">
						<img src="${basePath}frame/logo/images/login_left_logo.png?${random}" draggable="false" alt="">
					</div>
				</div>
				<div class="login_right">
					<div class="layui-tab" lay-filter="docTabBrief">
						<ul class="layui-tab-title head2_tab">
							<li class="layui-this">用户登录</li>
						</ul>
						<div class="layui-tab-content">
							<div class="layui-tab-item layui-show ">
								<form class="layui-form" onsubmit="return false;" lay-filter="param">
									<div class="input_box username_box compno_input  layui-hide ">
										<img src="${basePath}/frame/login/images/bhao4.png" class="i_user_psd" alt="">
										<input type="text" name="compNo" lay-verify="required" value="" placeholder="机构编号" autocomplete="off" class="layui-input username input">
									</div>
									<div class="input_box username_box">
										<img src="${basePath}/frame/login/images/username4.png" class="i_user_psd" alt="">
										<input type="text" name="userCode" lay-verify="required" value="" placeholder="用户名" autocomplete="off" class="layui-input username input">
									</div>
									<div class="input_box password_box">
										<img src="${basePath}/frame/login/images/password4.png" class="i_user_psd" alt="">
										<input type="password" name="ppwwddValue" value="" placeholder="密码" lay-verify="required|pwd" autocomplete="off" class="layui-input password">
									</div>
									<div class="loginAuthCode layui-hide">
										<div class="input_box v_code_box">
											<img src="${basePath}/frame/login/images/v_code_i4.png" class="i_user_psd" alt="">
											<input type="text" name="careCode" lay-verify="required" value="" placeholder="验证码" autocomplete="off" class="layui-input  input" style="width: 130px;">
										</div>
										<img class="v_code" title="点击更换" style="cursor: pointer;" onclick="login.chgImgCode(this)" src="" alt="" />
									</div>
									<div class="login_button_box">
										<input type="checkbox" name="remenber" value="1" title="7天之内自动登录" lay-skin="primary">
										<button type="button" lay-submit class="layui-btn layui-btn-normal btn_login">登录</button>
									</div>
									<div id="scanQRCodeLogin"></div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="link" class="layui-hide">
			<span class="link_text">友情链接：</span>
			<span class="link_text">
				<a target="_blank" href="http://www.nhc.gov.cn/">国家卫健委</a>
			</span>
			<span class="link_text">|</span>
			<span class="link_text">
				<a target="_blank" href="http://www.nmpa.gov.cn/">国家药监局</a>
			</span>
			<span class="link_text">|</span>
			<span class="link_text">
				<a target="_blank" href="http://wsjkw.gd.gov.cn/">广东省卫健委</a>
			</span>
			<span class="link_text">|</span>
			<span class="link_text">
				<a target="_blank" href="http://mpa.gd.gov.cn/">广东省药监局</a>
			</span>
			<span class="link_text">|</span>
			<span class="link_text">
				<a target="_blank" href="http://wjw.hunan.gov.cn/">湖南省卫健委</a>
			</span>
			<span class="link_text">|</span>
			<span class="link_text">
				<a target="_blank" href="http://wjw.fujian.gov.cn/">福建省卫健委</a>
			</span>
		</div>
		<div class="foot">
			<p>科进软件 — 助力医院营造安全就诊环境</p>
			<p>
				Copyright © 2009-
				<span id="currentYear"></span>
				Fortune SoftWare Itd. All Rights Reserved.
			</p>
		</div>
		<div style="text-align: center; margin-top: 10px;">
			<span class="link_text">
				<a href="${basePath}frame/fileData/chrome_installer_32.exe">谷歌浏览器下载</a>
			</span>
		</div>
	</div>
	<div class="ruler"></div>
</body>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/base64.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/cookie.js"></script>
<script type="text/javascript" src="${basePath}frame/login/js/login.js?ver=1.0"></script>
</html>
