<%@page language="Java" contentType="text/html;charset=UTF-8"%>
<%@page import="javax.servlet.http.Cookie"%>
<%@page import="org.hyena.frame.Globals"%>
<%@page import="java.util.Date"%>
<%@page import="org.hyena.frame.view.User"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);

	String ws_basePath = "ws://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
	request.setAttribute("ws_basePath", ws_basePath);

	//Cookie 用户名
	String username = "";
	//Cookie 密码
	String impersonateEmpNo = "";
	// 是否记住
	String remenber = "";
	/****************** Cookie设置 ******************/
	Cookie myCookie[] = request.getCookies();
	if (myCookie != null) {
		for (int n = 0; n <= myCookie.length - 1; ++n) {
			Cookie newCookie = myCookie[n];
			if (newCookie.getName().equals("username")) {
				username = newCookie.getValue();
			}
			if (newCookie.getName().equals("impersonateEmpNo")) {
				impersonateEmpNo = newCookie.getValue();
			}
			if (newCookie.getName().equals("remenber")) {
				remenber = newCookie.getValue();
			}
		}
	}
	request.setAttribute("username", username);
	request.setAttribute("impersonateEmpNo", impersonateEmpNo);
	request.setAttribute("remenber", remenber);

	User curUser = (User) session.getAttribute(Globals.KEY_USER);
	request.setAttribute("curUser", curUser == null ? "" : curUser.getUserId());

	ServletContext servletContext = request.getSession().getServletContext();
	String companyNo = servletContext.getInitParameter("companyNo") + "";
	request.setAttribute("companyNo", companyNo);
	
	long random = new Date().getTime();
	request.setAttribute("random", random);
	
%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, must-revalidate">
<meta http-equiv="expires" content="0">
<title>科进 | 医疗安全管控平台</title>
<link rel="icon" href="${basePath}favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" href="${basePath}/frame/login/css/loginDetail.css?ver=4.5">
<script>
	var basePath = "${basePath}";
	var curUser = "${curUser}";
	var username = "${username}";
	var impersonateEmpNo = "${impersonateEmpNo}";
	var remenber = "${remenber}";
	var ws_basePath = "${ws_basePath}";
	var companyNo = "${companyNo}";
	var knowledgeId = "${param.knowledgeId}";
	sessionStorage.removeItem("water_mark");
	if (!curUser) {
		location.href = "loginNew.jsp";
	}
</script>
</head>
<body>
	<div class="top-img">
		<div class="top-img-name">科进软件</div><div class="top-img-content">中国医院协会"唯一”医疗质量信息化服务理事成员、国家卫健委医管所不良事件行动小组“唯一”企业成员</div>
	</div>
	<div class="platform-name">
		<span class="platform-name-company">科进</span>
		<span class="platform-name-software">医疗质量管控平台</span>
	</div>
	<div class="nav">
		<div class="nav-tab">
			<span class="layui-breadcrumb" lay-separator="|">
			  <a href="javascript:;" class="layui-this">首页</a>
			  <a href="javascript:;">质量安全</a>
			  <a href="javascript:;">患者安全</a>
			  <a href="javascript:;">医院评审</a>
			  <a href="javascript:;">质量监测</a>
			  <a href="javascript:;">制度规范</a>
			  <a href="javascript:;">质量简讯</a>
			  <a href="javascript:;">医政管理</a>
			  <a href="javascript:;">质量论坛</a>
			</span>
			<div class="nav-tab-bottom"></div>
		</div>
		<div class="title">
			<div class="title-img">
				<img src="images/2024_login_title.png"/>
			</div>
		</div>
		<div class="content">
			<div class="content-title" id="knowledge_title">
				关于学习《国家卫生健康委办公厅关于进一步加强医学证明文件类医疗文书管理工作的通知国家卫生健康委办公厅关于进一步加强医学证明文件类医疗文书管理工作的通知》通知
			</div>
			<div class="content-date">
				发布日期: <font id="knowledge_datetime">2023.12.11</font> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 来源 : <font id="knowledge_creater">医务部</font>
			</div>
			<div class="content-text" id="knowledge_content">
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;一、起草背景</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;医学证明文件类医疗文书(以下简称医疗文书)在社会活动中发挥着重要作用，涉及工作、生活、学习等各个方面，但除出
				生医学证明书、居民死亡医学证明(推断)书，、职业病诊断证明书等已有明确规定的医疗文书以外，国家对其余医学证明文件类
				医疗文书尚无统一管理规定或要求，不利于医疗文书的规范开具和使用。为进一步加强医疗文书管理，指导医疗机构及其医务人
				员规范开具医疗文书，根据《中华人民共和国医师法》《医疗机构管理条例》等有关文件精神，在广泛征求意见的基础上，我委
				组织制定了《国家卫生健康委办公厅关于进一步加强医学证明文件类医疗文书管理工作的通知》 (以下简称《通知》)。</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;二、主要内容</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;《通知》就医疗机构内部加强医疗文书管理工作提出了统一要求。一是完善医疗文书管理制度。要求医疗机构建立健全医疗
				文书管理制度，梳理制定医疗文书目录，明确医疗文书开具人员条件并进行动态管理，落实证章分离要求。二是统一医疗文书开
				具内容。要求医疗机构统一本机构医疗文书格式，明确医疗文书内容的基本规范或要求，建立统一编号和留存备份机制。三是规
				范医疗文书开具行为。要求医疗机构从各个环节加强对本机构医师开具医疗文书的行为管理。四是加强医疗文书核查管理。要求
				医疗机构建立回溯核查机制，加强医疗文书结果质量管理。五是压实医疗文书开具责任。要求医疗机构建立医疗文书责任追究机
				制，强化医务人员的责任意识，保障医疗文书质量</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;三、工作要求</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;各级各类医疗机构是医疗文书管理的主体，要认真理解《通知》要求，结合工作实际，立即建立健全本机构医疗文书管理制
				度，进一步明确责任分工，落实相关管理要求，将尚未进行统一管理的各类医疗文书尽快纳入管理范畴，消除管理空白点和漏</p>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;一、起草背景</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;医学证明文件类医疗文书(以下简称医疗文书)在社会活动中发挥着重要作用，涉及工作、生活、学习等各个方面，但除出
				生医学证明书、居民死亡医学证明(推断)书，、职业病诊断证明书等已有明确规定的医疗文书以外，国家对其余医学证明文件类
				医疗文书尚无统一管理规定或要求，不利于医疗文书的规范开具和使用。为进一步加强医疗文书管理，指导医疗机构及其医务人
				员规范开具医疗文书，根据《中华人民共和国医师法》《医疗机构管理条例》等有关文件精神，在广泛征求意见的基础上，我委
				组织制定了《国家卫生健康委办公厅关于进一步加强医学证明文件类医疗文书管理工作的通知》 (以下简称《通知》)。</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;二、主要内容</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;《通知》就医疗机构内部加强医疗文书管理工作提出了统一要求。一是完善医疗文书管理制度。要求医疗机构建立健全医疗
				文书管理制度，梳理制定医疗文书目录，明确医疗文书开具人员条件并进行动态管理，落实证章分离要求。二是统一医疗文书开
				具内容。要求医疗机构统一本机构医疗文书格式，明确医疗文书内容的基本规范或要求，建立统一编号和留存备份机制。三是规
				范医疗文书开具行为。要求医疗机构从各个环节加强对本机构医师开具医疗文书的行为管理。四是加强医疗文书核查管理。要求
				医疗机构建立回溯核查机制，加强医疗文书结果质量管理。五是压实医疗文书开具责任。要求医疗机构建立医疗文书责任追究机
				制，强化医务人员的责任意识，保障医疗文书质量</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;三、工作要求</p>
				<br/>
				<br/>
				<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;各级各类医疗机构是医疗文书管理的主体，要认真理解《通知》要求，结合工作实际，立即建立健全本机构医疗文书管理制
				度，进一步明确责任分工，落实相关管理要求，将尚未进行统一管理的各类医疗文书尽快纳入管理范畴，消除管理空白点和漏</p>
			</div>
		</div>
		<div class="nav-foot">
			<p>科进软件 — 助力医院高质量发展</p>
		</div>
	</div>
	<div class="login_box">
		<div id="logined" class="login_right">
			<div class="layui-tab" lay-filter="docTabBrief"  style="text-align: center!important;">
				<img src="images/doctor.png" width="150" height="200"/>
			</div>
			<br/>
			<div class="layui-tab" lay-filter="docTabBrief" style="text-align: center!important;">
				<font style="font-size: 20px;font-weight: 700;">${ frameUser.userName }</font>
				<font> / ${ frameUser.userCode }</font>
			</div>
			<div class="layui-tab" lay-filter="docTabBrief" style="text-align: center!important;">
				<button type="button" class="" style="width: 200px;margin-top: 5px;background-color: RGB(54,138,226);color: white;padding: 5px;border-radius: 5px;border: 1px solid black;" onclick="login.loginIn()">进入工作台</button>
				<button type="button" class="" style="width: 200px;margin-top: 5px;background-color: black;color: white;padding: 5px;border-radius: 5px;border: 1px solid black;" onclick="login.loginOut()">退出登录</button>
				<button type="button" class="" style="width: 200px;margin-top: 5px;background-color: white;color: black;padding: 5px;border-radius: 5px;border: 1px solid black;" onclick="login.loginOut()">重新登录</button>
			</div>
		</div>
		<div class="foot">
			<p>科进软件 — 助力医院高质量发展</p>
		</div>
	</div>
	<div class="ruler"></div>
</body>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/base64.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/cookie.js"></script>
<script type="text/javascript" src="${basePath}frame/login/js/loginNew.js?ver=1.0"></script>
<script type="text/javascript" src="${basePath}frame/login/js/loginDetail.js?ver=1.0"></script>
<script type="text/javascript">
	$(function(){
		loginDetail.init();
	})
</script>
</html>
