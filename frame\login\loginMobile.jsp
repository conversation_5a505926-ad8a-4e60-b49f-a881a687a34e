<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String port = ":" + request.getServerPort();
	String serverPort = ((port).equals(":80") || (port).equals(":443")) ? "" : port;
	String basePath = request.getScheme() + "://" + request.getServerName() + serverPort + path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>绑定用户</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/app/css/common.css" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="${basePath}plugins/vant/css/list.css">
<link rel="stylesheet" type="text/css" href="${basePath}frame/login/css/mobileLogin.css">
<script type="text/javascript">
	var basePath = "${basePath}";
</script>
<style>
body {
	display: block;
}
</style>
</head>
<body class="bg">
	<div app="loginMobile" class="login-Mobiles" id="loginMobile" data-v-app="" style="display: unset;">
		<form class="layui-form" onsubmit="return false;" lay-filter="param">
		<input type="hidden" id="paramLoginKey" value="<c:out value="${singleParam.loginKey}"/>">
		<input type="hidden" id="paramLoginValue" value="<c:out value="${singleParam.userCode}"/>">
		<input type="hidden" id="paramSingleCode" value="<c:out value="${singleParam.singleCode}"/>">
		<input type="hidden" id="paramCompNo" value="<c:out value="${singleParam.compNo}"/>">
			<div class="login">
				<img src="../../frame/login/images/login_title.png" alt="">
			</div>
			<div class="login">
				<div class="login-input-userCode">
					<div class="van-cell-group van-cell-group--inset">
						<div class="van-cell van-field">
							<div class="van-field__left-icon">
								<i class="van-badge__wrapper van-icon van-icon-manager-o">
								</i>
							</div>
							<div class="van-cell__value van-field__value">
								<div class="van-field__body">
									<input type="text" id="userCode" name="userCode" class="van-field__control" lay-verify="required" placeholder="用户名" autocomplete="off">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="login-input-password">
					<div class="van-cell-group van-cell-group--inset">
						<div class="van-cell van-field">
							<div class="van-field__left-icon">
								<i class="van-badge__wrapper van-icon van-icon-closed-eye">
								</i>
							</div>
							<div class="van-cell__value van-field__value">
								<div class="van-field__body">
									<input type="password" id="ppwwddValue" name="ppwwddValue" class="van-field__control" lay-verify="required|pwd" placeholder="密码" autocomplete="off">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="bot-submit">
					<button type="submit" onclick="login();" lay-submit class="van-button van-button--primary van-button--normal van-button--block van-button--round" style="color: white; background: rgb(66, 155, 226); border-color: rgb(66, 155, 226);">
						<div style="color: white;" class="van-button__content">
							<span class="van-button__text">绑定用户</span>
						</div>
					</button>
				</div>
			</div>
		</form>
	</div>
</body>
</html>
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/app/js/common.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/base64.js"></script>
<script type="text/javascript" src="${basePath}frame/login/js/loginMobile.js"></script>
<script type="text/javascript">
	$(function() {
		assemblys.alert("检测当你未绑定账号，请绑定账号");
	});
</script>
