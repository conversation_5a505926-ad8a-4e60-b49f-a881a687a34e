<%@page language="Java" contentType="text/html;charset=UTF-8"%>
<%@page import="javax.servlet.http.Cookie"%>
<%@page import="org.hyena.frame.Globals"%>
<%@page import="java.util.Date"%>
<%@page import="org.hyena.frame.view.User"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);

	String ws_basePath = "ws://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
	request.setAttribute("ws_basePath", ws_basePath);

	//Cookie 用户名
	String username = "";
	//Cookie 密码
	String impersonateEmpNo = "";
	// 是否记住
	String remenber = "";
	/****************** Cookie设置 ******************/
	Cookie myCookie[] = request.getCookies();
	if (myCookie != null) {
		for (int n = 0; n <= myCookie.length - 1; ++n) {
			Cookie newCookie = myCookie[n];
			if (newCookie.getName().equals("username")) {
				username = newCookie.getValue();
			}
			if (newCookie.getName().equals("impersonateEmpNo")) {
				impersonateEmpNo = newCookie.getValue();
			}
			if (newCookie.getName().equals("remenber")) {
				remenber = newCookie.getValue();
			}
		}
	}
	request.setAttribute("username", username);
	request.setAttribute("impersonateEmpNo", impersonateEmpNo);
	request.setAttribute("remenber", remenber);

	User curUser = (User) session.getAttribute(Globals.KEY_USER);
	request.setAttribute("curUser", curUser == null ? "" : curUser.getUserId());

	ServletContext servletContext = request.getSession().getServletContext();
	String companyNo = servletContext.getInitParameter("companyNo") + "";
	request.setAttribute("companyNo", companyNo);
	
	long random = new Date().getTime();
	request.setAttribute("random", random);
	
%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, must-revalidate">
<meta http-equiv="expires" content="0">
<title>科进 | 医疗安全管控平台</title>
<link rel="icon" href="${basePath}favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" href="${basePath}/frame/login/css/loginNew.css?ver=4.5">
<script>
	var basePath = "${basePath}";
	var curUser = "${curUser}";
	var username = "${username}";
	var impersonateEmpNo = "${impersonateEmpNo}";
	var remenber = "${remenber}";
	var ws_basePath = "${ws_basePath}";
	var companyNo = "${companyNo}";
	sessionStorage.removeItem("water_mark");
</script>
</head>
<body>
	<div class="nav">
		<div class="nav-tab">
			<span class="layui-breadcrumb" lay-separator="|">
			  <a href="javascript:;" class="layui-this">首页</a>
			  <a href="javascript:;">质量安全</a>
			  <a href="javascript:;">患者安全</a>
			  <a href="javascript:;">医院评审</a>
			  <a href="javascript:;">质量监测</a>
			  <a href="javascript:;">制度规范</a>
			  <a href="javascript:;">质量简讯</a>
			  <a href="javascript:;">医政管理</a>
			  <a href="javascript:;">质量论坛</a>
			</span>
			<div class="nav-tab-bottom"></div>
		</div>
		<div class="title">
			<div class="title-img">
				<img src="images/2024_login_title.png"/>
			</div>
		</div>
		<div class="content">
			<div class="content-card-row">
				<div class="content-card">
					<p class="content-card-title"><span id="category_-998">公告</span><a>更多</a></p>
					<ul class="content-card-ul">
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">111111111111111</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">2222222222222222222222222222222222222</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">333333333333333333333333333333333333333333333333333333333333333333333333333</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">***************</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">55555</a><div class="content-card-ul-li-right">行风办</div></li>
					</ul>
				</div>
				<div class="content-card">
					<p class="content-card-title"><span id="category_-997">制度</span><a>更多</a></p>
					<ul class="content-card-ul">
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">111111111111111</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">2222222222222222222222222222222222222</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">333333333333333333333333333333333333333333333333333333333333333333333333333</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">***************</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">55555</a><div class="content-card-ul-li-right">行风办</div></li>
					</ul>
				</div>
			</div>
			<div class="content-card-row">
				<div class="content-card">
					<p class="content-card-title"><span id="category_-996">培训</span><a>更多</a></p>
					<ul class="content-card-ul">
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">111111111111111</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">2222222222222222222222222222222222222</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">333333333333333333333333333333333333333333333333333333333333333333333333333</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">***************</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">55555</a><div class="content-card-ul-li-right">行风办</div></li>
					</ul>
				</div>
				<div class="content-card">
					<p class="content-card-title"><span id="category_-995">警告</span><a>更多</a></p>
					<ul class="content-card-ul">
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">111111111111111</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">2222222222222222222222222222222222222</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">333333333333333333333333333333333333333333333333333333333333333333333333333</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">***************</a><div class="content-card-ul-li-right">行风办</div></li>
						<li class="content-card-ul-li"><a class="content-card-ul-li-left" href="loginDetail.jsp" target="_blank">55555</a><div class="content-card-ul-li-right">行风办</div></li>
					</ul>
				</div>	
			</div>
		</div>
	</div>
	<div class="main" style="display: none;">
		<div class="main-div">
			<img src="${basePath}frame/logo/images/login_top_logo.png?${random}" class="login_title" alt="">
			<div class="login_box_cotent">
				<div class="login_box">
					<div id="logining" class="login_right">
						<div class="layui-tab" lay-filter="docTabBrief">
							<ul class="layui-tab-title head2_tab">
								<li class="layui-this">用户登录</li>
							</ul>
							<div class="layui-tab-content">
								<div class="layui-tab-item layui-show ">
									<form class="layui-form" onsubmit="return false;" lay-filter="param">
										<div class="input_box username_box compno_input  layui-hide ">
											<img src="${basePath}/frame/login/images/bhao4.png" class="i_user_psd" alt="">
											<input type="text" name="compNo" lay-verify="required" value="" placeholder="机构编号" autocomplete="off" class="layui-input username input">
										</div>
										<div class="input_box username_box">
											<img src="${basePath}/frame/login/images/username4.png" class="i_user_psd" alt="">
											<input type="text" name="userCode" lay-verify="required" value="" placeholder="用户名" autocomplete="off" class="layui-input username input">
										</div>
										<div class="input_box password_box">
											<img src="${basePath}/frame/login/images/password4.png" class="i_user_psd" alt="">
											<input type="password" name="ppwwddValue" value="" placeholder="密码" lay-verify="required|pwd" autocomplete="off" class="layui-input password">
										</div>
										<div class="loginAuthCode layui-hide">
											<div class="input_box v_code_box">
												<img src="${basePath}/frame/login/images/v_code_i4.png" class="i_user_psd" alt="">
												<input type="text" name="careCode" lay-verify="required" value="" placeholder="验证码" autocomplete="off" class="layui-input  input" style="width: 130px;">
											</div>
											<img class="v_code" title="点击更换" style="cursor: pointer;" onclick="login.chgImgCode(this)" src="" alt="" />
										</div>
										<div class="login_button_box">
											<input type="checkbox" name="remenber" value="1" title="7天之内自动登录" lay-skin="primary">
											<button type="button" lay-submit class="layui-btn layui-btn-normal btn_login">登录</button>
										</div>
										<div style="text-align: right;">
											<span class="link_text">
												<a href="${basePath}frame/fileData/chrome_installer_32.exe" style="text-decoration: underline">谷歌浏览器下载</a>
											</span>
										</div>
										<div id="scanQRCodeLogin"></div>
									</form>
								</div>
							</div>
						</div>
					</div>
					<div id="logined" class="login_right layui-hide">
						<div class="layui-tab" lay-filter="docTabBrief"  style="text-align: center!important;">
							<img src="images/doctor.png" width="150" height="200"/>
						</div>
						<br/>
						<div class="layui-tab" lay-filter="docTabBrief" style="text-align: center!important;">
							<font style="font-size: 20px;font-weight: 700;" id="user_userName">初始化管理员</font>
							<font> / </font>
							<font id="user_userCode">ADMIN</font>
						</div>
						<div class="layui-tab" lay-filter="docTabBrief" style="text-align: center!important;">
							<button type="button" class="" style="width: 200px;margin-top: 5px;background-color: RGB(54,138,226);color: white;padding: 5px;border-radius: 5px;" onclick="login.loginIn()">进入工作台</button>
							<button type="button" class="" style="width: 200px;margin-top: 5px;background-color: black;color: white;padding: 5px;border-radius: 5px;" onclick="login.loginOut()">退出登录</button>
							<button type="button" class="" style="width: 200px;margin-top: 5px;background-color: white;color: black;padding: 5px;border-radius: 5px;" onclick="login.loginOut()">重新登录</button>
						</div>
					</div>
					<div class="foot">
						<p>科进软件 — 助力医院高质量发展</p>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="ruler"></div>
</body>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/base64.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/cookie.js"></script>
<script type="text/javascript" src="${basePath}frame/login/js/loginNew.js?ver=1.0"></script>
</html>
