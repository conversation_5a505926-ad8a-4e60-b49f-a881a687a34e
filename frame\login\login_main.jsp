<%@page language="Java" contentType="text/html;charset=UTF-8"%>
<%@page import="javax.servlet.http.Cookie"%>
<%@page import="org.hyena.frame.Globals"%>
<%@page import="org.hyena.frame.view.User"%>
<%@page import="java.util.Date"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);

	String ws_basePath = "wss://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
	request.setAttribute("ws_basePath", ws_basePath);

	User curUser = (User) session.getAttribute(Globals.KEY_USER);
	request.setAttribute("curUser", curUser == null ? "" : curUser.getUserId());

	ServletContext servletContext = request.getSession().getServletContext();
	String companyNo = servletContext.getInitParameter("companyNo") + "";
	request.setAttribute("companyNo", companyNo);

	long random = new Date().getTime();
	request.setAttribute("random", random);
	
	String remenber = "1";
	Cookie cooRemenber = new Cookie("remenber", remenber);
	cooRemenber.setMaxAge(30 * 24 * 60 * 60);
	cooRemenber.setPath("/");
	response.addCookie(cooRemenber);
%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, must-revalidate">
<meta http-equiv="expires" content="0">
<title>科进 | 医疗安全管控平台</title>
<link rel="icon" href="${basePath}favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}frame/css/default/common.css" />
<link rel="stylesheet" href="${basePath}/frame/login/css/login_main.css">
<script>
	var basePath = "${basePath}";
	var curUser = "${curUser}";
	var companyNo = "${companyNo}";
	var ws_basePath = "${ws_basePath}";
	sessionStorage.removeItem("water_mark");
</script>
</head>
<body>
	<div class="main" style="display: none;">
		<div class="login_box">
			<div class="img_left">
				<img src="${basePath}frame/login/images/login_main_bg_logo.png?${random}" draggable="false" alt="">
			</div>
			<div class="img_middle">
				<img src="${basePath}frame/login/images/login_mian_bg_font.png?${random}" draggable="false" alt="">
			</div>
			<div class="img_right">
				<input type="hidden" name="compNo" value="" />
				<input type="hidden" name="remenber" value="1">
				<div id="QRImage"></div>
				<div class="QR_window">
					<div class="QR_window_show"></div>
					<div id="QRImageGZHDiv">
						<img id="QRImageGZH">
						<font>
							请使用【
							<i class='layui-icon2' style="color: #07c160;">&#xe906;</i>
							微信】扫描二维码登录"公众号"
						</font>
					</div>
					<i class="layui-icon layui-icon-close QR_delete"></i>
				</div>
				<!-- <div class="help">
					访客登录
					<i class="layui-icon2 QR_help"> </i>
				</div> -->
			</div>
			<div class="foot">
				<p>
					友情链接：
					<a target="_blank" href="http://www.fortuneltd.com.cn">科进软件官网</a>
					|
					<a target="_blank" href="http://wiki.fortuneltd.com.cn">科进医疗安全知识库</a>
				</p>
				<p>科进软件 — 助力中国医院高质量发展</p>
				<p>
					Copyright © 2009-
					<span id="currentYear"></span>
					Fortune SoftWare Itd. All Rights Reserved.
				</p>
			</div>
			<div class="link_text">
				<a href="${basePath}frame/fileData/chrome_installer_32.exe">谷歌浏览器下载</a>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/base64.js"></script>
<script type="text/javascript" src="${basePath}plugins/common/js/cookie.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/wwLogin-1.0.0.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/wxGZHUtil.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/ddAndWxUtil.js"></script>
<script type="text/javascript" src="${basePath}frame/login/js/login_main.js"></script>
</html>
