<!DOCTYPE HTML>
<html>
<head>
<title>授权登录</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<meta name="content-type" content="text/html; charset=UTF-8">
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript">
	var wxPath = "https://open.weixin.qq.com/connect/oauth2/authorize";
	var corpID = assemblys.getParam("corpID");
	var code = assemblys.getParam("code");
	var appCode = assemblys.getParam("appCode");
	var singleCode = assemblys.getParam("singleCode");
	var compNo = assemblys.getParam("compNo");
	var loginKey = assemblys.getParam("loginKey");
	$.ajax({
		url : basePath + "frame/excludeUrl/scanCode/mobileLogin.spring",
		data : {
			code : code,
			loginKey : loginKey,
			singleCode : singleCode,
			compNo : compNo,
			appCode : appCode
		},
		success : function(data) {
			// 拼接格式
			var url = basePath + data.url;
			location.href = url;
		}
	});
</script>
</head>
<body>
	<div>请稍后...</div>
</body>
</html>