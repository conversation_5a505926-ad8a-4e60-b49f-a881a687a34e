<%@page language="Java" contentType="text/html;charset=UTF-8"%>
<%@page import="javax.servlet.http.Cookie"%>
<%@page import="org.hyena.frame.Globals"%>
<%@page import="java.util.Date"%>
<%@page import="org.hyena.frame.view.User"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);

	String ws_basePath = "ws://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
	request.setAttribute("ws_basePath", ws_basePath);

	//Cookie 用户名
	String username = "";
	//Cookie 密码
	String impersonateEmpNo = "";
	// 是否记住
	String remenber = "";
	/****************** Cookie设置 ******************/
	Cookie myCookie[] = request.getCookies();
	if (myCookie != null) {
		for (int n = 0; n <= myCookie.length - 1; ++n) {
			Cookie newCookie = myCookie[n];
			if (newCookie.getName().equals("username")) {
				username = newCookie.getValue();
			}
			if (newCookie.getName().equals("impersonateEmpNo")) {
				impersonateEmpNo = newCookie.getValue();
			}
			if (newCookie.getName().equals("remenber")) {
				remenber = newCookie.getValue();
			}
		}
	}
	request.setAttribute("username", username);
	request.setAttribute("impersonateEmpNo", impersonateEmpNo);
	request.setAttribute("remenber", remenber);

	User curUser = (User) session.getAttribute(Globals.KEY_USER);
	request.setAttribute("curUser", curUser == null ? "" : curUser.getUserId());

	ServletContext servletContext = request.getSession().getServletContext();
	String companyNo = servletContext.getInitParameter("companyNo") + "";
	request.setAttribute("companyNo", companyNo);
	
	long random = new Date().getTime();
	request.setAttribute("random", random);
	
%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, must-revalidate">
<meta http-equiv="expires" content="0">
<title>科进 | 医疗安全管控平台</title>
<link rel="icon" href="${basePath}favicon.ico" type="image/x-icon" />
<link rel="stylesheet" type="text/css" href="../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="../../plugins/vant/css/list.css">
<link rel="stylesheet" type="text/css" href="css/mobileLogin.css">
<script>
	var basePath = "${basePath}";
	var curUser = "${curUser}";
	var username = "${username}";
	var impersonateEmpNo = "${impersonateEmpNo}";
	var remenber = "${remenber}";
	var ws_basePath = "${ws_basePath}";
	var companyNo = "${companyNo}";
	sessionStorage.removeItem("water_mark");
</script>
</head>
<body class="bg">
	<div app="loginMobile" class="login-Mobiles" id="loginMobile" data-v-app="" style="display: unset;">
		<form class="layui-form" onsubmit="return false;" lay-filter="param">
			<input type="hidden" name="compNo" value="" >
			<div class="login">
				<img src="../../frame/login/images/login_title.png" alt="">
			</div>
			<div class="login">
				<div class="login-input-userCode">
					<div class="van-cell-group van-cell-group--inset">
						<div class="van-cell van-field">
							<div class="van-field__left-icon">
								<i class="van-badge__wrapper van-icon van-icon-manager-o">
								</i>
							</div>
							<div class="van-cell__value van-field__value">
								<div class="van-field__body">
									<input type="text" name="userCode" class="van-field__control" lay-verify="required" placeholder="用户名" autocomplete="off">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="login-input-password">
					<div class="van-cell-group van-cell-group--inset">
						<div class="van-cell van-field">
							<div class="van-field__left-icon">
								<i class="van-badge__wrapper van-icon van-icon-closed-eye">
								</i>
							</div>
							<div class="van-cell__value van-field__value">
								<div class="van-field__body">
									<input type="password" name="ppwwddValue" class="van-field__control" lay-verify="required|pwd" placeholder="密码" autocomplete="off">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="bot-submit">
					<button type="submit" lay-submit class="van-button van-button--primary van-button--normal van-button--block van-button--round" style="color: white; background: rgb(66, 155, 226); border-color: rgb(66, 155, 226);">
						<div class="van-button__content">
							<span class="van-button__text">登录</span>
						</div>
					</button>
				</div>
			</div>
		</form>
	</div>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys.js"></script>
<script type="text/javascript" src="../../plugins/common/js/base64.js"></script>
<script type="text/javascript" src="../../plugins/common/js/cookie.js"></script>
<script type="text/javascript" src="js/mobileLogin.js?ver=1.0"></script>
