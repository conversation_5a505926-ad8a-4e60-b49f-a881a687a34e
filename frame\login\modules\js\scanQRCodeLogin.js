var scanQRCodeLogin = {
	// 二维码缓存
	QRCodeConfigCache : {},
	/**
	 * 加载扫码
	 * @param moblieCode
	 * @returns
	 */
	loadQRCode : function() {
		
		// 医院编号为空不会获取
		var compNo = $("input[name='compNo']").val();
		if (!compNo) {
			return;
		}
		
		// 配置队列
		var moblieCodeType = [ "MOBLIE_QIYEWENXIN", "MOBLIE_DINGDING", "MOBLIE_WECHAT" ];
		
		var hasConfig = false;
		if (scanQRCodeLogin.QRCodeConfigCache[compNo]) {
			hasConfig = true;
		}
		
		for (var i = 0; i < moblieCodeType.length; i++) {
			var moblieCode = moblieCodeType[i];
			// 初始化
			$("#" + moblieCode).children().removeClass(scanQRCodeLogin.getCodeClass(moblieCode));
			
			// 修改图标样式
			var callback = function(tempCode) {
				scanQRCodeLogin.setCodeClass(compNo, tempCode);
			}
			// 如果有缓存，不再获取
			if (hasConfig) {
				callback(moblieCode);
			} else {
				// moblie 拿到的配置信息
				scanQRCodeLogin.getMoblieConfig(moblieCode, compNo).then(function(data) {
					callback(data.moblieCode);
				});
			}
		}
		
		// 监听
		scanQRCodeLogin.loadMonitor();
	},
	// 获取配置
	getMoblieConfig : function(moblieCode, compNo) {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/moblieSetting/getByCode.spring",
			type : "get", // 提供方接口定义请求是啥，这里就是啥 - 目前只支持 post 和 get
			data : {
				"moblieCode" : moblieCode,
				"compNo" : compNo
			},
			skipDataCheck : true,
			dataType : "json",
			success : function(moblie) {
				if (moblie.result == "success") {
					// 缓存配置
					if (!scanQRCodeLogin.QRCodeConfigCache[compNo]) {
						scanQRCodeLogin.QRCodeConfigCache[compNo] = {};
					}
					if (moblie && moblie.status && moblie.status == 1) {
						scanQRCodeLogin.QRCodeConfigCache[compNo][moblieCode] = moblie;
					} else {
						scanQRCodeLogin.QRCodeConfigCache[compNo][moblieCode] = null;
					}
				}
				moblie.moblieCode = moblieCode;
				return moblie;
			}
		});
	},
	// 修改图标样式
	setCodeClass : function(compNo, moblieCode) {
		var QRCodeConfig = scanQRCodeLogin.QRCodeConfigCache[compNo];
		var moblie = QRCodeConfig[moblieCode];
		// 存在，并且启用
		if (moblie && moblie.status && moblie.status == 1) {
			$("#" + moblieCode).children().addClass(scanQRCodeLogin.getCodeClass(moblieCode));
		}
	},
	/**
	 * 获取激活的图标
	 */
	getCodeClass : function(moblieCode) {
		if (moblieCode == "MOBLIE_DINGDING") {
			return "code1_enable";
		} else if (moblieCode == "MOBLIE_QIYEWENXIN") {
			return "code2_enable";
		} else if (moblieCode == "MOBLIE_WECHAT") {
			return "code3_enable";
		}
	},
	setQRImageStyle : function(id) {
		
		if (id == "MOBLIE_DINGDING") {
			$("#QRImage iframe").css({
				"position" : "fixed",
				"top" : "0",
				"left" : "0",
				"right" : "0",
				"bottom" : "0",
				"margin" : "auto"
			});
			
			$("#QRImage").css("display", "");
			
		} else if (id == "MOBLIE_QIYEWENXIN") {
			$("#QRImage iframe").css({
				"position" : "fixed",
				"top" : "0",
				"left" : "0",
				"right" : "0",
				"bottom" : "0",
				"margin" : "auto",
				"background" : "#fff",
				"padding-top" : "20px"
			});
			
			$("#QRImage").css("display", "");
			
		} else if (id == "MOBLIE_WECHAT") {
			// 开启容器
			$("#QRImageGZHDiv").show();
			$("#QRImageGZH").css({
				"position" : "fixed",
				"top" : "0",
				"left" : "0",
				"right" : "0",
				"bottom" : "0",
				"margin" : "auto",
				"width" : "300px"
			});
		}
	},
	/**
	 * 加载监听器
	 */
	loadMonitor : function() {
		
		// 触发
		$('.QR_left,.QR_delete').unbind();
		$('.QR_left').bind("click", function() {
			
			$('.QR_delete').css({
				marginLeft : '148px',
				marginBottom : '190px'
			});
			
			var compNo = $("input[name='compNo']").val();
			if (!compNo) {
				assemblys.msg("请输入机构编号");
				return;
			}
			
			var QRCodeConfig = scanQRCodeLogin.QRCodeConfigCache[compNo];
			var id = $(this).attr("id");
			if (!QRCodeConfig) {
				assemblys.msg("请检查机构编号是否正确");
				return;
			}
			if (!QRCodeConfig[id]) {
				assemblys.msg("未支持该扫码方式");
				return;
			}
			var config = QRCodeConfig[id];
			if (id == "MOBLIE_DINGDING") {
				ddAndWxUtil.ddQRCodeInit(config, "QRImage");
			} else if (id == "MOBLIE_QIYEWENXIN") {
				ddAndWxUtil.wxQRCodeInit(config, "QRImage");
			} else if (id == "MOBLIE_WECHAT") {
				wxGZHUtil.getQRCode(config, "QRImageGZH", function(data) {
					login.loginCheck({
						"singleDiscernKey" : "wxOpenID",
						"ppwwddValue" : "",
						"singleDiscernValue" : data.wxOpenID,
						"loginModel" : "scanQRCode",
						"compNo" : compNo
					});
				});
			}
			// 设置样式
			scanQRCodeLogin.setQRImageStyle(id);
			// 打开渲染二维码区和遮罩
			$('.QR_window_show,.QR_window').show();
			
		})

		// 点click销毁
		$('.QR_delete').bind('click', function() {
			$("#QRImage").removeAttr("style").empty();
			$("#QRImageGZH").attr("src", "").removeAttr("style");
			// 关闭二维码区、遮罩和公众号容器
			$('.QR_window_show,.QR_window,#QRImageGZHDiv').hide();
		})

	},
	/**
	 * 帮助
	 */
	help : function() {
		var url = basePath + "frame/login/modules/scanQRCodeHelp.jsp";
		layer.open({
			content : url,
			type : 2,
			skin : 'layui-layer-aems',
			title : "扫码说明",
			scrollbar : false,
			area : [ '90%', '90%' ]
		});
		
	},
	init : function() {
		scanQRCodeLogin.loadQRCode();
	}
}
scanQRCodeLogin.init();