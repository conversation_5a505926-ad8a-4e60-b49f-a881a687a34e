<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<%
	String baseURL = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ baseURL + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>扫码帮助</title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css" />
<script type="text/javascript">
	var basePath = "${basePath}";
</script>
</head>
<body>
	<form class="layui-form" onsubmit="return false;">
		<div class="bodys body_noTop">
			<div class="tableDiv table_noTree">
				<div class="layui-colla-item">
					<div class="layui-colla-content layui-show">
						<div style="color: red; float: right;">注：扫码登录需要外网环境支持, 如无法正常加载二维码以及扫码，请更换其他浏览器或联系管理员</div>
						<br>
						<br>
						<blockquote class="layui-elem-quote layui-text">
							<strong style="color: blue;">1. 如何绑定用户：</strong>
							未绑定【钉钉】、【企业微信】、【公众号】时，请通过【用户名】或【手机号】登录到系统后，按照下图操作进行操作。
						</blockquote>
						<div style="margin: 10px; text-align: center;">
							<img src="${basePath}frame/login/modules/images/buzhou1.png" style="width: 80%;">
						</div>
						<hr>
						<blockquote class="layui-elem-quote layui-text">
							<strong style="color: blue;">2. 扫码绑定：</strong>
							以钉钉为例，点击钉钉的图标就可以弹出扫码，请通过对应的APP平台进行扫码。
						</blockquote>
						<div style="margin: 10px; text-align: center;">
							<img src="${basePath}frame/login/modules/images/buzhou2.png" style="width: 80%;">
						</div>
						<hr>
						<blockquote class="layui-elem-quote layui-text">
							<strong style="color: blue;">3. 如何解绑：</strong>
							当图标为点亮时，证明已经绑定过用户，如需解绑，请点击图标按照提示框操作。
						</blockquote>
						<div style="margin: 10px; text-align: center;">
							<img src="${basePath}frame/login/modules/images/buzhou3.png" style="width: 80%;">
						</div>
						<hr>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>

