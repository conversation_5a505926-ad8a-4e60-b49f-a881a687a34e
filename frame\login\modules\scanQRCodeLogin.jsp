<%@page language="Java" contentType="text/html;charset=UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
	request.setAttribute("basePath", basePath);
%>
<div class="QR_code_box">
	<fieldset class="layui-elem-field layui-field-title layui-field-custom-title">
		<legend>
			扫码登录
			<i class="layui-icon2 QR_help" title="点击查看扫码说明" onclick="scanQRCodeLogin.help();">&#xe725; </i>
		</legend>
	</fieldset>
	<div class="QR_code_content">
		<div id="MOBLIE_DINGDING" class="QR_code QR_left">
			<div title="钉钉扫码" class="code1"></div>
		</div>
		<div id="MOBLIE_QIYEWENXIN" class="QR_code QR_left">
			<div title="企业微信扫码" class="code2"></div>
		</div>
		<div id="MOBLIE_WECHAT" class="QR_code QR_left">
			<div title="公众号扫码" class="code3"></div>
		</div>
	</div>
	<div class="QR_window">
		<div class="QR_window_show"></div>
		<div id="QRImage" style="display: none;"></div>
		<div id="QRImageGZHDiv">
			<img id="QRImageGZH">
			<font>请使用"微信"扫描二维码登录"公众号"</font>
		</div>
		<i class="layui-icon layui-icon-close QR_delete"></i>
		<div class="ruler"></div>
	</div>
</div>
<script type="text/javascript" src="${basePath}frame/loginUtil/ddLogin.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/wwLogin-1.0.0.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/ddAndWxUtil.js"></script>
<script type="text/javascript" src="${basePath}frame/loginUtil/wxGZHUtil.js"></script>
<script type="text/javascript" src="${basePath}frame/login/modules/js/scanQRCodeLogin.js"></script>
