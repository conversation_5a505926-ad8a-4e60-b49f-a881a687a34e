<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="/WEB-INF/c.tld" prefix="c"%>
<%
	String path = request.getContextPath();
	String port = ":" + request.getServerPort();
	String serverPort = ((port).equals(":80") || (port).equals(":443")) ? "" : port;
	String basePath = request.getScheme() + "://" + request.getServerName() + serverPort + path + "/";
	request.setAttribute("basePath", basePath);
%>
<!DOCTYPE HTML>
<html>
<head>
<title>授权登录</title>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<script type="text/javascript" src="${basePath}/plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}/plugins/layui/assemblys.js"></script>
<script type="text/javascript">
	var basePath = "${basePath}";
	var wxPath = "https://open.weixin.qq.com/connect/oauth2/authorize";
	var corpID = "${param.corpId}";
	var singleCode = "${param.singleCode}";
	var compNo = "${param.compNo}";
	var appCode = "${param.appCode}";
	var redirectUri = basePath + "frame/excludeUrl/scanCode/mobileLogin.spring?loginKey=wxUserID&singleCode=" + singleCode + "&compNo=" + compNo + "&appCode=" + appCode;
	// 拼接格式
	var url = wxPath + "?appid=" + corpID + "&redirect_uri=" + encodeURIComponent(redirectUri) + "&response_type=code&scope=SCOPE&state=wxUserID#wechat_redirect";
	location.href = url;
</script>
</head>
<body>
	<div>请稍后...</div>
</body>
</html>