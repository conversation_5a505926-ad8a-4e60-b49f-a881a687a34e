var logoConfig = {
	imgCacheList : [],
	init : function() {
		logoConfig.getLogoConfig().then(function() {
			logoConfig.loadLogo();
		});
	},
	getLogoConfig : function() {
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : "LOGO_MANAGE",
				"appCode" : "APP",
				"compNo" : ""
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				var dictList = data.dictList;
				for (var i = 0; i < dictList.length; i++) {
					var dict = dictList[i];
					logoConfig.imgCacheList.push({
						"name" : dict.dictName,
						"uri" : dict.dictContent,
						"width" : dict.value1,
						"height" : dict.value2
					});
				}
			}
		});
		
	},
	loadLogo : function() {
		var html = "";
		var imgCacheList = logoConfig.imgCacheList;
		for (var i = 0; i < imgCacheList.length; i++) {
			var img = imgCacheList[i];
			html += '	<fieldset class="layui-elem-field">';
			html += '	<legend>' + img.name + '</legend>';
			html += '	<div class="layui-field-box">';
			html += '		<blockquote class="layui-elem-quote skin-div-css">';
			html += '			<img src="' + basePath + img.uri + '" />';
			html += '		</blockquote>';
			html += '		<input type="button" class="layui-btn layui-btn-sm h28 lh28" onclick="logoConfig.changeImg(' + i + ')" value="更换">';
			html += '	</div>';
			html += '</fieldset>';
		}
		$(".bodys").html(html);
	},
	changeImg : function(index) {
		var imgCache = logoConfig.imgCacheList[index];
		var url = basePath + "plugins/components/photoClip/photoClip.html?width=" + imgCache.width + "&height=" + imgCache.height;
		url += "&callback=logoConfig.callback";
		layer.open({
			content : url,
			type : 2,
			skin : 'layui-layer-aems',
			title : "LOGO上传",
			scrollbar : false,
			area : [ '90%', '90%' ]
		});
		
		// 回调
		logoConfig["callback"] = function(data) {
			logoConfig.saveLogo(data, imgCache);
		}
	},
	saveLogo : function(data, imgCache) {
		// 防止重复提交
		if (window.hasSubmit) {
			return;
		}
		window.hasSubmit = true;
		$.ajax({
			url : basePath + "frame/fileUpload/saveLogo.spring",
			type : "post",
			async : false,
			data : {
				"imgUri" : data.imgUri,
				"imgPath" : imgCache.uri,
				"width" : imgCache.width,
				"height" : imgCache.height
			},
			dataType : "json",
			success : function(data) {
				if (data.opt == "1") {
					assemblys.msg("更换成功", function() {
						location.reload();
					});
				} else {
					assemblys.alert("更换失败，图片过大，导致请求体参数错误");
					window.hasSubmit = false;
				}
			}
		});
	}
}
logoConfig.init();