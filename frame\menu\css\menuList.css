.sortable-background {
	background-color: #F2F2F2;
}

.one_level_item {
	background-color: #dbf0e6;
	padding: 5px 10px;
	font-weight: bold;
	line-height: 20px;
	cursor: pointer;
}

.two_level_item {
	padding: 5px 15px;
	line-height: 20px;
	cursor: pointer;
}

.one_level_item:hover {
	background-color: #f2f2f2;
}

.two_level_item:hover {
	background-color: #f2f2f2;
}

.level_item_selected {
	background-color: #f2f2f2;
}

#menuList {
	min-height: 310px;
	overflow-y: auto;
	overflow-x: auto;
	width: 100%;
}

.body {
	position: absolute;
	margin: 0px;
	padding: 0px;
	width: 100%;
	height: 100%;
}

.leftMenuMain {
	position: absolute;
	left: 5px;
	top: 5px;
	bottom: 5px;
	width: 380px;
}

.rightMenuMain {
	position: absolute;
	padding-left: 15px;
	border-left: 3px solid #f2f2f2;
	left: 380px;
	right: 5px;
	top: 5px;
	bottom: 5px;
}

.leftMenuMain-div1 {
	position: absolute;
	top: 0px;
}

.leftMenuMain-fieldset {
	position: absolute;
	top: 40px;
	bottom: 150px;
	overflow-y: auto;
	width: 350px;
}

.leftMenuMain-div2 {
	position: absolute;
	bottom: 115px;
}

.foot {
	font-size: 14px;
	padding: 12px;
	position: absolute;
	bottom: 15px;
}