var menuEdit = {
	rpID : null,
	init : function() {
		menuEdit.loadAppList().then(function() {
			return menuEdit.getMenu();
		}).then(function(d) {
			menuEdit.initLayui();
			if (d) {
				param.set(null, d.appLeftMenu);
				param.set(null, d.appMenu);
				param.set("appIDTemp", d.realAppID);
			}
		});
	},
	initLayui : function() {
		var form = layui.form;
		form.render();
		
		// 监听切换应用
		form.on('select(appID)', function(data) {
			if (param.get("menuLevel") == 2) {
				menuEdit.initMenuLists();
			}
			return false;
		});
		
		var menuIconTypeCount = 1;
		// 监听图标风格
		form.on('radio(menuIconType)', function(data) {
			if (menuIconTypeCount > 1) {
				// 清空
				$("#menuIcon").val("").next().removeClass().addClass("layui-icon" + data.value);
			}
			menuIconTypeCount++;
			return false;
		});
		
		form.on('radio(state)', function(data) {
			if (data.value == "1") {
				$("#menuStateView").removeClass("layui-hide");
			} else {
				$("#menuStateView").addClass("layui-hide");
			}
		});
		
		form.on('radio(menuPosDirection)', function(data) {
			var result = data.value;
			if (result == "next") {
				param.menuPos = 1;
			} else {
				param.menuPos = 0;
			}
		});
		
		form.on('radio(menuLevel)', function(data) {
			param.menuLevel = data.value;
			if (param.menuLevel == 1) {
				// 触发二级
				$("select[name='rpID']").empty();
				layui.form.render();
				
				$("#state").children("input[value='2']").next().click();
				$(".oneLevelMenu").addClass("layui-hide");
			} else {
				menuEdit.initMenuLists();
				$(".oneLevelMenu").removeClass("layui-hide");
			}
		});
		
		// 监听提交
		form.on('submit(save)', function(data) {
			
			// 限制二级菜单不能放在首位
			if (param.menuLevel == 2 && param.menuPos == 0) {
				var selectValue = $(".level_item_selected").attr("data-value");
				var index = $("#menuList li[data-value='" + selectValue + "']").index();
				if (index == 0) {
					assemblys.msg("二级菜单不能放在一级菜单之前");
					return;
				}
			}
			
			// 防止重复提交
			if (hasSubmit) {
				return;
			}
			hasSubmit = true;
			
			$.ajax({
				url : basePath + "frame/menu/saveMenu.spring",
				data : param.__form(),
				type : "post",
				dataType : "json",
				skipDataCheck : true,
				success : function(data) {
					if (data.result == "success") {
						assemblys.msg("保存成功", function() {
							menuEdit.refresh();
							// 防止重复提交
							hasSubmit = false;
						});
					} else {
						assemblys.alert("服务运行出错，保存失败");
						
					}
				}
			});
			return false;
		});
	},
	// 返回上一层
	refresh : function() {
		
		// 获取列表选中的菜单
		var index = parent.$("#menuList").find("li.level_item_selected").index();
		// 重新加载
		parent.initMenuLists().then(function() {
			var menuID = param.get("menuID");
			// 新增
			if (!menuID || menuID == 0) {
				// 按指定菜单插入
				if (index != -1) {
					// 选中的菜单
					var $selectMenu = parent.$("#menuList").find("li:eq(" + index + ")");
					// 在选中地方插入
					$selectMenu.after(parent.$("#menuList li:last"));
				} else {
					// 定位
					parent.$(".leftMenuMain-fieldset").animate({
						scrollTop : parent.$("#menuList li:last").offset().top
					});
				}
				// 更新顺序
				parent.menuList.updateMenuSeqNo();
			}
			closeEdit();
		});
	},
	loadAppList : function(d) {
		var dom = "#appID";
		return $.ajax({
			url : basePath + "frame/comp/getCompAppRight.spring",
			data : {
				"menuRight" : 0
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				var appID = param.get("appID");
				// 加载下拉列表
				menuEdit.initSelectInput(dom, data.appList, appID, false, true);
				return d;
			}
		});
		
	},
	initMenuLists : function() {
		var appID = param.get("appIDTemp");
		$.ajax({
			url : basePath + "frame/menu/newMenu.spring",
			dataType : "json",
			data : {
				"appID" : appID
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					menuEdit.initSelectInput("#rpID", data.list, menuEdit.rpID, false, true);
					layui.form.render();
				} else {
					assemblys.alert("服务运行出错，获取菜单失败");
				}
			}
		});
	},
	getMenu : function() {
		var menuID = param.get("menuID");
		if (menuID) {
			return $.ajax({
				url : basePath + "frame/menu/getMenu.spring",
				dataType : "json",
				data : {
					"menuID" : param.get("menuID")
				},
				skipDataCheck : true,
				success : function(data) {
					if (data.result == "success") {
						menuEdit.rpID = data.appMenu.rpID;
						// 加载菜单
						if (data.appMenu.menuLevel == 2) {
							menuEdit.initMenuLists();
						}
						return data;
					} else {
						assemblys.alert("服务运行出错，获取菜单失败");
					}
				}
			});
		} else {
			return $.Deferred().resolve();
		}
	},
	initSelectInput : function(dom, list, selectedVal, hasAll, hasSearch, option) {
		var data = new Array();
		for (var i = 0; i < list.length; i++) {
			var json = {};
			json.name = list[i].name;
			json.value = list[i].value;
			data.push(json);
		}
		
		var select = "";
		if (hasSearch && hasSearch == true) {
			$(dom).attr("lay-search", "");
		}
		
		if (hasAll && hasAll == true) {
			if (option) {
				select += option;
			} else {
				select += "<option value='' selected >全部</option>";
			}
		}
		
		var defaultSelected = "";
		
		$.each(data, function(i, e) {
			if (data[i].selected && data[i].selected == true) {
				select += "<option value='" + data[i].value + "' selected >" + data[i].name + "</option>";
				selectedVal = data[i].value;
			} else {
				if (selectedVal && selectedVal == data[i].value) {
					select += "<option value='" + data[i].value + "' selected >" + data[i].name + "</option>";
				} else {
					select += "<option value='" + data[i].value + "'>" + data[i].name + "</option>";
					if (i == 0) {
						defaultSelected = data[i].value;
					}
					
				}
			}
		});
		$(dom).html(select);
		
		if (!hasAll && hasAll != true && selectedVal == "") {
			selectedVal = defaultSelected;
		}
		
		return selectedVal;
	}
}

function closeEdit() {
	parent.changeMenuEdit();
}

/**
 * 选择菜单图标
 * 
 * @returns
 */
function toSelectIcon() {
	// 图标
	var map = {
		"2" : "selectIcon.html",
		"3" : "selectIcon2.html"
	}
	var menuIconType = $("input[name='menuIconType']:checked").val();
	layui.use('layer', function() {
		var layer = layui.layer;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			closeBtn : 0,
			area : [ '100%', '100%' ],
			title : false,
			scrollbar : false,
			content : basePath + "frame/leftMenu/" + map[menuIconType]
		});
	});
}
