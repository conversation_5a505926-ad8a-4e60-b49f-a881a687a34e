var param = {
	appID : "",
	appName : "",
	dataValue : "0",
	dataName : ""
}

var hasSubmit = false;

$(function() {
	
	assemblys.getMenuIcon({
		funCode : assemblys.getParam("funCode"),
		hasOrg : false,
		dom : $("b#menuIcon")
	});
	
	// 计算
	computeSize();
	
	// 加载应用列表
	loadAppList().then(function() {
		return initMenuLists();
	});
	
});

var menuList = {
	updateMenuSeqNo : function() {
		var menuIDs = [];
		$("#menuList").find("ul").find("li").each(function(i, e) {
			menuIDs.push($(e).data("value"));
		})
		return $.ajax({
			url : basePath + "frame/menu/updateMenuSeqNo.spring",
			data : {
				menuID : menuIDs
			},
			type : "post",
			traditional : true
		})
	},
}

function computeSize() {
	// 计算编辑的iframe高度
	$("#editMenu").attr("height", "100%;");
	
}

// 加载应用系统
function loadAppList() {
	var dom = "#appList";
	return $.ajax({
		url : basePath + "frame/comp/getCompAppRight.spring",
		data : {
			"menuRight" : 0
		},
		dataType : "json",
		success : function(data) {
			if (data.result == "success") {
				// 加载下拉框
				param.appID = initSelectInput(dom, data.appList, param.appID, false, true);
				param.appName = $("#appList option[value='" + param.appID + "']").text();
				
			} else {
				assemblys.alert("服务运行出错，获取菜单失败");
			}
		}
	});
}

// 加载菜单
function initMenuLists() {
	return $.ajax({
		url : basePath + "frame/menu/getMenuByAppID.spring",
		dataType : "json",
		data : {
			"appID" : param.appID
		},
		success : function(data) {
			if (data.result == "success") {
				initMenus(data.list);
			} else {
				assemblys.alert("服务运行出错，获取菜单失败");
			}
		}
	}).then(function() {
		Sortable.create(document.getElementById("menuList").childNodes[0], {
			group : 'col', // set both lists to same group
			animation : 150,
			ghostClass : 'sortable-background',
			onMove : function(evt, originalEvent) {
				var fromIndex = $(evt.dragged).index();
				var toIndex = $(evt.related).index();
				return fromIndex != 0 && (toIndex != 0 || $(evt.dragged).hasClass("one_level_item"));
			},
			onEnd : function(customEvent) {
				menuList.updateMenuSeqNo();
			},
		});
	});
}

// 处理菜单数据
function initMenus(list) {
	
	var dom = "#menuList";
	if (list.length == 0) {
		$(dom).html("<p style=\"margin:10px 0px; color: red\">当前应用暂无菜单</p>");
		return false;
	}
	
	var data = new Array();
	for (var i = 0; i < list.length; i++) {
		var json = {};
		json.name = assemblys.escape(list[i].MenuName);
		json.value = list[i].MenuID;
		json.level = list[i].MenuLevel;
		json.icon = list[i].MenuIcon;
		json.iconType = list[i].MenuIconType;
		json.mobile = list[i].isMobileShow;
		json.timer = list[i].state;
		data.push(json);
	}
	var select = "<ul>";
	$.each(data, function(i, e) {
		// 类型
		var iconType = e.icon == "" ? 2 : e.iconType;
		if (e.level == 1) {
			select += "<li class='one_level_item skin-btn-minor'  data-value='" + e.value + "'><i class='layui-icon" + iconType + "'>" + (e.icon || "&#xe779;") + "</i> <span class='menu-name'>" + data[i].name + "</span></li>";
		} else {
			var menuID = e.value;
			var isMobileShow = e.mobile == 1 ? "&#xe77d;" : "&#xe77c;";
			var title = e.mobile == 1 ? "取消移动端显示" : "设置移动端显示";
			var opt = e.mobile == 1 ? "0" : "1";
			var timer = e.timer == 1 ? "<i class='layui-icon2' title='该菜单开启了监听器' style='float:right;font-size: 18px;'>&#xe737;</i>" : "";
			// 暂时屏蔽手机图标
			// select += "<li class='two_level_item'  data-value='" + data[i].value + "'> <i class='layui-icon2'>" + (data[i].icon || "&#xe779;") + "</i> " + data[i].name + "<i class='layui-icon2'  onclick='setMobileShow(" + menuID + "," + opt + ");layui.stope(event);'   title='" + title + "' style='float: right;font-size: 18px;' >" + isMobileShow + "</i>" + timer + "</li>";
			// 闹钟样式
			// select += "<li class='two_level_item'  data-value='" + data[i].value + "'> <i class='layui-icon2'>" + (data[i].icon || "&#xe779;") + "</i> " + data[i].name + timer + "</li>";
			// 图标显示
			var moblieShow = e.mobile == 1 ? "<i class='layui-icon2'   title='已开启移动端显示'  style='float: right;font-size: 18px;' >&#xe77d;</i>" : "";
			select += "<li class='two_level_item'  data-value='" + e.value + "'> <i class='layui-icon" + iconType + "'>" + (e.icon || "&#xe779;") + "</i> <span class='menu-name'>" + e.name + "</span>" + moblieShow + timer + "</li>";
		}
	});
	
	select += "</ul>";
	$(dom).html(select);
	
	$(dom + " li").on("click", function(i, e) {
		
		// 删除原有的
		$(dom + " li").each(function() {
			param.dataValue = "0";
			param.dataName = "";
			$(this).removeClass("level_item_selected");
		});
		
		// 选中当前
		$(this).addClass("level_item_selected");
		param.dataValue = $(this).attr("data-value");
		param.dataName = $(this).find("span.menu-name").text();
		toEdit();
		
	})
}

layui.use([ 'form' ], function() {
	var form = layui.form, layer = layui.layer;
	// 监听切换应用
	form.on('select(appList)', function(data) {
		param.appID = data.value;
		param.appName = $("#appList option[value='" + data.value + "']").text();
		
		initMenuLists();
		return false;
	});
	
});

function toAdd() {
	var url = basePath + "frame/menu/menuEdit.html?appName=&appID=" + param.appID + "&seletedPos=" + param.dataValue;
	changeMenuEdit(url);
}

function toEdit() {
	if (param.dataValue == "0") {
		assemblys.msg("请选择需要编辑菜单");
		return false;
	}
	var url = basePath + "frame/menu/menuEdit.html?menuID=" + param.dataValue + "&appID=" + param.appID + "&seletedPos=" + param.dataValue;
	changeMenuEdit(url);
}

function changeMenuEdit(url) {
	if (url) {
		$(".rightMenuMain").css("display", "");
		$("#editMenu").attr("src", url);
		
	} else {
		$(".rightMenuMain").css("display", "none");
		$("#editMenu").attr("src", "");
		
	}
}

function setMobileShow(menuID, opt) {
	if (hasSubmit) {
		return;
	}
	hasSubmit = true;
	
	$.ajax({
		url : basePath + "frame/menu/updateMobileShow.spring",
		dataType : "json",
		data : {
			"menuID" : menuID,
			"opt" : opt
		},
		success : function(data) {
			if (data.result == "success") {
				hasSubmit = false;
				assemblys.msg(opt == 1 ? "设置移动端显示成功" : "取消移动端显示成功");
				// 刷新菜单
				initMenuLists();
			} else {
				assemblys.alert("服务器异常，请联系管理员");
			}
		}
	});
}

function toDel() {
	
	if (hasSubmit) {
		return;
	}
	
	if (param.dataValue == "0") {
		assemblys.msg("请选择菜单");
		return false;
	}
	
	hasSubmit = true;
	layer.confirm('确定删除「' + param.dataName + "」菜单吗?", function(index) {
		$.ajax({
			url : basePath + "frame/menu/deleteMenu.spring",
			dataType : "json",
			data : {
				"menuID" : param.dataValue
			},
			success : function(data) {
				if (data.result == "success") {
					
					hasSubmit = false;
					
					assemblys.msg("删除成功");
					
					// 重置
					param.dataValue = "0";
					
					// 刷新菜单
					initMenuLists();
					
					// 清空右侧
					changeMenuEdit();
					
				} else {
					assemblys.alert("服务器异常，请联系管理员");
				}
			}
		});
	}, function() {
		hasSubmit = false;
	});
}

/**
 * 组件--初始化下拉框
 */
function initSelectInput(dom, list, selectedVal, hasAll, hasSearch, option) {
	var data = new Array();
	for (var i = 0; i < list.length; i++) {
		var json = {};
		json.name = list[i].name;
		json.value = list[i].value;
		data.push(json);
	}
	var select = "";
	if (hasSearch && hasSearch == true) {
		$(dom).attr("lay-search", "");
	}
	if (hasAll && hasAll == true) {
		if (option) {
			select += option;
		} else {
			select += "<option value='' selected >全部</option>";
		}
	}
	var defaultSelected = "";
	$.each(data, function(i, e) {
		if (data[i].selected && data[i].selected == true) {
			select += "<option value='" + data[i].value + "' selected >" + data[i].name + "</option>";
			selectedVal = data[i].value;
		} else {
			if (selectedVal && selectedVal == data[i].value) {
				select += "<option value='" + data[i].value + "' selected >" + data[i].name + "</option>";
			} else {
				select += "<option value='" + data[i].value + "'>" + data[i].name + "</option>";
				if (i == 0) {
					defaultSelected = data[i].value;
				}
				
			}
		}
	});
	$(dom).html(select);
	// 加载插件
	layui.use([ 'form' ], function() {
		var form = layui.form;
		form.render('select');
	});
	if (!hasAll && hasAll != true && selectedVal == "") {
		selectedVal = defaultSelected;
	}
	return selectedVal;
}
