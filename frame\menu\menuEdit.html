<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="pragma" content="no-cache" />
<meta http-equiv="content-type" content="no-cache, must-revalidate" />
<meta http-equiv="expires" content="0" />
<title></title>
<link rel="stylesheet" href="../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../plugins/static/css/edit.css" />
<script language="javascript">
	var hasSubmit = false;
</script>
<style>
body {
	font-style: normal;
	color: #484848;
	font: 14px Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1, Tahoma, Arial, sans-serif;
}

.one_level_item {
	background-color: #dbf0e6;
	padding: 5px 10px;
	font-weight: bold;
	line-height: 20px;
	cursor: pointer;
}

.two_level_item {
	padding: 5px 15px;
	line-height: 20px;
	cursor: pointer;
}

.one_level_item:hover {
	background-color: #f2f2f2;
}

.two_level_item:hover {
	background-color: #f2f2f2;
}

.level_item_selected {
	background-color: #f2f2f2;
}

.layui-input {
	height: 28px;
	line-height: 28px;
}

.menuBottom {
	position: fixed;
	bottom: 0px;
	background: #ffffff;
	left: 0;
	right: 0;
	height: 45px;
	line-height: 40px;
	margin: 0px;
}

.layui-form-item .layui-input-inline {
	padding-top: 5px;
}
</style>
</head>
<body>
	<div class="bodys bodys_noTop " style="min-width: 850px;">
		<form class="layui-form" lay-filter="param" onsubmit="return false;" style="min-width: 640px;">
			<!-- 上个页面的参数，用param属性标识 -->
			<input type="hidden" name="menuID" value="" />
			<input type="hidden" name="appID" value="" />
			<input type="hidden" param="seletedPos" value="" />
			<!-- 本页面的参数 -->
			<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
				<legend id="htmlTitle" style="font-weight: 400;"></legend>
			</fieldset>
			<div class="layui-form-item">
				<label class="layui-form-label">应用名称</label>
				<div class="layui-input-inline">
					<select id="appID" name="appIDTemp" lay-filter="appID"></select>
				</div>
				<label class="layui-form-label">菜单名称</label>
				<div class="layui-input-inline">
					<input type="text" id="menuName" name="menuName" maxlength="30" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">菜单级别</label>
				<div class="layui-input-inline">
					<div class="layui-input-block" style="margin-left: 0px; width: 300px;" id="menuLevel">
						<input type="radio" name="menuLevel" value="1" title="第一级" lay-filter="menuLevel" checked />
						<input type="radio" name="menuLevel" value="2" title="第二级" lay-filter="menuLevel" />
					</div>
				</div>
				<label class="layui-form-label">菜单功能</label>
				<div class="layui-input-inline">
					<select id="rpID" name="rpID" lay-filter="rpID">
					</select>
				</div>
			</div>
			<div class="layui-form-item oneLevelMenu layui-hide">
				<label class="layui-form-label">附加参数</label>
				<div class="layui-input-block layui-input-text" style="padding-top: 5px;">
					<input type="text" name="menuURLParam" placeholder="URL附加参数，参考格式：key1=value1&key2=value2" autocomplete="off" maxlength="200" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">图标风格</label>
				<div class="layui-input-inline">
					<input type="radio" name="menuIconType" value="2" title="通用型" lay-filter="menuIconType" checked="checked">
					<input type="radio" name="menuIconType" value="3" title="医疗风" lay-filter="menuIconType">
				</div>
				<label class="layui-form-label">菜单图标</label>
				<div class="layui-input-inline">
					<input type="text" name="menuIcon" value="" style="cursor: pointer; width: 230px; display: inline;" placeholder="请选择" onclick="toSelectIcon()" id="menuIcon" readonly="readonly" class="layui-input" />
					&nbsp;
					<i></i>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="oneLevelMenu layui-hide">
					<label class="layui-form-label">监听器开关</label>
					<div class="layui-input-inline">
						<div class="layui-input-block" style="margin-left: 0px; width: 240px;" id="state">
							<input type="radio" name="state" value="1" title="开启" lay-filter="state" />
							<input type="radio" name="state" value="2" title="关闭" lay-filter="state" checked />
						</div>
					</div>
				</div>
				<div class="oneLevelMenu layui-hide">
					<label class="layui-form-label">移动端显示</label>
					<div class="layui-input-inline">
						<input type="checkbox" id="isMobileShow" name="isMobileShow" value="1" lay-text="是|否" lay-skin="switch">
					</div>
				</div>
			</div>
			<div class="layui-form-item layui-hide" id="menuStateView">
				<label class="layui-form-label">显示类型</label>
				<div class="layui-input-inline">
					<div class="layui-input-block" style="margin-left: 0px; width: 300px;" id="displayType">
						<input type="radio" name="displayType" value="1" title="红点" lay-filter="displayType" checked />
						<input type="radio" name="displayType" value="2" title="数字" lay-filter="displayType" />
					</div>
				</div>
				<label class="layui-form-label">监听地址</label>
				<div class="layui-input-inline">
					<input type="text" name="timerUrl" value="" placeholder="" id="timerUrl" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">指向站点</label>
				<div class="layui-input-block layui-input-text" style="padding-top: 5px;">
					<input type="text" name="menuServer" placeholder="请输入" autocomplete="off" maxlength="50" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
					<input type="button" class="layui-btn layui-bg-black layui-btn-sm" value="取消" onclick="closeEdit();">
				</div>
			</div>
		</form>
	</div>
</body>
</html>
<script type="text/javascript" src="../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="js/menuEdit.js?ver=5.1"></script>
<script>
	$(function() {
		menuEdit.init();
	});
</script>