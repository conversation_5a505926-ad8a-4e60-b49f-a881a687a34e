<%@page language="Java" contentType="text/html;charset=UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	request.setAttribute("basePath", basePath);
%>
<%@ page import="org.hyena.frame.constant.BaseConstant"%>
<jsp:include page="../page/menuIcon.jsp"><jsp:param value="<%=BaseConstant.FUN_CODE_LIMIT_APP_MENU%>" name="funCode" /></jsp:include>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="pragma" content="no-cache" />
<meta http-equiv="content-type" content="no-cache, must-revalidate" />
<meta http-equiv="expires" content="0" />
<title></title>
<link rel="stylesheet" type="text/css" href="${basePath}plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="${basePath}/plugins/static/css/search.css" />
<link rel="stylesheet" type="text/css" href="css/menuList.css" />
<script type="text/javascript" src="${basePath}plugins/JQuery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="${basePath}plugins/layui/assemblys.js"></script>
<script language="javascript">
	var basePath = "${basePath}";
</script>
</head>
<body class="body">
	<div class="head0">
		<span class="head1_text fw700">
			<b id="menuIcon"></b>
		</span>
		<span style="float: right; color: red;" onmouseover="assemblys.tips(this, '当【功能点URL】为非http请求时，才会校验后面两环，拥有http请求的URL优先级是最高的', 0 ,'left');">
			<strong> 优先级：</strong>
			功能点URL → 菜单指向站点 → 应用指向站点
		</span>
	</div>
	<div class="bodys">
		<form class="layui-form">
			<div class="leftMenuMain">
				<div class="tableDiv table_noTree table_noSearch">
					<div class="layui-inline h28 lh28 leftMenuMain-div1">
						<label class="layui-form-label">应用系统：</label>
						<div class="layui-input-inline" style="width: 200px;">
							<select id="appList" name="appList" lay-filter="appList" class="e_type h31 fw700">
							</select>
						</div>
					</div>
					<fieldset class="layui-elem-field leftMenuMain-fieldset">
						<legend style="font-size: 15px;"> 菜单列表 </legend>
						<div class="layui-field-box">
							<div id="menuList" name="menuList"></div>
						</div>
					</fieldset>
					<div class="layui-input-block leftMenuMain-div2" style="margin-left: 100px;">
						<input type="button" class="layui-btn btn-default btn-add layui-btn-sm" value="新增" onclick="toAdd();">
						<input type="button" id="delButton" class="layui-btn layui-btn-danger layui-btn-sm" value="删除" onclick="toDel();">
					</div>
					<div class="foot">
						<div style="color: red;">
							<strong>1.</strong>
							长按鼠标可拖动【菜单】排序
						</div>
						<div style="color: red;">
							<strong>2.</strong>
							新增的【菜单】默认会追加到【最底部】
						</div>
						<div style="color: red;">
							<strong>3.</strong>
							先【选中菜单】后再【新增】，【新增的菜单】会根据【选中菜单】的下方自动插入
						</div>
					</div>
				</div>
			</div>
		</form>
		<div class="rightMenuMain" style="display: none">
			<iframe id="editMenu" src="" width="100%" frameborder="0"></iframe>
		</div>
	</div>
</body>
</html>
<script type="text/javascript" src="../../plugins/Sortable/Sortable.min.js"></script>
<script type="text/javascript" src="../../plugins/Sortable/Sortable.Swap.js"></script>
<script type="text/javascript" src="${basePath}frame/menu/js/menuList.js?ver=4.0"></script>
