<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>协助</title>
<link rel="stylesheet" type="text/css" href="../../../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/defaultApproval.css?version=*******" />
</head>
<body>
	<div app="approvalAssist">
		<div class="custom-field-parent">
			<van-cell title="协助" :style="{ fontSize : '20px'}"></van-cell>
		</div>
		
		<field-multiple :params="params" title="协助人" name="assistUID" v-model:value="assistUID" v-model:value-name="assistUserName" verify="required"></field-multiple>
		
		<field-textarea title="协助原因" name="assistContent" v-model:value="assistContent" verify="required|limit" limit="200"></field-textarea>
		
		<van-button block type="primary" size="normal" block @click="saveAssistingPeople">提交</van-button>
	</div>
</body>
<script type="text/javascript" src="../../../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/approvalAssist.js?version=*******"></script>
</html>
