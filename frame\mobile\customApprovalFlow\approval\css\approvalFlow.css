@charset "utf-8";

.van-step--vertical .van-step__line {
	top: 23px;
	left: -15px;
	width: 1px;
	height: 100%;
}

.van-steps {
	background-color: inherit;
}

.van-steps--vertical {
	padding: 0 var(--van-padding-xl) 12px var(--van-padding-xl);
}

.van-step--vertical {
	padding: 0px 0px 1px 0px;
	background-color: var(--van-steps-background-color);
}

.flow-margin-placeholder {
	width: 100%;
	height: 10px;
	background-color: #F7F8FA;
}

.flow-current-node-div {
	padding: 0px 5px 0px 5px;
}

.flow-current-approver-div {
	max-height: 250px;
	overflow: auto;
	text-align: left;
	color: #666;
}

.layui-elem-quote {
	padding: 5px;
	border-left: 5px solid #D5EAFF;
	line-height: 22px;
	border-radius: 0 2px 2px 0;
	background-color: #f2f2f2;
	margin: 0;
}

ul.layui-elem-quote li {
	margin-bottom: 10px;
	list-style: outside;
	margin-left: 20px;
}

ul.layui-elem-quote li div:first-child {
	display: inline;
}

ul.layui-elem-quote li::marker {
	content: counter(list-item) "、";
}

.flow-rollback:before {
	content: '回退';
	position: absolute;
	top: 30px;
	left: -20px;
	font-size: 12px;
	color: red;
	width: 15px;
	z-index: 0;
}

div.flow-loop-div {
	position: absolute;
	height: 100%;
	width: 1px;
	background-color: #BEBEBE;
	right: -20px;
	top: 20px;
	color: #BEBEBE;
}

div.flow-loop-div-end {
	width: 0px !important;
}

div.flow-loop-div-begin:before {
	position: absolute;
	content: '←';
	right: 0px;
	top: -10px;
	font-size: 20px;
}

div.flow-loop-div-begin:after {
	position: absolute;
	content: '循环';
	right: -5px;
	top: 5px;
	font-size: 12px;
	z-index: 0;
	background-color: #F7F8FA;
}

div.flow-loop-div-end:before {
	position: absolute;
	content: '—';
	right: 0px;
	top: -10px;
	font-size: 20px;
}

.flow-btn-container {
	position: fixed;
	bottom: 20px;
	right: 20px;
	width: 42px;
	height: 42px;
	background: var(--van-primary-color);
	border-radius: 50%;
	color: white;
	font-size: 33px;
	padding-left: 8px;
	padding-top: 3px;
	padding-bottom: 5px;
}

.van-step--vertical .van-step__circle-container {
	z-index: 0;
}

.layui-elem-quote {
	padding: 10px;
}