.approval-field {
	color: var(--van-primary-color);
	font-weight: 700;
	padding: 2px 12px;
}

.approval-field-inlink {
	color: var(--van-primary-color);
	font-weight: 700;
}

.van-col {
	margin: 0px 3px;
	margin-bottom: 5px;
}

.van-col--6 {
    flex: 0 0 23%;
    max-width: 23%;
}

.van-col--8 {
	flex: 0 0 31%;
	max-width: 31%;
}

.van-col--12 {
	flex: 0 0 48%;
	max-width: 48%;
}

.van-col--24 {
	flex: 0 0 99%;
	max-width: 99%;
}

.approval-handy-cell {
	padding: 5px 0;
}

.approval-handy-cell-title .van-cell--large{
	padding: 5px 0;
}

div.custom-field-parent{
	margin: 5px 0px;
	box-shadow: 0px 0px 3px #a0a0a0;
}

div.custom-field-parent:first-child{
	margin: 0;
}

.field-textarea{
	padding: 5px;
}