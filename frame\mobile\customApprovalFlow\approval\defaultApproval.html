<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>填写意见</title>
<link rel="stylesheet" type="text/css" href="../../../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/defaultApproval.css?version=1.0.1.0" />
</head>
<body class="vue-hide">
	<div app="defaultApproval">
		<div class="custom-field-parent">
			<van-cell title="审批" :style="{ fontSize : '20px'}"></van-cell>
		</div>
		<div class="custom-field-parent">
			<van-cell class="approval-field">
				<template #title>
					<span style="color: red;">*</span>
					审批意见
				</template>
			</van-cell>
			<van-cell class="approval-handy-cell" title-class="approval-handy-cell-title">
				<template #title>
					<textarea  v-model="commentContent" id="commentContents"   style="display: none;"></textarea>
				</template>
			</van-cell>
		</div>
		<div class="custom-field-parent">
			<van-cell title="上传附件" class="approval-field"></van-cell>
			<van-cell>
				<template #title>
					<uploader ref="uploader" :attas="formData" :attachments="attachments"></uploader>
				</template>
			</van-cell>
		</div>
		
		<field-multiple v-show="hasDynamic" :params="selectApproverParam" title="指定审批人" name="approvalUIDs" :verify="verifyApprovalUIDs" ref="approvalUIDs" style="position:absolute;top:-99999999px;"></field-multiple>
		
		<field-select v-show="hasStateStatusNo" :actions="customFormTypeStateList" title="表单状态流转" name="stateStatusNo" v-model:value="formData.stateStatusNo"></field-select>
		
		<field-multiple :params="selectCopyUserParam" title="抄送" name="copyUserCodes"></field-multiple>
		
		<van-row justify="space-between">
			<van-col :span="cols"><van-button type="primary" size="normal" block @click="save(2)">保存草稿</van-button></van-col>
			<van-col :span="cols"><van-button type="primary" size="normal" block @click="getNextApprovalBelongFlowNode(0)">提交</van-button></van-col>
			<van-col v-if="inLoop" :span="cols"><van-button plain type="primary" size="normal" block @click="saveLoop()">结束循环</van-button></van-col>
			<van-col v-if="hasCountersign" :span="cols"><van-button plain type="primary" size="normal" block @click="getNextApprovalBelongFlowNode(1)">结束会签</van-button></van-col>
		</van-row>
		<van-row v-if="hasFinish" justify="space-between">
			<van-col span="24"><van-button type="primary" size="normal" block @click="saveFinish();">一键结束</van-button></van-col>
		</van-row>
	</div>
</body>
<script type="text/javascript" src="../../../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/components/uploader.js"></script>
<script type="text/javascript" src="../../../../../plugins/vant/plugins/handyEditor/HandyEditor.min.js"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/defaultApproval.js?version=*******"></script>
</html>
