page.approvalAssist.option = {
	created : function() {
		var that = this;
		that.approvalAssistInit();
	},
	data : function() {
		var that = this;
		return {
			params : Vue.ref({
				URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
				param : {
					funCode : that.param.funCode,
					rightPoint : 1,
				},
				field : {
					name : "userName",
					value : "uID"
				},
				filterValue : [],
				parseData : function(data) {
					return data.users;
				},
			}),
			assistUID : Vue.ref([]),
			assistUserName : Vue.ref(""),
			assistContent : Vue.ref("")
		};
	},
	methods : {
		approvalAssistInit : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/approvalFlowRecord/approvalAssistInit.spring",
				dataType : "json",
				data : that.param,
			}).then(function(data) {
				for (var i = 0; i < data.approverList.length; i++) {
					that.params.filterValue.push(data.approverList[i].UID);
				}
			});
		},
		saveAssistingPeople : function() {
			var that = this;
			if (!that.form.__verify()) {
				return;
			}
			
			var formJson = that.form.__json();
			formJson.assistUID = formJson.assistUID.join(",");
			formJson.assistUserName = that.assistUserName;
			for ( var key in that.param) {
				formJson[key] = that.param[key];
			}
			
			if (isSubmit) {
				return;
			}
			isSubmit = true;
			
			return ajax({
				url : basePath + "frame/approvalFlowRecord/saveAssistingPeople.spring",
				data : formJson,
				type : "post",
			}).then(function(data) {
				assemblys.msg("保存成功", function() {
					history.back();
				});
			});
		},
	}
}