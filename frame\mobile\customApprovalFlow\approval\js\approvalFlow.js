components["approval-flow"] = {
	template : (function() {
		var html = '';
		html += '';
		html += '<van-steps direction="vertical" :active="currentIndex" finish-icon="checked" active-icon="clock">';
		html += '	<van-step v-for="(approvalBelongFlowNode,index) in approvalBelongFlowNodeList" :class="approvalBelongFlowNode.current == 2 ? \'flow-rollback\' : \'\'" :ref="dataHandle(approvalBelongFlowNode,index)">';
		html += '		<div class="flow-margin-placeholder"></div>';
		html += '		<van-cell :title-style="getApprovalBelongFlowNodeNameColor(approvalBelongFlowNode)" :title="approvalBelongFlowNode.approvalBelongFlowNodeName" :value="approvalFlowNodeTypeMap[approvalBelongFlowNode.approvalFlowNodeType] + \'节点\'" :label="getApprovedRecord(approvalBelongFlowNode)"></van-cell>';
		html += '		<van-cell v-if="approvalBelongFlowNode.current == 1 && approvalBelongFlowNode.approvalIndex > 0" title="审批人员" is-link @click="approvalBelongFlowNode.showBlockquote = !approvalBelongFlowNode.showBlockquote"></van-cell>';
		html += '		<div class="flow-current-approver-div" v-if="approvalBelongFlowNode.current == 1 && approvalBelongFlowNode.approvalIndex > 0">';
		html += '			<transition name="van-fade">';
		html += '				<blockquote class="layui-elem-quote" v-show="!approvalBelongFlowNode.showBlockquote">';
		html += '					<div v-for="approver in approverList" style="color: black;" v-html="approver.deptName + \' - \' + approver.userName"></div>';
		html += '				</blockquote>';
		html += '			</transition>';
		html += '		</div>';
		html += '		<van-cell v-if="logMap[approvalBelongFlowNode.approvalIndex]" title="操作日志" is-link @click="approvalBelongFlowNode.showLog = !approvalBelongFlowNode.showLog"></van-cell>';
		html += '		<div class="flow-current-approver-div" v-if="logMap[approvalBelongFlowNode.approvalIndex]">';
		html += '			<transition name="van-fade">';
		html += '				<ul class="layui-elem-quote" v-show="approvalBelongFlowNode.showLog">';
		html += '					<li v-for="(log,index) in logMap[approvalBelongFlowNode.approvalIndex]">';
		html += '						<div>{{ log.logContent }}</div>';
		html += '						<div style="text-align: right;">{{ dateToStr(log.createDate) }}</div>';
		html += '					</li>';
		html += '				</ul>';
		html += '			</transition>';
		html += '		</div>';
		html += '		<div v-if="approvalBelongFlowNode.approvalIndex >= beginIndex && approvalBelongFlowNode.approvalIndex <= endIndex" :class="getLoopNodeClass(approvalBelongFlowNode)" ></div>';
		html += '	</van-step>';
		html += '</van-steps>';
		html += '<div class="flow-btn-container" v-if="actions.length > 0" @click="showPopover = true">审';
		html += '	<van-popover v-model:show="showPopover" :actions="actions" placement="top-end" @select="onSelect" :offset="offset"></van-popover>';
		html += '</div>';
		return html;
	})(),
	created : function() {
		var that = this;
		if (that.params && that.params.appCode && that.params.approvalBelongCode && that.params.funCode) {
			that.getApprovalBelongFlowNodeList();
		}
	},
	data : function() {
		return {
			approvalBelongFlowNodeList : Vue.ref([]),
			currentApprovalBelongFlowNode : Vue.ref({}),
			canApproval : Vue.ref(false),
			canAssist : Vue.ref(false),
			hasExecRight : Vue.ref(false),
			currentIndex : Vue.ref(0),
			beginIndex : Vue.ref(-1),
			endIndex : Vue.ref(-1),
			showPopover : Vue.ref(false),
			approverList : Vue.ref([]),
			actions : [],
			offset : [ 20, 80 ],
			hasFirst : false
		};
	},
	methods : {
		getApprovalBelongFlowNodeList : function() {
			var that = this;
			
			if (that.hasFirst) {
				return;
			}
			
			that.hasFirst = true;
			var appCode = that.params.appCode;
			var approvalBelongCode = that.params.approvalBelongCode;
			var funCode = that.params.funCode;
			return ajax({
				url : basePath + "/frame/approvalFlow/getApprovalBelongFlowNodeList.spring",
				data : {
					approvalBelongCode : approvalBelongCode,
					appCode : appCode,
					funCode : funCode,
				}
			}).then(function(data) {
				that.currentApprovalBelongFlowNode = data.currentApprovalBelongFlowNode;
				if (that.currentApprovalBelongFlowNode) {
					var currentApprovalBelongFlowNodeCode = that.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode;
					return ajax({
						url : basePath + "/frame/approvalFlow/initFlow.spring",
						data : {
							approvalBelongCode : approvalBelongCode,
							approvalBelongFlowNodeCode : currentApprovalBelongFlowNodeCode,
							appCode : appCode,
							funCode : funCode,
						}
					}).then(function(d) {
						for ( var key in data) {
							d[key] = data[key];
						}
						return d;
					});
				} else {
					return data;
				}
			}).then(function(data) {
				var approverList = data.approverList;
				var assistingPeopleList = data.assistingPeopleList;
				if (assistingPeopleList && assistingPeopleList.length > 0) {
					for (var i = 0; i < assistingPeopleList.length; i++) {
						approverList.push({
							"deptName" : assistingPeopleList[i].DeptName,
							"userName" : assistingPeopleList[i].UserName
						});
					}
				}
				that.approverList = approverList;
				that.approvalBelongFlowNodeList = data.approvalBelongFlowNodeList;
				that.canApproval = data.canApproval;
				that.canAssist = data.canAssist;
				that.hasExecRight = data.hasExecRight;
				that.approvalFlowNodeTypeMap = data.approvalFlowNodeTypeMap;
				that.logMap = data.logMap;
				return data;
			}).then(function(data) {
				that.$nextTick(function() {
					that.getFlowContainerBtnDiv(that.params);
					var nodeList = document.getElementsByClassName("flow-loop-div");
					for (var i = 0; i < nodeList.length; i++) {
						nodeList[i].parentElement.parentElement.appendChild(nodeList[i]);
					}
				});
			});
		},
		dataHandle : function(approvalBelongFlowNode, index) {
			var that = this;
			if (approvalBelongFlowNode.current == 1 || approvalBelongFlowNode.approvalFlowNodeType == 100) {
				that.currentIndex = approvalBelongFlowNode.approvalIndex;
			}
			
			if (approvalBelongFlowNode.approvalFlowNodeType == 2) {
				that.loopNode = approvalBelongFlowNode;
				that.handleLoopNode();
			}
		},
		getApprovedRecord : function(approvalBelongFlowNode) {
			if (approvalBelongFlowNode.approvedRecordList) {
				var users = new Array();
				for (var i = 0; i < approvalBelongFlowNode.approvedRecordList.length; i++) {
					users.push(approvalBelongFlowNode.approvedRecordList[i].deptName + " - " + approvalBelongFlowNode.approvedRecordList[i].userName);
				}
				return users.join("\n");
			}
			return "";
		},
		getApprovalBelongFlowNodeNameColor : function(approvalBelongFlowNode) {
			if (approvalBelongFlowNode.current == 1) {
				return "color: #ee4f4f;";
			} else if (approvalBelongFlowNode.state == 1 || approvalBelongFlowNode.state == -1) {
				return "color: #009688;";
			} else {
				return "color: #A0A0A0;";
			}
		},
		dateToStr : function(date) {
			return assemblys.dateToStr(date);
		},
		// 处理循环节点指示器
		handleLoopNode : function() {
			var that = this;
			// 循环节点处理
			if (that.loopNode) {
				var approvalFlowNodeData = JSON.parse(that.loopNode.approvalFlowNodeData);
				for (var i = 0; i < that.approvalBelongFlowNodeList.length; i++) {
					// 找出开始节点
					if (approvalFlowNodeData.beginNode == that.approvalBelongFlowNodeList[i].approvalBelongFlowNodeCode) {
						that.beginIndex = i;
					}
					
					if (that.loopNode == that.approvalBelongFlowNodeList[i]) {
						that.endIndex = i;
						break;
					}
				}
			}
		},
		getLoopNodeClass : function(approvalBelongFlowNode) {
			var that = this;
			var className = "flow-loop-div";
			
			if (approvalBelongFlowNode.approvalIndex == that.beginIndex) {
				className += " flow-loop-div-begin";
			}
			
			if (approvalBelongFlowNode.approvalIndex == that.endIndex) {
				className += " flow-loop-div-end";
			}
			return className;
		},
		handleFlowLoopNodeDivHeight : function(el) {
			el.style.height = (el.clientHeight + 10) + "px";
			
		},
		getFlowContainerBtnDiv : function(param) {
			var that = this;
			if (!that.approvalBelongFlowNode && !that.hasExecRight && !that.canApproval && !that.canAssist) {
				return;
			}
			
			that.actions.length = 0;
			
			if (param.hasApproval && that.canApproval) {
				that.actions.push({
					text : '审批',
					value : 1
				});
				that.actions.push({
					text : '回退',
					value : 2
				});
			}
			
			if (param.hasApproval && that.hasExecRight && that.currentApprovalBelongFlowNode.state != 2) {
				that.actions.push({
					text : '改派',
					value : 3
				});
				
				that.actions.push({
					text : '协助',
					value : 4
				});
				
				that.actions.push({
					text : '更新流程',
					value : 5
				});
			}
			
			if (param.hasApproval && that.canAssist) {
				that.actions.push({
					text : '协助意见',
					value : 6
				});
			}
			
			that.$nextTick(function() {
				var el = document.getElementsByClassName("flow-btn-container");
				if (el && el.length > 0) {
					for (var i = 0; i < el.length; i++) {
						document.body.appendChild(el[i]);
					}
				}
			});
		},
		onSelect : function(action, index) {
			var that = this;
			
			if (action.value == 5) {
				that.updateApprovalFlow();
			} else {
				that.showPopover = false;
				location.url({
					url : that.getApprovalURL(action.value)
				});
			}
		},
		getApprovalURL : function(actionPopupValue) {
			var that = this;
			if (actionPopupValue == 1 || actionPopupValue == 6) {
				var approvalType = actionPopupValue == 6 ? 1 : 0;
				return basePath + "/frame/mobile/customApprovalFlow/approval/defaultApproval.html?funCode=" + that.params.funCode + "&appCode=" + that.params.appCode + "&approvalBelongCode=" + that.params.approvalBelongCode + "&approvalBelongFlowNodeCode="
						+ that.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode + "&inLoop=" + (that.beginIndex > -1 && that.endIndex > -1 && that.currentApprovalBelongFlowNode.approvalIndex >= that.beginIndex && that.currentApprovalBelongFlowNode.approvalIndex <= that.endIndex ? 1 : 0)
						+ (approvalType == 1 ? "&approvalType=1" : "&approvalType=0") + "&compNo=" + that.params.compNo;
			} else if (actionPopupValue == 2) {
				return basePath + "/frame/mobile/customApprovalFlow/approval/rollback.html?appCode=" + that.params.appCode + "&approvalBelongCode=" + that.params.approvalBelongCode + "&approvalBelongFlowNodeCode=" + that.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode + "&funCode="
						+ that.params.funCode + "&compNo=" + that.params.compNo;
			} else if (actionPopupValue == 3) {
				return basePath + "/frame/mobile/customApprovalFlow/approval/reassignment.html?appCode=" + that.params.appCode + "&funCode=" + that.params.funCode + "&approvalBelongCode=" + that.params.approvalBelongCode + "&approvalBelongFlowNodeCode="
						+ that.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode + "&approvalBelongFlowNodeID=" + that.currentApprovalBelongFlowNode.approvalBelongFlowNodeID + "&compNo=" + that.params.compNo;
			} else if (actionPopupValue == 4) {
				return basePath + "/frame/mobile/customApprovalFlow/approval/approvalAssist.html?appCode=" + that.params.appCode + "&funCode=" + that.params.funCode + "&approvalBelongCode=" + that.params.approvalBelongCode + "&approvalBelongFlowNodeCode="
						+ that.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode + "&compNo=" + that.params.compNo;
			}
		},
		updateApprovalFlow : function() {
			var that = this;
			var appCode = that.params.appCode;
			var approvalBelongCode = that.params.approvalBelongCode;
			
			assemblys.confirm("确认更新当前流程吗？", function() {
				ajax({
					url : basePath + "/frame/approvalFlow/updateApprovalFlow.spring",
					data : {
						approvalBelongCode : approvalBelongCode,
						appCode : appCode
					},
					type : "POST"
				}).then(function(data) {
					assemblys.msg("更新成功", function() {
						location.reload();
					});
				});
			});
		},
	},
	props : {
		params : {
			"type" : Object,
			"default" : function() {
				return Vue.ref({
					approvalBelongCode : "",
					appCode : "",
					funCode : "",
					hasApproval : false
				});
			}
		}
	},
	watch : {
		params : {
			handler : function() {
				var that = this;
				if (that.params && that.params.approvalBelongCode && that.params.appCode && that.params.funCode) {
					that.getApprovalBelongFlowNodeList();
					that.getFlowContainerBtnDiv(that.params);
				}
			},
			deep : true
		}
	}
};

/**
 * 审批记录
 */
components["approval-list"] = {
	template : (function() {
		var html = "";
		html += '<custom-detail-item :datalist="datalist"></custom-detail-item>';
		return html;
	})(),
	created : function() {
		var that = this;
		that.getApprovalBelongFlowNodeRecordList();
	},
	props : {
		params : Object
	},
	data : function() {
		var that = this;
		return {
			"appCode" : that.params.appCode,
			"approvalBelongCode" : that.params.approvalBelongCode,
			datalist : []
		};
	},
	methods : {
		getApprovalBelongFlowNodeRecordList : function() {
			var that = this;
			return ajax({
				url : basePath + "/frame/approvalFlowRecord/getApprovalBelongFlowNodeRecordList.spring",
				data : {
					approvalBelongCode : that.approvalBelongCode,
					appCode : that.appCode
				}
			}).then(function(data) {
				var recordList = data.approvalBelongFlowNodeRecordList;
				if (recordList.length > 0) {
					var recordCodes = [];
					for (var i = 0; i < recordList.length; i++) {
						recordCodes.push(recordList[i].ApprovalBelongFlowNodeRecordCode);
					}
					// 获取附件
					ajax({
						url : basePath + "frame/fileUpload/getAttachments.spring",
						data : {
							belongToCode : recordCodes.join(",")
						},
						skipDataCheck : true,
					}).then(function(data2) {
						var attaMapping = {};
						var attaList = data2.attachmentsList;
						for (var i = 0; i < attaList.length; i++) {
							var atta = attaList[i];
							if (attaMapping[atta.belongToCode]) {
								attaMapping[atta.belongToCode].push(atta);
							} else {
								attaMapping[atta.belongToCode] = [];
								attaMapping[atta.belongToCode].push(atta);
							}
							
						}
						var dataList = [];
						var recordList = data.approvalBelongFlowNodeRecordList;
						if (recordList.length > 0) {
							for (var i = 0; i < recordList.length; i++) {
								var record = recordList[i];
								let childList = [];
								var approvalContentList = JSON.parse(record.ApprovalContent);
								for (var j = 0; j < approvalContentList.length; j++) {
									// 每行
									childList.push({
										title : approvalContentList[j].name,
										label : approvalContentList[j].value,
										code : approvalContentList[j].code,
									});
								}
								// 附件
								var attaList2 = attaMapping[record.ApprovalBelongFlowNodeRecordCode] || [];
								if (attaList2.length > 0) {
									// 每行
									childList.push({
										title : "附件信息",
										list : attaList2,
									});
								}
								
								let title = record.approvalBelongFlowNodeName;
								let value = record.deptName + " - " + record.userName + " - " + assemblys.dateToStr(record.CreateDate);
								let label = "";
								if (record.ApprovalType == 2) {
									label = "<font style='color: red;'>【回退】</font>";
								}
								
								let temp = {
									title : title,
									value : value,
									label : label,
									data : childList
								}
								dataList.push(temp);
							}
							that.datalist = dataList;
						}
						
					})
				}
			});
		},
	}
}