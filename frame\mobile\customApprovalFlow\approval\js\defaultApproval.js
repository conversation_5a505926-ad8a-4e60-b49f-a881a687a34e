page.defaultApproval.option = {
	created : function() {
		var that = this;
		
		that.formData = that.param;
		that.formData.stateStatusNo = -999;
		that.defaultApprovalInit();
	},
	/*computed : {
		verifyApprovalUIDs : function() {
			return this.verifyDynamic && this.hasDynamic ? 'required' : '';
		}
	},*/
	data : function() {
		var that = this;
		return {
			formData : Vue.ref({
				approvalBelongCode : "",
				approvalType : 0,
				approvalBelongFlowNodeCode : "",
				funCode : "",
				appCode : "",
				compNo : "",
				inLoop : false,
				approvalFlowNodeData : "",
				approvalContent : "",
				attaName : [],
				attaUrl : [],
				attaSize : [],
				attaType : [],
				stateStatusNo : -999,
				approvalBelongFlowNodeRecordDraftCode : "",
				isDefault : 0,
				hasFinish : Vue.ref(false)
			}),
			attachments : Vue.ref({
				attaName : [],
				attaUrl : [],
				attaSize : [],
				attaType : [],
			}),
			customFormTypeStateList : Vue.ref([]),
			hasCountersign : Vue.ref(false),
			hasStateStatusNo : Vue.ref(false),
			hasDynamic : Vue.ref(false),
			/*verifyDynamic : Vue.ref(true),*/
			inLoop : Vue.ref(false),
			cols : Vue.ref(12),
			showSelectApprover : Vue.ref(false),
			selectApproverTitle : Vue.ref("指定审批人"),
			copyUserCodes : Vue.ref(""),
			copyUserNames : Vue.ref(""),
			commentContent : Vue.ref(""),
			selectApproverParam : Vue.ref({
				URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
				param : {
					funCode : that.param.funCode,
					rightPoint : 1,
				},
				field : {
					name : "userName",
					value : "uID"
				},
				parseData : function(data) {
					return data.users;
				},
				cancel : function() {
					window.isSubmit = false;
					that.hasDynamic = false;
				},
				callback : function(data) {
					var names = [];
					var values = [];
					for (var i = 0; i < data.length; i++) {
						names.push(data[i].text);
						values.push(data[i].value);
					}
					
					var n = names.join(",");
					that.approverNames = n;
					
					var approvalUIDs = values.join(",");
					var approvalFlowNodeData = {
						approvalRight : 3,
						approvalUIDs : approvalUIDs
					}
					that.formData.approvalFlowNodeData = JSON.stringify(approvalFlowNodeData);
				}
			}),
			selectCopyUserParam : Vue.ref({
				parentName : "科室",
				parentURL : basePath + "frame/common/getDeptListNew.spring",
				parentField : {
					name : "DeptName",
					value : "DeptID"
				},
				parentParam : {
					compNo : that.param.compNo || "",
				},
				parentParseData : function(data) {
					return data.deptList;
				},
				placeholder : "用户名称",
				URL : basePath + "frame/common/getUserList.spring",
				param : {},
				field : {
					name : "userName",
					value : "userCode"
				},
				parseData : function(data) {
					return data.userList;
				},
				callback : function(data) {
					var names = [];
					var values = [];
					for (var i = 0; i < data.length; i++) {
						names.push(data[i].text);
						values.push(data[i].value);
					}
					var n = names.join(",");
					that.copyUserNames = n;
					var copyUserCodes = values.join(",");
					
					that.formData.copyUserCodes = copyUserCodes;
				}
			})
		}
	},
	methods : {
		// 初始化
		defaultApprovalInit : function() {
			var that = this;
			ajax({
				url : basePath + "frame/approvalFlowRecord/defaultApprovalInit.spring",
				data : that.param,
			}).then(function(data) {
						that.param.compNo = data.approvalBelongFlowNode.compNo;
						if (data.approvalBelongFlowNode.approvalCustomFormCode && that.param.approvalType != 1) {
							location.href = basePath + "frame/mobile/customForm/customFormApprovalTemplate.html?submitType=1&customFormCode=" + data.approvalBelongFlowNode.approvalCustomFormCode + "&appCode=" + that.param.appCode + "&funCode=" + that.param.funCode + "&compNo="
									+ (that.param.compNo || "") + "&approvalBelongCode=" + that.param.approvalBelongCode + "&approvalBelongFlowNodeCode=" + that.param.approvalBelongFlowNodeCode + "&inLoop=" + that.param.inLoop;
							return;
						} else {
							document.body.className = "";
						}
						console.log("sdasdsd")
						if (data.approvalBelongFlowNode.state != 2 && that.param.approvalType == 0 && data.finishExecRight) {// 协助和回退审批不显示按钮
							that.hasStateStatusNo = true;
							that.hasFinish = true;
						}
						
						if (data.approvalBelongFlowNode.approvalFlowNodeType == 3 && that.param.approvalType == 0) {// 协助不显示按钮
							that.hasCountersign = true;
						}
						
						if (that.param.inLoop == 1 && that.param.approvalType != 1 && data.approvalBelongFlowNode.state != 2) {// 协助不显示按钮
							that.inLoop = true;
						}
						
						that.baseImgPath = data.baseImgPath;
						that.loadEditor();
						
						that.initStatus(data);
						if (data.approvalBelongFlowNode.state != 2 && data.nextApprovalBelongFlowNode) {
							for (var i = 0; i < data.customFormTypeStateList.length; i++) {
								if (data.customFormTypeStateList[i].customFormTypeStateNo == data.nextApprovalBelongFlowNode.approvalNodeState) {
									that.formData.stateStatusNo = data.nextApprovalBelongFlowNode.approvalNodeState || -999;
									break;
								}
							}
						}
						
						if (!that.hasCountersign && that.inLoop) {
							that.cols = 8;
						} else if (that.hasCountersign && !that.inLoop) {
							that.cols = 8;
						} else if (that.hasCountersign && that.inLoop) {
							that.cols = 6;
						}
						
						if (data.approvalBelongFlowNodeRecord) {
							if (data.isDefault) {
								that.formData.approvalBelongFlowNodeRecordDraftCode = data.approvalBelongFlowNodeRecord.approvalBelongFlowNodeRecordCode;
							}
							that.commentContent = JSON.parse(data.approvalBelongFlowNodeRecord.approvalContent)[0].value;
						}
						
						if (data.attachments) {
							for (var i = 0; i < data.attachments.length; i++) {
								that.attachments.attaName[i] = data.attachments[i].attachmentName;
								that.attachments.attaUrl[i] = data.attachments[i].attachmentURL;
								that.attachments.attaSize[i] = data.attachments[i].attachmentSize;
								that.attachments.attaType[i] = data.attachments[i].attachmentType;
								that.formData.attaName[i] = data.attachments[i].attachmentName;
								that.formData.attaUrl[i] = data.attachments[i].attachmentURL;
								that.formData.attaSize[i] = data.attachments[i].attachmentSize;
								that.formData.attaType[i] = data.attachments[i].attachmentType;
							}
						}
					});
		},
		initStatus : function(data) {
			this.customFormTypeStateList.push({
				name : "自动",
				value : -999
			});
			for (var i = 0; i < data.customFormTypeStateList.length; i++) {
				this.customFormTypeStateList.push({
					name : data.customFormTypeStateList[i].customFormTypeStateName,
					value : data.customFormTypeStateList[i].customFormTypeStateNo
				});
			}
		},
		loadEditor : function() {
			let that = this;
			that.he = HE.getEditor('commentContents', {
				width : '99%',
				height : '150px',
				autoHeight : true,//自动增长
				autoFloat : true,//浮动
				//item : ['bold','italic','underline','fontSize','color','backColor']
				item : [ 'bold', 'italic', 'strike', 'underline', 'fontSize', 'color', 'backColor' ]
			});
		},
		saveFinish : function() {
			let that = this;
			assemblys.confirm('确定一键结束审批吗？', function() {
				that.save(3);
			});
		},
		// 保存
		save : function(type) {
			let that = this;
			let url = basePath + "frame/approvalFlowRecord/saveApprovalBelongFlowNodeRecord.spring";
			if (type != 2) {
				var approvalContent = that.he.getHtml();
				if (!approvalContent) {
					assemblys.msg('请输入审批意见', null, {
						type : 'warning',
					});
					isSubmit = false;
					return;
				}
			} else {
				that.formData.isDefault = 1;
			}
			
			that.formData.approvalContent = that.he.getHtml();
			that.formData.type = type || 0;
			ajax({
				url : url,
				type : "post",
				data : that.formData,
				dataType : "json",
			}).then(function(data) {
				assemblys.msg(type != 2 ? '提交成功' : "保存成功", function() {
					if (type != 2) {// 不是草稿
						history.back();
					} else {
						that.formData.approvalBelongFlowNodeRecordDraftCode = data.approvalBelongFlowNodeRecordDraftCode;
					}
				});
			});
			that.formData.isDefault = 0;
		},
		// 结束循环
		saveLoop : function() {
			var that = this;
			
			assemblys.confirm("确定结束循环吗？", function() {
				ajax({
					url : basePath + "frame/approvalFlowRecord/getLoopEndNextNode.spring",
					data : {
						approvalBelongCode : that.param.approvalBelongCode,
						funCode : that.param.funCode,
						appCode : that.param.appCode,
					},
				}).then(function(data) {
					if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
						that.hasDynamic = true;
						that.selectApproverParam.callback = function(vs) {
							var values = [];
							for (var i = 0; i < vs.length; i++) {
								values.push(vs[i].value);
							}
							
							var approvalUIDs = values.join(",");
							var approvalFlowNodeData = {
								approvalRight : 3,
								approvalUIDs : approvalUIDs
							}

							that.formData.approvalFlowNodeData = JSON.stringify(approvalFlowNodeData);
							that.saveLoopAfterCheck();
							
							that.selectApproverParam.callback = null;
							that.hasDynamic = false;
						}
						that.$refs.approvalUIDs.show = true;
						that.$refs.approvalUIDs.loadParent();
					} else {
						that.formData.approvalFlowNodeData = "{}";
						that.saveLoopAfterCheck();
					}
				});
				
			});
			
		},
		getNextApprovalBelongFlowNode : function(type) {
			var that = this;
			
			if (that.param.approvalType == 1) {
				that.save(type);
				return;
			}
			
			if (isSubmit) {
				return;
			}
			isSubmit = true;
			
			ajax({
				url : basePath + "frame/approvalFlow/getNextApprovalBelongFlowNode.spring",
				data : {
					approvalBelongCode : that.param.approvalBelongCode,
					appCode : that.param.appCode,
				},
			}).then(function(data) {
				if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
					that.hasDynamic = true;
					that.selectApproverParam.callback = function(vs) {
						that.hasDynamic = false;
						var values = [];
						for (var i = 0; i < vs.length; i++) {
							values.push(vs[i].value);
						}
						
						if (values.length == 0) {
							assemblys.msg('请选择审批人', null, {
								type : 'warning',
							});
							isSubmit = false;
							return;
						}
						
						var approvalUIDs = values.join(",");
						var approvalFlowNodeData = {
							approvalRight : 3,
							approvalUIDs : approvalUIDs
						}

						that.formData.approvalFlowNodeData = JSON.stringify(approvalFlowNodeData);
						that.save(type);
						
						that.selectApproverParam.callback = null;
					}
					that.$refs.approvalUIDs.show = true;
					that.$refs.approvalUIDs.loadParent();
				} else {
					that.formData.approvalFlowNodeData = "{}";
					that.save(type);
				}
			});
			
		},
		// 结束循环
		saveLoopAfterCheck : function() {
			var that = this;
			
			var url = basePath + "frame/approvalFlowRecord/saveLoopApprovalBelongFlowNodeRecord.spring";
			var approvalContent = that.he.getHtml();
			if (!approvalContent) {
				assemblys.msg('请输入审批意见', null, {
					type : 'warning',
				});
				return;
			}
			that.formData.approvalContent = that.he.getHtml();
			ajax({
				url : url,
				type : "post",
				data : that.formData,
			}).then(function(data) {
				assemblys.msg('提交成功', function() {
					history.back();
				});
			});
			
		},
	}
}