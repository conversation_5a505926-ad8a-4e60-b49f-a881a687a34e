page.rollback.option = {
	created : function() {
		var that = this;
		assemblys.getMenuIcon(that.param.funCode, false).then(function(data) {
			return that.getDeptList(data);
		}).then(function() {
			return that.getCustomApprovalRightFunList();
		}).then(function() {
			return that.getCustomFormDeptFieldList();
		}).then(function() {
			return that.getCustomApprovalFlowNode();
		});
	},
	data : function() {
		var that = this;
		return {
			appCode : Vue.ref(""),
			approvalFlowNodeType : Vue.ref(null),
			approvalFlowNodeTypeAction : [ {
				name : "普通节点",
				value : 0
			}, {
				name : "会签节点",
				value : 3
			}, {
				name : "循环节点",
				value : 2,
				disabled : true
			}, {
				name : "指定审批节点",
				value : 1,
				disabled : true
			} ],
			approvalFlowNodeData : Vue.ref({
				approvalRight : "",
				approvalDeptID : "",
				approvalFunCode : "",
				approvalDeptOwnership : "",
				approvalUIDs : "",
				approvalUIDsAry : []
			}),
			approvalRightAction : [ {
				name : '审批科室',
				value : 1
			}, {
				name : '功能组织架构',
				value : 2
			}, {
				name : '指定审批人',
				value : 3
			} ],
			approvalDeptIDAction : [],
			approvalFunCodeAction : [],
			approvalDeptOwnershipAction : [ {
				name : "接口（默认上报人科室）",
				value : ""
			} ],
			approverUUIDsParam : Vue.ref({
				URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
				param : {
					funCode : that.param.funCode,
					rightPoint : 1,
				},
				field : {
					name : "userName",
					value : "uID"
				},
				parseData : function(data) {
					return data.users;
				},
			}),
			reason : Vue.ref("")
		};
	},
	methods : {
		getDeptList : function(d) {
			var that = this;
			return ajax({
				url : basePath + "frame/common/getDeptList.spring",
				data : {
					compNo : d.compNo
				},
				skipDataCheck : true,
			}).then(function(data) {
				var deptList = data.deptList;
				for (var i = 0; i < deptList.length; i++) {
					that.approvalDeptIDAction.push({
						name : deptList[i].DeptName,
						value : deptList[i].DeptID
					});
				}
			});
		},
		getCustomApprovalRightFunList : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/appFunsSet/getCustomApprovalRightFunList.spring",
				data : {
					appCode : that.param.appCode
				},
			}).then(function(data) {
				var funList = data.funList;
				for (var i = 0; i < funList.length; i++) {
					that.approvalFunCodeAction.push({
						name : funList[i].funName,
						value : funList[i].funCode
					});
				}
			});
		},
		getCustomFormDeptFieldList : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/approvalFlow/getDeptFieldList.spring",
				data : {
					appCode : that.param.appCode,
					approvalBelongCode : that.param.approvalBelongCode,
				},
			}).then(function(data) {
				var deptFieldList = data.deptFieldList;
				for (var i = 0; i < deptFieldList.length; i++) {
					that.approvalDeptOwnershipAction.push({
						name : deptFieldList[i].name,
						value : deptFieldList[i].value
					});
				}
			});
		},
		getCustomApprovalFlowNode : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/approvalFlow/getApprovalBelongFlowNode.spring",
				data : {
					approvalBelongFlowNodeID : that.param.approvalBelongFlowNodeID,
					appCode : that.param.appCode
				},
			}).then(function(data) {
				if (data.approvalBelongFlowNode) {
					that.approvalBelongFlowNode = data.approvalBelongFlowNode;
					that.approvalFlowNodeType = data.approvalBelongFlowNode.approvalFlowNodeType;
					that.approvalFlowNodeData = JSON.parse(data.approvalBelongFlowNode.approvalFlowNodeData);
					if (!that.approvalFlowNodeData.approvalDeptOwnership) {
						that.approvalFlowNodeData.approvalDeptOwnership = "";
					}
					if (that.approvalFlowNodeData.approvalUIDs) {
						that.approvalFlowNodeData.approvalUIDsAry = that.approvalFlowNodeData.approvalUIDs.split(",");
					}
				}
			});
		},
		saveCustomApprovalFlowNode : function() {
			var that = this;
			if (!that.form.__verify()) {
				return;
			}
			
			var approvalFlowNodeData = that.form.__json();
			if (approvalFlowNodeData.approvalUIDs && approvalFlowNodeData.approvalUIDs.length > 0) {
				approvalFlowNodeData.approvalUIDs = approvalFlowNodeData.approvalUIDs.join(",");
			}
			that.approvalBelongFlowNode.approvalFlowNodeType = approvalFlowNodeData.approvalFlowNodeType;
			that.approvalBelongFlowNode.reason = approvalFlowNodeData.reason;
			that.approvalBelongFlowNode.approvalFlowNodeData = JSON.stringify(approvalFlowNodeData);
			that.approvalBelongFlowNode.funCode = that.param.funCode;
			if (isSubmit) {
				return;
			}
			isSubmit = true;
			
			return ajax({
				url : basePath + "frame/approvalFlow/saveApprovalBelongFlowNode.spring",
				data : that.approvalBelongFlowNode,
				type : "post",
			}).then(function(data) {
				assemblys.msg("保存成功", function() {
					history.back();
				});
			});
		},
	}
}