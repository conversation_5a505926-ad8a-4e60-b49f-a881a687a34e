page.rollback.option = {
	created : function() {
		var that = this;
		that.formData = that.param;
		that.initRollbackNode();
		that.rollbackInit();
	},
	data : function() {
		var that = this;
		return {
			formData : Vue.ref({
				approvalBelongCode : "",
				approvalBelongFlowNodeCode : "",
				rollbackApprovalBelongFlowNodeCode : "",
				funCode : "",
				appCode : "",
				approvalContent : "",
				attaName : [],
				attaUrl : [],
				attaSize : [],
				attaType : []
			}),
			approvalBelongFlowNodeList : Vue.ref([])
		}
	},
	methods : {
		// 初始化
		rollbackInit : function() {
			var that = this;
			ajax({
				url : basePath + "frame/approvalFlowRecord/rollbackInit.spring",
				data : that.param,
			}).then(function(data) {
				that.baseImgPath = data.baseImgPath;
				that.loadEditor();
			});
		},
		loadEditor : function() {
			let that = this;
			that.he = HE.getEditor('commentContents', {
				width : '99%',
				height : '150px',
				autoHeight : true,//自动增长
				autoFloat : true,//浮动
				//item : ['bold','italic','underline','fontSize','color','backColor']
				item : [ 'bold', 'italic', 'strike', 'underline', 'fontSize', 'color', 'backColor' ]
			});
		},
		// 保存
		save : function(type) {
			let that = this;
			let url = basePath + "frame/approvalFlowRecord/saveRollbackApprovalBelongFlowNodeRecord.spring";
			var approvalContent = that.he.getHtml();
			if (!approvalContent) {
				assemblys.msg('请输入回退原因', null, {
					type : 'warning',
				});
				return;
			}
			
			if (!that.formData.rollbackApprovalBelongFlowNodeCode) {
				assemblys.msg('请选择回退节点', null, {
					type : 'warning',
				});
				return;
			}
			
			that.formData.approvalContent = "<p>" + that.he.getHtml() + "</p>";
			if (isSubmit) {
				return;
			}
			isSubmit = true;
			ajax({
				url : url,
				type : "post",
				data : that.formData,
				dataType : "json",
			}).then(function(data) {
				assemblys.msg('回退成功', function() {
					history.back();
				});
			});
		},
		initRollbackNode : function() {
			var that = this;
			ajax({
				url : basePath + "/frame/approvalFlow/getApprovalBelongFlowNodeList.spring",
				data : that.param
			}).then(function(data) {
				for (var i = 0; i < data.approvalBelongFlowNodeList.length; i++) {
					
					if (data.approvalBelongFlowNodeList[i].current == 1) {
						break;
					}
					
					if (data.approvalBelongFlowNodeList[i].state < 0) {
						continue;
					}
					
					data.approvalBelongFlowNodeList[i].name = data.approvalBelongFlowNodeList[i].approvalBelongFlowNodeName;
					data.approvalBelongFlowNodeList[i].value = data.approvalBelongFlowNodeList[i].approvalBelongFlowNodeCode;
					that.approvalBelongFlowNodeList.push(data.approvalBelongFlowNodeList[i]);
				}
			});
			
		},
	}
}