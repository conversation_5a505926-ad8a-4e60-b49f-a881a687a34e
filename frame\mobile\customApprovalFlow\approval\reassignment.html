<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>改派</title>
<link rel="stylesheet" type="text/css" href="../../../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/defaultApproval.css?version=1.0.1.0" />
</head>
<body>
	<div app="rollback">
		<div class="custom-field-parent">
			<van-cell title="改派" :style="{ fontSize : '20px'}"></van-cell>
		</div>
		
		<field-textarea title="改派原因" name="reason" v-model:value="reason" verify="required|limit"  limit="200"></field-textarea>
		
		<field-radio :actions="approvalFlowNodeTypeAction" title="节点类型" name="approvalFlowNodeType" v-model:value="approvalFlowNodeType" verify="required"></field-radio>
		
		<field-select v-if="approvalFlowNodeType == 0" :actions="approvalRightAction" title="审批权限" name="approvalRight" v-model:value="approvalFlowNodeData.approvalRight" verify="required"></field-select>
		
		<field-select v-if="approvalFlowNodeType == 0 && approvalFlowNodeData.approvalRight == 1" :actions="approvalDeptIDAction" title="审批科室" name="approvalDeptID" v-model:value="approvalFlowNodeData.approvalDeptID" verify="required"></field-select>
		
		<field-select v-if="approvalFlowNodeType == 0 && approvalFlowNodeData.approvalRight == 2" :actions="approvalFunCodeAction" title="功能点" name="approvalFunCode" v-model:value="approvalFlowNodeData.approvalFunCode" verify="required"></field-select>
		
		<field-select v-if="approvalFlowNodeType == 0 && approvalFlowNodeData.approvalRight == 2" :actions="approvalDeptOwnershipAction" title="审批口径" name="approvalDeptOwnership" v-model:value="approvalFlowNodeData.approvalDeptOwnership" ></field-select>
		
		<field-multiple v-if="approvalFlowNodeType == 0 && approvalFlowNodeData.approvalRight == 3" :params="approverUUIDsParam" title="指定审批人" name="approvalUIDs" v-model:value="approvalFlowNodeData.approvalUIDsAry" verify="required"></field-multiple>
		
		<field-multiple v-if="approvalFlowNodeType == 3" :params="approverUUIDsParam" title="指定会签人员" name="approvalUIDs" v-model:value="approvalFlowNodeData.approvalUIDsAry" verify="required" :lazy-render="false"></field-multiple>
		
		<van-button block type="primary" size="normal" block @click="saveCustomApprovalFlowNode">改派</van-button>
	</div>
</body>
<script type="text/javascript" src="../../../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/reassignment.js?version=*******"></script>
</html>
