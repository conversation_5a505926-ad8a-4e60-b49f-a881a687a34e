<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>回退</title>
<link rel="stylesheet" type="text/css" href="../../../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/defaultApproval.css?version=1.0.1.0" />
</head>
<body>
	<div app="rollback">
		<div class="custom-field-parent">
			<van-cell title="回退" :style="{ fontSize : '20px'}"></van-cell>
		</div>
		<div class="custom-field-parent">
			<van-cell class="approval-field">
				<template #title>
					<span style="color: red;">*</span>
					回退节点
				</template>
			</van-cell>
			<van-cell class="approval-handy-cell" title-class="approval-handy-cell-title">
				<template #title>
					<van-radio-group v-model="formData.rollbackApprovalBelongFlowNodeCode" inset>
						<div v-for="approvalBelongFlowNode in approvalBelongFlowNodeList">
							<van-cell :title="approvalBelongFlowNode.approvalBelongFlowNodeName + '&nbsp;&nbsp;&nbsp;---&nbsp;&nbsp;&nbsp;' + approvalBelongFlowNode.approvedRecordList[0].deptName + ' - ' + approvalBelongFlowNode.approvedRecordList[0].userName" clickable @click="formData.rollbackApprovalBelongFlowNodeCode = approvalBelongFlowNode.approvalBelongFlowNodeCode">
								<template #right-icon>
									<van-radio :name="approvalBelongFlowNode.approvalBelongFlowNodeCode"></van-radio>
								</template>
							</van-cell>
						</div>
					</van-radio-group>
				</template>
			</van-cell>
		</div>
		<div class="custom-field-parent">
			<van-cell class="approval-field">
				<template #title>
					<span style="color: red;">*</span>
					回退原因
				</template>
			</van-cell>
			<van-cell class="approval-handy-cell" title-class="approval-handy-cell-title">
				<template #title>
					<van-field id="commentContents" type="textarea" size="large"></van-field>
				</template>
			</van-cell>
		</div>
		<div class="custom-field-parent">
			<van-cell title="上传附件" class="approval-field"></van-cell>
			<van-cell>
				<template #title>
					<uploader ref="uploader" :attas="formData"></uploader>
				</template>
			</van-cell>
		</div>
		<van-button block type="primary" size="normal" block @click="save">回退</van-button>
	</div>
</body>
<script type="text/javascript" src="../../../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../../../plugins/vant/components/uploader.js"></script>
<script type="text/javascript" src="../../../../../plugins/vant/components/multipleSelect.js"></script>
<script type="text/javascript" src="../../../../../plugins/vant/plugins/handyEditor/HandyEditor.min.js"></script>
<script type="text/javascript" src="../../../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/rollback.js?version=*******"></script>
</html>
