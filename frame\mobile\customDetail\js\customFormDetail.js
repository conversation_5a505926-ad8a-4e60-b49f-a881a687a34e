page.customFormDetail.option = {
	created : function() {
		var that = this;
		assemblys.getMenuIcon(that.param.funCode, false).then(function(data) {
			that.param.compNo = data.compNo;
		}).then(function(data) {
			return that.getCustomDetailRight();
		}).then(function(data) {
			return that.getCustomFormTypeMenu();
		});
	},
	data : function() {
		var that = this;
		
		var tabList = {
			"formDetail" : {
				title : "表单详情",
				params : {
					"appCode" : that.param.appCode,
					"compNo" : that.param.compNo,
					"customFormCode" : that.param.customFormCode,
					"customFormFilledCode" : that.param.customFormFilledCode,
					"customFormBusinessCode" : that.param.customFormBusinessCode,
					"showLabel" : false
				}
			},
			"approvalList" : {
				title : "审核记录",
				params : {
					approvalBelongCode : that.param.approvalBelongCode,
					appCode : that.param.appCode,
					compNo : that.param.compNo,
				}
			},
			"optLogList" : {
				title : "操作日志",
				params : {
					appCode : that.param.appCode,
					bizRelationCode : that.param.approvalBelongCode
				}
			},
			"approvalFlow" : {
				title : "流程信息",
				params : {
					approvalBelongCode : that.param.approvalBelongCode,
					appCode : that.param.appCode,
					funCode : that.param.funCode,
					compNo : that.param.compNo,
					hasApproval : false
				}
			}
		}

		return {
			tabList : tabList,
			customForm : {
				url : "",
				title : "编辑表单",
				show : false
			},
			param : Vue.ref({
				titleName : "",
				appCode : "",
				funCode : "",
				customFormFilledCode : "",
				approvalBelongCode : "",
				customFormCode : "",
				customFormBusinessCode : "",
				customFormTypeCode : "",
				customFormTypeMenuCode : "",
				compNo : ""
			})
		};
		
	},
	methods : {
		/**
		 * 获取当前菜单类型
		 */
		// 菜单信息- 来自【应用接口管理 - getCustomFormTypeMenu 接口】
		getCustomFormTypeMenu : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/customFormType/getCustomFormTypeMenu.spring",
				data : {
					"customFormTypeCode" : that.param.customFormTypeCode,
					"customFormTypeMenuCode" : that.param.customFormTypeMenuCode,
					"appCode" : that.param.appCode
				},
				dataType : "json"
			}).then(function(data) {
				that.tabList['approvalFlow'].params.hasApproval = (data.customFormTypeMenu.customFormTypeMenuType == 0 ? true : false);
			})
		},
		/**
		 * 获取当前功能权限
		 */
		getCustomDetailRight : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/customDetail/getCustomDetailRight.spring",
				data : {
					"funCode" : that.param.funCode,
					"appCode" : that.param.appCode,
					"customFormFilledCode" : that.param.customFormFilledCode,
				},
				dataType : "json",
			}).then(function(data) {
				that.initTopButton(data);
			})
		},
		/**
		 * 顶部按钮
		 */
		initTopButton : function(rightMap) {
			var that = this;
			
			let isInvalid = rightMap.hasInvalidStatus != rightMap.status;
			if (rightMap.hasInvalidStatus && !isInvalid) {
				let div = document.createElement("div");
				div.className = "void";
				div.innerText = "已作废";
				document.body.appendChild(div);
			}
			
			const actions = [];
			if (rightMap.hasEditRight && (!rightMap.hasInvalidStatus || isInvalid)) {
				actions.push({
					text : '修改表单',
					value : 1
				});
			}
			
			if (rightMap.hasExecRight && rightMap.hasInvalidStatus && isInvalid) {
				actions.push({
					text : '作废',
					value : 2
				});
			}
			
			if (actions.length > 0) {
				var html = '';
				html += '<van-popover v-model:show="showPopover" theme="dark" :actions="actions" placement="bottom-end" @select="onSubmit" :offset="offset">';
				html += '	<template #reference>';
				html += '		<van-icon name="ellipsis" size="18" />';
				html += '	</template>';
				html += '</van-popover>';
				top.page.index.vm.initTopRightTitle({
					template : html,
					data : function() {
						const showPopover = top.Vue.ref(false);
						return {
							showPopover : showPopover,
							actions : actions,
							offset : [ 0, 20 ]
						};
					},
					methods : {
						onSubmit : function(action, index) {
							if (action.value == 1) {
								var url = basePath + "frame/mobile/customForm/registerReport.html?type=2";
								url += "&customFormCode=" + that.param.customFormCode;
								url += "&customFormFilledCode=" + that.param.customFormFilledCode;
								url += "&compNo=" + that.param.compNo;
								url += "&appCode=" + that.param.appCode;
								location.url({
									url : url
								});
							} else if (action.value == 2) {
								let approvalFlow = that.$refs.approvalFlow;
								let approvalBelongFlowNodeCode = approvalFlow.currentApprovalBelongFlowNode ? approvalFlow.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode
										: approvalFlow.approvalBelongFlowNodeList[approvalFlow.approvalBelongFlowNodeList.length - 1].approvalBelongFlowNodeCode
								let url = basePath + "frame/mobile/customDetail/invalid.html?1=1"
								url += "&appCode=" + that.param.appCode;
								url += "&approvalBelongCode=" + that.param.customFormFilledCode;
								url += "&approvalBelongFlowNodeCode=" + approvalBelongFlowNodeCode;
								location.url({
									url : url
								});
							}
							return;
						}
					}
				});
			} else {
				top.page.index.vm.initTopRightTitle({})
			}
			
		},
	}
}