components["custom-detail-item"] = {
	template : (function() {
		var html = "";
		html += '<van-collapse  v-if="datalist.length>0" v-model="customdetailitems" >';
		html += '	<van-collapse-item  v-for="(temp,index) in datalist" :title="temp.title" :value="temp.value" :name="index"  class="custom-detail-title" >';
		html += '		<template #label>';
		html += '			<span v-html="temp.label"></span>';
		html += '		</template>';
		html += '     	<van-cell-group  v-for="(child,index2) in temp.data" >';
		html += '			 <template v-if="child.label || child.type==\'label\'">';
		html += '			   	<van-cell  v-if="child.title && child.type!=\'file\'" v-bind:fieldbusinessCode="child.businessCode"  :title="child.title" class="custom-detail-title-label">';
		html += '			 		<template v-if="hasCode(child)" #value><span  v-html="setLabelText(child)"></span></template>';
		html += '			 	</van-cell>';
		html += '			 	<van-cell v-if="!hasCode(child)"  v-bind:fieldbusinessCode="child.businessCode" class="custom-detail-title-label" value-class="custom-detail-item-value-alone">';
		html += '			 		<template #value><span  v-html="setLabelText(child)"></span></template>';
		html += '			 	</van-cell>';
		html += '			 	<van-cell v-if="child.type==\'file\'" :title="child.title" class="custom-detail-title-label">';
		html += '			 		<template #label>';
		html += '						<van-cell v-for="(atta,index3) in child.label" :title="atta.title">';
		html += '							<template #value><van-icon name="down" @click="downLoadAttaPreview(atta)"></van-icon></template>';
		html += '						</van-cell>';
		html += '					</template>';
		html += '			 	</van-cell>';
		html += '			 </template>';
		html += '			 <van-cell v-if="child.list && child.list.length > 0" title="附件" class="custom-detail-title-label">';
		html += '			 	<template #label>';
		html += '					<van-cell v-for="(item,index3) in child.list" :title="item.title">';
		html += '						<template #value><van-icon name="down" @click="downLoadAttaPreview(item)"></van-icon></template>';
		html += '					</van-cell>';
		html += '				</template>';
		html += '			 </van-cell>';
		html += '	 	 </van-cell-group>';
		html += '	</van-collapse-item>';
		html += '</van-collapse>';
		html += '<van-cell v-if="datalist.length == 0" title="暂无数据" class="custom-detail-title-label">';
		return html;
	})(),
	created : function() {
	},
	props : {
		datalist : Array
	},
	data : function() {
		return {
			"customdetailitems" : Vue.ref([ 0 ]),
		};
	},
	methods : {
		downLoadAttaPreview : function(item) {
			var eifAttaName = item.title;
			var eifAttaUrl = item.url;
			var url = basePath + "/frame/fileUpload/downloadFile.spring?eifAttaUrl=" + encodeURIComponent(eifAttaUrl) + "&eifAttaName=" + encodeURIComponent(eifAttaName);
			location.href = url;
		},
		hasCode : function(child) {
			return child.code && child.label && child.label.length <= 12 ? true : false;
		},
		setLabelText : function(child) {
			if (child.type == "file") {
				return "";
			}
			return child.label;
		}
	},
}

components["custom-detail-list"] = {
	template : (function() {
		var html = "";
		html += '<div v-if="datalist.length>0" >';
		html += '	<van-cell-group  v-for="(temp,index) in datalist" :title="temp.title">';
		html += '  		<van-cell  v-for="(child,index2) in temp.data"  :title="child.title" :value="child.value"  />';
		html += '	</van-cell-group>';
		html += '</div>';
		html += '<van-cell v-if="datalist.length == 0" title="暂无数据" class="custom-detail-title-label">';
		return html;
	})(),
	created : function() {
	},
	props : {
		datalist : Array
	},
	data : function() {
		return {};
	},
	methods : {}
}

components["custom-show-window"] = {
	template : (function() {
		var html = "";
		html += '<van-popup  v-model:show="show"  closeable="true" :close-on-click-overlay="false"  :style="{ height: \'95%\',width: \'95%\' }">';
		html += '	<van-cell :title="title"></van-cell>';
		html += '	<iframe :src="url" width="100%" height="500px;" frameborder="0"></iframe>';
		html += '</van-popup>';
		return html;
	})(),
	created : function() {
	},
	props : {
		url : String,
		title : String
	},
	data : function() {
		return {
			"show" : Vue.ref([])
		};
	},
	methods : {}
}
