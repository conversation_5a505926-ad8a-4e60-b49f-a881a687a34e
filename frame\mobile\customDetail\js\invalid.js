page.invalid.option = {
	created : function() {
		var that = this;
	},
	data : function() {
		var that = this;
		return {
			invalidContent : Vue.ref("")
		};
	},
	methods : {
		saveInvalid : function() {
			var that = this;
			assemblys.confirm("确定作废『" + that.param.approvalBelongCode + "』吗？", function() {
				if (!that.form.__verify()) {
					return;
				}
				
				var formJson = that.form.__json();
				for ( var key in that.param) {
					formJson[key] = that.param[key];
				}
				
				return ajax({
					url : basePath + "frame/customDetail/saveInvalid.spring",
					data : formJson,
					type : "post",
				}).then(function(data) {
					assemblys.msg("已作废", function() {
						history.back();
					});
				});
			});
		},
	}
}