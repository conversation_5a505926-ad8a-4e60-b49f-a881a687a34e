page.form.components["custom-checkbox"] = {
	created : function() {
		if (!this.$root.param.customFormFilledCode) {
			for (var i = 0; i < this.field.fieldData.length; i++) {
				if (this.field.fieldData[i].hasDefault == 1) {
					this.values[this.field.customFieldCode + "-" + this.index].push(this.field.fieldData[i].customOptionSetCode);
				}
			}
			this.checkboxOnChange(this.values[this.field.customFieldCode + "-" + this.index]);
		}
		this.textValue = this.values[this.customFieldName].join(",");
	},
	name : "custom-checkbox",
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="textValue" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-checkbox-group v-model="values[customFieldName]" inset @change="checkboxOnChange" disabled>';
		html += '	<div v-for="(customOptionSet,i) in field.fieldData">';
		html += '		<van-cell :title="customOptionSet.customOptionSetContent" clickable @click="checkboxOnClick(customOptionSet.customOptionSetCode,i)">';
		html += '			<template #right-icon>';
		html += '				<van-checkbox v-if="isChecbox(customOptionSet.customFieldSet)" :name="customOptionSet.customOptionSetCode" shape="square" :ref="el => refs[customFieldName][i] = el"></van-checkbox>';
		html += '			</template>';
		html += '		</van-cell>';
		html += '		<transition v-if="isOther(customOptionSet) && isShow(customOptionSet)" name="van-fade">';
		html += '			<van-cell>';
		html += '				<template #title>';
		html += '					<van-field v-model="values[getTextNameKey(customOptionSet.customOptionSetCode)]" :placeholder="getPlaceholder(customOptionSet)" :rules="verifyInput"></van-field>';
		html += '				</template>';
		html += '			</van-cell>';
		html += '		</transition>';
		html += '		<transition v-if="customOptionSet.childOptionList.length > 0" name="van-fade" v-show="isShow(customOptionSet)">';
		html += '			<van-cell class="twoLevel">';
		html += '				<template #title>';
		html += '					<component :is="customFielsSet(customOptionSet.childOptionList[0])" :field="getTwoLevelField(customOptionSet)"></component>';
		html += '				</template>';
		html += '			</van-cell>';
		html += '		</transition>';
		html += '	</div>';
		html += '</van-checkbox-group>';
		return html;
	})(),
	data : function() {
		var fieldMap = {};
		for (var i = 0; i < this.field.fieldData.length; i++) {
			fieldMap[this.field.fieldData[i].customOptionSetCode] = this.field.fieldData[i];
		}
		
		return {
			verifyInput : this.$root.verify("required|limit", {
				vueObj : this
			}),
			verify : this.field.isNecessField == 1 ? this.$root.verify("required", {
				vueObj : this
			}) : [],
			textValue : Vue.ref(""),
			checkeds : Vue.ref([]),
			fieldMap : fieldMap
		};
	},
	methods : {
		checkboxOnChange : function(checkeds) {
			var that = this;
			for (var i = 0; i < that.checkeds.length; i++) {
				var flag = false;
				for (var j = 0; j < checkeds.length; j++) {
					if (that.checkeds[i] == checkeds[j]) {
						flag = true;
						break;
					}
				}
				
				if (!flag) {
					that.changeRelationNum(that.checkeds[i], 0);
				}
			}
			
			for (var i = 0; i < checkeds.length; i++) {
				var flag = false;
				for (var j = 0; j < that.checkeds.length; j++) {
					if (that.checkeds[j] == checkeds[i]) {
						flag = true;
						break;
					}
				}
				
				if (!flag) {
					that.changeRelationNum(checkeds[i], 1);
				}
			}
			
			that.checkeds = checkeds;
		},
		checkboxOnClick : function(customOptionSetCode, m) {
			if (this.refs[this.customFieldName][m].checked._value && this.checkRelationNum(customOptionSetCode, m)) {
				return;
			}
			
			this.checkboxOnClickFun(customOptionSetCode, m);
		},
		checkboxOnClickFun : function(customOptionSetCode, m) {
			this.refs[this.customFieldName][m].toggle();
			this.textValue = this.values[this.customFieldName].join(",");
		},
		checkRelationNum : function(customOptionSetCode, m) {// 检查是否有需要隐藏的关联内容
			var that = this;
			var customModularCodeAry = that.$root.customOptionSetCodeMap[customOptionSetCode];
			if (customModularCodeAry) {
				for (var i = 0; i < customModularCodeAry.length; i++) {
					var num = that.$root.relationCodeMap[customModularCodeAry[i]][that.index];
					num--;
					if (num == 0) {
						var customOptionSetContent = that.fieldMap[customOptionSetCode].customOptionSetContent;
						vant.Dialog.confirm({
							message : "取消『" + customOptionSetContent + "』选项会把它所关联的选项内容清空，确定要取消吗？",
						}).then(function() {
							that.checkboxOnClickFun(customOptionSetCode, m);
						});
						return true;
					}
				}
			}
			return false;
		},
		changeRelationNum : function(customOptionSetCode, type) {// x的作用是继续循环遍历其他关联项隐藏
			var that = this;
			var customModularCodeAry = that.$root.customOptionSetCodeMap[customOptionSetCode];
			if (customModularCodeAry) {
				for (var i = 0; i < customModularCodeAry.length; i++) {
					var num = that.$root.relationCodeMap[customModularCodeAry[i]][that.index];
					if (type == 1) { // 等于1选中,累加
						num++;
					} else {
						num--;
						if (num < 0) {// 不能少于0
							num = 0;
						}
					}
					that.$root.relationCodeMap[customModularCodeAry[i]][that.index] = num;
				}
			}
			return true;
		},
		isChecbox : function(customFieldSet) {
			return customFieldSet.replace("Other", "") == "checkbox";
		},
		isOther : function(customOptionSet) {
			return customOptionSet.customFieldSet == 'checkboxOther';
		},
		isShow : function(customOptionSet) {
			
			if (!this.values[this.customFieldName]) {
				return false;
			}
			
			var isShow = this.values[this.customFieldName].indexOf(customOptionSet.customOptionSetCode) != -1;
			if (!isShow) {
				this.values[this.getTextNameKey(customOptionSet.customOptionSetCode)] = "";
				if (this.values[this.getNameKey(customOptionSet.customOptionSetCode)]) {
					if (this.values[this.getNameKey(customOptionSet.customOptionSetCode)].push) {
						this.values[this.getNameKey(customOptionSet.customOptionSetCode)].length = 0;
					} else {
						this.values[this.getNameKey(customOptionSet.customOptionSetCode)] = "";
					}
				}
			}
			return isShow;
		},
		getPlaceholder : function(customOptionSet) {
			return '请输入' + customOptionSet.customOptionSetContent;
		},
		getNameKey : function(code) {
			return code + '-' + this.index;
		},
		getTextNameKey : function(code) {
			return code + '_text-' + this.index;
		},
		getTwoLevelField : function(customOptionSet) {
			return {
				customFieldCode : customOptionSet.customOptionSetCode,
				isNecessField : this.field.isNecessField,
				fieldData : customOptionSet.childOptionList,
				parentFieldSet : customOptionSet.customFieldSet,
				parentCustomFieldCode : this.field.customFieldCode
			}
		},
		customFielsSet : function(customOptionSet) {
			return "custom-" + customOptionSet.customFieldSet.replace("Other", "");
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};
