page.form.components["custom-field"] = {
	provide : function() {
		return {
			index : this.index
		};
	},
	props : [ "field", "index" ],
	inject : [ "modular", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<transition name="van-fade" v-if="showField && field.isMobile == 1">';
		html += '	<van-cell class="custom-field">';
		html += '		<template #title>';
		html += '			<span v-if="field.isNecessField == 1" style="color: red;">*</span>';
		html += '			{{ field.customFieldName }}';
		html += '		</template>';
		html += '	</van-cell>';
		html += '</transition>';
		html += '<transition name="van-fade" v-if="showField && field.isMobile == 1" v-bind:fieldbusinessCode="field.businessCode">';
		html += '	<van-cell class="custom-field-value"><template #title><component :is="customFielsSet" :field="field"  :index="index" ></component></template></van-cell>';
		html += '</transition>';
		return html;
	})(),
	data : function() {
		return {
			rechecked : "",
			isFirst : true
		};
	},
	methods : {},
	computed : {
		customFielsSet : function() {
			if (this.field.fieldData && this.field.fieldData.length > 0) {
				var fieldSet = "label";
				for (var i = 0; i < this.field.fieldData.length; i++) {
					var realFieldSet = this.field.fieldData[i].customFieldSet.replace("Other", "");
					if (this.field.fieldData[i].customFieldSet.replace("Other", "") != "label") {
						fieldSet = realFieldSet;
						break;
					}
				}
				return "custom-" + fieldSet;
			} else {
				return "custom-" + this.field.customFieldSet;
			}
		},
		customFieldCode : function() {
			return this.field.customFieldCode + "-" + this.index;
		},
		showField : function() {
			var relationNum = this.$root.relationCodeMap[this.field.customFieldRowCode];
			var result = !relationNum || relationNum[this.index] > 0;
			
			if (!result && !this.isFirst) {
				if (assemblys.isArray(this.values[this.customFieldCode])) {
					this.values[this.customFieldCode].length = 0;
				} else {
					// 清空值
					this.values[this.customFieldCode] = "";
					
					// 清空富文本
					var ue = this.$root.param["editorCache"]['editor_' + this.field.customFieldCode + "_" + this.index];
					if (ue) {
						ue.setContent("");
					}
					
					if (this.field.customFieldSet == "org") {
						this.values['remark--' + this.customFieldCode] = "";
					}
				}
			} else {
				this.isFirst = false;
			}
			return result;
		}
	}
};