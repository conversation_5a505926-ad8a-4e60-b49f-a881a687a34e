page.form.components["custom-modular"] = {
	created : function() {
		this.modular.showModular = false;
	},
	provide : function() {
		return {
			modular : this.modular,
			values : this.modular.values,
			refs : this.modular.refs,
			dates : this.modular.dates
		};
	},
	props : [ "modular" ],
	template : (function() {
		var html = '';
		html += '		<template v-for="(i,copyIndex) in modular.count">';
		html += '			<div v-for="customField in modular.customFieldList" class="custom-field-parent">';
		html += '				<custom-field :field="customField" :index="copyIndex"></custom-field>';
		html += '			</div>';
		html += '		</template>';
		return html;
	})(),
	data : function() {
		var that = this;
		Vue.watch(this.modular, function(newVal, oldVal) {
			if (that.modular.$count == that.modular.count) {
				return;
			}
			if (that.modular.$count > that.modular.count) {
				var length = that.modular.$count - that.modular.count;
				for (var i = 0; i < length; i++) {
					that.addModularFun(that.modular.count + i);
				}
			} else {
				that.deleteModularFun();
			}
			
			that.modular.count = that.modular.$count;
		});
		return {
			isFirst : true
		};
	},
	methods : {
		addModular : function() {
			this.modular.$count++;
		},
		addModularFun : function(index) {
			var values = this.modular.values;
			for ( var key in values) {
				if (/(-0)$/.test(key)) {
					var newValue;
					if (assemblys.isArray(values[key])) {
						newValue = Vue.ref([]);
					} else {
						newValue = "";
					}
					values[key.replace(/(-0)$/, "-" + index)] = newValue;
				}
			}
			
			var refs = this.modular.refs;
			for ( var key in refs) {
				if (/(-0)$/.test(key)) {
					var newValue;
					if (assemblys.isArray(refs[key])) {
						newValue = Vue.ref([]);
					} else {
						newValue = Vue.ref(false);
					}
					refs[key.replace(/(-0)$/, "-" + index)] = newValue;
				}
			}
			
			var dates = this.modular.dates;
			for ( var key in dates) {
				if (/(-0)$/.test(key)) {
					dates[key.replace(/(-0)$/, "-" + index)] = new Date();
				}
			}
			
			// 多个副本关联处理
			var fieldList = this.modular.customFieldList;
			for (var i = 0; i < fieldList.length; i++) {
				var relationNum = this.$root.relationCodeMap[fieldList[i].customFieldRowCode];
				if (relationNum && relationNum.length > 0) {
					relationNum.push(0);
				}
			}
		},
		deleteModular : function(copyIndex) {
			if (this.modular.$count == 1) {
				return;
			}
			this.modular.$count--;
			this.modular.deleteCopyIndex = copyIndex;
		},
		deleteModularFun : function(copyIndex) {
			var copyIndex = this.modular.deleteCopyIndex;
			var values = this.modular.values;
			for ( var key in values) {
				var keySplit = key.split("-");
				var index = parseInt(keySplit[1]);
				if (index >= copyIndex) {
					values[key] = values[keySplit[0] + "-" + (index + 1)];
				}
			}
			
			for ( var key in values) {
				var keySplit = key.split("-");
				var index = parseInt(keySplit[1]);
				if (index == this.modular.$count) {
					delete values[key];
				}
			}
			
			// 多个副本关联处理
			var fieldList = this.modular.customFieldList;
			for (var i = 0; i < fieldList.length; i++) {
				var relationNum = this.$root.relationCodeMap[fieldList[i].customFieldRowCode];
				if (relationNum && relationNum.length > 0) {
					for (var j = copyIndex; j < relationNum.length; j++) {
						relationNum[j] = relationNum[j + 1];
					}
					delete relationNum[relationNum.length - 1];
				}
			}
		}
	},
	computed : {
		showModular : function() {
			var relationNum = this.$root.relationCodeMap[this.modular.customModularCode];
			var result = !relationNum || relationNum[0] > 0;
			if (!result && !this.isFirst) {
				for ( var key in this.modular.values) {
					if (assemblys.isArray(this.modular.values[key])) {
						this.modular.values[key].length = 0;
					} else {
						this.modular.values[key] = "";
						if (this.modular.values['remark--' + key]) {
							this.modular.values['remark--' + key] = "";
						}
					}
				}
			} else {
				this.isFirst = false;
			}
			this.modular.show = result;
			return result && this.modular.showModular;
		},
		hasField : function() {
			
			for (var i = 0; i < this.modular.customFieldList.length; i++) {
				if (this.modular.customFieldList[i].isMobile == 1) {
					return true;
				}
			}
			return false;
		}
	}
};