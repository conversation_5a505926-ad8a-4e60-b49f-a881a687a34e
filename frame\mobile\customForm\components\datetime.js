page.form.components["custom-datetime"] = {
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs", "dates" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="values[customFieldName]" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-cell :value="values[customFieldName]" :title="field.customFieldName" @click="onClick" is-link></van-cell>';
		html += '<van-popup v-model:show="refs[customFieldName]" position="bottom">';
		html += '	<van-datetime-picker v-model="dates[customFieldName]" :type="field.fieldVerifyType" :min-date="min" :max-date="max" :title="field.customFieldName" @confirm="onConfirm" @cancel="refs[customFieldName] = false"></van-datetime-picker>';
		html += '</van-popup>';
		return html;
	})(),
	data : function() {
		
		var max = new Date("2199/12/31 23:59:59");
		var min = new Date("1900/01/01 00:00:00");
		if (this.field.dateRange == 0) {
			min = this.field.beginDate ? new Date(this.field.beginDate) : min;
			max = this.field.endDate ? new Date(this.field.endDate) : max;
		} else if (this.field.dateRange == 1) {
			max = new Date();
		} else if (this.field.dateRange == 2) {
			min = new Date();
		}
		
		return {
			max : max,
			min : min,
			verify : this.$root.verify(this.field.isNecessField == 1 ? "limit|required|" + (this.field.fieldVerifyType || "") : "limit", {
				vueObj : this,
				limit : 200
			})
		};
	},
	methods : {
		onConfirm : function(value) {
			this.refs[this.customFieldName] = false;
			if (this.field.fieldVerifyType == "date") {
				this.values[this.customFieldName] = assemblys.dateToStr(value, "yyyy-MM-dd");
			} else {
				this.values[this.customFieldName] = assemblys.dateToStr(value);
			}
		},
		onClick : function() {
			this.refs[this.customFieldName] = true;
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};