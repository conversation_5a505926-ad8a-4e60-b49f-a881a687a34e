page.form.components["custom-file"] = {
	props : [ "field", "index" ],
	inject : [ "attaFieldValues" ],
	template : (function() {
		var html = "";
		html += '<van-uploader v-model="fileList" multiple :after-read="afterRead" @delete="deleteFile"></van-uploader>';
		return html;
	})(),
	data : function() {
		return {
			fileList : Vue.ref([])
		};
	},
	methods : {
		initFile : function() {
			var that = this;
			var customFormFilledCode = page.form.vm.param.customFormFilledCode;
			if (customFormFilledCode) {
				var that = this;
				var code = that.field.customFieldCode + "_" + that.index;
				return ajax({
					url : basePath + "frame/fileUpload/getAttachments.spring",
					data : {
						"belongToCode" : customFormFilledCode,
						"belongToSubCode" : code
					},
					skipDataCheck : true
				}).then(function(data) {
					let attaList = data.attachmentsList;
					for (var l = 0; l < attaList.length; l++) {
						let atta = attaList[l];
						
						that.fileList.push({
							url : that.$root.baseImgPath + atta.attachmentURL
						});
						
						if (!that.attaFieldValues[code]) {
							that.attaFieldValues[code] = [ {
								attaName : atta.attachmentName,
								attaUrl : atta.attachmentURL,
								attaSize : atta.attachmentSize,
								attaType : atta.attachmentType,
								attaCode : code
							} ];
						} else {
							that.attaFieldValues[code].push({
								attaName : atta.attachmentName,
								attaUrl : atta.attachmentURL,
								attaSize : atta.attachmentSize,
								attaType : atta.attachmentType,
								attaCode : code
							});
						}
					}
				});
			}
		},
		afterRead : function(file, detail) {
			var that = this;
			if (file.file) {
				file = [ file ]
			}
			for (var i = 0; i < file.length; i++) {
				let f = file[i];
				file[i].status = "uploading";
				file[i].message = "正在上传";
				let formData = new FormData();
				formData.append("file", file[i].file, file[i].file.name);
				axios.post(basePath + "frame/fileUpload/newUpload.spring", formData, {
					headers : {
						"Content-Type" : "multipart/form-data"
					}
				}).then(function(data) {
					if (data.state == "SUCCESS") {
						f.status = "done";
						f.message = "上传成功";
						
						var code = that.field.customFieldCode + "_" + that.index;
						if (!that.attaFieldValues[code]) {
							that.attaFieldValues[code] = [];
						}
						
						that.attaFieldValues[code].push({
							attaName : data.original,
							attaUrl : data.url,
							attaSize : data.size,
							attaType : data.type,
							attaCode : code
						});
						
					} else {
						f.status = "failed";
						f.message = "上传失败";
					}
				});
			}
		},
		deleteFile : function(file, detail) {
			var that = this;
			var code = that.field.customFieldCode + "_" + that.index;
			that.attaFieldValues[code].splice(detail.index, 1);
		}
	},
	created : function() {
		this.initFile();
	}
}