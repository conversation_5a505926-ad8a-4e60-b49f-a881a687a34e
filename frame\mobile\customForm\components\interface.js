page.form.components["custom-interface"] = {
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="values[customFieldName]" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-search v-model="values[customFieldName]" :show-action="field.interfaceSwitch == 1" placeholder="请输入查询内容">';
		html += '	<template v-if="field.interfaceSwitch == 1" #action>';
		html += '		<div @click="onSearch">查询</div>';
		html += '	</template>';
		html += '</van-search>';
		html += '<van-popup v-model:show="showInterfaceList" position="bottom" :style="{ height: \'70%\', backgroundColor : \'#F2F2F2\'}">';
		html += '	<div :style="{position: \'absolute\',top:\'0px\',width:\'100%\'}">';
		html += '		<van-cell :title="field.customFieldName">';
		html += '			<template #value>';
		html += '				<van-button v-show="resultAry.length > 0" type="primary" plain size="small" @click="back">返回</van-button>';
		html += '				<van-button type="primary" size="small" @click="onConfirm">确定</van-button>';
		html += '			</template>';
		html += '		</van-cell>';
		html += '	</div>';
		html += '	<div class="interface-list" :style="{position: \'absolute\',top:\'53px\',bottom:\'0px\',width:\'100%\',overflow: \'auto\'}">';
		html += '		<component :is="selectInputType" v-model="selected" inset disabled>';
		html += '			<van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">';
		html += '				<van-row v-for="(item,index) in list"  :key="item.id">';
		html += '					<van-col span="3">';
		html += '						<van-cell-group class="list-cell">';
		html += '							<van-cell clickable @click="setSelected(index)">';
		html += '								<template #title>';
		html += '									<component :is="selectInputChildType" :name="index" :shape="shape" icon-size="24px" :ref="el => checkboxs[index] = el"></component>';
		html += '								</template>';
		html += '							</van-cell>';
		html += '							<van-cell title="　"></van-cell>';
		html += '							<transition name="van-fade">';
		html += '								<div v-show="active[index]" style="max-height: 300px;overflow: hidden;">';
		html += '									<template v-for="d in item.more">';
		html += '										<van-cell title="　"></van-cell>';
		html += '									</template>';
		html += '								</div>';
		html += '							</transition>';
		html += '						</van-cell-group>';
		html += '					</van-col>';
		html += '					<van-col span="21">';
		html += '						<van-cell-group class="list-cell">';
		html += '							<van-cell :title="item.key1" :value="item.key2" clickable @click="setSelected(index)"></van-cell>';
		html += '							<van-cell class="interface-row-col-cell" :value="item.key3" is-link @click="active[index] = !active[index]">';
		html += '								<template #title>';
		html += '									<van-button v-show="nextInterfaceName" type="primary" size="mini" @click.stop="next(item.more)">{{ nextInterfaceName }}</van-button>';
		html += '								</template>';
		html += '							</van-cell>';
		html += '							<transition name="van-fade">';
		html += '								<div v-show="active[index]" style="max-height: 300px;overflow: auto;">';
		html += '									<template v-for="(d,key) in item.more">';
		html += '										<van-cell :title="d.text" :value="d.value"></van-cell>';
		html += '									</template>';
		html += '								</div>';
		html += '							</transition>';
		html += '						</van-cell-group>';
		html += '					</van-col>';
		html += '				</van-row>';
		html += '			</van-list>';
		html += '		</component>';
		html += '	</div>';
		html += '</van-popup>';
		return html;
	})(),
	data : function() {
		var list = Vue.ref([]);
		var active = Vue.ref([]);
		
		var loading = Vue.ref(false);
		var finished = Vue.ref(false);
		
		var interfaceCode = this.field.fieldVerifyType;
		
		var param = {
			selectInputType : (this.index > 0 || !this.modular.hasAdd ? "radio" : "checkbox"),
			compNo : this.$root.param.compNo,
			interfaceCode : interfaceCode,
			interfaceKeys : [],
			interfaceValues : [],
			curPageNum : 1,
			pageSize : 20
		};
		
		var selected;
		if (param.selectInputType == "checkbox") {
			selected = Vue.ref([]);
		} else {
			selected = Vue.ref("");
		}
		var selectInputType = "van-" + param.selectInputType + "-group";
		var selectInputChildType = "van-" + param.selectInputType;
		
		return {
			shape : param.selectInputType == "radio" ? "round" : "square",
			showInterfaceList : Vue.ref(false),
			list : list,
			loading : loading,
			finished : finished,
			active : active,
			selected : selected,
			param : param,
			hasFirstLoad : false,
			selectInputType : selectInputType,
			selectInputChildType : selectInputChildType,
			checkboxs : [],
			paramCache : [],
			resultAry : Vue.ref([]),
			verify : this.$root.verify(this.field.isNecessField == 1 ? "limit|required" : "limit", {
				vueObj : this,
				limit : 200
			})
		};
	},
	methods : {
		onConfirm : function() {
			var that = this;
			
			if (that.param.selectInputType == "checkbox") {
				if (that.selected.length == 0) {
					assemblys.alert("请选择数据");
					return;
				}
			} else {
				if (that.selected === "") {
					assemblys.alert("请选择数据");
					return;
				}
			}
			
			var data = {};
			for (var i = 0; i < that.resultAry.length; i++) {
				for ( var key in that.resultAry[i]) {
					data[key] = that.resultAry[i][key];
				}
			}
			
			var selectedData = that.selected
			if (that.param.selectInputType == "radio") {
				selectedData = [ selectedData ];
			}
			
			if (selectedData.length > that.modular.$count) {
				that.modular.$count = selectedData.length;
			}
			
			var interval = setInterval(function() {// watch侦听器有延时，被迫等待
				if (that.modular.$count != that.modular.count) {
					return;
				}
				for (var i = 0; i < selectedData.length; i++) {
					
					var index = selectedData[i];
					var interfaceItem = that.field.fieldData;
					for ( var key in interfaceItem) {
						var name = interfaceItem[key] + "-" + (that.index || i);
						var value = (that.list[index].more[key] ? that.list[index].more[key].value : (data[key] ? data[key].value : "")) || "";
						var field = that.modular.customFieldMap[interfaceItem[key]];
						if (assemblys.isArray(that.values[name])) {
							if (value) {
								for (var j = 0; j < field.fieldData.length; j++) {
									if (field.fieldData[j].remark == value) {
										value = field.fieldData[j].customOptionSetCode;
										break;
									}
								}
							}
							that.values[name].length = 0;
							that.values[name].push(value);
						} else {
							if (value) {
								if (field.customFieldSet == "radio") {
									for (var j = 0; j < field.fieldData.length; j++) {
										if (field.fieldData[j].remark == value) {
											value = field.fieldData[j].customOptionSetCode;
											break;
										}
									}
								} else if (field.customFieldSet == "datetime") {
									if (field.fieldVerifyType == "date") {
										value = assemblys.dateToStr(value, "yyyy/MM/dd");
									} else {
										value = assemblys.dateToStr(value, "yyyy/MM/dd HH:mm");
									}
								}
							}
							that.values[name] = value;
						}
					}
				}
				
				that.selectedReset();
				that.showInterfaceList = false;
				
				clearInterval(interval);
			}, 100);
			
		},
		setSelected : function(index) {
			var that = this;
			if (that.param.selectInputType == "checkbox") {
				var has = -1;
				for (var i = 0; i < that.selected.length; i++) {
					if (that.selected[i] == index) {
						has = i;
						break;
					}
				}
				
				if (has == -1) {
					that.selected.push(index);
				} else {
					that.selected.splice(has, 1);
				}
			} else {
				that.selected = index;
			}
		},
		onLoad : function() {
			var that = this;
			if (that.hasFirstLoad) {// 第一次不加载
				that.getInterfaceQueryList();
			} else {
				that.hasFirstLoad = true;
				that.getRelationInterface();
			}
		},
		onSearch : function() {// 点击查询清空数据重新加载
		
			if (this.values[this.customFieldName] == "") {
				assemblys.alert("接口【" + this.field.customFieldName + "】的查询内容不能为空");
				return;
			}
			
			var interfaceKeys = [];
			var interfaceValues = [];
			var interfaceFieldData = this.field.fieldData;
			
			for ( var i in this.field.interfaceParam) {
				// 是入参时
				var fieldName = this.field.interfaceParam[i].otherCustomFieldCode + "-" + this.index;
				var paramName = i;
				var paramValue = this.values[fieldName];
				// 从控件检索值
				if (paramValue) {
					this.param.interfaceKeys.push(paramName);
					this.param.interfaceValues.push(paramValue);
				}
			}
			
			this.param.curPageNum = 1;
			this.list.length = 0;
			this.active.length = 0;
			this.showInterfaceList = true;
			this.getInterfaceQueryList();
		},
		getInterfaceQueryList : function() {
			var that = this;
			var data = "1=1";
			data += "&interfaceCode=" + that.param.interfaceCode;
			data += "&curPageNum=" + that.param.curPageNum++;
			data += "&pageSize=" + that.param.pageSize;
			data += "&compNo=" + that.param.compNo;
			for (var i = 0; i < that.param.interfaceKeys.length; i++) {
				data += "&keys=" + that.param.interfaceKeys[i];
				data += "&values=" + that.param.interfaceValues[i];
			}
			
			ajax({
				url : basePath + "frame/interface/getInterfaceQueryList.spring",
				data : data,
				type : "post",
				skipDataCheck : true
			}).then(function(data) {
				if (data.result == "success") {
					for (var i = 0; i < data.queryList.length; i++) {
						var item = {
							more : {}
						};
						for ( var key in data.queryList[i]) {
							item.more[key] = {
								text : data.itemList[key],
								value : data.queryList[i][key]
							};
						}
						for (var j = 0, k = 1; j < data.keyList.length; j++) {
							item["key" + k] = data.queryList[i][data.keyList[j].itemName];
							k++;
						}
						that.list.push(item);
						that.active.push(false);
					}
					
					// 加载状态结束
					that.loading = false;
					that.finished = data.totalPage <= data.curPageNum * data.pageSize || data.totalPage == 0;
				} else if (data.resultMessage == "noneData") {
					that.loading = false;
					that.finished = true;
				}
			});
			
		},
		next : function(data) {
			var that = this;
			// 缓存当前列表参数
			that.paramCache.push(that.param);
			
			var interfaceInfo = that.relationInterfaceList[that.param.interfaceCode];
			var keys = interfaceInfo.keys;
			
			var values = [];
			for ( var i in keys) {
				values.push(data[keys[i]].value);
			}
			
			var selectInputType = that.param.selectInputType;
			var relationInterfaceCode = interfaceInfo.relationInterfaceCode;
			that.param = {
				selectInputType : selectInputType,
				compNo : that.$root.param.compNo,
				interfaceCode : relationInterfaceCode,
				interfaceKeys : keys,
				interfaceValues : values,
				curPageNum : 1,
				pageSize : 20
			};
			
			that.resultAry.push(data);
			
			that.list.length = 0;
			that.active.length = 0;
			that.selectedReset();
			that.getInterfaceQueryList();
		},
		back : function() {
			var that = this;
			that.param = that.paramCache.pop();
			that.param.curPageNum = 1;
			that.param.pageSize = 20;
			that.resultAry.pop();
			
			that.list.length = 0;
			that.active.length = 0;
			
			that.selectedReset();
			that.getInterfaceQueryList();
		},
		selectedReset : function() {
			var that = this;
			if (that.param.selectInputType == "checkbox") {
				that.selected.length = 0;
			} else {
				that.selected = "";
			}
		},
		getRelationInterface : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/interface/getRelationInterface.spring",
				data : {
					"interfaceCode" : that.param.interfaceCode,
					"customFieldCode" : "",
					"compNo" : that.param.compNo,
					"appCode" : ""
				},
				skipDataCheck : true
			}).then(function(data) {
				if (data.result == "success") {
					that.relationInterfaceList = data.relationInterfaceList;
				} else {
					assemblys.alert("获取接口信息出错");
				}
			});
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		},
		nextInterfaceName : function() {
			var that = this;
			var interfaceInfo = that.relationInterfaceList[that.param.interfaceCode];
			var relationInterfaceCode = interfaceInfo.relationInterfaceCode;
			var nextInterfaceInfo = that.relationInterfaceList[relationInterfaceCode];
			
			if (!nextInterfaceInfo) {
				return "";
			}
			
			return nextInterfaceInfo.interfaceName;
		}
	},
	watch : {
		showInterfaceList : function(val) {
			if (!val) {
				if (this.paramCache[0]) {
					this.param = this.paramCache[0];
				}
				
				this.param.curPageNum = 1;
				this.param.pageSize = 20;
				this.param.interfaceKeys.length = 0;
				this.param.interfaceValues.length = 0;
				
				this.selectedReset();
				
				this.list.length = 0;
				this.active.length = 0;
			}
		}
	}
};