page.form.components["custom-org"] = {
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="values[customFieldName]" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-field v-model="values[\'remark--\' + customFieldName]" is-link readonly :label="field.relationField == \'dept\' ? \'科室\' : \'人员\'" placeholder="请选择" @click="showPickerFun"></van-field>';
		html += '<van-field v-show="false" v-model="values[customFieldName]"></van-field>';
		html += '<van-popup v-model:show="showPicker" round position="bottom">';
		html += '	<van-picker :columns="columns" swipe-duration="300" @cancel="showPicker = false" @confirm="onConfirm" :ref="el => picker = el" @change="pickerOnChange" :loading="loading">';
		html += '		<template #title>';
		html += '			<van-search v-model="searchValue" :placeholder="field.customFieldName" @update:model-value="onSearch"></van-search>';
		html += '		</template>';
		html += '	</van-picker>';
		html += '</van-popup>';
		return html;
	})(),
	data : function() {
		return {
			columns : Vue.ref([ {
				children : []
			} ]),
			showPicker : Vue.ref(false),
			picker : Vue.ref(null),
			searchValue : Vue.ref(""),
			verify : this.$root.verify(this.field.isNecessField == 1 ? "limit|required" : "limit", {
				vueObj : this,
				limit : 200
			}),
			loading : Vue.ref(true)
		};
	},
	methods : {
		showPickerFun : function() {
			this.loading = true;
			this.showPicker = true;
			var that = this;
			that.orgParam = {
				type : that.field.relationField,
				funCode : that.field.funCode,
				model : "radio",
				selectValues : "",
				currentValue : "",
				callbackDom : "",
				returnType : "value",
				hasCheck : "false",
				setValue : "code"
			}

			// 有权限时，要开启医院
			if (that.orgParam.funCode) {
				that.orgParam.allComp = "1";
			} else {
				that.orgParam.compNo = that.$root.param.compNo;
			}
			
			var value = that.values[this.customFieldName] || "";
			if (value) {
				value = value.split("/")[0];
			}
			
			// 如果显示所有医院，加载医院
			if (that.orgParam.allComp == "1") {
				that.getCompList().then(function(datas) {
					if (that.orgParam.type == "user") {
						// 用户
						var userIndex = -1;
						if (!datas.userList) {
							datas.userList = [];
						} else {
							for (var i = 0; i < datas.userList.length; i++) {
								datas.userList[i].text = datas.userList[i].userName;
								datas.userList[i].value = datas.userList[i].userCode;
								datas.userList[i].org = "user";
								if (value == datas.userList[i].userCode) {
									userIndex = i;
								}
							}
							
							if (userIndex > 0) {
								value = datas.userList[userIndex].deptID;
							}
						}
						that.valueColumns = datas.userList;
					} else {
						that.valueColumns = datas.deptList;
					}
					
					// 科室
					var deptIndex = -1;
					if (!datas.deptList) {
						datas.deptList = [];
					} else {
						for (var i = 0; i < datas.deptList.length; i++) {
							datas.deptList[i].text = datas.deptList[i].DeptName;
							datas.deptList[i].value = datas.deptList[i].DeptID;
							datas.deptList[i].org = "dept";
							if (value == datas.deptList[i].DeptID) {
								deptIndex = i;
							}
						}
						
						if (deptIndex > 0) {
							value = datas.deptList[deptIndex].CompNo;
						}
					}
					
					// 医院
					var compIndex = 0;
					if (!datas.compList) {
						datas.compList = [];
					}
					for (var i = 0; i < datas.compList.length; i++) {
						datas.compList[i].text = datas.compList[i].compName;
						datas.compList[i].value = datas.compList[i].compNo;
						datas.compList[i].org = "company";
						if (value == datas.compList[i].compNo) {
							compIndex = i;
						}
					}
					
					that.columns = [ {
						values : datas.compList,
						defaultIndex : compIndex
					}, {
						values : datas.deptList,
						defaultIndex : deptIndex
					} ];
					
					if (that.orgParam.type == "user") {
						that.columns.push({
							values : datas.userList,
							defaultIndex : userIndex
						})
					}
					
					that.loading = false;
				});
			} else {
				that.getDeptList({
					compNo : that.orgParam.compNo
				}).then(function(datas) {
					if (that.orgParam.type == "user") {
						// 用户
						var userIndex = -1;
						if (!datas.userList) {
							datas.userList = [];
						} else {
							for (var i = 0; i < datas.userList.length; i++) {
								datas.userList[i].text = datas.userList[i].userName;
								datas.userList[i].value = datas.userList[i].userCode;
								datas.userList[i].org = "user";
								if (value == datas.userList[i].userCode) {
									userIndex = i;
								}
							}
							
							if (userIndex > 0) {
								value = datas.userList[userIndex].deptID;
							}
						}
						that.valueColumns = datas.userList;
					} else {
						that.valueColumns = datas.deptList;
					}
					
					// 科室
					var deptIndex = -1;
					if (!datas.deptList) {
						datas.deptList = [];
					} else {
						for (var i = 0; i < datas.deptList.length; i++) {
							datas.deptList[i].text = datas.deptList[i].DeptName;
							datas.deptList[i].value = datas.deptList[i].DeptID;
							datas.deptList[i].org = "dept";
							if (value == datas.deptList[i].DeptID) {
								deptIndex = i;
							}
						}
						
						if (deptIndex > 0) {
							value = datas.deptList[deptIndex].CompNo;
						}
					}
					
					that.columns = [ {
						values : datas.deptList,
						defaultIndex : deptIndex
					} ];
					
					if (that.orgParam.type == "user") {
						that.columns.push({
							values : datas.userList,
							defaultIndex : userIndex
						})
					}
					that.loading = false;
				});
			}
		},
		onConfirm : function(value) {
			this.values[this.customFieldName] = value[value.length - 1].value + "/" + value[value.length - 1].text;
			this.values["remark--" + this.customFieldName] = value[value.length - 1].text;
			this.showPicker = false;
		},
		onSearch : function(value) {
			if (value) {
				value = value.trim();
			}
			var that = this;
			var columns = [];
			for (var i = 0; i < that.valueColumns.length; i++) {
				var column = that.valueColumns[i];
				if (!value || column.text.indexOf(value) != -1) {
					columns.push(column);
				}
			}
			that.columns[that.columns.length - 1].values = columns;
		},
		pickerOnChange : function(values, columnIndex) {
			if (values[columnIndex].org == "user") {
				return;
			}
			var that = this;
			if (values[columnIndex].org == "dept") {
				that.getUserList({
					deptID : values[columnIndex].DeptID
				}).then(function(datas) {
					that.setColumnValues(datas.userList, "userName", columnIndex + 1);
					that.valueColumns = datas.userList;
				});
			} else {
				that.getDeptList({
					compNo : values[columnIndex].compNo
				}).then(function(datas) {
					that.setColumnValues(datas.deptList, "DeptName", columnIndex + 1);
					if (values[columnIndex].org == "dept") {
						that.valueColumns = datas.deptList;
					}
				});
			}
		},
		setColumnValues : function(list, nameKey, index) {
			for (var i = 0; i < list.length; i++) {
				list[i].text = list[i][nameKey];
				list[i].children = [];
			}
			this.picker.setColumnValues(index, list);
		},
		getCompList : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/common/getCompList.spring",
				data : {
					funCode : that.orgParam.funCode,
					// 有医院权限才要传医院
					compNo : that.orgParam.funCode ? that.orgParam.compNo : ""
				},
				skipDataCheck : true,
			}).then(function(data) {
				if (data.result == "success") {
					return data.compList;
				} else {
					assemblys.alert("获取医院列表出错，请刷新重试");
				}
			}).then(function(compList) {
				var compNo = "";
				if (compList && compList.length > 0) {
					compNo = compList[0].compNo;
				}
				return that.getDeptList({
					compList : compList,
					compNo : compNo
				});
			});
		},
		getDeptList : function(datas) {
			var that = this;
			var compNo = that.orgParam.compNo;
			if (datas.compNo) {
				compNo = datas.compNo;
			}
			return ajax({
				url : basePath + "frame/common/getDeptList.spring",
				data : {
					compNo : compNo,
					funCode : that.orgParam.funCode
				},
				skipDataCheck : true
			}).then(function(data) {
				if (data.result == "success") {
					if (data.deptList && data.deptList.length > 0) {
						datas.deptID = data.deptList[0].DeptID;
					}
					datas.deptList = data.deptList;
					if (that.orgParam.type == "user") {
						return that.getUserList(datas);
					} else {
						return datas;
					}
				} else {
					assemblys.alert("获取科室列表出错，请刷新重试");
				}
			});
		},
		getUserList : function(datas) {
			var that = this;
			var compNo = that.orgParam.compNo;
			if (datas.compNo) {
				compNo = datas.compNo;
			}
			var deptID = that.orgParam.deptID;
			if (datas.deptID) {
				deptID = datas.deptID;
			}
			return ajax({
				url : basePath + "frame/common/getUserList.spring",
				dataType : "json",
				data : {
					compNo : deptID == "0" ? compNo : "",
					deptID : deptID == "0" ? "" : deptID
				}
			}).then(function(data) {
				datas.userList = data.userList;
				return datas;
			});
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};