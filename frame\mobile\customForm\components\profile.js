page.form.components["custom-profile"] = {
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="values[customFieldName]" :name="customFieldName" class="vue-field-verify-hide" ></van-field>';
		html += '<van-field :type="field.customFieldSet" v-model="values[customFieldName]" style="display:none;" ></van-field>';
		html += '<img style="max-width:150px;" :src="getImgurl()" />';
		return html;
	})(),
	data : function() {
	},
	methods : {
		getImgurl : function() {
			return this.values[this.customFieldName] || basePath + "/frame/images/tx.png";
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};