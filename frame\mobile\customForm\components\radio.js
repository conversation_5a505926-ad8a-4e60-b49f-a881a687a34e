page.form.components["custom-radio"] = {
	created : function() {
		if (!this.$root.param.customFormFilledCode) {
			for (var i = 0; i < this.field.fieldData.length; i++) {
				if (this.field.fieldData[i].hasDefault == 1) {
					this.radioOnClick(this.field.fieldData[i].customOptionSetCode);
					this.values[this.customFieldName] = this.field.fieldData[i].customOptionSetCode;
					this.radioOnChange(this.field.fieldData[i].customOptionSetCode);
					break;
				}
			}
		}
	},
	name : "custom-radio",
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs", "formVerify" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="values[customFieldName]" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-radio-group v-model="values[customFieldName]" inset @change="radioOnChange" disabled>';
		html += '	<div v-for="customOptionSet in field.fieldData">';
		html += '		<van-cell :title="customOptionSet.customOptionSetContent" clickable @click="radioOnClick(customOptionSet.customOptionSetCode)">';
		html += '			<template #right-icon>';
		html += '				<van-radio v-if="isRadio(customOptionSet.customFieldSet)" :name="customOptionSet.customOptionSetCode"></van-radio>';
		html += '			</template>';
		html += '		</van-cell>';
		html += '		<transition v-if="isOther(customOptionSet) && isShow(customOptionSet)" name="van-fade">';
		html += '			<van-cell>';
		html += '				<template #title>';
		html += '					<van-field v-model="values[getTextNameKey(customOptionSet.customOptionSetCode)]" :placeholder="getPlaceholder(customOptionSet)" :rules="verifyInput"></van-field>';
		html += '				</template>';
		html += '			</van-cell>';
		html += '		</transition>';
		html += '		<transition v-if="customOptionSet.childOptionList.length > 0" name="van-fade" v-show="isShow(customOptionSet)">';
		html += '			<van-cell class="twoLevel">';
		html += '				<template #title>';
		html += '					<component :is="customFielsSet(customOptionSet.childOptionList[0])" :field="getTwoLevelField(customOptionSet)"></component>';
		html += '				</template>';
		html += '			</van-cell>';
		html += '		</transition>';
		html += '	</div>';
		html += '</van-radio-group>';
		return html;
	})(),
	data : function() {
		
		return {
			verifyInput : this.$root.verify("required|limit", {
				vueObj : this
			}),
			verify : this.field.isNecessField == 1 ? this.$root.verify("required", {
				vueObj : this
			}) : [],
			_checked : this.values[this.field.customFieldCode + "-" + this.index]
		};
	},
	methods : {
		radioOnChange : function(val) {
			var that = this;
			val = that.values[that.customFieldName];
			if (that._checked) {
				that.changeRelationNum(that._checked, 0);
			}
			
			if (val) {
				that.changeRelationNum(val, 1);
			}
			
			that._checked = val;
		},
		radioOnClick : function(customOptionSetCode) {
			if (!this.field.disabled) {
				// 如果之前有选中项,先检查关联项
				if (this.values[this.customFieldName] && this.checkRelationNum(this.values[this.customFieldName], customOptionSetCode)) {
					return;
				}
				
				this.radioOnClickFun(customOptionSetCode);
			}
		},
		radioOnClickFun : function(customOptionSetCode) {
			if (!this.field.disabled) {
				var that = this;
				var flag = that.values[that.customFieldName] == customOptionSetCode;
				that.radioOnChange(""); // 短时间修改that.values[that.customFieldName]值2次只会触发最后一次onchange事件,可能是vue的一些机制导致
				that.values[that.customFieldName] = "";
				if (!flag) {
					that.values[that.customFieldName] = customOptionSetCode;
				}
			}
		},
		checkRelationNum : function(customOptionSetCode, nextValue) {// 检查是否有需要隐藏的关联内容
			var that = this;
			var customModularCodeAry = that.$root.customOptionSetCodeMap[customOptionSetCode];
			var nextCustomModularCodeAry = that.$root.customOptionSetCodeMap[nextValue];
			if (customModularCodeAry) {
				for (var i = 0; i < customModularCodeAry.length; i++) {
					if (nextCustomModularCodeAry && nextCustomModularCodeAry.indexOf(customModularCodeAry[i]) != -1) {
						continue;
					}
					var num = that.$root.relationCodeMap[customModularCodeAry[i]][that.index];
					num--;
					if (num == 0) {
						var customOptionSetContent = "";
						for (var j = 0; j < that.field.fieldData.length; j++) {
							if (that.field.fieldData[j].customOptionSetCode == customOptionSetCode) {
								customOptionSetContent = that.field.fieldData[j].customOptionSetContent;
								break;
							}
						}
						vant.Dialog.confirm({
							message : "取消『" + customOptionSetContent + "』选项会把它所关联的选项内容清空，确定要取消吗？",
						}).then(function() {
							that.radioOnClickFun(nextValue);
						});
						return true;
					}
				}
			}
			return false;
		},
		changeRelationNum : function(customOptionSetCode, type) {// x的作用是继续循环遍历其他关联项隐藏
			var that = this;
			var customModularCodeAry = that.$root.customOptionSetCodeMap[customOptionSetCode];
			if (customModularCodeAry) {
				for (var i = 0; i < customModularCodeAry.length; i++) {
					var num = that.$root.relationCodeMap[customModularCodeAry[i]][that.index];
					if (type == 1) { // 等于1选中,累加
						num++;
					} else {
						num--;
						if (num < 0) {// 不能少于0
							num = 0;
						}
					}
					that.$root.relationCodeMap[customModularCodeAry[i]][that.index] = num;
				}
			}
		},
		isRadio : function(customFieldSet) {
			return customFieldSet.replace("Other", "") == "radio";
		},
		isOther : function(customOptionSet) {
			return customOptionSet.customFieldSet == 'radioOther';
		},
		isShow : function(customOptionSet) {
			var isShow = this.values[this.customFieldName] == customOptionSet.customOptionSetCode;
			if (!isShow) {
				this.values[this.getTextNameKey(customOptionSet.customOptionSetCode)] = "";
				if (this.values[this.getNameKey(customOptionSet.customOptionSetCode)]) {
					if (this.values[this.getNameKey(customOptionSet.customOptionSetCode)].push) {
						this.values[this.getNameKey(customOptionSet.customOptionSetCode)].length = 0;
					} else {
						this.values[this.getNameKey(customOptionSet.customOptionSetCode)] = "";
					}
				}
			}
			return isShow;
		},
		getPlaceholder : function(customOptionSet) {
			return '请输入' + customOptionSet.customOptionSetContent;
		},
		getNameKey : function(code) {
			return code + "-" + this.index;
		},
		getTextNameKey : function(code) {
			return code + '_text-' + this.index;
		},
		getTwoLevelField : function(customOptionSet) {
			return {
				customFieldCode : customOptionSet.customOptionSetCode,
				isNecessField : this.field.isNecessField,
				fieldData : customOptionSet.childOptionList,
				parentFieldSet : customOptionSet.customFieldSet,
				parentCustomFieldCode : this.field.customFieldCode
			}
		},
		customFielsSet : function(customOptionSet) {
			return "custom-" + customOptionSet.customFieldSet.replace("Other", "");
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};
