page.form.components["custom-select"] = {
	created : function() {
		let v = this.values[this.customFieldName];
		if (v) {
			for (var i = 0; i < v.length; i++) {
				this.checkeds.push(v[i]);
			}
		}
	},
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="value" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-field v-model="value" v-show="false"></van-field>';
		html += '<van-field label="请选择" v-model="fieldDataMap[value].customOptionSetContent" is-link readonly :placeholder="field.customFieldName" @click="onClick"></van-field>';
		html += '<van-popup v-model:show="refs[customFieldName]" round position="bottom">';
		html += '	<div class="van-cascader__header">';
		html += '		<h2 class="van-cascader__title">{{ field.customFieldName }}</h2>';
		html += '		<van-search v-model="searchValue" placeholder="在当前列表筛选" @update:model-value="onSearch"></van-search>';
		html += '		<i class="van-badge__wrapper van-icon van-icon-cross van-cascader__close-icon van-haptics-feedback" @click="onClose"></i>';
		html += '	</div>';
		html += '	<div class="van-swipe__track" style="width:100%;overflow-x: auto;">';
		html += '		<div :class="getClass(tab)" v-for="(tab,index) in tabs" @click="tabOnClick(tab,index)">';
		html += '			{{ tab.customOptionSetContent }}';
		html += '		</div>';
		html += '	</div>';
		html += '	<div class="van-tabs__content van-tabs__content--animated">';
		html += '		<div class="van-swipe van-tabs__track">';
		html += '			<div class="van-swipe__track" style="transition-duration: 300ms; transform: translateX(0px); width: 100%;">';
		html += '				<div class="van-tab__panel-wrapper" role="tabpanel" aria-hidden="false" style="width: 100%;">';
		html += '					<div class="van-tab__panel">';
		html += '						<ul role="menu" class="van-cascader__options">';
		html += '							<li v-for="(option,index) in selectList" role="menuitemradio" class="van-cascader__option" aria-checked="false" @click="onChange(option)">';
		html += '								<span>{{ option.customOptionSetContent }}</span>';
		html += '							</li>';
		html += '						</ul>';
		html += '					</div>';
		html += '				</div>';
		html += '			</div>';
		html += '		</div>';
		html += '	</div>';
		html += '</van-popup>';
		return html;
	})(),
	data : function() {
		var that = this;
		var fieldName = this.field.customFieldCode + "-" + this.index;
		var fieldDataMap = {
			"" : {
				customOptionSetContent : ""
			}
		};
		var fieldData = [];
		var parentMap = {};
		var tabs = [ {
			customOptionSetCode : "",
			customOptionSetContent : "请选择"
		} ];
		
		for (var i = 0; i < this.field.fieldData.length; i++) {
			fieldDataMap[this.field.fieldData[i].customOptionSetCode] = this.field.fieldData[i];
			
			if (!this.field.fieldData[i].parentCustomOptionSetCode) {
				fieldData.push(this.field.fieldData[i]);
			}
			
			if (!parentMap[this.field.fieldData[i].parentCustomOptionSetCode]) {
				parentMap[this.field.fieldData[i].parentCustomOptionSetCode] = [];
			}
			parentMap[this.field.fieldData[i].parentCustomOptionSetCode].push(this.field.fieldData[i]);
		}
		
		var fun = function(list) {
			for (var i = 0; i < list.length; i++) {
				if (list[i].hasDefault == 1) {
					that.values[fieldName].push(list[i].customOptionSetCode);
					if (tabs[0].customOptionSetCode == "") {
						tabs[0] = list[i];
					} else {
						tabs.push(list[i]);
					}
				}
				
				var children = parentMap[list[i].customOptionSetCode];
				if (children && children.length > 0) {
					fun(children);
				}
			}
		}

		if (!this.$root.param.customFormFilledCode) {
			fun(fieldData);
		} else {
			if (this.values[fieldName]) {
				if (!assemblys.isArray(this.values[fieldName])) {
					this.values[fieldName] = [ this.values[fieldName] ];
				}
				for (var i = 0; i < this.values[fieldName].length; i++) {
					var tab = fieldDataMap[this.values[fieldName][i]];
					if (i == 0) {
						tabs[0] = tab;
					} else {
						tabs.push(tab);
					}
				}
			}
		}
		
		var that = this;
		Vue.watch(that.values[that.field.customFieldCode + "-" + that.index], function(checkeds, oldcheckeds) {
			for (var i = 0; i < that.checkeds.length; i++) {
				var flag = false;
				for (var j = 0; j < checkeds.length; j++) {
					if (that.checkeds[i] == checkeds[j]) {
						flag = true;
						break;
					}
				}
				
				if (!flag) {
					that.changeRelationNum(that.checkeds[i], 0);
				}
			}
			
			for (var i = 0; i < checkeds.length; i++) {
				var flag = false;
				for (var j = 0; j < that.checkeds.length; j++) {
					if (that.checkeds[j] == checkeds[i]) {
						flag = true;
						break;
					}
				}
				
				if (!flag) {
					that.changeRelationNum(checkeds[i], 1);
				}
			}
			
			that.checkeds.length = 0;
			for (var i = 0; i < checkeds.length; i++) {
				that.checkeds.push(checkeds[i]);
			}
			
			if (checkeds.length > 0) {
				that.value = checkeds[checkeds.length - 1];
			} else {
				that.value = "";
			}
		});
		return {
			tabs : tabs,
			tabSelected : tabs[tabs.length - 1],
			currIndex : tabs.length - 1,
			selectList : parentMap[tabs[tabs.length - 1].parentCustomOptionSetCode || ""],
			selectListTemp : parentMap[tabs[tabs.length - 1].parentCustomOptionSetCode || ""],
			fieldDataMap : fieldDataMap,
			parentMap : parentMap,
			value : tabs[tabs.length - 1].customOptionSetCode,
			checkeds : [],
			verify : this.field.isNecessField == 1 ? this.$root.verify("required", {
				vueObj : this,
				limit : 200
			}) : [],
			searchValue : Vue.ref("")
		};
	},
	methods : {
		onSearch : function(value) {
			value = value.trim();
			var selectList = [];
			for (var i = 0; i < this.selectListTemp.length; i++) {
				if (this.selectListTemp[i].customOptionSetContent.indexOf(value) != -1) {
					selectList.push(this.selectListTemp[i]);
				}
			}
			this.selectList = selectList;
		},
		getClass : function(tab) {
			return this.tabSelected == tab ? "van-swipe-item custom-form-select-li" : "van-swipe-item";
		},
		tabOnClick : function(tab, index) {
			this.tabSelected = tab;
			this.selectList = this.parentMap[tab.parentCustomOptionSetCode];
			this.selectListTemp = this.parentMap[tab.parentCustomOptionSetCode];
			this.searchValue = "";
			this.currIndex = index;
		},
		onFinish : function(values) {
			this.refs[this.customFieldName] = false;
		},
		onClose : function(values) {
			this.refs[this.customFieldName] = false;
		},
		onChange : function(option) {
			// 如果之前有选中项,先检查关联项
			if (this.values[this.customFieldName].length > 0 && this.checkRelationNum(option)) {
				return;
			}
			this.selectOnChangeFun(option);
		},
		selectOnChangeFun : function(option) {
			
			this.tabs = this.tabs.slice(0, this.currIndex + 1);
			this.tabs[this.currIndex] = option;
			var children = this.parentMap[option.customOptionSetCode];
			
			if (children && children.length > 0) {
				this.currIndex++;
				var currSelected = {
					customOptionSetCode : "",
					customOptionSetContent : "请选择"
				};
				this.tabs.push(currSelected);
				this.tabSelected = currSelected;
				this.selectList = children;
				this.selectListTemp = children;
				this.searchValue = "";
			} else {
				this.tabSelected = option;
				this.onFinish();
			}
			
			this.values[this.customFieldName].length = 0;
			for (var i = 0; i < this.tabs.length; i++) {
				this.values[this.customFieldName].push(this.tabs[i].customOptionSetCode);
			}
		},
		checkRelationNum : function(option) {// 检查是否有需要隐藏的关联内容
			var that = this;
			var tabs = this.tabs.slice(0, this.currIndex + 1);
			tabs[this.currIndex] = option;
			for (var i = this.values[this.customFieldName].length - 1; i >= 0; i--) {
				var customModularCodeAry = that.$root.customOptionSetCodeMap[that.values[that.customFieldName][i]];
				if (customModularCodeAry) {
					for (var j = 0; j < customModularCodeAry.length; j++) {
						var num = that.$root.relationCodeMap[customModularCodeAry[j]][that.index];
						num--;
						if (num == 0) {
							var has = false;
							for (var k = 0; k < tabs.length; k++) {
								if (tabs[k].customOptionSetCode == this.values[this.customFieldName][i]) {
									has = true;
								}
							}
							
							if (has) {
								continue;
							}
							
							var customOptionSetContent = that.fieldDataMap[that.values[that.customFieldName][i]].customOptionSetContent;
							assemblys.confirm("取消『" + customOptionSetContent + "』选项会把它所关联的选项内容清空，确定要取消吗？", function() {
								that.selectOnChangeFun(option);
							}, function() {
								that.value = that.values[that.customFieldName][that.values[that.customFieldName].length - 1];
								that.refs[that.customFieldName] = true;
							});
							return true;
						}
					}
				}
			}
			return false;
		},
		changeRelationNum : function(customOptionSetCode, type) {// x的作用是继续循环遍历其他关联项隐藏
			var that = this;
			var customModularCodeAry = that.$root.customOptionSetCodeMap[customOptionSetCode];
			if (customModularCodeAry) {
				for (var i = 0; i < customModularCodeAry.length; i++) {
					var num = that.$root.relationCodeMap[customModularCodeAry[i]][that.index];
					if (type == 1) { // 等于1选中,累加
						num++;
					} else {
						num--;
						if (num < 0) {// 不能少于0
							num = 0;
						}
					}
					that.$root.relationCodeMap[customModularCodeAry[i]][that.index] = num;
				}
			}
			return true;
		},
		onClose : function() {
			this.value = "";
			this.refs[this.customFieldName] = false;
		},
		onClick : function() {
			this.refs[this.customFieldName] = true;
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		},
	}
};