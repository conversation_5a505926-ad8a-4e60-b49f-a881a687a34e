page.form.components["custom-select"] = {
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="value" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-field v-model="value" v-show="false"></van-field>';
		html += '<van-field label="请选择" v-model="fieldDataMap[value].customOptionSetContent" is-link readonly :placeholder="field.customFieldName" @click="onClick"></van-field>';
		html += '<van-popup v-model:show="refs[customFieldName]" round position="bottom">';
		html += '	<van-cascader v-model="value" :title="field.customFieldName" :options="fieldData" active-color="var(--van-primary-color)" @close="onClose" @finish="onFinish" @change="onChange"></van-cascader>';
		html += '</van-popup>';
		return html;
	})(),
	data : function() {
		var that = this;
		var fieldName = this.field.customFieldCode + "-" + this.index;
		var fieldDataMap = {
			"" : {
				customOptionSetContent : ""
			}
		};
		for (var i = 0; i < this.field.fieldData.length; i++) {
			this.field.fieldData[i].text = this.field.fieldData[i].customOptionSetContent;
			this.field.fieldData[i].value = this.field.fieldData[i].customOptionSetCode;
			this.field.fieldData[i].children = [];
			fieldDataMap[this.field.fieldData[i].customOptionSetCode] = this.field.fieldData[i];
		}
		var fieldData = [];
		for (var i = 0; i < this.field.fieldData.length; i++) {
			if (!this.field.fieldData[i].parentCustomOptionSetCode) {
				fieldData.push(this.field.fieldData[i]);
			} else {
				var parentCustomOptionSet = fieldDataMap[this.field.fieldData[i].parentCustomOptionSetCode];
				if (parentCustomOptionSet) {
					parentCustomOptionSet.children.push(this.field.fieldData[i]);
				}
			}
			
		}
		
		var fun = function(list) {
			for (var i = 0; i < list.length; i++) {
				if (list[i].hasDefault == 1) {
					that.values[fieldName].push(list[i].customOptionSetCode);
				}
				if (list[i].children.length > 0) {
					fun(list[i].children);
				}
			}
		}

		if (!this.$root.param.customFormFilledCode) {
			fun(fieldData);
		}
		
		for (var i = 0; i < this.field.fieldData.length; i++) {
			if (this.field.fieldData[i].children.length == 0) {
				this.field.fieldData[i].children = null;
			}
		}
		
		var that = this;
		Vue.watch(that.values[that.field.customFieldCode + "-" + that.index], function(checkeds, oldcheckeds) {
			for (var i = 0; i < that.checkeds.length; i++) {
				var flag = false;
				for (var j = 0; j < checkeds.length; j++) {
					if (that.checkeds[i] == checkeds[j]) {
						flag = true;
						break;
					}
				}
				
				if (!flag) {
					that.changeRelationNum(that.checkeds[i], 0);
				}
			}
			
			for (var i = 0; i < checkeds.length; i++) {
				var flag = false;
				for (var j = 0; j < that.checkeds.length; j++) {
					if (that.checkeds[j] == checkeds[i]) {
						flag = true;
						break;
					}
				}
				
				if (!flag) {
					that.changeRelationNum(checkeds[i], 1);
				}
			}
			
			that.checkeds.length = 0;
			for (var i = 0; i < checkeds.length; i++) {
				that.checkeds.push(checkeds[i]);
			}
			
			if (checkeds.length > 0) {
				that.value = checkeds[checkeds.length - 1];
			} else {
				that.value = "";
			}
		});
		
		var v = this.values[fieldName] || [ "" ];
		return {
			fieldData : fieldData,
			fieldDataMap : fieldDataMap,
			value : Vue.ref(v[v.length - 1] || ""),
			checkeds : [],
			verify : this.field.isNecessField == 1 ? this.$root.verify("required", {
				vueObj : this,
				limit : 200
			}) : []
		};
	},
	methods : {
		onFinish : function(values) {
			this.refs[this.customFieldName] = false;
		},
		onChange : function(values) {
			// 如果之前有选中项,先检查关联项
			if (this.values[this.customFieldName].length > 0 && this.checkRelationNum(values)) {
				return;
			}
			this.selectOnChangeFun(values);
		},
		selectOnChangeFun : function(values) {
			this.values[this.customFieldName].length = 0;
			for (var i = 0; i < values.selectedOptions.length; i++) {
				this.values[this.customFieldName].push(values.selectedOptions[i].customOptionSetCode);
			}
		},
		checkRelationNum : function(values) {// 检查是否有需要隐藏的关联内容
			var that = this;
			for (var i = this.values[this.customFieldName].length - 1; i >= 0; i--) {
				var customModularCodeAry = that.$root.customOptionSetCodeMap[that.values[that.customFieldName][i]];
				if (customModularCodeAry) {
					for (var j = 0; j < customModularCodeAry.length; j++) {
						var num = that.$root.relationCodeMap[customModularCodeAry[j]][that.index];
						num--;
						if (num == 0) {
							var has = false;
							for (var k = 0; k < values.selectedOptions.length; k++) {
								if (values.selectedOptions[k].customOptionSetCode == this.values[this.customFieldName][i]) {
									has = true;
								}
							}
							
							if (has) {
								continue;
							}
							
							var customOptionSetContent = that.fieldDataMap[that.values[that.customFieldName][i]].customOptionSetContent;
							assemblys.confirm("取消『" + customOptionSetContent + "』选项会把它所关联的选项内容清空，确定要取消吗？", function() {
								that.selectOnChangeFun(values);
							}, function() {
								that.value = that.values[that.customFieldName][that.values[that.customFieldName].length - 1];
								that.refs[that.customFieldName] = true;
							});
							return true;
						}
					}
				}
			}
			return false;
		},
		changeRelationNum : function(customOptionSetCode, type) {// x的作用是继续循环遍历其他关联项隐藏
			var that = this;
			var customModularCodeAry = that.$root.customOptionSetCodeMap[customOptionSetCode];
			if (customModularCodeAry) {
				for (var i = 0; i < customModularCodeAry.length; i++) {
					var num = that.$root.relationCodeMap[customModularCodeAry[i]][that.index];
					if (type == 1) { // 等于1选中,累加
						num++;
					} else {
						num--;
						if (num < 0) {// 不能少于0
							num = 0;
						}
					}
					that.$root.relationCodeMap[customModularCodeAry[i]][that.index] = num;
				}
			}
			return true;
		},
		onClose : function() {
			this.value = "";
			this.refs[this.customFieldName] = false;
		},
		onClick : function() {
			this.refs[this.customFieldName] = true;
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};