page.form.components["custom-text"] = {
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="values[customFieldName]" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-field :type="field.customFieldSet" :readonly="field.isRead" v-model="values[customFieldName]" :placeholder="getPlaceholder()"></van-field>';
		return html;
	})(),
	data : function() {
		return {
			verify : this.$root.verify(this.field.isNecessField == 1 ? "limit|required|customRegex" : "limit|customRegex", {
				vueObj : this,
				limit : this.field.customFieldLength,
				customRegex : this.field.fieldVerifyType
			})
		};
	},
	methods : {
		getPlaceholder : function() {
			return this.field.remindText || '请输入' + this.field.customFieldName;
		}
	},
	created : function() {
		if (!this.$root.param.customFormFilledCode) {
			// 默认值
			var defaultValue = this.field.defaultValue || "";
			var customFieldLength = this.field.customFieldLength;
			if (defaultValue && defaultValue.length > customFieldLength) {
				defaultValue = defaultValue.substring(0, customFieldLength);
			}
			this.values[this.customFieldName] = defaultValue;
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};