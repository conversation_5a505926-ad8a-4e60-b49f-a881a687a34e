page.form.components["custom-textarea"] = {
	props : [ "field" ],
	inject : [ "modular", "index", 'values', "refs" ],
	template : (function() {
		var html = "";
		html += '<van-field v-model="values[customFieldName]" :name="customFieldName" class="vue-field-verify-hide" :rules="verify"></van-field>';
		html += '<van-field v-if="field.isRichText == 0" :type="field.customFieldSet" :readonly="field.isRead" v-model="values[customFieldName]" :placeholder="getPlaceholder()"></van-field>';
		html += '<textarea  v-if="field.isRichText == 1" v-model="values[customFieldName]" :id="getEditorID()" style="height:100px;" ></textarea>';
		return html;
	})(),
	data : function() {
		
		var verify = this.$root.verify(this.field.isNecessField == 1 ? "limit|required|customRegex" : "limit|customRegex", {
			vueObj : this,
			limit : this.field.customFieldLength,
			customRegex : this.field.fieldVerifyType
		});
		
		// 富文本时
		if (this.field.isRichText == 1) {
			verify = this.$root.verify(this.field.isNecessField == 1 ? "required" : "", {
				vueObj : this
			});
		}
		
		return {
			"verify" : verify
		};
	},
	methods : {
		getPlaceholder : function() {
			return this.field.remindText || '请输入' + this.field.customFieldName;
		},
		getEditorID : function() {
			return 'editor_' + this.field.customFieldCode + "_" + this.index;
		}
	},
	created : function() {
		// 默认值
		if (!this.$root.param.customFormFilledCode) {
			// 默认值
			var defaultValue = this.field.defaultValue || "";
			var customFieldLength = this.field.customFieldLength;
			if (defaultValue && defaultValue.length > customFieldLength) {
				defaultValue = defaultValue.substring(0, customFieldLength);
			}
			this.values[this.customFieldName] = defaultValue;
		}
	},
	mounted : function() {
		// 如果是富文本
		if (this.field.isRichText == 1) {
			var that = this;
			var id = 'editor_' + this.field.customFieldCode + "_" + this.index;
			// 初始化
			if (!that.$root.param["editorCache"]) {
				that.$root.param["editorCache"] = {};
			}
			var ue = UE.getEditor(id, {
				initialFrameWidth : "100%",// 初始化选项
				autoFloatEnabled : false,
				maximumWords : 10000000,
				elementPathEnabled : false,
				toolbars : [ [ 'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'removeformat' ] ]
			});
			that.$root.param["editorCache"][id] = ue;
			
			// 监听事件
			ue.addListener('contentChange', function() {
				that.values[that.customFieldName] = Vue.ref(ue.getContent());
			});
		}
	},
	computed : {
		customFieldName : function() {
			return this.field.customFieldCode + "-" + this.index;
		}
	}
};