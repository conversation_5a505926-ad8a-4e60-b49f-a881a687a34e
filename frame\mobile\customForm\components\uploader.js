page.form.components["custom-uploader"] = {
	template : (function() {
		var html = "";
		html += '<van-uploader v-model="fileList" multiple :after-read="afterRead" @delete="deleteFile"></van-uploader>';
		return html;
	})(),
	data : function() {
		return {
			fileList : Vue.ref([])
		};
	},
	inject : [ "attaValues" ],
	methods : {
		getAttachments : function(customFormFilledCode) {
			var that = this;
			if (customFormFilledCode) {
				return ajax({
					url : basePath + "frame/fileUpload/getAttachments.spring",
					data : {
						"belongToCode" : customFormFilledCode
					},
					skipDataCheck : true
				}).then(function(data) {
					for (var i = 0; i < data.attachmentsList.length; i++) {
						that.fileList.push({
							url : that.$root.baseImgPath + data.attachmentsList[i].attachmentURL
						});
						that.attaValues.attaName[i] = data.attachmentsList[i].attachmentName;
						that.attaValues.attaUrl[i] = data.attachmentsList[i].attachmentURL;
						that.attaValues.attaSize[i] = data.attachmentsList[i].attachmentSize;
						that.attaValues.attaType[i] = data.attachmentsList[i].attachmentType;
						that.attaValues.attaCode[i] = "";
						
					}
				});
			}
		},
		afterRead : function(file, detail) {
			var that = this;
			if (file.file) {
				file = [ file ]
			}
			for (var i = 0; i < file.length; i++) {
				let f = file[i];
				file[i].status = "uploading";
				file[i].message = "正在上传";
				let formData = new FormData();
				formData.append("file", file[i].file, file[i].file.name);
				axios.post(basePath + "frame/fileUpload/newUpload.spring", formData, {
					headers : {
						"Content-Type" : "multipart/form-data"
					}
				}).then(function(data) {
					if (data.state == "SUCCESS") {
						f.status = "done";
						f.message = "上传成功";
						
						let index = that.fileList.indexOf(f);
						that.attaValues.attaName[index] = data.original;
						that.attaValues.attaUrl[index] = data.url;
						that.attaValues.attaSize[index] = data.size;
						that.attaValues.attaType[index] = data.type;
						that.attaValues.attaCode[index] = "";
						
					} else {
						f.status = "failed";
						f.message = "上传失败";
					}
				});
			}
		},
		deleteFile : function(file, detail) {
			this.attaValues.attaCode.splice(detail.index, 1);
			this.attaValues.attaName.splice(detail.index, 1);
			this.attaValues.attaUrl.splice(detail.index, 1);
			this.attaValues.attaSize.splice(detail.index, 1);
			this.attaValues.attaType.splice(detail.index, 1);
		}
	},
}