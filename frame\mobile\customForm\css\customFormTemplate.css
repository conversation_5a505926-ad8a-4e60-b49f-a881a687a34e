div.custom-modular{
	margin-bottom: 3px;
}

div.custom-modular > div:first-child {
    /* color: var(--van-white);*/
    background: #d5eaff; 
    font-weight: 700;
    font-size: 16px;
    box-shadow: 0px 0px 10px #a0a0a0;
}

i.van-cell__right-icon {
    color: var(--van-primary-color);
}

div.van-collapse-item__content {
    padding: 2px 5px;
    background-color: #F2F2F2;
}

div.custom-field{
	color: var(--van-primary-color);
	font-weight: 700;
	padding: 2px 12px;
}

div.custom-field-value{
	padding: var(--van-cell-vertical-padding) 20px;
}

div.oneLevel{
	padding: 0;
}

div.twoLevel{
	padding-right: 0px;
}

textarea.form-textarea{
	width: 100%;
	border: none;
}

.van-cell::after {
    border-bottom: 1px solid #ccc;
}

div.custom-field-parent{
	margin: 5px 0px;
	box-shadow: 0px 0px 3px #a0a0a0;
}

div.vue-field-verify-hide input{
	display: none;
}

div.vue-field-verify-hide{
	padding: 0px var(--van-cell-horizontal-padding);
}

.van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon {
    color: var(--van-white);
	background-color: var(--van-checkbox-checked-icon-color);
	border-color: var(--van-checkbox-checked-icon-color);
}

.van-checkbox__icon--disabled .van-icon {
    background-color: unset;
    border-color: var(--van-checkbox-border-color);
}

.van-radio__icon--disabled.van-radio__icon--checked .van-icon {
    color: var(--van-white);
    background-color: var(--van-radio-checked-icon-color);
    border-color: var(--van-radio-checked-icon-color);
}

.van-radio__icon--disabled .van-icon {
    background-color: unset;
    border-color: var(--van-radio-border-color);
}

.list-cell{
	margin: 5px 0px;
}

.van-search__action{
	background-color: var(--van-primary-color);
	color: white;
	line-height: 32px;
}

.interface-row-col-cell > .van-cell__title{
	height: 24px;
}

.interface-row-col-cell > .van-cell__title > button{
	top: -5px;
}


.van-col {
	margin: 0px 3px;
	margin-bottom: 5px;
}

div.interface-list .van-col {
	margin: 0px 0px;
}

.van-col--6 {
    flex: 0 0 23%;
    max-width: 23%;
}

.van-col--8 {
	flex: 0 0 31%;
	max-width: 31%;
}

.van-col--12 {
	flex: 0 0 48%;
	max-width: 48%;
}

.van-col--24 {
	flex: 0 0 99%;
	max-width: 99%;
}

div.custom-form-select-li {
	border-bottom: 3px solid var(--van-primary-color);
}

div.van-swipe-item{
	width: auto!important;
	margin-left: 10px;
	height: 45px;
	line-height: 45px;
}

div[aria-labelledby="确认提交吗？"]{
	z-index: 999999!important;
}

div[aria-labelledby="签名不能为空"]{
	z-index: 999999!important;
}


.edui-editor-bottombar,.edui-editor-wordcount {
	 display: none; 
}


.van-cell__title .van-field__control[readonly]{
	background: #f2f2f2;
}


div.interface-list .van-cell--clickable {
	min-height: 44px;
}