<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>自定义表单</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/customFormTemplate.css">
</head>
<body>
	<div app="form">
		<van-form @submit="onSubmit" ref="forms" validate-first>
			<div class="custom-field-parent">
				<van-cell title="审批" :style="{ fontSize : '20px'}"></van-cell>
			</div>
			<custom-modular v-for="customModular in customModularList" :modular="customModular" :nocollapse="true"></custom-modular>
			<div v-show="showFileUpload" class="custom-field-parent">
				<van-cell title="上传附件" class="custom-field"></van-cell>
				<van-cell class="custom-field-value">
					<template #title>
						<custom-uploader ref="uploader"></custom-uploader>
					</template>
				</van-cell>
			</div>
			
			<field-multiple v-show="hasDynamic" :params="selectApproverParam" title="指定审批人" name="approvalUIDs" :verify="verifyApprovalUIDs" ref="approvalUIDs" style="position:absolute;top:-99999999px;" ></field-multiple>
			
			<field-select v-show="hasStateStatusNo" :actions="customFormTypeStateList" title="表单状态流转" name="stateStatusNo" v-model:value="formData.stateStatusNo"></field-select>
			
			<field-multiple :params="selectCopyUserParam" title="抄送" name="copyUserCodes"></field-multiple>
			
			<van-row v-if="hasSaveBtn" justify="space-between">
				<van-col :span="cols"><van-button type="primary" size="normal" block @click="submitFun(2,handleFormValues())">保存草稿</van-button></van-col>
				<van-col :span="cols"><van-button type="primary" size="normal" block @click="submitType = 0;$refs.forms.submit();">提交</van-button></van-col>
				<van-col v-if="inLoop" :span="cols"><van-button plain type="primary" size="normal" block @click="submitType = 1;$refs.forms.submit();">结束循环</van-button></van-col>
				<van-col v-if="hasCountersign" :span="cols"><van-button plain type="primary" size="normal" block @click="submitType = 2;$refs.forms.submit();">结束会签</van-button></van-col>
			</van-row>
			<van-row v-if="hasFinish" justify="space-between">
				<van-col span="24"><van-button type="primary" size="normal" block @click="submitType = 3;$refs.forms.submit();">一键结束</van-button></van-col>
			</van-row>
		</van-form>
	</div>
	<div id="teleport"></div>
</body>
</html>
<script type="text/javascript" src="../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/customFormApprovalTemplate.js?version=*******"></script>
<script type="text/javascript" src="components/radio.js?version=*******"></script>
<script type="text/javascript" src="components/checkbox.js?version=*******"></script>
<script type="text/javascript" src="components/datetime.js?version=*******"></script>
<script type="text/javascript" src="components/interface.js?version=*******"></script>
<script type="text/javascript" src="components/label.js?version=*******"></script>
<script type="text/javascript" src="components/org.js?version=*******"></script>
<script type="text/javascript" src="components/select.js?version=*******"></script>
<script type="text/javascript" src="components/text.js?version=*******"></script>
<script type="text/javascript" src="components/textarea.js?version=*******"></script>
<script type="text/javascript" src="components/customField.js?version=*******"></script>
<script type="text/javascript" src="components/customModularApproval.js?version=*******"></script>
<script type="text/javascript" src="components/uploader.js?version=*******"></script>
<script type="text/javascript" charset="utf-8" src="../../../plugins/fileUpload/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../plugins/fileUpload/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../plugins/fileUpload/lang/zh-cn/zh-cn.js"></script>
