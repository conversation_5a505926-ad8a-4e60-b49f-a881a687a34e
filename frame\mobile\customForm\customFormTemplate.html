<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>自定义表单</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/customFormTemplate.css">
</head>
<body>
	<div app="form">
		<van-form @submit="onSubmit" ref="form" validate-first>
			<van-collapse  v-model="customModularCode">
				<custom-modular v-for="customModular in customModularList" :modular="customModular"></custom-modular>
				<van-collapse-item v-show="showFileUpload" class="custom-modular" title="上传附件" name="atta">
					<van-cell title="上传附件" class="custom-field"></van-cell>
					<van-cell class="custom-field-value">
						<template #title>
							<custom-uploader ref="uploader"></custom-uploader>
						</template>
					</van-cell>
				</van-collapse-item>
			</van-collapse>
			<van-row justify="space-between">
				<van-col span="12"><van-button v-if="param.type == 1 || param.type == 2" plain type="primary" size="normal" block @click="saveDraft">{{ param.type == 1 ? '保存草稿' : '保存'}}</van-button></van-col>
				<van-col span="12">
				<van-button v-if="approvalBelongFlowNode == null"  type="primary" size="normal" block @click="save">提交</van-button>
				<div v-if="approvalBelongFlowNode != null">
					<van-button v-if="param['hasRollbackStatus'] == hasRollbackStatus || approvalBelongFlowNode.approvalIndex == 0 || param.type == 1  "  type="primary" size="normal" block @click="save">提交</van-button>				
				</div>
				</van-col>
				<van-col span="24"><van-button v-if="param.type == 3 || param.type == 4" type="primary" size="normal" block @click="save">提交</van-button></van-col>
			</van-row>
		</van-form>
	</div>
	<div id="teleport"></div>
</body>
</html>
<script type="text/javascript" src="../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/customFormTemplate.js?version=*******"></script>
<script type="text/javascript" src="components/radio.js?version=*******"></script>
<script type="text/javascript" src="components/checkbox.js?version=*******"></script>
<script type="text/javascript" src="components/datetime.js?version=*******"></script>
<script type="text/javascript" src="components/interface.js?version=*******"></script>
<script type="text/javascript" src="components/label.js?version=*******"></script>
<script type="text/javascript" src="components/org.js?version=*******"></script>
<script type="text/javascript" src="components/select.js?version=*******"></script>
<script type="text/javascript" src="components/text.js?version=*******"></script>
<script type="text/javascript" src="components/textarea.js?version=*******"></script>
<script type="text/javascript" src="components/profile.js"></script>
<script type="text/javascript" src="components/file.js"></script>
<script type="text/javascript" src="components/customField.js?version=*******"></script>
<script type="text/javascript" src="components/customModular.js?version=*******"></script>
<script type="text/javascript" src="components/uploader.js?version=*******"></script>
<script type="text/javascript" charset="utf-8" src="../../../plugins/fileUpload/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../plugins/fileUpload/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="../../../plugins/fileUpload/lang/zh-cn/zh-cn.js"></script>

