{"result": "success", "emptyData": [], "data": {"customModularList": [{"customModularCode": "CMU9lbjkRLqzcOcLv3pAF", "customModularName": "医疗安全事件登记", "isCommon": 0, "hasAdd": 0, "customFieldRowList": [{"customFieldRowCode": "CFRk7XqtfuBMnh3ZVu4ViL", "customModularCode": "CMU9lbjkRLqzcOcLv3pAF", "cols": 1, "seqNo": 0}], "customFieldList": [{"customFieldCode": "ComplaintMethod", "businessCode": "ComplaintMethod", "customFieldName": "事件分类", "customFieldSet": "radio", "customFieldRowCode": "CFRk7XqtfuBMnh3ZVu4ViL", "isNecessField": 1, "customModularCode": "CMU9lbjkRLqzcOcLv3pAF", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "fieldData": [{"customFieldCode": "ComplaintMethod", "customOptionSetCode": "ComplaintMethodComplaint", "customOptionSetContent": "投诉登记", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintMethodComplaint", "childOptionList": [{"customFieldCode": "CFDC6jWVNPmYBJtm3LY2oU", "customOptionSetCode": "COS4kerI6tBtIrW78orRUe", "customOptionSetContent": "zxa", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "COS4yI9xixPxvvlt8Y5YKh", "remark": "zxa", "businessCode": "zxa", "businessValue": "zxa", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDC6jWVNPmYBJtm3LY2oU", "customOptionSetCode": "nIRTnfNrtWz0RzpXmJ8", "customOptionSetContent": "男", "customFieldSet": "radio", "hasDefault": 1, "parentCustomOptionSetCode": "COS4yI9xixPxvvlt8Y5YKh", "businessCode": "sex", "businessValue": "1", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDC6jWVNPmYBJtm3LY2oU", "customOptionSetCode": "eft9finjMdRpuWyuQcS", "customOptionSetContent": "女", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "COS4yI9xixPxvvlt8Y5YKh", "businessCode": "sex", "businessValue": "0", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDC6jWVNPmYBJtm3LY2oU", "customOptionSetCode": "HBDLaYfJAbKsl1EsD1l", "customOptionSetContent": "其他", "customFieldSet": "radioOther", "hasDefault": 0, "parentCustomOptionSetCode": "COS4yI9xixPxvvlt8Y5YKh", "businessCode": "sex", "businessValue": "2", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDC6jWVNPmYBJtm3LY2oU", "customOptionSetCode": "mSZy1nFkbTKKpI6XimD", "customOptionSetContent": "未知", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "COS4yI9xixPxvvlt8Y5YKh", "businessCode": "sex", "businessValue": "-1", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintMethod", "customOptionSetCode": "ComplaintMethodRevisit", "customOptionSetContent": "信访（复访）登记", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintMethodRevisit", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintMethod", "customOptionSetCode": "ComplaintMethodLitigation", "customOptionSetContent": "诉讼登记", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintMethodLitigation", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}], "reportValues": {}, "detailValues": {}, "maxIndex": 0}, {"customModularCode": "ElQvqXyqA1nbCmmdDoG", "customModularName": "投诉来源", "isCommon": 0, "hasAdd": 0, "customFieldRowList": [{"customFieldRowCode": "hvP1B9cH2MPt28lR2lm", "customModularCode": "ElQvqXyqA1nbCmmdDoG", "cols": 1, "seqNo": 0}, {"customFieldRowCode": "hrPV7kEnE4El8jsPGuH", "customModularCode": "ElQvqXyqA1nbCmmdDoG", "cols": 1, "seqNo": 1}, {"customFieldRowCode": "izgfFTiC4qKT8MR0m0b", "customModularCode": "ElQvqXyqA1nbCmmdDoG", "cols": 1, "seqNo": 2}, {"customFieldRowCode": "5l385m19Hd9IcPCRuWS", "customModularCode": "ElQvqXyqA1nbCmmdDoG", "cols": 1, "seqNo": 3}, {"customFieldRowCode": "h5gJ88HIWD1jSt71sB1", "customModularCode": "ElQvqXyqA1nbCmmdDoG", "cols": 1, "seqNo": 4}], "customFieldList": [{"customFieldCode": "CFDKzsmQlfmBDCt3mbtFwH", "businessCode": "IsPatientComplaint", "customFieldName": "投诉来源", "customFieldSet": "radio", "customFieldRowCode": "hvP1B9cH2MPt28lR2lm", "isNecessField": 1, "customModularCode": "ElQvqXyqA1nbCmmdDoG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "businessValue": "", "fieldData": [{"customFieldCode": "CFDKzsmQlfmBDCt3mbtFwH", "customOptionSetCode": "COSdDidUXTA3pfXSzqrqWm", "customOptionSetContent": "下级上报", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "IsPatientComplaint_YES", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDKzsmQlfmBDCt3mbtFwH", "customOptionSetCode": "COSXHpVj0OqlH9rqWcLPmY", "customOptionSetContent": "医务科登记", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "IsPatientComplaint_NO", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}, {"customFieldCode": "CFDfePvvMu3vUGmNL6XKwS", "businessCode": "ComplaintType", "customFieldName": "投诉类型", "customFieldSet": "radio", "customFieldRowCode": "hrPV7kEnE4El8jsPGuH", "isNecessField": 1, "customModularCode": "ElQvqXyqA1nbCmmdDoG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "businessValue": "", "fieldData": [{"customFieldCode": "CFDfePvvMu3vUGmNL6XKwS", "customOptionSetCode": "COS0dA7OrF1oT0kg7rrAAu", "customOptionSetContent": "投诉医务人员", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintType_MEDICAL_STAFF", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfePvvMu3vUGmNL6XKwS", "customOptionSetCode": "COSpjS7A6WgQF9cHInbtho", "customOptionSetContent": "投诉医技科室", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintType_MEDICAL_TECHNOLOGY_DEPARTMENT", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfePvvMu3vUGmNL6XKwS", "customOptionSetCode": "COSCBiFA6Q7EnNxmaZBMHT", "customOptionSetContent": "投诉住院科室", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintType_INPATIENT_DEPARTMENT", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfePvvMu3vUGmNL6XKwS", "customOptionSetCode": "COSzq2vbaGusHfIsb5yBZs", "customOptionSetContent": "员工投诉", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintType_EMPLOYEE_COMPLAINTS", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfePvvMu3vUGmNL6XKwS", "customOptionSetCode": "COSeq6hpGoSAFxhl5atoJI", "customOptionSetContent": "夜间及节假日投诉", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "ComplaintType_NIGHT", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}, {"customFieldCode": "CFDyAx4M0iEd1CmasAfuSh", "businessCode": "MedicalStaff", "customFieldName": "医务人员", "customFieldSet": "org", "customFieldRowCode": "izgfFTiC4qKT8MR0m0b", "isNecessField": 1, "customModularCode": "ElQvqXyqA1nbCmmdDoG", "relationField": "user", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "funCode": "", "businessValue": "", "fieldData": []}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "businessCode": "MedicalTechnologyDepartment", "customFieldName": "医技科室", "customFieldSet": "select", "customFieldRowCode": "5l385m19Hd9IcPCRuWS", "isNecessField": 1, "customModularCode": "ElQvqXyqA1nbCmmdDoG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "businessValue": "", "fieldData": [{"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSN9YMozTjLCAyKSKDKyV", "customOptionSetContent": "检验科", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110062", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSfYdgiczeRxamC4zDIq8", "customOptionSetContent": "放射科", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110071", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSfADJhD9v9ZyoQLgBBVs", "customOptionSetContent": "特检科", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110063", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSFUhwzaRtXTWuz56NpnZ", "customOptionSetContent": "脑功能检测与治疗中心", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110139", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSXKj7Jioyf5zB41uOJ2B", "customOptionSetContent": "精神康复科", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110072", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COS40gtCAOvdWgqAAuaYvq", "customOptionSetContent": "心理咨询中心", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110064", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COStDmJUZI0LjfsoioSHDB", "customOptionSetContent": "司法鉴定所", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110052", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSYTHqYaBldTEigmLrA5c", "customOptionSetContent": "精神卫生办公室", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110118", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSCXhK4IacMpTMDimxb5E", "customOptionSetContent": "美沙酮药房", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "02242", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "CFDfVteIMjaFpmQTIy0oPM", "customOptionSetCode": "COSXv9XgbYkqgiXvJiRMZX", "customOptionSetContent": "体检中心", "customFieldSet": "select", "hasDefault": 0, "parentCustomOptionSetCode": "", "remark": "", "businessCode": "110075", "businessValue": "", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}, {"customFieldCode": "CFDhrv9EULSK6Dd9druF6C", "businessCode": "InpatientDepartment", "customFieldName": "住院科室", "customFieldSet": "org", "customFieldRowCode": "h5gJ88HIWD1jSt71sB1", "isNecessField": 1, "customModularCode": "ElQvqXyqA1nbCmmdDoG", "relationField": "dept", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "funCode": "", "businessValue": "", "fieldData": []}], "reportValues": {}, "detailValues": {}, "maxIndex": 0}, {"customModularCode": "CM2YkUGMclm2Jp79TndKG", "customModularName": "投诉事件登记", "isCommon": 0, "hasAdd": 0, "customFieldRowList": [{"customFieldRowCode": "CFRUXhRV7XFlFEGh0n566G", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 2, "seqNo": 0}, {"customFieldRowCode": "CFRPFQQPFkjPfO40EOvbVF", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 2, "seqNo": 1}, {"customFieldRowCode": "CFRJfOxlvBzweAjSpBeJji", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 2, "seqNo": 2}, {"customFieldRowCode": "CFR5zy8vpJwH5krkPUlc5i", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 2, "seqNo": 3}, {"customFieldRowCode": "CFRgjGSPn8f0xFruf31Ru7", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 3, "seqNo": 4}, {"customFieldRowCode": "CFRmxHe1VRSCUWimGvhEFp", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 1, "seqNo": 5}, {"customFieldRowCode": "CFR6YwOzMGS1Fjj5JtDfNS", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 1, "seqNo": 6}, {"customFieldRowCode": "CFRosUT6YY5KAPbg1uXVLt", "customModularCode": "CM2YkUGMclm2Jp79TndKG", "cols": 1, "seqNo": 7}], "customFieldList": [{"customFieldCode": "ComplaintPatientName", "businessCode": "ComplaintPatientName", "customFieldName": "患者姓名", "customFieldSet": "text", "customFieldRowCode": "CFRUXhRV7XFlFEGh0n566G", "isNecessField": 1, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 1, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintHospitalizationNO", "businessCode": "ComplaintHospitalizationNO", "customFieldName": "住院号", "customFieldSet": "text", "customFieldRowCode": "CFRUXhRV7XFlFEGh0n566G", "isNecessField": 1, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 1, "fieldVerifyType": "", "seqNo": 1, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintTreatmentDepartment", "businessCode": "ComplaintTreatmentDepartment", "customFieldName": "经治科室", "customFieldSet": "org", "customFieldRowCode": "CFRPFQQPFkjPfO40EOvbVF", "isNecessField": 1, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "relationField": "dept", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "funCode": "", "fieldData": []}, {"customFieldCode": "ComplaintDate", "businessCode": "ComplaintDate", "customFieldName": "投诉时间", "customFieldSet": "datetime", "customFieldRowCode": "CFRPFQQPFkjPfO40EOvbVF", "isNecessField": 1, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "date", "seqNo": 1, "fieldData": []}, {"customFieldCode": "ComplaintMedicalStaff", "businessCode": "ComplaintMedicalStaff", "customFieldName": "投诉医务人员", "customFieldSet": "text", "customFieldRowCode": "CFRJfOxlvBzweAjSpBeJji", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintComplainants", "businessCode": "ComplaintComplainants", "customFieldName": "投诉人", "customFieldSet": "text", "customFieldRowCode": "CFRJfOxlvBzweAjSpBeJji", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 1, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintComplainantsTEL", "businessCode": "ComplaintComplainantsTEL", "customFieldName": "投诉人电话", "customFieldSet": "text", "customFieldRowCode": "CFR5zy8vpJwH5krkPUlc5i", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintToDepartment", "businessCode": "ComplaintToDepartment", "customFieldName": "交科室时间", "customFieldSet": "datetime", "customFieldRowCode": "CFR5zy8vpJwH5krkPUlc5i", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "date", "seqNo": 1, "fieldData": []}, {"customFieldCode": "ComplaintDepartmentManagement", "businessCode": "ComplaintDepartmentManagement", "customFieldName": "科室经办人", "customFieldSet": "text", "customFieldRowCode": "CFRgjGSPn8f0xFruf31Ru7", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintReplyDepartment", "businessCode": "ComplaintReplyDepartment", "customFieldName": "科室是否回复", "customFieldSet": "radio", "customFieldRowCode": "CFRgjGSPn8f0xFruf31Ru7", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 1, "fieldData": [{"customFieldCode": "ComplaintReplyDepartment", "customOptionSetCode": "COSdxo6dw8KOTKzLoKeHss", "customOptionSetContent": "是", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSdxo6dw8KOTKzLoKeHss", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintReplyDepartment", "customOptionSetCode": "COSJQhHsOpJnK2AHhSq4FN", "customOptionSetContent": "否", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSJQhHsOpJnK2AHhSq4FN", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}, {"customFieldCode": "ComplaintReplyPatient", "businessCode": "ComplaintReplyPatient", "customFieldName": "是否回复患者", "customFieldSet": "radio", "customFieldRowCode": "CFRgjGSPn8f0xFruf31Ru7", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 2, "fieldData": [{"customFieldCode": "ComplaintReplyPatient", "customOptionSetCode": "COSA1IfAnm0HgYYv5C94Or", "customOptionSetContent": "是", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSA1IfAnm0HgYYv5C94Or", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintReplyPatient", "customOptionSetCode": "COS5KtUljlspumzkzqDUup", "customOptionSetContent": "否", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COS5KtUljlspumzkzqDUup", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}, {"customFieldCode": "ComplaintReason", "businessCode": "ComplaintReason", "customFieldName": "投诉理由", "customFieldSet": "textarea", "customFieldRowCode": "CFRmxHe1VRSCUWimGvhEFp", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 2000, "fieldData": []}, {"customFieldCode": "ComplaintCause", "businessCode": "ComplaintCause", "customFieldName": "原因", "customFieldSet": "textarea", "customFieldRowCode": "CFR6YwOzMGS1Fjj5JtDfNS", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 2000, "fieldData": []}, {"customFieldCode": "ComplaintRemark", "businessCode": "ComplaintRemark", "customFieldName": "备注", "customFieldSet": "textarea", "customFieldRowCode": "CFRosUT6YY5KAPbg1uXVLt", "isNecessField": 0, "customModularCode": "CM2YkUGMclm2Jp79TndKG", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 2000, "fieldData": []}], "reportValues": {}, "detailValues": {}, "maxIndex": 0}, {"customModularCode": "CMYa8oOsedLoOlzHbQZJF", "customModularName": "信访（复访）事件登记", "isCommon": 0, "hasAdd": 0, "customFieldRowList": [{"customFieldRowCode": "CFR6J6yCCc8ogmLfdtfCaE", "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "cols": 2, "seqNo": 0}, {"customFieldRowCode": "CFRDmeahNIoqiC4DeQEqSZ", "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "cols": 2, "seqNo": 1}, {"customFieldRowCode": "CFR6se238IAXJcrDUu2b3H", "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "cols": 1, "seqNo": 2}, {"customFieldRowCode": "CFRnChJPpRgZCNrz0VBoBn", "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "cols": 2, "seqNo": 3}, {"customFieldRowCode": "CFRsHr5zAbm51Vbz9WuzwK", "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "cols": 2, "seqNo": 4}], "customFieldList": [{"customFieldCode": "ComplaintSource", "businessCode": "ComplaintSource", "customFieldName": "来源", "customFieldSet": "text", "customFieldRowCode": "CFR6J6yCCc8ogmLfdtfCaE", "isNecessField": 1, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintPetitionDepartment", "businessCode": "ComplaintPetitionDepartment", "customFieldName": "信访科室", "customFieldSet": "org", "customFieldRowCode": "CFR6J6yCCc8ogmLfdtfCaE", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "relationField": "dept", "isCommon": 0, "fieldVerifyType": "", "seqNo": 1, "funCode": "", "fieldData": []}, {"customFieldCode": "ComplaintPetitionDate", "businessCode": "ComplaintPetitionDate", "customFieldName": "信访时间", "customFieldSet": "datetime", "customFieldRowCode": "CFRDmeahNIoqiC4DeQEqSZ", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "date", "seqNo": 0, "fieldData": []}, {"customFieldCode": "ComplaintPetitionPatientName", "businessCode": "ComplaintPetitionPatientName", "customFieldName": "信访患者姓名", "customFieldSet": "text", "customFieldRowCode": "CFRDmeahNIoqiC4DeQEqSZ", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "", "seqNo": 1, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "businessCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customFieldName": "信访内容", "customFieldSet": "textarea", "customFieldRowCode": "CFR6se238IAXJcrDUu2b3H", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 2000, "fieldData": []}, {"customFieldCode": "ComplaintDepartmentHead", "businessCode": "ComplaintDepartmentHead", "customFieldName": "科室负责人", "customFieldSet": "text", "customFieldRowCode": "CFRnChJPpRgZCNrz0VBoBn", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "businessCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customFieldName": "是否回复", "customFieldSet": "radio", "customFieldRowCode": "CFRnChJPpRgZCNrz0VBoBn", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "", "seqNo": 1, "fieldData": [{"customFieldCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customOptionSetCode": "COSYJxNibFhbfwHuuDnBY6", "customOptionSetContent": "是", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSYJxNibFhbfwHuuDnBY6", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customOptionSetCode": "COSusyQYHWvmyoSFxzwsK4", "customOptionSetContent": "否", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSusyQYHWvmyoSFxzwsK4", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}, {"customFieldCode": "ComplaintReplyDate", "businessCode": "ComplaintReplyDate", "customFieldName": "回复日期", "customFieldSet": "datetime", "customFieldRowCode": "CFRsHr5zAbm51Vbz9WuzwK", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "date", "seqNo": 0, "fieldData": []}, {"customFieldCode": "ComplaintBeVerified", "businessCode": "ComplaintBeVerified", "customFieldName": "是否属实", "customFieldSet": "radio", "customFieldRowCode": "CFRsHr5zAbm51Vbz9WuzwK", "isNecessField": 0, "customModularCode": "CMYa8oOsedLoOlzHbQZJF", "isCommon": 0, "fieldVerifyType": "", "seqNo": 1, "fieldData": [{"customFieldCode": "ComplaintBeVerified", "customOptionSetCode": "COSsVcHdTM0dcCotFQlTgr", "customOptionSetContent": "是", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSsVcHdTM0dcCotFQlTgr", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintBeVerified", "customOptionSetCode": "COS7lI3pzQ3vGgBtpqlUGW", "customOptionSetContent": "否", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COS7lI3pzQ3vGgBtpqlUGW", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}], "reportValues": {}, "detailValues": {}, "maxIndex": 0}, {"customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "customModularName": "诉讼事件登记", "isCommon": 0, "hasAdd": 0, "customFieldRowList": [{"customFieldRowCode": "CFRWKOBbAl1sKXaia3cWap", "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "cols": 2, "seqNo": 0}, {"customFieldRowCode": "CFRrtDj7HJ4KrCrF1FQVLi", "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "cols": 2, "seqNo": 1}, {"customFieldRowCode": "CFR68caHJuknXPwQpFWny8", "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "cols": 1, "seqNo": 2}, {"customFieldRowCode": "CFRY4pW81on88jzWsw5fLv", "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "cols": 1, "seqNo": 3}], "customFieldList": [{"customFieldCode": "ComplaintVolumeNumber", "businessCode": "ComplaintVolumeNumber", "customFieldName": "归卷编号", "customFieldSet": "text", "customFieldRowCode": "CFRWKOBbAl1sKXaia3cWap", "isNecessField": 0, "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "ComplaintAgreementTime", "businessCode": "ComplaintAgreementTime", "customFieldName": "协议时间", "customFieldSet": "datetime", "customFieldRowCode": "CFRWKOBbAl1sKXaia3cWap", "isNecessField": 0, "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "isCommon": 0, "fieldVerifyType": "date", "seqNo": 1, "fieldData": []}, {"customFieldCode": "LitigationPatientName", "businessCode": "LitigationPatientName", "customFieldName": "诉讼患者姓名", "customFieldSet": "text", "customFieldRowCode": "CFRrtDj7HJ4KrCrF1FQVLi", "isNecessField": 0, "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 100, "fieldData": []}, {"customFieldCode": "LitigationTreatmentDepartment", "businessCode": "LitigationTreatmentDepartment", "customFieldName": "经治科室", "customFieldSet": "org", "customFieldRowCode": "CFRrtDj7HJ4KrCrF1FQVLi", "isNecessField": 0, "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "relationField": "dept", "isCommon": 0, "fieldVerifyType": "", "seqNo": 1, "funCode": "", "fieldData": []}, {"customFieldCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "businessCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customFieldName": "产生原因", "customFieldSet": "textarea", "customFieldRowCode": "CFR68caHJuknXPwQpFWny8", "isNecessField": 0, "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "customFieldLength": 2000, "fieldData": []}, {"customFieldCode": "ComplaintCaseClosedMode", "businessCode": "ComplaintCaseClosedMode", "customFieldName": "结案方式", "customFieldSet": "checkbox", "customFieldRowCode": "CFRY4pW81on88jzWsw5fLv", "isNecessField": 0, "customModularCode": "CMOplNnyHBMQ36YMpXDCQ", "isCommon": 0, "fieldVerifyType": "", "seqNo": 0, "fieldData": [{"customFieldCode": "ComplaintCaseClosedMode", "customOptionSetCode": "COSgvdZeoh5jX5lnixi7Zv", "customOptionSetContent": "协商", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSgvdZeoh5jX5lnixi7Zv", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintCaseClosedMode", "customOptionSetCode": "COS8Vi1cLnbFCtNN2bGN7B", "customOptionSetContent": "鉴定", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COS8Vi1cLnbFCtNN2bGN7B", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintCaseClosedMode", "customOptionSetCode": "COS7TVkn5iHeghlCI0zZEs", "customOptionSetContent": "信访", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COS7TVkn5iHeghlCI0zZEs", "childOptionList": [], "optionValue": [], "optionValueStr": {}}, {"customFieldCode": "ComplaintCaseClosedMode", "customOptionSetCode": "COSMDUce2nUyU3rrQMFa9m", "customOptionSetContent": "诉讼", "customFieldSet": "radio", "hasDefault": 0, "parentCustomOptionSetCode": "", "businessCode": "COSMDUce2nUyU3rrQMFa9m", "childOptionList": [], "optionValue": [], "optionValueStr": {}}], "parentOptionID": ""}], "reportValues": {}, "detailValues": {}, "maxIndex": 0}], "customForm": {"appCode": "cdms", "businessCode": "COMPLAINT_REGISTER", "compNo": "CF148", "createDate": 1604650803000, "createUserCode": "ADMIN", "createUserName": "初始化管理员", "customFormCode": "COMPLAINT_REGISTER", "customFormID": 10, "customFormName": "医疗安全事件填报", "optDate": 1604650803000, "optUserCode": "ADMIN", "optUserName": "初始化管理员", "seqNo": 1, "status": 1}, "baseImgPath": "http://127.0.0.1:8080/up"}, "resultCode": "0", "responseDate": "2021-10-28 08:34:03", "resultMsg": "请求成功\n"}