{"dictList": [{"dictCode": "CUSTOMFORM_VERIFY_NUMBER", "dictName": "数字", "dictContent": "number", "remark": "^-?(0.[0-9]+|[1-9][0-9]*.[0-9]+|0|[1-9][0-9]*)$", "fieldColor": "", "dictTypeCode": "CUSTOMFORM_VERIFY", "compNo": "", "value1": null, "value2": null}, {"dictCode": "CUSTOMFORM_VERIFY_EMAIL", "dictName": "邮箱", "dictContent": "email", "remark": "^w+([-+.]w+)*@w+([-.]w+)*.w+([-.]w+)*$", "fieldColor": "", "dictTypeCode": "CUSTOMFORM_VERIFY", "compNo": "", "value1": null, "value2": null}, {"dictCode": "CUSTOMFORM_VERIFY_URL", "dictName": "URL", "dictContent": "url", "remark": "^((ht|f)tps?)://[w-]+(.[w-]+)+([w-.,@?^=%&:/~+#]*[w-@?^=%&/~+#])?$", "fieldColor": "", "dictTypeCode": "CUSTOMFORM_VERIFY", "compNo": "", "value1": null, "value2": null}, {"dictCode": "CUSTOMFORM_VERIFY_PHONE", "dictName": "手机号码", "dictContent": "phone", "remark": "^1[3456789]d{9}$", "fieldColor": "", "dictTypeCode": "CUSTOMFORM_VERIFY", "compNo": "", "value1": null, "value2": null}, {"dictCode": "CUSTOMFORM_VERIFY_IDENTITY", "dictName": "身份证", "dictContent": "identity", "remark": "^[1-9]d{5}(18|19|([23]d))d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)d{3}[0-9Xx]$", "fieldColor": "", "dictTypeCode": "CUSTOMFORM_VERIFY", "compNo": "", "value1": null, "value2": null}], "result": "success", "resultMsg": "获取数据成功"}