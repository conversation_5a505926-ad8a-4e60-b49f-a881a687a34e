page.form.option = {
	created : function() {
		var that = this;
		
		
		that.getFormVerify();
		that.defaultApprovalInit().then(function() {
			return that.getCustomFormData();
		}).then(function(values) {
			if (top === parent) {
				for (var i = 0; i < that.customModularList.length; i++) {
					that.customModularList[i].showModular = true;
				}
			} else {
				if (parent.initObj) {
					for ( var key in parent.initObj) {
						if (typeof parent.initObj[key] == "function") {
							parent.initObj[key].call(parent.window, window);
						}
					}
				} else {
					for (var i = 0; i < that.customModularList.length; i++) {
						that.customModularList[i].showModular = true;
					}
				}
			}
		});
	},
	/*computed : {
		verifyApprovalUIDs : function() {
			return this.verifyDynamic && this.hasDynamic ? 'required' : '';
		}
	},*/
	data : function() {
		var that = this;
		return {
			formData : {
				approvalFlowNodeData : "{}",
				copyUserCodes : "",
				stateStatusNo : -999,
				approvalBelongFlowNodeRecordDraftCode : ""
			},
			appCode : Vue.ref(""),
			param : {},
			baseImgPath : basePath,
			customFormFilled : null,
			customFormClass : 1,
			relationCodeMap : Vue.ref({}),
			customOptionSetCodeMap : Vue.ref({}),
			customModularList : Vue.ref([]),
			customModularCode : Vue.ref([ "atta" ]),
			forms : null,
			customFormCode : Vue.ref(""),
			showFileUpload : false,
			fileList : Vue.ref([]),
			attaValues : Vue.ref({
				attaName : [],
				attaUrl : [],
				attaSize : [],
				attaType : []
			}),
			customFormTypeStateList : Vue.ref([]),
			hasSaveBtn : Vue.ref(false),
			hasCountersign : Vue.ref(false),
			hasStateStatusNo : Vue.ref(false),
			hasDynamic : Vue.ref(false),
			/*verifyDynamic : Vue.ref(true),*/
			inLoop : Vue.ref(false),
			cols : Vue.ref(12),
			copyUserCodes : Vue.ref(""),
			copyUserNames : Vue.ref(""),
			selectApproverParam : Vue.ref({
				URL : basePath + "frame/useraction/getHasFunRightUsers.spring",
				param : {
					funCode : that.param.funCode,
					rightPoint : 1,
				},
				field : {
					name : "userName",
					value : "uID"
				},
				parseData : function(data) {
					return data.users;
				},
				cancel : function() {
					window.isSubmit = false;
					that.hasDynamic = false;
				},
				callback : function(data) {
					var names = [];
					var values = [];
					for (var i = 0; i < data.length; i++) {
						names.push(data[i].text);
						values.push(data[i].value);
					}
					var n = names.join(",");
					that.approverNames = n;
					
					var approvalUIDs = values.join(",");
					var approvalFlowNodeData = {
						approvalRight : 3,
						approvalUIDs : approvalUIDs
					}
					that.formData.approvalFlowNodeData = JSON.stringify(approvalFlowNodeData);
				}
			}),
			selectCopyUserParam : Vue.ref({
				parentName : "科室",
				parentURL : basePath + "frame/common/getDeptListNew.spring",
				parentField : {
					name : "DeptName",
					value : "DeptID"
				},
				parentParam : {
					compNo : that.param.compNo || "",
				},
				parentParseData : function(data) {
					return data.deptList;
				},
				placeholder : "用户名称",
				URL : basePath + "frame/common/getUserList.spring",
				param : {},
				field : {
					name : "userName",
					value : "userCode"
				},
				parseData : function(data) {
					return data.userList;
				},
				callback : function(data) {
					var names = [];
					var values = [];
					for (var i = 0; i < data.length; i++) {
						names.push(data[i].text);
						values.push(data[i].value);
					}
					var n = names.join(",");
					that.copyUserNames = n;
					var copyUserCodes = values.join(",");
					
					that.formData.copyUserCodes = copyUserCodes;
				}
			}),
			formVerify : {
				customRegexDictList : {},
				scrollToField : function(that, curr) {
					var boundingClientRect = curr.param.vueObj.$el.nextElementSibling.getBoundingClientRect();
					var offsetHeight = top.document.querySelector(".body-iframe").offsetHeight;
					if (curr.param && curr.param.vueObj && (boundingClientRect.top < 0 || boundingClientRect.top + 30 > offsetHeight)) {
						that.$refs.forms.scrollToField(curr.param.vueObj.customFieldName);
					}
				},
				required : function(value) { // value：表单的值、item：表单的DOM对象
					if (value) {
						value = value.trim();
					}
					
					if (!value) {
						var curr = this;
						if (that.customModularCode.indexOf(this.param.vueObj.modular.customModularCode) == -1) {
							that.customModularCode.push(this.param.vueObj.modular.customModularCode);
							setTimeout(function() {
								that.formVerify.scrollToField(that, curr);
							}, 300);
						} else {
							if (this.param && this.param.vueObj) {
								that.formVerify.scrollToField(that, curr);
							}
						}
						return "不能为空";
					}
				},
				// 长度校验
				limit : function(value) { // value：表单的值、item：表单的DOM对象
					if (value) {
						value = value.trim();
					}
					var limit = this.param.limit || 200;
					if (value.replace(/\n/g, "aaaa").replace(/\n/g, "aaaa").length > limit) {
						var curr = this;
						if (that.customModularCode.indexOf(this.param.vueObj.modular.customModularCode) == -1) {
							that.customModularCode.push(this.param.vueObj.modular.customModularCode);
							setTimeout(function() {
								that.formVerify.scrollToField(that, curr);
							}, 300);
						} else {
							if (this.param && this.param.vueObj) {
								that.formVerify.scrollToField(that, curr);
							}
						}
						return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
					}
				},
				customRegex : function(value) {
					if (value) {
						value = value.trim();
					}
					
					var dict = that.formVerify.customRegexDictList[this.param.customRegex];
					if (dict && value) {
						var reg = new RegExp(dict.remark);
						if (!reg.test(value)) {
							var curr = this;
							if (that.customModularCode.indexOf(this.param.vueObj.modular.customModularCode) == -1) {
								that.customModularCode.push(this.param.vueObj.modular.customModularCode);
								setTimeout(function() {
									that.formVerify.scrollToField(that, curr);
								}, 300);
							} else {
								if (this.param && this.param.vueObj) {
									that.formVerify.scrollToField(that, curr);
								}
							}
							return "请输入正确的" + dict.dictName;
						}
					}
				}
			}
		};
	},
	methods : {
		defaultApprovalInit : function() {
			var that = this;
			var param = that.param;
			var approvalBelongFlowNodeCode = param.approvalBelongFlowNodeCode;
			if (approvalBelongFlowNodeCode) {
				return ajax({
					url : basePath + "frame/approvalFlowRecord/defaultApprovalInit.spring",
					data : that.param,
				}).then(function(data) {
					
					
					if (that.param.submitType == 1 ) {
						that.hasSaveBtn = true;
					}
					if(data.finishExecRight){
						that.hasFinish = true;
					}
		
					if (data.approvalBelongFlowNode.state != 2) {// 协助和回退审批不显示按钮
						that.hasStateStatusNo = true;
					}
					
					if (data.approvalBelongFlowNode.approvalFlowNodeType == 3) {// 协助不显示按钮
						that.hasCountersign = true;
					}
					
					if (that.param.submitType == 1 && that.param.inLoop == 1 && data.approvalBelongFlowNode.state != 2) {// 协助不显示按钮
						that.inLoop = true;
					}
					
					that.initStatus(data);
					if (data.approvalBelongFlowNode.state != 2 && data.nextApprovalBelongFlowNode) {
						for (var i = 0; i < data.customFormTypeStateList.length; i++) {
							if (data.customFormTypeStateList[i].customFormTypeStateNo == data.nextApprovalBelongFlowNode.approvalNodeState) {
								that.formData.stateStatusNo = data.nextApprovalBelongFlowNode.approvalNodeState || -999;
								break;
							}
						}
					}
					
					if (!that.hasCountersign && that.inLoop) {
						that.cols = 8;
					} else if (that.hasCountersign && !that.inLoop) {
						that.cols = 8;
					} else if (that.hasCountersign && that.inLoop) {
						that.cols = 6;
					}
					
					if (data.approvalBelongFlowNodeRecord) {
						if (data.isDefault) {
							that.formData.approvalBelongFlowNodeRecordDraftCode = data.approvalBelongFlowNodeRecord.approvalBelongFlowNodeRecordCode;
						}
						that.param.customFormFilledCode = data.approvalBelongFlowNodeRecord.approvalFormBelongCode;
					}
				});
			}
			/*else if (that.param.hasDynamic == 1 && that.param.submitType == 1) {
				that.hasDynamic = true;
			}*/
			return new Promise(function(resolve, reject) {
				resolve();
			});
		},
		initStatus : function(data) {
			this.customFormTypeStateList.push({
				name : "自动",
				value : -999
			});
			for (var i = 0; i < data.customFormTypeStateList.length; i++) {
				this.customFormTypeStateList.push({
					name : data.customFormTypeStateList[i].customFormTypeStateName,
					value : data.customFormTypeStateList[i].customFormTypeStateNo
				});
			}
		},
		onSubmit : function(v) {
			var that = this;
			/*if (that.submitType == 1) {
				that.verifyDynamic = false;
			}*/
			that.$nextTick(function() {
				if (that.$refs.forms.form.__verify()) {
					var values = that.handleFormValues();
					if (that.submitType == 0) {
						that.save(values);
					} else if (that.submitType == 1) {
						that.saveLoop(values);
					} else if (that.submitType == 2) {
						that.saveCountersign(values);
					} else if (that.submitType == 3) {
						that.saveFinish(values);
					}
				}
				
//				that.verifyDynamic = true;
			})
		},
		handleFormValues : function() {
			var that = this;
			var values = {};
			for (var i = 0; i < that.customModularList.length; i++) {
				for ( var key in that.customModularList[i].values) {
					values[key] = that.customModularList[i].values[key];
				}
			}
			
			for ( var key in that.attaValues) {
				values[key] = that.attaValues[key];
			}
			
			for ( var key in that.param) {
				values[key] = that.param[key];
			}
			
			for ( var key in that.formData) {
				values[key] = that.formData[key];
			}
			
			values.customModularCode = that.customModularList[0].customModularCode;
			return values;
		},
		verify : function(name, param) {
			var that = this;
			var verifyNames = name.split("|");
			var result = [];
			for (var i = 0; i < verifyNames.length; i++) {
				if (that.formVerify[verifyNames[i]]) {
					result.push({
						validator : that.formVerify[verifyNames[i]],
						param : param,
						trigger : "onChange"
					});
				}
			}
			return result;
		},
		// 结束循环
		saveLoop : function(formDatas) {
			var that = this;
			formDatas.approvalMethod = "loop";
			assemblys.confirm("确定结束循环吗？", function() {
				if (isSubmit) {
					return;
				}
				isSubmit = true;
				ajax({
					url : basePath + "frame/approvalFlowRecord/getLoopEndNextNode.spring",
					data : {
						approvalBelongCode : that.param.approvalBelongCode,
						funCode : that.param.funCode,
						appCode : that.param.appCode,
					},
				}).then(function(data) {
					if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
						that.hasDynamic = true;
						that.selectApproverParam.callback = function(vs) {
							var values = [];
							for (var i = 0; i < vs.length; i++) {
								values.push(vs[i].value);
							}
							
							var approvalUIDs = values.join(",");
							var approvalFlowNodeData = {
								approvalRight : 3,
								approvalUIDs : approvalUIDs
							}

							formDatas.approvalFlowNodeData = JSON.stringify(approvalFlowNodeData);
							that.saveCustomFormFilled(0, formDatas);
							
							that.selectApproverParam.callback = null;
							that.hasDynamic = false;
						}
						that.$refs.approvalUIDs.show = true;
						that.$refs.approvalUIDs.loadParent();
					} else {
						formDatas.approvalFlowNodeData = "{}";
						that.saveCustomFormFilled(0, formDatas);
					}
				});
				
			});
			
		},
		getNextApprovalBelongFlowNode : function(type, formDatas) {
			if (isSubmit) {
				return;
			}
			isSubmit = true;
			var that = this;
			
			that.submitFun(2, that.handleFormValues(), true).then(function() {
				ajax({
					url : basePath + "frame/approvalFlow/getNextApprovalBelongFlowNode.spring",
					data : {
						approvalBelongCode : that.param.approvalBelongCode,
						appCode : that.param.appCode,
					},
				}).then(function(data) {
					if (data.nextApprovalBelongFlowNode && (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 1 || (data.nextApprovalBelongFlowNode.approvalFlowNodeType == 3 && JSON.parse(data.nextApprovalBelongFlowNode.approvalFlowNodeData).countersignMethod == 1))) {
						that.hasDynamic = true;
						that.selectApproverParam.callback = function(vs) {
							that.hasDynamic = false;
							var values = [];
							for (var i = 0; i < vs.length; i++) {
								values.push(vs[i].value);
							}
							
							if (values.length == 0) {
								assemblys.msg('请选择审批人', null, {
									type : 'warning',
								});
								isSubmit = false;
								return;
							}
							
							var approvalUIDs = values.join(",");
							var approvalFlowNodeData = {
								approvalRight : 3,
								approvalUIDs : approvalUIDs
							}

							formDatas.approvalFlowNodeData = JSON.stringify(approvalFlowNodeData);
							that.saveCustomFormFilled(type, formDatas);
							
							that.selectApproverParam.callback = null;
						}
						that.$refs.approvalUIDs.show = true;
						that.$refs.approvalUIDs.loadParent();
					} else {
						formDatas.approvalFlowNodeData = "{}";
						that.saveCustomFormFilled(type, formDatas);
					}
				});
			});
			
		},
		saveCountersign : function(values) {
			values.approvalMethod = "";
			this.getNextApprovalBelongFlowNode(1, values);
		},
		save : function(values) {
			this.getNextApprovalBelongFlowNode(0, values);
		},
		getCustomFormData : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/newCustomForm/getCustomFormData.spring",
				data : {
					"customFormBusinessCode" : that.param.customFormBusinessCode,
					"customFormCode" : that.param.customFormCode,
					"compNo" : that.param.compNo,
					"customFormFilledCode" : that.param.customFormFilledCode,
					"appCode" : that.param.appCode
				}
			}).then(function(data) {
				
				if (data.customForm.hasFileUpload == 1) {
					that.showFileUpload = true;
				}
				
				that.baseImgPath = data.baseImgPath;
				if (that.param.customFormFilledCode) {
					that.$refs.uploader.getAttachments(that.param.customFormFilledCode);
				}
				that.param.customFormCode = data.customForm.customFormCode;
				that.customModularList = data.customModularList;
				
				for (var i = 0; i < that.customModularList.length; i++) {
					var customModular = that.customModularList[i];
					customModular.values = {};// 表单值
					customModular.refs = {};// 多选和日期组件需要用到关联element
					customModular.dates = {};// 日期组件需要用到
					customModular.count = Vue.ref(1);// 分类副本数量，绑定用
					customModular.$count = 1;
					customModular.showModular = Vue.ref(true);
					customModular.customFieldMap = {};
					
					for (var j = 0; j < customModular.customFieldList.length; j++) {
						var customField = customModular.customFieldList[j];
						customModular.customFieldMap[customField.customFieldCode] = customField;
						if (customField.fieldData && customField.fieldData.length > 0) {
							that["field_" + customField.fieldData[0].customFieldSet.replace("Other", "")](customModular, customField);
						} else {
							that["field_" + customField.customFieldSet](customModular, customField);
						}
					}
					
					// 回显
					for ( var key in customModular.reportValues) {
						if (assemblys.isArray(customModular.values[key])) {
							customModular.values[key] = customModular.reportValues[key];
						} else {
							customModular.values[key] = customModular.reportValues[key][0];
						}
						
						var index = key.split("-")[1];
						var v = customModular.reportValues[key];
						for (var k = 0; k < v.length; k++) {
							var customModularCodeAry = that.customOptionSetCodeMap[v[k]];
							if (customModularCodeAry) {
								for (var l = 0; l < customModularCodeAry.length; l++) {
									var num = that.relationCodeMap[customModularCodeAry[l]][index];
									if (!num) {
										num = 0;
									}
									num++;
									that.relationCodeMap[customModularCodeAry[l]][index] = num;
								}
							}
						}
						
					}
					
					that.customModularCode.push(customModular.customModularCode);
				}
				return data;
			});
		},
		getFormVerify : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/dict/getDictListByCode.spring",
				data : {
					"dictTypeCode" : "CUSTOMFORM_VERIFY",
					"appCode" : "APP"
				},
				skipDataCheck : true
			}).then(function(data) {
				if (data.result == "success") {
					for (var i = 0; i < data.dictList.length; i++) {
						that.formVerify.customRegexDictList[data.dictList[i].dictContent] = data.dictList[i];
					}
				}
			});
		},
		saveCustomFormFilled : function(type, formData) {
			var that = this;
			
			that.submitFun(type, formData);
		},
		saveFinish : function(values) {
			let that = this;
			assemblys.confirm('确定一键结束审批吗？', function() {
				that.submitFun(3, values);
			});
		},
		submitFun : function(type, formData, showMsg) {
			formData.type = type || 0;
			if (type == 2) {
				formData.saveState = 0;
			} else {
				formData.saveState = 1;
			}
			return ajax({
				url : basePath + "frame/newCustomForm/saveCustomFormFilled.spring",
				type : "post",
				data : formData
			}).then(function(data) {
				if (!showMsg) {
					assemblys.msg(type != 2 ? '提交成功' : "保存成功", function() {
						if (type != 2) {
							history.back();
						}
					});
				}
				if (data.approvalBelongFlowNodeRecordDraftCode) {
					that.formData.approvalBelongFlowNodeRecordDraftCode = data.approvalBelongFlowNodeRecordDraftCode;
				}
			});
		},
		field_radio : function(customModular, customField) {
			var values = customModular.values;
			var refs = customModular.refs;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
			var customOptionSetList = customField.fieldData;
			for (var i = 0; i < customOptionSetList.length; i++) {
				if (customOptionSetList[i].childOptionList && customOptionSetList[i].childOptionList.length > 0) {
					if (customOptionSetList[i].childOptionList[0].customFieldSet.replace("Other", "") == "radio") {
						values[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref("");
					} else {
						values[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref([]);
						refs[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref([]);
					}
				}
			}
		},
		field_checkbox : function(customModular, customField) {
			var values = customModular.values;
			var refs = customModular.refs;
			values[customField.customFieldCode + "-0"] = Vue.ref([]);
			refs[customField.customFieldCode + "-0"] = Vue.ref([]);
			var customOptionSetList = customField.fieldData;
			for (var i = 0; i < customOptionSetList.length; i++) {
				if (customOptionSetList[i].childOptionList && customOptionSetList[i].childOptionList.length > 0) {
					if (customOptionSetList[i].childOptionList[0].customFieldSet.replace("Other", "") == "radio") {
						values[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref("");
					} else {
						values[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref([]);
						refs[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref([]);
					}
				}
			}
		},
		field_select : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref([]);
		},
		field_datetime : function(customModular, customField) {
			var values = customModular.values;
			var refs = customModular.refs;
			var dates = customModular.dates;
			values[customField.customFieldCode + "-0"] = "";
			dates[customField.customFieldCode + "-0"] = new Date();
			refs[customField.customFieldCode + "-0"] = Vue.ref(false);
		},
		field_text : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
		},
		field_textarea : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
		},
		field_interface : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
		},
		field_org : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
			values["remark--" + customField.customFieldCode + "-0"] = "";
		},
		field_label : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
		},
		field_img : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
		},
		field_file : function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
		}
	},
	provide : function() {
		return {
			relationCodeMap : this.relationCodeMap,
			customOptionSetCodeMap : this.customOptionSetCodeMap,
			attaValues : this.attaValues,
			formVerify : this.formVerify,
			customFormClass : this.customFormClass
		};
	}
};