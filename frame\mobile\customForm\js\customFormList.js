page.customFormList.option = {
	created : function() {
		var that = this;
		that.initTopButton();
		assemblys.getMenuIcon(that.param.funCode, true).then(function(data) {
			that.param.compNo = data.compNo;
			if (data.compList && data.compList.length > 0) {
				var options = [];
				for (var i = 0; i < data.compList.length; i++) {
					options.push({
						name : data.compList[i].compName,
						value : data.compList[i].compNo
					});
				}
				
				that.fieldList.push({
					label : "医院",
					fieldSet : "select",
					key : "compNo",
					notAll : true,
					options : options
				});
			}
			return that.getAppList();
		}).then(function() {
			that.$nextTick(function() {
				that.onSelect();
			});
		});
	},
	components : {
		"custom-filter-search" : null,
		"custom-list" : null
	},
	data : function() {
		var fieldList = [ {
			label : "关键字",
			fieldSet : "text",
			key : "keyword",
			placeholder : "表单名称"
		}, {
			label : "状态",
			fieldSet : "select",
			key : "status",
			allValue : "99",
			options : [ {
				name : "有效",
				value : "1"
			}, {
				name : "无效",
				value : "0"
			} ]
		} ];
		return {
			search : Vue.ref(false),
			fieldList : Vue.ref(fieldList),
			selectType : Vue.ref(""),
			checkboxSelectAll : Vue.ref(false),
			param : Vue.ref({
				appCode : top.page.index.vm.application.appCode,
				status : "",
				type : "2"
			}),
			data : {
				//调用表单列表接口
				url : basePath + "frame/newCustomForm/getCustomFormListData.spring?",
				method : "get",
				param : {
					"customFormClass" : this.param.customFormClass,
					"type" : this.param.type,
				},
				cols : [ {
					left : {
						key : "customFormName",
						encode : true
					},
					right : {
						key : function(d) {
							var html;
							if (d.status == 0) {
								html = '<font style="color: red">无效</font>';
							} else {
								html = '<font style="color: green;">有效</font>';
							}
							return html;
						}
					}
				}, {
					left : {
						key : "customFormTypeName",
						encode : true
					},
					right : {
						key : "appCode",
						encode : true
					}
				}, {
					left : {
						key : function(d) {
							var html;
							if (d.customFormClass == 0) {
								html = '<font style="color: green">上报</font>';
							} else {
								html = '<font style="color: blue;">审批</font>';
							}
							return html;
						},
					},
					right : {
						key : "businessCode",
						encode : true
					}
				} ],
			}
		};
	},
	methods : {
		//查询
		onSelect : function(values) {
			for ( var k in values) {
				this.param[k] = values[k];
			}
			
			this.data.param = this.param;
			if (this.data.param.status == "") {
				this.data.param.status = "99";
			}
			
			this.search = false;
			this.$refs.list.onRefresh();
		},
		initTopButton : function() {
			var that = this;
			var html = '';
			html += '<van-icon name="search" size="20" @click="showSearch" :style="{height: \'24px\'}"></van-icon>';
			
			//重写右上角导航栏
			top.page.index.vm.initTopRightTitle({
				template : html,
				props : [ "showIcon" ],
				data : function() {
					return {};
				},
				methods : {
					showSearch : function() {
						that.$root.search = !that.$root.search;
					}
				}
			});
		},
		getAppList : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/newCustomForm/getExceAppList.spring",
				data : {
					compNo : that.param.compNo
				}
			}).then(function(data) {
				if (data.appList && data.appList.length > 0) {
					var options = [];
					for (var i = 0; i < data.appList.length; i++) {
						options.push({
							name : data.appList[i].appName,
							value : data.appList[i].appCode
						});
					}
					
					that.fieldList.push({
						label : "应用系统",
						fieldSet : "select",
						key : "appCode",
						notAll : true,
						options : options
					});
				}
			});
		},
		toCustomFormTemplate : function(item, index) {
			var url = basePath + "frame/mobile/customForm/customFormTemplate.html?1=1";
			var params = "&customFormCode=" + item.customFormCode + "&type=3&compNo=" + this.param.compNo;
			params += "&appCode=" + this.param.appCode;
			location.url({
				url : url + params
			});
		}
	},
};
