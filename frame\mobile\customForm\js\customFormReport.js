page.customFormReport.option = {
	created : function() {
		var that = this;
		that.initTopButton();
		that.data.param = that.param;
		assemblys.getMenuIcon(that.param.funCode, true).then(function(data) {
			that.param.compNo = data.compNo;
			if (data.compList && data.compList.length > 0) {
				var options = [];
				for (var i = 0; i < data.compList.length; i++) {
					options.push({
						name : data.compList[i].compName,
						value : data.compList[i].compNo
					});
				}
				
				that.fieldList.unshift({
					label : "医院",
					fieldSet : "select",
					key : "compNo",
					options : options
				});
			}
		}).then(function(data) {
			that.$refs.list.onRefresh();
		});
	},
	components : {
		"custom-filter-search" : null,
		"custom-list" : null
	},
	data : function() {
		return {
			selectType : Vue.ref(""),
			
			//过滤条件
			search : Vue.ref(false),
			fieldList : Vue.ref([]),
			
			param : Vue.ref({
				funCode : "",
				appCode : "",
				customFormTypeCode : "",
				customFormTypeMenuCode : "",
				customFormClass : "0",
				type : "1",
				compNo : "",
				curPageNum : 0
			}),
			// 列表数据
			data : {
				url : basePath + "frame/newCustomForm/getCustomFormListData.spring",
				parseData : function(data) {
					return data;
				},
				cols : [ {
					left : {
						key : "customFormName"
					}
				} ]
			}
		}
	},
	methods : {
		listOnClick : function(item, index) {
			var that = this;
			//事件填报
			//var url = basePath  + "frame/mobile/customForm/customFormTemplate.html?";
			var url = basePath + "frame/mobile/customForm/registerReport.html?";
			url += "customFormCode=" + item.customFormCode;
			url += "&type=" + that.param.type;
			url += "&hasBack=" + that.param.hasBack;
			url += "&compNo=" + that.param.compNo;
			url += "&appCode=" + that.param.appCode;
			url += "&funCode=" + that.param.funCode;
			location.url({
				url : url
			});
		},
		showPopup : function() {
			var that = this;
			that.search = true;
		},
		//查询
		onSelect : function(values) {
			var that = this;
			this.searchParam = values;
			if (values) {
				for ( var key in values) {
					this.param[key] = values[key];
				}
			}
			this.search = false;
			this.$refs.list.onRefresh();
		},
		reset : function() {
			var that = this;
			for ( var key in this.searchParam) {
				this.param[key] = null;
			}
		},
		initTopButton : function() {
			var that = this;
			var html = '';
			html += '<van-icon name="search" size="20" @click="showSearch" :style="{height: \'24px\'}"></van-icon>';
			html += '<van-popover v-model:show="showPopover" theme="dark" :actions="actions" placement="bottom-end" @select="onSelect" :offset="offset">';
			html += '	<template #reference>';
			html += '		<van-icon v-show="showIcon[0]" name="ellipsis" size="20" :style="{marginLeft: \'5px\'}"></van-icon>';
			html += '	</template>';
			html += '</van-popover>';
			
			that.fieldList = [ {
				label : "关键字",
				fieldSet : "text",
				key : "keyword",
				placeholder : "表单名称"
			} ];
			
			// 重写右上角导航栏
			top.page.index.vm.initTopRightTitle({
				template : html,
				props : [ "showIcon" ],
				data : function() {
					const showPopover = top.Vue.ref(false);
					return {
						showPopover : showPopover,
						offset : [ 0, 20 ]
					};
				},
				methods : {
					showSearch : function() {
						that.$root.search = !that.$root.search;
					},
					onSelect : function(data) {
						that.$refs.list.onRefresh();
					}
				}
			});
		},
	}
}
