page.customFormReportType.option = {
	created : function() {
		var that = this;
		that.initTopButton();
		assemblys.getMenuIcon(that.param.funCode, true).then(function(data) {
			that.param.compNo = data.compNo;
			if (data.compList && data.compList.length > 0) {
				var options = [];
				for (var i = 0; i < data.compList.length; i++) {
					options.push({
						name : data.compList[i].compName,
						value : data.compList[i].compNo
					});
				}
				
				that.fieldList.unshift({
					label : "医院",
					fieldSet : "select",
					key : "compNo",
					options : options
				});
			}
		}).then(function(data) {
			that.getCustomFormTypeList();
		});
	},
	components : {
		"custom-filter-search" : null,
		"custom-list" : null
	},
	data : function() {
		var typeList = [ {
			"icon" : "&#xe779;",
			"iconType" : "2",
			"text" : "全部",
		} ]
		return {
			//过滤条件
			search : Vue.ref(false),
			fieldList : Vue.ref([]),
			
			typeList : Vue.ref(typeList),
			
			param : Vue.ref({
				funCode : "",
				appCode : "",
				compNo : "CF148",
				curPageNum : 0
			})
		}
	},
	methods : {
		getCustomFormTypeList : function(item, index) {
			var that = this;
			ajax({
				url : basePath + "frame/customFormType/getCustomFormTypeList.spring",
				data : {
					appCode : that.param.appCode,
					funCode : that.param.funCode,
					compNo : that.param.compNo,
				},
			}).then(function(data) {
				if (data.customFormTypeList.length > 0) {
					var typeList = data.customFormTypeList;
					for (var i = 0; i < typeList.length; i++) {
						that.typeList.push({
							"code" : typeList[i].customFormTypeCode,
							"icon" : typeList[i].customFormTypeIcon || "&#xe779;",
							"iconType" : typeList[i].customFormTypeIconType || "2",
							"text" : typeList[i].customFormTypeName,
						});
					}
				}
			});
		},
		showPopup : function() {
			var that = this;
			that.search = true;
		},
		//查询
		onSelect : function(values) {
			var that = this;
			this.searchParam = values;
			if (values) {
				for ( var key in values) {
					this.param[key] = values[key];
				}
			}
			this.search = false;
			this.$refs.list.onRefresh();
		},
		reset : function() {
			var that = this;
			for ( var key in this.searchParam) {
				this.param[key] = null;
			}
		},
		initTopButton : function() {
			var that = this;
			var html = '';
			html += '<van-icon name="search" size="20" @click="showSearch" :style="{height: \'24px\'}"></van-icon>';
			html += '<van-popover v-model:show="showPopover" theme="dark" :actions="actions" placement="bottom-end" @select="onSelect" :offset="offset">';
			html += '	<template #reference>';
			html += '		<van-icon v-show="showIcon[0]" name="ellipsis" size="20" :style="{marginLeft: \'5px\'}"></van-icon>';
			html += '	</template>';
			html += '</van-popover>';
			
			that.fieldList = [ {
				label : "关键字",
				fieldSet : "text",
				key : "keyword",
				placeholder : "分类名称"
			} ];
			
			// 重写右上角导航栏
			top.page.index.vm.initTopRightTitle({
				template : html,
				props : [ "showIcon" ],
				data : function() {
					const showPopover = top.Vue.ref(false);
					return {
						showPopover : showPopover,
						offset : [ 0, 20 ]
					};
				},
				methods : {
					showSearch : function() {
						that.$root.search = !that.$root.search;
					},
					onSelect : function(data) {
						that.$refs.list.onRefresh();
					}
				}
			});
		},
		listOnClick : function(item, index) {
			var that = this;
			//事件填报
			var url = basePath + "frame/mobile/customForm/customFormList.html?";
			url += "customFormTypeCode=" + item.code || "";
			url += "&compNo=" + that.param.compNo;
			url += "&appCode=" + that.param.appCode;
			url += "&funCode=" + that.param.funCode;
			location.url({
				url : url
			});
		},
	}
}
