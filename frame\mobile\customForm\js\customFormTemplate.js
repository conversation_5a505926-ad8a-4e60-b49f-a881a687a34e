page.form.option = {
	created: function() {
		var that = this;
		assemblys.getMenuIcon("CUSTOM_FORM", false).then(function(data) {
			that.param.compNo = data.compNo;
		});
		//倒计时自动保存草稿 hwx 2022-11-15只对type=1的
		if (that.param.type == 1) {
			that.initSaveTimer();
		}

		that.getFormVerify();
		that.getCustomFormFilled().then(function() {
			if (top === parent || that.param.hasTopButton == 1) {
				that.initTopButton();
			}
		});
		that.getOptionRelation().then(function(data) {
			return that.getCustomFormData();
		}).then(function() {
			if (top === parent) {
				for (var i = 0; i < that.customModularList.length; i++) {
					that.customModularList[i].showModular = true;
				}
			} else if (parent.initObj) {
				for (var key in parent.initObj) {
					if (typeof parent.initObj[key] == "function") {
						parent.initObj[key].call(parent.window, window);
					}
				}
			} else {
				for (var i = 0; i < that.customModularList.length; i++) {
					that.customModularList[i].showModular = true;
				}
			}
		});
	},
	data: function() {
		var that = this;
		return {
			param: Vue.ref({
				appCode: "",
				funCode: "",
				compNo: "",
				approvalBelongFlowNode: {},
			}),
			baseImgPath: basePath,
			customFormFilled: null,
			approvalBelongFlowNode: Vue.ref({}),
			relationCodeMap: Vue.ref({}),
			customOptionSetCodeMap: Vue.ref({}),
			customModularList: Vue.ref([]),
			customModularCode: Vue.ref(["atta"]),
			form: null,
			hasRollbackStatus: Vue.ref(null),
			customFormCode: Vue.ref(""),
			showFileUpload: false,
			fileList: Vue.ref([]),
			attaValues: Vue.ref({
				attaName: [],
				attaUrl: [],
				attaSize: [],
				attaType: [],
				attaCode: []
			}),
			attaFieldValues: Vue.ref({}),
			saveState: 0,
			showTime: Vue.ref(false),
			showWaiteTime: Vue.ref(""),
			sysSecond: Vue.ref(0),
			showWaiteObj: null,
			formVerify: {
				customRegexDictList: {},
				scrollToField: function(that, curr) {
					var boundingClientRect = curr.param.vueObj.$el.nextElementSibling.getBoundingClientRect();
					var offsetHeight = top.document.querySelector(".body-iframe").offsetHeight;
					if (curr.param && curr.param.vueObj && (boundingClientRect.top < 0 || boundingClientRect.top + 30 > offsetHeight)) {
						that.$refs.form.scrollToField(curr.param.vueObj.customFieldName);
					}
				},
				required: function(value) { // value：表单的值、item：表单的DOM对象
					if (value) {
						value = value.trim();
					}
					if (!this.param.vueObj.modular.show || !this.param.vueObj.modular.showModular) {
						return;
					}

					if (!this.param.vueObj.field.customFieldRowCode) {
						var parentVal = "";
						if (this.param.vueObj.field.parentFieldSet == "radio") {
							parentVal = this.param.vueObj.values[this.param.vueObj.parentCustomFieldCode + "-" + this.param.vueObj.index];
						} else {
							var ary = this.param.vueObj.values[this.param.vueObj.field.parentCustomFieldCode + "-" + this.param.vueObj.index];
							for (var i = 0; i < ary.length; i++) {
								if (this.param.vueObj.field.customFieldCode == ary[i]) {
									parentVal = ary[i];
									break;
								}
							}
						}
						if (!parentVal) {
							return;
						}
					} else {
						relationNum = that.relationCodeMap[this.param.vueObj.field.customFieldRowCode];
						result = !relationNum || relationNum[0] > 0;
						if (!result) {
							return;
						}
					}

					if (!value) {
						var curr = this;
						if (that.customModularCode.indexOf(this.param.vueObj.modular.customModularCode) == -1) {
							that.customModularCode.push(this.param.vueObj.modular.customModularCode);
							setTimeout(function() {
								that.formVerify.scrollToField(that, curr);
							}, 300);
						} else {
							if (this.param && this.param.vueObj) {
								that.formVerify.scrollToField(that, curr);
							}
						}
						return "不能为空";
					}
				},
				// 长度校验
				limit: function(value) { // value：表单的值、item：表单的DOM对象
					if (value) {
						value = value.trim();
					}
					if (!this.param.vueObj.modular.show || !this.param.vueObj.modular.showModular) {
						return;
					}
					var limit = this.param.limit || 200;
					if (value.replace(/\n/g, "aaaa").replace(/\n/g, "aaaa").length > limit) {
						var curr = this;
						if (that.customModularCode.indexOf(this.param.vueObj.modular.customModularCode) == -1) {
							that.customModularCode.push(this.param.vueObj.modular.customModularCode);
							setTimeout(function() {
								that.formVerify.scrollToField(that, curr);
							}, 300);
						} else {
							if (this.param && this.param.vueObj) {
								that.formVerify.scrollToField(that, curr);
							}
						}
						return "长度不能大于" + limit + "，当前长度：" + value.replace(/\n/g, "aaaa").length;
					}
				},
				customRegex: function(value) {
					if (value) {
						value = value.trim();
					}

					if (!this.param.vueObj.modular.show || !this.param.vueObj.modular.showModular) {
						return;
					}

					var dict = that.formVerify.customRegexDictList[this.param.customRegex];
					if (dict && value) {
						var reg = new RegExp(dict.remark);
						if (!reg.test(value)) {
							var curr = this;
							if (that.customModularCode.indexOf(this.param.vueObj.modular.customModularCode) == -1) {
								that.customModularCode.push(this.param.vueObj.modular.customModularCode);
								setTimeout(function() {
									that.formVerify.scrollToField(that, curr);
								}, 300);
							} else {
								if (this.param && this.param.vueObj) {
									that.formVerify.scrollToField(that, curr);
								}
							}
							return "请输入正确的" + dict.dictName;
						}
					}
				}
			}
		};
	},
	methods: {
		onSubmit: function(v) {
			var that = this;
			var values = that.handleFormValues();
			that.saveCustomFormFilled(that.saveState, values);
		},
		handleFormValues: function() {
			var that = this;
			var values = {};

			for (var i = 0; i < that.customModularList.length; i++) {
				for (var key in that.customModularList[i].values) {
					values[key] = that.customModularList[i].values[key];
				}
			}

			// 表单附件对象
			var attaLists = {
				attaCode: [],
				attaName: [],
				attaSize: [],
				attaType: [],
				attaUrl: []
			};

			// 附件组件
			var attaFieldValues = that.attaFieldValues;
			for (var code in attaFieldValues) {
				var attaList = attaFieldValues[code] || [];
				if (attaList.length > 0) {
					for (var j = 0; j < attaList.length; j++) {
						var atta = attaList[j];
						attaLists["attaCode"].push(atta.attaCode);
						attaLists["attaName"].push(atta.attaName);
						attaLists["attaSize"].push(atta.attaSize);
						attaLists["attaType"].push(atta.attaType);
						attaLists["attaUrl"].push(atta.attaUrl);
					}
				}
			}

			// 全局附件
			var attaValues = that.attaValues;
			for (var key in attaValues) {
				var attaList = attaValues[key];
				for (var i = 0; i < attaList.length; i++) {
					var value = attaList[i];
					attaLists[key].push(value);
				}
			}

			// 	处理附件组件数据
			for (var key in attaLists) {
				values[key] = attaLists[key];
			}

			for (var key in that.param) {
				values[key] = that.param[key];
			}

			var customModularCode = [];
			for (var i = 0; i < that.customModularList.length; i++) {
				if (that.customModularList[i].show) {
					customModularCode.push(that.customModularList[i].customModularCode);
				}
			}
			values.customModularCode = customModularCode;
			return values;
		},
		verify: function(name, param) {
			var that = this;
			var verifyNames = name.split("|");
			var result = [];
			for (var i = 0; i < verifyNames.length; i++) {
				result.push({
					validator: that.formVerify[verifyNames[i]],
					param: param,
					trigger: "onChange"
				});
			}
			return result;
		},
		saveDraft: function() {
			this.saveState = 0;
			var values = this.handleFormValues();
			this.saveCustomFormFilled(this.saveState, values);
		},
		save: function() {
			this.saveState = 1;
			this.$refs.form.submit();
		},
		//hwx 2022-4-26增加定时器保存
		initSaveTimer: function() {
			var that = this;
			return ajax({
				url: basePath + "frame/dict/getDictListByCode.spring",
				data: {
					"dictTypeCode": "Switch",
					"appCode": that.param.appCode
				},
				dataType: "json",
				skipDataCheck: true
			}).then(function(data) {
				if (data.dictList) {
					for (var i = 0; i < data.dictList.length; i++) {
						var dictList = data.dictList[i];
						if (dictList.dictCode == "FormSaveTimer") {
							that.param.showTime = true;
							that.sysSecond = 60 * Number(dictList.dictContent || 3);
							that.setRemainTime();
						}
					}
				}
			});
		},
		//hwx 2022-7-19增加定时器保存
		setRemainTime: function() {
			var that = this;
			var SysSecond = that.sysSecond;
			that.showWaiteObj = setInterval(function() {
				if (SysSecond > 0) {
					SysSecond = SysSecond - 1;
					var second = Math.floor(SysSecond % 60);
					var minite = Math.floor((SysSecond / 60) % 60);
					that.showWaiteTime = minite + "分" + second + "秒后保存草稿";
					that.initRemainTime();
				} else {
					//到计时完后保存草稿
					window.clearInterval(that.showWaiteObj);
					page.form.vm.saveDraft();
				}
			}, 1000);
		},
		//显示倒计时
		initRemainTime: function() {
			var that = this;
			var html = '';
			html += '<van-popover v-model:show="showPopover" >';
			html += '	<template #reference>';
			html += '		<font color="red">' + that.showWaiteTime + '</font>';
			html += '	</template>';
			html += '</van-popover>';
			var showTime = that.param.showTime
			// 重写右上角为倒计时自动保存草稿
			top.page.index.vm.initTopRightTitle({
				template: html,
				data: function() {
					const showPopover = showTime;
					return {
						showPopover: showPopover
					};
				}
			});
		},

		initTopButton: function() {
			var that = this;
			var html = '';
			html += '<van-popover v-model:show="showPopover" theme="dark" :actions="actions" placement="bottom-end" @select="onSubmit" :offset="offset">';
			html += '	<template #reference>';
			html += '		<van-icon name="ellipsis" size="18" />';
			html += '	</template>';
			html += '</van-popover>';

			var buttonAry = [];

			var type = that.param.type // 0预览,1新增,2编辑;3档案

			if (type == 3 || type == 4) {//hwx 2022-10-25 档案提交按钮
				buttonAry.push({
					text: '提交',
					value: 2
				});
			} else {
				if (type == 1 || type == 2) {
					buttonAry.push({
						text: type == 1 ? '保存草稿' : '保存',
						value: 1
					});
				}
				if(that.approvalBelongFlowNode){
					if ( that.param["hasRollbackStatus"] == that.hasRollbackStatus || that.approvalBelongFlowNode.approvalIndex == 0 || type == 1) {
						buttonAry.push({
							text: '提交',
							value: 2
						});
					}
				}else{
					buttonAry.push({
						text: '提交',
						value: 2
					});
				}
				
			}

			buttonAry.push({
				text: '全部收起',
				value: 3,
				isShow: true
			});

			top.page.index.vm.initTopRightTitle({
				template: html,
				data: function() {
					const showPopover = top.Vue.ref(false);
					const actions = buttonAry;
					return {
						showPopover: showPopover,
						actions: actions,
						offset: [0, 20]
					};
				},
				methods: {
					onSubmit: function(action, index) {
						if (action.value == 1) {// 草稿
							page.form.vm.saveDraft();
						} else if (action.value == 2) {// 保存提交
							page.form.vm.save();
						} else if (action.value == 3) {// 全部收起
							if (action.isShow) {
								page.form.vm.customModularCode.length = 0;
								action.isShow = false;
								action.text = "全部展开";
							} else {
								page.form.vm.customModularCode.push("atta");
								for (var i = 0; i < page.form.vm.customModularList.length; i++) {
									page.form.vm.customModularCode.push(page.form.vm.customModularList[i].customModularCode);
								}
								action.isShow = true;
								action.text = "全部收起";
							}
						}
					}
				}
			});
		},
		getCustomFormFilled: function() {
			var that = this;
			return ajax({
				url: basePath + "frame/newCustomForm/getCustomFormFilled.spring",
				data: {
					"customFormFilledCode": that.param.customFormFilledCode,
					"appCode": that.param.appCode
				}
			}).then(function(data) {
				for (var key in data.customFormFilled) {
					that.param[key] = data.customFormFilled[key];
				}
				that.hasRollbackStatus = data.hasRollbackStatus;
				that.approvalBelongFlowNode = data.approvalBelongFlowNode;
			});
		},
		getCustomFormData: function() {
			var that = this;
			return ajax({
				url: basePath + "frame/newCustomForm/getCustomFormData.spring",
				data: {
					"customFormBusinessCode": that.param.customFormBusinessCode,
					"customFormCode": that.param.customFormCode,
					"compNo": that.param.compNo,
					"customFormFilledCode": that.param.customFormFilledCode,
					"appCode": that.param.appCode
				}
			}).then(function(data) {
				that.customForm = data.customForm;

				if (data.customForm.hasFileUpload == 1) {
					that.showFileUpload = true;
				}

				that.baseImgPath = data.baseImgPath;
				if (that.param.customFormFilledCode) {
					that.$refs.uploader.getAttachments(that.param.customFormFilledCode);
				}
				that.param.customFormCode = data.customForm.customFormCode;
				that.customModularList = data.customModularList;

				for (var i = 0; i < that.customModularList.length; i++) {
					var customModular = that.customModularList[i];
					customModular.values = {};// 表单值
					customModular.refs = {};// 多选和日期组件需要用到关联element
					customModular.dates = {};// 日期组件需要用到
					customModular.count = Vue.ref(customModular.maxIndex + 1);// 分类副本数量，绑定用
					customModular.$count = customModular.maxIndex + 1;
					customModular.showModular = Vue.ref(true);
					customModular.customFieldMap = {};

					for (var j = 0; j < customModular.customFieldList.length; j++) {
						var customField = customModular.customFieldList[j];
						customModular.customFieldMap[customField.customFieldCode] = customField;
						if (customField.fieldData && customField.fieldData.length > 0) {
							that["field_" + customField.fieldData[0].customFieldSet.replace("Other", "")](customModular, customField);
						} else {
							that["field_" + customField.customFieldSet](customModular, customField);
						}
					}

					// 回显
					for (var key in customModular.reportValues) {
						if (assemblys.isArray(customModular.values[key])) {
							customModular.values[key] = customModular.reportValues[key];
						} else {
							customModular.values[key] = customModular.reportValues[key][0];
						}

						var index = key.split("-")[1];
						var v = customModular.reportValues[key];
						for (var k = 0; k < v.length; k++) {
							var customModularCodeAry = that.customOptionSetCodeMap[v[k]];
							if (customModularCodeAry) {
								for (var l = 0; l < customModularCodeAry.length; l++) {
									var num = that.relationCodeMap[customModularCodeAry[l]][index];
									if (!num) {
										num = 0;
									}
									num++;
									that.relationCodeMap[customModularCodeAry[l]][index] = num;
								}
							}
						}
					}
					that.customModularCode.push(customModular.customModularCode);
				}
				return data;
			});
		},
		getFormVerify: function() {
			var that = this;
			return ajax({
				url: basePath + "frame/dict/getDictListByCode.spring",
				data: {
					"dictTypeCode": "CUSTOMFORM_VERIFY",
					"appCode": "APP"
				},
				skipDataCheck: true
			}).then(function(data) {
				if (data.result == "success") {
					for (var i = 0; i < data.dictList.length; i++) {
						that.formVerify.customRegexDictList[data.dictList[i].dictContent] = data.dictList[i];
					}
				}
			});
		},
		getOptionRelation: function() {
			var that = this;
			var url = basePath + "frame/newCustomForm/getCustomRelatedList.spring";
			return ajax({
				url: url,
				data: {
					"customFormCode": that.param.customFormCode,
					"customFormBusinessCode": that.param.customFormBusinessCode,
					"compNo": that.param.compNo,
					"appCode": that.param.appCode
				}
			}).then(function(data) {
				that.relationCodeMap = data.relationCodeMap;
				that.customOptionSetCodeMap = data.customOptionSetCodeMap;
				for (var key in that.relationCodeMap) {
					that.relationCodeMap[key] = [0];
				}
			});
		},
		saveCustomFormFilled: function(saveState, formData) {
			var that = this;
			if (isSubmit) {
				return;
			}
			isSubmit = true;

			if (saveState == 1) {
				assemblys.confirm("确认提交吗？", function() {
					that.submitFun(saveState, formData)
				}, function() {
					isSubmit = false;
				});
			} else {
				that.submitFun(saveState, formData);
			}
		},
		submitFun: function(saveState, formData) {
			formData.saveState = saveState;
			return ajax({
				url: basePath + "frame/newCustomForm/saveCustomFormFilled.spring",
				type: "post",
				data: formData
			}).then(function(data) {
				data.saveState = saveState;
				if (parent.saveCallback) {
					for (var key in parent.saveCallback) {
						if (typeof parent.saveCallback[key] == "function") {
							parent.saveCallback[key].call(parent.window, data, window);
						}
					}
				}
			});
		},
		field_radio: function(customModular, customField) {
			var values = customModular.values;
			var refs = customModular.refs;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
			var customOptionSetList = customField.fieldData;
			for (var i = 0; i < customOptionSetList.length; i++) {
				if (customOptionSetList[i].childOptionList && customOptionSetList[i].childOptionList.length > 0) {
					if (customOptionSetList[i].childOptionList[0].customFieldSet.replace("Other", "") == "radio") {
						values[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref("");
					} else {
						values[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref([]);
						refs[customOptionSetList[i].customOptionSetCode + "-0"] = Vue.ref([]);
					}
				}
			}
		},
		field_checkbox: function(customModular, customField) {
			var values = customModular.values;
			var refs = customModular.refs;
			var customOptionSetList = customField.fieldData;
			for (var index = 0; index < customModular.maxIndex + 1; index++) {
				values[customField.customFieldCode + "-" + index] = Vue.ref([]);
				refs[customField.customFieldCode + "-" + index] = Vue.ref([]);
				for (var i = 0; i < customOptionSetList.length; i++) {
					if (customOptionSetList[i].childOptionList && customOptionSetList[i].childOptionList.length > 0) {
						if (customOptionSetList[i].childOptionList[0].customFieldSet.replace("Other", "") == "radio") {
							values[customOptionSetList[i].customOptionSetCode + "-" + index] = Vue.ref("");
						} else {
							values[customOptionSetList[i].customOptionSetCode + "-" + index] = Vue.ref([]);
							refs[customOptionSetList[i].customOptionSetCode + "-" + index] = Vue.ref([]);
						}
					}
				}
			}
		},
		field_select: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref([]);
		},
		field_datetime: function(customModular, customField) {
			var values = customModular.values;
			var refs = customModular.refs;
			var dates = customModular.dates;
			values[customField.customFieldCode + "-0"] = "";
			dates[customField.customFieldCode + "-0"] = new Date();
			refs[customField.customFieldCode + "-0"] = Vue.ref(false);
		},
		field_text: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
		},
		field_textarea: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
		},
		field_interface: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = Vue.ref("");
		},
		field_org: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
			values["remark--" + customField.customFieldCode + "-0"] = "";
		},
		field_label: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
		},
		field_img: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
		},
		field_profile: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
		},
		field_file: function(customModular, customField) {
			var values = customModular.values;
			values[customField.customFieldCode + "-0"] = "";
		},
		//hwx 2023-5-9 获取页面dom对象
		getDom: function() {
			return this;
		}
	},
	provide: function() {
		return {
			relationCodeMap: this.relationCodeMap,
			customOptionSetCodeMap: this.customOptionSetCodeMap,
			attaValues: this.attaValues,
			attaFieldValues: this.attaFieldValues,
			formVerify: this.formVerify
		};
	}
};