components["custom-form-detail"] = {
	template : (function() {
		var html = "";
		html += '<custom-detail-item :datalist="datalist"></custom-detail-item>';
		return html;
	})(),
	created : function() {
		var that = this;
		that.getCustomFormData();
	},
	props : {
		params : Object
	},
	data : function() {
		var that = this;
		return {
			"appCode" : that.params.appCode,
			"compNo" : that.params.compNo,
			"customFormCode" : that.params.customFormCode,
			"customFormFilledCode" : that.params.customFormFilledCode,
			"customFormBusinessCode" : that.params.customFormBusinessCode,
			"showLabel" : that.params.showLabel,
			datalist : []
		};
	},
	methods : {
		getCustomFormData : function() {
			var that = this;
			return ajax({
				url : basePath + "/frame/newCustomForm/getCustomFormData.spring",
				data : {
					"customFormCode" : that.customFormCode,
					"customFormBusinessCode" : that.customFormBusinessCode,
					"compNo" : that.compNo,
					"customFormFilledCode" : that.customFormFilledCode,
					"appCode" : that.appCode,
					"isDetail" : true,
					"showLabel" : that.showLabel+""
				},
				skipDataCheck : true
			}).then(function(data) {
				if (data.result == "success") {
					// 详情信息
					var customModularList = data.data.customModularList;
					// 数据
					var dataList = [];
					// 表单附件
					that.getAttachments().then(function(data1) {
						// 附件
						var attaList = data1.attachmentsList || [];
						if (attaList.length > 0) {
							// 每行
							dataList.push({
								title : "附件信息",
								data : [ {
									title : "",
									list : attaList
								} ]
							});
						}
						// 组件附件
						return that.getAttachments("all");
					}).then(function(data2) {
						
						var attaList = data2.attachmentsList || [];
						var attaMap = {};
						if (attaList.length > 0) {
							for (var l = 0; l < attaList.length; l++) {
								var atta = attaList[l];
								if (!attaMap[atta.belongToSubCode]) {
									attaMap[atta.belongToSubCode] = [ atta ];
								} else {
									attaMap[atta.belongToSubCode].push(atta);
								}
							}
						}
						for (var i = 0; i < customModularList.length; i++) {
							var modular = customModularList[i];
							if (JSON.stringify(modular.detailValues) != "{}") {
								var childList = [];
								for (var j = 0; j < customModularList[i].maxIndex + 1; j++) {
									// 新增
									if (modular.hasAdd) {
										// 每行
										childList.push({
											label : "<strong>" + modular.customModularName + '-' + (j + 1) + "</strong>",
										});
									}
									// 选项
									var customFieldList = modular.customFieldList;
									for (var k = 0; k < customFieldList.length; k++) {
										var customField = customFieldList[k];
										var customFieldName = customField.customFieldCode + "-" + j;
										var customFieldName2 = customField.customFieldCode + "_" + j;
										// 标签
										if (customField.customFieldSet == "label" && that.showLabel) {
											customFieldName = customField.customFieldCode;
										}
										// 获取值
										var value = customModularList[i].detailValues[customFieldName] || "";
										// 富文本
										if (customField.isRichText == 1) {
											value = value;
										} else
										// 图片
										if (customField.customFieldSet == "profile") {
											value = '<img style="max-width:150px;" src="' + value + '" />';
										} else if (customField.customFieldSet == "file") {
											// 附件
											value = attaMap[customFieldName2] || [];
										} else {
											value = assemblys.escape(value);
										}
										// 每行
										childList.push({
											title : customField.customFieldName,
											label : value,
											code : true,
											type : customField.customFieldSet,
											businessCode : customField.businessCode,
										});
									}
								}
								var temp = {
									title : modular.customModularName,
									data : childList
								}
								dataList.push(temp);
							}
						}
						that.datalist = dataList;
					});
				} else {
					assemblys.alert("加载表单失败");
				}
			});
		},
		getAttachments : function(belongToSubCode) {
			var that = this;
			// 获取附件
			return ajax({
				url : basePath + "frame/fileUpload/getAttachments.spring",
				data : {
					"belongToCode" : that.customFormFilledCode,
					"belongToSubCode" : belongToSubCode || ""
				},
				dataType : "json",
				skipDataCheck : true
			});
		}
	}
}