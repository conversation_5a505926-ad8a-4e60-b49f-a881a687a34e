/**
 * 全局控制 PS：这里只做页面的交互
 */
var pubObj = {
	/**
	 * 初始化iframe - 自定义表单提供
	 */
	initIframe : function() {
		var iframe = document.getElementById("report");
		iframe.height = parent.innerHeight - 112;
		var url = basePath + "frame/mobile/customForm/customFormTemplate.html?1=1";
		var params = "&customFormCode=" + param.customFormCode;
		params += "&type=" + param.type;
		params += "&compNo=" + param.compNo;
		params += "&appCode=" + param.appCode;
		params += "&hasBack=" + param.hasBack;
		params += "&funCode=" + param.funCode;
		params += "&customFormFilledCode=" + param.customFormFilledCode;
		url += "&hasTopButton=1";
		document.getElementById("report").src = url + params;
	},
	getParam : function(str, defaultStr) {
		var reg = new RegExp("(^|&)" + str + "=([^&]*)(&|$)");
		var r = window.location.search.substr(1).match(reg);
		if (r != null) {
			return decodeURIComponent(r[2]) || "";
		}
		return defaultStr || "";
	}
}

var param = {
	type : pubObj.getParam("type"),
	compNo : pubObj.getParam("compNo"),
	appCode : pubObj.getParam("appCode"),
	funCode : pubObj.getParam("funCode"),
	customFormCode : pubObj.getParam("customFormCode"),
	customFormFilledCode : pubObj.getParam("customFormFilledCode"),
	hasBack : pubObj.getParam("hasBack")
}

/**
 * 由自定义表单提供的回调方式 - 父页面的js请勿写在里面
 */
var saveCallback = {
	// 保存方法
	saveComplaintDispute : function(data, window) {
		var id = new Date().getTime();
		window.assemblys.msg("保存成功", function() {
			if (top.page.index.vm.showBack) {
				history.back();
			}
		});
	}
}

pubObj.initIframe();
