page.customList.option = {
	created : function() {
		var that = this;
		that.data.param = that.param;
		that.initTopButton();
		assemblys.getMenuIcon(that.param.funCode, true).then(function(data) {
			that.param.compNo = data.compNo;
			if (data.compList && data.compList.length > 0) {
				var options = [];
				for (var i = 0; i < data.compList.length; i++) {
					options.push({
						name : data.compList[i].compName,
						value : data.compList[i].compNo
					});
				}
				that.fieldList.push({
					label : "医院",
					fieldSet : "select",
					key : "compNo",
					notAll : true,
					options : options
				});
			}
			return data;
		}).then(function() {
			return that.getCustomTypeCount();
		}).then(function() {
			return that.getCustomFormTypeMenu();
		}).then(function() {
			return that.getCustomFormTypeStateList();
		}).then(function() {
			return that.getParamInfo();
		}).then(function() {
			that.$refs.list.onRefresh();
		});
	},
	components : {
		"custom-filter-search" : null,
		"custom-list" : null
	},
	data : function() {
		var that = this;
		var tabs = [ {
			title : "全部",
			show : true,
			selected : false,
			color : "",
			value : ""
		} ];
		return {
			tabs : Vue.ref(tabs),
			tabSelected : tabs[0],
			customFieldSetting : null,
			customFormTypeStateList : null,
			search : Vue.ref(false),
			fieldList : Vue.ref([]),
			selectType : Vue.ref(""),
			buttonAry : Vue.ref([]),
			statusObjArr : Vue.ref({}),
			statusColorArr : Vue.ref({}),
			param : Vue.ref({
				complaintMethod : "",
				funCode : "",
				appCode : "",
				customFormCode : "",
				customFormTypeCode : "",
				customFormTypeMenuCode : "",
				customFormTypeBusinessCode : "",
				customFormTypeMenuNo : "",
				customFormTypeMenuType : "",
				state : "",
				compNo : "",
				curPageNum : 0
			}),
			searchParam : {},
			// 列表数据
			data : {
				url : basePath + "frame/customList/getCustomListPager.spring",
				method : "post",
				parseData : function(data) {
					this.more.cols = [];
					for ( var i in that.customFieldSetting) {
						if(that.customFieldSetting[i].customFieldCode == "number" || that.customFieldSetting[i].customFieldCode == "operate"){
							continue;
						}
						this.more.cols.push({
							name : that.customFieldSetting[i].customFieldName,
							key : that.customFieldSetting[i].customFieldCode
						});
					}
					
					return data;
				},
				cols : [ {
					left : {
						key : "customFormName"
					},
					right : {
						key : function(d) {
							return "<span style='color : " + that.statusColorArr[d.status] + "'>" + that.statusObjArr[d.status] + "</span>"
						}
					}
				}, {
					left : {
						key : "createUserName"
					},
					right : {
						key : "createDate"
					}
				}, {
					left : {
						key : "customFormFilledCode"
					}
				} ],
				more : { // 更多
					key : function(d) {
						return "展开详情";
					}, // 左侧显示字段
					encode : true,
					cols : [],
				},
			}
		};
	},
	methods : {
		listOnClick : function(item, index) {
			var that = this;
			var type = "";
			if (item.status == "0" && item.approvalIndex != "0") {
				type = "1";
			} else {
				type = "2";
			}
			if (item.status == "0" || item.approvalIndex == "0") {
				var url = basePath + "frame/mobile/customForm/registerReport.html?type=" + type;
				url += "&customFormCode=" + item.customFormCode;
				url += "&customFormFilledCode=" + item.customFormFilledCode;
				url += "&compNo=" + that.param.compNo;
				url += "&appCode=" + that.param.appCode;
				location.url({
					url : url
				});
			} else {
				var url = basePath + "/frame/mobile/customDetail/customFormDetail.html?";
				url += "&titleName=" + "表单详情";
				url += "&appCode=" + that.$root.param.appCode;
				url += "&funCode=" + that.$root.param.funCode;
				url += "&customFormTypeCode=" + that.$root.param.customFormTypeCode;
				url += "&customFormTypeMenuCode=" + that.$root.param.customFormTypeMenuCode;
				url += "&compNo=" + item.compNo;
				url += "&customFormFilledCode=" + item.customFormFilledCode;
				url += "&approvalBelongCode=" + item.customFormFilledCode;
				url += "&customFormCode=" + item.customFormCode;
				url += "&customFormBusinessCode=" + item.businessCode;
				location.url({
					url : url
				});
			}
		},
		getTabClass : function(tab) {
			return this.tabSelected == tab ? "van-swipe-item custom-form-select-li" : "van-swipe-item";
		},
		tabOnchange : function(tab, index) {
			var that = this;
			that.tabSelected = tab;
			that.param.status = tab.value;
			top.page.index.vm.$refs.rightIcon.showIcon[0] = that.buttonAry.length > 0;
			// 重新加载列表
			this.$refs.list.onRefresh();
		},
		// 查询
		onSelect : function(values) {
			this.searchParam = values;
			if (values) {
				for ( var key in values) {
					this.param[key] = values[key];
				}
			}
			this.search = false;
			this.$refs.list.onRefresh();
		},
		reset : function() {
			for ( var key in this.searchParam) {
				this.param[key] = null;
			}
		},
		initTopButton : function() {
			var that = this;
			var html = '';
			html += '<van-icon name="search" size="20" @click="showSearch" :style="{height: \'24px\'}"></van-icon>';
			html += '<van-popover v-model:show="showPopover" theme="dark" :actions="actions" placement="bottom-end" @select="onSelect" :offset="offset">';
			html += '	<template #reference>';
			html += '		<van-icon v-show="showIcon[0]" name="ellipsis" size="20" :style="{marginLeft: \'5px\'}"></van-icon>';
			html += '	</template>';
			html += '</van-popover>';
			
			// 重写右上角导航栏
			top.page.index.vm.initTopRightTitle({
				template : html,
				props : [ "showIcon" ],
				data : function() {
					const showPopover = top.Vue.ref(false);
					return {
						showPopover : showPopover,
						offset : [ 0, 20 ]
					};
				},
				methods : {
					showSearch : function() {
						that.$root.search = !that.$root.search;
					},
					onSelect : function(data) {
						that.$refs.list.onRefresh();
					}
				}
			});
		},
		getParamInfo : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/customList/getCustomFieldSetting.spring",
				data : that.param
			}).then(function(data) {
				that.customFieldSetting = data.customFieldSetting;
				that.fieldList.push({
					label : "编号",
					fieldSet : "text",
					key : "customFormFilledCode",
					placeholder : "编号"
				}, {
					label : "表单名称",
					fieldSet : "text",
					key : "customFormName",
					placeholder : "表单名称"
				}, {
					label : "创建人",
					fieldSet : "text",
					key : "createUserName",
					placeholder : "创建人"
				}, {
					label : "创建时间",
					fieldSet : "datetime",
					key : "createDate"
				});
				
				var fieldList = data.customFieldSetting;
				for ( var i in fieldList) {
					var field = fieldList[i];
					if (!field.customFieldSet || field.customFieldSet == "label") {
						continue;
					}
					//序号 和 操作 不作为过滤条件。
					if(field.customFieldCode == "number" || field.customFieldCode == "operate"){
						continue;
					}
					field.label = field.customFieldName;
					field.fieldSet = field.customFieldSet;
					// 头像 、标签 不作为过滤条件。
					if (field.fieldSet == "profile" || field.fieldSet == "label") {
						continue;
					}
					if (field.fieldSet == "radio" || field.fieldSet == "checkbox" || field.fieldSet == "select") {
						field.fieldSet = "select";
						field.key = "filterVlaues";
					}
					// 接口
					if (field.fieldSet == "interface" || field.fieldSet == "text" || field.fieldSet == "textarea") {
						field.fieldSet = "text";
						field.key = "filterVlaues";
					}
					if (field.fieldSet == "datetime") {
						field.key = "filterVlaues";
					}
					// 组织架构
					if (field.fieldSet == "org") {
						field.fieldSet = "select";
						field.key = "filterVlaues";
					}
					field.code = field.customFieldCode;
					if (field.options) {
						var isDept = "dept" == field.relationField;
						var isUser = "user" == field.relationField;
						for (var j = 0; j < field.options.length; j++) {
							var option = field.options[j];
							if (isDept) {
								field.options[j].name = option.DeptName;
								field.options[j].value = option.DeptID;
							} else if (isUser) {
								field.options[j].name = option.userName;
								field.options[j].value = option.userCode;
							} else {
								field.options[j].name = option.customOptionSetContent;
								field.options[j].value = option.customOptionSetCode;
							}
						}
						//field.options = field.options;
					}
					that.fieldList.push(field);
				}
				
				that.initTabs();
			});
			
		},
		initTabs : function() {
			var that = this;
			var state = that.param.state;
			var tabList = that.customFormTypeStateList;
			var that = this;
			//我的填报列表
			var allValues = "";
			if (that.$root.param.customFormTypeMenuType == "2") {
				that.tabs.push({
					title : "草稿",
					show : true,
					selected : false,
					color : "",
					value : "0"
				})
				allValues += ",0";
			}
			for (var i = 0; i < tabList.length; i++) {
				var tab = tabList[i];
				that.tabs.push({
					title : tab.customFormTypeStateName,
					show : true,
					selected : false,
					color : tab.customFormTypeStateColor,
					value : tab.customFormTypeStateNo
				});
				allValues += "," + tab.customFormTypeStateNo;
			}
			that.tabs[0].value = allValues ? allValues.substr(1) : "";
			that.param.status = that.tabs[0].value;
			
			top.page.index.vm.$refs.rightIcon.showIcon[0] = that.buttonAry.length > 0;
		},
		getCustomFormTypeStateList : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/customFormType/getCustomFormTypeStateList.spring?",
				data : that.param
			}).then(function(data) {
				that.customFormTypeStateList = data.customFormTypeStateList;
				if (data.customFormTypeStateList) {
					for (var i = 0; i < data.customFormTypeStateList.length; i++) {
						var name = data.customFormTypeStateList[i].customFormTypeStateName;
						var no = data.customFormTypeStateList[i].customFormTypeStateNo;
						var color = data.customFormTypeStateList[i].customFormTypeStateColor;
						that.statusObjArr[no] = name;
						that.statusColorArr[no] = color;
					}
					that.statusObjArr["0"] = "草稿";
				}
				
				return data;
			});
		},
		getCustomFormTypeMenu : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/customFormType/getCustomFormTypeMenu.spring?",
				data : {
					appCode : that.$root.param.appCode,
					compNo : that.$root.param.compNo,
					customFormTypeCode : that.$root.param.customFormTypeCode,
					customFormTypeMenuCode : that.$root.param.customFormTypeMenuCode,
					customFormTypeBusinessCode : that.$root.param.customFormTypeBusinessCode,
					customFormTypeMenuNo : that.$root.param.customFormTypeMenuNo || that.$root.param.funCode || "",
				}
			}).then(function(data) {
				var customFormType = data.customFormType;
				var customFormTypeMenu = data.customFormTypeMenu;
				var customFormTypeMenuType = customFormTypeMenu.customFormTypeMenuType || "0";
				that.$root.param.customFormTypeCode = customFormType.customFormTypeCode;
				that.$root.param.customFormTypeMenuCode = customFormTypeMenu.customFormTypeMenuCode;
				that.$root.param.customFormTypeMenuType = customFormTypeMenuType;
				return data;
			});
		},
		getCustomTypeCount : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/customList/getCustomTypeCount.spring",
				data : that.param
			}).then(function(data) {
				if (data.customTypeCount && data.customTypeCount.length == 1) {
					that.param.customFormCode = data.customTypeCount[0].customFormCode;
				}
				return data;
			});
		},
	},
}