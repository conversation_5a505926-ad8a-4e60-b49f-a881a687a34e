page.userWorkbench.option = {
	created : function() {
		var that = this;
		that.getUserWorkBench();
	},
	data : function() {
		var that = this;
		return {
			param : Vue.ref({
				appCode : ""
			}),
			activeNames : Vue.ref([]),
			cardList : [],
			tableList : []
		};
	},
	methods : {
		getUserWorkBench : function() {
			var that = this;
			ajax({
				url : basePath + "frame/customWorkbench/getUserWorkBench.spring",
				data : {
					"appCode" : that.param.appCode,
					"isMobileShow" : "1"
				},
				dataType : "json",
			}).then(function(data) {
				// 卡片
				var list = data.customWorkBenchList;
				if (list.length > 0) {
					// 父类方法
					var checkUrl = parent.page.index.vm.checkUrl;
					// 处理卡片
					for (var i = 0; i < list.length; i++) {
						var temp = list[i];
						var tempData = {
							"title" : temp.customWorkbenchModuleName,
							"dataUrl" : that.handleUrl((checkUrl(temp.dataUrl, "0") || ""), temp.funCode),
							"url" : that.handleUrl((checkUrl(temp.url) || ""), temp.funCode),
							"icon" : temp.moduleIcon,
							"iconType" : temp.moduleIconType,
							"fontColor" : temp.moduleNameColor,
							"iconColor" : temp.moduleIconColor,
							"iconBgColor" : temp.iconBackGroundColor,
							"bgColor" : temp.moduleBackGroundColor,
							"num" : "",
							"click" : temp.isMobileClick
						}
						if (temp.moduleType == 0) {
							that.cardList.push(tempData);
						} else {
							that.tableList.push(tempData);
						}
					}
					that.loadMonitor();
				}
			});
		},
		handleUrl : function(url, funCode) {
			if (url == "") {
				return url;
			}
			if (url.indexOf("&funCode=") == -1 && url.indexOf("?funCode=") == -1) {
				if (url.indexOf("?") != -1) {
					url += "&funCode=" + funCode;
				} else {
					url += "?funCode=" + funCode;
				}
			}
			if (url.indexOf("$appCode=") == -1 && url.indexOf("?appCode=") == -1 && url.indexOf("@appCode=") == -1) {
				if (url.indexOf("&appCode=") == -1) {
					url += "&appCode=" + this.param.appCode;
				}
			}
			return url;
		},
		handleClass : function(temp) {
			return "layui-icon" + temp.iconType;
		},
		hasUrl : function(temp) {
			return temp.url && temp.click == "1" ? true : false;
		},
		loadMonitor : function() {
			var that = this;
			var cardList = that.cardList;
			for (var i = 0; i < cardList.length; i++) {
				let card = cardList[i];
				if (card.dataUrl) {
					ajax({
						url : card.dataUrl,
						skipDataCheck : true,
						dataType : "json",
					}).then(function(data) {
						let count = data.count || data.data.count || data.totalCount || data.data.totalCount || 0;
						card.num = count > 100000 ? "99999+" : count;
					});
				}
			}
		},
		openCard : function(temp) {
			if (!temp.url || temp.click == "0") {
				return;
			}
			var time = new Date().getTime();
			parent.page.index.vm.addMenu({
				url : temp.url,
				show : true,
				deleteState : true,
				menuName : temp.title,
				id : time
			})
		},
	}
}