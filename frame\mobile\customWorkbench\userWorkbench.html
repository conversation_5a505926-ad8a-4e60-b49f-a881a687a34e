<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>工作台</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/vant/css/list.css">
<link rel="stylesheet" type="text/css" href="css/userWorkbench.css">
</head>
<body>
	<div app="userWorkbench">
		<van-form :ref="el => form = el"> 
			<img src="images/workbench.jpeg" class="van-image__img">
			<van-cell  v-for="(temp,index) in cardList"  title-class="left-title" :value="temp.num" :style="{background:temp.bgColor}" @click="openCard(temp)" :is-link="hasUrl(temp)" >
				<template #title>
					<div class="icon-bg" :style="{background:temp.iconBgColor}" >
						<i :class="handleClass(temp)" :style="{color:temp.iconColor}" v-html="temp.icon" ></i> 
					</div> 
					<span class="icon-title" :style="{color:temp.fontColor}" >{{ temp.title }}</span>
				</template>
			</van-cell>
			<div style="margin-top: 5px;">
				<van-collapse  v-for="(temp,index) in tableList"  v-model="activeNames" >
				  <van-collapse-item :name="index" lazy-render >
				   	<template #title>
						<i :class="handleClass(temp)" :style="{color:temp.iconColor}" v-html="temp.icon" ></i> 
						<span class="icon-title" :style="{color:temp.fontColor}" >{{ temp.title }}</span>
					</template>
					<iframe :src="temp.url" frameborder="0" width="100%" height="100%" ></iframe>
				  </van-collapse-item>
				</van-collapse>
			</div>
		</van-form>
	</div>
</body>
</html>
<script type="text/javascript" src="../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/userWorkbench.js"></script>
