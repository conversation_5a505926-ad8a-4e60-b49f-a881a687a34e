body {
	background-color: #f7f8fa;
}

div.van-tabs__content {
	border-top: 1px solid #ccc;
}

div.van-tabbar {
	border-top: 1px solid #ccc;
}

#titleMenuDiv div.van-cell__value--alone {
	text-align: center;
	color: var(- -van-nav-bar-title-text-color);
	font-weight: var(- -van-font-weight-bold);
	font-size: var(- -van-nav-bar-title-font-size);
}

div.van-nav-bar {
	border-bottom: 1px solid #ccc;
}

#titleMenuDiv {
	max-height: 250px;
}

#leftMenuDiv {
	position: absolute;
	width: 100%;
	top: 50px;
	bottom: 0px;
}

div.van-dropdown-menu__bar {
	height: 14px;
}

div.van-collapse-item__title {
	background-color: #d5eaff;
}

div.van-dropdown-item__content {
	padding-top: 10px;
}

div.body-iframe {
	position: absolute;
	top: 46px;
	bottom: 46px;
	left: 0px;
	right: 0px;
}

div.van-sidebar{
	overflow-y: unset;
}

.topMenu{
	z-index: 9999!important;
}

.hide {
	display: none;
}

.show {
	display: unset;
}
