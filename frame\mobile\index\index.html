<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>科进 | 医疗管控平台</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/index.css">
</head>
<body>
	<form>
		<div app="index">
			<van-config-provider :theme-vars="themeVars">
				<van-nav-bar class="topMenu" :left-text="showBackText" :left-arrow="showBack" @click-left="goback">
					<template #title>
						<van-cell :title="topMenuName" @click="showTopMenu = true">
							<template #right-icon>
								<van-icon name="exchange" size="16px" :style="{top: '5px', left: '3px'}"></van-icon>
							</template>
						</van-cell>
					</template>
					<template #right>
						<right-icon ref="rightIcon"></right-icon>
					</template>
				</van-nav-bar>

				<div class="body-iframe">
					<menu-frame ref="menuFrame"></menu-frame>
				</div>
				<van-tabbar v-model="active">
					<van-tabbar-item icon="apps-o" @click="showPopup">菜单</van-tabbar-item>
					<van-tabbar-item icon="home-o" @click="menuOnClick({funCode : '0'})">工作台</van-tabbar-item>
					<van-tabbar-item icon="envelop-o" :badge="msgNum" @click="openMsg()">短消息</van-tabbar-item>
					<van-tabbar-item icon="manager-o" @click="menuOnClick({url:'userInfo.html',show:true,deleteState:false,menuName:'我的',id:new Date().getTime(),funCode : 'USER_INFO'})">我的</van-tabbar-item>
				</van-tabbar>
				<van-popup v-model:show="showMenu" position="left" :style="{ height: '100%',width: '200px'}">
					<div id="leftMenuDiv">
						<menu-left @select="menuOnClick"></menu-left>
					</div>
				</van-popup>
				<van-popup v-model:show="showTopMenu" position="top" :style="{ height: 'auto',maxHeight: '300px', width: '100%', top: '46px'}">
					<van-cell v-for="(menu,key) in menuList" v-show="!menu.deleteState" :title="menu.menuName" @click="titleName = menu.funCode,showTopMenu = false">
						<template #right-icon>
							<van-icon v-if="menu.funCode != '0'" name="close" color="#e0e0e0" size="24px" @click.stop="deleteMenu(menu.funCode)"></van-icon>
						</template>
					</van-cell>
				</van-popup>
			</van-config-provider>
			<van-popup v-model:show="newsShow" round  :style="{ height: '80%' , width: '80%', overflowY: 'hidden'}" :close-on-click-overlay="closeOnClick" >
				<div style="height: 100%;width: 100%;overflow-y: auto;">
					<div  v-for="(temp,index) in dataList" style="margin:15px 5px 50px 5px;">
							<div class="skin-div-css" style="">
								<span style="margin-left:15px;font-size: 20px;">{{ temp.newsTitle }}</span>
								<span style="margin-left:5px;font-size: 10px;color: #BEBEBE;">{{ timestampToTime(temp.createDate) }}</span> 
							</div>
							<div style="margin-left:15px;" v-html="temp.newsContent"></div>
					</div>
				</div>
				<van-button :style="{ position: 'fixed' , bottom: '0px'}" :disabled="buttonDisabled" type="primary" block  @click="closPop"  >
					{{ loading ? (count + '秒后可关闭') : buttonText }}
				</van-button>	
			</van-popup>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/common/js/base64.js"></script>
<script type="text/javascript" src="js/index.js?version=*******"></script>
<script type="text/javascript" src="js/menuFrame.js?version=*******"></script>
<script type="text/javascript" src="js/menuLeft.js?version=*******"></script>
<script type="text/javascript" src="js/topRightIcon.js?version=*******"></script>
