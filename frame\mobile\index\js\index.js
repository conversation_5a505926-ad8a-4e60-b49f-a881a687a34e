window.appCode = assemblys.getParam("appCode");
page.index.option = {
	created : function() {
		var that = this;
		that.getAppByCode().then(function(data) {
			return that.initMenu(data.application);
		}).then(function(menuList){
			that.getSsoConfig(menuList);
			that.readDraft();// 短消息状态
			setInterval(that.readDraft, 1000 * 60 * 2);
			
			// 获取公告内容
			that.getSystemNews();
			that.timeDown();
			
		});
		sessionStorage.clear();
		
	},
	computed : {
		rightIconComponent : function() {
			return "rightIcon-" + this.titleName;
		},
		topMenuName : function() {
			var menu = this.getMenu(this.titleName);
			return menu ? menu.menuName : "";
		},
		showBackText : function() {
			return this.showBack ? "返回" : "";
		}
	},
	data : function() {
		return {
			// 公告参数
			newsCodes : [], //公告编号
			count : 5, //公告倒计时
			loading : Vue.ref(true),
			buttonDisabled : true,
			buttonText :Vue.ref(""),
			closeOnClick : false,
			dataList : Vue.ref([]),
			time: 10000,
			newsShow : Vue.ref(false),
			// 框架参数
			showBack : Vue.ref(false),
			application : null,
			user : null,
			mdmsConstant : null,//hwx 2022-04-29 引入医务管理的静态常量
			menuType : Vue.ref([]),
			menuTypeName : Vue.ref([]),
			menuList : Vue.ref([]),
			active : Vue.ref(1),
			showMenu : Vue.ref(false),
			showTopMenu : Vue.ref(false),
			swipeable : true,
			showFrame : Vue.ref(false),
			titleName : Vue.ref("0"),
			menuFrame : Vue.ref(null),
			msgNum : null,
			themeVars : {
				sidebarSelectedBorderColor : "var(--van-primary-color)",
				sidebarWidth : "100%"
			},
			rightIconComponents : {},
		}
	},
	methods : {
		goback : function() {
			history.back();
		},
		showPopupFrame : function() {
			this.showFrame = true;
		},
		showPopup : function() {
			this.showMenu = true;
		},
		hide : function() {
			for (var i = 0; i < this.menuList.length; i++) {
				this.menuList[i].show = false;
			}
		},
		getMenu : function(funCode, index) {
			if (!index && index != 0) {
				index = this.getMenuIndex(funCode);
			}
			
			if (!index && index != 0) {
				return null;
			} else {
				return this.menuList[index];
			}
		},
		getMenuIndex : function(funCode) {
			for (var i = 0; i < this.menuList.length; i++) {
				if (this.menuList[i].funCode == funCode) {
					return i;
				}
			}
			return -1;
		},
		addMenu : function(menu) {
			var url;
			if (menu.funImplement) {
				url = this.checkUrl(menu.funImplement);
				if (menu.url) {
					url = menu.url;
				}
			} else {
				url = menu.url;
			}
			if (url) {
				if (url.indexOf("?") == -1) {
					url += "?";
				} else {
					url += "&";
				}
				// 如果不存在
				if(url.indexOf("&funCode=") == -1 && url.indexOf("?funCode=") == -1){
					url += "funCode=" + menu.funCode;
				}
			}
			if (menu.menuURLParam) {
				url += "&" + menu.menuURLParam + "&_=" + new Date().getTime();
			} 
			this.hide();
			var index = this.getMenuIndex(menu.funCode);
			if (index != -1) {
				var m = this.getMenu(menu.funCode, index);
				m.deleteState = false;
				m.show = true;
				if (!url && m.url) {
					url = m.url;
					if (url.indexOf("?") == -1) {
						url += "?";
					} else {
						url += "&";
					}
					url += "funCode=" + menu.funCode + "&_=" + new Date().getTime();
				}
				
				if (menu.menuName) {
					m.menuName = menu.menuName;
				}
			} else {
				this.menuList.push({
					url : url,
					menuName : menu.menuName,
					show : true,
					deleteState : false,
					id : new Date().getTime(),
					funCode : menu.funCode
				});
			}
			
			// 切换
			this.titleName = menu.funCode;
			if (index != -1) {
				this.clearSessionCache(menu.funCode);
				if(url){
					this.getThisTabIframe().location.href = url;
				}
			}
		},
		deleteMenu : function(funCode) {
			if (!funCode) {
				funCode = top.page.index.vm.getThisTabMenuID();
			}
			var index = this.getMenuIndex(funCode);
			var menu = this.getMenu(null, index);
			var isShow = menu.show;// 是否当前显示的页面
			menu.deleteState = true;
			menu.show = false;
			if (index - 1 < 0) {
				index = 0;
			} else {
				index = index - 1;
			}
			if(isShow){
				this.titleName = this.getShowMenu(index).funCode;
			}
		},
		getShowMenu(index){
			var menu = this.getMenu(null, index);
			if(!menu.deleteState){
				return menu;
			}else{
				if (index - 1 < 0) {
					index = 0;
				} else {
					index = index - 1;
				}
				return this.getShowMenu(index);
			}
		},
		getAppByCode : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/appaction/getAppByCode.spring",
				data : {
					appCode : assemblys.getParam("appCode")
				}
			}).then(function(data) {
				that.user = data.user;
				that.application = data.application;
				var indexPage = that.application.indexPage || "frame/customWorkbench/userWorkbench.html?appCode=" + that.application.appCode;
				that.menuList[0] = {
					url : that.checkUrl(indexPage),
					show : true,
					deleteState : false,
					menuName : "工作台",
					id : new Date().getTime(),
					funCode : "0"
				};
				that.addWatermark();
				return data;
			});
		},
		//hwx 2022-04-29 引入医务管理的静态常量
		initConstant : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/common/initConstant.spring",
				method : "get",
				skipDataCheck : true
			}).then(function(data) {
				that.mdmsConstant = data.data.mdms.mdmsConstant;
			});
		},
		initMenu : function(application) {
			var that = this;
			return ajax({
				url : basePath + "frame/leftMenu/initMenu.spring",
				method : "post",
				data : {
					appCode : application.appCode,
					"appId[]" : application.appID
				},
				skipDataCheck : true
			}).then(function(data) {
				for (var i = 0; i < data.MENUS.length; i++) {
					var menu = data.MENUS[i];
					if (menu.menuLevel == 1) {
						that.menuType.push(menu);
						that.menuTypeName.push(menu.menuId);
					} else {
						if (menu.isMobileShow != 1) {// 不是移动端显示跳过
							continue;
						}
						var lastMenuType = that.menuType[that.menuType.length - 1];
						if (!lastMenuType.twoLevelMenu) {
							lastMenuType.twoLevelMenu = [];
						}
						lastMenuType.twoLevelMenu.push(menu);
					}
				}
				
				var menuType = [];
				for (var i = 0; i < that.menuType.length; i++) {
					if (that.menuType[i].twoLevelMenu && that.menuType[i].twoLevelMenu.length > 0) {
						menuType.push(that.menuType[i]);
					}
				}
				
				that.menuType.length = 0;
				
				for (var i = 0; i < menuType.length; i++) {
					that.menuType.push(menuType[i]);
				}
				return data.MENUS;
			});
		},
		// 更新短消息状态
		readDraft : function() {
			var that = this;
			var url = basePath + "/frame/msg/hasUnRead.spring?appCode=" + top.appCode;
			ajax({
				type : "get",
				url : url,
			}).then(function(data){
				var hasUnReadNum = data.hasUnReadNum;
				if(hasUnReadNum == 0){
					that.msgNum = null;
				}else{
					that.msgNum = hasUnReadNum > 99 ? "99+" : hasUnReadNum;
				}
			});
		},
		menuOnClick : function(menu) {
			this.addMenu(menu);
			this.showMenu = false;
		},
		initTopRightTitle : function(component) {
			var that = this;
			if (component) {
				var titleName = that.titleName;
				that.rightIconComponents[titleName] = component;
//						index.vm.titleName = "";
//						index.vm.titleName = titleName;
				that.$refs.rightIcon.name = component;
			}
		},
		getThisTabIframe : function() {
			return this.getMenu(this.titleName).iframe.contentWindow;
		},
		getThisTabMenuID : function() {
			return this.getMenu(this.titleName).funCode;
		},
		getThisTabID : function() {
			return this.getMenu(this.titleName).id;
		},
		getIframeID : function(funCode) {
			return this.getMenu(funCode).id;
		},
		getIframeFromURL : function(iframeID) {
			return iframeID + "_" + "fromURL";
		},
		getIframeFromParam : function(iframeID) {
			return iframeID + "_" + "fromParam";
		},
		getIframeParam : function(iframeID) {
			return iframeID + "_" + "param";
		},
		clearSessionCache : function(funCode) {
			var iframeID = this.getIframeID(funCode);
			var iframeFromParam = this.getIframeFromParam(iframeID);
			var iframeFromURL = this.getIframeFromURL(iframeID);
			var iframeParam = this.getIframeParam(iframeID);
			sessionStorage.setItem(iframeFromParam, "");
			sessionStorage.setItem(iframeFromURL, "");
			sessionStorage.setItem(iframeParam, "");
		},
		checkUrl : function(indexPage, hasReplace){
			if(!indexPage){
				return "";
			}
			// 补充URL规则
			if(indexPage && indexPage.substring(0,1) !=  "/"){
				indexPage = "/" + indexPage;	
			}
			// 是否替换
			if(!hasReplace){
				hasReplace = "1";
			}
			// 替换移动端地址
			if(indexPage && hasReplace == "1"){
				indexPage = basePath + indexPage.replace(/^\/(((?!\/).)*)\//, "\/$1\/mobile\/");
			}else{
				indexPage = basePath + indexPage;
			}
			return indexPage || "";
		},
		openMsg : function(){
			this.menuOnClick({
				url: basePath + '/frame/mobile/msgs/msgList.html?appCode=' +assemblys.getParam("appCode") ,
				show:true,
				deleteState:false,
				menuName:'短消息',
				id:new Date().getTime(),
				funCode : 'WORKBENCH_MSG'
			});
		},
		getSsoConfig : function(menuList){
			var that = this;
			var singleCode = that.param.singleCode;
			if(singleCode){
				that.active = Vue.ref(0);
				ajax({
					type : "get",
					url : basePath + "frame/sso/getSsoConfig.spring",
					data : {
						"ssoCode" : singleCode
					},
					dataType : "json",
				}).then(function(data){
					// 单点对象
					var ssoSetting = data.ssoSetting;
					if (ssoSetting) {
						var singleSsoType = ssoSetting.ssoType;
						var singleSsoName = ssoSetting.ssoName;
						var singleOpenMethod = ssoSetting.openMethod;
						var singleMenuID = ssoSetting.menuID || 0;
						var singleSsoUrl = ssoSetting.ssoUrl;
						var funCode = "";
						// 存在
						if (singleMenuID || singleSsoUrl) {
							var url = "";
							// 如果是菜单
							if (singleSsoType && singleSsoType == 1) {
								for (var i = 0; i < menuList.length; i++) {
									var menu = menuList[i];
									if(menu.menuId == singleMenuID){
										funCode = menu.funCode;
										url = that.checkUrl(menu.funImplement);
										break;
									}
								}
							} else {
								funCode = new Date().getTime();
								// 菜单
								if(singleSsoUrl){
									url = basePath + singleSsoUrl;
								}
							}
							// 如果有URL
							if (url) {
								// 默认带单点登录Code
								if (url.indexOf("?") != -1) {
									url += "&singleCode=" + singleCode;
								} else {
									url += "?1=1&singleCode=" + singleCode;
								}
								// 如果存在扩展参数
								var singleParam = that.param.singleParam;
								if (singleParam) {
									var base64 = new Base64()
									url += "&" + base64.decode(singleParam);
								}
								// 弹出模式
								if (singleOpenMethod && singleOpenMethod == "1") {
									that.addMenu({
										url : url,
										show : true,
										deleteState : false,
										menuName : singleSsoName,
										id : new Date().getTime(),
										funCode : funCode
									})
								}
							}
						}
					}
				})
			}
		},
		getSystemNews : function() {
			var that = this;
			ajax({
				url : basePath + "frame/pubnews/getSystemNewsNoRead.spring",
				data : {
					"appCode" : assemblys.getParam("appCode"),
					"isRead" : "0"
				},
				dataType : "json",
			}).then(function(data) {
				if(data.systemNews.length > 0){
					that.newsShow = true;
					var dataList = data.systemNews[0].newsList;
					that.dataList = dataList;
					for (var i = 0; i < dataList.length; i++) {
						that.newsCodes.push(dataList[i].newsCode);
					}	
				}
				
			});
		},timeDown : function(){
			var that = this;
			// 开始倒计时，10s之后才能点击
			that.countDown(); 
		},
		/**
		 * 关闭弹窗
		 */
		closPop : function(obj) {
			var that = this;
			//生成当前用户已读数据
			var  url = basePath + "frame/pubnews/saveUserSystemNews.spring";
			ajax({
				url : url,
				type : "post",
				data : {
					"newsCode" : that.newsCodes.join(","),
				},
				dataType : "json",
			}).then(function(data) {
				that.newsShow = false;
			});
		},
		/**
		 * 倒计时
		 */
		countDown () {
			var that = this;
			this.timer = setInterval(() => {
				this.count--
				if (this.count == 0) {
					// 当计时小于零时，取消该计时器
					clearInterval(this.timer);
					that.buttonText =  "我知道了";
					that.buttonDisabled = false;
					that.loading = false;
				}
			}, 1000)
		},
		 /**
	     * 计算时间返回 yyyy-MM-dd h:m:s 格式的方法
	     * timestamp 时间戳
	     */
	    timestampToTime (timestamp) {
	       // 计算年月日时分的函数
	       var date = new Date(timestamp);
	       var Y = date.getFullYear() + '-';
	       var M = (date.getMonth() + 1) + '-';
	       var D = date.getDate() + ' ';
	       var h = date.getHours() + ':';
	       var m = date.getMinutes(); //+ ':'
	       var s = date.getSeconds();
	       return Y + M + D + h + m ;//+ s
	   },
	   addWatermark() {
			var that = this;
			return ajax({
				url : basePath + "frame/dict/getDictByCode.spring",
				data : {
					appCode : "APP",
					dictCode : "systemWatermark"
				},
				skipDataCheck : true
			}).then(function(data) {
				if(!data.dict){
					return;
				}
				//水印
				var water_mark = that.user.userCode + "/" + that.user.userName;
				// 默认设置
				var defaultSettings = {
					watermark_txt : water_mark,
					watermark_x : 20,// 水印起始位置x轴坐标
					watermark_y : 20,// 水印起始位置Y轴坐标
					watermark_rows : 100,// 水印行数
					watermark_cols : 20,// 水印列数
					watermark_x_space : 10,// 水印x轴间隔
					watermark_y_space : 10,// 水印y轴间隔
					watermark_color : '#e4e0e0',// 水印字体颜色
					watermark_alpha : 0.4,// 水印透明度
					watermark_fontsize : '15px',// 水印字体大小
					watermark_font : '微软雅黑',// 水印字体
					watermark_width : 120,// 水印宽度
					watermark_height : 120,// 水印长度
					watermark_angle : 15,
					watermark_type : ""//用于判断是否上报界面
				// 水印倾斜度数
				};
				// 采用配置项替换默认值，作用类似jquery.extend
				if (arguments.length === 1 && typeof arguments[0] === "object") {
					var src = arguments[0] || {};
					for (key in src) {
						if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key])
							continue;
						else if (src[key])
							defaultSettings[key] = src[key];
					}
				}
				var oTemp = document.createDocumentFragment();
				// 获取页面最大宽度
				var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
				var cutWidth = page_width * 0.0150;
				var page_width = page_width - cutWidth;
				// 获取页面最大高度
				var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight, document.body.offsetHeight);
				if (page_height < 100) {
					page_height = window.innerHeight;
				}
				// 如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
				if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
					defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
					defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
				}
				// 如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
				if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
					defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
					defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
				}
				var x;
				var y;
				for (var i = 0; i < defaultSettings.watermark_rows; i++) {
					y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
					for (var j = 0; j < defaultSettings.watermark_cols; j++) {
						x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
						var mask_div = document.createElement('div');
						mask_div.id = 'mask_div' + i + j;
						mask_div.className = 'mask_div';
						mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
						// 设置水印div倾斜显示
						mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
						mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
						mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
						mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
						mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
						mask_div.style.visibility = "";
						mask_div.style.position = "absolute";
						mask_div.style.left = (x - 15) + 'px';
						mask_div.style.top = (y - 15) + 'px';
						mask_div.style.overflow = "hidden";
						mask_div.style.zIndex = "9999";
						mask_div.style.pointerEvents = 'none';// pointer-events:none
						// 让水印不遮挡页面的点击事件
						// mask_div.style.border="solid #eee 1px";
						mask_div.style.opacity = defaultSettings.watermark_alpha;
						mask_div.style.fontSize = defaultSettings.watermark_fontsize;
						mask_div.style.fontFamily = defaultSettings.watermark_font;
						mask_div.style.color = defaultSettings.watermark_color;
						mask_div.style.textAlign = "center";
						mask_div.style.width = defaultSettings.watermark_width + 'px';
						mask_div.style.height = defaultSettings.watermark_height + 'px';
						mask_div.style.display = "block";
						// 交叉网格显示
						if ((i % 2 == 0) && (j % 2 == 0)) {
							oTemp.appendChild(mask_div);
						}
						if ((i % 2 == 1) && (j % 2 == 1)) {
							oTemp.appendChild(mask_div);
						}
					}
				}
				document.body.appendChild(oTemp);
			});
		}
	},
	provide : function() {
		return {
			menuList : this.menuList,
			menuType : this.menuType,
			menuTypeName : this.menuTypeName,
			titleName : this.titleName
		};
	},
	watch : {
		titleName : function(val) {
			this.hide();
			this.getMenu(val).show = true;
			this.$refs.rightIcon.name = this.rightIconComponents[this.titleName];
		}
	}
};