page.index.components["menu-left"] = {
	inject : [ 'menuTypeName', 'menuType' ],
	template : (function() {
		var html = "";
		html += '<van-sidebar v-model="menuSelected">';
		html += '	<van-collapse v-for="menu in menuType" v-model="menuTypeNames">';
		html += '		<van-collapse-item :name="menu.menuId">';
		html += '		<template #title>';
		html += '			<span v-html="handleMenu(menu)"></span>';
		html += '		</template>';
		html += '			<van-sidebar-item v-for="twoMenu in menu.twoLevelMenu" @click="menuOnClick(twoMenu);">';
		html += '				<template #title>';
		html += '					<span v-html="handleMenu(twoMenu)"></span>';
		html += '				</template>';
		html += '			</van-sidebar-item>';
		html += '		</van-collapse-item>';
		html += '	</van-collapse>';
		html += '</van-sidebar>';
		return html;
	})(),
	data : function() {
		return {
			menuTypeNames : Vue.ref(this.menuTypeName),
			menuSelected : Vue.ref("")
		};
	},
	emits : [ "select" ],
	methods : {
		handleMenu : function(menu) {
			var menuIconType = menu.menuIcon == "" ? 2 : menu.menuIconType;
			return '<i class="layui-icon' + menuIconType + '" >' + (menu.menuIcon || "&#xe779;") + '</i> ' + menu.menuName;
		},
		menuOnClick : function(twoMenu) {
			this.$emit("select", twoMenu);
		}
	}
}