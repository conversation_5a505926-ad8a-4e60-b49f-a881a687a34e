page.index.components["top-menu"] = {
	template : (function() {
		var html = "";
		html += '<van-cell :title="topMenuName" @click="showTopMenu = true">';
		html += '	<template #right-icon>';
		html += '		<van-icon name="exchange" size="16px" :style="{top: \'5px\', left: \'3px\'}"></van-icon>';
		html += '	</template>';
		html += '</van-cell>';
		return html;
	})(),
	data : function() {
		return {};
	},
	emits : [ "select" ],
	methods : {
		menuOnClick : function(twoMenu) {
			this.$emit("select", twoMenu);
		}
	}
}