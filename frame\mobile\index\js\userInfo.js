page.userInfo.option = {
	created : function() {
		this.getUserInfo();
	},
	data : function() {
		return {
			user : Vue.ref({})
		};
	},
	methods : {
		getUserInfo : function() {
			var that = this;
			return ajax({
				url : basePath + "frame/useraction/getUserInfo.spring"
			}).then(function(data) {
				that.user = data.user;
			});
		}
	},
	computed : {
		setSexText : function() {
			var value = this.user.sex;
			var text = "";
			if (value == "0") {
				text = "男";
			} else if (value == "1") {
				text = "女";
			} else if (value == "2") {
				text = "未知";
			}
			return text;
		},
		setManagerText : function() {
			var value = this.user.isManager;
			var text = "";
			if (value == "2") {
				text = "否";
			} else if (value == "1") {
				text = "是";
			} else if (value == "0") {
				text = "未知";
			}
			return text;
		}
	}
}