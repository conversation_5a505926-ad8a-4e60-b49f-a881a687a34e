<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>用户信息</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/vant/css/index.css">
<link rel="stylesheet" type="text/css" href="css/index.css">
</head>
<body>
	<form>
		<div app="userInfo">
			<van-cell-group>
				<van-cell>
					<template #title>
						<font style="color: green;font-weight: bolder;font-size:15px;">个人信息</font>
					</template>
				</van-cell>
				<van-cell title="所属医院" :value="user.compName"></van-cell>
				<van-cell title="所属科室" :value="user.deptName"></van-cell>
				<van-cell title="用户编号" :value="user.userCode"></van-cell>
				<van-cell title="用户名称" :value="user.userName"></van-cell>
				<van-cell title="拼音缩写" :value="user.pinyin"></van-cell>
				<van-cell title="顺序号" :value="user.sequence"></van-cell>
				<van-cell title="性 别" :value="setSexText"></van-cell>
				<van-cell title="本人学历" :value="user.education"></van-cell>
				<van-cell title="手机号码" :value="user.tel"></van-cell>
				<van-cell title="出生日期" :value="(user.birthday || '').substr(0,10)"></van-cell>
				<van-cell title="电子邮箱" :value="user.email"></van-cell>
				<van-cell title="职称" :value="user.title"></van-cell>
				<van-cell title="身份类别" :value="user.identityClass"></van-cell>
				<van-cell title="行政主管人员" :value="setManagerText"></van-cell>
				<van-cell title="入职时间" :value="(user.entryDate || '').substr(0,10)"></van-cell>
				<van-cell title="现任职务" :value="user.currentDuty"></van-cell>
				<van-cell title="兼任行政职务" :value="user.otherDuty"></van-cell>
				<van-cell title="执业证书编号" :value="user.certificateNo"></van-cell>
				<van-cell title="首次注册执业时间" :value="(user.firstCertificateTime || '').substr(0,10)"></van-cell>
			</van-cell-group>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/vant/js/vue.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/vant.min.js?version=*******"></script>
<script type="text/javascript" src="../../../plugins/vant/js/assemblys2.js?version=*******"></script>
<script type="text/javascript" src="js/userInfo.js?version=*******"></script>
