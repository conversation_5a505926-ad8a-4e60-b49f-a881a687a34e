var pubMsg = {
	openWindow : function(url, obj) {
		if (!url) {
			return;
		}
		url = basePath + decodeURIComponent(url);
		url += url.indexOf("?") != -1 ? "&HAS_FRAME_MSG_ENTRANCE=1" : "?HAS_FRAME_MSG_ENTRANCE=1";
		var title = obj.getAttribute("subject") ? obj.getAttribute("subject") + "详情" : "查看详情";
		top.assemblys.addTab(title, url);
	}
}

page.msgList.option = {
	created : function() {
		var that = this;
		that.initTopButton();
		that.$nextTick(function() {
			that.$refs.list.onRefresh();
		});
	},
	components : {
		"custom-filter-search" : null,
		"custom-list" : null
	},
	data : function() {
		var that = this;
		var fieldList = [ {
			label : "关键字",
			fieldSet : "text",
			key : "keyword",
			placeholder : "主题"
		}, {
			label : "状态",
			fieldSet : "select",
			key : "state",
			options : [ {
				name : "已读",
				value : "1"
			}, {
				name : "未读",
				value : "0"
			} ]
		} ];
		return {
			search : Vue.ref(false),
			fieldList : Vue.ref(fieldList),
			selectType : Vue.ref(""),
			checkboxSelectAll : Vue.ref(false),
			param : Vue.ref({
				state : "0",
			}),
			data : {
				url : basePath + "frame/msg/getMsgPager.spring",
				method : "get",
				param : {
					state : "0",
					"appCode" : top.appCode || ""
				},
				cols : [ {
					left : {
						key : function(d) {
							return "<span style='font-weight: bold;font-size: 15px;'>" + assemblys.escape(d.subject) + "</span>";
						},
					},
					right : {
						key : function(d) {
							var html = "";
							if (d.readDate) {
								html += '<font style="color: blue">已读</font>';
							} else {
								html += '<font style="color: red;">未读</font>';
							}
							return html;
						}
					}
				}, {
					left : {
						key : "empName",
						encode : true
					},
					right : {
						key : "sendDate"
					}
				} ],
				more : { // 更多
					key : function(d) {
						return "展开详情";
					}, // 左侧显示字段
					encode : true,
					cols : [ {
						key : function(d) {
							return that.htmlDecode(d.content);
						},
					} ]
				},
			}
		};
	},
	methods : {
		// HTML解码
		htmlDecode : function(text) {
			var temp = document.createElement("div");
			temp.innerHTML = text;
			var output = temp.innerText || temp.textContent;
			temp = null;
			return output;
		},
		setMsgReaded : function(obj, index) {
			if (obj.readDate) {
				return;
			}
			ajax({
				url : basePath + "frame/msg/saveMsgReaded.spring",
				type : "post",
				data : {
					msgID : obj.msgID
				}
			}).then(function() {
				obj.readDate = "1";
				
				// 更新小圆点
				top.page.index.vm.readDraft();
			})
		},
		//查询
		onSelect : function(values) {
			var that = this;
			that.data.param = values;
			that.data.param.appCode = that.param.appCode;
			if (that.data.param.state == "") {
				that.data.param.state = "99";
			}
			that.search = false;
			that.$refs.list.onRefresh();
		},
		initTopButton : function() {
			var that = this;
			var html = '';
			html += '<van-icon name="search" size="20" @click="showSearch" :style="{height: \'24px\'}"></van-icon>';
			
			//重写右上角导航栏
			top.page.index.vm.initTopRightTitle({
				template : html,
				props : [ "showIcon" ],
				data : function() {
					return {};
				},
				methods : {
					showSearch : function() {
						that.$root.search = !that.$root.search;
					}
				}
			});
		},
	},
};