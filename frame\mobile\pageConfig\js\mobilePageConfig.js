config = {
	singleParam : param.get("singleParam"),
	init : function() {
		config.getMobilePageConfig();
	},
	getMobilePageConfig : function() {
		$.ajax({
			url : basePath + "frame/dict/getMobilePageConfig.spring",
			type : "get",
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var mobileConfigList = data.mobileConfigList;
					if (mobileConfigList.length > 0) {
						if (mobileConfigList.length == 1) {
							window.location.href = basePath + mobileConfigList[0].dictContent;
						} else {
							$("#mobile").removeClass("layui-hide");
							for (var i = 0; i < mobileConfigList.length; i++) {
								config.addCard(mobileConfigList[i]);
							}
						}
					} else {
						var url = basePath + "/frame/mobile/index/index.html?appCode= " + param.get("appCode") + "&singleCode=" + param.get("singleCode");		
						if (config.singleParam) {
							url += "&singleParam=" + config.singleParam;
	 					}
						window.location.href = url;
					}
				} else {
					window.location.href = basePath + "/500.jsp";
				}
			}
		});
	},
	addCard : function(data) {
		var url = data.dictContent;
		var appCode = data.appCode;
		if (url) {
			var child = {
				"tagName" : "div",
				"className" : "pub_list",
				"id" : data.dictCode,
				"onclick" : function() {
					url += "?1=1";
					if (appCode) {
						url += "&appCode=" + appCode;
					}
					if (config.singleParam) {
						url += "&singleParam=" + config.singleParam;
 					}
					window.location.href = basePath + url;
				},
				"children" : [{
					"tagName" : "div",
					"className" : "pub_list_listLMess",
					"children" :  [ {
						"tagName" : "img",
						 "src" : data.logo != "" ? config.checkUrl(data.server, data.logo) : "",
						 "className" : "logoImg"
						}, {
							"tagName" : "span",
							"innerText" :  data.dictName,
						},{
						"tagName" : "i",
						"className" : "pub_icon layui-icon layui-icon-right i_icon"
					}]
				}]
			};
			assemblys.createElement(child, $("#mobile")[0]);
		} 
	
	},
	checkUrl : function(server, indexPage, customServer) {
		var prevUrl = "";
		var url = "";
		if (server == "" && indexPage == "") {
			return url;
		}
		if (server == "") {
			prevUrl = basePath;
		} else {
			if (server.indexOf("http") != -1) {
				prevUrl = server;
			} else {
				prevUrl = basePath2 + server;
			}
		}
		// 如果有自定义的指向站点
		if (customServer) {
			if (customServer.indexOf("http") != -1) {
				prevUrl = customServer;
			} else {
				prevUrl = basePath + customServer;
			}
		}
		
		if (!indexPage) {
			url = prevUrl;
		} else {
			if (indexPage.indexOf("http") != -1) {
				url = indexPage;
			} else {
				// 自动补斜杠
				var prefix = indexPage.substring(0, 1);
				if (prefix != "/") {
					indexPage = "/" + indexPage;
				}
				url = prevUrl + indexPage;
			}
		}
		return url;
	},
}

config.init();