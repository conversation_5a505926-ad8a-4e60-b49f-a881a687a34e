<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/documentTypeEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="docTypeId">
		<input type="hidden" name="docTypeCode">
		<input type="hidden" name="pCode">
		<input type="hidden" name="nCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						上级名称
					</label>
					 <div class="layui-input-inline">
						<select id="parentDocTypeCode" name="parentDocTypeCode" lay-filter="parentDocTypeCode" lay-search></select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						名称
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="docTypeName" value="" autocomplete="off"  maxlength="200" class="layui-input" />
					</div>
				</div>	
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						排序
					</label>
					<div class="layui-input-inline">
						<input type="text" name="sort" value="1" limit="5" min="1" lay-verify="required|limit|integer" autocomplete="off" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						是否显示
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="status" value="0" title="否"  />
						<input type="radio" name="status" value="1" title="是" checked="checked" />
					</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/documentTypeEdit.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		documentTypeEdit.init();
	});
</script>