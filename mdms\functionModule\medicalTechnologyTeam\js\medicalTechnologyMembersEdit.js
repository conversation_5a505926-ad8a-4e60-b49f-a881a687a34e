var medicalTechnologyMembersEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			medicalTechnologyMembersEdit.getMedicalTechnologyMembers();
			medicalTechnologyMembersEdit.initLayui();
			$("#showTeam").html("<font color='red'> -" + param.get("mTTeamName") + "</font>");
		});
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			medicalTechnologyMembersEdit.saveMedicalTechnologyMembers();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	getMedicalTechnologyMembers : function() {
		return $.ajax({
			url : basePath + "mdms/medicalTechnologyMembers/getMedicalTechnologyMembers.spring",
			data : {
				tMemberID : param.get("tMemberID"),
				deptID : param.get("deptID"),
				compNo : param.get("compNo")
			}
		}).then(function(data) {
			param.set(null, data.medicalTechnologyMembers);
			return data;
		});
	},
	saveMedicalTechnologyMembers : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/medicalTechnologyMembers/saveMedicalTechnologyMembers.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.medicalTechnologyMembersList.getMedicalTechnologyMembersPager();
				assemblys.closeWindow();
				window.isSubmit = false;
			});
			return data;
		});
	},
	// 指定医院，选部门人员
	showUDSelector : function() {
		layer.open({
			type : 2,
			title : "选择人员",
			scrollbar : false,
			maxmin : false,
			area : [ '100%', '100%' ],
			//hwx 2024年5月28日下午2:41:15 更换大数据获取用户
			content : basePath + "plugins/udSelector/selectUserPage.jsp?compNo=" + param.get("compNo") + "&type=user&callback=medicalTechnologyMembersEdit.callback&model=radio"
		});
	},
	callback : function(data) {
		$(".userName").val(data.name);
		$(".userCode").val(data.value);
	}
}