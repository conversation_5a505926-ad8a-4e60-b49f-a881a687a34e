var medicalTechnologyMembersList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			medicalTechnologyMembersList.medicalTechnologyMembersListInit().then(function(data) {
				medicalTechnologyMembersList.getMedicalTeamTree();
				var customBtnDom = [ {
					title : "导出",
					className : "layui-icon layui-icon-export skin-div-font",
					onclick : medicalTechnologyMembersList.exportList
				} ];
				filterSearch.init(basePath, medicalTechnologyMembersList.getFilterParams(data), medicalTechnologyMembersList.getMedicalTechnologyMembersPager, customBtnDom);
				//隐藏搜索框
				$("#filter").addClass("layui-hide");
				medicalTechnologyMembersList.initLayuiForm();
			});
		});
	},
	medicalTechnologyMembersListInit : function() {
		return $.ajax({
			url : basePath + "mdms/medicalTechnologyMembers/medicalTechnologyMembersListInit.spring?compNo=" + param.get("compNo")
		}).then(function(data) {
			var formSelects = layui.formSelects;
			formSelects.render('deptSelect');
			var src = "";
			$.each(data.deptList, function(index, item) {
				src += '<option value="' + item.deptId + '">' + item.deptName + '</option>';
			})
			$("#deptSelect").html(src);
			formSelects.render('deptSelect');
			window.userList = data.userList;
			//hwx 2024年1月26日下午3:43:00 根据功能权限控制按钮显示功能
			medicalTechnologyMembersList.handleFunRight();
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		mdmsCommon.loadCompNo(function() {
			medicalTechnologyMembersList.medicalTechnologyMembersListInit().then(function() {
				medicalTechnologyMembersList.getMedicalTeamTree().then(function() {
					medicalTechnologyMembersList.getMedicalTechnologyMembersPager();
				});
			});
		});
		
		layui.form.render();
	},
	getMedicalTeamTree : function() {
		return $.ajax({
			url : basePath + "mdms/medicalTechnologyMembers/getMedicalTechnologyTeam.spring?deptSelect=" + param.get("deptSelect") + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo"),
			dataType : "json",
			skipDataCheck : true
		}).then(function(data) {
			var childrenList = [];
			if (data && data.data && data.data.items && data.data.items.length > 0) {
				for (var i = 0; i < data.data.items.length; i++) {
					var treeNode = {};
					var dataTemp = data.data.items[i];
					treeNode.id = dataTemp.mTTeamCode;
					treeNode.mTTeamID = dataTemp.mTTeamID;
					treeNode.deptID = dataTemp.deptID;
					treeNode.title = dataTemp.mTTeamName;
					treeNode.spread = true;
					childrenList.push(treeNode);
				}
				var treeNodeList = [ {
					id : 0,
					mTTeamID : 0,
					deptID : 0,
					title : "全部",
					spread : true,
					children : childrenList
				} ];
				//渲染
				layui.tree.render({
					//绑定元素
					elem : '#tree',
					data : treeNodeList,
					onlyIconControl : true,
					click : function(item) {
						var mTTeamCode = item.data.id;
						var mTTeamID = item.data.mTTeamID;
						var deptID = item.data.deptID;
						var mTTeamName = item.data.title;
						param.set("mTTeamCode", mTTeamCode);
						param.set("mTTeamName", mTTeamName);
						param.set("mTTeamID", mTTeamID);
						param.set("deptID", 0);
						medicalTechnologyMembersList.getMedicalTechnologyMembersPager();
						layui.form.render();
					}
				});
			} else {
				var treeNodeList = [ {
					id : 0,
					mTTeamID : 0,
					deptID : 0,
					title : "全部",
					spread : true,
					children : childrenList
				} ];
				//渲染
				layui.tree.render({
					//绑定元素
					elem : '#tree',
					data : treeNodeList,
					onlyIconControl : true
				});
				param.set("mTTeamCode", "-1");
			}
			// 默认选中第一个节点点击
			if (param.get("mTTeamCode") == -1 || param.get("mTTeamCode") == 0) {
				var $treeChild = $(".layui-tree-entry:eq(0)");
				$treeChild.find(".layui-tree-txt").click();
				$treeChild.find(".layui-tree-txt").css("background-color", "rgb(220, 239, 230)");
			} else {
				//hwx 2024年6月3日下午6:07:22 选择某个小组，再输入姓名查询，会查询全部小组的人员
				$(".layui-tree-pack").find("div").each(function(item) {
					var mTTeamCode = $(this).attr("data-id");
					if (mTTeamCode == param.get("mTTeamCode")) {
						$(this).find(".layui-tree-txt").click();
						$(this).find(".layui-tree-txt").css("background-color", "rgb(220, 239, 230)");
					}
				});
				
			}
		});
	},
	getFilterParams : function(data) {
		var params = [];
		return params;
	},
	getMedicalTechnologyMembersPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 70,
			align : "center",
			templet : function(d) {
				var html = '';
				if (param.get("hasMEdit")) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditMedicalTechnologyMembers"></i>';
				}
				if (param.get("hasMDel")) {
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteMedicalTechnologyMembers"></i>';
				}
				return html;
			}
		} ];
		if (param.get("isTTeam")) {
			cols.push({
				title : '科室',
				align : "center",
				width : 150,
				templet : function(d) {
					return assemblys.htmlEncode(d.DeptName);
				}
			});
		}
		cols.push({
			title : '组名称',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.htmlEncode(d.mTTeamName);
			}
		}, {
			title : '组介绍',
			align : "center",
			width : 250,
			templet : function(d) {
				return assemblys.htmlEncode(d.mttDuty);
			}
		}, {
			title : '姓名',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.htmlEncode(d.userCode + "/" + d.userName);
			}
		}, {
			title : '职务',
			align : "center",
			width : 80,
			templet : function(d) {
				return assemblys.htmlEncode(d.identity);
			}
		}, {
			title : '职责',
			align : "center",
			width : 250,
			templet : function(d) {
				return assemblys.htmlEncode(d.duty);
			}
		}, {
			title : '顺序号',
			align : "center",
			width : 80,
			templet : function(d) {
				return assemblys.htmlEncode(d.seqNo);
			}
		}, {
			title : '备注',
			align : "center",
			width : 350,
			templet : function(d) {
				return assemblys.htmlEncode(d.remark);
			}
		}, {
			title : '创建时间',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.dateToStr(d.createDate);
			}
		}, {
			title : '创建人',
			align : "center",
			width : 180,
			templet : function(d) {
				return assemblys.htmlEncode(d.createUserCode + "/" + d.createUserName);
			}
		});
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/medicalTechnologyMembers/getMedicalTechnologyMembersPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditMedicalTechnologyMembers : medicalTechnologyMembersList.toEditMedicalTechnologyMembers,
				deleteMedicalTechnologyMembers : medicalTechnologyMembersList.deleteMedicalTechnologyMembers
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/medicalTechnologyMembers/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditMedicalTechnologyTeam : function(d) {
		if (d) {
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				id : "toEditMedicalTechnologyTeam",
				area : [ '850px', '75%' ],
				title : false,
				scrollbar : false,
				content : "medicalTechnologyTeamEdit.html?funCode=" + param.get("funCode") + "&mTTeamID=" + d.mTTeamID + "&compNo=" + param.get("compNo")
			});
		} else {
			var mTTeamID = param.get("mTTeamID");
			if (mTTeamID > 0) {
				layer.open({
					type : 2,
					skin : 'layui-layer-aems',
					id : "toEditMedicalTechnologyTeam",
					area : [ '850px', '80%' ],
					title : false,
					scrollbar : false,
					content : "medicalTechnologyTeamEdit.html?funCode=" + param.get("funCode") + "&mTTeamID=" + param.get("mTTeamID") + "&compNo=" + param.get("compNo")
				});
			} else {
				assemblys.msg("请选择组");
				return;
			}
		}
	},
	toDelMedicalTechnologyTeam : function() {
		var mTTeamID = param.get("mTTeamID");
		if (mTTeamID) {
			//添加对小组是否在使用中进行判断
			var checked = medicalTechnologyMembersList.findAssessment(mTTeamID, null)
			if (checked) {
				assemblys.msg("该组正在考评,无法删除");
			} else {
				if (mTTeamID > 0) {
					$.ajax({
						url : basePath + "mdms/medicalTechnologyMembers/deleteMedicalTechnologyTeam.spring",
						type : "post",
						data : {
							mTTeamID : mTTeamID
						}
					}).then(function(data) {
						assemblys.msg("删除成功", function() {
							medicalTechnologyMembersList.getMedicalTeamTree();
						});
					});
				} else {
					assemblys.msg("请选择组");
					return;
				}
			}
		}
	},
	toEditMedicalTechnologyMembers : function(d) {
		var mTTeamID = param.get("mTTeamID");
		if (mTTeamID <= 0) {
			assemblys.msg("请选择组");
			return;
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditMedicalTechnologyMembers",
			area : [ '850px', '70%' ],
			title : false,
			scrollbar : false,
			content : "medicalTechnologyMembersEdit.html?funCode=" + param.get("funCode") + "&tMemberID=" + d.tMemberID + "&mTTeamCode=" + param.get("mTTeamCode") + "&deptID=" + param.get("deptID") + "&mTTeamName=" + param.get("mTTeamName")
		});
	},
	deleteMedicalTechnologyMembers : function(d) {
		//添加对小组是否在使用中进行判断
		var checked = medicalTechnologyMembersList.findAssessment(null, d.tMemberID)
		if (checked) {
			assemblys.msg("该组员正在考评,无法删除");
		} else {
			return $.ajax({
				url : basePath + "mdms/medicalTechnologyMembers/deleteMedicalTechnologyMembers.spring",
				type : "post",
				data : {
					tMemberID : d.tMemberID
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					medicalTechnologyMembersList.getMedicalTechnologyMembersPager();
				});
				return data;
			});
		}
	},
	toSearch : function() {
		medicalTechnologyMembersList.getMedicalTeamTree();
		medicalTechnologyMembersList.getMedicalTechnologyMembersPager();
	},
	//查找是否与考评业务关联
	findAssessment : function(mTTeamID, tMemberID) {
		var checked;
		$.ajax({
			url : basePath + "mdms/doctorassessmentregister/getMedicaltechnologyteam.spring",
			type : "GET",
			data : {
				mTTeamID : mTTeamID,
				memberID : tMemberID
			},
			async : false,
			success : function(data) {
				if (data.veifyTeam > 0) {
					checked = true
				} else {
					checked = false
				}
			}
		})
		return checked
	},
	//hwx 2024年1月26日下午3:43:00 根据功能权限控制按钮显示功能
	handleFunRight : function() {
		var funCode = param.get("funCode");
		mdmsCommon.getRightByFunCode(funCode).then(function(d) {
			if (d) {
				if (d.hasAdd) {
					$(".showAdd").removeClass("layui-hide");
				}
				if (d.hasEdit) {
					$(".showEdit").removeClass("layui-hide");
				}
				if (d.hasDel) {
					$(".showDel").removeClass("layui-hide");
				}
			}
		});
		//hwx 2024年1月26日下午3:59:42 判断是否考评小组
		if (funCode == assemblys.top.mdms.mdmsConstant.MEDICAL_TECHNOLOGY_TEAM) {
			$(".showTTeam").removeClass("layui-hide");
			param.set("isTTeam", 1);
			mdmsCommon.getRightByFunCode(assemblys.top.mdms.mdmsConstant.MDMS_TEAM_MEMBER).then(function(d) {
				if (d) {
					if (d.hasAdd) {
						$(".showMAdd").removeClass("layui-hide");
					}
					if (d.hasEdit) {
						param.set("hasMEdit", true);
					}
					if (d.hasDel) {
						param.set("hasMDel", true);
					}
				}
			});
		} else if (funCode == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_TEAM) {
			//hwx 2024年1月29日上午10:02:44 考评小组组员权限
			mdmsCommon.getRightByFunCode(assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_TEAM_MEMBER).then(function(d) {
				if (d) {
					if (d.hasAdd) {
						$(".showMAdd").removeClass("layui-hide");
					}
					if (d.hasEdit) {
						param.set("hasMEdit", true);
					}
					if (d.hasDel) {
						param.set("hasMDel", true);
					}
				}
			});
		}
	}
}