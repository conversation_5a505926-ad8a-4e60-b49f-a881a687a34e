var medicalTechnologyTeamEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			if (param.get("mTTeamID") != 0) {
				medicalTechnologyTeamEdit.getMedicalTechnologyTeam();
			}
			medicalTechnologyTeamEdit.initFilelUpload();
			if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MEDICAL_TECHNOLOGY_TEAM) {
				$(".showTTeam").removeClass("layui-hide");
				$('input:radio[name=assessmentStatus]')[1].checked = true;
			} else if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_TEAM) {
				param.set("deptName", "信息科");
			}
			medicalTechnologyTeamEdit.initLayui();
		});
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			medicalTechnologyTeamEdit.saveMedicalTechnologyTeam();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	initFilelUpload : function() {
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		// 文件
		pubUploader.initCustomUpload("customFile", {
			type : "file",
			fileAllow : [ ".bmp", ".jpg", ".png", ".gif", ".BMP", ".JPG", ".JPEG", ".PNG", ".GIF" ],
			fileAllowNum : 1
		}, function(list) {
			$("#ueditorFileDiv").empty();
			pubUploader.setFileList(list, "#ueditorFileDiv");
		});
	},
	getMedicalTechnologyTeam : function() {
		return $.ajax({
			url : basePath + "mdms/medicalTechnologyMembers/getMedicalTechnologyTeamDetail.spring",
			data : {
				mTTeamID : param.get("mTTeamID")
			}
		}).then(function(data) {
			param.set(null, data.medicalTechnologyTeam);
			if (data.fileList) {
				if (data.fileList.length > 0) {
					var filesData = data.fileList;
					var result = [];
					for (var k = 0; k < filesData.length; k++) {
						var typeFileTemp = filesData[k];
						var files = {};
						files.title = typeFileTemp.attaName;
						files.url = typeFileTemp.attaUrl;
						files.size = typeFileTemp.attaSize;
						files.type = typeFileTemp.attaType;
						result.push(files);
					}
					
					pubUploader.setFileList(result, "#ueditorFileDiv");
				}
			}
			
			return data;
		});
	},
	saveMedicalTechnologyTeam : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		var fileList = [];
		$("#ueditorFileDiv").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			fileList.push(typeFiles);
		});
		//hwx 2024年5月13日下午3:27:52 增加附件必填校验
		if (fileList.length == 0) {
			assemblys.msg("请上传图片!");
			window.isSubmit = false;
			return false;
		}
		var fileListJson = JSON.stringify(fileList);
		$("#fileListJson").val(fileListJson);
		return $.ajax({
			url : basePath + "mdms/medicalTechnologyMembers/saveMedicalTechnologyTeam.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.medicalTechnologyMembersList.getMedicalTeamTree();
				assemblys.closeWindow();
				window.isSubmit = false;
			});
			return data;
		});
	},
	
	// 指定医院，选部门人员
	showUDSelector : function() {
		layer.open({
			type : 2,
			title : "选择科室",
			scrollbar : false,
			maxmin : false,
			area : [ '100%', '100%' ],
			content : basePath + "plugins/udSelector/selectUserList.jsp?compNo=" + param.get("compNo") + "&type=dept&callback=medicalTechnologyTeamEdit.callback&model=radio"
		});
	},
	callback : function(data) {
		$(".deptName").val(data.name);
		$(".deptID").val(data.value);
	}
}