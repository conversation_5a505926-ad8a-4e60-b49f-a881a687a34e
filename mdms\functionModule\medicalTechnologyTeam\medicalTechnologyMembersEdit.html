<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/medicalTechnologyMembersEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="mTTeamCode">
		<input type="hidden" name="mTTeamName">
		<input type="hidden" name="tMemberID">
		<input type="hidden" name="compNo">
		<input type="hidden" name="deptID">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
				<span id="showTeam"></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>姓名
					</label>
					<div class="layui-input-inline">
						<input type="text" name="userName" lay-filter="userName" style="cursor: pointer;"  lay-verify="required" onclick="medicalTechnologyMembersEdit.showUDSelector()"  class="layui-input showReadOnly userName" readonly="readonly"/>
						<input type="hidden" name="userCode" lay-filter="userCode" lay-verify="required"  class="layui-input layui-hide userCode" />
					</div>
					<label class="layui-form-label">
						<span style="color: red">*</span>职务
					</label>
					<div class="layui-input-inline">
						<select lay-filter="identity" name ="identity" id="identity">
							<option value="0" checked="true">组员</option>
							<option value="1">副组长</option>
							<option value="2">组长</option>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>职责
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="required|limit" limit="2000" style="width:520px;" maxlength="2000" name="duty" value="" class="layui-textarea"></textarea>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>顺序号
					</label>
					<div class="layui-input-inline">
						<input type="text" name="seqNo" limit="5" value="1" lay-verify="required|limit|integer" maxlength="5" class="layui-input" />
					</div>
					<label class="layui-form-label">
						<span style="color: red">*</span>状态
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="status" value="1" title="启用" checked="checked" lay-filter="status" />
						<input type="radio" name="status" value="0" title="停用" lay-filter="status" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						备注
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="limit" limit="2000" style="width:520px;" maxlength="2000" name="remark" value="" class="layui-textarea"></textarea>
					</div>
				</div>

			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/medicalTechnologyMembersEdit.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		medicalTechnologyMembersEdit.init();
	});
</script>