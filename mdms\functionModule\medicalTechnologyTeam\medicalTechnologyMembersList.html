<!DOCTYPE html>
<html>
<head>
<title>医疗技术小组管理</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
<link rel="stylesheet" type="text/css" href="css/medicalTechnologyMembersList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="mTTeamID">
		<input type="hidden" name="mTTeamCode" value="-1">
		<input type="hidden" name="mTTeamName">
		<input type="hidden" name="deptID">
		<input type="hidden" name="hasMEdit">
		<input type="hidden" name="hasMDel">
		<input type="hidden" name="isTTeam">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="layui-input-inline h28 lh28 layui-hide showTTeam" style="left:20px;z-index: 99990;">
				<select id="deptSelect"  xm-select-search="" xm-select="deptSelect"  name="deptSelect" ></select>
			</div>	
			<div class="layui-input-inline h28 lh28" style="left:30px;">
				<input class="layui-input" style="width: 200px; display: inline;" placeholder="工号/姓名" type="text" name="keyword" autocomplete="off" >
			</div>
			<div class="layui-input-inline" style="left:90px;margin-bottom:5px;">
				<button type="button" class="layui-btn layui-btn-sm" onclick="medicalTechnologyMembersList.toSearch()">查询</button>
			</div>
			<div class="head0_right fr">
				<div class="layui-input-inline layui-hide">
					<select name="compNo" lay-filter="compNo"></select>
					<input type="hidden" name="compNo" />
				</div>
				<button type="button" class="layui-btn layui-btn-sm layui-hide showAdd" onclick="medicalTechnologyMembersList.toEditMedicalTechnologyTeam({mTTeamID:0});">新增组</button>
				<button type="button" class="layui-btn layui-btn-sm layui-hide showEdit" onclick="medicalTechnologyMembersList.toEditMedicalTechnologyTeam();">编辑组</button>
				<button type="button" class="layui-btn layui-btn-sm layui-hide showDel" onclick="medicalTechnologyMembersList.toDelMedicalTechnologyTeam();">删除组</button>
				<button type="button" class="layui-btn layui-btn-sm layui-hide showMAdd" onclick="medicalTechnologyMembersList.toEditMedicalTechnologyMembers({tMemberID:0});">新增组员</button>
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		<div class="layui-row">
			<div class="treeDiv">
				<div class="treeHead">组分类</div>
				<ul id="tree" class="tree-table-tree-box layui-box layui-tree" style="height:93%;"></ul>
			</div>
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/formSelects/formSelects-v4.min.js?r="+Math.random()></script>
<script type="text/javascript" src="js/medicalTechnologyMembersList.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		medicalTechnologyMembersList.init();
	});
</script>
</html>