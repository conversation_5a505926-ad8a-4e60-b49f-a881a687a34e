<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/medicalTechnologyMembersEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="mTTeamID">
		<input type="hidden" name="compNo">
		<input type="hidden" name="mTTeamCode">
		<input type="hidden" name="fileListJson" id="fileListJson">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label layui-hide showTTeam">
						<span style="color: red">*</span>科室
					</label>
					<div class="layui-input-inline layui-hide showTTeam">
						<input type="text" name="deptName" lay-verify="required" lay-filter="deptName" style="cursor: pointer;"  onclick="medicalTechnologyTeamEdit.showUDSelector()"  class="layui-input showReadOnly deptName" readonly="readonly"/>
						<input type="hidden" name="deptID" lay-filter="deptID"   class="layui-input layui-hide deptID" />
					</div>
					<label class="layui-form-label">
						<span style="color: red">*</span>组名称
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required|limit" limit="15" maxlength="15"  name="mTTeamName" value="" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>组介绍
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="required|limit" limit="2000" style="width:520px;" maxlength="2000" name="duty" value="" class="layui-textarea"></textarea>
					</div>
					<label class="layui-form-label layui-hide">
						<span style="color: red">*</span>评审小组
					</label>
					<div class="layui-input-inline layui-hide">
						<input type="radio" name="assessmentStatus" value="1" title="启用"  lay-filter="assessmentStatus" checked="checked"/>
						<input type="radio" name="assessmentStatus" value="0" title="停用" lay-filter="assessmentStatus"/>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>顺序号
					</label>
					<div class="layui-input-inline">
						<input type="text" name="seqNo" limit="5" value="1" lay-verify="required|limit|integer" maxlength="5" class="layui-input" />
					</div>
					<label class="layui-form-label">
						<span style="color: red">*</span>状态
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="status" value="1" title="启用" checked="checked" lay-filter="status" />
						<input type="radio" name="status" value="0" title="停用" lay-filter="status" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						备注
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="limit" limit="2000" style="width:520px;" maxlength="2000" name="remark" value="" class="layui-textarea"></textarea>
					</div>
				</div>
				
				<fieldset class="layui-elem-field">
				   <legend>图片上传</legend>
				   <div class="layui-field-box">
				    <div class="layui-form-item">
				     <label class="layui-form-label"> 
				 		 <span style="color: red;">*</span>   
				   			  图片
				   	 </label>
				     <div class="layui-input-inline">
				      <input type="button" value="上传图片" class="layui-btn layui-btn-sm" id="customFile" />
				      <div class="collapse in">
				       <ul class="cattachqueue" id="ueditorFileDiv"></ul>
				      </div>
				     </div>
				    </div>
				   </div>
				</fieldset>
				
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/medicalTechnologyTeamEdit.js?r="+Math.random()></script>

<!-- 富文本、上传组件  - 直接拷贝到项目中 -->
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.config.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/lang/zh-cn/zh-cn.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>


<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		medicalTechnologyTeamEdit.init();
	});
</script>