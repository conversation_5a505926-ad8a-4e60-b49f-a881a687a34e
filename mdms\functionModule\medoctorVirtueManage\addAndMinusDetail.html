<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>医师加减分详情</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/addAndMinusDetail.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="drugManagementID">
		<input type="hidden" name="compNo">
		<input type="hidden" name="rmosCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName>医师加减分详情</span>
			</span>
			<div class="head0_right fr"></div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<table class="layui-table main_table detail_table">
					<tr>
						<td class="tright" style="width: 15%;">医师名称</td>
						<td class="tleft" style="width: 35%;" id="partyName" name="partyName"></td>
						<td class="tright" style="width: 15%;">加减分类型</td>
						<td class="tleft" style="width: 35%;" id="addType" name="addType"></td>
					</tr>

					<tr>
						<td class="tright">加减分项目</td>
						<td class="tleft" id="dictRemark"></td>
						<td class="tright">分值</td>
						<td class="tleft" id="point" name="point"></td>
					</tr>

					<tr>
						<td class="tright">获得时间</td>
						<td class="tleft" id="getDate"></td>
						<td class="tright">说明</td>
						<td class="tleft" id="remark" name="remark"></td>
					</tr>
					<tr>
						<td colspan="4">
							<fieldset class="layui-elem-field">
						   	<legend>附件上传</legend>
						 	<div class="layui-field-box">
							    <div class="layui-form-item">
								    <label class="layui-form-label"> 附件 </label>
								    <div class="layui-input-inline">
								    	<input type="button"  value="上传附件" class="layui-btn layui-btn-sm btnfile" onclick="pubUploader.openFiles(addAndMinusEdit.attaCallback);param.set('fileIndex', 0);" />
								    	<div class="collapse in">
								    		<ul class="cattachqueue" id="ueditorFileDiv-0"></ul>
								     	</div>
								    </div>
							    </div>
						    </div>
							</fieldset>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/addAndMinusEdit.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/newTechniqueManager/js/pubMethod.js?r="+Math.random()></script>
<!-- 富文本、上传组件  - 直接拷贝到项目中 -->
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.config.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/lang/zh-cn/zh-cn.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		addAndMinusEdit.getAddAndMinus().then(function(data) {
			var addAndMinus = data.addAndMinus;
			$("#partyName").text(addAndMinus.partyName);
			$("#addType").text(addAndMinus.addType == 1 ? "减分" : "加分");
			var point = addAndMinus.addType == 1 ? "-" + addAndMinus.point : addAndMinus.point;
			$("#point").text(point);
			var getDate = assemblys.dateToStr(addAndMinus.getDate);
			$("#getDate").text(getDate); 
			$("#remark").text(addAndMinus.remark); 
			return addAndMinusEdit.getDictRemark(addAndMinus.dictCode)
		}).then(function(data) {
			$("#dictRemark").text(data.dictCode[0].AddAndMinusDictContent);
		})
	});
</script>