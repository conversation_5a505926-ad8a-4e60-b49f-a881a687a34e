<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>加减分管理</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/addAndMinusDictEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="addAndMinusDictId">
		
		<input type="hidden" name="createUserCode">
		<input type="hidden" name="cretateUserName">
		<input type="hidden" name="createDeptId">
		<input type="hidden" name="addAndMinusDictCode">
		
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						适用人员
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="isMedical" value="1" title="医务人员" checked="checked" lay-filter="isMedical" />
						<input type="radio" name="isMedical" value="0" title="非医务人员" lay-filter="isMedical" />
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						类型
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="addAndMinusDictType" value="1" title="减分" checked="checked" lay-filter="addAndMinusDictType" />
						<input type="radio" name="addAndMinusDictType" value="0" title="加分" lay-filter="addAndMinusDictType" />

					</div>
				</div>
				<div class="layui-form-item">
					
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						内容
					</label>
					
					<div class="layui-input-inline">
						<textarea type="text"  lay-verify="limit" maxlength="500" limit="500" style="width:520px;" name="addAndMinusDictContent" id="addAndMinusDictContent" value="" class="layui-textarea"></textarea>
					</div>
					
				</div>
				<div class="layui-form-item">
					
					<label class="layui-form-label">
						<span id="addtype">最大/小值</span>
						
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required|number|limit|integer" maxlength=2 name="maxPoint" value="" class="layui-input" />
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						排序号
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="seq"  maxlength=3  value="1" class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						适用科室
					</label>
					<div class="layui-input-inline">
						<select  lay-verify="required" lay-search lay-filter="fitDeptId" id="fitDeptId" name="fitDeptId">
						</select>
					</div>
					<label class="layui-form-label">
						状态
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="status" value="1" title="有效" checked="checked" lay-filter="status" />
						<input type="radio" name="status" value="0" title="无效"  lay-filter="status" />
					</div>
				</div>
				
				<div class="layui-form-item">
					<label class="layui-form-label">
						是否固定分数
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="isFixPoint" value="1" title="是"  lay-filter="isFixPoint" />
						<input type="radio" name="isFixPoint" value="0" title="否"  checked="checked"lay-filter="isFixPoint" />
					</div>
					<div id="hidePoint" class="layui-hide">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						固定分数
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="" maxlength=4  id="fixPoint" name="fixPoint" value="" class="layui-input" />
					</div>
					</div>
				</div>
				
				<div class="layui-form-item">
					
					<label class="layui-form-label">
						<span style="color: red;"></span>
						备注
					</label>
					
					<div class="layui-input-inline">
						<textarea type="text"  lay-verify="limit" maxlength="500" limit="500" style="width:520px;" name="addAndMinusDictRemark" id="addAndMinusDictRemark" value="" class="layui-textarea"></textarea>
					</div>
					
				</div>
				
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/addAndMinusDictEdit.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		addAndMinusDictEdit.init();
	});
</script>