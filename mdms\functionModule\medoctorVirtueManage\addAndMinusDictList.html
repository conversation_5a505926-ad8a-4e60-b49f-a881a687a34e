<!DOCTYPE html>
<html>
<head>
<title>AddAndMinusDict列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
<link rel="stylesheet" type="text/css" href="css/addAndMinusDictList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="state" value="99">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<div class="layui-input-inline layui-hide">
					<select name="compNo" lay-filter="compNo"></select>
					<input type="hidden" name="compNo" />
				</div>
				<button type="button" class="layui-btn layui-btn-sm" onclick="addAndMinusDictList.toEditAddAndMinusDict({addAndMinusDictId:0});">新增</button>
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam"></form>
		<div class="layui-tab" lay-filter="tabView">
			<ul id="tabView" class="layui-tab-title head2_tab h28">
				<li class="layui-this" state="99">全部</li>
				<li class="layui-this" state="1">医务人员</li>
				<li class="layui-this" state="2">非医务人员</li>
				<li class="layui-this" state="3">加分</li>
				<li class="layui-this" state="4">减分</li>
				<li class="layui-this" state="5">有效</li>
				<li class="layui-this" state="6">无效</li>
			</ul>
		</div>
		<div class="layui-row">
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="js/addAndMinusDictList.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		addAndMinusDictList.init();
	});
</script>
</html>