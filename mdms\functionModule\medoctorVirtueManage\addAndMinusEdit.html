<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/addAndMinusEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="addAndMinusId">
		<input type="hidden" name="addAndMinusCode">
		<input type="hidden" name="compNo">
		<input type="hidden" name="optDeptId">
		<input type="hidden" name="optDate">
		<input type="hidden" name="optUserCode">
		<input type="hidden" name="addStatus">
		<input type="hidden" name="partyName">
		<input type="hidden" name="addAndMinusFileListJson" id="addAndMinusFileListJson">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						适用人员
					</label>
					<div class="layui-input-inline">
						<input type="radio" lay-verify="required" lay-filter="isMedical" name="isMedical" value="1" title="医务人员" checked>
						<input type="radio" lay-verify="required" lay-filter="isMedical" name="isMedical" value="0" title="非医务人员">
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						类型
					</label>
					<div class="layui-input-inline">
						<input type="radio" lay-verify="required" lay-filter="addType" name="addType" value="0" title="加分" checked>
						<input type="radio" lay-verify="required" lay-filter="addType" name="addType" value="1" title="减分">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						医师名称
					</label>
					<div class="layui-input-inline">
						<select  lay-verify="required" lay-search lay-filter="partyCode" id="partyCode" name="partyCode"></select>
					</div>
					
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						获得时间
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="getDate" id="getDate" lay-filter="getDate" autocomplete="off" class="layui-input" laydate/>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						加减分项目
					</label>
					<div class="layui-input-inline" style="width:85%;">
						<select  lay-verify="required" lay-search lay-filter="dictCode" id="dictCode" name="dictCode">
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						是否固定分数
					</label>
					<div class="layui-input-inline">
						<input type="radio" disabled name="isFixPoint" value="1" title="是"  lay-filter="isFixPoint" />
						<input type="radio" disabled name="isFixPoint" value="0" title="否"  checked="checked"lay-filter="isFixPoint" />
					</div>
					<label id="pointText" class="layui-form-label">
						<span style="color: red;">*</span>
						分值+
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required|number|limit|double" placeholder="请输入数字" maxlength=3 name="point" id="point" value="" class="layui-input" />
					</div>
					
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						说明
					</label>
					<div class="layui-input-inline">
						<textarea type="text"  lay-verify="limit|specialCharacters" maxlength="500" limit="500" style="width:520px;" name="remark" id="remark" value="" class="layui-textarea"></textarea>
					</div>
				</div>
				
				<fieldset class="layui-elem-field">
				   <legend>附件上传</legend>
				   <div class="layui-field-box">
				    <div class="layui-form-item">
				     <label class="layui-form-label"> 附件 </label>
				     <div class="layui-input-inline">
				      <input type="button" value="上传附件" class="layui-btn layui-btn-sm" onclick="pubUploader.openFiles(addAndMinusEdit.attaCallback);param.set('fileIndex', 0);" />
				      <div class="collapse in">
				       <ul class="cattachqueue" id="ueditorFileDiv-0"></ul>
				      </div>
				     </div>
				    </div>
				   </div>
				</fieldset>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/addAndMinusEdit.js?r="+Math.random()></script>
<!-- 富文本、上传组件  - 直接拷贝到项目中 -->
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.config.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/lang/zh-cn/zh-cn.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		addAndMinusEdit.init();
	});
</script>