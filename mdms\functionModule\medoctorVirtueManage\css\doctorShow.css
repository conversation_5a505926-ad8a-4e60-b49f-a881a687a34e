.bodys1 {
	min-height: auto;
	padding: 10px;
	background: #fff;
	overflow: auto;
	position: absolute;
	top: 48px;
	bottom: 10px;
	right: 10px;
	width: 250px;
}

.bodys2 {
	min-height: auto;
	padding: 10px 15px 15px;
	background: #fff;
	overflow: auto;
	position: absolute;
	top: 48px;
	bottom: 10px;
	left: 10px;
	right: 260px;
}

body {
	width: 100%;
	min-width: 805px;
	padding: 48px 10px 10px;
	background: #eee;
	font-size: 0px;
	margin: 0;
	color: #484848;
	overflow: visible;
	font: 14px Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1, Tahoma, Arial, sans-serif;
}

html {
	height: 100%;
}

* {
	box-sizing: border-box;
}

.inblock {
	display: inline-block;
}

.h13 {
	height: 13px;
}

.lh13 {
	line-height: 13px;
}

.h28 {
	height: 28px;
}

.lh28 {
	line-height: 28px;
}

.h31 {
	height: 31px;
}

.lh31 {
	line-height: 31px;
}

.fw700 {
	font-weight: 700;
}

.mgl95 {
	margin-left: 95px;
}

.top-1 {
	position: relative;
	top: -1px;
	top: -5px\9\0;
}

.tright {
	text-align: right;
}

.tleft {
	text-align: left;
}

.fr {
	float: right;
}

.fl {
	float: left;
}

/* --------top---------- */
.head0 {
	width: 100%;
	background: #fff;
	position: absolute;
	top: 0;
	left: 0;
	line-height: 38px;
	padding: 0 10px;
}

.top {
	line-height: 30px;
	padding: 4px 0;
}

.head0 form, .head0 form input {
	display: inline-block;
	height: 28px;
	line-height: 28px;
	z-index: 100;
}

.bodys {
	min-width: 840px;
	min-height: auto;
	background: #fff;
	overflow: auto;
	position: absolute;
	top: 10px;
	bottom: 10px;
	right: 10px;
	left: 10px;
}

.wordContent {
	margin: 10px auto;
	width: 80%;
	background: #fff;
	padding: 40px 60px;
}

.body-style {
	min-height: auto;
	padding: 10px 15px 15px;
	overflow: auto;
	position: absolute;
	background: #eee;
	top: 48px;
	bottom: 0px;
	left: 0px;
	right: 0px;
}