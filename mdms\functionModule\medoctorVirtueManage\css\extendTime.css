.body {
	position: absolute;
	margin: 0px;
	padding: 0px;
	width: 100%;
	height: 100%;
}

.one_level_item {
	background-color: #dbf0e6;
	padding: 5px 10px;
	font-weight: bold;
	line-height: 20px;
	cursor: pointer;
}

.two_level_item {
	padding: 5px 15px;
	line-height: 20px;
	cursor: pointer;
}

.one_level_item:hover {
	background-color: #fffae6;
}

.two_level_item:hover {
	background-color: #fffae6;
}

.level_item_selected {
	background-color: #fffae6;
}

#rangeList {
	min-height: 310px;
	overflow-y: auto;
	overflow-x: auto;
	width: 100%;
}

.layui-form-label {
	padding-left: 25px;
	width: 178px;
}

.layui-input-inline{
	width: 500px !important;
}

.layui-required:after {
	top: 6px;
	right: 5px;
	color: red;
	content: '*';
	position: absolute;
	margin-left: 4px;
	font-weight: 700;
	line-height: 1.8em;
}

.layui-elem-field legend {
	margin-left: 70px;
}

.showTileSpan {
	vertical-align: -webkit-baseline-middle;
}


.inputWidth {
	width: 100px;
}

label {
	color: black;
}
