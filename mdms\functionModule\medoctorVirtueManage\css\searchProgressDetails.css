.showLDiv {
	position: relative;
	float: left;
	width: 20%;
	height: 100%;
}

.showRDiv {
	position: relative;
	float: left;
	padding-left: 15px;
	margin-left: 15px;
	width: 78%;
	height: 100%;
	border-left: 3px solid rgb(242, 242, 242);
}

.body {
	position: absolute;
	margin: 0px;
	padding: 0px;
	width: 100%;
	height: 100%;
}

.one_level_item {
	background-color: #dbf0e6;
	padding: 5px 10px;
	font-weight: bold;
	line-height: 20px;
	cursor: pointer;
}

.two_level_item {
	padding: 5px 15px;
	line-height: 20px;
	cursor: pointer;
}

.one_level_item:hover {
	background-color: #fffae6;
}

.two_level_item:hover {
	background-color: #fffae6;
}

.level_item_selected {
	background-color: #fffae6;
}

#rangeList {
	min-height: 310px;
	overflow-y: auto;
	overflow-x: auto;
	width: 100%;
}

.showTypeDiv {
	vertical-align: middle;
	font-size: 16fpx;
	font-weight: 600;
	background-color: #D7EFFB;
}

.showF {
	width: 100%;
	height: 500px;
}

.showType {
	width: 100%
}

.showTypeCheck {
	padding: 36px;
}

.showItemDiv {
	height: 20px;
	vertical-align: middle;
	text-align: left;
	word-break: break-all;
	word-wrap: break-word;
}

.showReadOnly {
	color: #919191;
}

#canvas {
	border: 1px solid #C1C1C1;
}

.layui-form-label {
	padding-left: 25px;
	width: 178px;
}

.layui-input-inline .layui-textarea {
	width: 500px !important;
}

input:disabled, input[disabled] {
	border: 0px !important;
}

.layui-required:after {
	top: 6px;
	right: 5px;
	color: red;
	content: '*';
	position: absolute;
	margin-left: 4px;
	font-weight: 700;
	line-height: 1.8em;
}

.layui-elem-field legend {
	margin-left: 70px;
}

.showTileSpan {
	vertical-align: -webkit-baseline-middle;
}

.showSpan {
	color: black;
	font-weight: 600;
}

.scroll::-webkit-scrollbar-thumb {
	background-color: rgba(0, 0, 0, .05);
	border-radius: 10px;
	-webkit-box-shadow: inset1px1px0rgba(0, 0, 0, .1);
}

.scroll::-webkit-scrollbar {
	width: 10px;
	height: 10px;
}

.inputWidth {
	width: 70px;
}

label {
	color: black;
}

input:disabled, input[disabled], textarea:disabled, textarea:disabled[disabled], li:disabled, li[disabled] {
	color: black;
	opacity: 1;
	-webkit-text-fill-color: black;
	-webkit-opacity: 1;
}
.tableDiv{
	margin-top: 100px;
}
.layui-tab{
	margin : 0;
	display : inline;
}
