<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/doctorAssessmentEdit.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="assessmentID">
		<input type="hidden" name="assessmentCode">
		<input type="hidden" name="assessmentStatus">
		<input type="hidden" name="assessmentDeptStatus">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
				<input type="button" class="layui-btn layui-btn-sm" value="保存并发布" lay-submit lay-filter="saveIsseus" />
				<!-- <input type="button" id="saveTime" class="layui-btn layui-btn-sm layui-hide" value="延长计划时间" lay-submit lay-filter="saveTime" /> -->
				<input type="button" id="saveTime" class="layui-btn layui-btn-sm layui-hide" value="延长计划时间" onclick="doctorAssessmentEdit.saveTime()"/>
				<input type="button" id="uploadExcel" class="layui-btn layui-btn-sm layui-hide" value="导入增减人员"/>
				<button id="tempLate" type="button" class="layui-btn layui-btn-sm layui-hide" onclick="doctorAssessmentEdit.downloadTemplate();">增减人员模板下载</button>
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="saveTemplate" />
				<input type="button" class="layui-btn layui-btn-sm makeUp layui-hide" value="补考" lay-submit lay-filter="saveMakeUp" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label layui-required"> 考评频次 </label>
					<div class="layui-input-inline">
						<select name="assessmentFrequency" lay-filter="assessmentFrequencyFilter" lay-verify="required">
						</select>
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required"> 考评期间 </label>
						<div class="layui-input-inline" assessmentTermDiv>
							<input type="text" id="assessmentTermID" name="assessmentTerm" lay-filter="assessmentTermFilter" class="layui-input" placeholder="日期" autocomplete="off" lay-verify="required" />
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required"> 考评名称 </label>
					<div class="layui-input-inline">
						<input type="text" name="assessmentName" value="" class="layui-input" autocomplete="off" lay-verify="required" maxlength="200" />
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required"> 排序号 </label>
						<div class="layui-input-inline">
							<input type="text" name="assessmentSort" value="1" class="layui-input" autocomplete="off" lay-verify="required|number|limit|integer" maxlength="200" />
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required"> 考评方式 </label>
					<div class="layui-input-inline" assessmentModeDiv>
						<select name="assessmentMode" lay-filter="assessmentModeFilter" lay-verify="required">
						</select>
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required"> 考评时间 </label>
						<div class="layui-input-inline">
							<input type="text" name="assessmentValidity" value="" class="layui-input" id="assessmentValidityID" autocomplete="off" lay-verify="required" maxlength="200" />
						</div>
						<!-- <i class="layui-icon2 layui-hide" id="levelExplatin" name="levelExplatin"> &#xe725; </i> -->
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required"> 适用人员 </label>
					<div class="layui-input-inline">
						<input type="radio" lay-verify="required" lay-filter="assessmentIfMedical" name="assessmentIfMedical" value="1" title="医务人员" checked>
						<input type="radio" lay-verify="required" lay-filter="assessmentIfMedical" name="assessmentIfMedical" value="0" title="非医务人员">
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required"> 加减分时间范围 </label>
						<div class="layui-input-inline">
							<input type="text" name="addAndMinusTime" value="" class="layui-input" id="addAndMinusTimeID" autocomplete="off" lay-verify="required" maxlength="200" />
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required"> 自评范围 </label>
					<div class="layui-input-inline">
						<select name="assessmentScope" lay-filter="assessmentScope"></select>
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required layui-hide" customLabel></label>
						<div class="layui-input-inline layui-hide" customDiv id="demo1">
						</div>
					</div>
				</div>
				<div class="layui-form-item layui-hide">
					<label class="layui-form-label layui-required"> 考评范围 </label>
					<div class="layui-input-inline">
						<select name="assessmentApprovalScope"></select>
					</div>
					<div class="commonDIV">
						<label class="layui-form-label" customApprovalLabel></label>
						<div class="layui-input-inline"><!-- lay-verify="required" -->
							<select name="assessPojectApprovalUserCode"  xm-select-search xm-select="assessPojectApprovalSelect" id="assessPojectApprovalSelectSelectID" xm-select-radio xm-select-show-count="2" xm-select-search-type="dl"></select>
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label "> 备注 </label>
					<div class="layui-input-inline">
						<textarea placeholder="请输入内容" name="assessmentRemark" class="layui-textarea " maxlength="200"></textarea>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required"> 总分 </label>
					<div class="layui-input-inline">
						<input type="text" maxlength="200" name="assessmentSumScore" class="layui-input layui-disabled" autocomplete="off" lay-verify="number" />
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required"> 合格分 </label>
						<div class="layui-input-inline">
							<input placeholder="请输入合格分" type="text" maxlength="200" name="assessmentPassScore" class="layui-input " onchange="doctorAssessmentEdit.listenChange(this)" autocomplete="off" lay-verify="required|number|limit|integer" />
						</div>
					</div>
					<div class="layui-form-item">
						<h2 class="layui-colla-title">
							<div class=" commonBtn">
								<span class="commonSpan">考评项目设置<font style="font-size:15px;color:red;">(说明：加减分由系统读取医师加减分自动统计)</font></span>
								&nbsp;&nbsp;
								<input type="button" class="layui-btn layui-btn-sm" value="新增" addBtn onclick="doctorAssessmentEdit.addBtn(1)" />
							</div>
						</h2>
						<table class="layui-table addAssessment">
						</table>
					</div>
					<div class="layui-form-item layui-hide">
						<h2 class="layui-colla-title">
							<div class="layui-input-inline commonBtn">
								<span class="commonSpan">考评小组项目设置</span>
								&nbsp;&nbsp;
								<input type="button" class="layui-btn layui-btn-sm" value="新增" addBtn onclick="doctorAssessmentEdit.addBtn(2)" />
							</div>
						</h2>
						<table class="layui-table addGroupAssess">
						</table>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorAssessmentEdit.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/formSelects/formSelects-v4.min.js?r="+Math.random()></script>
<script type="text/javascript" src="../../plugins/xm-select-v1.2.4/dist/xm-select.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	 $(function() {
		doctorAssessmentEdit.init();
	}); 
</script>