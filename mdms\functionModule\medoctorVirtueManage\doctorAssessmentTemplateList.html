<!DOCTYPE html>
<html>
<head>
<title>考评模板列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="companyCode">
		<input type="hidden" name="state" value="2">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
				<div class="layui-inline">
					<!-- 注意：这一层元素并不是必须的 -->
					<input type="text" class="layui-input" name="assessmentName" placeholder="名称" autocomplete="off">
				</div>
				<button type="button" class="layui-btn layui-btn-sm" onclick="doctorAssessmentTemplateList.getDoctorAssessmentPager();">查询</button>
			</span>
			<div class="head0_right fr">
				<div class="layui-input-inline">
					<select name="compNo" lay-filter="compNo"></select>
					<input type="hidden" name="compNo" />
				</div>
				<button type="button" class="layui-btn layui-btn-sm" onclick="doctorAssessmentList.toEditDoctorAssessment({assessmentID:0});" addTemplate>新增</button>
				<button type="button" class="layui-btn layui-btn-sm" onclick="doctorAssessmentTemplateList.templateExport();" export>导入</button>
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		<div class="layui-row">
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorAssessmentTemplateList.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorAssessmentList.js?r="+Math.random()></script>

<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		doctorAssessmentTemplateList.init();
	});
</script>
</html>