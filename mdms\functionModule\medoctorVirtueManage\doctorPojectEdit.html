<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/doctorPojectEdit.css">
<link rel="stylesheet" type="text/css" href="../../../../plugins/formSelects/formSelects-v4.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="pojectID">
		<input type="hidden" name="pojectCode">
		<input type="hidden" name="pojectComanyCode" />
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label layui-required">
							类型
					</label>
					<div class="layui-input-inline">
					      <select name="pojectType" lay-verify="required"  lay-filter="assessmentFilter" >
					      </select>
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required">
							项目得分
						</label>
						<div class="layui-input-inline">
							<input type="text" maxlength="5" lay-verify="required|number|limit|integer"  autocomplete="off" name="pojectSumScore" value="0" class="layui-input" />
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required">
						     项目名称
					</label>
					<div class="layui-input-inline" >
 							<textarea placeholder="请输入内容"  maxlength="200" lay-verify="required"   class="layui-textarea" name="pojectName" onchange="doctorPojectEdit.search(this)"></textarea>					
 					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required">
							状态
					</label>
					<div class="layui-input-inline">
						<input type="radio" lay-verify="required" name="pojectStatus" value="0" title="停用" >
						<input type="radio" lay-verify="required" name="pojectStatus" value="1" title="启用" checked>
					</div>
					<div class="commonDIV">
						<label class="layui-form-label layui-required" >
							顺序号
						</label>
						<div class="layui-input-inline">
							<input type="text" maxlength="200" lay-verify="required|number|limit|integer" autocomplete="off" name="pojectSort" value="1" class="layui-input" />
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-required">
							适用人员
					</label>
					<div class="layui-input-inline">
						<input type="radio" lay-verify="required" name="pojectIfMedical" value="1" title="医务人员" checked>
						<input type="radio" lay-verify="required" name="pojectIfMedical" value="0" title="非医务人员" >
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
							项目说明
					</label>
					<div class="layui-input-inline">
					 	<textarea placeholder="请输入内容" maxlength="200"  name="pojectRemark" class="layui-textarea "></textarea>					
					</div>
				</div>
				<div>
					<div class="layui-form-item">
						<h2 class="layui-colla-title">
							<div class="layui-input-inline commonBtn" >
								<span class="commonSpan">考评内容</span>&nbsp;&nbsp;
								<input type="button" class="layui-btn layui-btn-sm" value="新增" contentBtn onclick="doctorPojectEdit.contentBtn()"/>
							</div>
						</h2>
						<table id="contentTable" class="layui-table contentScore" lay-size="sm">
						</table>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../../plugins/formSelects/formSelects-v4.min.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorPojectEdit.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		doctorPojectEdit.init();
		doctorPojectEdit.formSelects = layui.formSelects;
	});
	$(window).load(function(){
		$("input[name=pojectComanyCode]").val(param.get("compNo"))
	})
</script>