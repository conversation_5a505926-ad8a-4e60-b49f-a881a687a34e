<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>导出预览</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css" />
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
<link rel="stylesheet" type="text/css" href="css/doctorShow.css">
</head>
<body>
	<form id="form1" class="layui-form" lay-filter="param" method="post" onsubmit="return false;">
		<input type="hidden" name="userCode" value="">
		<input type="hidden" name="funCode" value="">
		<input type="hidden" name="userName" value="">
		<input type="hidden" name="assessmentCode" value="">
		<input type="hidden" name="assessmentIfMedical" value="">
		<input type="hidden" name="addPoint" value="">
		<input type="hidden" name="minusPoint" value="">
		<input type="hidden" name="addRemark" value="">
		<input type="hidden" name="minusRemark" value="">
		<input type="hidden" name="sumPoint" value="">
		<input type="hidden" name="statusTitle" value="">
		<input type="hidden" name="WriteAssessmentUserName" value="">
		<input type="hidden" name="WriteAssessmentUserCode" value="">
		<input type="hidden" name="addAndMinusTimeEnd" value="">
		<input type="hidden" name="addAndMinusTime" value="">
		<input type="hidden" name="rercordGroupScore" value="">
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2" menuIcon>&#xe7b3;</i>
				<span>导出预览</span>
			</span>
			<div class="head0_right fr">
				<div class="top top_div">
					<button type="button" class="layui-btn layui-btn-sm fr" onclick="doctorShow.exportWord();">导出</button>
				</div>
			</div>
		</div>
		<div class="body-style">
			<div class="layui-tab lr_box ">
				<div id="wordContent" class="wordContent">
				
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins//formSelects/formSelects-v4.min.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorShow.js?r="+Math.random()></script>
</html>