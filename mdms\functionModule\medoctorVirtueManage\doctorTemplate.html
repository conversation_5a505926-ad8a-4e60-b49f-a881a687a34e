<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>考评模板</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/doctorTemplate.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" id="assessmentCode" name="assessmentCode" />
		<input type="hidden" name="approvalCode" />
		<input type="hidden" name="approvalStatus" />
		<input type="hidden" name="examType" id="examType" />
		<input type="hidden" name="multiDeptStatus" />
		<input type="hidden" name="fileListJson" id="fileListJson" />
		<input type="hidden" name="veify" />
		<input type="hidden" name="search" />
		<input type="hidden" name="assessPojectStatus" />
		<input type="hidden" name="assessPojectUserCode" />
		<div class="head0">
			<span class="head1_text fw700 layui-hide">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<input type="button" class="layui-btn layui-btn-sm layui-hide " value="提交审核" id="pushApproval" lay-submit lay-filter="saveApproval" />
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" id="oneKeyBtn" onclick="doctorTemplate.oneKeyBtn()" value="一键总分" />
				&nbsp;
				<input type="button" class="layui-btn layui-btn-sm" name="pushBtns" value="草稿" id="pushBtns" lay-submit lay-filter="save" />
				&nbsp;
				<input type="button" class="layui-btn layui-btn-sm" name="pushBtn" value="提交" id="pushBtn" lay-submit lay-filter="save" />
			</div>
			<div class="showbody">
				<legend  style="font-size: 15px; font-weight: 400;" id="showPlanName"></legend>
				<div class="bodyOneDiv">
					<div style="width:20%;">
					<label class="showLabel">
						考评状态：<span name="nowAssessmentStatus" class="showSpan"></span>
					</label>	
					</div>
					<div style="width:20%;">
					<label class="showLabel">
						个人状态：<span name="assessmentStatus" class="showSpan"></span>
					</label>	
					</div>
					<div style="width:20%;">
					<label class="showLabel">
						期间：<span name="assessmentTerm" class="showSpan"></span>
					</label>
					</div>
					<div style="width:40%;">
					<label class="showLabel">
						考评时间：<span name="assessmentValidity" class="showSpan"></span>
					</label>
					</div>
				</div>
				<div class="bodyTowDiv">
					<div style="width:20%;">
					<label class="showLabel">
						类型：<span class="showSpan" id="showType"></span>
					</label>
					</div>
					<div style="width:20%;">	
					<label class="showLabel">
						项目：<span id="showItemNum" class="showSpan">0</span> 项
					</label>
					</div>
					<div style="width:20%;">
					<label class="showLabel">
						总分：<span id="assessmentSumScore" class="showSpan">0</span>
					</label>
					</div>
					<div style="width:40%;">
					<label class="showLabel">
						合格分：<span id="assessmentPassScore" class="showSpan">0</span>
					</label>
					</div>
				</div>
			</div>
		</div>
		<div class="tree_custom_opt layui-hide" data-tag="1">
			<i class="layui-icon2">&#xe730;</i>
		</div>
		<div class="bodys">
			<div class="showRDiv" id="contentDiv" style="font-size: 18px;">
				<div class="layui-form-item layui-hide" id="veifyDiv">
					<label class="layui-form-label">自评人：</label>
					<div class="layui-input-inline">
						<input type="text" id="userName" name="userName" readonly="readonly" class="layui-input showReadOnly" style="border: 0px;"/>
						<input type="text" id="userCode" name="userCode" class="layui-input layui-hide" />
					</div>
					<label class="layui-form-label layui-required"> 审批时间 </label>
					<div class="layui-input-inline">
						<input type="text" id="recordDate" name="registeViefyrWriteDate" lay-verify="required" readonly="readonly" autocomplete="off" class="layui-input" />
					</div>
				</div>
				<div id="showItems"></div>
			</div>
			<fieldset id="fileDiv" class="layui-elem-field layui-hide">
				<legend>附件上传</legend>
				<div class="layui-field-box">
					<div class="layui-form-item">
						<label class="layui-form-label"> </label>
						<div class="layui-input-inline">
							<input type="button" value="上传附件" id="uploadBtn" class="layui-btn layui-btn-sm" onclick="pubUploader.openFiles(doctorTemplate.attaCallback);param.set('fileIndex', 0);" />
							<div class="collapse in">
								<ul class="cattachqueue" id="ueditorFileDiv-0"></ul>
							</div>
						</div>
					</div>
				</div>
			</fieldset>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorTemplate.js?r="+Math.random()></script>
<!-- 富文本、上传组件  - 直接拷贝到项目中 -->
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.config.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/lang/zh-cn/zh-cn.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<!-- 自定义详情组件 -->
<script type="text/javascript" src="../../../frame/customDetail/js/initCustomDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="../../plugins/moment/js/moment-2.24.0.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		//加载时间选择器 - 更多请查看官网
		var layDate = layui.laydate;
		layDate.render({
			elem : '#recordDate',
			min : doctorTemplate.getNowFormatDate(),
			value : new Date()
		});
		doctorTemplate.init();
	});
	$(window).load(function() {
		// 2023/1/30 zh 显示考评的加减分
		doctorTemplate.echoAddAndRemove();
	})
</script>