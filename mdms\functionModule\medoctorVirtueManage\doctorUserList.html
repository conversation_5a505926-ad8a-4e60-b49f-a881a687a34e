<!DOCTYPE html>
<html>
<head>
<title>医师自评列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
<link rel="stylesheet" type="text/css" href="css/doctorUserList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="state">
		<input type="hidden" name="examType">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<div class="layui-input-inline">
					<select name="compNo" lay-filter="compNo"></select>
					<input type="hidden" name="compNo" />
				</div>
				<div class="layui-input-inline">
					<select id="deptID" name="deptID" lay-filter="dept"></select>
				</div>
			</div>
			<div class="layui-input-inline h28 lh28" style="left:20px;">
				<input type="text" class="layui-input" autocomplete="off" placeholder="考评名称" id="title" name="title">
			</div>
			<div class="layui-input-inline h28 lh28" style="left:30px;">
				<input type="button"  class="layui-btn layui-btn-sm" onclick="doctorUserList.btnSearch()" value="查询"></input>
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam"></form>
		<div class="layui-row">
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorUserList.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		doctorUserList.init();
	});
	
</script>
</html>