var addAndMinusDictEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		addAndMinusDictEdit.getAddAndMinusDict();
		addAndMinusDictEdit.getOrgDeptList();
		addAndMinusDictEdit.selectRadio();
		addAndMinusDictEdit.initLayui();
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			addAndMinusDictEdit.saveAddAndMinusDict();
			return false;
		});
		
		layui.form.on("radio(addAndMinusDictType)", function() {
			addAndMinusDictEdit.selectRadio();
		});
		layui.form.on("radio(isFixPoint)", function(data) {
			var value = data.value;
			if (value == 0) {
				$("#hidePoint").addClass("layui-hide");
				$("#fixPoint").attr("lay-verify", "");
				$("#fixPoint").val("0");
			} else {
				$("#hidePoint").removeClass("layui-hide");
				$("#fixPoint").attr("lay-verify", "required|number|limit|double");
				if ($("#fixPoint").val() == 0) {
					$("#fixPoint").val("");
				}
			}
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	getAddAndMinusDict : function() {
		return $.ajax({
			url : basePath + "mdms/meaddAndMinusDict/getAddAndMinusDict.spring",
			data : {
				addAndMinusDictId : param.get("addAndMinusDictId")
			}
		}).then(function(data) {
			param.set(null, data.addAndMinusDict);
			return data;
		});
	},
	saveAddAndMinusDict : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/meaddAndMinusDict/saveAddAndMinusDict.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.addAndMinusDictList.getAddAndMinusDictPager();
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},
	// 科室架构
	getOrgDeptList : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			data : {
				"funCode" : param.get("funCode"),
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			async : false,
			skipDataCheck : true, //- 如果接口响应回来的数据有问题，请增加该参数
			success : function(data) {
				var orgDeptList = data == undefined ? [] : data.deptList;
				if (orgDeptList.length > 0) {
					$("#fitDeptId").empty();
					var deptID = param.get("fitDeptId");
					for (var i = 0; i < orgDeptList.length; i++) {
						var dept = orgDeptList[i];
						var selected = deptID == dept.DeptID ? "selected" : "";
						$("#fitDeptId").append('<option ' + selected + '  value="' + dept.DeptID + '" >' + dept.compName + "-" + dept.DeptName + '</option>');
					}
					
				}
			}
		})
	},
	selectRadio : function() {
		var addAndMinusDictType = param.get("addAndMinusDictType");//获取加减类型，修改最大分值为正负
		if (addAndMinusDictType == 0) {
			$("#addtype").text("最大值+");
		} else {
			$("#addtype").text("最小值-");
		}
	}
}