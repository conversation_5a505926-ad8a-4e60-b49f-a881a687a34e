var addAndMinusDictList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		addAndMinusDictList.addAndMinusDictListInit().then(function(data) {
			addAndMinusDictList.getAddAndMinusDictPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export",
				onclick : addAndMinusDictList.exportList
			} ];
			filterSearch.init(basePath, addAndMinusDictList.getFilterParams(data), addAndMinusDictList.getAddAndMinusDictPager, customBtnDom);
			addAndMinusDictList.initLayuiForm();
			$("#keyword").attr("onkeydown", "if(event.keyCode == 13){return false;}");
		});
	},
	addAndMinusDictListInit : function() {
		return $.ajax({
			url : basePath + "mdms/meaddAndMinusDict/addAndMinusDictListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			addAndMinusDictList.getAddAndMinusDictPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "加减分内容,备注",
			title : "关键字",
			id : "keyword"
		} ];
		return params;
	},
	getAddAndMinusDictPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditAddAndMinusDict"></i>';
				html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteAddAndMinusDict"></i>';
				return html;
			}
		}, {
			title : '适用人员',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.isMedical);
			}
		}, {
			title : '类型',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.addAndMinusDictType);
			}
		}, {
			title : '内容',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.addAndMinusDictContent);
			}
		}, {
			title : '最大/小值',
			align : "center",
			width : 120,
			templet : function(d) {
				var tempHtml = '';
				
				if (d.addAndMinusDictType == "加分") {
					tempHtml += '<span style="color:green;">' + d.maxPoint + '</span>';
				} else {
					tempHtml += '<span style="color:red;">' + d.maxPoint + '</span>';
				}
				return tempHtml;
			}
		}, {
			title : '排序号',
			align : "center",
			width : 80,
			templet : function(d) {
				return assemblys.htmlEncode(d.seq);
			}
		}, {
			title : '适用科室',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.fitDeptId);
			}
		}, {
			title : '备注',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.addAndMinusDictRemark);
			}
		}, {
			title : '状态',
			align : "left",
			width : 80,
			templet : function(d) {
				return assemblys.htmlEncode(d.status);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/meaddAndMinusDict/getAddAndMinusDictPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditAddAndMinusDict : addAndMinusDictList.toEditAddAndMinusDict,
				deleteAddAndMinusDict : addAndMinusDictList.deleteAddAndMinusDict
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/meaddAndMinusDict/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditAddAndMinusDict : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditAddAndMinusDict",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "addAndMinusDictEdit.html?funCode=" + param.get("funCode") + "&addAndMinusDictId=" + d.addAndMinusDictId
		});
	},
	deleteAddAndMinusDict : function(d) {
		assemblys.confirm("确认删除吗?", function() {
			return $.ajax({
				url : basePath + "mdms/meaddAndMinusDict/deleteAddAndMinusDict.spring",
				type : "post",
				data : {
					addAndMinusDictId : d.addAndMinusDictId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					addAndMinusDictList.getAddAndMinusDictPager();
				});
				return data;
			});
		})
	}
}