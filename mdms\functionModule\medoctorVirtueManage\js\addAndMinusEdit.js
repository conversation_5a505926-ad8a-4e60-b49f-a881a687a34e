var addAndMinusEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		addAndMinusEdit.initAddOrMinus(param.get("addType"), param.get("isMedical"));
		addAndMinusEdit.getUsers(param.get("isMedical"));
		addAndMinusEdit.getAddAndMinus();
		addAndMinusEdit.initLayui();
	},
	initLayui : function() {
		layui.form.on("submit(save)", function() {
			addAndMinusEdit.saveAddAndMinus();
			return false;
		});
		
		layui.form.on("radio(isMedical)", function(data) {
			var val1 = data.value;
			var val2 = param.get("addType");
			//监听是否医务人员，动态控制"加减分项目"中内容
			addAndMinusEdit.initAddOrMinus(val2, val1);
			addAndMinusEdit.getUsers(val1);
			return false;
		});
		layui.form.on("select(partyCode)", function(data) {
			var name = $("#partyCode").find("option:selected").attr("attrname");
			param.set("partyName", name);
			return false;
		});
		layui.form.on("select(dictCode)", function(data) {
			var isFixPoint = $("#dictCode").find("option:selected").attr("isFixPoint");
			var fixPoint = $("#dictCode").find("option:selected").attr("fixPoint");
			$("input[name='isFixPoint']").removeAttr("disabled");
			if (isFixPoint == 1) {
				$("#point").val(fixPoint);
				$("#point").attr("readonly", "readonly");
				$("#point").css("background-color", "#f1f1f1");
			} else {
				$("#point").removeAttr("readonly");
				$("#point").css("background-color", "white");
			}
			param.set("isFixPoint", isFixPoint);
			$("input[name='isFixPoint']").attr("disabled", true);
			return false;
		});
		layui.form.on("radio(addType)", function(data) {
			var val1 = data.value;
			var val2 = param.get("isMedical");
			var temp = "";
			//监听类型，动态控制"分数+"和"分数-"的切换
			if (val1 == 0) {
				temp += '<span style="color: red;">*</span>分值+';
				$("#pointText").empty();
				$("#pointText").append(temp);
			} else {
				temp += '<span style="color: red;">*</span>分值-';
				$("#pointText").empty();
				$("#pointText").append(temp);
			}
			//监听类型，动态控制"加减分项目"中内容
			addAndMinusEdit.initAddOrMinus(val1, val2);
			return false;
		});
		
		var beginTime = layui.laydate.render({
			elem : "#getDate",
			trigger : "click",
			type : "datetime",
			format : "yyyy-MM-dd HH:mm",
			done : function(value, date) {
				param.set("getDate", value);
			}
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "datetime",
				max : 'today',
				format : "yyyy-MM-dd HH:mm"
			});
		});
		layui.form.render();
	},
	getAddAndMinus : function() {
		return $.ajax({
			url : basePath + "mdms/meaddAndMinus/getAddAndMinus.spring",
			data : {
				addAndMinusId : param.get("addAndMinusId")
			}
		}).then(function(data) {
			
			param.set(null, data.addAndMinus);
			if (data.addAndMinus != undefined) {
				param.set("dictCode", data.addAndMinus.dictCode);
				param.set("point", data.addAndMinus.point);
			}
			addAndMinusEdit.initTypeFile(data.fileList);
			
			// 2023/06/27 15:50:57 zh 查看详情屏蔽附件上次
			if (param.get("addAndMinusId") != 0) {
				$(".btnfile").addClass("layui-hide");
			}
			return data;
		});
	},
	saveAddAndMinus : function() {
		if (param.get("dictCode") == "") {
			assemblys.alert("加减分项目不能为空！");
			return false;
		}
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		
		var addAndMinusFileList = [];
		$("#ueditorFileDiv-0").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			addAndMinusFileList.push(typeFiles);
		});
		var addAndMinusFileListJson = JSON.stringify(addAndMinusFileList);
		$("#addAndMinusFileListJson").val(addAndMinusFileListJson);
		$("input[name='isFixPoint']").removeAttr("disabled");
		return $.ajax({
			url : basePath + "mdms/meaddAndMinus/saveAddAndMinus.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.addAndMinusList.getAddAndMinusPager();
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},
	// 初始化处方列别
	initAddOrMinus : function(addType, isMedical) {
		$.ajax({
			url : basePath + "/mdms/meaddAndMinus/getAddOrMinus.spring",
			type : "get",
			data : {
				addType : addType,
				isMedical : isMedical
			},
			dataType : "json",
			skipDataCheck : true,
			async : false,
			success : function(data) {
				var length = data.data.dictList.length;
				//hwx 2023-7-3 有没有加分项都清除
				$("#dictCode").empty();
				if (length > 0) {
					var htmlTemp = "";
					for (var i = 0; i < length; i++) {
						var temp = data.data.dictList[i];
						htmlTemp += "<option isFixPoint='" + temp.isFixPoint + "' fixPoint='" + temp.fixPoint + "' value='" + temp.addAndMinusDictCode + "' >" + temp.addAndMinusDictContent + "</option>";
					}
					$("#dictCode").append(htmlTemp);
				}
			}
		});
		layui.form.render();
	},
	getUsers : function(isMedical) {
		$.ajax({
			url : basePath + "/mdms/meaddAndMinus/getUsers.spring",
			type : "get",
			data : {
				isMedical : isMedical
			},
			dataType : "json",
			skipDataCheck : true,
			async : false,
			success : function(data) {
				var length = data.data.userList.length;
				$("#partyCode").empty();
				if (length > 0) {
					var htmlTemp = "<option value='' >请选择</option>";
					for (var i = 0; i < length; i++) {
						var temp = data.data.userList[i];
						htmlTemp += "<option  value='" + temp.userCode + "' attrname='" + temp.userName + "' >" + temp.userName + "(" + temp.userCode + ")" + "</option>";
					}
					$("#partyCode").append(htmlTemp);
				}
			}
		});
		layui.form.render();
	},
	attaCallback : function(result) {// 自定义上传图片后的回调
		var fileHtml = "";
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		for (var i = 0; i < result.length; i++) {
			fileHtml += "<li style='width: 500px;'>";
			fileHtml += "	<em title=\"" + result[i].title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + result[i].title + "&nbsp;&nbsp;" + result[i].size + "</em>";
			var suffix = result[i].type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" id=\"preview\" onclick=\"pubUploader.preview('" + result[i].title + "','" + result[i].url + "');\"  >预览图片</a></span>";
			}
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + result[i].title + "','" + result[i].url + "');\">下载</a></span>";
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove attaDelete\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a></span>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + result[i].title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + result[i].url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + result[i].size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + result[i].type + "\"/>";
			fileHtml += "</li>";
		}
		$("#ueditorFileDiv-0").append(fileHtml);
		if (param.get("showOrEdit") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
	},
	initTypeFile : function(meetingRecordFileList) {
		var filesData = meetingRecordFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.title = typeFileTemp.attaName;
				files.url = typeFileTemp.attaUrl;
				files.size = typeFileTemp.attaSize;
				files.type = typeFileTemp.attaType;
				result.push(files);
			}
			addAndMinusEdit.attaCallback(result);
		}
	},
	getDictRemark : function(dictCode) {
		return $.ajax({
			url : basePath + "mdms/meaddAndMinus/getDictRemark.spring",
			data : {
				dictCode : dictCode
			}
		}).then(function(data) {
			return data;
		});
	}
}