var addAndMinusList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		addAndMinusList.addAndMinusListInit().then(function(data) {
			addAndMinusList.initDate();
			addAndMinusList.getAddAndMinusPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export",
				onclick : addAndMinusList.exportList
			} ];
			filterSearch.init(basePath, addAndMinusList.getFilterParams(data), addAndMinusList.getAddAndMinusPager, customBtnDom);
			$("#filter").addClass("layui-hide");
			$("div[class='layui-input-block mgl86']").remove();
			addAndMinusList.initLayuiForm();
			if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_ADDMINUSSEARCH) {
				$("#addbtn").addClass("layui-hide");
			}
		});
	},
	addAndMinusListInit : function() {
		return $.ajax({
			url : basePath + "mdms/meaddAndMinus/addAndMinusListInit.spring"
		}).then(function(data) {
			
			param.set("hasAddRight", data.hasAddRight);
			param.set("hasEditRight", data.hasEditRight);
			param.set("hasDelRight", data.hasDelRight);
			if (data.hasAddRight == true) {
				$("#addbtn").removeClass("layui-hide");
			}
			return data;
		});
	},
	initDate : function() {
		var index = layui.laydate.render({
			elem : '#assessmentValidityID',
			range : "~",
			type : 'datetime',
			trigger : 'click', // 采用click弹出
			ready : function(date) {
				$("#layui-laydate2").css("width", "auto");
			}
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			addAndMinusList.getAddAndMinusPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "医师名称,加减分项目,医师工号",
			title : "关键字"
		} ];
		return params;
	},
	getAddAndMinusPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 100,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_delete" title="查看" lay-event="toShowAddAndMinus"></i>';
				if (param.get("hasEditRight") == 'true' && param.get("funCode") != assemblys.top.mdms.mdmsConstant.MDMS_ME_ADDMINUSSEARCH) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditAddAndMinus"></i>';
				}
				if (param.get("hasDelRight") == 'true' && param.get("funCode") != assemblys.top.mdms.mdmsConstant.MDMS_ME_ADDMINUSSEARCH) {
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteAddAndMinus"></i>';
				}
				
				return html;
			}
		}, {
			title : '适用人员',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.isMedical);
			}
		}, {
			title : '医师名称',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.htmlEncode(d.partyName);
			}
		}, {
			title : '加减分项目',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.addAndMinusDictContent);
			}
		}, {
			title : '分值',
			align : "center",
			width : 60,
			templet : function(d) {
				var tempHtml = '';
				if (d.addType == "0") {
					tempHtml += '<span style="color:green;">' + d.point + '</span>';
				} else {
					tempHtml += '<span style="color:red;">' + d.point + '</span>';
				}
				return tempHtml;
			}
		}, {
			title : '获得时间',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.dateToStr(d.getDate);
			}
		} ];
		
		if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_ADDMINUSSEARCH) {
			cols.push({
				title : '创建人',
				align : "center",
				width : 150,
				templet : function(d) {
					return assemblys.htmlEncode(d.optUserCode + " / " + d.UserName);
				}
			});
		}
		
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/meaddAndMinus/getAddAndMinusPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditAddAndMinus : addAndMinusList.toEditAddAndMinus,
				toShowAddAndMinus : addAndMinusList.toShowAddAndMinus,
				deleteAddAndMinus : addAndMinusList.deleteAddAndMinus
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/meaddAndMinus/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toShowAddAndMinus : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toShowAddAndMinus",
			area : [ '80%', '80%' ],
			title : false,
			scrollbar : false,
			content : "addAndMinusDetail.html?funCode=" + param.get("funCode") + "&addAndMinusId=" + d.addAndMinusId
		});
	},
	toEditAddAndMinus : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditAddAndMinus",
			area : [ '80%', '80%' ],
			title : false,
			scrollbar : false,
			content : "addAndMinusEdit.html?funCode=" + param.get("funCode") + "&addAndMinusId=" + d.addAndMinusId
		});
	},
	deleteAddAndMinus : function(d) {
		assemblys.confirm("确认删除吗?", function() {
			return $.ajax({
				url : basePath + "mdms/meaddAndMinus/deleteAddAndMinus.spring",
				type : "post",
				data : {
					addAndMinusId : d.addAndMinusId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					addAndMinusList.getAddAndMinusPager();
				});
				return data;
			});
		})
	}
}