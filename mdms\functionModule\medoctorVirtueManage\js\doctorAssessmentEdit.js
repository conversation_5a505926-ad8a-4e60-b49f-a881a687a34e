var doctorAssessmentEdit = {
	users : null,
	depts : null,
	userOptions : null,
	deptOptions : null,
	hasRight : false,
	hasShowTempLate : false,
	doctorAssessPoject : null,
	num : 0,
	arrs : new Array(),
	notArrs : new Array(),
	whetherArrs : new Array(),
	objArrs : new Array(),
	tempStartDate : "",
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		//获取延长时间权限
		doctorAssessmentEdit.getHasRight();
		//获取导入增删人员Excel、模板下载权限
		doctorAssessmentEdit.getShowTempLate();
		// 2023/1/18 zh 获取范围
		doctorAssessmentEdit.assessmentScope().then(function(){
			// 2023/1/18 zh 获取频次
			return doctorAssessmentEdit.assessmentEvalFre();
		}).then(function(){
			// 2023/1/18 zh 获取方式
			return doctorAssessmentEdit.assessmentMode();
		}).then(function(data){
			doctorAssessmentEdit.initLayui();
			doctorAssessmentEdit.initTable();
			doctorAssessmentEdit.initDate();
			if (param.get("assessmentStatus") == 0 && param.get("assessmentStatus") != "") {
				$("input[lay-filter=save]").val("修改");
				$("input[lay-filter=saveIsseus]").val("修改并发布");
			}
			if (param.get("assessmentStatus") == 2) {
				$("input[lay-filter=save]").val("保存");
				$("input[lay-filter=saveIsseus]").val("保存并发布");
			}
			$("select[name=assessmentFrequency] option").each(function(i,e){
				if($(e).attr("selected") == "selected"){
					doctorAssessmentEdit.initCutDate(undefined,undefined,{value : $(e).attr("value")})
				}
			})
			if (param.get("search") || param.get("edit") || param.get("export")) {
				doctorAssessmentEdit.getDoctorAssessment();
				layui.form.render();
				if (param.get("search")) {
					doctorAssessmentEdit.disabledDom("input[name=assessmentTerm]")
					$("input[addBtn]").remove()
					$("input[lay-filter=save]").remove()
					$("input[lay-filter=saveIsseus]").remove()
					// 2023/2/15 zh 考评项目设置
					doctorAssessmentEdit.addItem(doctorAssessmentEdit.doctorAssessPoject.doctorPoject,1);
				}
				layui.formSelects.render('assessPojectApprovalSelect');
				
				if(param.get("search")){
					layui.formSelects.disabled("assessPojectApprovalSelect");
				}
				layui.formSelects.value("assessPojectApprovalSelect", doctorAssessmentEdit.doctorAssessPoject.assessPojectApprovalUserCode)
				
				// 2023/1/19 zh 设置灰色
				$("#assessPojectApprovalSelectSelectID").next().find(".xm-select-label").find("span").css({
					"background":"#C2C2C2",
					"border-color" : "#C2C2C2"
				})
			}
			// 隐藏保存和保存发布按钮
			if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE) {
				$("input[lay-filter=save]").addClass("layui-hide");
				$("input[lay-filter=saveIsseus]").addClass("layui-hide");
				$("input[name=assessmentStatus]").val(2);
			}
			
			// 隐藏保存模板按钮
			if (param.get("funCode") != assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE) {
				$("input[lay-filter=saveTemplate]").addClass("layui-hide");
			}
			
			$("input[name=assessmentSumScore]").val(doctorAssessmentEdit.num)
			$("#test1").removeAttr("lay-verify")
			$("input[name=select]").addClass("layui-input")
			$("input[name=select]").attr("lay-verify", "required")
			
			// zh-2022-11-9 14:03【添加默认考评频次初始化进行联动考评方式】
			$("select[name=assessmentMode]").next().find("dd").each(function(index,elem){
				if(typeof($(elem).attr("class")) != "undefined" && $(elem).attr("class") != "" && $(elem).attr("class").indexOf("layui-this") >= 0){
					doctorAssessmentEdit.click("assessmentMode","assessmentFrequency",{value:$(elem).attr("lay-value")});
				};
			});
			
			doctorAssessmentEdit.disabledDom("input[name=assessmentSumScore]")
			
			// 2023/1/12 zh 监听考评方式
			doctorAssessmentEdit.listenAassessmentMode();

			// 2023/1/19 zh 监听考评频次
			doctorAssessmentEdit.listenAssessmentFre();
			
			return data;
		}).then(function(data){
			// 2023/2/13 zh 判断是否为科室考评
			if(param.get("assessmentDeptStatus") == 1){
				$("select[name=assessmentApprovalScope]").parent().parent().addClass("layui-hide");
				$("#assessPojectApprovalSelectSelectID").removeAttr("lay-verify");
				$("#assessPojectApprovalSelectSelectID").next().find(".xm-hide-input").removeAttr("lay-verify");
			}
		}).then(function(data){
			// 2023/2/27 zh 是否有补考人员
			return doctorAssessmentEdit.getDoctorAssessmentMakeUp();
			 
		}).then(function(data){
			if(param.get("search")){
				if(data && data.data && data.data.doctorAssessmentMakeUp){
					var doctorAssessmentMakeUp =  data.data.doctorAssessmentMakeUp ;	
					if(doctorAssessmentMakeUp.length > 0){
						$(".makeUp").removeClass("layui-hide");
					}
				}
			}
		}).then(function(){
			if(param.get("funCode") != assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE){
				if(param.get("assessmentStatus")!=0){//未发布屏蔽按钮
					if(param.get("search") && doctorAssessmentEdit.hasRight){
						$("#saveTime").removeClass("layui-hide");
					}
					/*if(param.get("search") && doctorAssessmentEdit.hasRight){
						$("#levelExplatin").removeClass("layui-hide");
					}*/
					
					if(param.get("search") && doctorAssessmentEdit.hasShowTempLate){
						$("#uploadExcel").removeClass("layui-hide");
					}
					
					if(param.get("search") && doctorAssessmentEdit.hasShowTempLate){
						$("#tempLate").removeClass("layui-hide");
					}
				}else{
					if(param.get("search")) {
						$("#assessmentValidityID").attr("disabled",true);
						$("#assessmentValidityID").addClass("layui-disabled");
					}
				}
			}else{
				if(param.get("search")) {
					$("input[lay-filter=saveTemplate]").addClass("layui-hide");
					$("#assessmentValidityID").attr("disabled",true);
					$("#assessmentValidityID").addClass("layui-disabled");
				}
			}
		}).then(function(){
			//导入考评计划增删人员方法
			doctorAssessmentEdit.importAddAndDel();
		})
		
		// 2023/2/8 cjl 鼠标移动至问号图标上显示的提醒
		doctorAssessmentEdit.explatinWarn();
		
	},
	getHasRight : function(){
		$.ajax({
			url : basePath + "mdms/medoctorAssessment/getHasRight.spring?",
			type : "get",
			dataType : "json",
			async : false,
			success : function(data) {
				var hasAdd = data.hasAdd;
				doctorAssessmentEdit.hasRight = hasAdd;
			}
		})
	},
	getShowTempLate : function(){
		$.ajax({
			url : basePath + "mdms/medoctorAssessment/getShowTempLate.spring?",
			type : "get",
			dataType : 'json',
			async : false,
			success : function(data) {
				doctorAssessmentEdit.hasShowTempLate = data.hasShowTempLate;
			}
		})
	},
	explatinWarn : function() {
		$("i[name=levelExplatin]").hover(function() {
			assemblys.tips($("i[name=levelExplatin]"), "延长计划时间：当您在延长考评计划时，应选择一个时间范围。如：当某个已过期的考评计划时间为 2008-10-01 09:00:00 ~ 2008-10-10 23:59:59，在延长该计划时间时应现选择 2008-10-01 09:00:00 ~ xxxx-xx-xx xx:xx:xx。延长计划开始时间是原计划开始时间：2008-10-01 09:00:00，延长计划结束时间则是您选择的结束时间：xxxx-xx-xx xx:xx:xx", 2000, "right");
		}, function() {
			layer.closeAll('tips');
		})
	},
	initDate : function() {
		var index = layui.laydate.render({
			elem : '#assessmentValidityID',
			range : "~",
			type : 'datetime',
			trigger : 'click', // 采用click弹出
			//min : layui.util.toDateString(new Date(), "yyyy-MM-DD HH:mm:ss"),
			ready : function(date) {
				$("#layui-laydate2").css("width", "auto");
			}
		});
		var index = layui.laydate.render({
			elem : '#addAndMinusTimeID',
			range : "~",
			type : 'datetime',
			trigger : 'click', // 采用click弹出
			//min : layui.util.toDateString(new Date(), "yyyy-MM-DD HH:mm:ss"),
			ready : function(date) {
				$("#layui-laydate2").css("width", "auto");
			}
		})
	},
	initTable : function() {
		var common = '<colgroup><col width="30"><col width="200"><col width="380"><col width="80"></colgroup>'
		common += '<thead><tr><th style="text-align: center;">操作</th><th style="text-align: center;">考评项目类型'
		common += '</th><th style="text-align: center;"> 考评项目名称</th> <th style="text-align: center;">'
		common += '考评项目总分</th></tr></thead>'
		$(".addAssessment").append(common)
		
		// 2023/2/16 zh 小组设置项目
		$(".addGroupAssess").append(common)

	},
	user : function(val) {
		var users
		$.ajax({
			url : basePath + "mdms/medoctorAssessment/getDoctorUsers.spring?compNo=" + param.get("compNo") + "&assessmentIfMedical=" + val,
			//url : basePath + "mdms/survey/getUsers.spring?compNo=" + param.get("compNo"),
			type : "get",
			dataType : "json",
			async : false,
			success : function(data) {
				users = data.users
			}
		})
		return users;
	},
	dept : function() {
		var depts
		$.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			type : "get",
			data : {
				"compNo" : param.get("compNo"),
				"funCode" : param.get("funCode")
			},
			dataType : "json",
			async : false,
			skipDataCheck : true, // - 如果接口响应回来的数据有问题，请增加该参数,
			success : function(data) {
				depts = data.deptList
			}
		})
		return depts;
	},
	assessmentScope : function() {
		return doctorAssessmentEdit.getDict(assemblys.top.mdms.mdmsConstant.DOCTORVIRTUESCOPE).then(function(data){
			if(data.dictList){
				for (var i = 0; i < data.dictList.length; i++) {
					$("select[name=assessmentScope]").append('<option value="' + data.dictList[i].dictCode + '">' + data.dictList[i].dictName + '</option>')
					
					if(i == 0){
						if (data.dictList[i].dictCode == assemblys.top.mdms.mdmsConstant.DOCTOR_USER) {
							$("label[customLabel]").html("选择医师")
							$("select[customSelect]").attr("lay-verify", "required");
							doctorAssessmentEdit.users = doctorAssessmentEdit.user(param.get("assessmentIfMedical"));
							doctorAssessmentEdit.userOptions = doctorAssessmentEdit.users.map(function(item) {
								return {
									name : item.UserName,
									value : item.UserCode
								}
							})

							xmSelect.render({
								el : '#demo1',
								language : 'zn',
								theme : {
									color : '#0081ff',
								},
								paging : true,
								pageSize : 20,
								toolbar : {
									show : true,
								},
								filterable : true,
								data : doctorAssessmentEdit.userOptions
							})

						} else if (data.dictList[i].dictCode == assemblys.top.mdms.mdmsConstant.DOCTOR_DEPT) {
							$("label[customLabel]").text("选择科室")
							$("select[customSelect]").attr("lay-verify", "required");
							doctorAssessmentEdit.depts = doctorAssessmentEdit.dept();
							doctorAssessmentEdit.deptOptions = doctorAssessmentEdit.depts.map(function(item) {
								return {
									name : item.DeptName,
									value : item.DeptID
								}
							})
							xmSelect.render({
								el : '#demo1',
								language : 'zn',
								theme : {
									color : '#0081ff',
								},
								paging : true,
								pageSize : 20,
								toolbar : {
									show : true,
								},
								filterable : true,
								data : doctorAssessmentEdit.deptOptions
							})
							$("#customSelectID").append(doctorAssessmentEdit.deptOptions)
						} else {
							$("select[customSelect]").removeAttr("lay-verify");
							$("label[customLabel]").hide()
							$("div[customDiv]").hide()
							$("input[name=interest]").removeAttr("lay-verify")
						}
							
						 // 默认激发选项框
						$('select[name="assessmentScope"]').siblings("div.layui-form-select").find('dl dd[lay-value=' + data.dictList[i].dictCode + ']').click();
					}
				}
			}
		});
	},
	assessmentEvalFre : function(){
		return doctorAssessmentEdit.getDict(assemblys.top.mdms.mdmsConstant.DOCTOREVALUATIONFRE).then(function(data){
			if(data.dictList){
				for (var i = 0; i < data.dictList.length; i++) {
					if (i == 0) {
						if (data.dictList[i].dictCode == assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY) {
							layui.laydate.render({
								elem : '#assessmentTermID',
								type : 'month',
								format : 'M月',
								min : layui.util.toDateString(new Date(), "MM"),
								max : new Date().setMonth(11)
							});
						} else if (data.dictList[i].dictCode == assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR) {
							layui.laydate.render({
								elem : '#assessmentTermID',
								type : 'year',
								format : 'y年',
								min : layui.util.toDateString(new Date(), "YYYY")
							});
						}
					}
					$("select[name=assessmentFrequency]").append('<option value="' + data.dictList[i].dictCode + '" value1="'+data.dictList[i].value1+'" >' + data.dictList[i].dictName + '</option>')
				}
			}
			layui.form.render();
		})
	},
	assessmentMode : function(){
		return doctorAssessmentEdit.getDict(assemblys.top.mdms.mdmsConstant.DOCTORMODE).then(function(data){
			if(data.dictList){
				for (var i = 0; i < data.dictList.length; i++) {
					
					// 2023/1/19 zh 更换为下拉框
					$("select[name=assessmentMode]").append('<option value="' + data.dictList[i].dictCode + '" value1="'+data.dictList[i].value1+'" >' + data.dictList[i].dictName + '</option>')
				}
			}
			layui.form.render();
		})
	},
	getDict : function(dictTypeCode){
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
				"dictTypeCode" : dictTypeCode
			},
			dataType : "json",
			skipDataCheck : true,
		});
	},
	initLayui : function() {
		var index = 0;
		layui.form.on("submit(save)", function(data) {
			/**
			 * 防止多次提交
			 */
			if (index == 0) {
				if ($(".addAssessment").find("tbody").find("td")[0]) {
					data.field.assessmentStatus = 0;
					doctorAssessmentEdit.saveDoctorAssessment(data);
					index += 1;
				} else {
					layer.msg('请添加考评项目');
				}
			} else {
				setTimeout(function() {
					layer.msg('请勿重复添加');
				}, 500);
			}
			return false;
		});
		
		layui.form.on("submit(saveIsseus)", function(data) {
			/**
			 * 防止多次提交
			 */
			if (index == 0 && $(".addAssessment").find("tbody").find("td")[0]) {
				data.field.assessmentStatus = 1
				doctorAssessmentEdit.saveDoctorAssessment(data);
				index += 1;
			} else {
				layer.msg('请添加考评项目');
			}
			return false;
		});
		
		//修改延长时间
		/*layui.form.on("submit(saveTime)", function(data) {
			*//**
			 * 防止多次提交
			 *//*
			var oldStartDate = doctorAssessmentEdit.tempStartDate;
			var assessmentValidity = data.field.assessmentValidity;
			var tempArr = assessmentValidity.split("~");
			if(new Date(tempArr[1]) > new Date(oldStartDate)){
				doctorAssessmentEdit.saveTime(tempArr[1]);
			}else{
				layer.msg('您选择的延长计划结束时间是：' + tempArr[1] + ',小于原计划开始时间：' + oldStartDate + ',请重新选择延长计划结束时间！');
			}
			return false;
		});*/
		
		layui.form.on("submit(saveTemplate)", function(data) {
			/**
			 * 防止多次提交
			 */
			if (index == 0 && $(".addAssessment").find("tbody").find("td")[0]) {
				data.field.assessmentStatus = 2;
				data.field.template = 3;
				doctorAssessmentEdit.saveDoctorAssessment(data);
				index += 1;
			} else {
				layer.msg('请添加考评项目');
			}
		});
		
		// 2023/2/28 zh 补考提交
		layui.form.on("submit(saveMakeUp)", function(data) {
			/**
			 * 防止多次提交
			 */
			if (index == 0) {
				if ($(".addAssessment").find("tbody").find("td")[0]) {
					data.field.makeUpStatus = 1;
					doctorAssessmentEdit.saveDoctorAssessment(data);
					index += 1;
				} else {
					layer.msg('请添加考评项目');
				}
			} else {
				setTimeout(function() {
					layer.msg('请勿重复添加');
				}, 500);
			}
			return false;
		});

		layui.form.on("select(assessmentFrequencyFilter)", function(data) {
			
			var commomediction = "" ; 
			
			if (data.value == assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR) {
				doctorAssessmentEdit.cutInputDate("#assessmentTermID", "div[assessmentTermDiv]", "year", "y年", "YYYY");
				
				commomediction  = assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR_ASSESSMENT;
			}
			
			if (data.value == assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY) {
				doctorAssessmentEdit.cutInputDate("#assessmentTermID", "div[assessmentTermDiv]", "month", "yyyy年M月", "MM");
				
				commomediction  = assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY_ASSESSMENT;
				
			}
			if (data.value == assemblys.top.mdms.mdmsConstant.DOCTOR_QUARTER) {
				doctorAssessmentEdit.cutInputDate("#assessmentTermID", "div[assessmentTermDiv]", "month", "yyyy年M季度", "MM");
				
				commomediction  = assemblys.top.mdms.mdmsConstant.DOCTOR_QUARTER_ASSESSMENT;
				
			}
			//zh-2022-11-9 13:37【点击年份和月份考评方式也选择为年份和月份】
			$("input[name=assessmentMode]").each(function(index,elem){
				if($(this).val() == commomediction){
					$(this).next().click();	
				}
			})
			
		});
		
		layui.form.on("radio(assessmentIfMedical)", function(data) {
			var val = data.value;
			doctorAssessmentEdit.user(val);

			doctorAssessmentEdit.refleshUser();
			
			
			// 2023/3/10 zh 非医务人员选项点击
			doctorAssessmentEdit.nonMedicalPersonnelRadio(val);
			
			
			if(param.get("assessmentScope") == assemblys.top.mdms.mdmsConstant.DOCTOR_ALL){
				$("#demo1").find(".xm-select-default").removeAttr("lay-verify");
			}else{
				$("#demo1").find(".xm-select-default").attr("lay-verify","required");
			}
			
			if(param.get("assessmentScope") == assemblys.top.mdms.mdmsConstant.DOCTOR_DEPT){
				$("label[customLabel]").text("选择科室").removeClass('layui-hide')
				$("#demo1").removeClass('layui-hide');
				$("select[customSelect]").attr("lay-verify", "required");
				
				if (param.get("edit")) {
					doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode = null
				}
				
				if (doctorAssessmentEdit.deptOptions == null) {
					doctorAssessmentEdit.depts = doctorAssessmentEdit.dept();
					doctorAssessmentEdit.deptOptions = doctorAssessmentEdit.depts.map(function(item) {
						return {
							name : item.DeptName,
							value : item.DeptID
						}
					})
				}
				
				if (doctorAssessmentEdit.doctorAssessPoject != null && doctorAssessmentEdit.doctorAssessPoject.assessPojectDeptCode != null) {
					
					if (param.get("search")) {
						doctorAssessmentEdit.deptOptions = doctorAssessmentEdit.depts.map(function(item) {
							return {
								name : item.DeptName,
								value : item.DeptID,
								disabled : true
							}
						})
					}
					
					xmSelect.render({
						el : '#demo1',
						language : 'zn',
						theme : {
							color : '#0081ff',
						},
						paging : true,
						pageSize : 20,
						toolbar : {
							show : true,
						},
						initValue : doctorAssessmentEdit.doctorAssessPoject.assessPojectDeptCode,
						filterable : true,
						data : doctorAssessmentEdit.deptOptions
					})
				} else {
					xmSelect.render({
						el : '#demo1',
						language : 'zn',
						theme : {
							color : '#0081ff',
						},
						paging : true,
						pageSize : 20,
						toolbar : {
							show : true,
						},
						filterable : true,
						data : doctorAssessmentEdit.deptOptions
					})
				}
				$("#customSelectID").empty()
				$("div[customDiv]").show()
				$("label[customLabel]").show()
			}
			
			return false;
		});

		layui.form.on("select(assessmentScope)", function(data) {
			// 2023-02-08 cjl 控制选择人员框后前的问号图标的显示与隐藏
			var val = data.value;
			if(val == assemblys.top.mdms.mdmsConstant.DOCTOR_USER){
				doctorAssessmentEdit.refleshUser();
				$("#demo1").find(".xm-select-default").attr("lay-verify","required");
				//$("i[name=levelExplatin]").removeClass("layui-hide");
			}else{
				$("i[name=levelExplatin]").addClass("layui-hide");
				$("#demo1").find(".xm-select-default").removeAttr("lay-verify");
			}
			
			var checked
			$.ajax({
				url : basePath + "mdms/medoctorAssessment/findAssessment.spring",
				data : {
					assessmentName : $("input[name=assessmentName]").val(),
					assessmentScope : data.value,
					assessmentID : $("input[name=assessmentID]").val(),
					assessmentStatus : $("input[name=assessmentStatus]").val()
				},
				async : false,
				success : function(data) {
					checked = data.checked
				}
			})

			if (param.get("search") == "") {
				if (checked) {
					$("input[name=assessmentName]").val("")
					assemblys.msg("名称已重复，请重新输入");
				}
			}
			
			if (data.value == assemblys.top.mdms.mdmsConstant.DOCTOR_DEPT) {
				$("label[customLabel]").text("选择科室").removeClass('layui-hide')
				$("#demo1").removeClass('layui-hide');
				$("select[customSelect]").attr("lay-verify", "required");
				
				if (param.get("edit")) {
					doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode = null
				}
				
				if (doctorAssessmentEdit.deptOptions == null) {
					doctorAssessmentEdit.depts = doctorAssessmentEdit.dept();
					doctorAssessmentEdit.deptOptions = doctorAssessmentEdit.depts.map(function(item) {
						return {
							name : item.DeptName,
							value : item.DeptID
						}
					})
				}
				
				if (doctorAssessmentEdit.doctorAssessPoject != null && doctorAssessmentEdit.doctorAssessPoject.assessPojectDeptCode != null) {
					
					if (param.get("search")) {
						doctorAssessmentEdit.deptOptions = doctorAssessmentEdit.depts.map(function(item) {
							return {
								name : item.DeptName,
								value : item.DeptID,
								disabled : true
							}
						})
					}
					
					xmSelect.render({
						el : '#demo1',
						language : 'zn',
						theme : {
							color : '#0081ff',
						},
						paging : true,
						pageSize : 20,
						toolbar : {
							show : true,
						},
						initValue : doctorAssessmentEdit.doctorAssessPoject.assessPojectDeptCode,
						filterable : true,
						data : doctorAssessmentEdit.deptOptions
					})
				} else {
					xmSelect.render({
						el : '#demo1',
						language : 'zn',
						theme : {
							color : '#0081ff',
						},
						paging : true,
						pageSize : 20,
						toolbar : {
							show : true,
						},
						filterable : true,
						data : doctorAssessmentEdit.deptOptions
					})
				}
				$("#customSelectID").empty()
				$("div[customDiv]").show()
				$("label[customLabel]").show()

			} else if (data.value == assemblys.top.mdms.mdmsConstant.DOCTOR_USER) {
				$("label[customLabel]").text("选择医师")
				$("select[customSelect]").attr("lay-verify", "required");
				
				//考评管理中  自评范围和适用人员  对选择人员的显示隐藏  cjl  2023-02-28
				$("label[customLabel]").text("选择医师").removeClass('layui-hide');
				$("#demo1").removeClass('layui-hide');
				
				if (doctorAssessmentEdit.userOptions == null) {
					doctorAssessmentEdit.users = doctorAssessmentEdit.user(param.get("assessmentIfMedical"));
					doctorAssessmentEdit.userOptions = doctorAssessmentEdit.users.map(function(item) {
						return {
							name : item.UserName,
							value : item.UserCode
						}
					})
				}
				
				if (doctorAssessmentEdit.doctorAssessPoject != null && doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode != null) {
					if (param.get("search")) {
						doctorAssessmentEdit.userOptions = doctorAssessmentEdit.users.map(function(item) {
							return {
								name : item.UserName,
								value : item.UserCode,
								disabled : true
							}
						})
					}
					
					// 2023/216 zh 全员范围再选择人员范围导致显示问题
					if(param.get("assessmentID") == ""){
						doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode = [];
					}
					
					xmSelect.render({
						el : '#demo1',
						language : 'zn',
						theme : {
							color : '#0081ff',
						},
						paging : true,
						pageSize : 20,
						toolbar : {
							show : true,
						},
						initValue : doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode,
						filterable : true,
						data : doctorAssessmentEdit.userOptions
					})
				} else {
					xmSelect.render({
						el : '#demo1',
						language : 'zn',
						theme : {
							color : '#0081ff',
						},
						paging : true,
						pageSize : 20,
						toolbar : {
							show : true,
						},
						filterable : true,
						data : doctorAssessmentEdit.userOptions
					})
				}
				$("#customSelectID").empty()
				$("div[customDiv]").show()
				$("label[customLabel]").show()

			} else {
				if (param.get("edit")) {
					if (doctorAssessmentEdit.doctorAssessPoject != null) {
						doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode = null
						doctorAssessmentEdit.doctorAssessPoject.assessPojectDeptCode = null
					}
				}
				$("label[customLabel]").hide()
				$("div[customDiv]").hide()
				$("input[name=interest]").removeAttr("lay-verify")
			}
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		layui.form.render();
	},
	getDoctorAssessment : function() {
		return $.ajax({
			url : basePath + "mdms/medoctorAssessment/getDoctorAssessment.spring",
			data : {
				assessmentCode : param.get("assessmentCode"),
				comanyCode : param.get("compNo"),
				assessmentID : param.get("assessmentID"),
				assessmentStatus : param.get("assessmentStatus")
			},
			async : false,
		}).then(function(data) {
			
			if(data.doctorAssessment.assessmentIfMedical == 1){
				
				if(data.doctorAssessment.doctorAssessPoject.doctorPoject.length > 0){
					doctorAssessmentEdit.whetherArrs = data.doctorAssessment.doctorAssessPoject.doctorPoject;
				}
				
			}else{
				
				if(data.doctorAssessment.doctorAssessPoject.doctorPoject.length > 0){
					doctorAssessmentEdit.notArrs = data.doctorAssessment.doctorAssessPoject.doctorPoject;
				}
				
			}

			var end = layui.util.toDateString(data.doctorAssessment.assessmentValidityEnd, "yyyy-MM-dd HH:mm:ss")
			var start = layui.util.toDateString(data.doctorAssessment.assessmentValidity, "yyyy-MM-dd HH:mm:ss")
			doctorAssessmentEdit.tempStartDate = start;
			var addAndMinEnd = layui.util.toDateString(data.doctorAssessment.addAndMinusTimeEnd, "yyyy-MM-dd HH:mm:ss")
			var addAndMinStart = layui.util.toDateString(data.doctorAssessment.addAndMinusTime, "yyyy-MM-dd HH:mm:ss")
			/**
			 * 格式化回显时间
			 */
			if (data.doctorAssessment.assessmentFrequency == assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY) {
				data.doctorAssessment.assessmentTerm = new Date(data.doctorAssessment.assessmentTerm).getFullYear() + "年" + (new Date(data.doctorAssessment.assessmentTerm).getMonth() + 1) + "月"
			} else if(data.doctorAssessment.assessmentFrequency == assemblys.top.mdms.mdmsConstant.DOCTOR_QUARTER) {
				data.doctorAssessment.assessmentTerm = new Date(data.doctorAssessment.assessmentTerm).getFullYear() + "年" + (new Date(data.doctorAssessment.assessmentTerm).getMonth() + 1) / 3 + "季度"
			}else {
				data.doctorAssessment.assessmentTerm = new Date(data.doctorAssessment.assessmentTerm).getFullYear() + "年"
			}
			
			data.doctorAssessment.assessmentValidity = start.concat(" ~ ", end)
			data.doctorAssessment.addAndMinusTime = addAndMinStart.concat(" ~ ", addAndMinEnd)
			doctorAssessmentEdit.doctorAssessPoject = data.doctorAssessment.doctorAssessPoject
			$("input[name=assessmentSumScore]").val(doctorAssessmentEdit.num)
			if (param.get("search")) {
				doctorAssessmentEdit.disabledDom("select[name=assessmentFrequency]")
				// 2023/1/19 zh 添加考评方式进行禁止样式
				doctorAssessmentEdit.disabledDom("select[name=assessmentMode]")
				doctorAssessmentEdit.disabledDom("select[name=assessmentScope]")
				doctorAssessmentEdit.disabledDom("input[name=assessmentTerm]")
				doctorAssessmentEdit.disabledDom("input[name=assessmentName]")
				doctorAssessmentEdit.disabledDom("input[name=assessmentSort]")
				/*if( !doctorAssessmentEdit.hasRight ){
					doctorAssessmentEdit.disabledDom("input[name=assessmentValidity]")
				}*/
				doctorAssessmentEdit.disabledDom("input[name=assessmentValidity]")
				doctorAssessmentEdit.disabledDom("input[name=addAndMinusTime]")
				$("input[name=assessmentMode]").attr("disabled", "disabled")
				$("input[name=assessmentMode]").next().addClass("layui-radio-disabled layui-disabled")
				doctorAssessmentEdit.disabledDom("textarea[name=assessmentRemark]")
				doctorAssessmentEdit.disabledDom("select[name=assessmentApprovalScope]")
				doctorAssessmentEdit.disabledDom("input[name=assessmentSumScore]")
				doctorAssessmentEdit.disabledDom("input[name=assessmentPassScore]")
				doctorAssessmentEdit.disabledDom("input[name=assessmentIfMedical]")
			}
			if (param.get("export") == "true") {
				data.doctorAssessment.assessmentID = "";
				data.doctorAssessment.assessmentCode = "";
			}
			
			param.set(null, data.doctorAssessment);
			
			return data;
		});
	},
	saveDoctorAssessment : function(data) {
		var date = data.field.assessmentTerm.split("年");
		
		if (data.field.assessmentTerm.search(/季度/g) != -1 ) {
			
			data.field.assessmentTerm = layui.util.toDateString(new Date(date[0], date[1].replace("季度", "") * 3, 0), "yyyy-MM-dd HH:mm:ss")
			
		} else if(data.field.assessmentTerm.search(/月/g) != -1) {
			
			data.field.assessmentTerm = layui.util.toDateString(new Date(date[0], date[1].replace("月", ""), 0), "yyyy-MM-dd HH:mm:ss")
			
		}else {
			
			data.field.assessmentTerm = layui.util.toDateString(new Date(data.field.assessmentTerm.replace("年", "")), "yyyy-MM-dd HH:mm:ss")
		}
		var doctorAssessment = {
			assessmentName : data.field.assessmentName,
			assessmentScope : data.field.assessmentScope,
			assessmentMode : data.field.assessmentMode,
			assessmentFrequency : data.field.assessmentFrequency,
			assessmentTerm : data.field.assessmentTerm,
			assessmentValidity : data.field.assessmentValidity.split("~")[0],
			assessmentValidityEnd : data.field.assessmentValidity.split("~")[1],
			addAndMinusTime : data.field.addAndMinusTime.split("~")[0], //加减分开始时间
			addAndMinusTimeEnd : data.field.addAndMinusTime.split("~")[1],//加减分结束时间
			assessmentRemark : data.field.assessmentRemark,
			assessmentSort : data.field.assessmentSort,
			assessmentSumScore : data.field.assessmentSumScore,
			assessmentPassScore : data.field.assessmentPassScore,
			assessmentApprovalScope : data.field.assessmentApprovalScope,
			assessmentStatus : data.field.assessmentStatus,
			assessmentIfMedical : data.field.assessmentIfMedical,
			pojectCode : data.field.pojectCode,
			assessmentManyCode : param.get("compNo"),
			assessmentDeptStatus : param.get("assessmentDeptStatus")
		}
		
		// 2023/2/28 zh 补考
		if(data.field.makeUpStatus){
			doctorAssessment["makeUpStatus"] = data.field.makeUpStatus;
			doctorAssessment["assessmentID"] = param.get("assessmentID");
		}

		doctorAssessment["doctorAssessPoject.assessPojectApprovalUserCode"] = data.field.assessPojectApprovalUserCode

		if (doctorAssessmentEdit.arrs.length > 0) {
			doctorAssessment["doctorAssessPoject.pojectCode"] = doctorAssessmentEdit.arrs.reduce(function(previousValue, currentValue) {
				return previousValue + "," + currentValue
			})
		} else {
			doctorAssessment["doctorAssessPoject.pojectCode"] = doctorAssessmentEdit.arrs[0]
		}
		
		var dataContent, checked, users, url = "", userStr = ""
		var writerUrl = "mdms/functionModule/medoctorVirtueManage/doctorTemplate.html?funCode=" + param.get("funCode") + "&width=1500&height=1500&examType=5";
		
		var sendData = {
			"type" : "system",
			"subject" : "考评填写提醒",
			"content" : "您好，有一个" + data.field.assessmentName + "请您查收，位置在工作台上的考评管理填写。",
			"appCode" : "mdms",
			"compNo" : param.get("compNo"),
		}

		if (data.field.assessmentScope == assemblys.top.mdms.mdmsConstant.DOCTOR_USER) {
			
			doctorAssessment["doctorAssessPoject.assessPojectUserCode"] = data.field.select
			sendData.toUserCodes = data.field.select

			checked = false

		} else if (data.field.assessmentScope == assemblys.top.mdms.mdmsConstant.DOCTOR_DEPT) {
			doctorAssessment["doctorAssessPoject.assessPojectDeptCode"] = data.field.select
			url += basePath + "mdms/survey/findUserList.spring"
			dataContent = {
				"surveyDept" : data.field.select
			}
			checked = true
		} else {
			checked = true
			url += basePath + "mdms/medoctorAssessment/getDoctorUsers.spring"
			dataContent = {
				"compNo" : param.get("compNo"),
				"assessmentIfMedical" : param.get("assessmentIfMedical")
			}
		}
		
		if (checked) {
			$.ajax({
				url : url,
				type : "get",
				data : dataContent,
				dataType : "json",
				skipDataCheck : true,
				async : false,
				success : function(data) {
					if (data.users != undefined) {
						users = data.users
					} else if (data.data.data != undefined) {
						users = data.data.data
					} else {
						users = data.data.users
					}
				},
			});
			
			for (var i = 0; i < users.length; i++) {
				if (i == 0) {
					userStr += users[i].UserCode + ","
				} else if (users.length - 1 == i) {
					userStr += users[i].UserCode
				} else {
					userStr += users[i].UserCode + ","
				}
			}
			
			var temp = userStr.substr(-1);
			if(temp == ","){
				userStr = userStr.slice(0, -1);
			}
			sendData.toUserCodes = userStr
			doctorAssessment["doctorAssessPoject.assessPojectUserCode"] = userStr
		}
		
		var assUrl = "", assessmentCode
		if (param.get("edit")) {
			doctorAssessment.assessmentCode = data.field.assessmentCode
			assUrl += basePath + "mdms/medoctorAssessment/updateDoctorAssessment.spring"
		} else {
			assUrl += basePath + "mdms/medoctorAssessment/saveDoctorAssessment.spring"
		}
		
		// 2023/1/12 zh 修改模板发布的为模板状态
		if(data.field.template){
			doctorAssessment["doctorAssessPoject.assessPojectStatus"] = data.field.template;
		}else{
			doctorAssessment["doctorAssessPoject.assessPojectStatus"] = data.field.assessmentStatus;
		}
		
		
		$.ajax({
			url : assUrl,
			type : "post",
			async : false,
			data : doctorAssessment
		}).then(function(dataAss) {
			if (data.field.assessmentStatus == 1) {
				assessmentCode = dataAss.assessmentCode
			}
			if (param.get("edit")) {
				assemblys.msg("修改成功", function() {
					// 考评模板
					if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE) {
						parent.doctorAssessmentTemplateList.getDoctorAssessmentPager();
					} else {
						parent.doctorAssessmentList.getDoctorAssessmentPager();
					}
					assemblys.closeWindow();
				});
			} else {
				assemblys.msg("保存成功", function() {
					// 考评模板
					if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE) {
						parent.doctorAssessmentTemplateList.getDoctorAssessmentPager();
					} else {
						parent.doctorAssessmentList.getDoctorAssessmentPager();
					}
					assemblys.closeWindow();
				});
			}
		})

		if (param.get("edit") && data.field.assessmentStatus == 1) {
			assessmentCode = data.field.assessmentCode
		}
		
		if (data.field.assessmentStatus == 1) {
			sendData.url += "&assessmentCode=" + assessmentCode
			$.ajax({
				url : basePath + "frame/message/pushMsg.spring",
				type : "post",
				data : sendData,
				dataType : "json",
				skipDataCheck : true,
				success : function(data) {
					
				}
			});
		}
	},
	saveTime : function(){
		var assessmentValidity = param.get("assessmentValidity");
		var tempArr = assessmentValidity.split("~");
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/extendTime.html?";
		url += "assessmentCode=" + param.get("assessmentCode") + "&startDate=" + tempArr[0] + "&endDate=" + tempArr[1] + "&funCode=" + param.get("funCode");
		layer.open({
			type : 2,
			id : "extendTime",
			title : "延长时间",
			scrollbar : false,
			maxmin : false,
			area : [ '500px', '400px' ],
			content : url
		});
		
		/*var url = basePath + "mdms/medoctorAssessment/updateTime.spring"
		$.ajax({
			url : url,
			type : "post",
			async : false,
			data : {
				"assessmentCode" : param.get("assessmentCode"),
				"assessmentValidityEnd" : assessmentValidityEnd
			}
		}).then(function(data) {
			assemblys.msg("修改成功", function() {
				parent.doctorAssessmentList.getDoctorAssessmentPager();
				assemblys.closeWindow();
			});
		})*/
	},
	addBtn : function(index) {
		var url = basePath + 'mdms/functionModule/medoctorVirtueManage/doctorPojectItem.html?'
		
		url += "compNo=" + param.get("compNo");
		url += "&funCode=" + param.get("funCode")
		url += "&assessmentIfMedical=" + param.get("assessmentIfMedical")
		url += "&controGroup=" + index;
		
		if (doctorAssessmentEdit.doctorAssessPoject != null) {
			for (var i = 0; i < doctorAssessmentEdit.arrs.length; i++) {
				url += "&pojectCode=" + doctorAssessmentEdit.arrs[i]
			}
		}
		layer.open({
			type : 2,
			id : "doctorPojectItem",
			title : "选择项目",
			scrollbar : false,
			maxmin : false,
			area : [ '900px', '500px' ],
			content : url
		});
	},
	addItem : function(poject,controIndex) {
		
		doctorAssessmentEdit.objArrs = [];
		
		if (poject) {
			
			var options = poject.map(function(item, index) {
				
				item.pojectType = doctorAssessmentEdit.judgeType(item.pojectType);
				doctorAssessmentEdit.objArrs.push(item);
				doctorAssessmentEdit.num += item.pojectSumScore;

				if (item.value) {
					doctorAssessmentEdit.arrs.push(item.value);
					
					var htmlStr = '<tr class="' + item.value + '"><td style="text-align:center">'
					htmlStr += '<i class="layui-icon layui-icon-delete i_delete " title="删除" onclick="doctorAssessmentEdit.deleteDoctorOperation(\'' + item.value + "-" + item.pojectSumScore + '\','+controIndex+',1)"></i>'
					htmlStr += '<input type="hidden" name="pojectCode" value="' + item.value + '"/></td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectType + '"class="layui-input" disabled="disabled" /></td>'
					htmlStr += '<td><input type="text" style="border:none;text-align:center" autocomplete="off"   value="' + item.title + '"  class="layui-input" disabled="disabled"/></td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectSumScore + '" class="layui-input" disabled="disabled" /></td>'
					htmlStr += '</tr>'
					return htmlStr
				}
					
				if(item.pojectCode){
					doctorAssessmentEdit.arrs.push(item.pojectCode);
					var htmlStr = '<tr class="' + item.pojectCode + '"><td style="text-align:center">'
					if (param.get("search")) {
						htmlStr += ''
					} else {
						htmlStr += '<i class="layui-icon layui-icon-delete i_delete " title="删除" onclick="doctorAssessmentEdit.deleteDoctorOperation(\'' + item.pojectCode + "-" + item.pojectSumScore + '\','+controIndex+',1)"></i>'
					}
					htmlStr += '<input type="hidden" name="pojectCode" value="' + item.pojectCode + '"/>'
					htmlStr += '</td><td>'
					htmlStr += '<input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectType + '"class="layui-input" disabled="disabled />'
					htmlStr += '</td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectSumScore + '" class="layui-input" disabled="disabled" /></td>'
					htmlStr += '<td><input type="text" style="border:none;text-align:center" autocomplete="off"   value="' + item.pojectName + '"  class="layui-input" disabled="disabled" /></td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectSumScore + '" class="layui-input" disabled="disabled" /></td>'
					htmlStr += '</tr>'
					return htmlStr;
				}
					
			})
		}
		
		$("input[name=assessmentSumScore]").val(doctorAssessmentEdit.num)
		
		if(controIndex){
			
			if(controIndex == 1){
				$(".addAssessment").append(options)
			}else{
				$(".addGroupAssess").append(options)
			}
			
		}
	},
	cutInputDate : function(inputName, appendInputName, type, format, dateFormat) {
		$(inputName).remove()
		$(appendInputName).append('<input type="text" id="assessmentTermID" name="assessmentTerm" lay-verify="required" lay-filter="assessmentTermFilter" class="layui-input"  placeholder="日期"  autocomplete="off"/>')
		
		var data = {
				elem : inputName,
				type : type,
				format : format,
		}
		
		if(format == 'yyyy年M季度'){
			data.ready = function(){
				var hd = $("#layui-laydate" + $(inputName).attr("lay-key"));
				if(hd.length > 0){
					hd.click(function(){
						doctorAssessmentEdit.ren($(this));
					});
				}
				doctorAssessmentEdit.ren(hd)
			}
			data.btns =  ['clear','confirm']
		}
		layui.laydate.render(data);
	},
	deleteDoctorOperation : function(index,controIndex,controSelect) {

		doctorAssessmentEdit.num -= index.split("-")[1]
		doctorAssessmentEdit.arrs.splice(doctorAssessmentEdit.arrs.indexOf(index.split("-")[0]), 1)

		if(!controSelect){
			for (var i = 0; i < doctorAssessmentEdit.objArrs.length; i++) {
				if(doctorAssessmentEdit.objArrs[i].value){
					if(doctorAssessmentEdit.objArrs[i].value == index.split("-")[0]){
						doctorAssessmentEdit.objArrs.splice(i,1);
					}
				}else{
					if(doctorAssessmentEdit.objArrs[i].pojectCode == index.split("-")[0]){
						doctorAssessmentEdit.objArrs.splice(i,1);
					}
				}
			}
		}else{

			for (var j = 0; j < doctorAssessmentEdit.whetherArrs.length; j++) {
				
				var code = "";
				if(doctorAssessmentEdit.whetherArrs[j].value){
					code = doctorAssessmentEdit.whetherArrs[j].value;
				}else{
					code = doctorAssessmentEdit.whetherArrs[j].pojectCode;
				}
				
				if(code == index.split("-")[0]){
					doctorAssessmentEdit.whetherArrs.splice(j,1);
				}
			}
			
			
			for (var j = 0; j < doctorAssessmentEdit.notArrs.length; j++) {
				
				var code = "";
				if(doctorAssessmentEdit.notArrs[j].value){
					code = doctorAssessmentEdit.notArrs[j].value;
				}else{
					code = doctorAssessmentEdit.notArrs[j].pojectCode;
				}
				
				if(code == index.split("-")[0]){
					doctorAssessmentEdit.notArrs.splice(j,1);
				}
			}
		
		}
		
		
		if(doctorAssessmentEdit.num < 0 || isNaN(doctorAssessmentEdit.num)){
			doctorAssessmentEdit.num = 0;
		}
		
		if(controIndex){
			
			if(controIndex == 1){
				$(".addAssessment").children().siblings("tbody").find("tr").filter("." + index.split("-")[0]).remove()
			}else{
				$(".addGroupAssess").children().siblings("tbody").find("tr").filter("." + index.split("-")[0]).remove()
			}
			
		}

		$("input[name=assessmentSumScore]").val(doctorAssessmentEdit.num)
	},
	listenChange : function(data) {
		if (data.value > doctorAssessmentEdit.num) {
			assemblys.msg("不能大于总分数");
			$("input[name=assessmentPassScore]").val("")
		}
		
	},
	// 禁用样式
	disabledDom : function(index) {
		$(index).attr("disabled", "disabled")
		if (index.indexOf("input") || index.indexOf("textarea")) {
			$(index).addClass("layui-disabled")
		}
	},
	// 对比类型
	judgeType : function(type) {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.DOCTORVIRTUETYPE,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
			},
			dataType : "json",
			async : false,
			skipDataCheck : true,
			success : function(data) {
				if (data.dictList) {
					if (data.dictList.length > 0) {
						for (let dict of data.dictList) {
							if(type == dict.dictCode){
								type = dict.dictName;
							}
						}
					}
				}
			}
		});
		return type;
	},
	// 监听考评方式
	listenAassessmentMode : function(){
		// 2023/1/19 zh 考评方式
		layui.form.on('select(assessmentModeFilter)', function(data){
				doctorAssessmentEdit.initCutDate("assessmentMode","assessmentFrequency",data);
		});  
	},
	// 监听考评频次
	listenAssessmentFre : function(){
		// 2023/1/19 zh 考评频次
		layui.form.on('select(assessmentFrequencyFilter)', function(data){
				doctorAssessmentEdit.initCutDate("assessmentFrequency","assessmentMode",data);
		});
	},
	initCutDate : function(incrrE,prevE,data){

		var val = doctorAssessmentEdit.judgeMode(data.value);
		doctorAssessmentEdit.cutInputDate("#assessmentTermID", "div[assessmentTermDiv]", val[1], val[2], val[3]);

		if(incrrE && prevE){
			doctorAssessmentEdit.click(incrrE,prevE,data);
		}
	},
	click : function(currName,prevName,data){
		
		$("select[name="+currName+"]").find("option").each(function(index,elem){
			  if($(elem).val() == data.value){
				  $("select[name="+prevName+"] option").each(function(cindex,celem){
					  if($(celem).attr("value") == $(elem).attr("value1")){
						  $(celem).prop("selected","selected");
					  }else{
						  $(celem).removeAttr("selected");
					  }
				  })
			  }
		  });
		
		  layui.form.render();

	
	  },
	ren : function(thiz){
		var mls = thiz.find(".laydate-month-list");
		mls.each(function(){
			
			$(this).find("li").each(function(inx, ele){
				var cx = ele.innerHTML;
				if( inx < 4) {
					ele.innerHTML = cx.replace(/月/g, "季度");
				}else{
					ele.style.display = "none";
				}
			})
		})
	},
	refleshUser : function(){
		//考评管理中  自评范围和适用人员  对选择人员的显示隐藏  cjl  2023-02-28
		if(param.get('assessmentScope') == assemblys.top.mdms.mdmsConstant.DOCTOR_USER){
			$("label[customLabel]").text("选择医师").removeClass('layui-hide')
			$("#demo1").removeClass('layui-hide');
		}else{
			$("label[customLabel]").text("选择医师").addClass('layui-hide')
			$("#demo1").addClass('layui-hide');
		}
		
			doctorAssessmentEdit.users = doctorAssessmentEdit.user(param.get("assessmentIfMedical"));
			doctorAssessmentEdit.userOptions = doctorAssessmentEdit.users.map(function(item) {
				return {
					name : item.UserName,
					value : item.UserCode
				}
			})
		
		if (doctorAssessmentEdit.doctorAssessPoject != null && doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode != null) {
			if (param.get("search")) {
				doctorAssessmentEdit.userOptions = doctorAssessmentEdit.users.map(function(item) {
					return {
						name : item.UserName,
						value : item.UserCode,
						disabled : true
					}
				})
			}
			
			// 2023/216 zh 全员范围再选择人员范围导致显示问题
			if(param.get("assessmentID") == ""){
				doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode = [];
			}
			
			xmSelect.render({
				el : '#demo1',
				language : 'zn',
				theme : {
					color : '#0081ff',
				},
				paging : true,
				pageSize : 20,
				toolbar : {
					show : true,
				},
				initValue : doctorAssessmentEdit.doctorAssessPoject.assessPojectUserCode,
				filterable : true,
				data : doctorAssessmentEdit.userOptions
			})
		} else {
			xmSelect.render({
				el : '#demo1',
				language : 'zn',
				theme : {
					color : '#0081ff',
				},
				paging : true,
				pageSize : 20,
				toolbar : {
					show : true,
				},
				filterable : true,
				data : doctorAssessmentEdit.userOptions
			})
		}
		$("#customSelectID").empty()
		$("div[customDiv]").show()
		$("label[customLabel]").show()
	},
	// 补考
	getDoctorAssessmentMakeUp : function(){
		return $.ajax({
			url : basePath + "mdms/medoctorAssessment/getDoctorAssessmentMakeUp.spring",
			type : "get",
			data : {
				"assessmentID" : param.get("assessmentID"),
			},
			dataType : "json",
		})
	},
	nonMedicalPersonnelRadio : function(val){
		
		
		doctorAssessmentEdit.num = 0;

		$("input[name=assessmentPassScore]").val("");
		
		if(val == 0){
			if(doctorAssessmentEdit.notArrs.length > 0){
				doctorAssessmentEdit.eachArrRemove(Object.assign([{}],doctorAssessmentEdit.whetherArrs));
				doctorAssessmentEdit.num = 0;
				doctorAssessmentEdit.addItem(doctorAssessmentEdit.notArrs,1);
				for (var i = 0; i < doctorAssessmentEdit.notArrs.length; i++) {
					if(doctorAssessmentEdit.num == 0){
						doctorAssessmentEdit.num += doctorAssessmentEdit.notArrs[i].pojectSumScore;
					}
				}
			}else{
				
				if(doctorAssessmentEdit.whetherArrs.length > 0){
					doctorAssessmentEdit.eachArrRemove(Object.assign([{}],doctorAssessmentEdit.whetherArrs));
				}else{
					doctorAssessmentEdit.eachArrRemove(Object.assign([{}],doctorAssessmentEdit.objArrs));
				}
				
			}
		}else{
			if(doctorAssessmentEdit.whetherArrs.length > 0){
				doctorAssessmentEdit.eachArrRemove(Object.assign([{}],doctorAssessmentEdit.notArrs));
				doctorAssessmentEdit.num = 0;
				doctorAssessmentEdit.addItem(doctorAssessmentEdit.whetherArrs,1);
				for (var i = 0; i < doctorAssessmentEdit.whetherArrs.length; i++) {
					if(doctorAssessmentEdit.num == 0){
						doctorAssessmentEdit.num += doctorAssessmentEdit.whetherArrs[i].pojectSumScore;
					}
				}
			}else{
				
				if(doctorAssessmentEdit.notArrs.length > 0){
					doctorAssessmentEdit.eachArrRemove(Object.assign([{}],doctorAssessmentEdit.notArrs));
				}else{
					doctorAssessmentEdit.eachArrRemove(Object.assign([{}],doctorAssessmentEdit.objArrs));
				}
			}
		}
		
		if(isNaN(doctorAssessmentEdit.num)){
			doctorAssessmentEdit.num = 0;
		}
		
		$("input[name=assessmentSumScore]").val(doctorAssessmentEdit.num);

	},
	eachArrRemove : function(arr){
		for (var i = 0; i < arr.length; i++) {
			var code = "";
			if(arr[i].value){
				code = arr[i].value;
			}else{
				code = arr[i].pojectCode;
			}
			
			doctorAssessmentEdit.deleteDoctorOperation(code + "-" + arr[i].pojectSumScore,1);
		}
	},
	downloadTemplate : function(){
		var url = basePath + "mdms/functionModule/template/考评计划增减人导入模板.xls";
		window.location.href = url;
	},
	//导入
	importAddAndDel : function(){
		var uploadInst = layui.upload.render({
			elem : '#uploadExcel',
			url : basePath + "mdms/medoctorAssessment/importAddAndDel.spring?",
			data : {
				assessmentCode : param.get("assessmentCode"),
				assessmentIfMedical : param.get("assessmentIfMedical"),
				assessmentScope : param.get("assessmentScope")
			},
			accept : 'file',
			exts : 'xls',
			done : function(res) {
				// 上传完毕回调
				if (res.rtn == "success") {
					assemblys.msg(res.msg, function(){
						window.location.reload();
						parent.doctorAssessmentList.getDoctorAssessmentPager();
					}, 5000);
					//layer.alert(res.msg);
				} else {
					assemblys.msg("导入失败, 请检查模板是否正确");
				}
			},
			error : function() {
				// 请求异常回调
				assemblys.alert("上传失败");
			}
		});
	},
	/***
	 * 判断方式
	 * @description
	 * <AUTHOR>
	 * @date 2023/6/9 13:54
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	judgeMode : function(value) {
		var param = new Map(
			[
				[
					assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR , [assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR_ASSESSMENT,"year","y年","YYYY"]
				],
				[
					assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY , [assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY_ASSESSMENT,"month", "yyyy年M月", "MM"]
				],
				[
					assemblys.top.mdms.mdmsConstant.DOCTOR_QUARTER , [assemblys.top.mdms.mdmsConstant.DOCTOR_QUARTER_ASSESSMENT,"month", "yyyy年M季度", "MM"]
				],
				[
					assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR_ASSESSMENT , [assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR,"year","y年","YYYY"]
				],
				[
					assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY_ASSESSMENT , [assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY,"month", "yyyy年M月", "MM"]
				],
				[
					assemblys.top.mdms.mdmsConstant.DOCTOR_QUARTER_ASSESSMENT , [assemblys.top.mdms.mdmsConstant.DOCTOR_QUARTER,"month", "yyyy年M季度", "MM"]
				]
			]
		);

		return param.get(value);
	}
}
