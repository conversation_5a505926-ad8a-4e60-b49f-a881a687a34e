var doctorAssessmentList = {
	// 公司编号
	comanyNo : null,
	// 权限
	permission : null,
	// 表格列
	cols : null,
	renderData : null,
	num : 0,
	hasShowTempLate : false,
	init : function() {
		// 获取权限
		doctorAssessmentList.getPermission(assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT, doctorAssessmentList);
		// 获取考评计划加人、删除人、导入模板权限
		doctorAssessmentList.getShowTempLate();
		/**
		 * 该用户是否拥有新增权限
		 */
		if (doctorAssessmentList.permission.hasAdd == false) {
			$("button[addAssessmentBtn]").remove();
			$("button[addTemplateBtn]").remove();
		}
		
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		
		var laydate = layui.laydate;
		
		// 执行一个laydate实例
		var dateIndex = laydate.render({
			elem : '#test1', // 指定元素
			range : '~',
			type : 'datetime',
			done : function(value, date, endDate) {
				var dateStart = value.split("~")[0]
				var dateEnd = value.split("~")[1]
				var url = basePath + "mdms/medoctorAssessment/getDoctorAssessmentPager.spring?" + param.__form() + "&" + filterParam.__form() + "&companyCode=" + doctorAssessmentList.comanyNo;
				// 2023/1/12 zh 修复时间组件问题
				if (value != "") {
					url += "&dateStart=" + dateStart + "&dateEnd=" + dateEnd;
				}
				assemblys.tableRender({
					elem : '#list',
					url : url,
					cols : [ doctorAssessmentList.cols ],
					done : function(res, curr, count) {
						page.set("curPageNum", res.curPageNum);
						page.set("pageSize", res.pageSize);
						$("#filterNum").text(count);
					},
					events : {
						toEditDoctorAssessment : doctorAssessmentList.toEditDoctorAssessment,
						deleteDoctorAssessment : doctorAssessmentList.deleteDoctorAssessment,
						searchDoctorAssessment : doctorAssessmentList.searchDoctorAssessment
					}
				});
			}
		})

		doctorAssessmentList.doctorComany();
		var customBtnDom = [];
		if (doctorAssessmentList.permission.hasExec) {
			customBtnDom.push({
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : doctorAssessmentList.exportList
			});
		}
		
		filterSearch.init(basePath, doctorAssessmentList.getFilterParams({}), doctorAssessmentList.getDoctorAssessmentPager, customBtnDom);
		doctorAssessmentList.initLayuiForm();
		$("#keyword").attr("onkeydown", "if(event.keyCode == 13){return false;}");
		doctorAssessmentList.loadCompNo();
		
		if (doctorAssessmentList.hasShowTempLate) {
			$('#tempLate').removeClass("layui-hide");
		}
		
	},
	/**
	 * 获取当前用户的权限
	 */
	getPermission : function(funCode, result) {
		$.ajax({
			url : basePath + "mdms/EmployeeTrailWork/findPermissionAndUser.spring?",
			type : "get",
			dataType : 'json',
			async : false,
			data : {
				funCode : funCode
			},
			success : function(data) {
				result.permission = data;
			}
		})
	},
	/**
	 * 选择模板列表
	 */
	getShowTempLate : function() {
		$.ajax({
			url : basePath + "mdms/medoctorAssessment/getShowTempLate.spring?",
			type : "get",
			dataType : 'json',
			async : false,
			success : function(data) {
				doctorAssessmentList.hasShowTempLate = data.hasShowTempLate;
			}
		})
	},
	/**
	 * 获取所有公司
	 */
	doctorComany : function() {
		$.ajax({
			url : basePath + "/frame/common/getMenuIcon.spring?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT + "&hasOrg=true",
			type : "GET",
			dataType : "json",
			async : false,
			success : function(data) {
				var arrs = data.compList.map(function(item, index) {
					return ('<option value="' + item.compNo + '">' + item.compName + '</option>')
				})
				$("select[name=compNo]").append(arrs)
			}
		})
		/**
		 * 获取当前用户的公司
		 */
		$.ajax({
			url : basePath + "/mdms/mdmsCommon/getUserInfo.spring",
			type : "GET",
			dataType : "json",
			async : false,
			success : function(data) {
				doctorAssessmentList.comanyNo = data.user.compNo;
				$("select[name=compNo]").val(data.user.compNo);
			}
		})

		layui.form.render()
	},
	/**
	 * 加载默认公司
	 */
	loadCompNo : function() {
		mdmsCommon.loadCompNo(function(data) {
			$("#test1").val("")
			doctorAssessmentList.comanyNo = data.value
			doctorAssessmentList.getDoctorAssessmentPager(data.value);
		})
		layui.form.render()
	},
	/**
	 * 初始化渲染
	 */
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
		});
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			doctorAssessmentList.getDoctorAssessmentPager();
		});
		
		layui.form.render();
	},
	/**
	 * 字段查询
	 */
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "考评名称",
			title : "关键字",
			id : "keyword"
		} ];
		return params;
	},
	/**
	 * 初始化表格信息
	 */
	getDoctorAssessmentPager : function(comanyCode) {
		doctorAssessmentList.cols = [ {
			title : '操作',
			width : 90,
			align : "center",
			templet : function(d) {
				var html = '';
				if (d.assessmentStatus == 1) {
					html += '<i class="layui-icon layui-icon-search i_delete" title="查看" lay-event="searchDoctorAssessment"></i>';
					html += '<i class="layui-icon layui-icon-log i_delete" title="进度详情" lay-event="searchProgress"></i>';
				}
				if (d.assessmentStatus == 0) {
					html += '<i class="layui-icon layui-icon-search i_delete" title="查看" lay-event="searchDoctorAssessment"></i>'
					if (doctorAssessmentList.permission.hasEdit) {
						html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditDoctorAssessment"></i>';
					}
					if (doctorAssessmentList.permission.hasDel) {
						html += '<i class="layui-icon layui-icon-delete i_delete" title="作废" lay-event="deleteDoctorAssessment"></i>';
					}
				}
				
				// 2023/1/17 zh-作废状态
				if (d.assessmentStatus == 3) {
					html += '<i class="layui-icon2 i_delete" title="还原" lay-event="toCancellectionAssessment">&#xeac2;</i>';
				}
				
				return html;
			}
		}, {
			title : '考评频次',
			align : "center",
			width : 110,
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentFrequencyName + "频次");
			}
		}, {
			title : '考评方式',
			align : "center",
			width : 110,
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentModeName);
			}
		}, {
			title : '考评名称',
			align : "center",
			width : 300,
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentName);
			}
		}, {
			title : '关联项目数',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectSize);
			}
		}, {
			title : '关联项目名称',
			align : "center",
			width : 200,
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectName);
			}
		}, {
			title : '状态',
			align : "center",
			width : 80,
			templet : function(d) {
				var html = "";
				
				if (d.assessmentStatus == 3) {
					html += "<span style='color:red'>已作废</span>";
				}
				if (d.assessmentStatus == 0) {
					html += "<span style='color:grey'>未发布</span>";
				}
				if (d.assessmentStatus == 1 && d.num3 == 0) {
					html += "<span >已发布</span>";
				}
				if (d.assessmentStatus == 1 && d.num3 != 0 && (d.num4 != d.num2 || d.num4 == 0)) {
					html += "<span >考评中</span>";
				}
				if (d.assessmentStatus == 1 && d.num2 == d.num4 && d.num4 != 0) {
					html += "<span style='color:green'>已完成</span>";
				}
				return html;
			}
		}, {
			title : '考评范围',
			align : "center",
			width : 90,
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentScopeName);
			}
		}, {// cjl 2022-11-03
			title : '创建人',
			align : "center",
			width : 120,
			templet : function(d) {
				return assemblys.htmlEncode(d.UserName);
			}
		}, {
			title : '考评期间',
			align : "center",
			width : 92,
			templet : function(d) {
				var date = new Date(layui.util.toDateString(d.assessmentTerm, "yyyy-MM-dd HH:mm:ss"))
				if (d.assessmentMode == assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY_ASSESSMENT) {
					return assemblys.htmlEncode(date.getFullYear() + "年" + (date.getMonth() + 1) + "月份");
				} else {
					return assemblys.htmlEncode(date.getFullYear() + "年份");
				}
			}
		}, {
			title : '考评时间',
			align : "center",
			width : 200,
			templet : function(d) {
				var start = layui.util.toDateString(d.assessmentValidity, "yyyy-MM-dd")
				var end = layui.util.toDateString(d.assessmentValidityEnd, "yyyy-MM-dd")
				return assemblys.htmlEncode(start.concat(" ~ ", end));
			}
		}, {
			title : '科评说明',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentRemark != "" ? d.assessmentRemark : "无");
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/medoctorAssessment/getDoctorAssessmentPager.spring?" + param.__form() + "&" + filterParam.__form() + "&companyCode=" + (comanyCode ? comanyCode : doctorAssessmentList.comanyNo),
			cols : [ doctorAssessmentList.cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditDoctorAssessment : doctorAssessmentList.toEditDoctorAssessment,
				deleteDoctorAssessment : doctorAssessmentList.deleteDoctorAssessment,
				searchDoctorAssessment : doctorAssessmentList.searchDoctorAssessment,
				toCancellectionAssessment : doctorAssessmentList.toCancellectionAssessment,
				searchProgress : doctorAssessmentList.searchProgress
			}
		});
	},
	/**
	 * 导出考评信息
	 */
	exportList : function() {
		location.href = basePath + "mdms/medoctorAssessment/exportList.spring?" + param.__form() + "&" + filterParam.__form() + "&companyCode=" + doctorAssessmentList.comanyNo;
	},
	/**
	 * 新增/修改考评信息根据考评业务编码和状态
	 */
	toEditDoctorAssessment : function(d) {
		
		var content = "doctorAssessmentEdit.html?funCode=" + param.get("funCode") + "&assessmentID=" + d.assessmentID + "&compNo=" + doctorAssessmentList.comanyNo;
		
		if (d.assessmentID != 0) {
			content += "&assessmentID=" + d.assessmentID + "&assessmentStatus=" + d.assessmentStatus + "&edit=true&assessmentCode=" + d.assessmentCode;
			
		}
		
		// 2023/2/14 zh 控制是否含有科室考评
		content += "&assessmentDeptStatus=1";
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditDoctorAssessment",
			area : [ '100%', '100%' ],
			title : false,
			scrollbar : false,
			content : content
		});
	},
	/**
	 * 查看考评详情信息
	 */
	searchDoctorAssessment : function(d) {
		layer.open({
			type : 2,
			area : [ '90%', '90%' ],
			title : "考评详情",
			maxmin : true,
			content : "doctorAssessmentEdit.html?funCode=" + param.get("funCode") + "&assessmentCode=" + d.assessmentCode + "&compNo=" + doctorAssessmentList.comanyNo + "&assessmentID=" + d.assessmentID + "&assessmentStatus=" + d.assessmentStatus + "&search=true"
		});
	},
	/**
	 * 删除考评信息根据ID
	 */
	deleteDoctorAssessment : function(d) {
		assemblys.confirm("确认删除吗?", function() {
			return $.ajax({
				url : basePath + "mdms/medoctorAssessment/deleteDoctorAssessment.spring",
				type : "post",
				data : {
					assessmentID : d.assessmentID,
					assessmentCode : d.assessmentCode
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					// 考评模板
					if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE) {
						doctorAssessmentTemplateList.getDoctorAssessmentPager();
					} else {
						doctorAssessmentList.getDoctorAssessmentPager();
					}
				});
				return data;
			});
		});
	},
	/**
	 * 模板导入
	 */
	toDoctorAssessmentTemplate : function() {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorAssessmentTemplateList.html?funCode=" + param.get("funCode") + "&companyCode=" + doctorAssessmentList.comanyNo + "&template=1";
		layer.open({
			type : 2,
			title : "",
			area : [ '90%', '90%' ],
			content : url
		});
	},
	/**
	 * 作废
	 */
	toCancellectionAssessment : function(d) {
		var assUrl = basePath + "mdms/medoctorAssessment/updateDoctorAssessment.spring";
		// 2023/1/17 zh 删除状态变为未发布状态
		d.assessmentStatus = 0;
		d.restoreStatus = 1;
		d.assessmentDeptStatus = 0;
		
		$.ajax({
			url : assUrl,
			type : "post",
			async : false,
			data : d,
			success : function(sc) {
				assemblys.msg("还原成功", function() {
					doctorAssessmentList.getDoctorAssessmentPager();
				});
			}
		})
	},
	/**
	 * 进度
	 */
	searchProgress : function(d) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/searchProgressDetails.html?funCode=" + param.get("funCode") + "&assessmentCode=" + d.assessmentCode;
		//自评总人数，科评总人数
		url += "&selfSumScore=" + d.num + "&deptSumScore=" + d.num2
		//已自评人数，已科评人数
		url += "&selfScore=" + d.num3 + "&deptScore=" + d.num4
		layer.open({
			type : 2,
			title : "进度详情",
			maxmin : true,
			area : [ '98%', '98%' ],
			content : url
		});
	},
}