var doctorAssessmentTemplateList = {
	assessment : {},
	init : function() {
		
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		//获取权限
		doctorAssessmentList.getPermission(assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE, doctorAssessmentTemplateList);
		
		layui.form.on('radio(templateFilter)', function(data) {
			var assessmentID = "", assessmentCode = "", assessmentStatus = "", companyCode = "", assessmentStr = "";
			assessmentID = data.value.substring(0, data.value.indexOf("-"));
			assessmentStr = data.value.substring(data.value.indexOf("-") + 1, data.value.length);
			assessmentCode = assessmentStr.substring(0, assessmentStr.indexOf("-"));
			assessmentStr = assessmentStr.substring(assessmentStr.indexOf("-") + 1, assessmentStr.length);
			assessmentStatus = assessmentStr.substring(0, assessmentStr.indexOf("-"));
			assessmentStr = assessmentStr.substring(assessmentStr.indexOf("-") + 1, assessmentStr.length);
			companyCode = assessmentStr.substring(0, assessmentStr.length);
			doctorAssessmentTemplateList.assessment.assessmentID = assessmentID;
			doctorAssessmentTemplateList.assessment.assessmentCode = assessmentCode;
			doctorAssessmentTemplateList.assessment.assessmentStatus = assessmentStatus;
			doctorAssessmentTemplateList.assessment.comanyCode = companyCode;
		});
		//隐藏导入按钮
		if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE) {
			$("button[export]").addClass("layui-hide");
		} else {
			$("button[addTemplate]").addClass("layui-hide");
			$(".tableDiv").css("top", "5px");
		}
		doctorAssessmentList.doctorComany();
		doctorAssessmentTemplateList.loadCompNo();
	},
	loadCompNo : function() {
		mdmsCommon.loadCompNo(function(data) {
			$("input[name=companyCode]").val("");
			$("input[name=companyCode]").val(data.value);
			doctorAssessmentList.comanyNo = data.value;
			doctorAssessmentTemplateList.getDoctorAssessmentPager();
		})
		layui.form.render()
	},
	getDoctorAssessmentPager : function() {
		var cols = [ {
			title : '操作',
			width : 105,
			minWidth : 95,
			align : "center",
			templet : function(d) {
				var html = '';
				
				if (param.get("funCode") != assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_ASSESSMENT_TEMPLATE) {
					html += ' <input type="radio" name="templateExport" lay-filter="templateFilter" value="' + d.assessmentID + '-' + d.assessmentCode + '-' + d.assessmentStatus + '-' + param.get("companyCode") + '">'
				} else {
					html += '<i class="layui-icon layui-icon-search i_delete" title="查看" lay-event="searchDoctorAssessment"></i>'
					if (doctorAssessmentTemplateList.permission.hasEdit) {
						html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditDoctorAssessment"></i>';
					}
					if (doctorAssessmentTemplateList.permission.hasDel) {
						html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteDoctorAssessment"></i>';
					}
				}
				
				return html;
			}
		}, {
			title : '考评频次',
			align : "center",
			width : 110,
			minWidth : 110,
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentFrequencyName + "频次");
			}
		}, {
			title : '考评方式',
			align : "center",
			width : 110,
			minWidth : 110,
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentModeName);
			}
		}, {
			title : '考评名称',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentName);
			}
		}, {
			title : '关联数量',
			align : "center",
			width : 90,
			minWidth : 90,
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectSize);
			}
		}, {
			title : '关联项目名称',
			align : "center",
			width : 120,
			minWidth : 120,
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectName);
			}
		}, {
			title : '考评范围',
			align : "center",
			width : 90,
			minWidth : 90,
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentScopeName);
			}
		}, {
			title : '考评期间',
			align : "center",
			width : 92,
			minWidth : 92,
			templet : function(d) {
				var date = new Date(layui.util.toDateString(d.assessmentTerm, "yyyy-MM-dd HH:mm:ss"))
				if (d.assessmentMode == assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY_ASSESSMENT) {
					return assemblys.htmlEncode(date.getFullYear() + "年" + (date.getMonth() + 1) + "月份");
				} else {
					return assemblys.htmlEncode(date.getFullYear() + "年份");
				}
			}
		}, {
			title : '考评时间',
			align : "center",
			width : 150,
			templet : function(d) {
				var start = layui.util.toDateString(d.assessmentValidity, "yyyy-MM-dd")
				var end = layui.util.toDateString(d.assessmentValidityEnd, "yyyy-MM-dd")
				return assemblys.htmlEncode(start.concat(" ~ ", end));
			}
		}, {
			title : '科评说明',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentRemark != "" ? d.assessmentRemark : "无");
			}
		} ];
		
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/medoctorAssessment/getDoctorAssessmentPager.spring?" + param.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				deleteDoctorAssessment : doctorAssessmentList.deleteDoctorAssessment,
				searchDoctorAssessment : doctorAssessmentList.searchDoctorAssessment,
				toEditDoctorAssessment : doctorAssessmentList.toEditDoctorAssessment
			}
		});
	},
	/**
	 * 用于考评管理选择模板导入
	 */
	templateExport : function() {
		if ($.isEmptyObject(doctorAssessmentTemplateList.assessment)) {
			assemblys.confirm("请选择模板");
		} else {
			
			var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorAssessmentEdit.html?";
			
			url += "funCode=" + param.get("funCode");
			url += "&assessmentCode=" + doctorAssessmentTemplateList.assessment.assessmentCode;
			url += "&compNo=" + doctorAssessmentTemplateList.assessment.comanyCode;
			url += "&assessmentID=" + doctorAssessmentTemplateList.assessment.assessmentID;
			url += "&assessmentStatus=" + doctorAssessmentTemplateList.assessment.assessmentStatus;
			url += "&export=true";
			url += "&assessmentDeptStatus=1";
			
			window.location.href = url;
		}
	},
	/**
	 * 查看考评详情信息
	 */
	searchDoctorAssessment : function(d) {
		layer.open({
			type : 2,
			area : [ '90%', '90%' ],
			title : "考评详情",
			maxmin : true,
			content : "doctorAssessmentEdit.html?funCode=" + param.get("funCode") + "&assessmentCode=" + d.assessmentCode + "&compNo=" + doctorAssessmentList.comanyNo + "&assessmentID=" + d.assessmentID + "&assessmentStatus=" + d.assessmentStatus + "&search=true"
		});
	}
}