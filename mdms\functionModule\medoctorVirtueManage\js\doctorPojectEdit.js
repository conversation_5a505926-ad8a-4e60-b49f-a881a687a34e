var doctorPojectEdit = {
	addIndex : 0,
	addScord : 0,
	contentIndex : 0,
	contentScord : 0,
	miniusIndex : 0,
	miniusScord : 0,
	addArr : new Array(),
	miniusArr : new Array(),
	contentArr : new Array(),
	doctorAssessPoject : null,
	groupList : null,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		if (param.get("pojectCode")) {
			doctorPojectEdit.getDoctorPoject()
		}
		
		doctorPojectEdit.getDict();
		doctorPojectEdit.initLayui();
		doctorPojectEdit.initTable();
	},
	/**
	 * 计算得分
	 */
	addScore : function($this, val) {
		var temp = 0;
		$(".addPoint").each(function(rowIndex) {
			if ($(this).val() != "" && !isNaN($(this).val())) {
				temp += parseInt($(this).val());
			} else {
				layer.alert("请输入基础得分，必须输入数字！");
				return;
			}
		});
		param.set("pojectSumScore", temp);
		$("input[name='pojectSumScore']").attr("disabled", "disabled");
	},
	/**
	 * 根据公司切换获取字典
	 */
	getDict : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.DOCTORVIRTUETYPE
			},
			dataType : "json",
			skipDataCheck : true,
			async : false,
			success : function(data) {
				var dataList = [];
				if (data.dictList) {
					for (var i = 0; i < data.dictList.length; i++) {
						dataList.push(data.dictList[i]);
					}
				}
				$.ajax({
					url : basePath + "frame/dict/getDictListByCode.spring",
					type : "get",
					data : {
						"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
						"dictTypeCode" : assemblys.top.mdms.mdmsConstant.DOCTORVIRTUETYPE,
						"compNo" : param.get("compNo")
					},
					dataType : "json",
					skipDataCheck : true,
					async : false,
					success : function(data1) {
						if (data1.dictList) {
							for (var i = 0; i < data1.dictList.length; i++) {
								dataList.push(data1.dictList[i]);
							}
						}
					}
				});
				for (var i = 0; i < dataList.length; i++) {
					$("select[name=pojectType]").append('<option value="' + dataList[i].dictCode + '">' + dataList[i].dictName + '</option>')
				}
			}
		});
		layui.form.render()
	},
	/**
	 * 添加子项内容
	 */
	addItem : function(poject) {
		if (poject) {
			var options = poject.map(function(item, index) {
				item.pojectType = doctorAssessmentEdit.judgeType(item.pojectType);
				if (item.value) {
					doctorAssessmentEdit.arrs.push(item.value);
					var htmlStr = '<tr class="' + item.value + '"><td style="text-align:center">'
					htmlStr += '<i class="layui-icon layui-icon-delete i_delete " title="删除" onclick="doctorAssessmentEdit.deleteDoctorOperation(\'' + item.value + "-" + item.pojectSumScore + '\')"></i>'
					htmlStr += '<input type="hidden" name="pojectCode" value="' + item.value + '"/></td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectType + '"class="layui-input" disabled="disabled" /></td>'
					htmlStr += '<td><input type="text" style="border:none;text-align:center" autocomplete="off"   value="' + item.title + '"  class="layui-input" disabled="disabled"/></td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectSumScore + '" class="layui-input" disabled="disabled" /></td>'
					htmlStr += '</tr>'
					return htmlStr
				} else {
					doctorAssessmentEdit.arrs.push(item.pojectCode);
					doctorAssessmentEdit.num += item.pojectSumScore
					var htmlStr = '<tr class="' + item.pojectCode + '"><td style="text-align:center">'
					if (param.get("search")) {
						htmlStr += ''
					} else {
						htmlStr += '<i class="layui-icon layui-icon-delete i_delete " title="删除" onclick="doctorAssessmentEdit.deleteDoctorOperation(\'' + item.pojectCode + "-" + item.pojectSumScore + '\')"></i>'
					}
					htmlStr += '<input type="hidden" name="pojectCode" value="' + item.pojectCode + '"/>'
					htmlStr += '</td><td>'
					htmlStr += '<input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectType + '"class="layui-input" disabled="disabled />'
					htmlStr += '</td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectSumScore + '" class="layui-input" disabled="disabled" /></td>'
					htmlStr += '<td><input type="text" style="border:none;text-align:center" autocomplete="off"   value="' + item.pojectName + '"  class="layui-input" disabled="disabled" /></td>'
					htmlStr += '<td><input type="text"  style="border:none;text-align:center" autocomplete="off"  value="' + item.pojectSumScore + '" class="layui-input" disabled="disabled" /></td>'
					htmlStr += '</tr>'
					return htmlStr;
				}
			})
		}
		$("input[name=assessmentSumScore]").val(doctorAssessmentEdit.num)
		$(".addAssessment").append(options)
	},
	/**
	 * 初始化减分项部分元素
	 */
	initTable : function() {
		//考评内容
		var kaoCommon = '<colgroup><col width="30"><col width="30"><col width="300"><col width="300"><col width="50"><col width="80"><col width="200"></colgroup>'
		kaoCommon += '<thead><tr><th style="text-align: center;width:50px;">操作</th> '
		kaoCommon += '<th style="text-align: center;width:40px">序号</th> '
		kaoCommon += '<th style="text-align: center;">考评内容<label class="commonLabel">*</label></th>'
		kaoCommon += '<th style="text-align: center;">扣分标准<label class="commonLabel">*</label></th>'
		kaoCommon += '<th style="text-align: center;width:200px;">基础得分<label class="commonLabel">*</label></th>'
		kaoCommon += '<th style="text-align: center;">状态<label class="commonLabel">*</label></th>'
		kaoCommon += '<th style="text-align: center;">考评说明</th></tr></thead>';
		
		if (!param.get("pojectCode")) {
			doctorPojectEdit.addArr.push(doctorPojectEdit.addIndex)
			doctorPojectEdit.miniusArr.push(doctorPojectEdit.miniusIndex)
			doctorPojectEdit.contentArr.push(doctorPojectEdit.contentIndex)
			addOperation(".contentScore", kaoCommon, "content", doctorPojectEdit.contentIndex, doctorPojectEdit.contentScord)
		} else {
			$("input[lay-submit]").val("修改");
			$("input[lay-submit]").attr("lay-filter", "update");
			$(".contentScore").append(kaoCommon);
		}
		layui.form.render();
		
	},
	/**
	 * 初始化渲染
	 */
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function(data) {
			doctorPojectEdit.saveDoctorPoject(data);
			return false;
		});
		layui.form.on("submit(update)", function(data) {
			doctorPojectEdit.saveDoctorPoject(data)
			return false;
		})
		layui.form.on("select(selectGroup)", function(data) {
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	/**
	 * 获取项目库信息根据项目库ID和业务编码
	 */
	getDoctorPoject : function() {
		return $.ajax({
			url : basePath + "mdms/medoctorPoject/getDoctorPoject.spring",
			data : {
				pojectID : param.get("pojectID"),
				pojectCode : param.get("pojectCode")
			}
		}).then(function(data) {
			doctorPojectEdit.groupList = data.groupList;
			for (var i = 0; i < data.doctorPoject.doctorContent.length; i++) {
				doctorPojectEdit.contentIndex = i
				doctorPojectEdit.contentSeq = data.doctorPoject.doctorContent[i].ContentSeq
				doctorPojectEdit.contentArr.push(doctorPojectEdit.contentIndex)
				echoOperation(".contentScore", "content", i, data.doctorPoject.doctorContent[i], "content")
			}
			
			if (param.get("search")) {
				disabledDom("select[name=pojectType]");
				disabledDom("input[name=pojectSort]");
				disabledDom("textarea[name=pojectName]");
				$("input[name=pojectStatus]").attr("disabled", "disabled");
				$("input[name=pojectStatus]").next().addClass("layui-radio-disabled ");
				$("input[name=pojectIfMedical]").attr("disabled", "disabled");
				$("input[name=pojectIfMedical]").next().addClass("layui-radio-disabled ");
				disabledDom("input[name=pojectSumScore]");
				disabledDom("textarea[name=pojectRemark]");
				disabledDom("input[class=addPoint]");
				$("input[lay-submit]").hide();
				$("input[contentBtn]").hide();
				
			}
			
			param.set(null, data.doctorPoject);
			return data;
		});
	},
	/**
	 * 保存项目库信息
	 */
	saveDoctorPoject : function(data) {
		
		if ($("#contentTable tr").length == 1 && $("#addTable tr").length == 1 && $("#miniusTable tr").length == 1) {
			layer.alert("考评内容和加减分项不能同时为空！");
			return false;
		}
		
		var doctor = {
			pojectSort : data.field.pojectSort,
			pojectType : data.field.pojectType,
			pojectName : data.field.pojectName,
			pojectStatus : data.field.pojectStatus,
			pojectSumScore : data.field.pojectSumScore,
			pojectRemark : data.field.pojectRemark,
			pojectIfMedical : data.field.pojectIfMedical,
			pojectComanyCode : param.get("compNo")
		}
		if (data.field.pojectCode) {
			doctor["pojectCode"] = data.field.pojectCode
		}
		//具体内容
		for (var i = 0; i < doctorPojectEdit.contentArr.length; i++) {
			if (data.field["content" + i + "doctorContentID"]) {
				doctor["doctorContent" + "[" + i + "]" + ".doctorContentID"] = data.field["content" + i + "doctorContentID"]
			}
			doctor['doctorContent' + "[" + i + "].contentSeq"] = data.field["content" + doctorPojectEdit.contentArr[i] + "contentSeq"]
			doctor['doctorContent' + "[" + i + "].contentDetail"] = data.field["content" + doctorPojectEdit.contentArr[i] + "contentDetail"]
			doctor['doctorContent' + "[" + i + "].contentMinus"] = data.field["content" + doctorPojectEdit.contentArr[i] + "contentMinus"]
			doctor['doctorContent' + "[" + i + "].contentScore"] = data.field["content" + doctorPojectEdit.contentArr[i] + "contentScore"]
			doctor['doctorContent' + "[" + i + "].contentStatus"] = data.field["content" + doctorPojectEdit.contentArr[i] + "contentStatus"]
			doctor['doctorContent' + "[" + i + "].contentRemark"] = data.field["content" + doctorPojectEdit.contentArr[i] + "contentRemark"]
		}
		if (data.field.pojectID) {
			$.ajax({
				url : basePath + "mdms/medoctorPoject/updateDoctorPoject.spring",
				type : "post",
				data : doctor,
				dataType : 'json',
			}).then(function(data) {
				assemblys.msg("修改成功", function() {
					parent.doctorPojectList.getDoctorPojectPager(parent.doctorPojectList.comanyNo);
					assemblys.closeWindow();
				});
			});
		} else {
			$.ajax({
				url : basePath + "mdms/medoctorPoject/saveDoctorPoject.spring",
				type : "post",
				data : doctor,
				dataType : 'json',
			}).then(function(data) {
				assemblys.msg("保存成功", function() {
					parent.doctorPojectList.getDoctorPojectPager(parent.doctorPojectList.comanyNo);
					assemblys.closeWindow();
				});
			});
		}
	},
	/**
	 * 点击新增
	 */
	contentBtn : function() {
		doctorPojectEdit.contentIndex++
		doctorPojectEdit.contentScord++
		doctorPojectEdit.contentArr.push(doctorPojectEdit.contentIndex)
		addOperation(".contentScore", null, "content", doctorPojectEdit.contentIndex, doctorPojectEdit.contentScord)
	},
	/**
	 * 点击考评内容删除
	 */
	deleteDoctorOperation : function(pojectCode) {
		var arrs = pojectCode.split(",");
		if (arrs.length > 2) {
			if (arrs[1] == "content") {
				$(".contentScore").find("tbody").find("tr").filter("." + arrs[2]).remove()
				doctorPojectEdit.contentArr.splice(doctorPojectEdit.contentArr.indexOf(arrs[2] * 1), 1)
				var doctorContentID = arrs[0]
				$.ajax({
					url : basePath + "mdms/medoctorPoject/deleteComponent.spring",
					type : "GET",
					data : {
						doctorContentID : doctorContentID
					},
					dataType : 'json',
				})
				doctorPojectEdit.addScore(null, null);
			}
		} else {
			if (arrs[0] == 'content') {
				(doctorPojectEdit.miniusScord-- <= 0 ? 1 : doctorPojectEdit.miniusScord)
				doctorPojectEdit.contentArr.splice(doctorPojectEdit.contentArr.indexOf(arrs[1] * 1), 1)
				$(".contentScore").find("tbody").find("tr").filter("." + arrs[1]).remove()
				doctorPojectEdit.addScore(null, null);
			}
		}
	},
	/**
	 * 根据项目库名称和类型查询如含有该数据则返回true
	 */
	search : function(data) {
		$.ajax({
			url : basePath + "mdms/medoctorPoject/findDoctorPoject.spring",
			type : "get",
			data : {
				pojectName : data.value,
				pojectType : $("select[name=pojectType]").val(),
				pojectIfMedical : param.get("pojectIfMedical")
			},
			dataType : 'json',
		}).then(function(data) {
			if (data.checked) {
				assemblys.msg("考评项目重复");
				$("textarea[name=pojectName]").val("")
			}
		});
	}
}
/**
 * 渲染加分项和减分项
 */
echoOperation = function(checkedName, type, key, value, echoType) {
	var pojectIndex = value["Component" + echoType + "ID"] + ',' + type + ',' + key
	if (type == "content") {
		pojectIndex = value.DoctorContentID + ',' + type + ',' + key
		var str = '<tr class="' + key + '">'
		str += '<td style="text-align:center">'
		str += '<i class="layui-icon layui-icon-delete i_delete ' + (param.get("search") ? "layui-hide" : "") + '" title="删除" onclick="doctorPojectEdit.deleteDoctorOperation(\'' + pojectIndex + '\')"></i>'
		str += '<input type="hidden" name="' + type + key + 'doctorContentID" value="' + value.DoctorContentID + '"/>'
		str += '</td>'
		var $disabled = "disabled=disabled";
		if (!param.get("search")) {
			$disabled = "";
		}
		str += '<td><input type="text"  style="border:none" autocomplete="off"  maxlength="3" lay-verify="number" name="' + type + key + 'contentSeq" value="' + value.ContentSeq + '" class="layui-input" ' + $disabled + ' /></td>';
		str += '<td><input type="text" lay-verify="required" autocomplete="off"  maxlength="200"  name="' + type + key + 'contentDetail" value="' + value.ContentDetail + '"  class="layui-input" ' + $disabled + ' /></td>'
		str += '<td><input type="text" lay-verify="required" autocomplete="off" maxlength="3" name="' + type + key + 'contentMinus" value="' + value.ContentMinus + '" class="layui-input " ' + $disabled + '  /></td>'
		str += '<td style="width:200px;text-align: center;"><input type="text" lay-verify="required|number|limit|integer"  autocomplete="off" maxlength="3" class="addPoint" onkeyup="doctorPojectEdit.addScore(this,this.value)" name="' + type + key + 'contentScore" value="' + value.ContentScore + '" class="layui-input ' + (param.get("search") ? "layui-disabled" : " ") + '"  /></td>'
		str += '<td  style="width:130px;" >'
		str += '<input type="radio" lay-verify="" name="' + type + key + 'contentStatus" value="0" title="停用" ' + (value.ContentStatus == 0 ? "checked" : "") + ' ' + (param.get("search") ? "disabled=disabled" : " ") + '>'
		str += '<input type="radio" lay-verify="" name="' + type + key + 'contentStatus" value="1" title="启用" ' + (value.ContentStatus == 1 ? "checked" : "") + ' ' + (param.get("search") ? "disabled=disabled" : " ") + '>'
		str += '</td>'
		str += '<td><input type="text" lay-verify="" autocomplete="off" maxlength="200"  name="' + type + key + 'contentRemark" class="layui-input" value="' + value.ContentRemark + '" ' + $disabled + '/></td>'
		str += '</tr>'
	}
	$(checkedName).append(str);
	layui.form.render();
	
	$("[name=" + type + key + 'contentStatus]').next().removeClass("layui-radio-disbaled").removeClass("layui-disabled");
}

/**
 * 添加操作减分项或加分项
 */
addOperation = function(checkedName, common, type, key, scord) {
	var optionHtml = "";
	if (doctorPojectEdit.groupList != null) {
		for (var i = 0; i < doctorPojectEdit.groupList.length; i++) {
			var t = doctorPojectEdit.groupList[i];
			optionHtml = optionHtml + '<option value=' + t.MTTeamCode + '>' + t.MTTeamName + '</option>';
		}
	}
	var pojectIndex = type + "," + key
	var str = '<tr class="' + key + '">'
	str += '<td style="text-align:center"><i class="layui-icon layui-icon-delete i_delete" title="删除" onclick="doctorPojectEdit.deleteDoctorOperation(\'' + pojectIndex + '\')" style="font-size:17px;"></i></td>'
	str += '<td><input type="text"  style="border:none" autocomplete="off"  maxlength="3" name="' + type + key + 'contentSeq" class="layui-input" lay-verify="number" value="' + scord + '"/></td>'
	str += '<td><input type="text" lay-verify="required"autocomplete="off" maxlength="200" name="' + type + key + 'contentDetail"  class="layui-input" /></td>'
	str += '<td><input type="text" lay-verify="required" autocomplete="off"  maxlength="200"  name="' + type + key + 'contentMinus"  class="layui-input" /></td>'
	str += '<td style="width:200px;text-align: center;"><input type="text" lay-verify="required|number|limit|integer" autocomplete="off"  maxlength="3" name="' + type + key + 'contentScore"  onkeyup="doctorPojectEdit.addScore(this,this.value)"  value="1"  class="addPoint layui-input" /></td>'
	str += '<td style="width:130px">'
	str += '<input type="radio" lay-verify="" name="' + type + key + 'contentStatus" value="0" title="停用" >'
	str += '<input type="radio" lay-verify="" name="' + type + key + 'contentStatus" value="1" title="启用" checked>'
	str += '</td>'
	str += '<td><input type="text" lay-verify="" autocomplete="off" maxlength="200" name="' + type + key + 'contentRemark"  class="layui-input" /></td></tr>'
	if (common) {
		if (type == 'content') {
			doctorPojectEdit.addArr = new Array(), doctorPojectEdit.miniusArr = new Array(), doctorPojectEdit.contentArr = new Array(), $(checkedName).append(common);
		}
	} else {
		if (type == 'content') {
			$(checkedName).append(str);
		}
	}
	layui.form.render()
}
/**
 * 禁用样式
 */
var disabledDom = function(index) {
	if (index.includes("select")) {
		$(index).prop("disabled", true);
		setTimeout(function() {
			$(index).next().find("div input").removeClass("layui-disabled");
		}, 200);
	} else {
		$(index).prop("disabled", "disabled")
	}
	
}