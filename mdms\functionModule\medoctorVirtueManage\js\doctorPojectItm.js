var supItem
var doctorPojectItem = {
	index : 0,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function(data) {
		});
		doctorPojectItem.getDoctorPoject().then(function() {
			doctorPojectItem.initLayui();
		});
	},
	initLayui : function() {
		layui.form.on("submit(save)", function() {
			doctorPojectItem.savePojectItems();
			return false;
		});
		layui.form.render();
	},
	getDoctorPoject : function() {
		return $.ajax({
			url : basePath + "mdms/medoctorPoject/getDoctorPojectList.spring",
			data : {
				company : param.get("compNo"),
				assessmentIfMedical : param.get("assessmentIfMedical"),
				controGroup : param.get("controGroup")
			}
		}).then(function(data) {
			
			supItem = layui.transfer.render({
				elem : '#supItem',
				showSearch : true,
				title : [ "未选", "已选" ],
				width : 388,
				value : parent.doctorAssessmentEdit.arrs,
				parseData : function(res) {
					return {
						"value" : res.pojectCode,
						"title" : res.pojectName,
						"pojectSumScore" : res.pojectSumScore,
						"pojectType" : res.pojectType
					}
				},
				onchange : function(object, index) {
					
					parent.$("input[name=assessmentPassScore]").val("");
					
					doctorPojectItem.index = index;
					
					if (index == 1) {
						for (var i = 0; i < object.length; i++) {
							//parent.doctorAssessmentEdit.deleteDoctorOperation(object[i].value + "-" + object[i].pojectSumScore, index);
							parent.doctorAssessmentEdit.deleteDoctorOperation(object[i].value + "-" + object[i].pojectSumScore, index, 1);
						}
						
					} else {
						
						// 2023/2/16 zh 项目设置
						parent.doctorAssessmentEdit.addItem(object, param.get("controGroup"));
						
					}
				},
				data : data.doctorPoject,
			});
			return data;
		});
	},
	savePojectItems : function() {
		if (window.isSubmit) {
			return;
		}
		
		parent.$.each(parent.$("input[name=assessmentIfMedical]"), function(i, e) {
			if (parent.$(e).next().attr("class").indexOf("layui-form-radioed") >= 0) {
				if (parent.$(e).val() == 1) {
					if (parent.doctorAssessmentEdit.whetherArrs.length == 0) {
						parent.doctorAssessmentEdit.whetherArrs = [];
						parent.doctorAssessmentEdit.whetherArrs = Object.assign([], parent.doctorAssessmentEdit.objArrs);
					} else {
						if (doctorPojectItem.index == 0) {
							$.each(parent.doctorAssessmentEdit.objArrs, function(i2, e2) {
								if (e2.value) {
									if (!doctorPojectItem.isExit(e2.value, 1)) {
										parent.doctorAssessmentEdit.whetherArrs.push(e2);
									}
								} else {
									if (!doctorPojectItem.isExit(e2.pojectCode, 1)) {
										parent.doctorAssessmentEdit.whetherArrs.push(e2);
									}
								}
							})
						}
					}
					
				} else {
					if (parent.doctorAssessmentEdit.notArrs.length == 0) {
						parent.doctorAssessmentEdit.notArrs = [];
						parent.doctorAssessmentEdit.notArrs = Object.assign([], parent.doctorAssessmentEdit.objArrs);
					} else {
						if (doctorPojectItem.index == 0) {
							$.each(parent.doctorAssessmentEdit.objArrs, function(i2, e2) {
								if (e2.value) {
									if (!doctorPojectItem.isExit(e2.value, 2)) {
										parent.doctorAssessmentEdit.notArrs.push(e2);
									}
								} else {
									if (!doctorPojectItem.isExit(e2.pojectCode, 2)) {
										parent.doctorAssessmentEdit.notArrs.push(e2);
									}
								}
							})
						}
					}
					
				}
			}
		})

		assemblys.closeWindow();
	},
	isExit : function(isCode, index) {
		
		if (index == 1) {
			for (var o = 0; o < parent.doctorAssessmentEdit.whetherArrs.length; o++) {
				var code = "";
				if (parent.doctorAssessmentEdit.whetherArrs[o].value) {
					code = parent.doctorAssessmentEdit.whetherArrs[o].value;
				} else {
					code = parent.doctorAssessmentEdit.whetherArrs[o].pojectCode;
				}
				
				if (code == isCode) {
					return true;
				}
			}
			
		} else {
			for (var o = 0; o < parent.doctorAssessmentEdit.notArrs.length; o++) {
				var code = "";
				if (parent.doctorAssessmentEdit.notArrs[o].value) {
					code = parent.doctorAssessmentEdit.notArrs[o].value;
				} else {
					code = parent.doctorAssessmentEdit.notArrs[o].pojectCode;
				}
				
				if (code == isCode) {
					return true;
				}
			}
		}
	}

}