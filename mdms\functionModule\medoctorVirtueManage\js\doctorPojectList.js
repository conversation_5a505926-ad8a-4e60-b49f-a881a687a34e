var doctorPojectList = {
	comanyNo : null,
	selectCompany : null,
	permission : null,
	cols : null,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]);
		/**
		 * 获取所有公司
		 */
		doctorPojectList.doctorComany();
		/**
		 * 获取当前用户的权限
		 */
		$.ajax({
			url : basePath + "mdms/EmployeeTrailWork/findPermissionAndUser.spring?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_POJECT,
			type : "get",
			dataType : 'json',
			async : false,
			success : function(data) {
				doctorPojectList.permission = data
			}
		})

		/**
		 * 是否含有添加功能权限
		 */
		if (doctorPojectList.permission.hasAdd == false) {
			$("button[addPoject]").hide()
		}
		var laydate = layui.laydate;
		
		//执行一个laydate实例
		laydate.render({
			elem : '#test1', //指定元素
			range : '~',
			trigger : 'click', //采用click弹出	
			done : function(value, date, endDate) {
				var dateStart = value.split("~")[0]
				var dateEnd = value.split("~")[1]

				var url = basePath + "mdms/medoctorPoject/getDoctorPojectPager.spring?" + param.__form() + "&" + filterParam.__form() + "&companyCode=" + doctorPojectList.comanyNo;// + "&dateStart=" + dateStart + "&dateEnd=" + dateEnd
				
				if (value != "") {
					url += "&dateStart=" + dateStart + "&dateEnd=" + dateEnd;
				}
				
				assemblys.tableRender({
					elem : '#list',
					url : url,
					cols : [ doctorPojectList.cols ],
					done : function(res, curr, count) {
						page.set("curPageNum", res.curPageNum);
						page.set("pageSize", res.pageSize);
						$("#filterNum").text(count);
					},
					events : {
						toEditDoctorPoject : doctorPojectList.toEditDoctorPoject,
						deleteDoctorPoject : doctorPojectList.deleteDoctorPoject,
						searchDoctorPoject : doctorPojectList.searchDoctorPoject
					}
				});
			}
		});
		var customBtnDom = [];
		if (doctorPojectList.permission.hasExec) {
			customBtnDom.push({
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : doctorPojectList.exportList
			});
		}
		
		filterSearch.init(basePath, doctorPojectList.getFilterParams({}), doctorPojectList.getDoctorPojectPager, customBtnDom);
		doctorPojectList.initLayuiForm();
		$("#keyword").attr("onkeydown", "if(event.keyCode == 13){return false;}");
	},
	doctorComany : function() {
		/**
		 * 获取所有公司
		 */
		$.ajax({
			url : basePath + "/frame/common/getMenuIcon.spring?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_POJECT + "&hasOrg=true",
			type : "GET",
			dataType : "json",
			async : false,
			success : function(data) {
				var arrs = data.compList.map(function(item, index) {
					return ('<option value="' + item.compNo + '">' + item.compName + '</option>');
				})
				$("select[name=compNo]").append(arrs);
			}
		})
		/**
		 * 选中公司的信息
		 */
		mdmsCommon.loadCompNo(function(data) {
			$("#test1").val("");
			doctorPojectList.comanyNo = data.value;
			doctorPojectList.getDoctorPojectPager(data.value);
		})

		layui.form.render()
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			doctorPojectList.getDoctorPojectPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "项目名称",
			title : "关键字",
			id : "keyword"
		} ];
		return params;
	},
	getDoctorPojectPager : function(company) {
		
		doctorPojectList.cols = [ {
			title : '操作',
			width : 105,
			minWidth : 95,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_delete" title="查看" lay-event="searchDoctorPoject"></i>	'
				if (doctorPojectList.permission.hasEdit) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditDoctorPoject"></i>';
				}
				if (doctorPojectList.permission.hasDel) {
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteDoctorPoject"></i>';
				}
				return html;
			}
		}, {
			title : '类型',
			align : "center",
			width : 150,
			minWidth : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.DictName);
			}
		}, {
			title : '项目名称',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectName);
			}
		}, {
			title : '适用人员',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectIfMedical);
			}
		}, {
			title : '项目得分',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectSumScore);
			}
		}, {
			title : '创建人',
			align : "center",
			width : 150,
			minWidth : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.UserName);
			}
		}, {
			title : '创建时间',
			align : "center",
			width : 180,
			minWidth : 180,
			templet : function(d) {
				return assemblys.dateToStr(d.pojectCreateDate);
			}
		}, {
			title : '项目说明',
			align : "center",
			width : 250,
			minWidth : 250,
			templet : function(d) {
				return assemblys.htmlEncode(d.pojectRemark != "" ? d.pojectRemark : "无");
			}
		}, {
			title : '状态',
			align : "center",
			width : 70,
			minWidth : 70,
			templet : function(d) {
				return (d.pojectStatus == 0 ? "<span style='color:red'>停用</span>" : "<span style='color:green'>启用</span>");
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/medoctorPoject/getDoctorPojectPager.spring?" + param.__form() + "&" + filterParam.__form() + "&companyCode=" + (company ? company : doctorPojectList.comanyNo),
			cols : [ doctorPojectList.cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditDoctorPoject : doctorPojectList.toEditDoctorPoject,
				deleteDoctorPoject : doctorPojectList.deleteDoctorPoject,
				searchDoctorPoject : doctorPojectList.searchDoctorPoject
			}
		});
		
	},
	/**
	 * 导出项目库信息
	 */
	exportList : function() {
		location.href = basePath + "mdms/medoctorPoject/exportList.spring?" + param.__form() + "&" + filterParam.__form() + "&companyCode=" + doctorPojectList.comanyNo;
	},
	/**
	 * 新增项目库信息
	 */
	toAddDoctorPoject : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditDoctorPoject",
			area : [ '96%', '96%' ],
			title : false,
			scrollbar : false,
			content : "doctorPojectEdit.html?funCode=" + param.get("funCode") + "&compNo=" + doctorPojectList.comanyNo
		});
	},
	/**
	 * 修改项目库信息
	 */
	toEditDoctorPoject : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditDoctorPoject",
			area : [ '96%', '96%' ],
			title : false,
			scrollbar : false,
			content : "doctorPojectEdit.html?funCode=" + param.get("funCode") + "&compNo=" + doctorPojectList.comanyNo + "&pojectID=" + d.pojectID + "&pojectCode=" + d.pojectCode
		});
	},
	/**
	 * 删除项目库信息
	 */
	deleteDoctorPoject : function(d) {
		assemblys.confirm("确认删除吗?", function() {
			return $.ajax({
				url : basePath + "mdms/medoctorPoject/deleteDoctorPoject.spring",
				type : "post",
				data : {
					pojectCode : d.pojectCode,
					pojectID : d.pojectID
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					doctorPojectList.getDoctorPojectPager();
				});
				return data;
			});
		})
	},
	/**
	 * 查看详情项目库信息
	 */
	searchDoctorPoject : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditDoctorPoject",
			area : [ '96%', '96%' ],
			title : false,
			scrollbar : false,
			content : "doctorPojectEdit.html?funCode=" + param.get("funCode") + "&compNo=" + doctorPojectList.comanyNo + "&pojectID=" + d.pojectID + "&pojectCode=" + d.pojectCode + "&search=true"
		});
	},
	/**
	 * 类型管理
	 */
	getDoctorPojectType : function() {
		var url = basePath + "mdms/functionModule/systemdictionary/systemDictionaryList.html?funCode=" + param.get("funCode") + "&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME + "&dictTypeCode=" + assemblys.top.mdms.mdmsConstant.DOCTORVIRTUETYPE + "&isOne=1";
		assemblys.top.addTab(null, '类型管理', url);
	}
}