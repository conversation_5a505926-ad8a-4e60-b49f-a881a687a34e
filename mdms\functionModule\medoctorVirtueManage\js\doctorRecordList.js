var doctorRecordList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : doctorRecordList.exportList
			} ];
			doctorRecordList.initLayuiForm();
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
		});
		$("#tabView li").click(function(data) {
			$.ajax({
				url : basePath + "mdms/medoctorRecord/getDoctorRecordPager.spring?state=" + $(this).attr("state"),
				type : "GET",
				dataType : "json",
				async : false,
				success : function(data) {
					
				}
			})
		})

		mdmsCommon.loadCompNo(function() {
			doctorRecordList.getDoctorRecordPager();
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			doctorRecordList.getDoctorRecordPager();
		});
		
		layui.form.render();
	},
	getDoctorRecordPager : function() {
		var cols = [ {
			title : '操作',
			align : "center",
			templet : function(d) {
				var html = '<i  class="layui-icon layui-icon-search i_delete" title="查看" lay-event="toSearchVeify"></i>';
				html += '<i  class="layui-icon layui-icon-form" title="确认汇总表" lay-event="toShowWord"></i>';
				return html;
			}
		}, {
			title : '考评名称',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentName);
			}
		}, {
			title : '考评范围',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentScopeName);
			}
		}, {
			title : '总分',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentSumScore);
			}
		}, {
			title : '合格分',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentPassScore);
			}
		}, {
			title : '自评人',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.WriteAssessmentUserCode + "/" + d.WriteAssessmentUserName);
			}
		}, {
			title : '自评得分',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.RercordGroupScore);
			}
		}, {
			title : '科评人',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.AssessmentUserCode + "/" + d.AssessmentUserName);
			}
		}, {
			title : '科评得分',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.RecordOneselfScore);
			}
		}, {
			title : '加减分总分',
			align : "center",
			templet : function(d) {
				var temp = d.addPoint - d.minusPoint;
				return temp;
			}
		}, {
			title : '最终得分',
			align : "center",
			templet : function(d) {
				return d.sumPoint;
			}
		}, {
			title : '结果',
			align : "center",
			templet : function(d) {
				var html = ""
				if (d.statusTitle == 0) {
					html += '<a  style="color:red;"  >较差</a>';
				} else if (d.statusTitle == 1) {
					html += '<a  style="color:RGB(199,174,109,109)"  >合格</a>';
				} else if (d.statusTitle == 2) {
					html += '<a  style="color:aquamarine"  >良好</a>';
				} else if (d.statusTitle == 3) {
					html += '<a  style="color:green"  >优秀</a>';
				}
				return html;
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/medoctorRecord/getDoctorRecordPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toSearchVeify : doctorRecordList.toSearchVeify,
				toShowWord : doctorRecordList.toShowWord
			}
		});
		
	},
	/**
	 * 查看考评详情
	 */
	toSearchVeify : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorTemplate.html?assessment=true&funCode=" + param.get("funCode") + "&assessmentCode=" + data.AssessmentCode + "&search=true&veify=true&UserName=" + encodeURI(data.WriteAssessmentUserName).replace(/%5B/g, '[').replace(/%5D/g, ']') + "&assessPojectUserCode=" + data.WriteAssessmentUserCode + "&examType=5";//+ "&assessmentUserCode=" + data.AssessmentUserCode;
		layer.open({
			title : '考评管理',
			content : url,
			maxmin : true,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	/**
	 * 确认汇总表查看
	 */
	toShowWord : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorShow.html?funCode=" + param.get("funCode") + "&assessmentCode=" + data.AssessmentCode + "&assessmentIfMedical=" + data.AssessmentIfMedical + "&addPoint=" + data.addPoint + "&minusPoint=" + data.minusPoint + "&addRemark=" + data.addRemark + "&minusRemark=" + data.minusRemark + "&WriteAssessmentUserName=" + data.WriteAssessmentUserName + "&userCode=" + data.WriteAssessmentUserCode + "&sumPoint=" + data.sumPoint
				+ "&statusTitle=" + data.statusTitle + "&addAndMinusTime=" + assemblys.dateToStr(data.AddAndMinusTime) + "&addAndMinusTimeEnd=" + assemblys.dateToStr(data.AddAndMinusTimeEnd) + "&rercordGroupScore=" + data.RercordGroupScore;
		doctorRecordList.pageIndex = layer.open({
			title : '确认汇总表',
			content : url,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	exportList : function() {
		location.href = basePath + "mdms/medoctorRecord/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	}
}