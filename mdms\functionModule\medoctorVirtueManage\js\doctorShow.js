$(function() {
	doctorShow.init();
});

// 界面操作
var doctorShow = {
	sumReigster : 0,
	sumUser : 0,
	sumPoject : 0,
	tempAddContent : "",
	tempAddDeptName : "",
	tempMinusContent : "",
	tempMinusDeptName : "",
	// 生成table
	init : function() {
		doctorShow.initShowHTML();
	},
	initShowHTML : function() {
		var now = new Date();
		var name = "";
		var reigsterSore = 0;
		var endTime = "";
		var html = '';
		html += '<div plate>';
		html += '<p style="text-align: center;">';
		html += '	<strong>';
		html += '		<span id="fileName" style="font-family: 宋体;color: rgb(34, 34, 34);letter-spacing: 0;font-size: 28px;background: rgb(255, 255, 255)">';
		if (param.get("assessmentIfMedical") == 1) {
			html += '考评汇总表（医务人员）<br/>';
		} else {
			html += '考评汇总表（非医务人员）<br/>';
		}
		html += '		</span>';
		html += '	</strong>';
		html += '</p>';
		
		html += '<table cellspacing="0" cellpadding="0" style=" border-style:none;border-collapse: collapse; font-size: 16px>';
		
		// 个人基本信息
		$.ajax({
			url : basePath + "mdms/medoctorShow/getUserBasic.spring",
			type : "get",
			dataType : "json",
			data : {
				writeAssessmentUserCode : param.get("userCode"),
				assessmentCode : param.get("assessmentCode"),
			},
			async : false,
			success : function(data) {
				var birthDate = "";
				if (data.birthDate) {
					var birthDateTemp = data.birthDate.split(" ");
					birthDate += birthDateTemp[0];
				}
				
				var tempTime = "";
				if (data.timeList) {
					var temp = assemblys.dateToStr(data.timeList).split(" ");
					tempTime += temp[0];
				} else {
					tempTime = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate();
				}
				endTime += tempTime;
				
				var userTitleTemp = ""
				if (typeof (data.userTitle2) == "undefined") {
					userTitleTemp += "";
				} else {
					userTitleTemp += data.userTitle2;
				}
				var deptName = data.deptTitle == "" ? data.user.deptName : data.deptTitle
				name += data.user.userName;
				
				var user = data.user;
				html += '	<tbody style="border : none;">';
				html += '		<tr>';
				html += '			<td height = 50px; width="176" style="border: 0px solid #fff;vertical-align: middle;text-align:right;"><strong>科室：</strong></td>';
				html += '			<td width="176" style="border: 0px solid #fff;vertical-align: middle;text-align:left;">' + data.deptTitle + '</td>';
				html += '			<td width="176" rowspan="1" colspan="2" style="border: 0px solid #fff;vertical-align: middle;"></td>';
				html += '			<td width="176" style="text-align:right;border: 0px solid #fff;vertical-align: middle;"><strong>填写时间：</strong></td>';
				html += '			<td width="176" style="text-align:left;border: 0px solid #fff;vertical-align: middle;">' + tempTime + '</td>';
				html += '		</tr>';
				html += '	</tbody>';
				html += '	<tbody style="border: 1px solid black; margin-top">';
				html += '		<tr style="text-align:center" >';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">姓 名</td>';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">' + param.get("WriteAssessmentUserName") + '</td>';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">工 号</td>';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">' + param.get("userCode") + '</td>';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">出生年月</td>';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">' + birthDate + '</td>';
				html += '		</tr>';
				html += '		<tr style="text-align:center">';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">政治面貌</td>';
				if (data.policitalStatus) {
					html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">' + data.policitalStatus + '</td>';
				} else {
					html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;"></td>';
				}
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">文化程度</td>';
				if (data.userEducation) {
					html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">' + data.userEducation + '</td>';
				} else {
					html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;"></td>';
				}
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">职 称</td>';
				html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;">' + userTitleTemp + '</td>';
				html += '		</tr>';
			}
		})

		html += doctorShow.createModel();
		
		// 具体考评项目遍历
		var emptyHtml = '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" ></td>';
		$.ajax({
			url : basePath + "mdms/medoctorShow/getAssessmentInfo.spring",
			type : "get",
			dataType : "json",
			data : {
				assessmentCode : param.get("assessmentCode"),
				writeAssessmentUserCode : param.get("userCode"),
				rercordGroupScore : param.get("rercordGroupScore")
			},
			async : false,
			success : function(data) {
				var info = data.Info;
				var tempSumPoject = 0;
				var tempSumUser = 0;
				var tempSumReigster = 0;
				for (var i = 0; i < info.length; i++) {
					reigsterSore += info[i].ReigsterSore;
					tempSumPoject += info[i].PojectSumScore;
					tempSumUser += info[i].usersore;
					tempSumReigster += info[i].RecordOneselfScore;
					html += '		<tr style="text-align:center">';
					html += '			<td rowspan="1" colspan="3" style="height : 50px;border : 1px solid black;border-style:soild;">' + info[i].PojectName + '</td>';
					if (info[i].PojectSumScore || info[i].PojectSumScore == 0) {
						html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" >' + info[i].PojectSumScore + '</td>';
					} else {
						html += emptyHtml;
					}
					if (info[i].usersore || info[i].usersore == 0) {
						html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" >' + info[i].usersore + '</td>';
					} else {
						html += emptyHtml;
					}
					if (info[i].ReigsterSore || info[i].ReigsterSore == 0) {
						html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" >' + info[i].ReigsterSore + '</td>';
					} else {
						html += emptyHtml;
					}
					html += '		</tr>';
				}
				doctorShow.sumPoject = tempSumPoject;
				doctorShow.sumUser = tempSumUser;
				doctorShow.sumReigster = tempSumReigster;
			}
		});
		
		// 尾部格式
		var addPoint = param.get("addPoint") != "" ? param.get("addPoint") : 0;
		var minusPoint = param.get("minusPoint") != "" ? param.get("minusPoint") : 0;
		var sumPoint = param.get("sumPoint") != "" ? param.get("sumPoint") : 0;
		var addRemark = param.get("addRemark");
		var minusRemark = param.get("minusRemark");
		var tempMinusPoint = Number(addPoint) - Number(minusPoint);
		var tempReigsterSore = (Number(reigsterSore) + Number(addPoint)) - Number(minusPoint);
		//var tempReigsterSore2 = isNaN(tempReigsterSore) ? ;
		
		html += '		<tr style="text-align:center">';
		html += '			<td rowspan="1" colspan="3" style="height : 50px;border : 1px solid black;border-style:soild;"> 合计 </td>';
		if (doctorShow.sumPoject) {
			html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" > ' + doctorShow.sumPoject + ' </td>';
		} else {
			html += emptyHtml;
		}
		if (doctorShow.sumUser) {
			html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" > ' + doctorShow.sumUser + ' </td>';
		} else {
			html += emptyHtml;
		}
		if (reigsterSore) {
			html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" > ' + reigsterSore + ' </td>';
		} else {
			html += emptyHtml;
		}
		html += '		</tr>';
		
		var addHtml = "";
		var minusHtml = "";
		var tempAddLength = 0;
		var tempMinusLength = 0;
		$.ajax({
			url : basePath + "mdms/medoctorShow/getAddAndMinusContent.spring",
			type : "get",
			dataType : "json",
			data : {
				addAndMinusTime : param.get("addAndMinusTime"),
				addAndMinusTimeEnd : param.get("addAndMinusTimeEnd"),
				writeAssessmentUserCode : param.get("userCode")
			},
			async : false,
			success : function(data) {
				var addContent = data.addContent;
				var addLength = data.addContent.length;
				
				if (addLength > 0) {
					for (var i = 0; i < addContent.length; i++) {
						addHtml += '		<tr>';
						addHtml += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">加分依据：' + addContent[i].addContent + '<br/>加分部门：' + addContent[i].DeptName + '<br/>多部门考评加分：' + addContent[i].Point + '<br/>  加分备注：' + addContent[i].Remark + '<br/>  </td>';
						addHtml += '		</tr>';
					}
					tempAddLength += addLength;
				} else {
					addHtml += '		<tr>';
					addHtml += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">加分依据：暂无<br/>加分部门：<br/>多部门考评加分：<br/>  加分备注： </td>';
					addHtml += '		</tr>';
					tempAddLength += 1;
				}
				
				var minusContent = data.minusContent;
				var minusLength = data.minusContent.length;
				if (minusLength > 0) {
					for (var i = 0; i < minusContent.length; i++) {
						minusHtml += '		<tr>';
						minusHtml += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">减分依据：' + minusContent[i].minusContent + '<br/>减分部门：' + minusContent[i].DeptName + '<br/>多部门考评减分：' + minusContent[i].Point + '<br/>  减分备注：' + minusContent[i].Remark + '<br/>  </td>';
						minusHtml += '		</tr>';
					}
					tempMinusLength += minusLength;
				} else {
					minusHtml += '		<tr>';
					minusHtml += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">减分依据：暂无<br/>减分部门：<br/>多部门考评减分：<br/>  减分备注：<br/>  </td>';
					minusHtml += '		</tr>';
					tempMinusLength += 1;
				}
			}
		});
		
		var tempLength = tempMinusLength + tempAddLength + 3

		html += '		<tr>';
		html += '			<td style="text-align:center;height : 50px;border : 1px solid black;border-style:soild;" rowspan="' + tempLength + '" colspan="3" "><strong>加减分情况</strong></td>';
		html += '		</tr>';
		//加载加分项
		html += addHtml;
		//加载减分项
		html += minusHtml;
		html += '		<tr>';
		if (minusPoint == 0) {
			html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">加分总分：' + addPoint + '<br/>减分总分：' + minusPoint + '</td>';
		} else {
			html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">加分总分：' + addPoint + '<br/>减分总分：-' + minusPoint + '</td>';
		}
		html += '		</tr>';
		html += '		<tr>';
		html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">加减分总分：' + (addPoint - minusPoint) + '</td>';
		html += '		</tr>';
		html += '		<tr>';
		html += '			<td rowspan="2" colspan="3" style="text-align:center;height : 50px;border : 1px solid black;border-style:soild;border-bottom:none;">考评小组综合评价及意见</td>';
		html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;border-top: 1px solid black;">该同志本次医德考核结果为（科评得分+加减分）：<br/>&nbsp;&nbsp;&nbsp;';
		if (param.get("statusTitle") == 3 && !isNaN(tempReigsterSore)) {
			html += '优秀  ' + tempReigsterSore;
		} else if (param.get("statusTitle") == 2 && !isNaN(tempReigsterSore)) {
			html += '良好  ' + tempReigsterSore;
		} else if (param.get("statusTitle") == 1 && !isNaN(tempReigsterSore)) {
			html += '合格  ' + tempReigsterSore;
		} else if (param.get("statusTitle") == 0 && !isNaN(tempReigsterSore)) {
			html += '较差  ' + tempReigsterSore;
		} else {
			html += '';
		}
		html += '			</td>';
		html += '		</tr>';
		html += '		<tr style="text-align:center;height : 50px;border : 1px solid black;border-style:soild;border-top:none;" >';
		if (param.get("statusTitle") == 3 && !isNaN(tempReigsterSore)) {
			html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">√优秀&nbsp;&nbsp;良好&nbsp;&nbsp;合格&nbsp;&nbsp;较差</td>';
		} else if (param.get("statusTitle") == 2 && !isNaN(tempReigsterSore)) {
			html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">优秀&nbsp;&nbsp;√良好&nbsp;&nbsp;合格&nbsp;&nbsp;较差</td>';
		} else if (param.get("statusTitle") == 1 && !isNaN(tempReigsterSore)) {
			html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">优秀&nbsp;&nbsp;良好&nbsp;&nbsp;√合格&nbsp;&nbsp;较差</td>';
		} else if (param.get("statusTitle") == 0 && !isNaN(tempReigsterSore)) {
			html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">优秀&nbsp;&nbsp;良好&nbsp;&nbsp;合格&nbsp;&nbsp;√较差</td>';
		} else {
			html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;">暂无结果</td>';
		}
		html += '		</tr>';
		html += '		<tr>';
		html += '			<td style="text-align:center;height : 50px;border : 1px solid black;border-style:soild;" rowspan="2" colspan="3">被考核人意见</td>';
		html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;border-top: 1px solid black;">签名：' + param.get("WriteAssessmentUserName") + '</td>';
		html += '		</tr>';
		html += '		<tr>';
		html += '			<td rowspan="1" colspan="3" style="height : 50px;border: 0px solid #fff;vertical-align: middle;border-right: 1px solid black;border-bottom: 1px solid black;">时间：' + endTime + '</td>';
		html += '		</tr>';
		html += '	</tbody>';
		html += '</table>';
		html += '</div>';
		
		$("#wordContent").html(html);
		layui.form.render();
	},
	// 导出word
	exportWord : function() {
		var exportWordText = "";
		// 获取非隐藏的板块
		$("#wordContent").find("div[plate]:visible").each(function() {
			exportWordText += $(this).clone().html();
		});
		// 先缓存后，再下载，因为数据量大！
		$.ajax({
			url : basePath + "frame/fileUpload/cacheWordData.spring",
			type : "post",
			data : {
				exportWordText : exportWordText,
				fileName : $("#fileName").text()
			},
			dataType : "json"
		}).then(function(data) {
			location.href = basePath + "frame/fileUpload/exportWord.spring";
		});
	},
	createModel : function() {
		var html = '';
		html += '		<tr style="text-align:center">';
		html += '			<td rowspan="1" colspan="3" style="height : 50px;border : 1px solid black;border-style:soild;"><strong>考评项目</strong></td>';
		html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" ><strong>基础得分</strong></td>';
		html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" ><strong>自评得分</strong></td>';
		html += '			<td width="176" style="height : 50px;border : 1px solid black;border-style:soild;" ><strong>科评得分</strong></td>';
		html += '		</tr>';
		return html;
	}
}
