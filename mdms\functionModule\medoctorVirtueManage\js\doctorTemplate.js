var doctorTemplate = {
	assessment : null,
	pojectCodeList : new Array(),
	doctorRegister : null,
	doctorUser : null,
	writerChecked : true,
	addComponentClassArr : new Array(),
	miniusComponentClassArr : new Array(),
	fileList : new Array(),
	submitIndex : 0,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]")).then(function(data) {
			var title = "考评填写";
			$("span[titleName]").text(title);
			param.set("compNo", param.get("newCompNo"));
		});
		doctorTemplate.initAssessment().then(function(data) {
			if (param.get("search") && param.get("veify") == "") {
				$("input[lay-filter=save]").remove()
			}
			if (param.get("veify") != "") {
				$("input[lay-filter=save]").val("提交")
				$("input[name=pushBtns]").val("草稿")
				$("#veifyDiv").removeClass("layui-hide")
			}
			// 2023/1/29 zh 保存自评和考评
			doctorTemplate.saveDoctorAssessment();
			
			if ($("#examType").val() == 5) {//浏览去掉删除和上传按钮
				$("#uploadBtn").addClass("layui-hide");
				$("a[class='cattachqueue-remove attaDelete']").empty();
			}

			// 2023/06/27 16:41:21 zh 调整高度
			if($(window).height() < 1000){
				$("#contentDiv").height($(window).height()-150);
			}
		});
		//hwx 2023-3-6 根据状态隐藏一键总分
		if (param.get("examType") == 1 && param.get("assessPojectStatus") == 2) {
			$("#oneKeyBtn").addClass("layui-hide");
		}
		
	},
	/**
	 * 初始化渲染
	 */
	initAssessment : function(assessmentData) {
		return doctorTemplate.getAssessmentComponent().then(function() {
			var url = "";
			// 2023/1/18 zh 完成考评后删除考评时间必填
			if (param.get("search") != "") {
				$("#recordDate").parent().prev().removeClass("layui-required");
			}
			url += basePath + "mdms/medoctorassessmentregister/findAssessmentRegister.spring?assessmentCode=" + param.get("assessmentCode") + "&assessPojectUserCode=" + param.get("assessPojectUserCode");
			return doctorTemplate.getAssessmentRegister(url);
		}).then(function(data) {
			if (typeof (assessmentData) == "undefined") {
				assessmentData = []
			}
			//删除项目下子元素
			$("#showItems").empty()
			//屏蔽当前状态
			if ((param.get("search") != "" && param.get("veify") == "" && param.get("assessment") == "") || (param.get("search") == "" && param.get("veify") == "" && param.get("assessment") == "")) {
				var css = {
					"background-color" : "orange",
					"padding" : "2px",
					"color" : "white"
				};
				// 2023/5/4 zh 完成自评状态
				if (param.get("assessPojectStatus") == 2) {
					$("span[name=nowAssessmentStatus]").text("考评中")
				} else {
					$("span[name=nowAssessmentStatus]").text("未考评")
				}
				$("span[name=nowAssessmentStatus]").css(css);
			}
			
			if (assessmentData.length > 0) {
				$("#veifyDiv").removeClass("layui-hide")
				var layDate = layui.laydate;
				layDate.render({
					elem : '#recordDate',
					min : doctorTemplate.getNowFormatDate(),
					value : layui.util.toDateString(assessmentData[0].RegisteViefyrWriteDate, "yyyy-MM-dd"),
				});
				$("#recordDate").addClass("layui-disabled").attr("disabled", "disabled")

			} else {
				$("#showPlanName").html("<font color='#028BFD' size='6' style='font-weight:600;'>" + doctorTemplate.assessment.assessmentName + "</font>");
				$("h1[name=assessmentName]").text(doctorTemplate.assessment.assessmentName)
				$("input[name=assessmentCode]").val(doctorTemplate.assessment.assessmentCode)
				if (doctorTemplate.assessment.assessmentMode.indexOf("月") >= 0) {
					$("span[name=assessmentTerm]").text(layui.util.toDateString(doctorTemplate.assessment.assessmentTerm, "MM月"));
				}
				if (doctorTemplate.assessment.assessmentMode.indexOf("年") >= 0) {
					$("span[name=assessmentTerm]").text(layui.util.toDateString(doctorTemplate.assessment.assessmentTerm, "yyyy年"));
				}
				if (doctorTemplate.assessment.assessmentMode.indexOf("季") >= 0) {
					$("span[name=assessmentTerm]").text(moment(doctorTemplate.assessment.assessmentTerm).format("YYYY[年]Q[季度]"));
					$("span[name=assessmentTerm]").parent().next().css("left", "696px").css("top", "-52px");
				}
				$("span[name=assessmentTerm]").css("font-weight", "600")
				var start = layui.util.toDateString(doctorTemplate.assessment.assessmentValidity, "yyyy-MM-dd")
				var end = layui.util.toDateString(doctorTemplate.assessment.assessmentValidityEnd, "yyyy-MM-dd")
				var date = start + " ~ " + end
				$("span[name=assessmentValidity]").text(date).css("font-weight", "600")
				$("span[name=assessmentSumScore]").text(doctorTemplate.assessment.assessmentSumScore).css("font-weight", "600")
				if (param.get("veify") != "" || param.get("search")) {
					// 2023/5/4 zh 完成自评状态
					if (param.get("assessPojectStatus") == 2) {
						$("span[name=assessmentStatus]").text("完成自评").css({
							"background-color" : "green",
							"padding" : "2px",
							"color" : "white"
						})
					}
				} else {
					$("span[name=assessmentStatus]").text("自评中").css({
						"background-color" : "green",
						"padding" : "2px",
						"color" : "white"
					})
				}
			}
		}).then(function(data) {
			$(".showRDiv").append('<div id="showComponent" class="scroll" style="position:absolute;left:800px;width:370px;top:25%;max-height:' + doctorTemplate.assessment.doctorAssessPojects.length * 410 + 'px;overflow:auto;"></div>')
			$(".showRDiv").append('<input type="hidden" name="assessmentSocre" value="' + doctorTemplate.assessment.assessmentPassScore + '"/>')
			var registerAddNum = 0;
			var registerMiniusNum = 0;
			$("#showComponent").empty();
			doctorTemplate.addComponentClassArr = [];
			doctorTemplate.miniusComponentClassArr = [];
			return doctorTemplate.redenAddAndRemove(assessmentData);
		}).then(function(data) {
			// 2023/2/14 zh 自评
			if (param.get("examType") != 1 && param.get("examType") != 2 && param.get("examType") != 3 && param.get("examType") != 5) {
				return doctorTemplate.getTeamGroup(assessmentData);
			} else {
				
				// 2023/2/14 zh 改变当前状态
				if (param.get("examType") == 2) {
					$("span[name=nowAssessmentStatus]").text("科主任考评中").css("color", "#fff");
					
					$("span[name=nowAssessmentStatus]").css({
						"background-color" : "orange",
						"padding" : "2px"
					});
					
					$("#showComponent").addClass("layui-hide");
				}
				//hwx 2023-3-6 修改显示草稿状态
				if (param.get("examType") == 1 && param.get("assessPojectStatus") == 6) {
					$("span[name=assessmentStatus]").text("草稿").css({
						"background-color" : "black",
						"padding" : "2px",
						"color" : "white"
					})
				}
				
//				if (param.get("examType") == 1) {
				$("#veifyDiv").removeClass("layui-hide");
				$("#recordDate").parent().addClass("layui-hide");
				$("#recordDate").parent().prev().addClass("layui-hide");
//				}
				
				// 2023/2/15 zh 改变当前状态
				if (param.get("examType") == 3) {
					$("span[name=nowAssessmentStatus]").text("多部门联合考评中").css("color", "#fff");
					
					$("span[name=nowAssessmentStatus]").css({
						"background-color" : "orange",
						"padding" : "2px"
					});
					
					$("#showComponent").addClass("layui-hide");
				}
				
				if (param.get("multiDeptStatus") == 1) {
					$("span[name=assessmentStatus]").text("多部门联合考评中").css("background-color", "orange");
					
				}
				
				// 2023/2/15 zh 改变当前状态
				if (param.get("examType") == 5) {
					
					$("span[name=nowAssessmentStatus]").text("完成考评").css("color", "#fff");
					$("span[name=assessmentStatus]").text("完成自评").css("color", "#fff").css("background-color", "green");
					
					$("span[name=nowAssessmentStatus]").css({
						"background-color" : "green",
						"padding" : "2px"
					});
					
					$("#showComponent").addClass("layui-hide");
					$("#pushApproval").addClass("layui-hide");
					$("#pushBtn").addClass("layui-hide");
					$("#pushBtns").addClass("layui-hide");
					$("#oneKeyBtn").addClass("layui-hide");
					$("#savePreserve").addClass("layui-hide");
				}
				
				//控制考评填写详情中，自评得分和说明  禁用
				if (param.get("examType") == 1 && param.get("assessPojectStatus") == 2) {
					$("input[name*='contentSore']").attr("disabled", "disabled");
					$("textarea[name*='contentRemark']").attr("disabled", "disabled");
				}
				
				//控制考评记录详情中，科评得分和说明  禁用
				//param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_ME_DOCTOR_RECORD
				if (param.get("examType") == 5) {
					$("input[name*='registerSorce']").attr("disabled", "disabled");
					$("textarea[name*='registerRemark']").attr("disabled", "disabled");
				}
				
				// 2023/2/14 zh 自评屏蔽左边
				$("#leftPack").addClass("layui-hide");
				return data;
			}
		}).then(function(data) {
			
			// 2023/2/14 zh 自评
			if (param.get("examType") != 1 && param.get("examType") != 2 && param.get("examType") != 3 && param.get("examType") != 5) {
				var arr = data.arr;
				var userSeach = data.userSeach;
				var checkedStatus = data.checkedStatus;
				var ulIndexClick = data.ulIndexClick;
				var nulIndexClick = data.nulIndexClick;
				for (var i = 0; i < arr.length; i++) {
					$("#userLiVeify" + arr[i] + "").removeClass("layui-disabled").removeAttr("disabled")
				}
			}
			
			if (assessmentData.length > 0 || param.get("veify")) {
				doctorTemplate.finshDom(assessmentData);
			}
			
			if (doctorTemplate.doctorUser || assessmentData.length > 0) {
				for (var i = 0; i < doctorTemplate.pojectCodeList.length; i++) {
					$("#veifyId" + doctorTemplate.pojectCodeList[i].index + doctorTemplate.pojectCodeList[i].pojectCode + "").removeClass("layui-hide")
				}
				/**
				 * 当前小组名称
				 */
				$(".showRDiv").append('<input type="hidden" name="groupName" value="' + doctorTemplate.doctorUser.MTTeamName + '"/>')
			}
			
			/**
			 * 是否当前用户考评对该自评人的考评是否完成
			 */
			if (checkedStatus) {
				$("span[name=assessmentStatus]").text("已考评完").css("background-color", "aquamarine")
			}
			
			if (param.get("veify") != "" && !doctorTemplate.writerChecked && assessmentData.length == 0) {
				$(".fr").empty()
				$(".fr").append('<input type="button" class="layui-btn layui-btn-sm" value="考评" id="pushBtn" lay-submit lay-filter="save" />')
				$("span[name=nowAssessmentStatus]").text("未评").css({
					"background-color" : "orange",
					"padding" : "2px",
					"color" : "white"
				})
			} else if ((param.get("veify") != "" && !doctorTemplate.writerChecked) || (param.get("assessment") != "" && assessmentData.length > 0)) {
				$("#pushBtn").removeClass("layui-hide")
				$("#pushBtns").removeClass("layui-hide")
				$("#pushBtn").val("导出")
				$("#pushBtn").attr("lay-filter", "download")
			}
			return data;
		}).then(function(data) {
			$("#showType").text(doctorTemplate.assessment.assessmentMode);
			$("#showItemNum").text(doctorTemplate.assessment.doctorAssessPojects.length);
			$("#assessmentSumScore").text(doctorTemplate.assessment.assessmentSumScore);
			$("#assessmentPassScore").text(doctorTemplate.assessment.assessmentPassScore);
			
			$(".showRDiv").append("<div id='hiddenDiv'></div>");
			$("#hiddenDiv").empty();
			if (param.get("veify") != "" || param.get("search")) {
				$("#userName").val(param.get("UserName"));
				$("#userCode").val(param.get("assessPojectUserCode"));
				
				return doctorTemplate.getDoctorRecord().then(function(result) {
					$("#hiddenDiv").append('<input type="hidden" name="rercordGroupScore" value="' + result.doctorRecord.rercordGroupScore + '"/>')
					$("#hiddenDiv").append('<input type="hidden" name="recordScore" value="' + result.doctorRecord.recordScore + '"/>')
					$("#hiddenDiv").append('<input type="hidden" name="recordID" value="' + result.doctorRecord.recordID + '"/>')
					$("#hiddenDiv").append('<input type="hidden" name="writeAssessmentUserCode" value="' + result.doctorRecord.writeAssessmentUserCode + '"/>')
					return result;
				})
			}
			
		});
	},
	// 监听自评和考评得分不能大于总分
	listenChangeScore : function(data, datas, pojectCode, value) {
		
		if ($("input[name=pojectSore" + data + pojectCode + "]").length > 0) {
			if (datas.value > Number.parseInt($("input[name=pojectSore" + data + pojectCode + "]").val())) {
				assemblys.msg("不能大于基础得分")
				if (value) {
					$("input[name=registerSorce" + data + pojectCode + "]").val("")
				} else {
					$("input[name=assessmentSore" + data + pojectCode + "]").val("")
				}
			}
		}
		
		if ($("input[name=doctorContentSore" + data + pojectCode + "]").length > 0) {
			if (datas.value > Number.parseInt($("input[name=doctorContentSore" + data + pojectCode + "]").val())) {
				assemblys.msg("不能大于基础得分");
				if (value) {
					$("input[name=registerSorce" + data + pojectCode + "]").val("")
				} else {
					$("input[name=contentSore" + data + pojectCode + "]").val("")
				}
			}
		}
		
	},
	save : function(register) {
		// 短信中打开自评
		var index = parent.layer.getFrameIndex(window.name);
		// 是否存在
		var checked = true
		var addAndMinusFileList = [];
		$("#ueditorFileDiv-0").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			typeFiles.attaAssessmentCode = $("#assessmentCode").val();
			typeFiles.attaUserCode = $("#userCode").val();
			addAndMinusFileList.push(typeFiles);
		});
		register.fileListJson = JSON.stringify(addAndMinusFileList);
		// 判断是否是短信
		if (index) {
			$.ajax({
				url : basePath + "mdms/medoctorassessmentregister/findAssessmentRegister.spring",
				method : "GET",
				data : {
					assessmentCode : param.get("assessmentCode")
				},
				contentType : "application/json;charset=utf-8",
				async : false,
				dataType : 'json'
			})
		}
		// 判断是否重复
		if (checked) {
			//防止重复提交
			if (doctorTemplate.submitIndex == 0) {
				$.ajax({
					url : basePath + "mdms/medoctorassessmentregister/insertAssessmentRegister.spring",
					method : "POST",
					data : JSON.stringify(register),
					contentType : "application/json;charset=utf-8",
					async : false,
					dataType : 'json',
					success : function(data) {
						doctorTemplate.submitIndex++;
						assemblys.msg("保存成功", function() {
							if (parent.doctorUserList) {
								parent.doctorUserList.init()
								parent.layer.close(parent.doctorUserList.pageIndex);
							} else {
								assemblys.top.pubSystem.workDesk();
							}
							assemblys.top.closeTab('考评管理');
						});
					}
				})
			}
		}
	},
	//返回现在时分秒
	getNowFormatDate : function() {
		
		var date = new Date();
		var seperator1 = "-";
		var seperator2 = ":";
		var month = date.getMonth() + 1;
		var strDate = date.getDate();
		if (month >= 1 && month <= 9) {
			month = "0" + month;
		}
		if (strDate >= 0 && strDate <= 9) {
			strDate = "0" + strDate;
		}
		var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate + " " + date.getHours() + seperator2 + date.getMinutes() + seperator2 + date.getSeconds();
		return currentdate;
	},
	// 刷新改变加分项和减分项的元素
	finshDom : function(assessmentData) {
		if (param.get("assessment") != "" || param.get("veify") != "") {
			for (var i = 0; i < doctorTemplate.addComponentClassArr.length; i++) {
				$("input[name=addScore" + doctorTemplate.addComponentClassArr[i].index + doctorTemplate.addComponentClassArr[i].pojectCode + "]").next().addClass("layui-form-checked layui-disabled")
			}
		}
		if (param.get("assessment") != "" || param.get("veify") != "") {
			for (var i = 0; i < doctorTemplate.miniusComponentClassArr.length; i++) {
				$("input[name=miniusScore" + doctorTemplate.miniusComponentClassArr[i].index + doctorTemplate.miniusComponentClassArr[i].pojectCode + "]").next().addClass("layui-form-checked layui-disabled")
			}
		}
	},
	/**
	 * 递归获取进行算出top的高度
	 */
	topFnction : function(top, length, index) {
		if (length == index) {
			return top;
		} else {
			top += -418
			index++;
			return doctorTemplate.topFnction(top, length, index);
		}
	},
	/**
	 * 关闭表单
	 */
	closeDoctorAssessment : function(message) {
		if (message != "") {
			var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
			layer.msg(message);
			setTimeout(function() {
				if (param.get("veify") == "" && param.get("search") == "" && message != "") {
					if (message != "" && typeof (parent.doctorUserList) == "undefined" && index) {
						parent.layer.close(index); //再执行关闭 
					} else if (message != "" && parent.doctorUserList && index) {
						parent.doctorUserList.init()
						parent.layer.close(parent.doctorUserList.pageIndex);
					} else {
						assemblys.top.pubSystem.workDesk();
					}
				}
			}, 1400);
		}
	},
	/**
	 * 保存自评和考评
	 */
	saveDoctorAssessment : function() {
		layui.form.on('submit(save)', function(data) {
			var DoctorassessmentregisterListInitParam = {
				"doctorRegister" : new Array(),
				"doctorassessmentregister" : new Array(),
				"doctorRecord" : {},
				"tempStatus" : "",
				"doctorMultideptExams" : new Array()
			}, addIndex = 0, addArr = new Array();
			DoctorassessmentregisterListInitParam.assessmentCode = data.field.assessmentCode
			DoctorassessmentregisterListInitParam.assessmentSocre = data.field.assessmentSocre
			DoctorassessmentregisterListInitParam.insertVeify = (param.get("veify") != "" ? "true" : "false")
			DoctorassessmentregisterListInitParam.assessPojectUserCode = param.get("assessPojectUserCode")
			if (param.get("veify") != "") {
				DoctorassessmentregisterListInitParam.doctorRecord.assessmentGroupName = data.field.groupName
				DoctorassessmentregisterListInitParam.doctorRecord.writeAssessmentUserCode = data.field.userCode
				DoctorassessmentregisterListInitParam.doctorRecord.rercordGroupScore = data.field.rercordGroupScore
				DoctorassessmentregisterListInitParam.doctorRecord.recordScore = data.field.recordScore
				DoctorassessmentregisterListInitParam.doctorRecord.recordID = data.field.recordID
				DoctorassessmentregisterListInitParam.doctorRecord.rercordVeifyDate = $("#recordDate").val()

				DoctorassessmentregisterListInitParam.examType = param.get("examType");
				
				var registerScore = 0, pojectSumScore = 0, registerDeptScore = 0, registerDeptName = "", registerWriteDate = "";
				
				for (var i = 0; i < doctorTemplate.assessment.doctorAssessPojects.length; i++) {
					for (var j = 0; j < doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject.length; j++) {
						var poject = {
							"assessmentCode" : data.field.assessmentCode,
							"pojectCode" : doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode,
							"reigsterSore" : data.field["registerSorce" + j + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode],
							"reigsterRemark" : data.field["registerRemark" + j + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode],
							"doctorAdd" : new Array(),
							"doctorMinius" : new Array(),
							"registeViefyrWriteDate" : $("#recordDate").val(),
						}

						pojectSumScore += doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode;
						// 2023/2/15 zh 获取科室考评内容
						if (param.get("examType") == 2) {
							if (doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent) {
								for (var c = 0; c < doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent.length; c++) {
									poject = {};
									poject["assessmentCode"] = data.field.assessmentCode;
									poject["pojectCode"] = doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode;
									poject["userSore"] = data.field["registerSorce" + c + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode];
									poject["userRemark"] = data.field["registerRemark" + c + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode];
									poject["reigsterRemark"] = data.field["registerRemark" + c + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode];
									poject["reigsterSore"] = data.field["registerSorce" + c + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode];
									poject["writeUserCode"] = data.field.userCode;
									DoctorassessmentregisterListInitParam.doctorassessmentregister.push(poject)
									DoctorassessmentregisterListInitParam.doctorRegister.push(poject);
								}
							}
						} else {
							DoctorassessmentregisterListInitParam.doctorassessmentregister.push(poject)
						}
					}
				}
			} else {
				for (var i = 0; i < doctorTemplate.assessment.doctorAssessPojects.length; i++) {
					for (var j = 0; j < doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject.length; j++) {
						var poject = {
							"assessmentCode" : data.field.assessmentCode,
							"pojectCode" : doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode,
							"userSore" : data.field["assessmentSore" + j + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode],
							"userRemark" : data.field["assessmentRemark" + j + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode],
						}

						if (doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent) {
							for (var c = 0; c < doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent.length; c++) {
								poject = {};
								poject["assessmentCode"] = data.field.assessmentCode;
								poject["pojectCode"] = doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode;
								poject["userSore"] = data.field["contentSore" + c + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode];
								poject["userRemark"] = data.field["contentRemark" + c + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode];
								DoctorassessmentregisterListInitParam.doctorassessmentregister.push(poject)
							}
						} else {
							DoctorassessmentregisterListInitParam.doctorassessmentregister.push(poject)
						}
						
					}
				}
			}
			DoctorassessmentregisterListInitParam.tempStatus = $(this).val(); //
			doctorTemplate.save(DoctorassessmentregisterListInitParam)
			return false; // 阻止表单跳转。如果需要表单跳转，去掉这段即可。
		});
		
	},
	/**
	 * 获取组件
	 */
	getAssessmentComponent : function() {
		return $.ajax({
			url : basePath + "mdms/medoctorassessmentregister/findAssessmentComponent.spring?assessmentCode=" + param.get("assessmentCode") + "&assessPojectUserCode=" + param.get("assessPojectUserCode") + "&examType=" + param.get("examType"),
			type : "GET",
			dataType : "json",
			success : function(data) {
				doctorTemplate.assessment = data.assessmentComponent;
				doctorTemplate.fileList = data.fileList;
				doctorTemplate.initTypeFile(doctorTemplate.fileList);
				var message = ""
				if (!doctorTemplate.assessment) {
					message = '考评已关闭';
					doctorTemplate.closeDoctorAssessment(message);
				}
			}
		})
	},
	/**
	 * 获取自评和考评信息
	 */
	getAssessmentRegister : function(url) {
		return $.ajax({
			url : url,
			type : "GET",
			dataType : "json",
			success : function(data) {
				doctorTemplate.doctorRegister = data.doctorRegister;
				return data;
			}
		})
	},
	/**
	 * 渲染加减分
	 */
	redenAddAndRemove : function(assessmentData) {
		
		for (var i = 0; i < doctorTemplate.assessment.doctorAssessPojects.length; i++) {
			for (var j = 0; j < doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject.length; j++) {
				var itemStr = '<div class="showTypeDiv"><div class="showType">';
				var scoreHtml = '<label class="showLabel">项目得分：<span class="showSpan">' + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectSumScore + '</span></label>';
				itemStr += '<span class="showTileSpan">' + (i + 1) + " " + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectName + '</span><span class="showTitleRight">' + scoreHtml + '</span></label></div></div>'
				itemStr = doctorTemplate.echoDoctorPoject(doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j], j, itemStr, 0);
				// 2023/2/13 zh 考评项目下子项目
				if (doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent) {
					for (var c = 0; c < doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent.length; c++) {
						itemStr = doctorTemplate.echoDoctorPoject(doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c], c, itemStr, (i + 1));
						
						if (doctorTemplate.doctorRegister != null && doctorTemplate.doctorRegister.length > 0) {
							for (var r = 0; r < doctorTemplate.doctorRegister.length; r++) {
								if (doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c].contentCode == doctorTemplate.doctorRegister[r].pojectCode) {
									itemStr = doctorTemplate.echoSelfAssess(2, itemStr, c, doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c], doctorTemplate.doctorRegister[r]);
								}
							}
						} else {
							itemStr = doctorTemplate.echoSelfAssess(1, itemStr, c, doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c]);
						}
						if (param.get("veify") != "") {
							var ReigsterRemark, ReigsterSore, RegisteViefyrWriteDate, component, pojectCodes;
							if (param.get("assessment") || assessmentData.length > 0) {
								for (var d = 0; d < assessmentData.length; d++) {
									if (doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode == assessmentData[d].PojectCode) {
										ReigsterRemark = assessmentData[d].ReigsterRemark
										ReigsterSore = assessmentData[d].ReigsterSore
										RegisteViefyrWriteDate = assessmentData[d].RegisteViefyrWriteDate
										component = assessmentData[d].component
										pojectCodes = assessmentData[d].PojectCode
									}
								}
							}
							var num = 0;
							for (var n = 0; n < doctorTemplate.doctorRegister.length; n++) {
								if (doctorTemplate.doctorRegister[n].registerStatus == 1) {
									num++;
								}
							}
							// 2023/2/15 zh 科评
							if (param.get("examType") == 2 && num == 0) {
								itemStr = doctorTemplate.echoDeptDoctorAssess(itemStr, c, doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].doctorContent[c], ReigsterSore, ReigsterRemark);
							}
							itemStr += '</div>'
						}
					}
					
				} else {
					if (doctorTemplate.doctorRegister != null && doctorTemplate.doctorRegister.length > 0) {
						for (var r = 0; r < doctorTemplate.doctorRegister.length; r++) {
							if (doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode == doctorTemplate.doctorRegister[r].pojectCode) {
								itemStr = doctorTemplate.echoSelfAssess(2, itemStr, c, doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j], doctorTemplate.doctorRegister[r]);
							}
						}
					} else {
						itemStr = doctorTemplate.echoSelfAssess(1, itemStr, j, doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j]);
					}
					if (param.get("veify") != "") {
						var ReigsterRemark, ReigsterSore, RegisteViefyrWriteDate, component, pojectCodes;
						if (param.get("assessment") || assessmentData.length > 0) {
							for (var d = 0; d < assessmentData.length; d++) {
								if (doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode == assessmentData[d].PojectCode) {
									ReigsterRemark = assessmentData[d].ReigsterRemark
									ReigsterSore = assessmentData[d].ReigsterSore
									RegisteViefyrWriteDate = assessmentData[d].RegisteViefyrWriteDate
									component = assessmentData[d].component
									pojectCodes = assessmentData[d].PojectCode
								}
							}
						}
						// 2023/2/15 zh 科室考评
						if (param.get("examType") == 2) {
							itemStr = doctorTemplate.echoDeptDoctorAssess(itemStr, j, doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j], ReigsterSore, ReigsterRemark);
						}
					}
				}
				doctorTemplate.pojectCodeList.push({
					"index" : j,
					"pojectCode" : parseInt(doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectCode)
				})
				if (param.get("veify") != "") {
					var componentStr = '';
					componentStr += '<div  id="componentDIV' + i + '" style="height:400px"><div><fieldset class="layui-elem-field layui-field-title">'
					componentStr += '<legend>' + doctorTemplate.assessment.doctorAssessPojects[i].doctorPoject[j].pojectName + '</legend>'
					componentStr += '<div class="layui-field-box"><div class="layui-collapse"><div class="layui-colla-item"><h2 class="layui-colla-title">加分项    +</h2>'
					componentStr += '<div class="layui-colla-content scroll layui-show" id="addComponent' + i + '" style="overflow:auto;max-width:400px;max-height:150px;" >'
					componentStr += '</div></div> <div class="layui-colla-item"><h2 class="layui-colla-title">减分项    -</h2>'
					componentStr += '<div class="layui-colla-content scroll layui-show" id="miniusComponent' + i + '" style="overflow:auto;max-width:400px;max-height:150px;" >'
					componentStr += '</div></div></div></div></fieldset></div></div>'
					$("#showComponent").append(componentStr)

					$("#showItems").append(itemStr)
					layui.element.render('collapse');
					layui.form.render();
				} else {
					$("#showItems").append(itemStr)
				}
				
			}
		}
		
	},
	/**
	 * 获取考评记录
	 */
	getDoctorRecord : function() {
		return $.ajax({
			url : basePath + "mdms/medoctorRecord/getDoctorRecord.spring",
			type : "GET",
			data : {
				"writeAssessmentUserCode" : param.get("assessPojectUserCode"),
				"assessmentCode" : param.get("assessmentCode"),
				//"assessmentCode" : doctorTemplate.doctorRegister[0].assessmentCode,
				"recordStatus" : 0
			},
			dataType : "json"
		})
	},
	/**
	 * 显示已考评的加减分
	 */
	echoAddAndRemove : function() {
		if (param.get("assessment") != "") {
			for (var i = 0; i < doctorTemplate.addComponentClassArr.length; i++) {
				$("input[name=addScore" + doctorTemplate.addComponentClassArr[i].index + doctorTemplate.addComponentClassArr[i].pojectCode + "]").next().addClass("layui-form-checked layui-disabled")
			}
		}
		if (param.get("assessment") != "") {
			for (var i = 0; i < doctorTemplate.miniusComponentClassArr.length; i++) {
				$("input[name=miniusScore" + doctorTemplate.miniusComponentClassArr[i].index + doctorTemplate.miniusComponentClassArr[i].pojectCode + "]").next().addClass("layui-form-checked layui-disabled")
			}
		}
	},
	/**
	 * 显示自评
	 */
	echoSelfAssess : function(controIndex, itemStr, j, poject, doctorRegister) {
		if (controIndex == 1) {
			itemStr += '<div class="layui-form-item" ><label class="layui-form-label layui-required" style="margin-top: -7px;">自评得分</label><div class="layui-input-inline" style="width:50px">'

			if (poject.contentCode) {
				itemStr += '<input type="text"   lay-verify="required|number|limit|integer"   name="contentSore' + j + poject.contentCode + '" placeholder="" autocomplete="off" class="layui-input assessmentSore showInput" onkeyup="doctorTemplate.listenChangeScore(' + j + ',this,\'' + poject.contentCode + '\')">'

			}
			
			itemStr += '</div><label class="layui-form-label">自评说明</label><div class="layui-input-inline">';//自评说明
			
			if (poject.contentCode) {
				itemStr += '<textarea  placeholder="请输入内容" maxLength="200"  class="layui-textarea"   name="contentRemark' + j + poject.contentCode + '"></textarea>'
			}
			
			itemStr += '</div>'
		} else {
			
			itemStr += '<div class="layui-form-item" >'

			if (param.get("search")) {
				
				// 2023/2/15 zh 科室考评
				if (doctorRegister.reigsterSore || doctorRegister.reigsterSore == "0") {
					if (param.get("examType") == 5) {
						itemStr += '<label class="layui-form-label showLabel">科评得分</label>'
					} else {
						itemStr += '<label class="layui-form-label showLabel"><font color="red">*</font>科评得分</label>'
					}
				} else {
					itemStr += '<label class="layui-form-label showLabel">自评得分</label>'
				}
				
			} else {
				itemStr += '<label class="layui-form-label">自评得分</label>'
			}
			
			itemStr += '<div class="layui-input-inline" style="width:50px">'

			if (poject.contentCode) {
				
				// 2023/2/15 zh 科室考评
				if (doctorRegister.reigsterSore || doctorRegister.reigsterSore == "0") {
					itemStr += '<input type="text" name="registerSorce' + j + poject.contentCode + '" placeholder=""  value="' + doctorRegister.reigsterSore + '" autocomplete="off" class="layui-input" onkeyup="doctorTemplate.listenChangeScore(' + j + ',this,\'' + poject.contentCode + '\',1)" />'
				} else {
					//取消自评填写和科室考评disabled="disabled" 和  layui-disabled hwx 2023-5-4 只有自评编辑与科评编辑可填
					if (param.get("assessPojectStatus") == 6 || param.get("assessPojectStatus") == 7) {
						itemStr += '<input type="text" name="contentSore' + j + poject.contentCode + '" placeholder=""  value="' + doctorRegister.userSore + '" autocomplete="off" class="layui-input" onkeyup="doctorTemplate.listenChangeScore(' + j + ',this,\'' + poject.contentCode + '\',1)" />'
					} else {
						itemStr += '<input type="text"   disabled="disabled" name="contentSore' + j + poject.contentCode + '" placeholder=""  value="' + doctorRegister.userSore + '" autocomplete="off" class="layui-input layui-disabled" />'
					}
				}
				
			}
			
			if (param.get("search")) {
				itemStr += '</div><label class="layui-form-label showLabel">自评说明</label>';//自评说明
			} else {
				itemStr += '</div><label class="layui-form-label">自评说明</label>';//自评说明
			}
			
			itemStr += '<div class="layui-input-inline">'

			if (poject.contentCode) {
				// 2023/2/15 zh 科室考评
				if (doctorRegister.reigsterSore || doctorRegister.reigsterSore == "0") {
					itemStr += '<textarea  placeholder="请输入内容"   class="layui-textarea "   name="registerRemark' + j + poject.contentCode + '">' + doctorRegister.reigsterRemark + '</textarea>'
				} else {
					//取消自评填写和科室考评disabled="disabled" 和  layui-disabled hwx 2023-5-4 只有自评编辑与科评编辑可填
					if (param.get("assessPojectStatus") == 6 || param.get("assessPojectStatus") == 7) {
						itemStr += '<textarea  placeholder="请输入内容"   class="layui-textarea"   name="contentRemark' + j + poject.contentCode + '">' + doctorRegister.userRemark + '</textarea>'
					} else {
						itemStr += '<textarea  placeholder="请输入内容"  disabled="disabled" class="layui-textarea layui-disabled"   name="contentRemark' + j + poject.contentCode + '">' + doctorRegister.userRemark + '</textarea>'
					}
				}
			}
			
			itemStr += '</div>'
		}
		
		return itemStr;
	},
	/**
	 * 显示项目
	 */
	echoDoctorPoject : function(poject, j, itemStr, num) {
		if (poject.contentCode) {
			itemStr += '<div class="layui-form-item" style="margin-top:20px;" ><label class="layui-form-label showLabel">' + num + "-" + (j + 1) + ' 基础得分</label>'
			itemStr += '<div class="layui-input-inline" style="width:50px"><input  type="text" name="doctorContentSore' + j + poject.contentCode + '" disabled="disabled" placeholder="" value="' + poject.contentScore + '" autocomplete="off" class="layui-input layui-disabled" ">'
			itemStr += '</div>'
			itemStr += '<label class=" layui-form-label showLabel"> 考评内容: </label><div class="layui-input-inline">'
			itemStr += '<textarea name="desc" style="border:0px;"  placeholder="请输入内容" class="layui-textarea layui-disabled"  disabled="disabled">'

			if (poject.contentDetail) {
				itemStr += poject.contentDetail;
			}
			itemStr += "</textarea></div>";
			
			// 2023/2/14 zh 扣分标准
			if (param.get("examType") == 2) {
				itemStr += '<div class="layui-form-item" >';
				itemStr += '<label class=" layui-form-label " style="visibility:hidden;"> 扣分标准: </label><div class="layui-input-inline" style="width:50px">'
				itemStr += '<input  type="text"  style="visibility:hidden;"  placeholder="请输入内容" class="layui-input layui-disabled"  disabled="disabled">'
				itemStr += "</input></div>";
				
				itemStr += '<label class=" layui-form-label showLabel"> 扣分标准: </label><div class="layui-input-inline">'
				itemStr += '<textarea name="desc" style="width: 655px" placeholder="请输入内容" class="layui-textarea layui-disabled"  disabled="disabled">'
				if (poject.contentMinus) {
					itemStr += poject.contentMinus;
				}
				itemStr += "</textarea>";
				itemStr += '</div>';
			}
			itemStr += "</textarea></div></div>";
		} else {
			itemStr += '<label class=" layui-form-label showLabel">项目说明: </label><div class="layui-input-inline">'
			itemStr += '<span class="showTypeSpans">'

			if (poject.pojectRemark && poject.pojectRemark == "") {
				itemStr += ''
			} else {
				itemStr += poject.pojectRemark;
			}
			itemStr += "</span></div></div>";
		}
		return itemStr;
		
	},
	/**
	 * 显示科室考评
	 */
	echoDeptDoctorAssess : function(itemStr, j, poject, ReigsterSore, ReigsterRemark) {
		if (poject.contentCode) {
			itemStr += '<div class="layui-form-item" id="veifyId' + j + poject.contentCode + '">'
		} else {
			itemStr += '<div class="layui-form-item" id="veifyId' + j + poject.pojectCode + '">'
		}
		if (ReigsterSore) {
			itemStr += '<label class="layui-form-label showLabel">科室得分</label>'
		} else {
			itemStr += '<label class="layui-form-label layui-required" style="margin-top: -7px;">科室考评</label>'
		}
		
		itemStr += '<div class="layui-input-inline" style="width:50px">'

		if (ReigsterSore) {
			if (poject.contentCode) {
				itemStr += '<input type="text"   lay-verify="required|number|limit|integer"   placeholder="" name="registerSorce' + j + poject.contentCode + '" autocomplete="off" class="layui-input layui-disabled" disabled="disabled" value="' + ReigsterSore + '"  onkeyup="doctorTemplate.listenChangeScore(' + j + ',this,\'' + poject.contentCode + '\',1)">'
			}
		} else {
			if (poject.contentCode) {
				itemStr += '<input type="text"   lay-verify="required|number|limit|integer"   placeholder="" name="registerSorce' + j + poject.contentCode + '" autocomplete="off" class="layui-input showInput"  onkeyup="doctorTemplate.listenChangeScore(' + j + ',this,\'' + poject.contentCode + '\',1)">'
			}
		}
		
		itemStr += '</div>'

		if (ReigsterRemark || param.get("veify") != "") {
			itemStr += '<label class="layui-form-label showLabel">科室考评说明</label>'
		} else {
			itemStr += '<label class="layui-form-label">科室考评说明</label>'
		}
		
		itemStr += '<div class="layui-input-inline">'

		if (ReigsterRemark || (ReigsterRemark == "" && param.get("veify") != "")) {
			if (poject.contentCode) {
				itemStr += '<textarea  name="registerRemark' + j + poject.contentCode + '" placeholder="请输入内容" class="layui-textarea ' + ((ReigsterRemark || ReigsterRemark == "") ? "layui-disabled" : "") + '" disabled="disabled" >' + ReigsterRemark + '</textarea>'
			} else {
				itemStr += '<textarea  name="registerRemark' + j + poject.pojectCode + '" placeholder="请输入内容" class="layui-textarea ' + ((ReigsterRemark || ReigsterRemark == "") ? "layui-disabled" : "") + '" disabled="disabled" >' + ReigsterRemark + '</textarea>'
			}
		} else {
			if (poject.contentCode) {
				itemStr += '<textarea maxLength="200"  name="registerRemark' + j + poject.contentCode + '" placeholder="请输入内容" class="layui-textarea" ></textarea>'
			} else {
				itemStr += '<textarea maxLength="200"  name="registerRemark' + j + poject.pojectCode + '" placeholder="请输入内容" class="layui-textarea" ></textarea>'
			}
		}
		
		return itemStr;
	},
	//hwx 2023-2-23 对现有填写的内容一键总分
	oneKeyBtn : function() {
		$("input").each(function() {
			var uid = $(this);
			if (uid.hasClass("assessmentSore")) {
				var uName = uid.attr("name");
				if (uName.indexOf("contentSore") > -1) {
					var ucode = uName.substring(11, uName.length);
					var preCode = "doctorContentSore" + ucode;
					var preVal = $("input[name='" + preCode + "']").val();
					if (preVal) {
						uid.val(preVal);
					}
				}
			} else if (uid.attr("name") && uid.attr("name").indexOf("registerSorce") > -1) {
				var uName = uid.attr("name");
				var ucode = uName.substring(13, uName.length);
				var preCode = "doctorContentSore" + ucode;
				var preVal = $("input[name='" + preCode + "']").val();
				if (preVal) {
					uid.val(preVal);
				}
			}
		})

	},
	attaCallback : function(result) {// 自定义上传图片后的回调
		var fileHtml = "";
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		for (var i = 0; i < result.length; i++) {
			fileHtml += "<li style='width: 500px;'>";
			fileHtml += "	<em title=\"" + result[i].title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + result[i].title + "&nbsp;&nbsp;" + result[i].size + "</em>";
			var suffix = result[i].type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" id=\"preview\" onclick=\"pubUploader.preview('" + result[i].title + "','" + result[i].url + "');\"  >预览图片</a></span>";
			}
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + result[i].title + "','" + result[i].url + "');\">下载</a></span>";
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove attaDelete\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a></span>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + result[i].title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + result[i].url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + result[i].size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + result[i].type + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaAssessmentCode\"   value=\"" + $("#assessmentCode").val() + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUserCode\"   value=\"" + $("#userCode").val() + "\"/>";
			fileHtml += "</li>";
		}
		$("#ueditorFileDiv-0").append(fileHtml);
		if (param.get("showOrEdit") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
	},
	initTypeFile : function(meetingRecordFileList) {
		var filesData = meetingRecordFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.title = typeFileTemp.attaName;
				files.url = typeFileTemp.attaUrl;
				files.size = typeFileTemp.attaSize;
				files.type = typeFileTemp.attaType;
				files.attaAssessmentCode = $("#assessmentCode").val();
				files.attaUserCode = $("#userCode").val();
				result.push(files);
			}
			doctorTemplate.attaCallback(result);
		}
	},
	insertfileHtml : function() {
		var fileHtml = '<fieldset id="fileDiv" class="layui-elem-field layui-hide">';
		fileHtml = fileHtml + ' <legend>附件上传</legend>';
		fileHtml = fileHtml + '<div class="layui-field-box">';
		fileHtml = fileHtml + ' <div class="layui-form-item">';
		fileHtml = fileHtml + '<label class="layui-form-label"> 附件 </label>';
		fileHtml = fileHtml + '<div class="layui-input-inline">';
		fileHtml = fileHtml + '<input type="button" value="上传附件" id="uploadBtn" class="layui-btn layui-btn-sm" onclick="pubUploader.openFiles(doctorTemplate.attaCallback);param.set("fileIndex", 0);" />';
		fileHtml = fileHtml + '  <div class="collapse in">';
		fileHtml = fileHtml + ' <ul class="cattachqueue" id="ueditorFileDiv-0"></ul>';
		fileHtml = fileHtml + ' <ul class="cattachqueue" id="ueditorFileDiv-0"></ul>';
		fileHtml = fileHtml + ' </div>';
		fileHtml = fileHtml + '  </div>';
		fileHtml = fileHtml + ' </div>';
		fileHtml = fileHtml + '  </div>';
		fileHtml = fileHtml + '</fieldset> ';
		return fileHtml;
	}
}