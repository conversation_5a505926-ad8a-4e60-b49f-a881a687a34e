var doctorUserList = {
	permission : null,
	/**
	 * 当前iframe页面索引
	 */
	pageIndex : 0,
	init : function() {
		
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			return doctorUserList.getOrgDeptList();
		}).then(function() {
			return doctorUserList.getUserRight();
		}).then(function() {
			doctorUserList.doctorPagerInit().then(function(data) {
				// 2023/2/14 zh 自评
				if (param.get("examType") == 1) {
					$("li[state=0]").addClass("layui-this");
					$("#deptID").parent().addClass("layui-hide");
				}
				// 2023/2/14 zh 科室考评
				if (param.get("examType") == 2) {
					$("li[state=2]").addClass("layui-this");
				}
				layui.form.render();
				// 2023/2/14 zh 选择公司
				doctorUserList.selectedCompNo();
				// 2023/2/14 zh 选择部门
				doctorUserList.selectedDept();
				//hwx 2023-5-18 判断部门含有本科室默认显示
				var $deptId = data.deptId;
				if ($deptId) {
					$("#deptID option").each(function() {
						var selectVal = $(this).val();
						if (selectVal == $deptId) {
							var filter = $("#deptID").attr('lay-filter');
							$("#deptID").val(selectVal);
							layui.form.render();
							layui.event('form', 'select(' + filter + ')', {
								elem : $("#deptID"),
								value : selectVal
							});
						}
					});
				}
			});
		})

	},
	/**
	 * 初始化调用方法
	 */
	doctorPagerInit : function() {
		return $.ajax({
			url : basePath + "mdms/medoctorassessmentregister/doctorPagerInit.spring",
			async : false
		}).then(function(data) {
			return data;
		});
	},
	/**
	 * 获取列表数据
	 */
	getDoctorPager : function() {
		
		var cols = [ [ {
			title : '操作',
			align : "center",
			width : 80,
			templet : function(d) {
				var html = "";
				//自评
				if (d.assessPojectStatus == 1) {
					if ((!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) || (assemblys.dateToStr(d.assessmentValidity, "yyyy-MM-dd HH:mm") > assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm"))) {
						html += "";
					} else {
						html += '<i class="layui-icon layui-icon-edit i_delete" title="填写" lay-event="toForm"></i>';
					}
				}
				//自评草稿修改保存
				if (d.assessPojectStatus == 6) {
					//自评超时
					// 2023/2/28 zh 补考状态
					if (!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
						html += "";
					} else {
						html += '<i class="layui-icon layui-icon-edit i_delete" title="修改填写" lay-event="toForm"></i>';
					}
				}
				//待科评
				if (d.assessPojectStatus == 2) {
					//考评超时
					// 2023/2/28 zh 补考状态
					if (!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
						html += "";
					} else {
						//科室考评
						if (doctorUserList.hasEdit) {
							html += '<i  class="layui-icon" title="科室考评" lay-event="toDeptVeify">&#xe60a;</i>';
						}
						// 2023/3/6 zh 可以看到填完后的记录
						if (param.get("examType") == 1) {
							html += '<i  class="layui-icon layui-icon-search i_delete" title="查看" lay-event="toSearch"></i>';
						}
					}
					
				}
				//科评草稿修改保存
				if (d.assessPojectStatus == 7) {
					//自评超时
					// 2023/2/28 zh 补考状态
					if (!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
						html += "";
					} else {
						html += '<i class="layui-icon layui-icon-edit i_delete" title="修改填写" lay-event="toDeptVeify"></i>';
					}
				}
				// 2023/2/15 zh 完成考评
				if (d.assessPojectStatus == 5) {
					html += '<i  class="layui-icon layui-icon-search i_delete " title="查看" lay-event="toSearchVeify"></i>';
					html += '<i  class="layui-icon layui-icon-form" title="确认汇总表" lay-event="toShowWord"></i>';
				}
				return html;
			}
		}, {
			title : '考评方式',
			align : "center",
			width : 90,
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentMode)
			}
		}, {
			title : '考评名称',
			align : "center",
			width : 250,
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentName)
			}
		}, {
			title : '状态',
			align : "center",
			width : 130,
			templet : function(d) {
				var html = "";
				//自评
				if (d.assessPojectStatus == 1) {
					if (assemblys.dateToStr(d.assessmentValidity, "yyyy-MM-dd HH:mm") > assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
						html += '<a  style="color:grey;"  >未开始</a>';
					} else {
						//自评超时
						// 2023/2/28 zh 补考状态
						if (!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
							html += '<a  style="color:red;"  >未填写已过期</a>';
						} else {
							if (d.makeUpStatus == 1) {
								html += '<a  style="color:red;"  >补考</a>';
							} else {
								html += '<a  style="color:red;"  >未填写</a>';
							}
						}
					}
					
				}
				//自评草稿
				if (d.assessPojectStatus == 6) {
					//自评超时
					// 2023/2/28 zh 补考状态
					if (!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
						html += '<a  style="color:red;"  >填写未提交已过期</a>';
					} else {
						html += '<a  style="color:red;"  >填写未提交</a>';
					}
				}
				//科评
				if (d.assessPojectStatus == 2) {
					//考评超时
					// 2023/2/28 zh 补考状态
					if (!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
						html += '<a  style="color:red;"  >未科评已过期</a>';
					} else {
						html += '<a  style="color:red;"  >待科评</a>';
					}
				}
				//科评草稿
				if (d.assessPojectStatus == 7) {
					//自评超时
					// 2023/2/28 zh 补考状态
					if (!d.makeUpStatus && assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd HH:mm") < assemblys.dateToStr(new Date, "yyyy-MM-dd HH:mm")) {
						html += '<a  style="color:red;"  >填写未提交已过期</a>';
					} else {
						html += '<a  style="color:red;"  >填写未提交</a>';
					}
				}
				
				if (d.assessPojectStatus == 5) {
					html += '<a  style="color:green;"  >完成考评</a>';
				}
				return html;
			}
		}, {
			title : '总分',
			align : "center",
			width : 74,
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentSumScore);
			}
		}, {
			title : '合格分',
			align : "center",
			width : 74,
			templet : function(d) {
				return assemblys.htmlEncode(d.assessmentPassScore);
			}
		}, {
			title : '自评得分',
			align : "center",
			width : 90,
			templet : function(d) {
				if (d.rercordGroupScore) {
					return d.rercordGroupScore;
				} else {
					return "无自评";
				}
			}
		}, {
			title : '自评人',
			align : "center",
			width : 120,
			templet : function(d) {
				return assemblys.htmlEncode(d.userName);
			}
		}, {
			title : '有效期',
			align : "center",
			width : 120,
			templet : function(d) {
				return assemblys.dateToStr(d.assessmentValidityEnd, "yyyy-MM-dd");
			}
		}, {
			title : '填写时间',
			align : "center",
			width : 130,
			templet : function(d) {
				var RercoreWriteDate = d.rercoreWriteDate;
				if (RercoreWriteDate) {
					RercoreWriteDate = assemblys.dateToStr(RercoreWriteDate, "yyyy-MM-dd");
				} else {
					RercoreWriteDate = "无";
				}
				return RercoreWriteDate;
			}
		}, {
			title : '考评备注',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode((d.assessmentRemark ? d.assessmentRemark : "无"))
			}
		} ] ]

		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/medoctorassessmentregister/getdoctorassessmentregisterPager.spring?" + param.__form(),
			page : false,
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			cols : cols,
			events : {
				toForm : doctorUserList.toForm,
				toSearch : doctorUserList.toSearch,
				toDeptVeify : doctorUserList.toDeptVeify,
				toShowWord : doctorUserList.toShowWord,
				toSearchVeify : doctorUserList.toSearchVeify
			}
		});
	},
	/**
	 * 查看详情
	 */
	toSearch : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorTemplate.html?funCode=" + param.get("funCode") + "&assessmentCode=" + data.assessmentCode + "&search=true&UserName=" + data.userName + "&assessPojectUserCode=" + data.assessPojectUserCode;
		url += "&examType=" + param.get("examType") + "&multiDeptStatus=" + data.multiDeptStatus
		url += "&assessPojectStatus=" + data.assessPojectStatus
		layer.open({
			title : '考评管理',
			maxmin : true,
			content : url,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	//自评
	toForm : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorTemplate.html?funCode=" + param.get("funCode") + "&assessmentCode=" + data.assessmentCode + "&assessPojectUserCode=" + data.assessPojectUserCode;
		url += "&examType=" + param.get("examType")
		url += "&assessPojectStatus=" + data.assessPojectStatus
		url += "&UserName=" + data.userName
		doctorUserList.pageIndex = layer.open({
			title : '考评管理',
			maxmin : true,
			content : url,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	//科室考评
	toDeptVeify : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorTemplate.html?funCode=" + param.get("funCode") + "&assessmentCode=" + data.assessmentCode + "&veify=true&search=true&UserName=" + data.userName + "&assessPojectUserCode=" + data.assessPojectUserCode;
		url += "&examType=" + param.get("examType")
		url += "&assessPojectStatus=" + data.assessPojectStatus
		doctorUserList.pageIndex = layer.open({
			maxmin : true,
			title : '考评管理',
			content : url,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	//汇总表导出预览
	toShowWord : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorShow.html?funCode=" + param.get("funCode") + "&userCode=" + data.assessPojectUserCode + "&UserName=" + data.userName + "&assessmentCode=" + data.assessmentCode + "&addPoint=" + data.addPoint + "&minusPoint=" + data.minusPoint + "&addRemark=" + data.addRemark + "&minusRemark=" + data.minusRemark + "&WriteAssessmentUserName=" + data.userName + "&sumPoint=" + data.sumPoint + "&statusTitle=" + data.statusTitle
				+ "&assessmentIfMedical=" + data.AssessmentIfMedical + "&addAndMinusTimeEnd=" + assemblys.dateToStr(data.AddAndMinusTimeEnd) + "&addAndMinusTime=" + assemblys.dateToStr(data.AddAndMinusTime) + "&rercordGroupScore=" + data.rercordGroupScore;
		doctorUserList.pageIndex = layer.open({
			title : '确认汇总表',
			maxmin : true,
			content : url,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	/**
	 * 查看考评信息
	 */
	toSearchVeify : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorTemplate.html?assessment=true&funCode=" + param.get("funCode") + "&assessmentCode=" + data.assessmentCode + "&search=true&veify=true&UserName=" + data.userName + "&assessPojectStatus=" + data.assessPojectStatus + "&assessPojectUserCode=" + data.assessPojectUserCode;
		if (data.assessPojectStatus == 5) {
			url += "&examType=5"
		}
		layer.open({
			title : '考评管理',
			content : url,
			maxmin : true,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	/**
	 * 查询
	 */
	btnSearch : function() {
		doctorUserList.getDoctorPager();
	},
	/**
	 * 获取科室考评
	 */
	getOrgDeptList : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			data : {
				"funCode" : param.get("funCode"),
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			async : false,
			skipDataCheck : true, //- 如果接口响应回来的数据有问题，请增加该参数
			success : function(data) {
				var orgDeptList = data == undefined ? [] : data.deptList;
				if (orgDeptList.length > 0) {
					$("#deptID").empty();
					for (var i = 0; i < orgDeptList.length; i++) {
						var dept = orgDeptList[i];
						$("#deptID").append('<option  value="' + dept.DeptID + '" >' + dept.DeptName + '</option>');
					}
				}
			}
		})
	},
	// 选择公司
	selectedCompNo : function() {
		var form = layui.form;
		
		// 监听公司
		mdmsCommon.loadCompNo(function(data) {
			// 重新加载科室
			doctorUserList.getOrgDeptList().then(function() {
				form.render();
				$($("#deptID").next().find("dd")[0]).click();
			});
		});
	},
	// 选择部门
	selectedDept : function() {
		var form = layui.form;
		
		form.on("select(dept)", function(data) {
			doctorUserList.getDoctorPager();
			doctorUserList.getUserRight();
		})
	},
	// 获取权限
	getUserRight : function() {
		return $.ajax({
			url : basePath + "mdms/EmployeeTrailWork/findPermissionAndUser.spring",
			data : {
				"funCode" : param.get("funCode"),
			},
			dataType : "json",
			success : function(data) {
				doctorUserList.hasEdit = data.hasEdit;
			}
		});
	}
}
