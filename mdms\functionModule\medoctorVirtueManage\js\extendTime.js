var extendTime = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		var laydate = layui.laydate;
		
		var dateIndex = laydate.render({
			elem : '#starTime', // 指定元素
			type : 'datetime',
			format : "yyyy-MM-dd HH:mm:ss",
			value : param.get("startDate")
		})
		
		var dateIndex = laydate.render({
			elem : '#endTime', // 指定元素
			trigger : "click",
			type : 'datetime',
			min : param.get("endDate"),
			format : "yyyy-MM-dd HH:mm:ss",
			value : param.get("endDate")
		})
		
		extendTime.initLayui();
	},
	initLayui : function (){
		//提交延长时间
		layui.form.on("submit(save)", function(data) {
			var endTime = data.field.endTime;
			extendTime.saveEndDate(endTime);
			return false;
		});
	},
	saveEndDate : function (endTime){
		var url = basePath + "mdms/medoctorAssessment/updateTime.spring"
		$.ajax({
			url : url,
			type : "post",
			async : false,
			data : {
				"assessmentCode" : param.get("assessmentCode"),
				"assessmentValidityEnd" : endTime
			}
		}).then(function(data) {
			assemblys.msg("修改成功", function() {
				parent.location.reload();
				//parent.doctorAssessmentEdit.getDoctorAssessmentPager();
				assemblys.closeWindow();
			});
		})
	}
}
