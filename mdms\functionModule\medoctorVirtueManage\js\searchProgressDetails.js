var searchProgressDetails = {
	// 表格列
	cols : null,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]")).then(function() {
			searchProgressDetails.initBasicData().then(function() {
				searchProgressDetails.getSearchProgressDetailsPager();
				searchProgressDetails.initLayuiForm();
			});
		});
	},
	exportList : function() {
		location.href = basePath + "mdms/meaddAndMinus/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	initLayuiForm : function() {
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			searchProgressDetails.getSearchProgressDetailsPager();
		});
		
		layui.form.render();
	},
	initBasicData : function() {
		return $.ajax({
			url : basePath + "mdms/medoctorAssessment/initBasicData.spring?",
			type : "get",
			dataType : 'json',
			async : false,
			data : {
				assessmentCode : param.get("assessmentCode")
			},
			success : function(data) {
				//获取基本信息
				var number = data.number;
				var data = data.list;
				if (data.assessmentScope == assemblys.top.mdms.mdmsConstant.DOCTOR_USER) {
					$("#showPlanName").append('<font style="font-size: 20px; text-align: center; font-weight:600;">【' + data.assessmentName + '】多医师</font>');
				} else if (data.assessmentScope == assemblys.top.mdms.mdmsConstant.DOCTOR_DEPT) {
					$("#showPlanName").append('<font color="#3298C8" style="font-size: 20px; font-weight:600;margin-left:20px;">【' + data.assessmentName + '】多科室</font>');
				} else {
					$("#showPlanName").append('<font color="#3298C8" style="font-size: 20px; font-weight:600;margin-left:20px;">【' + data.assessmentName + '】全院区</font>');
				}
				
				if (data.assessmentFrequency == assemblys.top.mdms.mdmsConstant.DOCTOR_YEAR) {
					$("span[name=assessmentTerm]").text(layui.util.toDateString(data.assessmentTerm, "yyyy年"));
					$("span[name=assessmentType]").text("年度考评");
				} else if (data.assessmentFrequency == assemblys.top.mdms.mdmsConstant.DOCTOR_MONTHLY) {
					$("span[name=assessmentTerm]").text(layui.util.toDateString(data.assessmentTerm, "MM月"));
					$("span[name=assessmentType]").text("月度考评");
				} else {
					$("span[name=assessmentTerm]").text(moment(data.assessmentTerm).format("YYYY[年]Q[季度]"));
					$("span[name=assessmentTerm]").parent().next().css("left", "696px").css("top", "-52px");
					$("span[name=assessmentType]").text("季度考评");
				}
				
				var start = layui.util.toDateString(data.assessmentValidity, "yyyy-MM-dd");
				var end = layui.util.toDateString(data.assessmentValidityEnd, "yyyy-MM-dd");
				var tempDate = start + " ~ " + end;
				$("span[name=assessmentValidity]").text(tempDate).css("font-weight", "600");
				$("span[name=assessmentSumScore]").text(data.assessmentSumScore).css("font-weight", "600");
				$("span[name=assessmentPassScore]").text(data.assessmentPassScore).css("font-weight", "600");
				
				if (number != 0) {
					$("span[name=assessmentNumber]").text(number + "个项目");
				} else {
					$("span[name=assessmentNumber]").text("暂无项目");
				}
				
				//自评进度条
				if (param.get("selfScore") != 0) {
					var selfScore = param.get("selfScore");
					var selfSumScore = param.get("selfSumScore");
					var tatol = (selfScore / selfSumScore) * 100;
					tatol = tatol.toFixed(0);
					$("td[name=assessmentSelfProgress]").append('<div class="layui-progress layui-progress-big" lay-showPercent="true"><div class="layui-progress-bar skin-btn-main" lay-percent="' + tatol + '%" style="width:' + tatol + '%;"><span class="layui-progress-text">' + tatol + '%</span></div></div>');
				} else {
					$("td[name=assessmentSelfProgress]").append('<div class="layui-progress layui-progress-big" lay-showPercent="true"><div class="layui-progress-bar skin-btn-main" lay-percent="0%" style="width:0%;"><span class="layui-progress-text">0%</span></div></div>');
				}
				//自评进度条
				if (param.get("deptScore") != 0) {
					var deptScore = param.get("deptScore");
					var deptSumScore = param.get("deptSumScore");
					var tatol = (deptScore / deptSumScore) * 100;
					tatol = tatol.toFixed(0);
					$("td[name=assessmentDeptProgress]").append('<div class="layui-progress layui-progress-big" lay-showPercent="true"><div class="layui-progress-bar skin-btn-main" lay-percent="' + tatol + '%" style="width:' + tatol + '%;"><span class="layui-progress-text">' + tatol + '%</span></div></div>');
				} else {
					$("td[name=assessmentDeptProgress]").append('<div class="layui-progress layui-progress-big" lay-showPercent="true"><div class="layui-progress-bar skin-btn-main" lay-percent="0%" style="width:0%;"><span class="layui-progress-text">0%</span></div></div>');
				}
				
			}
		})
	},
	/**
	 * 初始化表格信息
	 */
	getSearchProgressDetailsPager : function() {
		searchProgressDetails.cols = [ {
			title : '操作',
			width : 200,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i  class="layui-icon layui-icon-form" title="确认汇总表" lay-event="searchProgress"></i>';
				return html;
			}
		}, {
			title : '科室',
			align : "center",
			minWidth : 200,
			templet : function(d) {
				return assemblys.htmlEncode(d.DeptName);
			}
		}, {
			title : '姓名',
			align : "center",
			width : 200,
			templet : function(d) {
				return assemblys.htmlEncode(d.UserCode + "/" + d.UserName);
			}
		}, {
			title : '自评得分',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.RercordGroupScore);
			}
		}, {
			title : '科评得分',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.RecordOneselfScore);
			}
		}, {
			title : '加减总分',
			align : "center",
			templet : function(d) {
				var tempPoint = 0;
				if (d.tempPoint) {
					tempPoint = assemblys.htmlEncode(d.tempPoint)
				}
				return tempPoint;
			}
		}, {
			title : '最终得分',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.RecordOneselfScore == "" ? "" : d.sumPoint);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/medoctorAssessment/getSearchProgressDetailsPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ searchProgressDetails.cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			
			events : {
				searchProgress : searchProgressDetails.searchProgress
			}
		});
	},
	searchProgress : function(data) {
		var url = basePath + "mdms/functionModule/medoctorVirtueManage/doctorShow.html?funCode=" + param.get("funCode") + "&assessmentCode=" + data.AssessmentCode + "&assessmentIfMedical=" + data.AssessmentIfMedical + "&addPoint=" + data.addPoint + "&minusPoint=" + data.minusPoint + "&addRemark=" + data.addRemark + "&minusRemark=" + data.minusRemark + "&WriteAssessmentUserName=" + data.UserName + "&userCode=" + data.UserCode + "&sumPoint=" + data.sumPoint + "&statusTitle=" + data.statusTitle
				+ "&addAndMinusTime=" + assemblys.dateToStr(data.AddAndMinusTime) + "&addAndMinusTimeEnd=" + assemblys.dateToStr(data.AddAndMinusTimeEnd) + "&rercordGroupScore=" + data.RercordGroupScore;
		layer.open({
			title : '确认汇总表',
			content : url,
			area : [ '100%', '100%' ],
			type : 2
		});
	},
	exportWord : function() {
		var body = $(".bodys").clone();//复制.bodys盒子中的HTML
		//清空不然要导出的html
		body.find("#exportWord").empty();
		body.find(".tableDiv").empty();
		body.find(".layui-tab").empty();
		var temp = searchProgressDetails.createTable();
		body.find(".tableDiv").append(temp);
		var exportWordText = "";
		// 获取非隐藏的板块
		body.find("div[plate]:visible").each(function() {
			exportWordText += $(this).clone().html();
		});
		// 先缓存后，再下载，因为数据量大！
		$.ajax({
			url : basePath + "frame/fileUpload/cacheWordData.spring",
			type : "post",
			data : {
				exportWordText : body.html(),
				fileName : $("#showPlanName").text()
			},
			dataType : "json"
		}).then(function(data) {
			location.href = basePath + "frame/fileUpload/exportWord.spring";
		});
	},
	createTable : function() {
		var html = '';
		var htmlArr = new Array();
		$.ajax({
			url : basePath + "mdms/medoctorAssessment/getSearchProgressDetailsList.spring",
			type : "get",
			dataType : 'json',
			async : false,
			data : {
				assessmentCode : param.get("assessmentCode"),
				state : param.get("state")
			},
			success : function(data) {
				htmlArr = data.detailsList;
			}
		})
		html += '<table>';
		html += '	<tbody>';
		html += '		<tr>';
		html += '			<td width="275" valign="top" style="word-break: break-all;text-align:center;">科室</td>';
		html += '			<td width="275" valign="top" style="word-break: break-all;text-align:center;">姓名</td>';
		html += '			<td width="275" valign="top" style="word-break: break-all;text-align:center;">自评得分</td>';
		html += '			<td width="275" valign="top" style="word-break: break-all;text-align:center;">科评得分</td>';
		html += '		</tr>';
		for (var i = 0; i < htmlArr.length; i++) {
			html += '		<tr>';
			html += '			<td width="275" style="word-break: break-all;text-align:center;">' + htmlArr[i].DeptName + '</td>';
			html += '			<td width="275" style="word-break: break-all;text-align:center;">' + htmlArr[i].UserName + '</td>';
			html += '			<td width="275" style="word-break: break-all;text-align:center;">' + htmlArr[i].RercordGroupScore + '</td>';
			html += '			<td width="275" style="word-break: break-all;text-align:center;">' + htmlArr[i].RecordOneselfScore + '</td>';
			html += '		</tr>';
		}
		html += '	</tbody>';
		html += '</table>';
		return html;
	}
}