<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>考评模板</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../frame/customApprovalFlow/approval/css/approvalFlow.css">
<link rel="stylesheet" type="text/css" href="css/searchProgressDetails.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" id="assessmentCode" name="assessmentCode" />
		<input type="hidden" name="state" value="99">
		<input type="hidden" name="selfSumScore" value="">
		<input type="hidden" name="deptSumScore" value="">
		<input type="hidden" name="selfScore" value="">
		<input type="hidden" name="deptScore" value="">
		<div class="head0">
			<span class="head1_text fw700 layui-hide">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
		</div>
	</form>
	<div class="bodys layui-form" style="margin-top: -40px; text-align: center;">
		<div plate>
			<div class="top top_div">
				<button id="exportWord" type="button" class="layui-btn layui-btn-sm fr" onclick="searchProgressDetails.exportWord();">导出</button>
			</div>
			<legend style="font-weight: 400px;text-align:center;" id="showPlanName"></legend>
			<div class="layui-form-item">
				<label class="showLabel">考评期间 :</label>
				&nbsp;
				<span name="assessmentTerm"></span>
				<label class="showLabel">考评时间 : </label>
				&nbsp;
				<span name="assessmentValidity"></span>
				<br />
				<br />
				<!-- 项目个数 -->
				<span name="assessmentNumber"></span>
				&nbsp;
				<label class="showLabel">类型 : </label>
				&nbsp;
				<span name="assessmentType"></span>
				<label class="showLabel">总分 : </label>
				&nbsp;
				<span name="assessmentSumScore"></span>
				<label class="showLabel">合格分 : </label>
				&nbsp;
				<span name="assessmentPassScore"></span>
			</div>
			<div class="layui-form-item" style="text-align: center;">
				<table style="width: 100%;">
					<tr>
						<td style="text-align: right; width: 20%;">自评总体进度:</td>
						<td name="assessmentSelfProgress" style="text-align: left; width: 20%;"></td>
						<td style="text-align: right; width: 20%;">科评总体进度:</td>
						<td name="assessmentDeptProgress" style="text-align: left; width: 20%;"></td>
						<td style="text-align: right; width: 20%;"></td>
					</tr>
				</table>
			</div>
			<form class="layui-form" lay-filter="filterParam"></form>
			<div class="layui-tab" lay-filter="tabView">
				<ul id="tabView" class="layui-tab-title head2_tab h28">
					<li class="layui-this" state="99">全部</li>
					<li class="layui-this" state="3">优秀</li>
					<li class="layui-this" state="2">良好</li>
					<li class="layui-this" state="1">合格</li>
					<li class="layui-this" state="0">较差</li>
					<li class="layui-this" state="4">补考</li>
				</ul>
			</div>
			<div class="layui-row">
				<div class="tableDiv table_noTree">
					<div id="list" lay-filter="list"></div>
				</div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/searchProgressDetails.js?r="+Math.random()></script>
<script type="text/javascript" src="../../plugins/moment/js/moment-2.24.0.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		searchProgressDetails.init();
	});
</script>