var meetingPlaceAdd = {
		
		init : function(){
			meetingPlaceAdd.initLayui();
		},
		initLayui : function() {
			layui.form.render();
			layui.form.on("submit(save)", function() {
				meetingPlaceAdd.saveMeetingPlace();
				return false;
			});
		},
		saveMeetingPlace : function(){
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			
			return $.ajax({
				url : basePath + "mdms/meetingRecord/saveMeetingPlace.spring",
				data : param.__form(),
				type : "post",
			}).then(function(data) {
				
				if(data.state != 0 ){
					assemblys.msg("保存成功", function() {
						parent.meetingRecordEdit.getMeetingPlace();
						assemblys.closeWindow();
					});
				}else{
					assemblys.msg(data.msg);
				}
					window.isSubmit = false;
					return data;
			});
		},
}