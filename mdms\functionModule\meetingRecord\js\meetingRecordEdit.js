var meetingRecordEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		meetingRecordEdit.getMeetingPlace().then(function(data) {
			meetingRecordEdit.getMeetingRecord().then(function(data) {
				var isShow = param.get("isShow");
				if (isShow == 'true') {
					meetingRecordEdit.initReadOnly();
				}
				meetingRecordEdit.initLayui();
			});
		});
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			meetingRecordEdit.saveMeetingRecord();
			return false;
		});
		
		layui.form.on('select(meetingPlace)', function(data) {
			meetingRecordEdit.formOnMeeting();
		});
		
		var beginTime = layui.laydate.render({
			elem : "#beginTime",
			trigger : "click",
			type : "datetime",
			min : new Date().toLocaleString('chinese', {
				hour12 : false
			}),
			format : "yyyy-MM-dd HH:mm",
			done : function(value, date) {
				//会议结束时间>会议开始时间
				endTime.config.min = {
					year : date.year,
					month : date.month - 1,//关键
					date : date.date,
					hours : date.hours,
					minutes : date.minutes,
					seconds : date.seconds
				},
				//签到时间<会议开始时间
				checkTime.config.max = {
					year : date.year,
					month : date.month - 1,//关键
					date : date.date,
					hours : date.hours,
					minutes : date.minutes,
					seconds : date.seconds
				},
				//会议占用核对
				param.set("beginTime", value);
				meetingRecordEdit.formOnMeeting();
				
			}
		});
		var endTime = layui.laydate.render({
			elem : "#endTime",
			trigger : "click",
			type : "datetime",
			min : new Date().toLocaleString('chinese', {
				hour12 : false
			}),
			format : "yyyy-MM-dd HH:mm",
			done : function(value, date) {
				//会议开始时间最大值<会议结束时间
				beginTime.config.max = {
					year : date.year,
					month : date.month - 1,//关键
					date : date.date,
					hours : date.hours,
					minutes : date.minutes,
					seconds : date.seconds
				},
				//会议占用核对
				param.set("endTime", value);
				meetingRecordEdit.formOnMeeting();
			},
		
		});
		
		var checkTime = layui.laydate.render({
			elem : "#checkTime",
			trigger : "click",
			type : "datetime",
			min : new Date().toLocaleString('chinese', {
				hour12 : false
			}),
			format : "yyyy-MM-dd HH:mm",
		});
		
	},
	
	formOnMeeting : function() {
		var beginTime = param.get("beginTime");
		var endTime = param.get("endTime");
		var meetingPlace = param.get("meetingPlace");
		var meetingRecordId = param.get("meetingRecordId");
		if (beginTime != '' && endTime != '' && meetingPlace != '') {
			var meetingDateRange = beginTime + "~" + endTime;
			meetingRecordEdit.getCheckMeetingRecord(meetingDateRange, meetingPlace, meetingRecordId).then(function(data) {
				if (data.useMeeting.length > 0) {
					assemblys.msg("会议室已被占用！")
				}
			});
		}
	},
	
	getMeetingRecord : function() {
		return $.ajax({
			url : basePath + "mdms/meetingRecord/getMeetingRecord.spring",
			data : {
				meetingRecordId : param.get("meetingRecordId")
			}
		}).then(function(data) {
			param.set(null, data.meetingRecord);
			meetingRecordEdit.initTypeFile(data.fileList);
			return data;
		});
	},
	
	getCheckMeetingRecord : function(meetingDateRange, meetingPlace, meetingRecordId) {
		return $.ajax({
			url : basePath + "mdms/meetingRecord/getCheckMeetingRecord.spring?",
			data : {
				meetingPlace : meetingPlace,
				meetingDateRange : meetingDateRange,
				meetingRecordId : meetingRecordId
			}
		}).then(function(data) {
			return data;
		});
	},
	
	//会议地点
	getMeetingPlace : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.MEETINGPLACE,
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
		}).then(function(data) {
			var html = "<option value='' selected>请选择会议地点</option>";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "'>" + val.dictName + "</option>";
			})
			$("#meetingPlace").html(html);
			layui.form.render();
			return data;
		});
	},
	
	//选择与会人员
	getUserList : function() {
		var selectValues = param.get("joinCode");
		layer.open({
			type : 2,
			title : "与会人员",
			scrollbar : false,
			maxmin : false,
			area : [ '700px', '90%' ],
			content : basePath + "plugins/udSelector/selectUserPage.jsp?funCode=" + param.get("funCode") + "&selectValues=" + selectValues + "&type=user&callback=meetingRecordEdit.setUserList"
		});
	},
	
	//与会人员回显
	setUserList : function(list) {
		var joinCode = "";
		var joinName = "";
		for (var i = 0; i < list.length; i++) {
			joinName += list[i].userName + ",";
			joinCode += list[i].userCode + ",";
		}
		joinName = joinName.substring(0, joinName.length - 1);
		joinCode = joinCode.substring(0, joinCode.length - 1);
		param.set("joinName", joinName);
		param.set("joinCode", joinCode);
	},
	
	saveMeetingRecord : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		
		var meetingRecordFileList = [];
		$("#ueditorFileDiv-0").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			meetingRecordFileList.push(typeFiles);
		});
		var meetingRecordFileListJson = JSON.stringify(meetingRecordFileList);
		$("#meetingRecordFileListJson").val(meetingRecordFileListJson);
		
		return $.ajax({
			url : basePath + "mdms/meetingRecord/saveMeetingRecord.spring",
			data : param.__form(),
			type : "post",
		}).then(function(data) {
			if (data.hasMeeting) {
				assemblys.msg("保存成功", function() {
					parent.meetingRecordList.getMeetingRecordPager();
					assemblys.closeWindow();
				});
				window.isSubmit = false;
				return data;
			} else {
				assemblys.msg("保存失败，会议室已被占用！");
				window.isSubmit = false;
			}
		});
	},
	
	attaCallback : function(result) {// 自定义上传图片后的回调
		var fileHtml = "";
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		for (var i = 0; i < result.length; i++) {
			fileHtml += "<li style='width: 500px;'>";
			fileHtml += "	<em title=\"" + result[i].title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + result[i].title + "&nbsp;&nbsp;" + result[i].size + "</em>";
			var suffix = result[i].type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" id=\"preview\" onclick=\"pubUploader.preview('" + result[i].title + "','" + result[i].url + "');\"  >预览图片</a></span>";
			}
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + result[i].title + "','" + result[i].url + "');\">下载</a></span>";
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove attaDelete\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a></span>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + result[i].title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + result[i].url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + result[i].size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + result[i].type + "\"/>";
			fileHtml += "</li>";
		}
		$("#ueditorFileDiv-0").append(fileHtml);
		if (param.get("showOrEdit") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
	},
	
	//编辑页面只读
	initReadOnly : function() {
		$("span[titleName]").text("查看会议记录");
		$(".attaDelete").each(function() {
			$(this).addClass("layui-hide");
		});
		$(".layui-btn").each(function() {
			$(this).addClass("layui-hide");
		});
		$(".layui-input").each(function() {
			$(this).addClass("showReadOnly");
			$(this).attr("readonly", true);
		});
		$(".layui-textarea").each(function() {
			$(this).addClass("showReadOnly");
			$(this).attr("readonly", true);
		});
		//hwx 2023-7-27 浏览隐藏按钮
		$(".place").addClass("layui-hide");
	},
	
	initTypeFile : function(meetingRecordFileList) {
		var filesData = meetingRecordFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.title = typeFileTemp.AttaName;
				files.url = typeFileTemp.AttaUrl;
				files.size = typeFileTemp.AttaSize;
				files.type = typeFileTemp.AttaType;
				result.push(files);
			}
			meetingRecordEdit.attaCallback(result);
		}
	},
	addMeetingPlace : function() {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '60%', '60%' ],
			title : '新增会议地点',
			scrollbar : false,
			content : "meetingPlaceAdd.html?appCode=" + param.get("appCode")
		})
	}
}