var meetingRecordList = {
	
	//控制权限
	selectRight : {
		hasDelRight : "",
		hasEditRight : "",
	},
	
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			meetingRecordList.meetingRecordListInit().then(function(data) {
				meetingRecordList.getMeetingPlace().then(function(data) {
					meetingRecordList.initLayuiForm();
					if (param.get("customFormFilledCode") != "") {
						$("select[name='compNo']").parent().addClass("layui-hide");
					}
				});
			});
		});
		
	},
	
	meetingRecordListInit : function() {
		return $.ajax({
			url : basePath + "mdms/meetingRecord/meetingRecordListInit.spring"
		}).then(function(data) {
			param.set("appCode", data.appCode);
			
			//新增会议权限
			if (data.hasAddRight) {
				$("#addButton").removeClass("layui-hide");
			}
			
			meetingRecordList.selectRight.hasDelRight = data.hasDelRight;
			meetingRecordList.selectRight.hasEditRight = data.hasEditRight;
			
			return data;
		});
	},
	initLayuiForm : function() {
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "datetime",
				range : "~",
				format : "yyyy-MM-dd HH:mm"
			});
		});
		
		mdmsCommon.loadCompNo(function() {
			meetingRecordList.getMeetingRecordPager();
		});
		
		layui.form.on('select(meetingPlace)', function(data) {
			meetingRecordList.getMeetingRecordPager();
		});
		
		layui.form.render();
	},
	
	getMeetingRecordPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 100,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_delete" title="查看" lay-event="toSearchMeetingRecord"></i>';
				if (meetingRecordList.selectRight.hasEditRight) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditMeetingRecord"></i>';
				}
				if (meetingRecordList.selectRight.hasDelRight) {
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="toDeleteMeetingRecord"></i>';
				}
				return html;
			}
		}, {
			title : '会议主题',
			width: 180,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.meetingTitle);
			}
		}, {
			title : '开始时间',
			width: 160,
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.beginTime);
			}
		}, {
			title : '结束时间',
			width: 160,
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.endTime);
			}
		}, {
			title: '会议地点',
			width: 150,
			align: "center",
			templet: function (d) {
				return assemblys.htmlEncode(d.DictName);
			}
		}, {
			title: '会议内容',
			width: 180,
			align: "center",
			templet: function (d) {
				return assemblys.htmlEncode(d.meetingContent);
			}
		}, {
			title : '发起人姓名',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.applyName);
			}
		}, {
			title: '登记时间',
			width: 160,
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.applyTime);
			}
		}, {
			title : '会议状态 ',
			width : 100,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.meetingStateValue);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/meetingRecord/getMeetingRecordPager.spring?" + param.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toSearchMeetingRecord : meetingRecordList.toSearchMeetingRecord,
				toEditMeetingRecord : meetingRecordList.toEditMeetingRecord,
				toDeleteMeetingRecord : meetingRecordList.toDeleteMeetingRecord
			}
		});
		
	},
	
	toSearchMeetingRecord : function(d) {
		param.set("isShow", true);
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '700px', '80%' ],
			title : "查看会议记录",
			scrollbar : false,
			content : "meetingRecordEdit.html?funCode=" + param.get("funCode") + "&meetingRecordId=" + d.meetingRecordId + "&appCode=" + param.get("appCode") + "&isShow=" + param.get("isShow")
		});
	},
	
	toEditMeetingRecord : function(d) {
		param.set("isShow", false);
		var $title = "会议记录新增";
		if (d.meetingRecordId) {
			$title = "会议记录编辑";
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			area : [ '800px', '90%' ],
			title : $title,
			scrollbar : false,
			content : "meetingRecordEdit.html?customFormFilledCode=" + $("#customFormFilledCode").val() + "&funCode=" + param.get("funCode") + "&meetingRecordId=" + d.meetingRecordId + "&appCode=" + param.get("appCode") + "&isShow=" + param.get("isShow")
		});
	},
	toDeleteMeetingRecord : function(d) {
		return $.ajax({
			url : basePath + "mdms/meetingRecord/deleteMeetingRecord.spring",
			type : "post",
			data : {
				meetingRecordId : d.meetingRecordId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				meetingRecordList.getMeetingRecordPager();
			});
			return data;
		});
	},
	
	//会议地点
	getMeetingPlace : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.MEETINGPLACE,
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
		}).then(function(data) {
			var html = "<option value='' selected>请选择会议地点</option>";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
			})
			$("#meetingPlace").html(html);
			layui.form.render();
			return data;
		});
	},

}