<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
	<input type="hidden" name="appCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<!-- <div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					会议编号
				</label>
				<div class="layui-input-inline">
					<input type="text" id="dictCode" name="dictCode" limit="200" lay-verify="required|limitc" class="layui-input" />
				</div>
			</div> -->
			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					会议地点
				</label>
				<div class="layui-input-inline">
					<input type="text" name="dictName" limit="200" lay-verify="required|limitc" class="layui-input" />
				</div>
			</div>
<!-- 			<div class="layui-form-item">
				<label class="layui-form-label">
					<span style="color: red;">*</span>
					顺序号
				</label>
				<div class="layui-input-inline">
					<input type="text" name="seqNo" limit="5" lay-verify="required|limitc|intc"  value="1" class="layui-input" />
				</div>
				<label class="layui-form-label"> 状态 </label>
				<div class="layui-input-inline">
					<input type="radio" name="state" value="1" title="启用" checked="checked" />
					<input type="radio" name="state" value="0" title="停用" />
				</div>
			</div> -->
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/meetingPlaceAdd.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		meetingPlaceAdd.init();
	});
</script>