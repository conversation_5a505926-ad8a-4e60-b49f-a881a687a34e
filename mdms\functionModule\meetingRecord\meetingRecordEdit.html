<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/meetingRecordEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="appCode">
		<input type="hidden" name="isShow">
		<input type="hidden" name="meetingRecordId">
		<input type="hidden" name="customFormFilledCode" id="customFormFilledCode">
		<input type="hidden" name="joinCode" id="joinCode">
		<input type="hidden" name="meetingRecordFileListJson" id="meetingRecordFileListJson">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						会议主题
					</label>
					<div class="layui-input-inline">
 						<input type="text" lay-verify="required" name="meetingTitle"  maxlength="200" value="" class="layui-input " autocomplete="off" />
					</div>
				</div>
	 			<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						开始时间
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="beginTime" id="beginTime" lay-filter="beginTime" class="layui-input" autocomplete="off" laydate/>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						结束时间
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="endTime" id="endTime" lay-filter="endTime" class="layui-input" autocomplete="off" laydate />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						会议地点
					</label>
					<div class="layui-input-inline">
						<select  lay-verify="required" name="meetingPlace" xm-select="meetingPlace" id="meetingPlace" lay-filter="meetingPlace" autocomplete="off" lay-search></select>
					</div>
					<i class="layui-icon2 place" title="新增会议地点" onclick="meetingRecordEdit.addMeetingPlace()">&#xe819;</i>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						签到时间
					</label>
					<div class="layui-input-inline">
						<input type="text" name="checkTime" value="" id="checkTime" class="layui-input" id="checkTime" autocomplete="off" laydate/>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						会议状态
					</label>
					<div class="layui-input-inline">
						<select name="meetingState" id="meetingState" xm-select="meetingState" >
							<option value="2" selected>未开始</option>
							<option value="1">进行中</option>
							<option value="0">已结束</option>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						与会人员
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="required"  placeholder="点击选择与会人员" readonly="readonly"   maxlength="500" style="width:500px;" id="joinName" name="joinName" value="点击选择" class="layui-textarea" onclick="meetingRecordEdit.getUserList()"></textarea>
					</div>
				</div>
					<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						会议内容
					</label>
					<div class="layui-input-inline">
						<textarea type="text" placeholder="填写会议内容"  maxlength="990" lay-verify="required" style="width:500px;"  name="meetingContent" class="layui-textarea"></textarea>
					</div>
				</div>
				
				<fieldset class="layui-elem-field">
				   <legend>附件上传</legend>
				   <div class="layui-field-box">
				    <div class="layui-form-item">
				     <label class="layui-form-label"> 附件 </label>
				     <div class="layui-input-inline">
				      <input type="button" value="上传附件" class="layui-btn layui-btn-sm" onclick="pubUploader.openFiles(meetingRecordEdit.attaCallback);param.set('fileIndex', 0);" />
				      <div class="collapse in">
				       <ul class="cattachqueue" id="ueditorFileDiv-0"></ul>
				      </div>
				     </div>
				    </div>
				   </div>
				</fieldset>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/meetingRecordEdit.js?r="+Math.random()></script>
<!-- 富文本、上传组件  - 直接拷贝到项目中 -->
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.config.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/ueditor.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/lang/zh-cn/zh-cn.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		meetingRecordEdit.init();
	});
</script>