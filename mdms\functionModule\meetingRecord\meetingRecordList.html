<!DOCTYPE html>
<html>
<head>
<title>会议记录列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
<link rel="stylesheet" type="text/css" href="css/meetingRecordList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="appCode">
		<input type="hidden" name="state" value="99">
		<input type="hidden" name="customFormFilledCode" id="customFormFilledCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="layui-input-inline h28 lh28" style="left:20px;">
				<input id="keyword" name="keyword" placeholder="主題"  type="text" class="layui-input" onkeydown="if(event.keyCode == 13) {$(this).next().click();}" autocomplete="off"/>
			</div>
			<div class="layui-input-inline h28 lh28" style="left:20px;">
				<select id="meetingPlace" name="meetingPlace" lay-filter="meetingPlace"  xm-select="meetingPlace" placeholder="会议地点" autocomplete="off"></select>
			</div>								
			<div class="layui-input-inline h28 lh28" style="left:30px;">
				<input class="layui-input" style="width: 200px; display: inline;" placeholder="时间" type="text" name="meetingDateRange" autocomplete="off" laydate/>
			</div>
			<div class="layui-input-inline h28 lh28" style="left:90px;">
				<button type="button" class="layui-btn layui-btn-sm" onclick="meetingRecordList.getMeetingRecordPager();">查询</button>
			</div>
			<div class="head0_right fr">
				<div class="layui-input-inline ">
					<select id="compNo" name="compNo"  lay-filter="compNo"></select>
				</div>
				<button type="button" id="addButton" class="layui-btn layui-btn-sm" onclick="meetingRecordList.toEditMeetingRecord({meetingRecordId:0});">新增</button>
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		<div class="layui-tab" lay-filter="tabView">
		</div>
		<div class="layui-row">
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="js/meetingRecordList.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		meetingRecordList.init();
	});
</script>
</html>