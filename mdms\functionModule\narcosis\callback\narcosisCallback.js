var saveCallback = {
	// 保存方法
	init : function(win, cwind) {
		var saveState = cwind.saveState;
		var customFormFilledCode = cwind.customFormFilled.customFormFilledCode;
		var customFormFilledID = cwind.customFormFilled.customFormFilledID;
		var createUserCode = cwind.customFormFilled.createUserCode;
		var createUserName = cwind.customFormFilled.createUserName;
		var createDate = cwind.customFormFilled.createDate;
		var saveName = "保存成功！";
		if (saveState == 1) {
			saveName = "提交成功！";
		}
		assemblys.msg("提交成功！", function() {
			if (win.customFormTemplate.isAutoSave == undefined || win.customFormTemplate.isAutoSave == 0) {
				window.location.reload();
				win.assemblys.closeWindow();
			} else {
				if (saveState == 0) {
					win.$("input[name='customFormFilledCode']").val(customFormFilledCode);
					win.$("input[name='customFormFilledID']").val(customFormFilledID);
					win.$("input[name='createUserCode']").val(createUserCode);
					win.$("input[name='createUserName']").val(createUserName);
					win.$("input[name='createDate']").val(createDate);
					win.$("input[name='status']").val("0");
					win.isSubmit = false;
					win.customFormTemplate.initSaveTimer();
				}
			}
		});
	}
}