var narcosisList = {
	limitTag : 0,
	//存储证件编号
	hocus : [],
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		narcosisList.threeWardRoundListInit().then(function(data) {
			
			if (data.hasNarcosisAddRight) {
				$("#addBtn").removeClass("layui-hide");
			}
			
			param.set("customFormCompNo", data.customFormCompNo);
			narcosisList.initTableList("#flowContainer");
			narcosisList.initLayuiForm();
		});
	},
	
	initLayuiForm : function() {
		layui.form.on("checkbox(chkAll)", function(data) {
			if (data.elem.checked) {
				$("input[name=chkNames]").not(":checked").next().click();
			} else {
				$("input[name=chkNames]:checked").next().click();
			}
		});
		
		layui.form.on("checkbox(chkHocus)", function(data) {
			var str = "";
			str = data.elem.value.substring(data.elem.value.lastIndexOf("-") + 1, data.elem.value.length);
			if (data.elem.checked) {
				narcosisList.hocus.push(str);
			} else {
				for ( var index in narcosisList.hocus) {
					if (narcosisList.hocus[index] == str) {
						narcosisList.hocus.splice(index, 1)
					}
				}
			}
		});
		layui.form.render();
	},
	
	threeWardRoundListInit : function() {
		return $.ajax({
			url : basePath + "mdms/threeWardRound/threeWardRoundListInit.spring"
		}).then(function(data) {
			param.set("prevCustomFormFilledCode", data.prevCustomFormFilledCode);
			param.set("prevCustomFormCode", assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSDAGL);
			param.set("appCode", assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME);
			return data;
		});
	},
	/**
	 * 初始化表格
	 */
	initTableList : function(selector) {
		var cols = [ {
			title : '<input type="checkbox" lay-skin="primary" lay-filter="chkAll">',
			width : 65,
			align : "center",
			templet : function(d) {
				//if (d.hasRight == 0 && d.certifiCateListSize > 0) {
				//修改为规则库控制
				if (d.hasRight == 0 || d.hasRight == 3) {
					return '<input type="checkbox" name="chkNames"  lay-filter="chkHocus"  lay-skin="primary" value="' + d.anClassCode + '@' + d.anClassName + '@' + d.anClassHisCode + '@' + d.useCondition + '@' + d.anClassId + '" lay-filter="checked">';
				} else {
					return '';
				}
				
			}
		}, {
			title : "麻醉代码",
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.anClassHisCode);
			}
		}, {
			title : "麻醉名称",
			width : 150,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.anClassName);
			}
		}, {
			title : "申请条件",
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.attention);
			}
		}, {
			title : "状态",
			width : 120,
			align : "center",
			templet : function(d) {
				var showHtml = "";
				if (d.hasRight == '1') {
					showHtml = "<font color='#64D572'>已授权</font>";
				} else if (d.hasRight == '0') {
					showHtml = "<font color='red'>未授权</font>"
				} else if (d.hasRight == '3') {
					showHtml = "<font color='DarkGoldenRod'>已暂停</font>"
				} else if (d.hasRight == '2') {
					if (d.status == "0") {
						showHtml = "<font color='blue'>草稿</font>"
					} else if (d.status == "2") {
						showHtml = "<font color='blue'>回退</font>"
					} else {
						showHtml = "<font color='blue'>申请中</font>"
					}
					
				}
				return showHtml;
			}
		}, {
			title : "授权开始时间",
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.htmlEncode(d.createTime == null ? "" : assemblys.dateToStr(d.createTime));
			}
		}, {
			title : "授权结束时间",
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.htmlEncode(d.createTime == null ? "" : assemblys.dateToStr(d.createEndTime));
			}
		} ];
		layui.table.render({
			elem : selector,
			url : basePath + "mdms/anaesthesiaRight/getUserAnesthesiaList.spring?" + param.__form(),
			parseData : function(res) {
				var list = res.userAnesList;
				return {
					"code" : "0",
					"data" : list
				};
			},
			done : function() {
				var fromHeight = $(".layui-table-box").height() + 50;
				$(".showDiv").height(fromHeight);
				$(".tableDiv").height(fromHeight);
			},
			cols : [ cols ],
			page : false
		});
	},
	toEdit : function() {
		
		strs = "";
		$('input[type="checkbox"]:checked').each(function(index, value) {
			if ($(value).val() != 'on') {
				strs += $(this).val() + '&';
			}
		});
		
		if (strs == "") {
			layer.alert("请选择数据后执行！");
			return false;
		} else {
			strs = strs.substring(0, strs.length - 1);
		}
		
		/**
		 * 麻醉授权控制
		 */
		/*if (typeof (authorizationLimit.judgeCheck) == "undefined") {
			authorizationLimit.verify(narcosisList.hocus);
		}
		
		if (authorizationLimit.judgeCheck) {
			return false;
		}
		
		authorizationLimit.judgeCheck = undefined;*/

		// 页面路径
		var url = basePath + "frame/customForm/customFormTemplate.html?customFormBusinessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSMZSQ + "&compNo=" + param.get("customFormCompNo") + "&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME + "&type=1&hasBack=0&limitTag=" + narcosisList.limitTag;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '麻醉申请',
			maxmin : true,
			area : [ '98%', '98%' ], // 设置弹窗打开大小
			content : url
		});
	},
	toMyNarcosis : function() {
		var paramStr = "?funCode=" + param.get("funCode") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&compNo=" + assemblys.top.compNo + "";
		var url = basePath + "mdms/functionModule/mdmsCustomList/mdmsCustomList.html" + paramStr + "&customFormTypeCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_CODE + "&customFormTypeMenuCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_MENU_CODE + "&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME + "&type=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSMZSQ + "&onlyShow=1&status=0&isDocFile=4";
		assemblys.top.addTab(null, '麻醉权限-申请记录', url);
	},
	moreList : function(type) {
		if (type == 1) {
			$(".toShow").addClass("layui-hide");
			$(".showMore").removeClass("layui-hide");
			$(".showHide").removeClass("layui-hide");
		} else if (type == 2) {
			$(".showMore").addClass("layui-hide");
			$(".showHide").addClass("layui-hide");
			$(".toShow").removeClass("layui-hide");
		}
	}
}
