<!DOCTYPE html>
<html>
<head>
<title>麻醉权限列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="prevCustomFormFilledCode">
		<input type="hidden" name="prevCustomFormCode">
		<input type="hidden" name="appCode">
		<input type="hidden" name="selectedLevel">
		
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName>麻醉权限</span>
			</span>
			<div class="head0_right fr">
				<button type="button" class="layui-btn layui-btn-sm layui-hide" id="addBtn" onclick="narcosisList.toEdit();">申请</button>
				<button type="button" class="layui-btn layui-btn-sm" onclick="narcosisList.toMyNarcosis();">我的申请</button>
			</div>
		</div>
		<div class="bodys layui-form" style="overflow-y: scroll;">
			<div class="layui-row showDiv">
				<div class="tableDiv table_noTree table_noSearch" >
					<div id="flowContainer" lay-filter="list"></div>
				</div>
			</div>
			<div>
			<p style="color:red;font-weight: 600; margin-bottom: 15px;">
				提醒： <span style="color: blue;" >麻醉权申请需先上传麻醉权证件</span>
			</p>
			<p style="color:red;font-weight: 600;">
				ASA分级标准： <a class="toShow" style="color: blue; cursor: pointer;text-decoration: underline;"  onclick="narcosisList.moreList(1);">更多</a><a class="layui-hide showHide" style="color: blue; cursor: pointer;text-decoration: underline;" onclick="narcosisList.moreList(2);">隐藏</a>
			</p>
			<p style="padding: 10px;">
				一级麻醉
			</p>
			<p style="padding: 10px;">
				体格健康，发育营养良好，各器官功能正常。围手术期死亡率0.06%-0.08%；
			</p>
			<p style="padding: 10px;">	
				二级麻醉
			</p>
			<p style="padding: 10px;">	
				除外科疾病外，有轻度并存病，功能代偿健全。围手术期死亡率0.27%-0.40%；
			</p>
		</div>
		<div class="layui-hide showMore">
			<p style="padding: 10px;">	
				三级麻醉
			</p>
			<p style="padding: 10px;">	
				并存病情严重，体力活动受限，但尚能应付日常活动。围手术期死亡率1.82%-4.30%；
			</p>
			<p style="padding: 10px;">	
				四级麻醉
			</p>
			<p style="padding: 10px;">	
				并存病严重，丧失日常活动能力，经常面临生命威胁。围手术期死亡率7.80%-23.0%；
			</p>
		</div>
		<div class="layui-hide showMore">
			<p style="padding: 10px;">	
				五级麻醉
			</p>
			<p style="padding: 10px;">	
				无论手术与否，生命难以维持24小时的濒死病人。围手术期死亡率9.40%-50.7%；
			</p>
			<p style="padding: 10px;">	
				六级麻醉
			</p>
			<p style="padding: 10px;">	
				确证为脑死亡，其器官拟用于器官移植手术。
			</p>
		</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="js/narcosisList.js??r="+Math.random()></script>
<script type="text/javascript" src="callback/narcosisCallback.js?r="+Math.random()></script>
<script type="text/javascript">
	document.write("<script type='text/javascript' src='../base/js/customBusiness.js?r="+new Date().getTime()+"'><"+'/'+"script>");
</script>
<script type="text/javascript" src="../../../frame/customDetail/js/initCustomDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="../authorizationLimit/js/authorizationLimit.js?r="+Math.random()></script>
<script type="text/javascript" src="../authorizationLimit/js/narcosisListLimit.js?r="+Math.random()></script>
<script type="text/javascript" src="../authorizationLimit/js/customBusinessLimit.js?r="+Math.random()></script>

<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		narcosisList.init();
	});
</script>
</html>