html .skin-13 .skin-div-border {
	border-color: #0066ff !important;
}

.doctorApproveDiv .content .bottom_div {
	padding: 15px;
	padding-top: 24px;
	height: 178px;
	position: relative;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div {
	top: 10px;
	position: relative;
	height: 60px;
	width: 90%;
	margin-left: 5%;
	border: 2px dashed;
	border-left-style: none;
	border-top-right-radius: 20px;
	border-bottom-style: none;
}

.doctorApproveDiv .content .bottom_div .bottom_bottom_div {
	position: relative;
	height: 60px;
	width: 90%;
	margin-left: 5%;
	border: 2px dashed;
	border-top-style: none;
	border-left-style: none;
	border-bottom-right-radius: 20px;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .top_node {
	width: 100%;
	height: 10px;
	position: relative;
	bottom: 10px;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .top_node>div:first-child {
	position: relative;
	right: 10px;
}

.doctorApproveDiv .content .bottom_div .bottom_bottom_div .top_node2 {
	width: 100%;
	height: 10px;
	position: relative;
	top: 52px;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .top_node>div>div:nth-child(1) {
	width: 40px;
	height: 20px;
	border-radius: 20px;
	clear: left;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .top_node .node_block_left, .doctorApproveDiv .content .bottom_div .bottom_bottom_div .top_node2 .node_block_left {
	width: 40px;
	float: left;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .top_node .node_block_left_margin_left, .doctorApproveDiv .content .bottom_div .bottom_bottom_div .top_node2 .node_block_left_margin_left {
	margin-left: 21%;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .top_node>div:last-child .node_block_right {
	position: relative;
	float: right;
	left: 30px;
	bottom: auto;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .top_node .node_block_left .node_block_span, .doctorApproveDiv .content .bottom_div .bottom_bottom_div .top_node2 .node_block_left .node_block_span {
	width: 40px;
	position: relative;
	left: 20px;
}

.content .layui-icon2 {
	font-size: 20px;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .layui-icon2 {
	position: relative;
	bottom: 2px;
	background: #fff;
}

.doctorApproveDiv .content .bottom_div .bottom_top_div .right_icon {
	position: absolute;
	z-index: 9999;
	top: 1px;
	left: 1px;
}

.content .layui-icon3 {
	font-size: 40px;
}

.doctorApproveDiv .content .bottom_div .bottom_bottom_div .top_node2>div>div {
	width: 20px;
	height: 20px;
	clear: left;
	border-radius: 20px;
}

.doctorApproveDiv .content .bottom_div .bottom_bottom_div .top_node2 .layui-icon2 {
	position: relative;
	bottom: 3px;
	background: #fff;
}

.doctorApproveDiv .table_content .table_header {
	height: 40px;
	line-height: 40px;
	position: relative;
	width: 100%;
}

.doctorApproveDiv .table_content .table_header .table_header_left {
	float: left;
}

.doctorApproveDiv .table_content .table_header .table_header_left .table_header_img {
	position: relative;
	left: 10px;
    bottom: 4px;
    width: 23px;
}

.doctorApproveDiv .table_content .table_header .table_header_left .table_header_h {
	display: inline;
	margin-left: 20px;
}

.doctorApproveDiv .table_content .table_header .table_header_right {
	float: right;
	padding: 2px 10px;
}

.doctorApproveDiv .table_content .table_header .table_header_right .table_header_search>input {
	padding: 5px;
}

#approvalSearch {
	position: relative;
	margin-left: 5px;
	/* r: 9999; */
	right: 0px;
	left: 0p;
	line-height: 30px;
	height: 30px;
	font-size: 24px;
}

.suspendIcon {
	position: relative;
	display: inline-block;
}

.suspendIcon .suspendText {
	visibility: hidden;
	position: absolute;
	width: 300px;
	height: 200px;
	background-color: white;
	text-align: left;
	color: black;
	border-radius: 6px;
	z-index: 99999;
	overflow: auto;
	list-style: disc;
	resize: both;
}

.suspendIcon .suspendText_left {
	visibility: hidden;
	position: absolute;
	width: 300px;
	height: 200px;
	background-color: white;
	text-align: left;
	color: black;
	right: 20px;
	border-radius: 6px;
	z-index: 99999;
	overflow: auto;
	list-style: disc;
	resize: both;
}

.suspendText::-webkit-scrollbar-thumb {
	background-color: rgba(0, 0, 0, .05);
	border-radius: 10px;
	-webkit-box-shadow: inset1px1px0rgba(0, 0, 0, .1);
}

.suspendText::-webkit-scrollbar {
	width: 10px;
	height: 10px;
}

.item {
	font-size: 15px;
	position: relative;
	cursor: default;
	box-sizing: border-box;
	line-height: 41px;
	font-size: @font-size-normal;
	color: @primary-blue-color;
	width: 100%;
	border: 1px solid #cccccc;
	border-right-style: none;
	border-left-style: none;
	border-top-style: none;
}

.item_left {
	font-size: 15px;
	position: relative;
	cursor: default;
	box-sizing: border-box;
	line-height: 41px;
	font-size: @font-size-normal;
	color: @primary-blue-color;
	width: 100%;
	border: 1px solid #cccccc;
	border-right-style: none;
	border-left-style: none;
	border-top-style: none;
	text-align: center;
}

.item_left::after {
	content: " ";
	position: absolute;
	right: 8px;
	top: 15px;
	border: 1px solid black;
	background-color: black;
	display: inline-block;
	width: 3px;
	height: 3px;
	border-radius: 50%;
	margin-right: 12px;
}

.item::before {
	content: ' ';
	position: absolute;
	left: 8px;
	top: 15px;
	border: 1px solid black;
	background-color: black;
	display: inline-block;
	width: 3px;
	height: 3px;
	border-radius: 50%;
	margin-right: 12px;
}