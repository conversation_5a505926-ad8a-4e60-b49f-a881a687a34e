@charset "utf-8";

.top {
	width: 100%;
	height: 40px;
	border: 1px solid red;
	background-color: #f2f2f2;
}

.middleLeft {
	width: 85%;
	height: 25%;
	border: 1px solid blue;
	margin-top: 10px;
	background-color: #ffffff;
	background: url('../../../../mdms/functionModule/newDoctorFile/image/docBag.png') 0px 0px;
}

.middleRight {
	width: 14%;
	height: 100%;
	border: 1px solid green;
	float: right;
	margin-top: 10px;
	margin-left: 10px;
	background-color: #ffffff;
}

.bottom {
	width: 85%;
	height: 70%;
	border: 1px solid green;
	margin-top: 10px;
	background-color: #eee;
}

.headLeft {
	width: 240px;
	height: 100%;
	border: 0px solid blue;
	float: left;
	background: url('../../../../mdms/functionModule/newDoctorFile/image/headImg.png') no-repeat 4px 5px;
}

.headRight {
	position: absolute;
	height: 160px;
	width: 100%;
}

.top_fl {
	width: 100px;
	font-size: 16px;
	padding-top: 13px;
	padding-left: 20px;
	float: left;
}

.top_rg {
	width: 200px;
	padding-top: 5px;
	margin-left: 95%;
}

.bottomLeft {
	width: 15%;
	height: 100%;
	border: 1px solid orange;
	float: left;
	background-color: #ffffff;
}

.bottomRight {
	width: 83%;
	height: 100%;
	border: 1px solid red;
	float: left;
	margin-left: 10px;
	background-color: #ffffff;
}

.headRow1, .headRow2, .headRow3, .headRow4 {
	border: 0px solid green;
}

.headRow1 {
	width: 100%;
	border: 0px solid red;
	line-height: 29px;
    padding-top: 15px;
    height:30%;
}

.headRow2 {
	width: 100%;
    line-height: 29px;
    height:30%;
    padding-left: 1%;
}

.headRow3 {
	width: 100%;
    line-height: 29px;
    height:30%;
}

.docName {
	color: #333;
	float: left;
	font-size: 23px;
	font-weight: bolder;
	font-family: 思源黑体;
	text-align: left;
	min-width: 10%;
	padding-left: 1%;
}
.docDeptImg{
	position: absolute;
    width: 45px;
    height: 45px;
   	top: 4px;
    left: 15%;
    border-radius: 60px;
    background-color: #8FBCFD;
}
.docDeptImg img {
	padding: 9px 6px 6px 10px;
}
.docDept {
	float: left;
    background-color: #0164FE;
    height: 23px;
    border-radius: 20px;
    padding:0px 15px 0px 48px;
    width: 8%;
    margin-left: 4%;
}
.docDeptText {
	text-align:center;
	color: #fff;
    font-size: 12px;
    margin-top: -2px;
}
.docSchool, .profession, .education, .studyClass {
	float: left;
	margin-left:3%;
	color: #333;
	font-size: 14px;
	font-family: 思源黑体;
	max-width: 20%;
	min-width: 5%;
	text-align: center;
	padding: 0px 2px 0px 2px;
}

.docSchool {
	text-align:center;
 	background-color: #aed4ff; 
}

.profession {
	text-align:center;
 	background-color: #aed4ff; 
}

.education {
	text-align:center;
 	background-color: #aed4ff; 
}

.studyClass {
	text-align:center;
 	background-color: #aed4ff; 
}

.tel {
	float: left;
	margin-left: 100px;
	border: 0px solid green;
	max-width: 10%;
}
.tel img{
	position: absolute;
	top: 118px;
    width: 20px;
    padding-left: 9px;
}

.telNo {
	border: 0px solid green;
	padding-left: 35px;
	padding-top: 5px;
}

.sex, .age {
	border: 0px solid green;
	float: left;
	text-align: center;
	line-height: 41px;
}

.age {
	width: 5%;
}

.status, .identityClass {
	border: 0px solid green;
	float: left;
	text-align: center;
    line-height: 41px;
}

.status {
}

.identityClass {
}

.split {
	float: left;
	color: #C1C4C8;
    line-height: 41px;
}

.docCode {
	float: left;
	min-width: 60px;
	height: 30px;
	margin-left: 2%;
	padding-right: 2%;
	background: url('../../../../mdms/functionModule/newDoctorFile/image/userCode.png') no-repeat 4px 5px;
}

.userCode {
	margin-left: 40px;
	line-height: 41px;
}

.workYear {
	margin-left: 2%;
	padding-right: 2%;
	min-width: 60px;
	float: left;
	background: url('../../../../mdms/functionModule/newDoctorFile/image/workYears.png') no-repeat 4px 5px;
}

.years {
	margin-left: 40px;
	line-height: 41px;
}

.group {
	float: left;
	min-width: 140px;
    max-width: 50%;
	line-height: 41px;
}

.beGoodAt {
    float: left;
	line-height: 41px;
	min-width: 180px;
    max-width: 80%;
	border: 0px solid blue;
}

.groupAll {
	color: #FF7301;
	display: flex;
	min-width:110px;
	line-height: 41px;
}
.groupImg{
	width:20px;
	height:20px;
	margin-top: 9%;
}
.group1 {
	background: url('../../../../mdms/functionModule/newDoctorFile/image/honor1.png') no-repeat;
}

.group2 {
	background: url('../../../../mdms/functionModule/newDoctorFile/image/honor2.png') no-repeat;
}

.groupName1 {
	border: 0px solid green;
	width: 90%;
	margin-left: 5px;
	padding-right:5px;
	font-size: 13px;
	font-family: Arial;
}

.goodText {
	font-size: 13px;
	display: flex;
	width:100%;
}
.fonts{
	float: right;
	min-width:70px;
	padding-left: 4%;
}

.goodness {
	border: 0px solid red;
	line-height: 41px;
	min-width:100%;
}

.good {
	border: 1px solid #0164FE;
	padding-left: 15px;
	padding-right: 15px;
	line-height: 20px;
    height: 20px;
    margin-top: 8px;
	margin-left:1%;
	margin-right:0%;
	max-width:70%;
	float: left;
	font-family: 思源黑体;
	font-size: 13px;
	color: #0164FE;
	text-align: center;
}

.skillGroup {
}
.skill {
	cursor: pointer;
	line-height: 41px;
	float: left;
	text-align: center;
	color: #666;
	min-width: 10%;
}
.skill1 font, .skill2 font, .skill3 font, .skill4 font, .skill5 font {
	text-decoration: underline;
}

#shoushu, #mazui, #chufang, #chafang, #yazhuanye {
	color: #ff7301;
	font-size: 14px;
}

.introudice, .classTime {
	float: left;
	color: #333;
	font-family: 思源黑体;
	max-width: 22%;
	margin-left: 3%;
}

.layui-tab-title li {
	display: block;
	width: 150px;
	top: 7px;
    left: 5px;
    height: 45px;
    line-height: 45px;
}

.layui-tab-title .layui-this {
	padding: 0px;
	border: 0px solid blue;
	box-shadow: 6px 5px 20px 0px rgba(1,102,255,0.3);
}
.layui-tab-title {
	margin-top: 15px;
	border-bottom-width: 0px;
	border-bottom-style: none;
}

.certificateTop {
	border-bottom: 1px solid #BEBEBE;
	margin-top: 5px;
	padding: 5px;
	height: 25px;
}

.certificateImg {
	margin: 0px 0px 2px 2px;
	border: 0px solid blue;
	height: 20px;
	width: 20px;
	padding-left: 10px;
	margin-left: 10px;
	cursor: pointer;
	background: url('../../../../mdms/functionModule/newDoctorFile/image/certificate.png') no-repeat;
}

.certificateBottom {
	overflow: auto;
	position: absolute;
	top: 40px;
	left: 0px;
	right: 0px;
	bottom: 0px;
}

.certifiDiv {
	position: fixed;
    top: 0px;
    right: -2px;
    height: 25px;
    width: 45px;
    z-index: 9999999;
}
.certifiDiv input{
    margin:6px;
}

.ztl {
	position: fixed;
	top: 0px;
	left: 0px;
	right: 0px;
	width: 100%;
	height: 42px;
	background: #ffffff;
	z-index: 9999;
}

.container {
	position: absolute;
	top: 0px;
	right: 0px;
	left: 0px;
	bottom: 0px;
	background: #f2f2f2;
/* 	width: 100%; */
	padding-left: 10px;
	padding-right: 10px;
}

.main {
	position: relative;
	min-height: 160px;
	background: url('../../../../mdms/functionModule/newDoctorFile/image/docBag.png');
}

.content {
	position: relative;
}

.left {
	width: 140px;
	height: 140px;
	left: 21px;
	position: absolute;
}

.right {
	left: 190px;
	height: 160px;
	position: fixed;
	width: 78%;
}

.dn {
	position: relative;
	background: #ffffff;
}

.leftMenu {
	width: 165px;
	position: absolute;
	background-color: #fff;
}

.rightContent {
	background: #ffffff;
	/* width: 130px; */
	position: absolute;
	left: 175px;
	top: 0px;
	bottom: 0px;
/* 	right: 10px; */
}

.rightmenu {
	position: absolute;
	width: 200px;
	height: 100%;
	right: 0px;
	top: 5px;
	background: #ffffff;
	z-index: 0;
	display: none;
}

/* 右侧证书样式 */
#certificate {
	overflow: scroll;
	height: 100%;
}

.certificateName {
	margin-left: 50px;
	font-size: 20px;
	font-weight: 500;
	margin-top: -25px;
}

.rtTitleImg {
	margin-left: 10px;
	margin-top: 10px;
	width:25px;
	height:26px;
}

.rtTitleName {
	margin-left: 10px;
	font-size: large;
	top: 6px;
	position: relative
}

.rtCertificateName {
	margin-top: 10px;
	margin-left: 50px;
	font-size: 15px;
}

div.cerName:hover {
	color: #028bfd;
	font-size: 17px !important;
	text-decoration: underline;
	cursor: pointer;
}

/* 技术授权样式 */
.technicalAuthorizationFrame {
	margin-top: 5px;
}

/* 基本表格 */
.tdRight, .tdLeft {
	border: 1px solid #EAEBEC;
	height: 31.61px;
	text-align: center;
}

.tdLeft {
	background: #EFF8FF;
	width: 7%;
}

.tdRight {
	width: 15%;
}

.layui-table td {
	text-align: center;
}

.layui-tab-item {
	padding: 10px;
	overflow-y: auto;
	background-color: #ffffff;
}

.layui-tab-bar[lay-stope=tabmore] {
	display: none;
}

ul[lay-filter='#excamManageDivUL'] {
	width: 100%;
	background-color: #ffffff;
	overflow: auto;
	margin-top: 10px;
	position: absolute;
	left: 0px;
	right: 0px;
	width: 100%;
}

html .skin-13 .main_title, html .skin-13 .treeHead, html .skin-13 th {
	background-color: #EFF8FF !important;
}

.layui-nav .layui-nav-item a {
	color: black !important;
}

.container .mainBar {
	position: relative;
	width: 100%;
	height: 10px;
	line-height: 4px;
	text-align: center;
	cursor: pointer;
	font-size: 18px;
	font-weight: 600;
}

.container .mainBar-up {
/* 	padding-bottom: 5px; */
}

.container .mainBar-up:after {
	content: "︿";
}

.container .mainBar-down {
	padding-up: 5px;
}

.container .mainBar-down:after {
	content: "﹀";
}

.rightmenu  .rcTitle {
	margin-top: 10px;
	margin-bottom: 10px;
	margin-left: 8px;
}

.rightmenu  .rcTitle{
	margin-top: 10px;
	margin-left: 10px;
}

.rightmenu  .rcTitle .rtTitleName {
	position: relative;
	font-size: larger;
	margin-left: 10px;
	top: 6px;
}

.rightmenu .cerName {
	margin-left: 40px;
	margin-top: 10px;
	margin-bottom: 10px;
	font-size: 15px;
}

#tabView .layui-this {
	width: 145px;
	border-right: 5px solid;
}

.leftMenu ::-webkit-scrollbar {
	width: 5px;
	height: 5px;
	background-color: #F1F1F1;
	display:none;
}
.showIcon{
	position: absolute;
    text-align: left;
    padding: 5px 0px 0px 5px;
}
.showTx{
	width: 180px;
    height: 180px;
}
.showMain{
	padding:5px; 
	background-color: #fff;
}


.layui-tab-title li {
    text-align: left;
    padding-left: 30px;
}
.searchDiv{
	display: flex;
}
.searchLeftDiv{
	margin-top: 7px;
    width: 30%;
    border-radius: 30px;
    text-align: center;
    position: absolute;
    z-index: 99;
    font-size: 21px;
    height: 30px;
    margin-left: 0px;
}
.searchLeftDiv i{
	color: white;
	font-size: 18px;
}
.searchRightDiv{
	
}
.searchInput{
	width: 82%;
    border-radius: 18px;
    left: 27px;
    top: 7px;
    position: relative;
    border-color: #A0C5FE !important;
    text-indent: 20px;
}
#headMage{
	margin-top: 13px;
    margin-left: 0;
    border-radius: 200px;
    width: 135px;
    height: 135px;
}
#groupId{
	display: flex;
}
.goodFont{
	color: gray;
    position: relative;
}
.groupFont{
	color: gray;
    cursor:pointer;
    position: relative;
}
.tree_custom_opt {
	position: absolute;
	left: 165px;
	background: #acb7c1;
	height: 60px;
	border-radius: 0 10px 10px 0;
	top: 260px;
	line-height: 60px;
	width: 14px;
	color: #ffffff;
	cursor: pointer;
	z-index: 999998;
}

.tree_custom_opt i {
	font-size: 14px;
}
.operation_ul .operation_li .showDS{
    color: #ffffff !important;
    font-weight:bolder;
    padding: 0px 0px;
}
.operation_ul .operation_li .showYS{
    color: #ffffff !important;
    font-weight:bolder;
    padding: 0px 0px;
}
.operation_ul .operation_li .showHT{
    color: #ffffff !important;
    font-weight:bolder;
    padding: 0px 0px;
}
html .skin-13 .skin-btn-minor{
	background-color: #0066ff !important;
}
.txk{
	position: absolute;
    width: 208px;
    height: 202px;
    top: -16px;
    left: -32px;
}
.operation_ul {
	width: 100px;
	height: 30px;
	line-height: 30px;
	background: #009688;
	padding: 0;
	text-align: center;
	cursor: pointer;
	display: inline-block;
	vertical-align: top;
	margin-top: 6px;
	margin-right:17px;
	border-radius: 0px 0px 0px 16px !important;
}

.operation_ul .operation_li a {
	padding-left: 0px;
}

.operation_ul .operation_li {
	width: 100%;
	line-height: 30px;
}

.operation_ul .layui-nav-child {
	top: 35px;
}
.layui-input{
	height: 30px !important;
}
.layui-icon{
	font-size: 12px !important;
}