.layui-table-cell {
	height: inherit;
	line-height: inherit;
}
th .layui-table-cell {
	height: 28px;
	line-height: 28px;
}

div.layui-tab {
	left: 10px;
	width: 838.5px;
	position: absolute;
	overflow: hidden;
}

#tabView{
	left: 0px;
}

div.tableDiv{
	top: 40px;
}

html .skin-13 .bodys .layui-tab-title .layui-this {
    color: white;
    /* border-bottom: 2px solid #0088fe; */
    background-color: #028bfd;
    border-radius: 30px;
}

.layui-tab-title .layui-this {
	color: white;
    /* border-bottom: 2px solid #0088fe; */
    background-color: #028bfd;
    border-radius: 30px;
}

#tabView {
    height: 200px;
}

.bodys {
	top:7px;
}


element.style {
}
div.tableDiv {
    top: 40px;
}
.table_noTree {
    left: 10px;
    padding-top: -20px;
}
.tableDiv {
    overflow: hidden;
}

body {
	background:white;
}
div.layui-layer-title{
	padding: 0px;
}
