<!DOCTYPE html>
<html>
<head>
<title>证件一览list表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName>查看证书</span>
			</span>
			<div class="head0_right fr">
				
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		<div class="layui-tab" lay-filter="tabView"></div>
		<div class="layui-row">
			<div class="tableDiv table_noTree">
				<img id="imgId" src="" alt="image.png" style="width: 100%;height: 100%;" layer-pid="undefined" style="cursor: move;">
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="10">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js"></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js"></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js"></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js"></script>
<script type="text/javascript" src="../../../plugins/formSelects/formSelects-v4.min.js"></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		$("#imgId").attr("src",parent.param.get("src"));
	});
</script>
</html>