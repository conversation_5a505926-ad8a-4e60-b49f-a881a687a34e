var anaesthesiaRightEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		$("span[class='head1_text fw700']").text("麻醉授权");
		
		anaesthesiaRightEdit.getAnaesthesiaRight();
		
		anaesthesiaRightEdit.initLayui();
	},
	initLayui : function() {
		layui.form.render();
		
		//加载时间选择器 - 更多请查看官网
		var layDate = layui.laydate;
		//hwx 2023年12月13日上午11:07:11 优化日期选择框
		layDate.render({
			elem : '#createTime',
			type : 'datetime',
			format : 'yyyy-MM-dd HH:mm',
			trigger : 'click',
			min : mdmsCommon.dateToStr(new Date(), 'yyyy-MM-dd HH:mm'),
			ready : function(date) {
				// 可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
		layDate.render({
			elem : '#createEndTime',
			trigger : 'click',
			type : 'datetime',
			format : 'yyyy-MM-dd HH:mm',
			min : mdmsCommon.dateToStr(new Date(), 'yyyy-MM-dd HH:mm'),
			ready : function(date) {
				// 可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
		
		layui.form.on("submit(save)", function() {
			anaesthesiaRightEdit.saveAnaesthesiaRight();
			return false;
		});
		
	},
	
	getAnaesthesiaRight : function() {
		return $.ajax({
			url : basePath + "mdms/anaesthesiaRight/getAnaesthesiaRight.spring",
			data : {
				anaesthesiaRightId : param.get("anaesthesiaRightId")
			}
		}).then(function(data) {
			if (data.anaesthesiaRight) {
				//恢复
				param.set("createTime", data.anaesthesiaRight.createTime);
				param.set("createEndTime", data.anaesthesiaRight.createEndTime);
				$("#saveBtn").val("恢复");
			}
			return data;
		});
	},
	
	saveAnaesthesiaRight : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/anaesthesiaRight/saveAnaesthesiaRight.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("授权成功", function() {
				assemblys.closeWindow();
				parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
				parent.parent.newDoctorInfo.holdAuthority();
				//hwx 2024年3月21日下午3:51:00 重复提交问题
				window.isSubmit = false;
			});
			return data;
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}