var anaesthesiaRightReason = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		$("span[class='head1_text fw700']").text("麻醉授权");
		var isVaild = param.get("isValid")
		if (isVaild == "0") {
			$("#saveBtn").val("暂停");
		}
		anaesthesiaRightReason.initLayui();
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			anaesthesiaRightReason.saveReason();
		});
	},
	saveReason : function() {
		//暂停 
		if (param.get("isValid") == "0") {
			anaesthesiaRightReason.stopAR(param.get("anaesthesiaRightId"), $(".showReason").val(), param.get("anClassName"), param.get("customFormFilledCode"));
		} else {
			//回收
			anaesthesiaRightReason.deleteAR(param.get("anaesthesiaRightId"), $(".showReason").val(), param.get("anClassName"), param.get("customFormFilledCode"));
		}
		
	},
	
	deleteAR : function(anaesthesiaRightId, reason, anClassName, customFormFilledCode) {
		layer.confirm("确定要回收吗？", function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/anaesthesiaRight/updateAnaesthesiaRight.spring",
				type : "post",
				data : {
					anaesthesiaRightId : anaesthesiaRightId,
					reason : reason,
					anClassName : anClassName,
					customFormFilledCode : customFormFilledCode,
					//操作类型（1新增2暂停3恢复4回收5到期）
					type : 4
				}
			}).then(function(data) {
				if (data.res == "fail") {
					assemblys.msg(data.msg);
				} else {
					assemblys.msg("回收成功", function() {
						assemblys.closeWindow();
						parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
						parent.parent.newDoctorInfo.holdAuthority();
					});
				}
				window.isSubmit = false;
			});
		});
	},
	
	stopAR : function(anaesthesiaRightId, reason, anClassName, customFormFilledCode) {
		var reConfirm = "";
		var reMessage = "";
		if (param.get("isValid") == "0") {
			reConfirm = "确定要暂停吗？";
			reMessage = "暂停成功";
		}
		layer.confirm(reConfirm, function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/anaesthesiaRight/saveAnaesthesiaRight.spring",
				type : "post",
				data : {
					anaesthesiaRightId : anaesthesiaRightId,
					reason : reason,
					anClassName : anClassName,
					customFormFilledCode : customFormFilledCode,
					type : 2,
					isValid : param.get("isValid"),
					userCode : param.get("userCode"),
				}
			}).then(function(data) {
				assemblys.msg(reMessage, function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
					window.isSubmit = false;
				});
			});
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}