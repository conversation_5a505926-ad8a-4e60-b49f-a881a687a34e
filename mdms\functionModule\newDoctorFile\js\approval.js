var approval = {
		/**
		 * 保存上个对象
		 */
	this_elem : null,	
	render : function(element) {
		$(element).addClass(element.replace(/\#/g, ""))
		$(element).empty();
		var  arr = [];
		approval.getApprovalBelongFlowNodeList(param.get("customFormFilledCode")).then(function(nodeData) {
			setTimeout(function() {
				arr = approval.getTag(nodeData);
				assemblys.createElement(arr, $(element)[0]);
				approval.renderTable(param.get("customFormFilledCode"));
				$(".item").hover(function(){
					$(this).addClass("skin-div-css")
				},function(){
					$(this).removeClass("skin-div-css")
				});
				layui.laydate.render({
					elem : '#test1', //指定元素
					trigger : 'click', //采用click弹出
					range: '~'
				});
			}, 180);
		})
	},
	renderTable : function(customFormFilledCode) {
		
		var table = layui.table;
		//执行渲染
		var tableIns =table.render({
			elem : '#list', //指定原始表格元素选择器（推荐id选择器）
			url : basePath + "frame/approvalFlowRecord/getApprovalBelongFlowNodeRecordList.spring?" + param.__form() + "&approvalBelongCode=" + customFormFilledCode + "&appCode" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
			cols : [ approval.getClos() ], //设置表头
			height: newDoctorInfo.returnHeight(20),
			parseData : function(res) { //res 即为原始返回的数据
				// 分页
				var total = res.approvalBelongFlowNodeRecordList.length; //请求的数据总数
				var list = []; //用来保存当前页显示的数据
				var msg = "";
				var page = $("div[id^='layui-table-page']").find(".layui-laypage-em").next().html();
				var limit = $("div[id^='layui-table-page']").find(".layui-laypage-limits select").val();
				if (page == undefined || page == null || page == "") {
					page = 1;
				}
				if (limit == undefined || limit == null || limit == "") {
					limit = 10;
				}
				//数据从哪条数据开始
				var start = (page - 1) * limit;
				//数据从哪条数据结束
				var end = page * limit;
				if (end > total) {
					end = total;
				}
				//取分页数据
				for (var i = start; i < end; i++) {
					list.push(res.approvalBelongFlowNodeRecordList[i]);
				}
				if(list.length == 0){
					$(".layui-table-page").css("visibility","hidden");
					msg = "无数据";
				}
				return {
					"code" : "",
					"data" : list,
					"msg" : msg,
					"count" : total,
				}
			},
			limit : 10,
			page : {
				layout : [ 'prev', 'page', 'next', 'limit', 'skip' ],
				groups : 1,
				first : false,
				last : false
			},
		});
		
		$("#approvalSearch").click(function(index,elem){
			$centents = $(this).prev();
			$date = $($centents).prev();
			var centents =  $($centents).val();
			var date = $($date).val();
			tableIns.reload({
				parseData: function(res) { //res 即为原始返回的数据
					// 分页
					var total = res.approvalBelongFlowNodeRecordList.length; //请求的数据总数
					var list = []; //用来保存当前页显示的数据
					var msg = "";
					var page = $("div[id^='layui-table-page']").find(".layui-laypage-em").next().html();
					var limit = $("div[id^='layui-table-page']").find(".layui-laypage-limits select").val();
					if (page == undefined || page == null || page == "") {
						page = 1;
					}
					if (limit == undefined || limit == null || limit == "") {
						limit = 10;
					}
					//数据从哪条数据开始
					var start = (page - 1) * limit;
					//数据从哪条数据结束
					var end = page * limit;
					if (end > total) {
						end = total;
					}
					
					/**
					 * 过滤条件
					 */
					if(centents && date){
						for (let flowNode of res.approvalBelongFlowNodeRecordList) {
							var nodeDate = new Date(flowNode.CreateDate);
							var flowCentents = JSON.parse(flowNode.ApprovalContent);
							var flowValue = "";
							if(flowCentents && flowCentents.length > 0){
								flowValue = flowCentents[0].value.replace(/\/|p|<|>/g,"");
							}
							var startDate = new Date(date.substring(0,date.indexOf("~")));
							var endDate = new Date(date.substring(date.indexOf("~")+1,date.length))

							if(nodeDate.getFullYear() >= startDate.getFullYear() &&nodeDate.getFullYear() <=  endDate.getFullYear() && flowValue.indexOf(centents) >= 0){
								if(nodeDate.getMonth() >= startDate.getMonth() && nodeDate.getMonth() <= endDate.getMonth()){
									if(nodeDate.getDate() >= startDate.getDate() && nodeDate.getDate() <= endDate.getDate()){
										list.push(flowNode)
									}
								}
							}
						}
					}else if(centents || date){
						if(centents){
							for (let flowNode of res.approvalBelongFlowNodeRecordList) {
								var flowCentents = JSON.parse(flowNode.ApprovalContent);
								var flowValue = "";
								if(flowCentents && flowCentents.length > 0){
									flowValue = flowCentents[0].value.replace(/\/|p|<|>/g,"");
								}
								if(flowValue.indexOf(centents) >= 0){
									list.push(flowNode)
								}
							}
						}
						if(date){
							for (let flowNode of res.approvalBelongFlowNodeRecordList) {
								var nodeDate = new Date(flowNode.CreateDate);
								var startDate = new Date(date.substring(0,date.indexOf("~")));
								var endDate = new Date(date.substring(date.indexOf("~")+1,date.length))
								if(nodeDate.getFullYear() >= startDate.getFullYear() &&nodeDate.getFullYear() <=  endDate.getFullYear() ){
									if(nodeDate.getMonth() >= startDate.getMonth() && nodeDate.getMonth() <= endDate.getMonth()){
										if(nodeDate.getDate() >= startDate.getDate() && nodeDate.getDate() <= endDate.getDate()){
											list.push(flowNode)
										}
									}
								}
							}
						}
					}else{
						//取分页数据
						for (var i = start; i < end; i++) {
							list.push(res.approvalBelongFlowNodeRecordList[i]);
						}
					}
					
					if(list.length == 0){
						$(".layui-table-page").css("visibility","hidden");
						msg = "无数据";
					}
					return {
						"code" : "",
						"data" : list,
						"msg" : msg,
						"count" : total,
					}
				}
			})
			$(".layui-table-view").css("position", "relative").css("top", "18%")
		})
		$(".layui-table-view").css("position", "relative").css("top", "18%")
	},
	getClos : function() {
		clos = [ {
			title : '审批人',
			align : "center",
			templet : function(d) {
				return d.approvalBelongFlowNodeName + ":\t" + d.userName;
			}
		}, {
			title : '时间',
			align : "center",
			templet : function(d) {
				return layui.util.toDateString(d.CreateDate, "yyyy-MM-dd");
			}
		}, {
			title : '审批内容',
			align : "center",
			templet : function(d) {
				d.ApprovalContent = JSON.parse(d.ApprovalContent)
				d.ApprovalContent[0].value = d.ApprovalContent[0].value.replace(/\/|<|>|p/g, "")
				return d.ApprovalContent[0].value;
			}
		}, {
			title : '附件信息',
			align : "center",
			templet : function(d) {
				var html = ""
				var isShow = approval.getFile(d.ApprovalBelongFlowNodeRecordCode);
				if(isShow){
					html += "<div  onclick=approval.openFileWindow('" + d.ApprovalBelongFlowNodeRecordCode + "') ><i class='layui-icon layui-icon-download-circle i_icon' title='点击下载'></i></div>"
				}
				return html;
			}
		} ]
		return clos;
	},
	openFileWindow : function(approvalBelongFlowNodeRecordCode) {
		var url = basePath + "mdms/functionModule/newDoctorFile/shuttleWindow.html?approvalBelongFlowNodeRecordCode=" + approvalBelongFlowNodeRecordCode
		layer.open({
			title : "文件下载",
			content : url,
			area : [ '700px', '500px' ],
			type : 2
		})
	},
	//获取文件信息
	getFile : function(approvalBelongFlowNodeRecordCode) {
		var isShow = false;
		$.ajax({
			url : basePath + "frame/fileUpload/getAttachments.spring",
			data : {
				belongToCode : approvalBelongFlowNodeRecordCode
			},
			dataType : "json",
			type : "GET",
			async : false,
			skipDataCheck : true,
			success : function(data){
				if(data.attachmentsList){
					if(data.attachmentsList.length > 0){
						isShow = true;
					}
				}
			}
		})
		return isShow;
	},
	getApprovalBelongFlowNodeList : function(customFormFilledCode) {
		return $.ajax({
			url : basePath + "/frame/approvalFlow/getApprovalBelongFlowNodeList.spring",
			data : {
				"approvalBelongCode" : customFormFilledCode,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
				"funCode" : assemblys.top.mdms.mdmsConstant.MDMS_APPLY_SEARCH,
			}
		}).then(function(data){
			if(data.currentApprovalBelongFlowNode){
				var approvalBelongCode = data.currentApprovalBelongFlowNode.approvalBelongCode;
				var approvalBelongFlowNodeCode = data.currentApprovalBelongFlowNode.approvalBelongFlowNodeCode;
				$.ajax({
					url : basePath + "/frame/approvalFlow/initFlow.spring",
					data : {
						approvalBelongCode : approvalBelongCode,
						approvalBelongFlowNodeCode : approvalBelongFlowNodeCode,
						appCode : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
						funCode : assemblys.top.mdms.mdmsConstant.MDMS_APPLY_SEARCH
					},
					success : function(flow){
						data.approverList = flow.approverList;
					}
				})
			}
			return data
		})
	},
	getTag : function(data) {
		
		var top_arr = [], bottom_arr = [], control = {
			top : {
				style : {

				}
			},
			bottom : {
				style : {

				}
			}
		},approvalBelongFlowNodeList = [], logMap = [],approverList = [];
		
		if(data.approvalBelongFlowNodeList){
			approvalBelongFlowNodeList = data.approvalBelongFlowNodeList;
		}
		
		if(data.logMap){
			logMap = data.logMap;
		}

		if(data.approverList){
			approverList = data.approverList;
		}
		//approvalBelongFlowNodeList.length =3;
		var approvalConditionType = "";
		if(approvalBelongFlowNodeList.length > 0){
			approvalConditionType = this.judgeType(approvalBelongFlowNodeList[0].approvalConditionType);
		}
		var start_node_text = [],logList = [];
		/**
		 * 开始节点
		 */
		if(approvalBelongFlowNodeList.length >0){
			
			if(approvalBelongFlowNodeList[0].state == -3){
				start_node_text.push( {
					"tagName" : "li",
					"className" : "item",
					"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;跳过审批 - 一键结束跳过"
				})
			}else if(approvalBelongFlowNodeList[0].state == 1 || approvalBelongFlowNodeList[0].state == 2){
				logList = logMap[approvalBelongFlowNodeList[0].approvalIndex];
				if(logList && logList.length > 0){
					for (let log of logList) {
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+layui.util.toDateString(log.createDate, "yyyy-MM-dd HH:mm:ss")
						})
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+log.logContent
						})
					}
				}
			}else if( (approvalBelongFlowNodeList[0].state == 1 || approvalBelongFlowNodeList[0].state == 0 ) && approvalBelongFlowNodeList[0].current == 1){
				if(approverList.length > 0){
					for (let list of approverList) {
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+list.deptName + "-" + list.userName
						})
					}
				}
			}
			
			top_arr.push({
				"tagName" : "div",
				"className" : "node_block_left",
				"children" : [ {
					"tagName" : "div",
					"children" : [ {
						"tagName" : "i",
						"className" : " layui-icon2 suspendIcon skin-div-font",
						"innerHTML" : "" + ( approvalBelongFlowNodeList[0].state == 0 || approvalBelongFlowNodeList[0].state == 2  ? "&#xe66a;" : "&#xe6fb;"),
						"attr":{
							"onclick" : "" +( (approvalBelongFlowNodeList[0].state == 0 || approvalBelongFlowNodeList[0].state == 2 ) && approvalBelongFlowNodeList[0].current == 0 ? "" : "approval.suspendWindow(this)")
						},
						"children" : [ {
							"tagName" : "span",
							"className" : "suspendText",
							"children" : [{
								"tagName" : "ul",
								"children" : start_node_text
							}]
						} ]
					} ]
				}, {
					"tagName" : "div",
					"className" : "node_block_span",
					"style" : {
						"width" : "140px"
					},
					"children" : [ {
						"tagName" : "span",
						"innerHTML" : approvalConditionType + "- 开始"
					}, {
						"tagName" : "br"
					}, {
						"tagName" : "strong",
						"innerHTML" :  approvalBelongFlowNodeList[0].approvedRecordList[0].deptName + " - " + approvalBelongFlowNodeList[0].approvedRecordList[0].userName 
					} ]
				} ]
			})
		}

		if (approvalBelongFlowNodeList.length <= 5 && approvalBelongFlowNodeList.length > 0) {
			var left = 0, length = approvalBelongFlowNodeList.length - 2;
			left = (0.6 / length) * 100;
			if (length <= 1) {
				left = 45;
			}
			control.bottom.style.display = "none";
			control.top.style["border-style"] = "dashed none none none";
			control.top.style["border-radius"] = "inherit";
			control.top.style["top"] = "65px";
			for (var i = 1; i < approvalBelongFlowNodeList.length - 1; i++) {
				var approvalConditionType_node = this.judgeType(approvalBelongFlowNodeList[i].approvalConditionType)
				start_node_text = [];
				logList = [];
				if(approvalBelongFlowNodeList.length > 0 && approvalBelongFlowNodeList[i].state == -3){
					start_node_text.push( {
						"tagName" : "li",
						"className" : "item",
						"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;跳过审批 - 一键结束跳过"
					})
				}else if(approvalBelongFlowNodeList[i].state == 1 || approvalBelongFlowNodeList[i].current == 2){
					logList = logMap[approvalBelongFlowNodeList[i].approvalIndex];
					if(logList && logList.length > 0){
						for (let log of logList) {
							start_node_text.push( {
								"tagName" : "li",
								"className" : "item",
								"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+layui.util.toDateString(log.createDate, "yyyy-MM-dd HH:mm:ss")
							})
							start_node_text.push( {
								"tagName" : "li",
								"className" : "item",
								"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+log.logContent
							})
						}
					}
				}else if((approvalBelongFlowNodeList[i].state == 1 || approvalBelongFlowNodeList[i].state == 0) && approvalBelongFlowNodeList[i].current == 1){
					if(approverList.length > 0){
						for (let list of approverList) {
							start_node_text.push( {
								"tagName" : "li",
								"className" : "item",
								"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+list.deptName + "-" + list.userName
							})
						}
					}
				}
				
				top_arr.push({
					"tagName" : "div",
					"className" : "node_block_left",
					"style" : {
						"margin-left" : left + "%"
					},
					"children" : [ {
						"tagName" : "div",
						"children" : [ {
							"tagName" : "i",
							"className" : " layui-icon2 suspendIcon skin-div-font",
							"innerHTML" : "" + (approvalBelongFlowNodeList[i].state == 0 ? "&#xe66a;" : "&#xe6fb;"),
							"attr":{
								"onclick" : "" +((approvalBelongFlowNodeList[i].state == 0 || approvalBelongFlowNodeList[i].state == 2) && approvalBelongFlowNodeList[i].current == 0  ? "" : "approval.suspendWindow(this)")
							},
							"children" : [ {
								"tagName" : "span",
								"className" : "suspendText",
								"children" : [{
									"tagName" : "ul",
									"children" : start_node_text
								}]
							} ]
						} ]
					}, {
						"tagName" : "div",
						"className" : "node_block_span",
						"style" : {
							"width" : "140px"
						},
						"children" : [ {
							"tagName" : "span",
							"innerHTML" : approvalConditionType_node
						}, {
							"tagName" : "br"
						}, {
							"tagName" : "strong",
							"innerHTML" : "" + approvalBelongFlowNodeList[i].approvalBelongFlowNodeName
						} ]
					} ]
				})
			}
			
			/**
			 * 结尾节点
			 */
			var approvalConditionType_end = this.judgeType(approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].approvalConditionType);
			start_node_text = [];
			logList = [];
			if(approvalBelongFlowNodeList.length > 0 && approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == -3){
				start_node_text.push( {
					"tagName" : "li",
					"className" : "item_left",
					"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;跳过审批 - 一键结束跳过"
				})
			}else if(approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 1 || approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].current == 2){
				logList = logMap[approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].approvalIndex];
				if(logList && logList.length > 0){
					for (let log of logList) {
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item_left",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+layui.util.toDateString(log.createDate, "yyyy-MM-dd HH:mm:ss")
						})
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item_left",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+log.logContent
						})
					}
				}else{
					start_node_text.push( {
						"tagName" : "li",
						"className" : "item_left",
						"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;;结束 - 结束节点"
					})
				}
			}else if((approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 1 || approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 0) && approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].current == 1){
				if(approverList.length > 0){
					for (let list of approverList) {
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item_left",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+list.deptName + "-" + list.userName
						})
					}
				}
			}

			top_arr.push({
				"tagName" : "div",
				"className" : "",
				"style" : {
					"float" : "right",
					"position" : "relative",
					"left" : "122px"
				},
				"children" : [ {
					"tagName" : "div",
					"children" : [ {
						"tagName" : "i",
						"className" : " layui-icon2 suspendIcon skin-div-font",
						"innerHTML" : "" + (approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 0 ? "&#xe66a;" : "&#xe6fb;"),
						"attr":{
							"onclick" : "" +((approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 0 || approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 2) && approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].current == 0? "" : "approval.suspendWindow(this)")
						},
						"children" : [ {
							"tagName" : "span",
							"className" : "suspendText_left",
							"children" : [{
								"tagName" : "ul",
								"children" : start_node_text
							}]
						} ]
					} ]
				}, {
					"tagName" : "div",
					"className" : "node_block_span",
					"style" : {
						"width" : "140px",
					},
					"children" : [ {
						"tagName" : "span",
						"innerHTML" : approvalConditionType_end
					}, {
						"tagName" : "br"
					}, {
						"tagName" : "strong",
						"innerHTML" : "" + approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].approvalBelongFlowNodeName
					} ]
				} ]
			})

		} else if(approvalBelongFlowNodeList.length > 0) {
			var end = approvalBelongFlowNodeList.length - 2, flag = true, top_end = 0, bottom_start = 0, top_left = 0, bottom_left = 0;
			while (flag) {
				if (top_end + bottom_start != end) {
					top_end += 1;
				}
				
				if (bottom_start + top_end != end) {
					bottom_start += 1;
				}
				
				if (top_end + bottom_start == end) {
					flag = false;
				}
			}
			top_left = (0.6 / top_end) * 100;
			bottom_left = (0.6 / bottom_start) * 100;
			bottom_left -= 5;
			/**
			 * 头部中间节点
			 */
			for (var i = 1; i <= top_end; i++) {
				var approvalConditionType_node = this.judgeType(approvalBelongFlowNodeList[i].approvalConditionType);
				start_node_text = [];
				logList = [];
			
				if(approvalBelongFlowNodeList[i].state == -3){
					start_node_text.push( {
						"tagName" : "li",
						"className" : "item",
						"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;跳过审批 - 一键结束跳过"
					})
				}else if(approvalBelongFlowNodeList[i].state == 1 || approvalBelongFlowNodeList[i].current == 2 ){
					logList = logMap[approvalBelongFlowNodeList[i].approvalIndex];
					if(logList && logList.length > 0){
							for (let log of logList) {
								start_node_text.push( {
									"tagName" : "li",
									"className" : "item",
									"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+layui.util.toDateString(log.createDate, "yyyy-MM-dd HH:mm:ss")
								})
								start_node_text.push( {
									"tagName" : "li",
									"className" : "item",
									"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+log.logContent
								})
						}
					}
				}else if((approvalBelongFlowNodeList[i].state == 0 || approvalBelongFlowNodeList[i].state == 2) && approvalBelongFlowNodeList[i].current == 1){
					if(approverList.length > 0){
						for (let list of approverList) {
							start_node_text.push( {
								"tagName" : "li",
								"className" : "item",
								"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+list.deptName + "-" + list.userName
							})
						}
					}
				}

				top_arr.push({
					"tagName" : "div",
					"className" : "node_block_left",
					"style" : {
						"margin-left" : top_left + "%"
					},
					"children" : [ {
						"tagName" : "div",
						"children" : [ {
							"tagName" : "i",
							"className" : " layui-icon2 suspendIcon skin-div-font",
							"innerHTML" : "" + (approvalBelongFlowNodeList[i].state == 1  ? "&#xe6fb;" : "&#xe66a;"),
							"attr":{
								"onclick" : "" +((approvalBelongFlowNodeList[i].state == 0 || approvalBelongFlowNodeList[i].state == 2) && approvalBelongFlowNodeList[i].current == 0 ? "" : "approval.suspendWindow(this)")
							},
							"children" : [ {
								"tagName" : "span",
								"className" : "suspendText",
								"children" : [{
									"tagName" : "ul",
									"children" : start_node_text
								}]
							} ]
						} ]
					}, {
						"tagName" : "div",
						"className" : "node_block_span",
						"style" : {
							"width" : "140px"
						},
						"children" : [ {
							"tagName" : "span",
							"innerHTML" : approvalConditionType_node
						}, {
							"tagName" : "br"
						}, {
							"tagName" : "strong",
							"innerHTML" : "" + approvalBelongFlowNodeList[i].approvalBelongFlowNodeName
						} ]
					} ]
				})
			}
			
			top_arr.push({
				"tagName" : "div",
				"children" : [ {
					"tagName" : "div",
					"className" : "node_block_right",
					"children" : [ {
						"tagName" : "i",
						"className" : "layui-icon2 right_icon skin-div-font",
						"innerHTML" : "&#xe920;"
					} ]
				} ]
			})

			/**
			 * 末尾节点
			 */
			var approvalConditionType = this.judgeType(approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].approvalConditionType)
			start_node_text = []
			logList = [];
			if(approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == -3){
				start_node_text.push( {
					"tagName" : "li",
					"className" : "item",
					"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;跳过审批 - 一键结束跳过"
				})
			}else if(approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 1  || approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].current == 2){
				logList = logMap[approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].approvalIndex];
				if(logList && logList.length > 0){
					for (let log of logList) {
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+layui.util.toDateString(log.createDate, "yyyy-MM-dd HH:mm:ss")
						})
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+log.logContent
						})
					}
				}else{
					start_node_text.push( {
						"tagName" : "li",
						"className" : "item",
						"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;结束 - 结束节点"
					})
				}
			}else if((approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 0 || approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 2) && approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].current == 1){
				if(approverList.length > 0){
					for (let list of approverList) {
						start_node_text.push( {
							"tagName" : "li",
							"className" : "item",
							"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+list.deptName + "-" + list.userName
						})
					}
				}
			}

			bottom_arr.push({
				"tagName" : "div",
				"className" : "node_block_left",
				"children" : [ {
					"tagName" : "div",
					"style" : {
						"cursor" : 'pointer'
					},
					"attr":{
						"onclick" : "" + ((approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 0 || approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 2) && approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].current == 0 ? "" : "approval.suspendWindow(this)")
					},
					"children" : [ {
						"tagName" : "i",
						"className" : "layui-icon2 right_icon skin-div-font suspendIcon",
						"innerHTML" : "" + (approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].state == 1 ? "&#xe6fb;" : "&#xe66a;"),
						"children" : [ {
							"tagName" : "span",
							"className" : "suspendText",
							"children" : [{
								"tagName" : "ul",
								"children" : start_node_text
							}]
						} ]
					} ]
				}, {
					"tagName" : "div",
					"style" : {
						"width" : "100px"
					},
					"className" : "node_block_span",
					"children" : [ {
						"tagName" : "span",
						"innerHTML" : approvalConditionType
					}, {
						"tagName" : "br"
					}, {
						"tagName" : "strong",
						"innerHTML" : approvalBelongFlowNodeList[approvalBelongFlowNodeList.length - 1].approvalBelongFlowNodeName
					} ]
				} ]
			})

			/**
			 * 末尾中间节点
			 */
			for (var i = top_end + 1; i < approvalBelongFlowNodeList.length - 1; i++) {
				start_node_text = [];
				logList = [];
				if(approvalBelongFlowNodeList[i].state == -3){
					start_node_text.push( {
						"tagName" : "li",
						"className" : "item",
						"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;跳过审批 - 一键结束跳过"
					})
				}else if(approvalBelongFlowNodeList[i].state == 1  || approvalBelongFlowNodeList[i].current == 2){
					logList = logMap[approvalBelongFlowNodeList[i].approvalIndex];
					if(logList && logList.length > 0){
						for (let log of logList) {
							start_node_text.push( {
								"tagName" : "li",
								"className" : "item",
								"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+layui.util.toDateString(log.createDate, "yyyy-MM-dd HH:mm:ss")
							})
							start_node_text.push( {
								"tagName" : "li",
								"className" : "item",
								"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+log.logContent
							})
						}
					}
				}else if((approvalBelongFlowNodeList[i].state == 0 || approvalBelongFlowNodeList[i].state == 2) && approvalBelongFlowNodeList[i].current == 1){
					if(approverList.length > 0){
						for (let list of approverList) {
							start_node_text.push( {
								"tagName" : "li",
								"className" : "item",
								"innerHTML" : 	"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+list.deptName + "-" + list.userName
							})
						}
					}
				}
				
				bottom_arr.push({
					"tagName" : "div",
					"className" : " node_block_left_margin_left",
					"style" : {
						"float" : "right",
						"margin-right" : bottom_left + "%",
						"margin-left" : "0px"
					},
					"children" : [ {
						"tagName" : "div",
						"className" : "",
						"children" : [ {
							"tagName" : "i",
							"className" : "layui-icon2 suspendIcon skin-div-font",
							"innerHTML" : "" + (approvalBelongFlowNodeList[i].state == 1 ? "&#xe6fb;" : "&#xe66a;"),
							"attr":{
								"onclick" : "" + ((approvalBelongFlowNodeList[i].state == 0 || approvalBelongFlowNodeList[i].state == 2 ) && approvalBelongFlowNodeList[i].current == 0 ? "" : "approval.suspendWindow(this)")
							},
							"children" : [ {
								"tagName" : "span",
								"className" : "suspendText",
								"children" : [{
									"tagName" : "ul",
									"children" :start_node_text  
								}]
							} ]
						} ]
					}, {
						"tagName" : "div",
						"className" : "node_block_span",
						"style" : {
							"width" : "100px"
						},
						"children" : [ {
							"tagName" : "span",
							"innerHTML" : approvalConditionType
						}, {
							"tagName" : "br"
						}, {
							"tagName" : "strong",
							"innerHTML" : approvalBelongFlowNodeList[i].approvalBelongFlowNodeName
						} ]
					} ]
				})
			}

		}
		return approval.dynamicTag(top_arr, bottom_arr, control);
	},
	/**
	 * 判断节点类型
	 */
	judgeType : function(approvalConditionTypes) {

		var approvalConditionType = "";
		
		switch (approvalConditionTypes) {
		case 0:
			approvalConditionType = "普通节点";
			break;
		case 1:
			approvalConditionType = "动态节点";
		case 2:
			approvalConditionType = "循环节点";
		case 3:
			approvalConditionType = "会签节点";
		default:
			break;
		}
		return approvalConditionType;
	},
	/**
	 * 封装动态标签
	 */
	dynamicTag : function(top_arr, bottom_arr, control) {

		var ar = [];
		var div_content = {
			"tagName" : "div",
			"className" : "content",
			"style" : {
				"display" : top_arr.length == 0 ? "none" : ""
			},
			"children" : [ {
				"tagName" : "div",
				"className" : "bottom_div",
				"children" : [ {
					"tagName" : "div",
					"className" : "bottom_top_div skin-div-border",
					"style" : control.top.style,
					"children" : [ {
						"tagName" : "div",
						"className" : "top_node",
						"children" : top_arr
					} ]
				}, {
					"tagName" : "div",
					"className" : 'bottom_bottom_div skin-div-border',
					"style" : control.bottom.style,
					"children" : [ {
						"tagName" : "div",
						"className" : "top_node2",
						"children" : bottom_arr
					} ]
				} ]
			} ]
		};
		
		var table_content_div = {
			"tagName" : "div",
			"className" : "table_content",
//			"style" : {
//				"height" : "100%",
//				"position" : "absolute",
//				"width" : "100%"
//			},
			"children" : [ {
				"tagName" : "div",
				"className" : "table_header",
				"children" : [ {
					"tagName" : "div",
					"className" : "table_header_left",
					"children" : [ {
						"tagName" : "img",
						"className" : "table_header_img",
						"attr" : {
							"src" : "image/approval/stamp.png"
						}
					}, {
						"tagName" : "h2",
						"className" : "h2 table_header_h",
						"innerHTML" : "档案审批记录"
					} ]
				}, {
					"tagName" : "div",
					"className" : "table_header_right skin-div-css",
					"children" : [{
						"tagName" : "input",
						"attr" : {
							"type" : "text",
							"id" : "test1",
							"placeholder" :"请选择时间范围"
						},
						"className" : "layui-input",
						"style":{
						    "display": "inline-block",
						    "width" : "180px"
						}
					},  {
						"tagName" : "input",
						"attr":{
							"name" : "searchApproval",
							"type" : "text",
							"placeholder" :"审批内容"
						},
						"className" : "layui-input",
						"style" : {
							"display": "inline-block",
							"width" : "180px",
							"margin-left": "5px"
						}
					}, {
						"tagName" : "i",
						"attr" : {
							"id" : "approvalSearch"
						},
						"className" : "layui-icon2 skin-div-font",
						"innerHTML" : "&#xe701;"
					} ]
				} ]
			}, {
				"tagName" : "div",
				"children" : [ {
					"tagName" : "table",
					"attr" : {
						"id" : "list",
						"lay-filter" : "list"
					}
				} ]
			} ]
		};
		
		ar.push(div_content);
		ar.push(table_content_div);
		
		return ar;
	},
	/**
	 * 鼠标悬浮详情信息
	 */
	suspendWindow : function(elem) {
		$("i span").each(function(index,span){
			if(approval.this_elem != null && approval.this_elem[0]== $(this)[0]){
				$($(this)[0]).css("visibility","hidden");
				approval.this_elem = null;
			}else{
				if($(this)[0] == $($(elem)).find("span")[0]) {
					approval.this_elem = $($(this)[0])
					$($(this)[0]).css("visibility","visible");
				}else{
					$($(this)[0]).css("visibility","hidden");
				}
			}
		})
	}
}