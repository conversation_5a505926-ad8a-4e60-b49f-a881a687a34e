var behaviorRecord = {
	init : function(elem) {
		$(elem).empty();
		behaviorRecord.showRecord(param.get("customFormFilledCode")).then(function(record) {
			var behaviorRecordList = [];
			if (record.behaviorRecordList && record.behaviorRecordList.length > 0) {
				behaviorRecordList = record.behaviorRecordList;
			}
			behaviorRecord.render(elem, behaviorRecordList, param.get("customFormFilledCode"));
		})
	},
	render : function(elem, behaviorRecordList, customFormFilledCode) {
		var list = behaviorRecordList;
		$.each(list, function(index, temp) {
			// 序号
			temp["index"] = index + 1;
		});
		
		var length = list.length;
		var mapping = [ {
			name : "操作",
			width : 30,
			opt : [ {
				"classModel" : "1",
				"className" : "layui-icon-search",
				"onclick" : function(data) {
					behaviorRecord.behaviorRecordEdit(data.BehaviorRecordId, 1);
				}
			}, {
				"classModel" : "1",
				"className" : "layui-icon-edit",
				"show" : function(data) {
					if (((param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && userCode == data.OptUserCode) || param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES) && param.get("behaviorRecordEdit") == 'true') {
						return true;
					} else {
						return false;
					}
					return true;
				},
				"onclick" : function(data) {
					behaviorRecord.behaviorRecordEdit(data.BehaviorRecordId, 0);
				}
			}, {
				"classModel" : "1",
				"className" : "layui-icon-delete",
				"show" : function(data) {
					if (((param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && userCode == data.OptUserCode) || param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES) && param.get("behaviorRecordDel") == 'true') {
						return true;
						
					} else {
						return false;
					}
				},
				"onclick" : function(data) {
					behaviorRecord.behaviorRecordDel(data.BehaviorRecordId, elem);
				}
			} ]
		}, {
			name : "序号",
			width : 30,
			value : "DictName",
			templet : function(data) {
				return data.index;
			}
		}, {
			name : "时间",
			value : "OptDate"
		}, {
			name : "事件性质",
			value : "DictName"
		}, {
			name : "情况摘要",
			value : "Summary"
		} ]
		// 渲染
		initCustomDetail.initTableList(elem, mapping, list);
		if (param.get("formStatus") != 1 && param.get("behaviorRecordAdd") == 'true') {
			$(elem).find("table").before('<div style="float: right; margin-bottom: 5px;;margin-top:9px;"><button type="button" class="layui-btn layui-btn-sm" onclick="behaviorRecord.addBehaviorRecord(\'' + customFormFilledCode + '\');">新增</button></div>');
		}
	},
	//新增医疗安全行为记录
	addBehaviorRecord : function(customFormFilledCode) {
		var url = basePath + "mdms/functionModule/newTechniqueManager/behaviorRecordEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName") + "&behaviorRecord=true";
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "behaviorRecordEdit",
			area : [ '850px', '550px' ],
			title : false,
			scrollbar : false,
			content : url
		});
		
	},
	//修改医疗安全行为记录
	behaviorRecordEdit : function(behaviorRecordId, showOrEdit) {
		var id = parseInt(behaviorRecordId);
		var html = "behaviorRecordEdit.html";
		if (showOrEdit == 1) {
			html = "behaviorRecordView.html";
		}
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "certifiToEdit",
			area : [ '850px', '550px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&behaviorRecordId=" + behaviorRecordId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&behaviorRecord=true"
		});
	},
	//删除医疗安全行为记录
	behaviorRecordDel : function(behaviorRecordId, elem) {
		layer.confirm('确定要删除吗?', function(index) {
			$.ajax({
				url : basePath + "/mdms/behaviorRecord/deleteBehaviorRecord.spring",
				type : "post",
				data : {
					behaviorRecordId : behaviorRecordId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					behaviorRecord.init(elem);
				});
				return data;
			});
			layer.close(index);
		});
	},
	// 医疗安全行为记录
	showRecord : function(customFormFilledCode) {
		return $.ajax({
			url : basePath + "/mdms/behaviorRecord/getBehaviorRecordList.spring",
			type : "get",
			data : {
				"customFormFilledCode" : customFormFilledCode
			},
			dataType : "json"
		})
	}
}