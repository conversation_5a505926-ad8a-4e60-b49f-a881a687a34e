var checkRoomRightEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		$("span[class='head1_text fw700']").text("查房授权");
		checkRoomRightEdit.getCheckRoomRight();
		
		checkRoomRightEdit.initLayui();
		
	},
	initLayui : function() {
		
		//加载时间选择器 - 更多请查看官网
		var layDate = layui.laydate;
		layDate.render({
			elem : '#optDate',
			type : 'datetime',
			format : 'yyyy-MM-dd HH:mm',
			trigger : 'click',
			min : new Date().getTime()
		});
		layDate.render({
			elem : '#optEndDate',
			trigger : 'click',
			type : 'datetime',
			format : 'yyyy-MM-dd HH:mm',
			min : new Date().getTime()
		});
		
		layui.form.on("submit(save)", function() {
			checkRoomRightEdit.saveCheckRoomRight();
			return false;
		});
		layui.form.render();
	},
	
	getCheckRoomRight : function() {
		return $.ajax({
			url : basePath + "mdms/checkRoomRight/getCheckRoomRight.spring",
			data : {
				checkRoomRightId : param.get("checkRoomRightId")
			}
		}).then(function(data) {
			if (data.checkRoomRight) {
				//恢复
				param.set("optDate", data.checkRoomRight.optDate);
				param.set("optEndDate", data.checkRoomRight.optEndDate);
				$("#saveBtn").val("恢复");
			}
			return data;
		});
	},
	
	saveCheckRoomRight : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/checkRoomRight/saveCheckRoomRight.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("授权成功", function() {
				window.isSubmit = false;
				assemblys.closeWindow();
				parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
				parent.parent.newDoctorInfo.holdAuthority();
			});
			return data;
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}