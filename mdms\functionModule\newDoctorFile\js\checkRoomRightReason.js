var checkRoomRightReason = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		$("span[class='head1_text fw700']").text("处方授权");
		var isVaild = param.get("isValid")
		if (isVaild == "0") {
			$("#savebtn").val("暂停");
		}
		checkRoomRightReason.initLayui();
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			checkRoomRightReason.saveReason();
		});
	},
	saveReason : function() {
		//暂停 
		if (param.get("isValid") == "0") {
			checkRoomRightReason.stopOrStarAR(param.get("checkRoomRightId"), $(".showReason").val(), param.get("dictName"), param.get("customFormFilledCode"));
		} else {
			//回收
			checkRoomRightReason.deleteAR(param.get("checkRoomRightId"), $(".showReason").val(), param.get("dictName"), param.get("customFormFilledCode"));
		}
		
	},
	
	deleteAR : function(checkRoomRightId, reason, dictName, customFormFilledCode) {
		//hwx 2024年3月21日下午3:51:00 重复提交问题
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		layer.confirm("确定要回收吗？", function() {
			$.ajax({
				url : basePath + "mdms/checkRoomRight/updateCheckRoomRight.spring",
				type : "post",
				data : {
					checkRoomRightId : checkRoomRightId,
					reason : reason,
					dictName : dictName,
					customFormFilledCode : customFormFilledCode,
					type : 4
				}
			}).then(function(data) {
				assemblys.msg("回收成功", function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
				});
				window.isSubmit = false;
			});
		});
	},
	
	stopOrStarAR : function(checkRoomRightId, reason, rmosName, customFormFilledCode) {
		var reConfirm = "";
		var reMessage = "";
		if (param.get("isValid") == "0") {
			reConfirm = "确定要暂停吗？";
			reMessage = "暂停成功";
		}
		layer.confirm(reConfirm, function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/checkRoomRight/saveCheckRoomRight.spring",
				type : "post",
				data : {
					checkRoomRightId : checkRoomRightId,
					reason : reason,
					rmosName : rmosName,
					customFormFilledCode : customFormFilledCode,
					type : 2,//
					isValid : param.get("isValid"),
					userCode : param.get("userCode"),
				}
			}).then(function(data) {
				assemblys.msg(reMessage, function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
				});
				window.isSubmit = false;
			});
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}