var customCommon = {
	
	//表单修改内容
	getCustomFormUserValue : function(win) {
		$.ajax({
			url : basePath + "mdms/mdmsCommon/getCustomFormUserValue.spring",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
				var newValueList = data.newValueList;
				if (newValueList.length > 0) {
					win.$("span:contains(基本信息)").css("color", "red");
					win.$("span:contains(基本信息)").text("基本信息（红色部分为被回退部分）");
					var $customFieldCode = [];
					for (var i = 0; i < newValueList.length; i++) {
						var $onecustomFieldCode = newValueList[i].CustomFieldCode;
						if ($.inArray($onecustomFieldCode, $customFieldCode) == -1) {
							$customFieldCode.push($onecustomFieldCode);
						}
					}
					if ($customFieldCode.length > 0) {
						for (var i = 0; i < $customFieldCode.length; i++) {
							var $name = $customFieldCode[i] + "-0";
							win.$("input[name='" + $name + "']").each(function() {
								$(this).next().removeClass("layui-form-checked");
								$(this).parent().next().removeClass("second_show");
								$(this).parent().next().find("input").each(function() {
									$(this).next().removeClass("layui-form-checked")
								});
							});
						}
					}
					
					for (var i = 0; i < newValueList.length; i++) {
						
						var list = newValueList[i];
						var customFieldCode = list.CustomFieldCode;
						var businessCode = list.BusinessCode;
						var customTextValue = list.CustomTextValue;
						var remark = list.remark;
						var selectValue = list.selectValue;
						var CustomFieldSet = list.CustomFieldSet;
						var CustomFieldName = list.CustomFieldName;
						var CustomOptionSetLevel = list.CustomOptionSetLevel;
						var copyIndex = list.copyIndex;
						var copyCode = customFieldCode + "-" + copyIndex;
						
						if (list.CustomOptionSetCode) {//是单选多选的
							if (CustomFieldSet == "select") {
								customCommon.onselect(businessCode, selectValue, false, win, copyIndex, copyCode);
							} else {
								if (CustomOptionSetLevel == 1) {
									if (win.$("input[level='1'][customoptionsetcontent='" + selectValue + "'][name='" + copyCode + "']").length > 0) {
										if (CustomFieldSet == "radio") {//单选按钮
											win.$("input[level='1'][customoptionsetcontent='" + selectValue + "'][name='" + copyCode + "']").next().find(".third").css("background-color", "red");
											//hwx 2024年6月4日下午3:08:22 单选回显选择
											win.initCustomFormTemplate.setValue(win.$("input[optionremark='" + selectValue + "']").prop("name"), win.$("input[optionremark='" + selectValue + "']").val());
										} else {//复选按钮
											win.$("input[level='1'][customoptionsetcontent='" + selectValue + "'][name='" + copyCode + "']").prop("checked", true);
											win.$("input[level='1'][customoptionsetcontent='" + selectValue + "'][name='" + copyCode + "']").next().addClass("layui-form-checked");
											win.$("input[customoptionsetcontent='" + selectValue + "'][name='" + copyCode + "']").parent().next().addClass("second_show");
										}
										
										win.$("span:contains(" + CustomFieldName + ")").css("color", "red");
									}
									
								} else {
									if (win.$("input[level='2'][customoptionsetcontent='" + selectValue + "'][copyCode='" + copyCode + "']").length > 0) {
										win.$("input[level='2'][customoptionsetcontent='" + selectValue + "'][copyCode='" + copyCode + "']").prop("checked", true);
										win.$("input[level='2'][customoptionsetcontent='" + selectValue + "'][copyCode='" + copyCode + "']").next().addClass("layui-form-checked");
										win.$("input[customoptionsetcontent='" + selectValue + "'][copyCode='" + copyCode + "']").parent().next().addClass("second_show");
										win.$("span:contains(" + CustomFieldName + ")").css("color", "red");
									}
								}
								
							}
							
						} else if (CustomFieldSet == "org") {
							win.$("[name='" + customFieldCode + "-" + copyIndex + "']").val(customTextValue + "/" + remark);
						} else {//文本框文本域日期
						
							win.$("[name='" + customFieldCode + "-" + copyIndex + "']").val(customTextValue);
						}
						win.$("[name='" + customFieldCode + "-" + copyIndex + "']").parent().parent().parent().parent().find("span:contains(" + CustomFieldName + ")").css("color", "red");
						
					}
				}
				
			}
		});
	},
	
	onselect : function(businessCode, val, isHide, win, seq, copyCode) {
		var $select2 = win.$("select[name='" + copyCode + "']");
		if (seq != "" && seq != undefined) {
			$select2 = win.$("select[customfieldbusinesscode='" + businessCode + "'][name$='" + seq + "']");
		}
		var $obj = win.$("select[name='" + copyCode + "']").next().children().find("dd[lay-content='" + val + "']");
		$select2.val($($obj).attr("lay-value")).change();
		var elem = $select2[0];
		if (elem != undefined) {
			var eventField = elem.eventField;
			var value = $select2.val();
			var index = elem.name.split("-")[1];
			var $dl = $select2.next().children("dl");
			var $dd = $dl.children("dd[lay-value='" + value + "']");
			$dl.prev().children("input").val($dd.text());
			$dd.addClass("layui-this").siblings().removeClass("layui-this");
			if (isHide) {
				if (seq != "") {
					win.$("select[customfieldbusinesscode='" + businessCode + "'][name$='" + seq + "']").parent().addClass("layui-hide");
					win.$("select[customfieldbusinesscode='" + businessCode + "'][name$='" + seq + "']").parent().parent().append("<span class='layui-input input_item item1'>" + val + "</span>");
				} else {
					win.$("select[customfieldbusinesscode='" + businessCode + "']").parent().addClass("layui-hide");
					win.$("select[customfieldbusinesscode='" + businessCode + "']").parent().parent().append("<span class='layui-input input_item item1'>" + val + "</span>");
				}
				
			}
		}
	}
}