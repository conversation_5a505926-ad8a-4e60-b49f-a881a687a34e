var doctorNegative = {
	isInit : true,
	doctorNegativeList : [],
	adverseEventList : 1,
	complaintDisputeList : 1,
	ADVERSE_EVENT_HTML : "myAdverseEventsList.html",
	COMPLAIN_DISPUTE_HTML : "myComplainingDisputeList.html",
	hasAdd : false,
	negativeType : 0,
	/**
	 * 初始化
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/20 16:41
	 * @return {@link null}
	 * @throws
	 * @param contentID
	 **/
	init : function(contentID, tag) {
		if (tag == 0) {//hwx 2023年9月15日下午2:50:03 处理医师负面从短信息进入跳转时重复加载问题
			doctorNegative.doctorNegativeListInit().then(function(data) {
				doctorNegative.hasAdd = data.hasAdd;
				doctorNegative.getTab(contentID);
			});
		}
	},
	/**
	 * 医师负面初始化
	 * @title:
	 * @description:
	 * @author: zh
	 * @version:
	 * @date:  2023-08-03 10:41:47 10:41
	 * @param: * @param null
	 * @return
	 * @throws
	 **/
	doctorNegativeListInit : function() {
		return $.ajax({
			url : basePath + "mdms/doctorNegative/doctorNegativeListInit.spring",
			type : "get",
			dataType : "json"
		})
	},
	/**
	 * 获取页面
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/20 16:42
	 * @return {@link null}
	 * @throws
	 * @param contentID
	 **/
	getTab : function(contentID) {
		return doctorNegative.getDictList(assemblys.top.mdms.mdmsConstant.FMSJLX).then(function(data) {
			return doctorNegative.getDictList(assemblys.top.mdms.mdmsConstant.COMPLAINTSWITCH).then(function(c) {
				return doctorNegative.getDictList(assemblys.top.mdms.mdmsConstant.EVENTREPORTSWITCH).then(function(e) {
					if (e.dictList && data.dictList) {
						if (e.dictList) {
							for (var i = 0; i < e.dictList.length; i++) {
								e.dictList[i].dictName = "不良事件";
								data.dictList.push(e.dictList[i]);
							}
						}
					}
					if (c.dictList && data.dictList) {
						if (c.dictList) {
							for (var i = 0; i < c.dictList.length; i++) {
								c.dictList[i].dictName = "投诉纠纷";
								data.dictList.push(c.dictList[i]);
							}
						}
					}
					return data;
				});
			});
		}).then(function(data) {
			var newData = [];
			var inners = [];
			var dictList = data.dictList || [];
			var isArea = false;
			var isMdms = false;
			for (var i = 0; i < dictList.length; i++) {
				var dict = dictList[i];
				var htmUrl = "";
				var url = "";//hwx 2023-8-16 不可为空
				if (dict.dictCode == assemblys.top.mdms.mdmsConstant.WDQJCYP) {//不良
					isArea = true;
				} else if (dict.dictCode == assemblys.top.mdms.mdmsConstant.WRZSXBL) {//投诉纠纷
					isMdms = true;
				}
				if (isArea && dict.dictCode == assemblys.top.mdms.mdmsConstant.FTNEVENTURL) {
					continue;
				}
				if (!isArea && dict.dictCode == assemblys.top.mdms.mdmsConstant.FTNEVENTURL) {
					htmUrl = doctorNegative.ADVERSE_EVENT_HTML;
					url = "1";
				}
				if (isMdms && dict.dictCode == assemblys.top.mdms.mdmsConstant.FTNCOMPLAINTURL) {
					continue;
				}
				if (!isMdms && dict.dictCode == assemblys.top.mdms.mdmsConstant.FTNCOMPLAINTURL) {
					htmUrl = doctorNegative.COMPLAIN_DISPUTE_HTML;
					url = "1";
				}
				var obj_2 = {
					isShow : 1,
					innerHtml : dict.dictName,
					type : dict.dictCode,
					url : url,
					htmlUrl : htmUrl,
					businessCode : dict.businessCode
				};
				inners.push(obj_2);
			}
			var container = {
				"tagName" : "div",
				"className" : "containerNegative",
				"children" : []
			};
			
			var tabDiv = {
				"tagName" : "div",
				"className" : "tabDiv",
				"children" : []
			};
			
			var bottomDiv = {
				"tagName" : "div",
				"className" : "bottomDiv",
				"attr" : {
					"id" : "bottomDiv"
				},
				"children" : []
			};
			
			var btn = {
				"tagName" : "div",
				"className" : "btn",
				"children" : [ {
					"tagName" : "button",
					"className" : "layui-btn layui-btn-sm",
					"attr" : {
						"type" : "button"
					},
					"onclick" : doctorNegative.toEditDoctorNegative,
					"innerHTML" : "新增"
				} ]
			}

			var btn2 = {
				"tagName" : "div",
				"className" : "btn",
				"children" : [ {
					"tagName" : "button",
					"className" : "layui-btn layui-btn-sm",
					"attr" : {
						"type" : "button"
					},
					"onclick" : doctorNegative.appealUserRecord,
					"innerHTML" : "医师申诉记录"
				} ]
			}

			for (var i = 0; i < inners.length; i++) {
				var inner = inners[i];
				var obj = {
					"tagName" : "button",
					"className" : "layui-btn layui-btn-sm  bottomNe ",
					"attr" : {
						"type" : "bottom",
						"hasType" : ""
					},
					"innerHTML" : "",
					"onclick" : doctorNegative.cutType
				};
				
				if (i > 0) {
					obj.className += "background-white";
				}
				
				if (inner.isShow == 1) {
					obj.innerHTML = inner.innerHtml;
					obj.attr.hasType = inner.type;
					obj.attr.isShow = inner.isShow;
					obj.attr.url = inner.url;
					obj.attr.htmlUrl = inner.htmlUrl;
					obj.attr.businessCode = inner.businessCode;
					tabDiv.children.push(obj);
				}
				
			}
			
			if (param.get("formStatus") != 1 && param.get("doctorNegativeAdd") == 'true' && param.get("funCode") !== assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF) {
				tabDiv.children.push(btn);
			}
			
			if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && doctorNegative.hasAdd) {
				tabDiv.children.push(btn2);
			}
			container.children.push(tabDiv);
			container.children.push(bottomDiv);
			newData.push(container);
			$(contentID).parent().css("position", "relative");
			$(contentID).parent().css("overflow-y", "hidden");
			assemblys.createElement(newData, $(contentID)[0]);
			
			if (window.negativeType) {
				$(".bottomNe[hastype=" + window.negativeType + "]").click();
				window.negativeType = "";
			} else {
				$(".bottomNe:eq(0)").click();
			}
			
		});
	},
	/**
	 * 切换类型
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/25 17:20
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	cutType : function(e) {
		var classs = "layui-btn layui-btn-sm  bottomNe background-white";
		$(".bottomNe").each(function(i, o) {
			if ($(o) !== $(e.target)) {
				classs = "layui-btn layui-btn-sm  bottomNe background-white";
				$(o).attr("class", classs);
			}
		});
		classs = "layui-btn layui-btn-sm  bottomNe color-font";
		$(e.target).attr("class", classs);
		
		var type = $(e.target).attr("hasType");
		var isShow = $(e.target).attr("isShow");
		var url = $(e.target).attr("url");
		var html = $(e.target).attr("htmlUrl");
		doctorNegative.negativeType = type;
		doctorNegative.getListByType(type, isShow, url, html);
		window.type = type;
	},
	/**
	 * 根据类型获取数据
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/25 17:42
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	getListByType : function(type, isShow, url, html) {
		var contentID = ".bottomDiv";
		$(contentID).empty();
		if (isShow == 1 && !url) {
			$(".bottomDiv").css("overflow", "auto");
			$(".btn").removeClass("layui-hide");
			doctorNegative.getDoctorList(contentID, type);
		} else {
			$(".btn").addClass("layui-hide");
			$(".bottomDiv").css("overflow", "hidden");
			doctorNegative.implantIframe(url, html);
		}
	},
	/**
	 * 获取字典
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/27 10:50
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	getDictList : function(dictTypeCode) {
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : dictTypeCode,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
				"compNo" : ""
			},
			dataType : "json",
			skipDataCheck : true
		})
	},
	/**
	 * 获取医师负面
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/26 10:12
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	getDoctorList : function(contentID, type) {
		
		if (window.negativeType) {
			type = window.negativeType;
		}
		
		doctorNegative.getDoctorNegativeList(type).then(function(data) {
			var doctorNegativeList = data.doctorNegativeList;
			
			$.each(doctorNegativeList, function(index, temp) {
				// 序号
				temp["index"] = index + 1;
			});
			
			var mapping = doctorNegative.getCols();
			
			// 渲染
			initCustomDetail.initTableList(contentID, mapping.mapping, doctorNegativeList);
			
			return data;
		}).then(function(data) {
			
			var finds = [];
			var find = $("#doctorNegativeDiv .bottomNe ");
			
			find.each(function(i, o) {
				if (!$(o).attr("htmlurl")) {
					finds.push($(o).attr("hastype"));
				}
			});
			finds = finds.join(",");
			
			return doctorNegative.getDoctorNegativeList(finds).then(function(dsc) {
				doctorNegative.doctorNegativeList = dsc.doctorNegativeList || [];
			});
		}).then(function(data) {
			
			var obj = {};
			
			obj[assemblys.top.mdms.mdmsConstant.OTHERURL] = {
				suffix : basePath + "mdms/adverseEvent/getInterfaceData.spring",
				type : 1,
				rType : "POST"
			};
			obj[assemblys.top.mdms.mdmsConstant.FTNEVENTURL] = {
				suffix : "aers/adverseEvents/getAdverseEventsPager.spring",
				type : 0,
				rType : "GET"
			};
			obj[assemblys.top.mdms.mdmsConstant.OTHERCOMPLAINTURL] = {
				suffix : basePath + "mdms/complainingDispute/getInterfaceData.spring",
				type : 1,
				rType : "POST"
			};
			obj[assemblys.top.mdms.mdmsConstant.FTNCOMPLAINTURL] = {
				suffix : "cdmsv2/myComplaintList/getMyComplaintList.spring",
				type : 0,
				rType : "POST"
			};
			
			var at = doctorNegative.ADVERSE_EVENT_HTML;
			var ad = $("button[htmlurl='" + at + "'] ");
			var attr = $(ad).attr("url");
			var businesscode = $(ad).attr("businesscode");
			var bsVal = attr;
			
			if (attr && obj[businesscode]) {
				
				if (obj[businesscode].type == 1) {
					attr = obj[businesscode].suffix;
				} else {
					attr = attr + obj[businesscode].suffix;
				}
				var reType = obj[businesscode].rType;
				var cType = obj[businesscode].type;
				doctorNegative.getAdverseEventComplaintList(attr, reType, cType, bsVal).then(function(dsc) {
					
					if (dsc.items) {
						doctorNegative.adverseEventList = dsc.items || [];
					}
					
					if (dsc.list && dsc.list.queryList) {
						doctorNegative.adverseEventList = dsc.list.queryList || [];
					}
				});
			}
			
			at = doctorNegative.COMPLAIN_DISPUTE_HTML;
			ad = $("button[htmlurl='" + at + "'] ");
			attr = $(ad).attr("url");
			bsVal = $(ad).attr("url");
			businesscode = $(ad).attr("businesscode");
			
			if (attr && obj[businesscode]) {
				if (obj[businesscode].type == 1) {
					attr = obj[businesscode].suffix;
				} else {
					attr = attr + obj[businesscode].suffix;
				}
				var reType = obj[businesscode].rType;
				var cType = obj[businesscode].type;
				doctorNegative.getAdverseEventComplaintList(attr, reType, cType, bsVal).then(function(dsc) {
					if (dsc.items) {
						doctorNegative.complaintDisputeList = dsc.items || [];
					}
					
					if (dsc.list && dsc.list.queryList) {
						doctorNegative.complaintDisputeList = dsc.list.queryList || [];
					}
				});
			}
			
			return data;
		});
	},
	/**
	 * 获取数据
	 * @title:
	 * @description:
	 * @author: zh
	 * @version:
	 * @date:  2023-08-01 10:23:59 10:23
	 * @param: * @param null
	 * @return
	 * @throws
	 **/
	getDoctorNegativeList : function(type) {
		return $.ajax({
			url : basePath + "/mdms/doctorNegative/getDoctorNegativeList.spring",
			type : "get",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"type" : type
			},
			dataType : "json",
		});
	},
	
	/**
	 * 获取不良和投诉纠纷
	 * @title:
	 * @description:
	 * @author: zh
	 * @version:
	 * @date:  2023-08-01 11:10:56 11:10
	 * @param: * @param null
	 * @return
	 * @throws
	 **/
	getAdverseEventComplaintList : function(url, type, cType, interFaceCode) {
		
		var data = {
			"compNo" : param.get("compNo"),
			"curPageNum" : 1,
			"pageSize" : 99999999,
			"userCode" : param.get("userCode")
		};
		
		var pa = {
			dataType : "json"
		};
		
		var contentType = "";
		
		if (cType == 1) {
			data["interfaceCode"] = interFaceCode;
			data["pageSize"] = 50000000;
			data = JSON.stringify(data);
			contentType = "application/json";
		}
		
		if (url) {
			pa["url"] = url;
		}
		
		if (contentType) {
			pa["contentType"] = contentType;
		}
		
		if (type) {
			pa["type"] = type;
		}
		
		if (data) {
			pa["data"] = data;
		}
		
		return $.ajax(pa);
	},
	
	/**
	 * 获取数据
	 * @title:
	 * @description:
	 * @author: zh
	 * @version:
	 * @date:  2023-08-01 10:55:09 10:55
	 * @param: * @param null
	 * @return
	 * @throws
	 **/
	getResult : function() {
		var adverseEventList = doctorNegative.adverseEventList;
		var doctorNegativeList = doctorNegative.doctorNegativeList;
		var complaintDisputeList = doctorNegative.complaintDisputeList;
		var obj = {};
		
		if (adverseEventList != 1) {
			obj["aeList"] = [];
			for (var i = 0; i < adverseEventList.length; i++) {
				var ad = adverseEventList[i];
				var tema = [];
				tema.push(assemblys.htmlDecode(ad.eventCodeNo || ad.EventCodeNo) || "");
				tema.push(assemblys.htmlDecode(ad.TfEventStatus || ad.DraftStatus) || "");
				tema.push(assemblys.htmlDecode(ad.eventName || ad.EventType) || "");
				tema.push(assemblys.htmlDecode(ad.EventLevel) || "");
				tema.push(assemblys.htmlDecode(ad.SpecOccurTime) || "");
				tema.push(assemblys.htmlDecode(ad.reporterName || ad.ReporterName) || "");
				tema.push(assemblys.htmlDecode(ad.inputDate || ad.InputDate) || "");
				tema.push(assemblys.htmlDecode(ad.FollowDeptName) || "");
				tema.push(assemblys.htmlDecode(ad.caseHistoryNum || ad.Casehistorynum) || "");
				tema.push(assemblys.htmlDecode(ad.specBadName) || "");
				obj.aeList.push(tema);
			}
		}
		
		if (complaintDisputeList != 1) {
			obj["cdList"] = [];
			for (var i = 0; i < complaintDisputeList.length; i++) {
				var cd = complaintDisputeList[i];
				var tema = [];
				
				tema.push(cd.CustomFormFilledCode || "");
				
				if (cd.Status != null) {
					tema.push(assemblys.htmlDecode(cd.Status) || "");
				} else {
					tema.push("已结案");
				}
				
				tema.push(assemblys.htmlDecode(cd.CustomFormName) || "");
				tema.push(assemblys.htmlDecode(cd.CreateUserName) || "");
				tema.push(assemblys.htmlDecode(cd.CreateDate) || "");
				tema.push(assemblys.htmlDecode(cd.ComplaintHospitalizationNO) || "");
				tema.push(assemblys.htmlDecode(cd.ComplaintPatientName) || "");
				tema.push(assemblys.htmlDecode(cd.ComplaintCause) || "");
				tema.push(assemblys.htmlDecode(cd.ComplaintRemark) || "");
				obj.cdList.push(tema);
			}
		}
		
		if (doctorNegativeList.length > 0) {
			obj["dnList"] = [];
			for (var i = 0; i < doctorNegativeList.length; i++) {
				var dn = doctorNegativeList[i];
				var tema = [];
				
				tema.push(i + 1 || "");
				tema.push(assemblys.htmlDecode(dn.negativeType) || "");
				tema.push(assemblys.htmlDecode(dn.statusName) || "");
				tema.push(assemblys.dateToStr(dn.negativeDate, 'yyyy-MM-dd'));
				tema.push(assemblys.htmlDecode(dn.eventLocation));
				tema.push(assemblys.htmlDecode(dn.natureEventName) || "");
				tema.push(assemblys.htmlDecode(dn.remark) || "");
				obj.dnList.push(tema);
			}
		}
		
		return obj;
	},
	
	/**
	 * 获取列
	 * @title:
	 * @description:
	 * @author: zh
	 * @version:
	 * @date:  2023-07-31 16:49:28 16:49
	 * @param: * @param null
	 * @return
	 * @throws
	 **/
	getCols : function() {
		
		var obj = {
			mapping : [],
			mappingArr : [],
			aeArr : [ "事件编号", "状态", "事件类型", "事件等级", "发生日期", "上报人", "上报时间", "当前跟进科室", "住院号", "患者姓名" ],
			cdArr : [ "事件编号", "状态", "事件分类", "上报人", "上报时间", "住院号", "患者姓名", "原因", "备注" ]
		};
		
		var mapping = [ {
			name : "操作",
			width : "10%",
			opt : [ {
				"classModel" : "1",
				"className" : "layui-icon-list",
				"title" : "申诉登记",
				"show" : function(data) {
					if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && (data.status == 1 || data.status == 3 || data.status == 5) && doctorNegative.hasAdd) {
						return true;
					} else {
						return false;
					}
				},
				"onclick" : function(data) {
					doctorNegative.toRepresentation(data, 0);
				}
			}, {
				"classModel" : "1",
				"className" : "layui-icon-search",
				"title" : "查看",
				"onclick" : function(data) {
					doctorNegative.toEditDoctorNegative(data.doctorNegativeId, 1);
				}
			}, {
				"classModel" : "1",
				"className" : "layui-icon-edit",
				"title" : "编辑",
				"show" : function(data) {
					if (param.get("funCode") != assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && param.get("doctorNegativeEdit") == 'true' && data.status == 1 && param.get("formStatus") != 1) {
						return true;
					}
					return false;
				},
				"onclick" : function(data) {
					doctorNegative.toEditDoctorNegative(data.doctorNegativeId, 0);
				}
			}, {
				"classModel" : "1",
				"className" : "layui-icon-edit",
				"title" : "申诉编辑",
				"show" : function(data) {
					if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF) {
						if (data.status == 0 && doctorNegative.hasAdd) {
							return true;
						}
					}
					return false;
				},
				"onclick" : function(data) {
					doctorNegative.toRepresentation(data);
				}
			}, {
				"classModel" : "1",
				"className" : "layui-icon-delete",
				"title" : "删除",
				"show" : function(data) {
					if ((param.get("funCode") != assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF)) {
						if (param.get("doctorNegativeDel") == 'true' && data.status == 1 && param.get("formStatus") != 1) {
							return true;
						} else {
							return false;
						}
					}
				},
				"onclick" : function(data) {
					doctorNegative.deleteDoctorNegative(data.doctorNegativeId, data.negativeTypev);
				}
			} ]
		}, {
			name : "序号",
			width : "8%",
			value : "DictName",
			templet : function(data) {
				return data.index;
			}
		}, {
			name : '事件性质',
			width : "15%",
			value : "natureEventName",
			templet : function(data) {
				var html = "";
				html = "<font style='color: " + data.color + "'>" + data.natureEventName + "</font>"
				if (data.natureEvent == "YLANJD") {
					html = "<font style='color: " + data.color + ";font-weight:bold;'>" + data.natureEventName + "</font>"
				}
				return html;
			}
		}, {
			name : "事件发生时间",
			width : "25%",
			value : "negativeDate"
		}, {
			name : "具体描述",
			width : "32%",
			value : "remark"
		}, {
			name : "状态",
			width : '10%',
			value : "status",
			templet : function(data) {
				
				var html = "";
				
				if (data.status == 0) {
					html = "<font>" + data.statusName + "</font>"
				}
				
				if (data.status == 1) {
					html = "<font style='color: orange'>" + data.statusName + "</font>"
				}
				
				if (data.status == 2) {
					html = "<font style='color: #FF4500'>" + data.statusName + "</font>"
				}
				
				if (data.status == 3) {
					html = "<font style='color: red'>" + data.statusName + "</font>"
				}
				
				if (data.status == 5) {
					html = "<font style='color: red'>" + data.statusName + "</font>"
				}
				
				return html;
			}
		} ];
		
		for (var i = 0; i < mapping.length; i++) {
			var mapp = mapping[i];
			
			if (mapp.name && mapp.name != '操作') {
				obj.mappingArr.push(mapp.name);
			}
		}
		
		obj.mapping = mapping;
		return obj;
	},
	
	/**
	 * 编辑和新增医师负面
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/26 10:13
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	toEditDoctorNegative : function(id, type) {
		
		// @date 2023/07/26 10:13:05 <AUTHOR> 新增医师负面
		if (!Number.isInteger(Number.parseInt(id)) && !Number.isInteger(Number.parseInt(type))) {
			id = 0;
			type = 0;
		}
		
		var html = "doctorNegativeEdit.html";
		if (type == 1) {
			html = "doctorNegativeView.html";
		}
		
		var negativeType = window.type;
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditDoctorNegative",
			area : [ '850px', '500px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?onlyShow=" + type + "&funCode=" + param.get("funCode") + "&doctorNegativeId=" + id + "&negativeTypev=" + negativeType
		});
	},
	/**
	 * 删除负面
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/27 10:33
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	deleteDoctorNegative : function(id, negativeType) {
		layer.confirm("确定要删除吗？", function() {
			return $.ajax({
				url : basePath + "mdms/doctorNegative/deleteDoctorNegative.spring",
				type : "post",
				data : {
					doctorNegativeId : id
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					$("#doctorNegativeDiv").empty();
					doctorNegative.init("#doctorNegativeDiv");
					window.negativeType = negativeType;
				});
				return data;
			});
		})
	},
	/**
	 * 申诉个人记录
	 * @description
	 * <AUTHOR>
	 * @date 2023/7/27 10:33
	 * @param  * @param null
	 * @return {@link null}
	 * @throws
	 **/
	appealUserRecord : function() {
		var url = basePath + "mdms/functionModule/mdmsCustomList/mdmsCustomList.html";
		url += "?customFormTypeCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_CODE;
		url += "&customFormTypeMenuCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_MENU_CODE;
		url += "&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		url += "&businessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTOR_NE_RE;
		url += "&onlyShow=1";
		url += "&type=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTOR_NE_RE;
		url += "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_DOCTORNEGATIVE;
		
		assemblys.top.addTab(null, '医师申诉记录', url);
	},
	/**
	 * 负面申诉
	 * @title:
	 * @description:
	 * @author: zh
	 * @version:
	 * @date:  2023-08-02 14:41:10 14:41
	 * @param: * @param null
	 * @return
	 * @throws
	 **/
	toRepresentation : function(d) {
		
		var url = basePath + "frame/customForm/customFormTemplate.html?customFormCode=&customFormBusinessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTOR_NE_RE + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode") + "&type=1";
		url += "&doctorNegativeId=" + d.doctorNegativeId;
		url += "&negativeType=" + d.negativeType;
		url += "&natureEventName=" + d.natureEventName;
		url += "&negativeDate=" + assemblys.dateToStr(d.negativeDate, 'yyyy-MM-dd');
		url += "&eventLocation=" + d.eventLocation;
		url += "&doctorNegativityDesc=" + d.remark;
		if (d.appealCustomFormFilledCode) {
			url += "&customFormFilledCode=" + d.appealCustomFormFilledCode;
		}
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : false,
			scrollbar : false,
			closeBtn : 1,//右上角的×按钮
			area : [ '100%', '100%' ],
			content : url
		});
	},
	/**
	 * 不良和纠纷列表
	 * @title:
	 * @description:
	 * @author: zh
	 * @version:
	 * @date:  2023-07-31 11:47:55 11:47
	 * @param: * @param null
	 * @return
	 * @throws
	 **/
	implantIframe : function(url, html) {
		var src = basePath + "mdms/functionModule/newDoctorFile/" + html + "?userCode=" + param.get("userCode");
		var height = $(".bottomDiv").height();
		var iframe = "<iframe src=" + src + " style='border:0px;height:100%;' width='100%' ></iframe>";
		$(".bottomDiv").append(iframe);
	},
	reload : function() {
		$(".bottomNe[hastype=" + doctorNegative.negativeType + "]").click();
	}
}