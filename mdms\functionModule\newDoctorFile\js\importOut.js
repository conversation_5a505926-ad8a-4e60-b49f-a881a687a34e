var importOut = {
	
	getData : function() {
		return $.ajax({
			url : basePath + "mdms/mdmsCommon/getRightList.spring",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"pageSize" : 9999,
				"isValid" : 1,
				"state" : 1,
				"deptId" : deptId,
			}
		}).then(function(data) {
			var operationList = data.operationList;//手术
			var anesList = data.anesList;//麻醉
			var writeList = data.writeList;//处方
			var roomList = data.roomList;//查房
			var professionList = data.professionList;//亚专业
			
			var nameArray = [ "手术名称", "手术等级", "操作类型", "授权开始时间", "授权结束时间" ];
			var nameArray2 = [ "麻醉分级", "类型", "授权开始时间", "授权结束时间" ];
			var nameArray3 = [ "处方分类", "处方名", "授权开始时间", "授权结束时间", "注意事项" ];
			var nameArray4 = [ "查房等级", "授权开始时间", "授权结束时间" ];
			var nameArray5 = [ "院内代码", "亚专业名称", "内容", "级别", "授权结束时间", "注意事项" ];
			
			//手术
			var valArray = new Array();
			var dataArray = new Array();
			for (var i = 0; i < operationList.length; i++) {
				var arrObj = [ operationList[i].operationName, operationList[i].operationLevelName, operationList[i].operationTypeName, assemblys.dateToStr(operationList[i].createTime), assemblys.dateToStr(operationList[i].createEndTime) ];
				valArray[i] = arrObj

			}
			if (operationList.length == 0) {
				valArray[0] = new Array();
			}
			dataArray[0] = nameArray;
			dataArray[1] = valArray;
			operation = dataArray;
			
			//处方
			valArray = new Array();
			dataArray = new Array();
			for (var i = 0; i < writeList.length; i++) {
				//hwx 2024年5月7日下午3:18:01 修改导出处方注意事项
				var arrObj = [ writeList[i].parentName, writeList[i].RmosName, assemblys.dateToStr(writeList[i].authorizeDate), assemblys.dateToStr(writeList[i].authorizeEndDate), writeList[i].remark ];
				valArray[i] = arrObj

			}
			if (writeList.length == 0) {
				valArray[0] = new Array();
			}
			dataArray[0] = nameArray3;
			dataArray[1] = valArray;
			write = dataArray;
			
			//查房
			valArray = new Array();
			dataArray = new Array();
			for (var i = 0; i < roomList.length; i++) {
				var arrObj = [ roomList[i].checkRoomLevel, assemblys.dateToStr(roomList[i].optDate), assemblys.dateToStr(roomList[i].optEndDate) ];
				valArray[i] = arrObj

			}
			if (roomList.length == 0) {
				valArray[0] = new Array();
			}
			dataArray[0] = nameArray4;
			dataArray[1] = valArray;
			room = dataArray;
			
			//麻醉
			valArray = new Array();
			dataArray = new Array();
			for (var i = 0; i < anesList.length; i++) {
				var arrObj = [ anesList[i].AnClassName, anesList[i].rightTypeName, assemblys.dateToStr(anesList[i].createTime), assemblys.dateToStr(anesList[i].createEndTime) ];
				valArray[i] = arrObj
			}
			if (anesList.length == 0) {
				valArray[0] = new Array();
			}
			dataArray[0] = nameArray2;
			dataArray[1] = valArray;
			anaes = dataArray;
			
			//亚专业
			valArray = new Array();
			dataArray = new Array();
			for (var i = 0; i < professionList.length; i++) {
				var arrObj = [ professionList[i].subProClassHisCode, professionList[i].subProClassName, professionList[i].content, professionList[i].levelName, assemblys.dateToStr(professionList[i].createTime), assemblys.dateToStr(professionList[i].createEndTime) ];
				valArray[i] = arrObj
			}
			if (professionList.length == 0) {
				valArray[0] = new Array();
			}
			dataArray[0] = nameArray5;
			dataArray[1] = valArray;
			profession = dataArray;
			
		});
	},
	//表单修改内容
	getCustomFormLog : function(customFormFilledCode) {
		$.ajax({
			url : basePath + "mdms/mdmsCommon/getCustomFormOptLog.spring",
			type : "post",
			data : {
				"customFormFilledCode" : customFormFilledCode
			},
			dataType : "json",
			success : function(data) {
				
				if (data.customFormLogList.length > 0) {
					for (var i = 0; i < data.customFormLogList.length; i++) {
						var beformValue = data.customFormLogList[i].BeforeValue;
						var afterValue = data.customFormLogList[i].AfterValue;
						var CustomFieldName = data.customFormLogList[i].CustomFieldName;
						
						if (data.customFormLogList[i].CustomFieldName) {
							//$("div[id='physicianInfoPhotoDiv'] td[class='tdLeft skin-div-css']:contains(" + CustomFieldName + ")").next().css("color", "red");
							$("div[class='layui-tab-item layui-show'] td[class='tdLeft skin-div-css']:contains(" + CustomFieldName + ")").next().css("color", "red");
							if ($("div[class='layui-tab-item layui-show'] td:contains(" + CustomFieldName + ")").next().find("i[name='showImportant']").length == 0) {
								$("div[class='layui-tab-item layui-show'] td[class='tdLeft skin-div-css']:contains(" + CustomFieldName + ")").next().append('<i class="layui-icon2" name="showImportant">&#xea1d;</i>');
							}
							$("div[class='layui-tab-item layui-show'] td[class='tdLeft skin-div-css']:contains(" + CustomFieldName + ")").next().attr("title", "修改前是【" + beformValue + "】");
						} else {
							$("li:contains(我的证件)").css("color", "red");
							$("dd:contains(我的证件)").css("color", "red");
							$("input[value='我的证件']").css("color", "red");
							$("div[id='certificateDiv'] td:contains(" + CustomFieldName + ")").css("color", "red");
							$("div[id='certificateDiv'] td:contains(" + afterValue + ")").css("color", "red");
							
						}
					}
				}
			}
		});
	},
}