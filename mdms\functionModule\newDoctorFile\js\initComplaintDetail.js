/**
 * 用于处理页面渲染
 */
var initComplaintDetail = {
		currFollowIndex : -1,
	// 加载TR
	createTr : function(temp) {
		var trTemplate = {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.commentType
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"innerHTML" : temp.commentContent || ""
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : (function() {
					var html = "";
					html += "<div>" + temp.execDeptName + " - " + temp.execUserName + "</div>";
					html += "<div style='color: #A0A0A0;'>" + assemblys.dateToStr(temp.execDate) + "</div>";
					return html;
				})()
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"children" : (function() {
					var tdChildren = [];
					var attaList = temp.attaList;
					if (attaList.length != 0) {
						// 拼装内容
						$.each(attaList, function(j, atta) {
							tdChildren.push({
								"tagName" : "div",
								"attr" : {
									"title" : atta.attachmentName,
									"onclick" : "pubUploader.downLoadAttaPreview('" + atta.attachmentName + "','" +  atta.attachmentURL + "');"
								},
								"style" : {
									"cursor" : "pointer",
									"padding" : "5px"
								},
								"children" : [ {
									"tagName" : "i",
									"className" : "layui-icon layui-icon-download-circle i_icon",
									"attr" : {
										"title" : "点击下载"
									}
								}, {
									"tagName" : "span",
									"innerHTML" : atta.attachmentName
								} ]
							});
						});
					} else {
						tdChildren.push({
							"tagName" : "div",
							"innerHTML" : "无"
						});
					}
					return tdChildren;
				})()
			} ]
		};
		return trTemplate;
	},
	// 加载TR
	createAttaTr : function(temp) {
		var trTemplate = {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"children" : [ {
					"tagName" : "i",
					"className" : "layui-icon layui-icon-download-circle i_icon",
					"style" : {
						"cursor" : "pointer"
					},
					"attr" : {
						"title" : "点击下载",
						"onclick" : "pubUploader.downLoadAttaPreview('" + temp.attachmentName + "','" + temp.attachmentURL + "');"
					}
				} ]
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"innerHTML" : temp.attachmentName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.attachmentSize
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : (temp.createUserCode + " / " + temp.createUserName)
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : assemblys.dateToStr(temp.createDate.time)
			} ]
		};
		return trTemplate;
	},
	createLogTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : (function() {
					return  temp.funName == "表单修改" || temp.funName.indexOf("编辑") > -1 ? '<i class="layui-icon layui-icon-search i_check" title="详情" style="cursor: pointer" optLogID="' + temp.optLogID + '"  onclick="complaintDetail.showOptDetailList(this);"  ></i>' : "";
				})()
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.funName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"innerHTML" : temp.remark
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : (temp.deptName + " - " + temp.optor) + "<br>"+ assemblys.dateToStr(temp.optDate)
			} ]
		};
	},
	createFlowLi : function(flow,nextFlow) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [flow.hasCurrent == 1 && nextFlow && nextFlow.followType == 100 ? 
				{
					"tagName" : "div",
					"className" : "back",
					"innerText" : "回退",
					"children" :[{
						"tagName" : "div",
						"className" : "i_back"
					}]
				} : null, {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : (function() {
							if (flow.hasCurrent == 1) {
								initComplaintDetail.currFollowIndex = flow.followIndex;
								return basePath + "plugins/static/image/image3/jinxingzhong.png";
							} else if (initComplaintDetail.currFollowIndex == -1) {
								return basePath + "plugins/static/image/image3/wancheng.png";
							} else if (flow.followType == 100) {
								return basePath + "plugins/static/image/image3/huitui.png";
							} else {
								return basePath + "plugins/static/image/image3/dengdai.png";
							}
						})(),
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" :  (function() {
						if (flow.hasCurrent == 1) {
							return "right_item  item_red";
						} else if (initComplaintDetail.currFollowIndex == -1) {
							return "right_item  item_green";
						} else {
							return "right_item  item_gray";
						}
					})(),
					"children" : [ {
						"tagName" : "h3",
						"innerHTML" : flow.followName
					}, {
						"tagName" : "span",
						"className" : "item_content",
						"innerHTML" : (function(){
							var deptNames = complaintDetail.deptNames;
							var deptList = flow.deptList;
							var depts = new Array();
							if(flow.deptList){
								for(var index in deptList){
									depts.push(deptNames[deptList[index]["execDeptID"]]);
								}
								return  depts.join("、");
							}
							return "";
						})()
					}, {
						"tagName" : "div",
						"style" : {
							"max-height" : "250px",
							"overflow" : "auto",
							"text-align" : "left",
						},
						"children" : [ {
							"tagName" : "blockquote",
							"className" : "layui-elem-quote ",
							"title" : "科室负责人名单",
							"style" : {
								"display" : flow.userList && flow.userList.length > 0 /*&& flow.hasCurrent == 1*/ ? "block" : "none"
							},
							"children" : (function() {
								var userList = flow.userList;
								var userHtml = [];
								if (userList && userList.length != 0) {
									$.each(userList, function(i, u) {
										userHtml.push({
											"tagName" : "span",
											"className" : "item_content",
											"title" : (u.deptName + " - " + u.userName),
											"innerText" : (u.deptName + " - " + u.userName),
										});
									});
								}
								return userHtml;
							})()
						} ]
					} ]
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : flow.followIndex
					},
					"style" : {
						"text-align" : "left"
					}
				} ]
			} ]
		};
	},
	createFlowLogLi : function(flow, dom) {
		var childLength = dom.children("ul").length;
		// 如果大于1 就是两个以上，它本身还未被渲染
		var hasHide = childLength > 1;
		var ulTemplate = {
			"tagName" : "ul",
			"className" : hasHide ? "layui-hide moreul" : "",
			"children" : [ {
				"tagName" : "li",
				"className" : "right_data",
				"innerHTML" : assemblys.dateToStr(flow.optDate)
			}, {
				"tagName" : "li",
				"className" : "right_text",
				"innerHTML" : flow.remark
			} ]
		};
		if (hasHide) {
			dom.next(".more").remove();
			dom.after("<div class='more'>查看更多<i class='layui-icon'>&#xe625;</i></div>");
		}
		return ulTemplate;
	},
	createFlowOneLi : function(title) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : basePath + "plugins/static/image/image3/wancheng.png",
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" : "right_item  item_green",
					"children" : [ {
						"tagName" : "h3",
						"innerHTML" : "事件上报"
					}, {
						"tagName" : "span",
						"className" : "item_content",
						"innerHTML" : title
					} ]
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : "0"
					},
					"style" : {
						"text-align" : "left"
					}
				} ]
			} ]
		};
	},
	createFlowLastLi : function(temp) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : basePath + "plugins/static/image/image3/" + (complaintDetail.hasClosure ? "wancheng" : "dengdai") + ".png",
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" : "item_unfinished",
					"innerHTML" : "已结案"
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : "-6"
					},
					"style" : {
						"text-align" : "left"
					}
				} ]
			} ]
		};
	},
	createTable : function(temp, contentID) {
		var titleName = temp.commentTypeName + "：" + temp.execDeptName + " - " + temp.execUserName + " - " + assemblys.dateToStr(temp.execDate);
		return {
			"tagName" : "li",
			"className" : temp.index == 1 ? "layui-nav-item subject eventDetail  layui-nav-itemed" : "layui-nav-item subject eventDetail  " ,
			"children" : [ {
				"tagName" : "a",
				"className" : "main_table_title skin-div-css",
				"children" : [ {
					"tagName" : "span",
					"innerHTML" : titleName
				},{
					"tagName" : "span",
					"innerHTML" :  "",
				}, {
					"tagName" : "span",
					"className" : "layui-nav-more"
				} ]
			}, {
				"tagName" : "dl",
				"className" : "layui-nav-child main_table_box",
				"children" : [ {
					"tagName" : "dd",
					"children" : [ {
						"tagName" : "table",
						"className" : "layui-table main_table detail_table",
						"style" : {
							"table-layout" : "fixed"
						},
						"children" : [ {
							"tagName" : "tbody",
							"children" : [ {
								"tagName" : "tr",
								"children" : [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerHTML" : "意见内容"
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"innerHTML" : temp.commentContent
								} ]
							}
							, {
								"tagName" : "tr",
								"children" : [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerHTML" : "附件信息"
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"children" : (function() {
										var tdChildren = [];
										var attaList = temp.attaList;
										if (attaList.length != 0) {
											// 拼装内容
											$.each(attaList, function(j, atta) {
												tdChildren.push({
													"tagName" : "div",
													"attr" : {
														"title" : atta.attachmentName,
														"onclick" : "pubUploader.downLoadAttaPreview('" + atta.attachmentName + "','" +  atta.attachmentURL + "');"
													},
													"style" : {
														"cursor" : "pointer",
														"padding" : "5px"
													},
													"children" : [ {
														"tagName" : "i",
														"className" : "layui-icon layui-icon-download-circle i_icon",
														"attr" : {
															"title" : "点击下载"
														}
													}, {
														"tagName" : "span",
														"innerHTML" : atta.attachmentName
													} ]
												});
											});
										} else {
											tdChildren.push({
												"tagName" : "div",
												"innerHTML" : "无"
											});
										}
										return tdChildren;
									})()
								} ]
							} ]
						} ]
					} ]
				} ]
			} ]
		}
	},
	commentType:{
		"5":"dean",
		"4":"judiciary",
		"3":"lead",
		"8":"insurance",
	},
	createTable2 : function(temp, contentID) {
		var titleName = temp.commentTypeName + "：" + temp.execDeptName + " - " + temp.execUserName + " - " + assemblys.dateToStr(temp.execDate);
		return {
			"tagName" : "li",
			"className" : temp.index == 1 ? "layui-nav-item subject eventDetail  layui-nav-itemed" : "layui-nav-item subject eventDetail  " ,
			"children" : [ {
				"tagName" : "a",
				"className" : "main_table_title skin-div-css",
				"children" : [ {
					"tagName" : "span",
					"innerHTML" : titleName
				},{
					"tagName" : "span",
					"innerHTML" :  "",
				}, {
					"tagName" : "span",
					"className" : "layui-nav-more"
				} ]
			}, {
				"tagName" : "dl",
				"className" : "layui-nav-child main_table_box",
				"children" : [ {
					"tagName" : "dd",
					"children" : [ {
						"tagName" : "table",
						"className" : "layui-table main_table detail_table",
						"style" : {
							"table-layout" : "fixed"
						},
						"children" : [ {
							"tagName" : "tbody",
							"children" : [ temp.commentType == 6 ? {
								"tagName" : "tr",
								"children" : [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerHTML" : "解决途径"
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"innerHTML" : temp.closeFileModeName
								} ]
							} : null,{
								"tagName" : "tr",
								"children" : [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerHTML" : "意见内容"
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"innerHTML" : temp.commentContent
								} ]
							}, function (){ 
								if(temp.commentType != "2"){
									return;
								}
								return {
									"tagName" : "tr",
									"children" : [ {
										"tagName" : "td",
										"className" : "tright",
										"attr" : {
											"colspan" : "1"
										},
										"innerHTML" : "处理科室"
									}, {
										"tagName" : "td",
										"className" : "tleft",
										"attr" : {
											"colspan" : "3"
										},
										"innerHTML" : temp.handleDeptName
									} ]
								}
							}(),
							{
								"tagName" : "tr",
								"children" : [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerHTML" : "附件信息"
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"children" : (function() {
										var tdChildren = [];
										var attaList = temp.attaList;
										if (attaList.length != 0) {
											// 拼装内容
											$.each(attaList, function(j, atta) {
												tdChildren.push({
													"tagName" : "div",
													"attr" : {
														"title" : atta.attachmentName,
														"onclick" : "pubUploader.downLoadAttaPreview('" + atta.attachmentName + "','" +  atta.attachmentURL + "');"
													},
													"style" : {
														"cursor" : "pointer",
														"padding" : "5px"
													},
													"children" : [ {
														"tagName" : "i",
														"className" : "layui-icon layui-icon-download-circle i_icon",
														"attr" : {
															"title" : "点击下载"
														}
													}, {
														"tagName" : "span",
														"innerHTML" : atta.attachmentName
													} ]
												});
											});
										} else {
											tdChildren.push({
												"tagName" : "div",
												"innerHTML" : "无"
											});
										}
										return tdChildren;
									})()
								} ]
							} ]
						} ]
					} ]
				} ]
			} ]
		}
	},
	getApprovalBelongFlowNodeRecordList : function(param) {
		var basePath = param.basePath || window.basePath;
		var appCode = param.appCode;
		var approvalBelongCode = param.approvalBelongCode;
		var selector = param.selector;
		
		return $.ajax({
			url : basePath + "/frame/approvalFlowRecord/getApprovalBelongFlowNodeRecordList.spring",
			data : {
				approvalBelongCode : approvalBelongCode,
				appCode : appCode,
			}
		}).then(function(data) {
			var result = [];
			if (data.approvalBelongFlowNodeRecordList.length > 0) {
				for (var i = 0; i < data.approvalBelongFlowNodeRecordList.length; i++) {
					result.push(initComplaintDetail.createRecord(data.user, basePath, data.approvalBelongFlowNodeRecordList[i], i));
				}
			} else {
				result.push({
					"tagName" : "div",
					"className" : "layui-colla-item",
					"children" : [ {
						"tagName" : "h2",
						"className" : "layui-colla-title skin-div-css",
						"innerHTML" : "无审批记录"
					}, {
						"tagName" : "div",
						"className" : "layui-colla-content layui-show",
						"children" : [ {
							"tagName" : "table",
							"className" : "layui-table main_table detail_table",
							"style" : {
								"table-layout" : "fixed"
							},
							"children" : [ {
								"tagName" : "tr",
								"className" : "layui-table-tr",
								"children" : [ {
									"tagName" : "td",
									"style" : {
										"text-align" : "center"
									},
									"innerText" : "无审批记录"
								} ]
							} ]
						} ]
					} ]
				});
			}
			
			assemblys.createElement({
				tagName : "div",
				className : "layui-collapse",
				children : result
			}, $(selector)[0]);
			
			layui.element.render();
		});
	},
	createRecord : function(user, basePath, approvalBelongFlowNodeRecord, i) {
		var titleName = approvalBelongFlowNodeRecord.approvalBelongFlowNodeName + "：" + approvalBelongFlowNodeRecord.deptName + " - " + approvalBelongFlowNodeRecord.userName + " - " + assemblys.dateToStr(approvalBelongFlowNodeRecord.CreateDate);
		if (approvalBelongFlowNodeRecord.ApprovalType == 2) {
			titleName += "<font style='color: red;'>【回退】</font>";
		}
		
		/*if (user.uID == approvalBelongFlowNodeRecord.ApprovalUID) {
			titleName += '<button type="button" class="layui-btn layui-btn-xs" style="margin-left: 10px;" onclick="event.stopPropagation();approvalFlow.toApproval(' + approvalBelongFlowNodeRecord.ApprovalType + ',\'' + approvalBelongFlowNodeRecord.ApprovalBelongFlowNodeRecordCode
					+ '\');">编辑</button>';
		}*/
		
		var approvalContentList = JSON.parse(approvalBelongFlowNodeRecord.ApprovalContent);
		return {
			"tagName" : "div",
			"className" : "layui-colla-item",
			"children" : [ {
				"tagName" : "h2",
				"className" : "layui-colla-title skin-div-css",
				"innerHTML" : titleName
			}, {
				"tagName" : "div",
				"className" : "layui-colla-content" + (i == 0 ? " layui-show" : ""),
				"children" : [ {
					"tagName" : "table",
					"className" : "layui-table main_table detail_table",
					"style" : {
						"table-layout" : "fixed"
					},
					"children" : (function() {
						var result = [];
						for (var i = 0; i < approvalContentList.length; i++) {
							result.push({
								"tagName" : "tr",
								"className" : "layui-table-tr",
								"children" : [ {
									"tagName" : "td",
									"className" : "tright",
									"attr" : {
										"colspan" : "1"
									},
									"innerText" : approvalContentList[i].name
								}, {
									"tagName" : "td",
									"className" : "tleft",
									"attr" : {
										"colspan" : "3"
									},
									"innerHTML" : approvalContentList[i].value
								} ]
							})
						}
						
						result.push({
							"tagName" : "tr",
							"className" : "layui-table-tr",
							"children" : [ {
								"tagName" : "td",
								"className" : "tright",
								"attr" : {
									"colspan" : "1"
								},
								"innerHTML" : "附件信息"
							}, {
								"tagName" : "td",
								"className" : "tleft",
								"attr" : {
									"colspan" : "3"
								},
								"children" : (function() {
									var tdChildren = [];
									$.ajax({
										url : basePath + "frame/fileUpload/getAttachments.spring",
										data : {
											belongToCode : approvalBelongFlowNodeRecord.ApprovalBelongFlowNodeRecordCode
										},
										async : false,
										skipDataCheck : true,
										success : function(data) {
											if (data.result == "success") {
												if (data.attachmentsList.length > 0) {
													for (var i = 0; i < data.attachmentsList.length; i++) {
														// 拼装内容
														tdChildren.push({
															"tagName" : "div",
															"attr" : {
																"title" : data.attachmentsList[i].attachmentName,
																"onclick" : "approvalFlow.downLoadAttaPreview('" + data.attachmentsList[i].attachmentName + "','" + data.attachmentsList[i].attachmentURL + "');"
															},
															"style" : {
																"cursor" : "pointer",
																"padding" : "5px"
															},
															"children" : [ {
																"tagName" : "i",
																"className" : "layui-icon layui-icon-download-circle i_icon",
																"attr" : {
																	"title" : "点击下载"
																}
															}, {
																"tagName" : "span",
																"innerHTML" : data.attachmentsList[i].attachmentName
															} ]
														});
													}
												} else {
													tdChildren.push({
														"tagName" : "div",
														"innerHTML" : "无"
													});
												}
											}
										}
									})
									return tdChildren;
								})()
							} ]
						});
						
						return result;
					})()
				} ]
			} ]
		}
	},
}
