var majorsAccident = {
	init : function(elem) {
		$(elem).empty();
		majorsAccident.initMajorsAccidentList(param.get("customFormFilledCode"), elem)
	},
	initMajorsAccidentList : function(customFormFilledCode, elem) {
		$.ajax({
			url : basePath + "/mdms/majorsAccident/getMajorsAccidentList.spring",
			type : "get",
			data : {
				"customFormFilledCode" : customFormFilledCode
			},
			dataType : "json",
			success : function(data) {
				var list = data.majorsAccidentList;
				
				$.each(list, function(index, temp) {
					// 序号
					temp["index"] = index + 1;
				});
				
				var length = list.length;
				var mapping = [ {
					name : "操作",
					width : 30,
					opt : [ {
						"classModel" : "1",
						"className" : "layui-icon-search",
						"onclick" : function(data) {
							majorsAccident.majorsAccidentEdit(data.MajorsAccidentId, 1);
						}
					}, {
						"classModel" : "1",
						"className" : "layui-icon-edit",
						"show" : function(data) {
							if (((param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && userCode == data.OptUserCode) || param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES) && param.get("majorAccidentEdit") == 'true') {
								return true;
							} else {
								return false;
							}
						},
						"onclick" : function(data) {
							majorsAccident.majorsAccidentEdit(data.MajorsAccidentId, 0);
						}
					}, {
						"classModel" : "1",
						"className" : "layui-icon-delete",
						"show" : function(data) {
							if (((param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && userCode == data.OptUserCode) || param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES) && param.get("majorAccidentDel") == 'true') {
								return true;
								
							} else {
								return false;
							}
						},
						"onclick" : function(data) {
							majorsAccident.majorsAccidentDel(data.MajorsAccidentId, elem);
						}
					} ]
				}, {
					name : "序号",
					width : 30,
					value : "DictName",
					templet : function(data) {
						return data.index;
					}
				}, {
					name : "发生时间",
					value : "AccidentTime"
				}, {
					name : "发生原因",
					value : "Reason"
				}, {
					name : "事故级别",
					value : "AccidentName"
				}, {
					name : "事故详情",
					value : "AccidentDetails"
				}, {
					name : "处分情况",
					value : "Punishment"
				} ]
				// 渲染
				initCustomDetail.initTableList(elem, mapping, list);
				if (param.get("formStatus") != 1 && param.get("majorAccidentAdd") == 'true') {
					$(elem).find("table").before('<div style="float: right; margin-bottom: 5px;"><button type="button" class="layui-btn layui-btn-sm" onclick="majorsAccident.addMajorsAccident(\'' + customFormFilledCode + '\');">新增</button></div>');
				}
			}
		});
	},
	addMajorsAccident : function(customFormFilledCode) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "majorsAccidentEdit",
			area : [ '900px', '70%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/majorsAccidentEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
		
	},
	majorsAccidentEdit : function(majorsAccidentId, showOrEdit) {
		var html = "majorsAccidentEdit.html";
		if (showOrEdit == 1) {
			html = "majorsAccidentView.html";
		}
		var id = parseInt(majorsAccidentId);
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "majorsAccidentEdit",
			area : [ '900px', '70%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&majorsAccidentId=" + majorsAccidentId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
		});
	},
	majorsAccidentDel : function(majorsAccidentId, elem) {
		layer.confirm('确定要删除吗?', function(index) {
			$.ajax({
				url : basePath + "/mdms/majorsAccident/deleteMajorsAccident.spring",
				type : "post",
				data : {
					majorsAccidentId : majorsAccidentId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					majorsAccident.init(elem);
				});
				return data;
			});
			layer.close(index);
		});
	}
}