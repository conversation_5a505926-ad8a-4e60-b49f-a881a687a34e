var myAdverseEventsList = {
	dictCode : "",
	url : "",
	listInterfaceCode : "",
	detailInterfaceCode : "",
	totalCount : "",
	pageSize : "",
	curPageNum : "",
	init : function() {
		myAdverseEventsList.url();
		// 科进不良
		if (myAdverseEventsList.dictCode == "FtnEventUrl") {
			$("#btnExport").removeClass("layui-hide");
			myAdverseEventsList.getmyAdverseEventsListPager();
			// 第三方不良	
		} else if (myAdverseEventsList.dictCode == "OtherUrl") {
			myAdverseEventsList.thirdPartyAdverseEventList($("#curPageNum").val(), $("#pageSize").val());
			myAdverseEventsList.loadPage();
		}
	},
	// 第三方不良分页组件
	loadPage : function() {
		layui.use([ 'form', 'laypage' ], function() {
			var form = layui.form;
			var laypage = layui.laypage;
			laypage.render({
				elem : 'layui-table-page1',
				count : myAdverseEventsList.totalCount,
				limit : myAdverseEventsList.pageSize,
				curr : myAdverseEventsList.curPageNum,
				limits : [ 10, 20, 50, 100, 500, 1000 ],
				layout : [ 'prev', 'page', 'next', 'skip', 'limit', 'count' ],
				jump : function(obj, first) {
					if (!first) {
						document.getElementById("curPageNum").value = obj.curr;
						document.getElementById("pageSize").value = obj.limit;
						myAdverseEventsList.thirdPartyAdverseEventList($("#curPageNum").val(), $("#pageSize").val());
					}
				}
			});
			form.render("select", "layui-table-page");
		});
	},
	getmyAdverseEventsListPager : function() {
		var cols = [ {
			title : '操作',
			width : "10%",
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_delete" title="浏览" lay-event="toViewMyAdverseEventsList"></i>';
				return html;
			}
		}, {
			title : '序号',
			align : "center",
			width : "8%",
			type : 'numbers'
		}, {
			title : '事件编号',
			width : 140,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.eventCodeNo);
			}
		}, {
			title : '状态',
			width : 80,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.TfEventStatus);
			}
		}, {
			title : '事件类型',
			minWidth : 200,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.eventName);
			}
		}, {
			title : '事件等级',
			width : 90,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.EventLevel);
			}
		}, {
			title : '发生日期',
			width : 160,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.SpecOccurTime);
			}
		}, {
			title : '上报人',
			minWidth : 200,
			align : "center",
			templet : function(d) {
				var html = '';
				html += d.reporterDeptName + "-";
				html += '<span> ' + d.reporterName + ' </span>';
				return html;
			}
		}, {
			title : '上报时间',
			width : 160,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.inputDate);
			}
		},
		//hwx 2023-8-11 已完成数据为空，去掉
//			{
//				title : '当前跟进科室',
//				width : 120,
//				align : "center",
//				templet : function(d) {
//					var html = "";
//					// 状态是已完成不显示当前跟进科室
//					if(d.draftStatus != 7){
//						html = assemblys.htmlEncode(d.FollowDeptName);
//					}
//					return html;
//				}
//			}, 
		{
			title : '住院号',
			width : 80,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.caseHistoryNum);
			}
		}, {
			title : '患者姓名',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.specBadName);
			}
		} ];
		var height = newDoctorInfo.returnHeight(30);
		var width = newDoctorInfo.returnWidth(-200);
		if (height < 200) {
			var showHeight = param.get("showHeight") - 50;
			if (showHeight) {
				height = showHeight;
			} else {
				height = "460";
			}
		}
		if (width < 200) {
			var showWidth = param.get("showWidth") - 380;
			if (showWidth) {
				width = showWidth;
			} else {
				width = "1024";
			}
		}
		assemblys.tableRender({
			elem : '#list',
			url : myAdverseEventsList.url + "aers/adverseEvents/getAdverseEventsPager.spring?" + param.__form(),
			cols : [ cols ],
			height : height,
			width : width,
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				// 工具栏监听
				toViewMyAdverseEventsList : myAdverseEventsList.toViewMyAdverseEventsList,
			}
		});
		
	},
	// 科进不良详情
	toViewMyAdverseEventsList : function(d) {
		var url = basePath + "mdms/functionModule/newDoctorFile/myAdverseEventsListDetail.html?eventID=" + d.eventID + "&eventInputID=" + d.eventInputID + "&insertEventID=" + d.insertEventID;
		url += "&url=" + encodeURIComponent(myAdverseEventsList.url) + "&isAnonymity=" + d.isAnonymity + "&eventName=" + encodeURIComponent(d.eventName) + "&eventCodeNo=" + d.eventCodeNo + "&eventInputTypeName=" + encodeURIComponent(d.eventInputTypeName) + "&dictCode=" + myAdverseEventsList.dictCode;
		//var url = aersBasePath +"aers/assign/toAssignDetail.spring?compNo="+param.get("compNo") +"&eventInputID="+ d.eventInputID+"&returnTrue=1&followDeptID="+d.followDeptID+"&draftStatus=" + d.draftStatus;
		assemblys.top.addTab(null, '事件详情', url);
	},
	// 获取科进不良事件地址和第三方不良事件地址
	url : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictByCode.spring",
			type : "get",
			async : false,
			data : {
				"dictCode" : 'OtherUrl,FtnEventUrl',
				"appCode" : "mdms"
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					myAdverseEventsList.dictCode = data.dict[0].dictCode;
					
					if (myAdverseEventsList.dictCode == "FtnEventUrl") {
						// 科进不良地址
						myAdverseEventsList.url = data.dict[0].dictContent;
					} else if (myAdverseEventsList.dictCode == "OtherUrl") {
						// 第三方不良数据接口,通过字段获取
						var interfaceCodeArray = data.dict[0].dictContent.split(",");
						
						myAdverseEventsList.listInterfaceCode = interfaceCodeArray[0];
						myAdverseEventsList.detailInterfaceCode = interfaceCodeArray[1];
					}
				}
			},
			error : function() {
			}
		});
		
	},
	// 第三方不良列表
	thirdPartyAdverseEventList : function(curPageNum, pageSize) {
		var compNo = param.get("compNo");
		// 参数
		var obj = {
			compNo : compNo,
			interfaceCode : myAdverseEventsList.listInterfaceCode,
			curPageNum : curPageNum,
			pageSize : pageSize
		}

		$.ajax({
			url : basePath + "mdms/adverseEvent/getInterfaceData.spring",
			dataType : "json",
			type : "POST",
			contentType : "application/json",
			data : JSON.stringify(obj),
			async : false,
			success : function(data) {
				if (data.list.result == "success") {
					// 第二次分页查询需要清空table
					$("#expenseTable").empty();
					// 获取分页数据
					myAdverseEventsList.curPageNum = data.list.curPageNum;
					myAdverseEventsList.pageSize = data.list.pageSize;
					myAdverseEventsList.totalCount = data.list.totalPage;
					// 显示导出按钮
					$("#btnExport").removeClass("layui-hide");
					$("#btnExport").attr("onclick", "exportList.exportToExcel('expenseTable','不良事件记录');");
					var queryList = data.list.queryList;
					var table = "<div id='top-badevent' class='layui-form' style='' >";
					table += "<table class='layui-table main_table ' id='expenseTable'></table></div>";
					table += "<div class='layui-table-page  layui-form' style='border-width: 1px; height: 38px; padding: 0px; width: 100%;' lay-filter='layui-table-page'>";
					table += "<div id='layui-table-page1' style='margin: 5px;'></div>";
					table += "</div>";
					
					/*		table+="<colgroup><col width='40'><col width='60'><col width='140'><col width='80'>";
							table+="<col width='250'><col width='100'><col width='120'><col width='120'>";
							table+="<col width='100'><col width='120'><col width='120'><col width='120'><col width='120'>";
							table+="</colgroup></table></div>";*/
					// 清除页面上的table,添加第三方不良列表
					$("#table").empty().html(table);
					// 第二次加载分页
					myAdverseEventsList.loadPage();
					// 添加滚动条
					$("#table").addClass("bodys hasScroll");
					//$("#table").css( {"height":newDoctorInfo.returnHeight(-35),"width": newDoctorInfo.returnWidth(-200)});
					var html = "";
					html += "<tr class='main_title'>";
					html += "<td nowrap='nowrap'>序号</td>";
					html += "<td nowrap='nowrap'>操作</td>";
					html += "<td nowrap='nowrap'>事件编号</td>";
					html += "<td nowrap='nowrap'>状态</td>";
					html += "<td nowrap='nowrap'>事件类型</td>";
					html += "<td nowrap='nowrap'>事件等级</td>";
					html += "<td nowrap='nowrap'>发生日期</td>";
					html += "<td nowrap='nowrap'>上报科室</td>";
					html += "<td nowrap='nowrap'>上报人</td>";
					html += "<td nowrap='nowrap'>上报时间</td>";
					html += "<td nowrap='nowrap'>当前跟进科室</td>";
					html += "<td nowrap='nowrap'>住院号</td>";
					html += "<td nowrap='nowrap'>患者姓名</td>";
					html += "</tr>";
					// 添加表头
					$("#expenseTable").append(html);
					var str = "";
					var len = queryList.length;
					if (len == 0) {
						$("#expenseTable").append("<tr><td style='text-align:center' colspan='13'>无相关记录!</td></tr>");
					}
					$.each(queryList, function(i, n) {
						var td = "";
						td += "<tr>";
						td += "<td style='text-align:center;'>" + (i + 1) + "</td>";
						td += "<td style='text-align:center;'>";
						td += "<i class='layui-icon layui-icon-search i_delete' title='浏览' onclick=\"myAdverseEventsList.toThirdViewMyAdverseEventsList('" + n.EventCodeNo + "','" + n.EventType + "')\";></i>";
						td += "</td>";
						$.each(n, function(j, m) {
							// 事件是已完成状态，不显示跟进部门
							if (n.DraftStatus == "已完成" && j == "FollowDeptName") {
								td += "<td style='text-align:center;'>";
								td += "";
								td += "</td>";
							} else {
								td += "<td  style='text-align:center;'>";
								td += m;
								td += "</td>";
							}
						});
						td += "</tr>";
						// 添加数据到table
						$("#expenseTable").append(td);
					});
				}
			}
		});
	},
	// 第三方不良详情
	toThirdViewMyAdverseEventsList : function(eventCodeNo, eventType) {
		var url = basePath + "mdms/functionModule/newDoctorFile/myAdverseEventsListDetail.html?eventCodeNo=" + eventCodeNo + "&dictCode=" + myAdverseEventsList.dictCode;
		url += "&detailInterfaceCode=" + myAdverseEventsList.detailInterfaceCode + "&compNo=" + param.get("compNo") + "&eventType=" + encodeURIComponent(eventType);
		//var url = aersBasePath +"aers/assign/toAssignDetail.spring?compNo="+param.get("compNo") +"&eventInputID="+ d.eventInputID+"&returnTrue=1&followDeptID="+d.followDeptID+"&draftStatus=" + d.draftStatus;
		assemblys.top.addTab(null, '事件详情', url);
	},

}