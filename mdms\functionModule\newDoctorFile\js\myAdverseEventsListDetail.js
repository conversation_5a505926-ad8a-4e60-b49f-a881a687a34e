
// 获取表单值
var url = param.get("url");
var eventInputID = param.get("eventInputID");
var eventID = param.get("eventID");
var insertEventID = param.get("insertEventID");
var dictCode = param.get("dictCode");
var eventCodeNo = param.get("eventCodeNo");
var myAdverseEventsListDetail = {
		loadDetail : false, 
		// 存储附件内容
		eventAffirmInfoAtta : [],
		init :function(){
			// 科进不良
			if(dictCode=="FtnEventUrl"){
				$("#eventCodeNo").append($("input[attribute='eventCodeNo']").val());
				$("#eventName").append($("input[attribute='eventName']").val());
				$(".button").removeClass("layui-hide");
				$(".tab").removeClass("layui-hide");
				myAdverseEventsListDetail.getEventAffirmInfoAtta();
				myAdverseEventsListDetail.switchEventDetail();
				myAdverseEventsListDetail.switchReviewContent();
				// 第三方不良 
			}else if(dictCode=="OtherUrl"){
				myAdverseEventsListDetail.thirdPartyEventDetail();
			}
		},
		// 事件详情
		switchEventDetail :function () {
			$(".eventDetail").show();
			$("#formDetail").removeClass("layui-hide");
			$("#approvalList").addClass("layui-hide");
			if(myAdverseEventsListDetail.loadDetail){
				return;
			}
			myAdverseEventsListDetail.loadDetail = true;
			$.ajax({
				url : url + "aers/customEventForm/getCustomFormData.spring",
				dataType : "json",
				data : {
					"eventID" : eventID,
					"eventInputID" : eventInputID,
					"insertEventID" : insertEventID,
					"isDetail" : true
				},
				success : function(data) {
					var anonymitySwitch = true;
					var table = "";
					var eventFieldTypeList = data.eventFieldTypeList;
					for (var i = 0; i < eventFieldTypeList.length; i++) {
						if (JSON.stringify(eventFieldTypeList[i].detailValues) != "{}") {
							var li = '<li id="eventInputDetailLi" class="layui-nav-item layui-nav-itemed subject eventDetail" style="display: block;">';
							var eventFieldTypeName = eventFieldTypeList[i].eventFieldTypeName;
							if (eventFieldTypeName == "删除的内容") {
								eventFieldTypeName = "<del style='color: blue;'>"+eventFieldTypeName+"</del>";
							}
							li += '<a href="javascript:;" class="main_table_title skin-div-css">' + eventFieldTypeName + '</a>';
							li += '<dl class="layui-nav-child main_table_box"><dd><table class="layui-table main_table detail_table"><tbody>';
							for (var j = 0; j < eventFieldTypeList[i].maxIndex + 1; j++) {
								if (eventFieldTypeList[i].isCommon) {
									li += '<tr class="layui-table-tr"><td class="tleft" colspan="4" style="color: #009688;">' + eventFieldTypeList[i].eventFieldTypeName + '-' + (j + 1) + '</td>';
									li += '</tr>';
								}
								var eventFieldList = eventFieldTypeList[i].eventFieldList;
								for (var k = 0; k < eventFieldList.length;) {
									var eventField = eventFieldList[k];
									var fieldName = eventField.fieldName + "-" + j;
									var value = eventFieldTypeList[i].detailValues[fieldName] || "";
									if (!eventFieldTypeList[i].isCommon && !value) {
										k++;
										continue;
									}
									var next, nextFieldName, nextValue;
									while ((next = eventFieldList[k + 1])) {
										nextFieldName = eventFieldList[k + 1].fieldName + "-" + j;
										nextValue = eventFieldTypeList[i].detailValues[nextFieldName] || "";
										if (!eventFieldTypeList[i].isCommon && !nextValue) {
											k++;
											continue;
										} else {
											break;
										}
									}
									li += "<tr class='layui-table-tr'>";
									var colspan = next ? 1 : 3;
									
									var isTextArea = false;
									if(eventField.fieldSet == "textarea"){//第一列是文本域需独立一行
										if(next){
											colspan = 3;
											next = null;
											isTextArea = true;
										}
									}else if(next && next.fieldSet == "textarea"){
										colspan = 3;
										next = null;
										isTextArea = true;
									}
									// 是否匿名
									var isHasAnonymity = !eventFieldTypeList[i].commonEventFieldTypeID && eventFieldTypeList[i].eventFieldTypeName != "删除的内容" && param.get("isAnonymity")==1 && anonymitySwitch == "true";
									if (value || eventFieldTypeList[i].isCommon) {
										if(isHasAnonymity ){
											var valueTemp = "<span>***</span>";
											if( hasAnonymityReport == "true"){
												valueTemp +=" <img src='"+basePath+"frame/images/default/ico/eye.png' data-value='"+value+"'  onclick=\"pubOpt.changUserMain(this)\">";
											}
											value = valueTemp;
										}else{
											value = assemblys.escape(value);
										}
										
										li += '<td class="tright" colspan="1" title="' + eventField.fieldContent + '">' + eventField.fieldContent + '</td>';
										li += '<td class="tleft" colspan="' + colspan + '">' +value + '</td>';
									}
									
									if(isTextArea) {
										k++;
									}else if (next && (nextValue || eventFieldTypeList[i].isCommon)) {
										if(isHasAnonymity){
											var valueTemp = "<span>***</span>";
											if(anonymitySwitch == "true"&& hasAnonymityReport == "true"){
												valueTemp +=" <img src='"+basePath+"frame/images/default/ico/eye.png' data-value='"+nextValue+"'  onclick=\"pubOpt.changUserMain(this)\">";
											}
											
											nextValue = valueTemp;
										}else{
											nextValue = assemblys.escape(nextValue);
										}
										
										li += '<td class="tright" colspan="1" title="' + next.fieldContent + '">' + next.fieldContent + '</td>';
										li += '<td class="tleft" colspan="1">' + nextValue + '</td>';
										k = k + 2;
									} else {
										k = eventFieldList.length;
									}
									li += '</tr>';
								}
							}
							li += '</tbody></table></dd></dl></li>';
							table += li;
						}
					}
					$("#eventInputDetailLi").after(table).remove();
					layui.element.render("nav", "eventInputDetailUl");
				}
			});
		},
		// 审核内容
		switchReviewContent : function(){
			$.ajax({
				url : url + "aers/adverseEvents/getEventAuditOpinion.spring",
				dataType : "json",
				data : {
					"eventInputID" : eventInputID
				},
				success : function(data) {
					// 附件内容
					var attachment = myAdverseEventsListDetail.eventAffirmInfoAtta;
					// 审核内容
					var eventAuditOpinion = data.eventAuditOpinion;
					// 有审核内容
				   			if(eventAuditOpinion.length>0){
				   			 var li = "<div class='layui-collapse'>";
				   				// 审核内容li属性
				   				$("li[right='hasAuditContent']").removeClass("layui-hide");
				   				$("li[right='hasAuditContent']").attr("index","1");
				   				$("li[right='hasAuditContent']").attr("hasExport","true");
				   				for(var i = 0;i <eventAuditOpinion.length; i++){
				   					li += "<div class='layui-colla-item'>";
				   					li += "<h2 class='layui-colla-title skin-div-css'>";
				   					li += eventAuditOpinion[i].oldFollowDeptName + "：" + eventAuditOpinion[i].DeptName +" - "+eventAuditOpinion[i].auditUserName + " - " + assemblys.dateToStr(eventAuditOpinion[i].auditDate);
				   					li += "</h2>";
				   					li += "<div class='layui-colla-content layui-show'>";
				   					li += "<table class='layui-table main_table detail_table' style='table-layout: fixed;'>";
				   					li += "<tr class='layui-table-tr'>";
				   					li += "<td class='tright' colspan='1'>";
				   					if(eventAuditOpinion[i].reformMeasure!=''){
										li += "原因分析";
									}else{
										li += "审核内容";
									}
				   					li += "</td>";
				   					li += "<td class='tleft' colspan='3'>";
				   					li += "<p>"+ eventAuditOpinion[i].improvedOpinion +"</p>";
				   					li += "</td>";
				   					li += "</tr>";
				   					
				   					if(eventAuditOpinion[i].reformMeasure!=''){
				   						li += "<tr class='layui-table-tr'>";
				   						li += "<td class='tright' colspan='1'>";
				   						li += "整改措施";
				   						li += "</td>";
				   						li += "<td class='tleft' colspan='3'>";
					   					li += "<p>"+ eventAuditOpinion[i].reformMeasure +"</p>";
					   					li += "</td>";
					   					li += "</tr>";
				   					}
				   					
				   					li += "<tr class='layui-table-tr'>";
				   					li += "<td class='tright' colspan='1'>";
				   					li += "附件信息";
				   					li += "</td>";
				   					li += "<td class='tleft' colspan='3'>";
				   					for(var k=0;k<attachment.length;k++){
										if(eventAuditOpinion[i].improvedAuditID == attachment[k].EventAffirmInfoID){
											li += "<div title='"+attachment[k].AccessName+"' style='cursor: pointer' accessName='"+attachment[k].AccessName +"' accessUrl='"+attachment[k].AccessUrl +"'  onclick='myAdverseEventsListDetail.downloadFile(this);'>";
											li += "<i class='layui-icon layui-icon-download-circle i_icon' title='下载'></i>";
											li += attachment[k].AccessName;
											li += "</div>";
										}
									}
				   					li += "</td>";
				   					li += "</tr>";
				   					li += "</table>";
				   					li += "</div>";
				   					li += "</div>";
				   				}
				   			     li += "</div>";	
				   				$("#approvalList").append(li);
								layui.element.render();
				   			}
				     			       
						/*var li = '';
						li += '<dl class="layui-nav-child main_table_box">';
						li += '<dd>';
						li += '<table class="layui-table main_table detail_table" cellpadding="0" cellspacing="0" style="width: 100%; margin: 0 auto;">';
						if(eventAuditOpinion.length>0){
							$("li[right='hasAuditContent']").removeClass("layui-hide");
						for(var i = 0;i <eventAuditOpinion.length; i++){
						li += "<tr class="+ eventAuditOpinion[i].returnOpinion +" style='background-color: #DBF0E6;'>";
						li += "<td style='white-space: normal; text-align: right; padding: 7px 12px 7px 12px; width: 25%;' class='tright skin-div-css'>跟进科室</td>";
						li += "<td style='white-space: normal; text-align: left; padding: 7px 12px 7px 12px; width: 25%' class='tleft skin-div-css'>";
						li += "<pre>"+ eventAuditOpinion[i].oldFollowDeptName+"</pre>";
						li += "</td>";
						li += "<td style='white-space: normal; text-align: right; padding: 7px 12px 7px 12px; width: 25%' class='tright skin-div-css'>审核科室</td>";
						li += "<td style='white-space: normal; text-align: left; padding: 7px 12px 7px 12px; width: 25%' class='tleft skin-div-css'>";
						li += "<pre>"+ eventAuditOpinion[i].DeptName +"</pre>";
						li += "</td>";
						li += "</tr>";
						li += "<tr class=" + eventAuditOpinion[i].returnOpinion +">";
						li += "<td style='white-space: normal; text-align: right; padding: 7px 12px 7px 12px; width: 25%' class='tright'>审核人</td>";
						li += "<td style='white-space: normal; text-align: left; padding: 7px 12px 7px 12px; width: 25%' class='tleft'>";
						li += "<pre>"+ eventAuditOpinion[i].auditUserName +"</pre>";
						li += "</td>";
						li += "<td style='white-space: normal; text-align: right; padding: 7px 12px 7px 12px; width: 25%' class='tright'>审核日期</td>";
						li += "<td style='white-space: normal; text-align: left; padding: 7px 12px 7px 12px; width: 25%' class='tleft'>";
						li += "<pre>"+ assemblys.dateToStr(eventAuditOpinion[i].auditDate) + "</pre>";
						li += "</td>";
						li += "</tr>";
						li += "<tr class="+ eventAuditOpinion[i].returnOpinion +">";
						li += "<td style='white-space: normal; text-align: right; padding: 7px 12px 7px 12px; width: 25%' class='tright'>";
						if(eventAuditOpinion[i].reformMeasure!=''){
							li += "原因分析";
						}else{
							li += "审核内容";
						}
						li += "<td colspan='3' style='white-space: normal; text-align: left; padding: 7px 12px 7px 12px; width: 75%' class='tleft'>";
						li += "<pre>"+ eventAuditOpinion[i].improvedOpinion + "</pre>";
						li += "</td>";
						li += "</tr>";
						if(eventAuditOpinion[i].reformMeasure!=''){
							li += "<tr class="+ eventAuditOpinion[i].returnOpinion +">";
							li += "<td style='white-space: normal; text-align: right; padding: 7px 12px 7px 12px; width: 25%' class='tright'>整改措施</td>";
							li += "<td colspan='3' style='white-space: normal; text-align: left; padding: 7px 12px 7px 12px; width: 75%' class='tleft'>";
							li += "<pre>"+ eventAuditOpinion[i].reformMeasure+"</pre>";
							li += "</td>";
							li += "</tr>";
						}
						li += "<tr class="+ eventAuditOpinion[i].returnOpinion +">";
						li += "<td style='white-space: normal; text-align: right; padding: 7px 12px 7px 12px; width: 25%' class='tright'>附件</td>";
						li += "<td colspan='3' style='white-space: normal; text-align: left; padding: 7px 12px 7px 12px; width: 75%' class='tleft'>";
						for(var k=0;k<attachment.length;k++){
							if(eventAuditOpinion[i].improvedAuditID == attachment[k].EventAffirmInfoID){
								li += "<div title='"+attachment[k].AccessName+"' style='cursor: pointer' accessName='"+attachment[k].AccessName +"' accessUrl='"+attachment[k].AccessUrl +"'  onclick='myAdverseEventsListDetail.downloadFile(this);'>";
								li += "<i class='layui-icon layui-icon-download-circle i_icon' title='下载'></i>";
								li += attachment[k].AccessName;
								li += "</div>";
							}
						}
						li += "</td>";
						li += "</tr>";
				}}else{
					
					li += "<tr style='background-color: #DBF0E6;'>";
					li += "<td style='white-space: normal; text-align: center; padding: 7px 12px 7px 12px; width: 98%;' class='tright skin-div-css'>无审批记录</td>";
					li += "</tr>";
				}
						li += "</table>";
						li += "</dd>";
						li += "</dl>";
						*/
				
				}
			});
		},
		
		// 附件内容
		getEventAffirmInfoAtta : function(){
			 $.ajax({
				url : url + "aers/adverseEvents/getEventAffirmInfoAtta.spring",
				dataType : "json",
				async : false,
				data : {
					"eventInputID" : eventInputID
				},
				success : function(data){
					myAdverseEventsListDetail.eventAffirmInfoAtta = data.eventAffirmInfoAttas;
				}
			});
		},
		// 切换tab
		switchTab : function(tab) {
			$(".subject").hide();
			if (tab == "eventDetail") {// 事件详情
				myAdverseEventsListDetail.switchEventDetail();
			} else if (tab == "auditContentInfo") {// 审核内容
				$("#approvalList").removeClass("layui-hide");
				$("#formDetail").addClass("layui-hide");
				$("#approvalList").show();
			}
		},

		// 下载附件
		 downloadFile : function(obj) {
			var eifAttaName = $(obj).attr("accessName");
			var eifAttaUrl = $(obj).attr("accessUrl");
			pubUploader.downLoadAttaPreview(eifAttaName, eifAttaUrl);
		},
		// 导出详情
		 toExportDetail : function(type) {
			 var eventInputTypeName = param.get("eventInputTypeName");
			 var eventName = param.get("eventName");
			 var eventCodeNo = param.get("eventCodeNo");
			 var eventType = param.get("eventType");
			 var fileName = "";
			 var data = null;
			 // 科进不良
			 if(type == "FtnEventUrl"){
				 
			  data = initCustomDetail.handleTableOrListExportData(eventInputTypeName, eventName, '事件编号：&nbsp;&nbsp;'+ eventCodeNo);
			  fileName = "【"+ eventInputTypeName + "-" + eventName+"】" + "不良事件报告表" + eventCodeNo;
			  // 第三方不良
			 }else{
				 data =  myAdverseEventsListDetail.handleTableOrListExportData('不良事件详情报告');
				 fileName = "【"+eventType+"】" + "不良事件报告表" + eventCodeNo;
			 }
			// 调用导出工具
				setTimeout(function() {
					commonExportUtil.exportWord({
						"data" : data,
						"tabName" : "预览效果",
						"fileName" : fileName
					});
				}, 300);
		},
		// 收起li
		openOrClose : function() {
			var contentID = $("li[class='layui-nav-item layui-nav-itemed']").attr("id");
			//表单明细
			if (contentID == 'formDetail') {
				if ($("#formDetail").children().children().attr("class").indexOf('layui-nav-itemed') > -1) {
					
					$("#formDetail").children().children().removeClass("layui-nav-itemed");
				} else {
					
					$("#formDetail").children().children().addClass("layui-nav-itemed");
				}
			}
			//审核记录
			if (contentID == 'approvalList') {
				if ($("#" + contentID).find(".layui-colla-content").attr("class").indexOf('layui-show') > -1) {
					$("#" + contentID).find(".layui-colla-title").click();
				} else {
					
					$("#" + contentID).find(".layui-colla-title").click();
				}
			}
		},
		//第三方不良事件详情
		thirdPartyEventDetail: function(){
			var keys = ["EventCodeNo"];
			
			var values = [eventCodeNo];
			
			var obj = {
					compNo : param.get("compNo"),
					interfaceCode : param.get("detailInterfaceCode"),
					curPageNum : '0',
					pageSize : '0',
					keys : keys,
					values : values
			}
			
			
			$.ajax({
				url : basePath + "mdms/adverseEvent/getInterfaceDetailData.spring",
				type : "POST",
				dataType : "json",
				contentType : "application/json",
				data : JSON.stringify(obj),
				success : function(data){
					if(data.list.result =="success"){
					var detail = data.list.queryList[0];
					var button = "<button type='button' class='layui-btn layui-btn-sm fr skin-btn-normal' onclick=\"assemblys.top.closeTab('事件详情');\">关闭</button>";
					button += "<button type='button' class='layui-btn layui-btn-sm fr skin-btn-minor' onclick=\"myAdverseEventsListDetail.toExportDetail('OtherUrl');\">导出</button>";
					$(".top_div").empty().append(button);
					$(".bodys").empty();
					var html= "";
					html+= "<table id='expenseTable' class='layui-table' style='border:0px solid red;width:100%;align:center;'>";
					html+= "<tbody>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>事件编号</td>";
						html+= "<td class='tdRight'>"+ detail.EventCodeNo+"</td>";
						html+= "<td class='tdLeft skin-div-css'>事件类型</td>";
						html+= "<td class='tdRight'>"+ detail.EventType+"</td>";
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>状态</td>";
						html+= "<td class='tdRight'>"+ detail.DraftStatus+"</td>";
						html+= "<td class='tdLeft skin-div-css'>上报时间</td>";
						html+= "<td class='tdRight'>"+ detail.InputDate+"</td>";
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>上报科室</td>";
						html+= "<td class='tdRight'>"+ detail.ReporterDeptName+"</td>";
						html+= "<td class='tdLeft skin-div-css'>上报人</td>";
						html+= "<td class='tdRight'>"+ detail.ReporterName+"</td>";	
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>住院号</td>";
						html+= "<td class='tdRight'>"+ detail.Casehistorynum+"</td>";
						html+= "<td class='tdLeft skin-div-css'>患者姓名</td>";
						html+= "<td class='tdRight'>"+ detail.specBadName+"</td>";
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>当前跟进科室</td>";
						html+= "<td class='tdRight'>"+ detail.FollowDeptName+"</td>";
						html+= "<td class='tdLeft skin-div-css'>事件等级</td>";
						html+= "<td class='tdRight'>"+ detail.EventLevel+"</td>";
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>事件严重程度</td>";
						html+= "<td class='tdRight'>"+ detail.EventSeverity+"</td>";
						html+= "<td class='tdLeft skin-div-css'>事件风险度</td>";
						html+= "<td class='tdRight'>"+ detail.EventRisk+"</td>";
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>发生日期</td>";
						html+= "<td class='tdRight'>"+ detail.SpecOccurTime+"</td>";
					html+= "</tr>";
					html+= "<tr  class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>事件发生地点</td>";
						html+= "<td class='tdRight' colspan='3' style='text-align: left;text-indent: 2em;padding: 15px;'>"+ detail.EventCourse+"</td>";
				    html+= "</tr>";
				    html+= "<tr  class='layui-table-tr' >";
					    html+= "<td class='tdLeft skin-div-css'>事件发生场所</td>";
					    html+= "<td class='tdRight' colspan='3' style='text-align: left;text-indent: 2em;padding: 15px;'>"+ detail.SpecOccurPlace+"</td>";
				    html+= "</tr>";
					html+= "<tr  class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>事件发生经过</td>";
						html+= "<td class='tdRight' colspan='3' style='text-align: left;text-indent: 2em;padding: 15px;'>"+ detail.SpecOccurLocation+"</td>";
					html+= "</tr>";
					html+= "<tr  class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>事件发生可能原因</td>";
						html+= "<td class='tdRight' colspan='3' style='text-align: left;text-indent: 2em;padding: 15px;'>"+ detail.PossibleCausesOfEvents+"</td>";
					html+= "</tr>";
					html+= "</tbody>";
					html+= "</table>";
					$(".bodys").append(html);
					layui.element.render();
				}
			 }
			});
		},
		// 第三方不良处理导出数据
		handleTableOrListExportData : function(fileTitle, leftTitle, rightTitle) {	
			var tempList = [];
			// 数据集合
			var data = [];
			var $table = $("#expenseTable");
			var trs = [];
			$table.find("tr.layui-table-tr").each(function(){
				var tds = [];
				$(this).children("td").each(function() {
					tds.push($.trim($(this).html()));
				});
				trs.push(tds);
			});
			
			data.push({
				title : "列表信息",
				list : trs
			});
			
			tempList.push({
				bigTitle : "事件详情",
				title : "列表信息",
				show : "1",
				type : "table",
				data : data
			});
			
			var newData = {
					title : fileTitle || "",
					leftTitle : leftTitle || "",
					rightTitle : rightTitle || "",
					list : tempList
				}
			
			return newData;
		}
		
}