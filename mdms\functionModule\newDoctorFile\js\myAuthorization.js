var technicalAuthorization = {
	init : function(obj) {
		$(obj).empty();
		var html = '<iframe id="technicalAuthorizationFrame" class="technicalAuthorizationFrame" src="myAuthorization.html" frameborder="0" width="100%" height="100%"></iframe>';
		$(obj).append(html);
	},
	initAuth : function() {
		param.set("customFormFilledCode", parent.param.get("customFormFilledCode"));
		param.set("findUserCode", parent.$("#userCode").text());
		param.set("compNo", parent.param.get("compNo"));
		technicalAuthorization.initLayuiForm();
		// 点击第一个
		var tabState = param.get("tabState");
		if (tabState == 99) {
			$("#authTabView li:eq(0)").click();
		} else if (tabState == 1) {
			$("#authTabView li:eq(1)").click();
		} else if (tabState == 3) {
			$("#authTabView li:eq(2)").click();
		}
		//hwx 2023-6-25 隐藏功能
		/* else if (tabState == 2) {
			$("#authTabView li:eq(3)").click();
		}else if (tabState == 4) {
			$("#authTabView li:eq(4)").click();
		}*/
		if (param.get("titleName")) {
			$("span[titleName]").text(param.get("titleName"));
		}
	},
	initLayuiForm : function() {
		// 监听 tab
		layui.element.on("tab(authTabView)", function(data) {
			// 写入
			param.set("tabState", $(this).attr("tabState"));
			// 当前
			$(this).siblings().removeClass("skin-btn-main");
			$(this).addClass("skin-btn-main");
			// 查询
			filterParam.set("keyword", "");
			technicalAuthorization.getAnesthesiaClassPager();
		});
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "院内代码,含义,创建人姓名",
			title : "关键字"
		} ];
		return params;
	},
	getAnesthesiaClassPager : function() {
		var type = param.get("tabState");
		if (type == 99) {//手术
			var cols = [ {
				title : '手术名称',
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.operationName);
				}
			}, {
				title : '手术等级',
				align : "center",
				width : 100,
				templet : function(d) {
					var html = '';
					var operationLevelName = d.operationLevelName;
					if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel1) {
						html += '<font style="color:#338100;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel2) {
						html += '<font style="color:#0056FF;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel3) {
						html += '<font style="color:#CC0000;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel4) {
						html += '<font style="color:#FF002B;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel5) {
						html += '<font style="color:orange" >';
					} else {
						html += '<font style="color:orange" >';
					}
					if (d.operationLevelName != undefined) {
						html += d.operationLevelName + '</font>';
					} else {
						html += '<font style="color:orange;" >暂无等级</font>';
					}
					return html;
				}
			}, {
				title : '操作类型',
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.operationTypeName);
				}
			}, {
				title : '授权开始时间',
				align : "center",
				templet : function(d) {
					var html = "";
					if (d.createTime) {
						html = assemblys.dateToStr(d.createTime);
					}
					return html;
				}
			}, {
				title : '授权结束时间',
				align : "center",
				templet : function(d) {
					var html = "";
					if (d.createEndTime) {
						html = assemblys.dateToStr(d.createEndTime);
					}
					return html;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/operationRight/getOperationRightiPager.spring?" + param.__form() + "&" + filterParam.__form(),
				
				data : {
					"customFormFilledCode" : param.get("customFormFilledCode"),
				},
				cols : [ cols ],
				height : $(window).height() * 0.86,
				width : $(window).width() * 0.97,
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					$("#filterNum").text(count);
					layui.form.render();
				},
				events : {}
			});
		} else if (type == 1) {//处方
			var cols = [ {
				title : '处方分类',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.parentName;
				}
			}, {
				title : '处方名',
				align : "center",
				minWidth : 200,
				templet : function(d) {
					return d.RmosName;
				}
			}, {
				title : '授权开始时间',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.authorizeDate ? assemblys.dateToStr(d.authorizeDate) : "";
				}
			}, {
				title : '授权结束时间',
				align : "center",
				templet : function(d) {
					var html = "";
					if (d.authorizeEndDate) {
						html = assemblys.dateToStr(d.authorizeEndDate);
					}
					return html;
				}
			}, {
				title : '注意事项',
				align : "center",
				minWidth : 200,
				templet : function(d) {
					return d.remark;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/writeRight/getWriteRightPager.spring?" + param.__form() + "&" + filterParam.__form(),
				cols : [ cols ],
				data : {
					"customFormFilledCode" : param.get("customFormFilledCode")
				},
				height : $(window).height() * 0.86,
				width : $(window).width() * 0.97,
				done : function(res, curr, count) {
					
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					layui.form.render();
					return res;
				},
				events : {}
			});
		} else if (type == 2) {
			var cols = [ {
				title : '麻醉分级',
				width : 200,
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.AnClassName);
				}
			}, {
				title : '类型',
				width : 200,
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.rightTypeName);
				}
			}, {
				title : '授权开始时间',
				align : "center",
				minWidth : 200,
				templet : function(d) {
					return assemblys.htmlEncode(assemblys.dateToStr(d.createTime));
				}
			}, {
				title : '授权结束时间',
				align : "center",
				templet : function(d) {
					var html = "";
					if (d.createEndTime) {
						html = assemblys.dateToStr(d.createEndTime);
					}
					return html;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/anaesthesiaRight/getAnaesthesiaRightPager.spring?" + param.__form() + "&" + filterParam.__form() + "&operation_state=1",
				data : {
					"customFormFilledCode" : param.get("customFormFilledCode")
				},
				height : $(window).height() * 0.86,
				width : $(window).width() * 0.97,
				cols : [ cols ],
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					layui.form.render();
				},
				events : {}
			});
		} else if (type == 3) {
			var cols = [ {
				title : '查房等级',
				align : "center",
				templet : function(d) {
					return d.checkRoomLevel;
				}
			}, {
				title : '授权开始时间',
				align : "center",
				minWidth : 200,
				templet : function(d) {
					return d.optDate ? assemblys.dateToStr(d.optDate) : "";
				}
			}, {
				title : '授权结束时间',
				align : "center",
				templet : function(d) {
					var html = "";
					if (d.optEndDate) {
						html = assemblys.dateToStr(d.optEndDate);
					}
					return html;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/checkRoomRight/getCheckRoomRightPager.spring?" + param.__form() + "&" + filterParam.__form(),
				cols : [ cols ],
				data : {
					"customFormFilledCode" : param.get("customFormFilledCode")
				},
				height : $(window).height() * 0.86,
				width : $(window).width() * 0.97,
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					layui.form.render();
					return res;
				},
				events : {}
			});
		} else if (type == 4) {
			var cols = [ {
				title : '院内代码',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.subProClassHisCode;
				}
			}, {
				title : '亚专业名称',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.subProClassName;
				}
			}, {
				title : '内容',
				align : "center",
				minWidth : 200,
				templet : function(d) {
					return d.content
				}
			}, {
				title : '级别',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.levelName
				}
			}, {
				title : '授权开始时间',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.createTime ? assemblys.dateToStr(d.createTime) : "";
				}
			}, {
				title : '授权结束时间',
				align : "center",
				templet : function(d) {
					var html = "";
					if (d.createEndTime) {
						html = assemblys.dateToStr(d.createEndTime);
					}
					return html;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/subProfessionRight/getSubProfessionRightPager.spring?" + param.__form() + "&" + filterParam.__form(),
				cols : [ cols ],
				data : {
					"customFormFilledCode" : param.get("customFormFilledCode")
				},
				height : $(window).height() * 0.86,
				width : $(window).width() * 0.97,
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					layui.form.render();
					return res;
				},
				events : {}
			});
		}
	},
	userRightInit : function() {
		return $.ajax({
			url : basePath + "mdms/mdmsCommon/userRightInit.spring?lookUserCode=" + param.get("lookUserCode")
		}).then(function(data) {
			return data;
		});
	},
	query : function() {
		technicalAuthorization.getAnesthesiaClassPager();
	},
	//授权
	apply : function(d) {
		var titleName = "&nbsp;&nbsp;&nbsp;手术授权";
		var funCode = assemblys.top.mdms.mdmsConstant.MDMS_SHOUSHUQUANXIAN;
		var type = param.get("tabState");
		if (type == 1) {
			titleName = "&nbsp;&nbsp;&nbsp;处方授权";
			funCode = assemblys.top.mdms.mdmsConstant.MDMS_PRESCRIPTION;
		}
		if (type == 2) {
			titleName = "&nbsp;&nbsp;&nbsp;麻醉授权";
			funCode = assemblys.top.mdms.mdmsConstant.MDMS_MAZUISHOUQUAN;
		}
		if (type == 3) {
			titleName = "&nbsp;&nbsp;&nbsp;查房授权";
			funCode = assemblys.top.mdms.mdmsConstant.MDMS_THREE_WARD_ROUND;
		}
		if (type == 4) {
			titleName = "&nbsp;&nbsp;&nbsp;亚专业授权";
			funCode = assemblys.top.mdms.mdmsConstant.MDMS_SUBPROFESSIONRIGHT;
		}
		var customFormFilledCode = param.get("customFormFilledCode");
		var findUserCode = param.get("findUserCode");
		var url = '';
		if (d == 'right') {
			//授权
			url = basePath + "/mdms/functionModule/newDoctorFile/rightAddList.html?customFormFilledCode=" + customFormFilledCode + "&docUserCode=" + param.get("findUserCode") + "&type=" + type + "&funCode=" + funCode;
		} else {
			titleName = titleName + "日志";
			//授权日志
			url = basePath + "/mdms/functionModule/newDoctorFile/rightLogList.html?&rightClass=" + technicalAuthorization.tabToRightClass(type) + "&funCode=" + funCode + "&findUserCode=" + param.get("findUserCode");
		}
		parent.layer.open({
			id : "ids",
			type : 2,
			skin : 'riskbtn-class',
			content : url,
			title : [ '<div class="skin-btn-main"><span>' + titleName + '</span></div>' ],
			area : [ "800px", "600px" ],
			resize : false,
			success : function(layero, index) {
				$div = $(layero).find("div")[0]
				$($div).css("padding", "0px")
			},
			cancel : function(index, layero) {
				layer.close(index);
				technicalAuthorization.getAnesthesiaClassPager();
			}
		})
	},
	//申请
	userApply : function() {
		var type = param.get("tabState");
		var url = basePath + "mdms/functionModule/userDeptOperation/userDeptOperationList.html?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SHOUSHUQUANXIAN;
		var titleName = "&nbsp;&nbsp;&nbsp;手术授权";
		if (type == 1) {
			titleName = "&nbsp;&nbsp;&nbsp;处方授权";
			url = basePath + "mdms/functionModule/prescription/prescriptionList.html?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_PRESCRIPTION;
		}
		if (type == 2) {
			titleName = "&nbsp;&nbsp;&nbsp;麻醉授权";
			url = basePath + "mdms/functionModule/narcosis/narcosisList.html?funCode=" + assemblys.top.mdms.mdmsConstant.FUN_ANESTHESIA;
		}
		if (type == 3) {
			titleName = "&nbsp;&nbsp;&nbsp;查房授权";
			url = basePath + "mdms/functionModule/threeWardRound/threeWardRoundList.html?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_THREE_WARD_ROUND;
		}
		if (type == 4) {
			titleName = "&nbsp;&nbsp;&nbsp;亚专业授权";
			url = basePath + "mdms/functionModule/subProfessionRight/subProfessionRightList.html?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SUBPROFESSIONRIGHT;
		}
		assemblys.top.addTab(null, titleName, url);
	},
	mamualAuthList : function() {
		var type = param.get("tabState");
		var url = basePath + "/mdms/functionModule/userRightDD/mamualAuthList.html?type=" + type + "&funCode=" + param.get("funCode") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&rightType=SQLXSD";
		var titleName = "手术手动授权记录";
		if (type == 1) {
			titleName = "处方手动授权记录";
		}
		if (type == 2) {
			titleName = "麻醉手动授权记录";
		}
		if (type == 3) {
			titleName = "查房手动授权记录";
		}
		if (type == 4) {
			titleName = "亚专业授权记录";
		}
		layer.open({
			id : "ids",
			type : 2,
			skin : 'riskbtn-class',
			content : url,
			title : [ '<div class="skin-btn-main"><span>' + titleName + '</span></div>' ],
			area : [ "800px", "400px" ],
			resize : false,
			success : function(layero, index) {
			}
		})
	},
	tabToRightClass : function(tab) {
		var rightClass = '';
		switch (tab) {
		case '99'://手术
			rightClass = '3';
			break;
		case '1'://处方
			rightClass = '2';
			break;
		case '2'://麻醉
			rightClass = '1';
			break;
		case '3'://查房
			rightClass = '4';
			break;
		case '4'://亚专业
			rightClass = '6';
			break;
		}
		return rightClass;
	}
}