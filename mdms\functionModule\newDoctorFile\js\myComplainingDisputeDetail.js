
var url = param.get("url");
var dictCode = param.get("dictCode");
var myComplainingDisputeDetail = {		
		// 表单编号
		customFormFilledCode : param.get("customFormFilledCode"),
		// 文件访问路径
		baseImgPath : "",
		// 表单对象
		customFormFilled : "",
		// 应用编号
		appCode : "cdms",
		// 功能编号
		funCode : "COMPLAINTS",
		// 部门映射
		deptNames : {},
		// 用户部门映射
		userDeptNames : {},
		
	init : function(){
		if( dictCode== "FtnComplaintUrl"){
				// 显示li标签
		    $(".layui-tab").removeClass("layui-hide");
		    //$("#packUp").removeClass("layui-hide");
			// 加载部门映射
			myComplainingDisputeDetail.getDeptNameAndUserNames().then(function() {
				return myComplainingDisputeDetail.complaintDetailInit();
			}).then(function() {
			// 数据集合
			var dataMethod = myComplainingDisputeDetail.getDataMethod;
			for ( var key in dataMethod) {
				dataMethod[key](key);
			}
			// 监听TAB
			myComplainingDisputeDetail.tabMonitor();
			// 触发第一次tab
			$("#tabView").children("li:eq(0)").click();
			});
		}else if(dictCode == "OtherComplaintUrl"){
			myComplainingDisputeDetail.thirdPartyComplainingDetail();
		}
		},	
		// 获取用户权限
		complaintDetailInit : function() {
			
			return $.ajax({
				url : url + "cdmsv2/myComplaintList/complaintDetailInit.spring",
				data : {
					customFormFilledCode : myComplainingDisputeDetail.customFormFilledCode,
				},
				dataType : "json",
				success : function(data) {
					// 表单对象
					myComplainingDisputeDetail.customFormFilled = data.customFormFilled;
					// 显示编号
					$("span[customFormFilledCode]").append(" - " + myComplainingDisputeDetail.customFormFilledCode);
				}
			});
		},
		/**
		 * 获取部门用户映射关系
		 */
		getDeptNameAndUserNames : function() {
			return $.ajax({
				url : url + "frame/common/getDeptNameAndUserNames.spring",
				type : "post",
				dataType : "json",
				skipDataCheck : true,
				success : function(data) {
					if (data.result == "success") {
						myComplainingDisputeDetail.deptNames = data.deptNames;
						myComplainingDisputeDetail.userDeptNames = data.userDeptNames;
					} else {
						assemblys.alert("获取用户部门映射关系出错");
					}
				},
				error : function() {
					assemblys.alert("获取用户部门映射关系出错");
				}
			});
			
		},
	    // 选项卡监听
		tabMonitor : function() {
			// tab触发监听
			$("#tabView").children("li").on("click", function() {
				var index = $(this).index();
				$(this).siblings().removeClass("layui-this");
				$(this).addClass("layui-this");
				// 对应内容项
				var $tabContent = $("#container").children(".tab-content");
				$tabContent.hide();
				// 显示并获取ID
				$tabContent.eq(index).show();
			});
			
			//满意度调查签页显示/隐藏
			var satisfaction = myComplainingDisputeDetail.satisfactionCustomFormFilled;
			if (!!satisfaction) {
				$("#satisfactionDegree").removeClass("layui-hide");
				$("#satisfactionDegreeAtta").removeClass("layui-hide");
			}
			
			// 监听操作按钮
			$("button[tag]").on("click", function() {
				myComplainingDisputeDetail.toComplaintEdit(this);
			});
		},
		// 获取数据的接口，目前contentID 就是方法名，也对应页面渲染的ID
		getDataMethod : {
			// 事件详情
			customFormDetail : function(contentID) {
				var filled = myComplainingDisputeDetail.customFormFilled;
				getCustomFormDetail.getCustomFormData(url, filled.customFormCode, filled.customFormFilledCode, myComplainingDisputeDetail.appCode, contentID).then(function() {
					// 删除ID - 没了之后就不会加载第一次了
					$("#" + contentID).removeAttr("id");
				});
			},
			// 处理记录
			handleRecordDetail : function(contentID) {
				initComplaintDetail.getApprovalBelongFlowNodeRecordList({
					"basePath" : url,
					"appCode" : "cdms",
					"approvalBelongCode" : myComplainingDisputeDetail.customFormFilledCode,
					"selector" : "#" + contentID
				});
			},
			// 院内专家委员会意见
			hospitalLeadDetail : function(contentID) {
				// 入参
				var paramObj = {
					customFormFilledCode : myComplainingDisputeDetail.customFormFilledCode,
					commentType : "3",
				}
				myComplainingDisputeDetail.getAjax(paramObj, contentID, "table");
			},
			// 精神鉴定意见
			judiciaryDetail : function(contentID) {
				// 入参
				var paramObj = {
					customFormFilledCode : myComplainingDisputeDetail.customFormFilledCode,
					commentType : "4",
				}
				myComplainingDisputeDetail.getAjax(paramObj, contentID, "table");
			},
			// 院长意见
			hospitalDeanDetail : function(contentID) {
				// 入参
				var paramObj = {
					customFormFilledCode : myComplainingDisputeDetail.customFormFilledCode,
					commentType : "5",
				}
				myComplainingDisputeDetail.getAjax(paramObj, contentID, "table");
			},
			// 保险理赔记录
			insuranceDetail : function(contentID) {
				// 入参
				var paramObj = {
					customFormFilledCode : myComplainingDisputeDetail.customFormFilledCode,
					commentType : "8",
				}
				myComplainingDisputeDetail.getAjax(paramObj, contentID, "table");
			},
		},
		// 异步查询
		getAjax : function(paramObj, contentID, type) {
			$.ajax({
				url : url + "cdmsv2/complaintdetail/getComplaintCommentList.spring",
				dataType : "json",
				data : paramObj,
				success : function(data) {
					var deptNames = myComplainingDisputeDetail.deptNames;
					var userDeptNames = myComplainingDisputeDetail.userDeptNames;
					var complaintCommentList = data.complaintCommentList;
					var commentTypeMap = data.commentTypeMap;
					var length = complaintCommentList.length;
					if (length != 0) {
						// 列表
						if (type == "list") {
							var newData = [];
							// 生成TR
							$.each(complaintCommentList, function(index, temp) {
								// 序号
								temp["index"] = index + 1;
								// 状态
								temp["commentType"] = commentTypeMap["type-" + temp.commentType];
								temp["execDeptName"] = deptNames[temp["execDeptID"]];
								newData.push(initComplaintDetail.createTr(temp));
							});
							// 写入容器
							var $tbody = $("#" + contentID).find("tbody");
							$tbody.empty();
							// 渲染
							assemblys.createElement(newData, $tbody[0]);
							// 删除ID - 没了之后就不会加载第一次了
							$("#" + contentID).removeAttr("id");
						} else
	
						// 表格
						if (type == "table") {
							var newData = [];
							// 生成TR
							$.each(complaintCommentList, function(index, temp) {
								// 序号
								temp["index"] = index + 1;
								// 状态
								temp["commentTypeName"] = commentTypeMap["type-" + temp.commentType];
								temp["execDeptName"] = userDeptNames[temp["execUserCode"]];
								// 如果是处理意见
								if (contentID == "handleRecordDetail") {
									temp["execDeptName"] = userDeptNames[temp["execUserCode"]];
									temp["handleDeptName"] = userDeptNames[temp["execUserCode"]];
									newData.push(initComplaintDetail.createTable2(temp, contentID));
								} else {
									newData.push(initComplaintDetail.createTable(temp, contentID));
								}
							});
							// 渲染
							var $ul = $("#" + contentID).find("ul");
							$ul.empty();
							assemblys.createElement(newData, $ul[0]);
							layui.element.render("nav", contentID + "UL");
						}
					}
				}
			});
		},
		//第三方投诉纠纷详情
		thirdPartyComplainingDetail: function(){
			var keys = ["CustomFormFilledCode"];
			
			var values = [param.get("customFormFilledCode")];
			
			var obj = {
					compNo : param.get("compNo"),
					interfaceCode : param.get("listInterfaceCode"),
					curPageNum : '0',
					pageSize : '0',
					keys : keys,
					values : values
			}
			
			
			$.ajax({
				url : basePath + "mdms/complainingDispute/getInterfaceDetailData.spring",
				type : "POST",
				dataType : "json",
				contentType : "application/json",
				data : JSON.stringify(obj),
				success : function(data){
					if(data.list.result =="success"){
					var detail = data.list.queryList[0];
					$(".bodys").empty();
					var html= "";
					html+= "<table id='expenseTable' class='layui-table' style='border:0px solid red;width:100%;align:center;'>";
					html+= "<tbody>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>事件编号</td>";
						html+= "<td class='tdRight'>"+ detail.CustomFormFilledCode+"</td>";
						html+= "<td class='tdLeft skin-div-css'>状态</td>";
						html+= "<td class='tdRight'>"+ detail.Status+"</td>";
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>上报人</td>";
						html+= "<td class='tdRight'>"+ detail.CreateUserName+"</td>";	
						html+= "<td class='tdLeft skin-div-css'>上报时间</td>";
						html+= "<td class='tdRight'>"+ detail.CreateDate+"</td>";
					html+= "</tr>";
					html+= "<tr class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>住院号</td>";
						html+= "<td class='tdRight'>"+ detail.ComplaintHospitalizationNO+"</td>";
						html+= "<td class='tdLeft skin-div-css'>患者姓名</td>";
						html+= "<td class='tdRight'>"+ detail.ComplaintPatientName+"</td>";
					html+= "</tr>";
					html+= "<tr  class='layui-table-tr' >";
						html+= "<td class='tdLeft skin-div-css'>事件分类</td>";
						html+= "<td class='tdRight' colspan='3' style='text-align: left;text-indent: 2em;padding: 15px;'>"+ detail.CustomFormName+"</td>";
				    html+= "</tr>";
				    html+= "<tr  class='layui-table-tr' >";
				    html+= "<td class='tdLeft skin-div-css'>原因</td>";
				    html+= "<td class='tdRight' colspan='3' style='text-align: left;text-indent: 2em;padding: 15px;'>"+ detail.ComplaintCause+"</td>";
				    html+= "</tr>";
				    html+= "<tr  class='layui-table-tr' >";
					    html+= "<td class='tdLeft skin-div-css'>备注</td>";
					    html+= "<td class='tdRight' colspan='3' style='text-align: left;text-indent: 2em;padding: 15px;'>"+ detail.ComplaintRemark+"</td>";
				    html+= "</tr>";
					html+= "</tbody>";
					html+= "</table>";
					$(".bodys").append(html);
					layui.element.render();
				}
			 }
			});
		},
		// 收起li
		openOrClose : function() {
			   // 事件详情
			   var customFormDetail = $(".customFormDetail").css("display");
			   if(customFormDetail == "block"){
					if ($(".customFormDetail").children().children().attr("class").indexOf('layui-nav-itemed') > -1) {
						
						$(".customFormDetail").children().children().removeClass("layui-nav-itemed");
					} else {
						
						$(".customFormDetail").children().children().addClass("layui-nav-itemed");
					}
			   }
			   
			   // 处理记录
			   var handleRecordDetail = $("#handleRecordDetail").css("display");
			   if(handleRecordDetail == "block"){
				   if ($("#handleRecordDetail").children().children().children(".layui-colla-content").attr("class").indexOf('layui-show') > -1) {
					   
					   $("#handleRecordDetail").children().children().children("h2").click();
				   } else {
					   
					   $("#handleRecordDetail").children().children().children("h2").click();
				   }
			   }
			   
			   // 院内专家委员会意见
			   var hospitalLeadDetail = $("#hospitalLeadDetail").css("display");
			   if(hospitalLeadDetail == "block"){
				   if ($("#hospitalLeadDetail").children().children().attr("class").indexOf('layui-nav-itemed') > -1) {
					   
					   $("#hospitalLeadDetail").children().children().removeClass("layui-nav-itemed");
				   } else {
					   
					   $("#hospitalLeadDetail").children().children().addClass("layui-nav-itemed");
				   }
			   }
			   
			   // 精神鉴定意见
			   var judiciaryDetail = $("#judiciaryDetail").css("display");
			   if(judiciaryDetail == "block"){
				   if ($("#judiciaryDetail").children().children().attr("class").indexOf('layui-nav-itemed') > -1) {
					   
					   $("#judiciaryDetail").children().children().removeClass("layui-nav-itemed");
				   } else {
					   
					   $("#judiciaryDetail").children().children().addClass("layui-nav-itemed");
				   }
			   }
			   
			   // 院长意见
			   var hospitalDeanDetail = $("#hospitalDeanDetail").css("display");
			   if(hospitalDeanDetail == "block"){
				   if ($("#hospitalDeanDetail").children().children().attr("class").indexOf('layui-nav-itemed') > -1) {
					   
					   $("#hospitalDeanDetail").children().children().removeClass("layui-nav-itemed");
				   } else {
					   
					   $("#hospitalDeanDetail").children().children().addClass("layui-nav-itemed");
				   }
			   }
			   
			   // 保险理赔记录
			   var insuranceDetail = $("#insuranceDetail").css("display");
			   
			   if(insuranceDetail == "block"){
				   if ($("#insuranceDetail").children().children().attr("class").indexOf('layui-nav-itemed') > -1) {
					   
					   $("#insuranceDetail").children().children().removeClass("layui-nav-itemed");
				   } else {
					   
					   $("#insuranceDetail").children().children().addClass("layui-nav-itemed");
				   }
			   }


		},
		// 导出详情
		 toExportDetail : function() {

			var eventNumber = "事件编号：" + param.get("customFormFilledCode");
		    var data = myComplainingDisputeDetail.complaintsExport('投诉纠纷详情报告', '', eventNumber);
		
			// 调用导出工具
				setTimeout(function() {
					commonExportUtil.exportWord({
						"data" : data,
						"tabName" : "预览效果",
						"fileName" : "投诉纠纷详情"
					});
				}, 300);
		},
		// 科进投诉导出详情
		complaintsExport : function(fileTitle, leftTitle, rightTitle) {	
		if(dictCode== "FtnComplaintUrl" ){
			var modulers = [];
			$(".layui-tab-title").children("li").each(function(){
				var bigTitle = $.trim($(this).text());
				modulers.push({
					title : bigTitle
				});
			})
			
			var tempList = [];
			$(".tab-content").each(function(i,e){
				// 数据集合
				var data = [];
				var bigTitle = modulers[i].title;
				var $divs = $(this).find("div.layui-colla-item");
				var $lis = $(this).find("li");
				if ($divs.length > 0) {
					$divs.each(function() {
						var $h2 = $(this).find("h2:eq(0)").clone();
						$h2.find("i").remove();
						var title = $h2.text();
						var trs = [];
						$(this).find("table.layui-table tr.layui-table-tr").each(function() {
							var tds = [];
							$(this).children("td").each(function() {
								tds.push($.trim($(this).html()));
							});
							trs.push(tds);
						});
						data.push({
							title : title,
							list : trs
						});
					});
				}else if($lis.hasClass("layui-nav-item")){
					$lis.each(function(){
						var title = $(this).find(".main_table_title:eq(0)").text();
						var trs = [];
						$(this).find("table.layui-table tr").each(function(){
							var tds = [];
							$(this).children("td").each(function(){
								tds.push($.trim($(this).html()));
							});
							trs.push(tds);
						});
						data.push({
							title : title,
							list : trs
						});
					});
				}
				tempList.push({
					bigTitle : bigTitle,
					title : "",
					show : "1",
					type : "table",
					data : data
				});
			});
			// 第三方投诉导出详情
		}else if(dictCode == "OtherComplaintUrl"){
			var tempList = [];
			// 数据集合
			var data = [];
			var $table = $("#expenseTable");
			var trs = [];
			$table.find("tr.layui-table-tr").each(function(){
				var tds = [];
				$(this).children("td").each(function() {
					tds.push($.trim($(this).html()));
				});
				trs.push(tds);
			});
			
			data.push({
				title : "列表信息",
				list : trs
			});
			
			tempList.push({
				bigTitle : "事件详情",
				title : "列表信息",
				show : "1",
				type : "table",
				data : data
			});
		}
			var newData = {
					title : fileTitle || "",
					leftTitle : leftTitle || "",
					rightTitle : rightTitle || "",
					list : tempList
				}
			return newData;
		}
}