var myComplainingDisputeList = {
	dictCode : "",
	url : "",
	listInterfaceCode : "",
	init : function() {
		myComplainingDisputeList.url().then(function(data) {
			myComplainingDisputeList.getmyComplainingDisputeListPager();
		});
		
	},
	getmyComplainingDisputeListPager : function() {
		var cols = [ {
			title : '操作',
			width : "10%",
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_delete" title="浏览" lay-event="toViewMyComplainingDisputeDetail"></i>';
				return html;
			}
		}, {
			title : '序号',
			width : "8%",
			align : "center",
			type : 'numbers'
		}, {
			title : '事件编号',
			width : 165,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.CustomFormFilledCode);
			}
		}, {
			title : '状态',
			width : 80,
			align : "center",
			templet : function(d) {
				
				if (d.Status != null) {
					return assemblys.htmlEncode(d.Status);
				} else {
					return "已结案";
				}
				
			}
		}, {
			title : '事件分类',
			width : 200,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.CustomFormName);
			}
		}, {
			title : '上报人',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.CreateUserName);
			}
		}, {
			title : '上报时间',
			width : 160,
			align : "center",
			templet : function(d) {
				
				return assemblys.dateToStr(d.CreateDate);
			}
		}, {
			title : '住院号',
			width : 200,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.ComplaintHospitalizationNO);
			}
		}, {
			title : '患者姓名',
			width : 200,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.ComplaintPatientName);
			}
		}, {
			title : '原因',
			width : 200,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.ComplaintCause);
			}
		}, {
			title : '备注',
			width : 200,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.ComplaintRemark);
			}
		} ];
		var url = "";
		// 科进投诉纠纷
		if (myComplainingDisputeList.dictCode == "FtnComplaintUrl") {
			url = myComplainingDisputeList.url + "cdmsv2/myComplaintList/getMyComplaintList.spring?" + param.__form();
			// 第三方投诉纠纷
		} else if (myComplainingDisputeList.dictCode == "OtherComplaintUrl") {
			url = basePath + "mdms/complainingDispute/getInterfaceData.spring?compNo=" + param.get("compNo") + "&interfaceCode=" + myComplainingDisputeList.listInterfaceCode;
		}
		var height = newDoctorInfo.returnHeight(30);
		var width = newDoctorInfo.returnWidth(-200);
		if (height < 200) {
			var showHeight = param.get("showHeight") - 50;
			if (showHeight) {
				height = showHeight;
			} else {
				height = "460";
			}
		}
		if (width < 200) {
			var showWidth = param.get("showWidth") - 380;
			if (showWidth) {
				width = showWidth;
			} else {
				width = "1024";
			}
		}
		assemblys.tableRender({
			elem : '#list',
			url : url,
			cols : [ cols ],
			method : "post",
			height : height,
			width : width,
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				// 工具栏监听
				toViewMyComplainingDisputeDetail : myComplainingDisputeList.toViewMyComplainingDisputeDetail,
			}
		});
		
	},
	// 投诉纠纷详情
	toViewMyComplainingDisputeDetail : function(d) {
		var url = basePath + "mdms/functionModule/newDoctorFile/myComplainingDisputeDetail.html?customFormFilledCode=" + d.CustomFormFilledCode;
		if (myComplainingDisputeList.dictCode == "FtnComplaintUrl") {
			url += "&url=" + myComplainingDisputeList.url;
		}
		url += "&listInterfaceCode=" + myComplainingDisputeList.listInterfaceCode;
		url += "&compNo=" + param.get("compNo") + "&dictCode=" + myComplainingDisputeList.dictCode;
		
		assemblys.top.addTab(null, '事件详情', url);
	},
	// 获取科进投诉纠纷地址和第三方投诉纠纷地址
	url : function() {
		return $.ajax({
			url : basePath + "frame/dict/getDictByCode.spring",
			type : "get",
			data : {
				"dictCode" : 'FtnComplaintUrl,OtherComplaintUrl',
				"appCode" : "mdms"
			},
			dataType : "json",
			skipDataCheck : true
		}).then(function(data) {
			if (data.result == "success") {
				myComplainingDisputeList.dictCode = data.dict[0].dictCode;
				
				if (myComplainingDisputeList.dictCode == "FtnComplaintUrl") {
					// 科进投诉地址
					myComplainingDisputeList.url = data.dict[0].dictContent;
				} else if (myComplainingDisputeList.dictCode == "OtherComplaintUrl") {
					// 第三方投诉数据接口,通过字典获取
					myComplainingDisputeList.listInterfaceCode = data.dict[0].dictContent;
				}
			}
			return data;
		});
	}

}