var myMedicalActivitiesList = {
	initDiv : function(obj, userCode) {
		$(obj).empty();
		var html = '<iframe id="myMedicalActivities" class="" src="myMedicalActivitiesList.html?userCode=' + userCode + '&funCode=' + assemblys.top.mdms.mdmsConstant.MDMS_MEDICALACTIVITIES + '" frameborder="0" width="100%" height=' + newDoctorInfo.returnHeight(5) + '"></iframe>';
		var $div = $("div[class='bodys layui-form']");
		$(obj).append(html);
	},
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]")).then(function() {
			myMedicalActivitiesList.getMyMedicalActivitiesPager();
			myMedicalActivitiesList.initLayuiForm();
		});
		
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "处方类别,名称,编码",
			title : "关键字"
		} ];
		return params;
	},
	getMyMedicalActivitiesPager : function() {
		var cols = [ {
			title : '操作',
			width : '7%',
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_delete" title="浏览" lay-event="toMyMedicalActivities"></i>';
				return html;
			}
		}, {
			title : '技术/项目名称',
			width : '20%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.projectName);
			}
		}, {
			title : '技术/项目类别',
			width : '20%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.newProjectType);
			}
		}, {
			title : '技术挡位',
			width : '20%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.technicalGear);
			}
		}, {
			title : '技术/项目负责人',
			width : '20%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.projectCharger);
			}
		}, {
			title : '状态',
			align : "center",
			templet : function(d) {
				var stateName = "";
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_APPROVE) {
					stateName = "审核中";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_RETURN) {
					stateName = "回退";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_LAUNCH) {
					stateName = "开展中";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_ZCG) {
					stateName = "转常规";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YZCG) {
					stateName = "已转常规";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_ZZ) {
					stateName = "终止";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YZZ) {
					stateName = "已终止";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YQ) {
					stateName = "延期";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YYQ) {
					stateName = "已延期";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_FINISH) {
					stateName = "已完成";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_DRAFT) {
					stateName = "草稿";
				}
				
				return assemblys.htmlEncode(stateName);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/functionModule/medicalActivities/getMyMedicalActivitiesList.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			height : newDoctorInfo.returnHeight(),
			width : newDoctorInfo.returnWidth(-200),
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toMyMedicalActivities : myMedicalActivitiesList.toMyMedicalActivities,
			}
		});
		
	},
	
	toMyMedicalActivities : function(d) {
		var customFormFilledCode = d.CustomFormFilledCode;
		var customFormCode = d.CustomFormCode;
		var url = basePath + "mdms/functionModule/mdmsCustomDetail/mdmsCustomDetail.html?appCode=" + parent.param.get("appCode") + "&funCode=" + parent.param.get("funCode") + "&customFormFilledCode=" + customFormFilledCode + "&approvalBelongCode=" + customFormFilledCode + "&customFormCode=" + customFormCode + "&customFormTypeCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_CODE + "&customFormTypeMenuCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_MENU_CODE;
		parent.layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toMyMedicalActivities",
			area : [ '98%', '98%' ],
			title : false,
			scrollbar : false,
			content : url
		});
		
	},

}