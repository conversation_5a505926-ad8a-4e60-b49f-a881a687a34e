var myNewProjectAndTechnologyList = {
	
	initDiv : function(obj, userCode) {
		$(obj).empty();
		var html = '<iframe id="myNewProjectAndTechnology" class="" src="myNewProjectAndTechnology.html?userCode=' + userCode + '&funCode=' + assemblys.top.mdms.mdmsConstant.FUN_MDMS_LAUNCH + '" frameborder="0" width="100%" height=' + newDoctorInfo.returnHeight(5) + '"></iframe>';
		var $div = $("div[class='bodys layui-form']");
		$(obj).append(html);
		
	},
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]")).then(function() {
			myNewProjectAndTechnologyList.getmyNewProjectAndTechnologyPager();
			myNewProjectAndTechnologyList.initLayuiForm();
		});
		
	},
	myNewProjectAndTechnologyListInit : function() {
		return $.ajax({
			url : basePath + "mdms/myNewProjectAndTechnology/myNewProjectAndTechnologyListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [];
		return params;
	},
	getmyNewProjectAndTechnologyPager : function() {
		var cols = [ {
			title : '操作',
			width : '10%',
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_delete" title="浏览" lay-event="toViewmyNewProjectAndTechnology"></i>';
				
				return html;
			}
		}, {
			"title" : '序号',
			"align" : "center",
			"width" : "8%",
			"type" : 'numbers'
		}, {
			title : '新技术新项目名称',
			width : '25%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.projectName);
			}
		}, {
			title : '新技术新项目类别',
			width : '15%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.newProjectType);
			}
		}, {
			title : '先进性评估',
			width : '12%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.progressiveness);
			}
		}, {
			title : '新技术新项目负责人',
			width : '20%',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.projectCharger);
			}
		}, {
			title : '状态',
			align : "center",
			width : '10%',
			templet : function(d) {
				var stateName = "";
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_APPROVE) {
					stateName = "审核中";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_RETURN) {
					stateName = "回退";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_LAUNCH) {
					stateName = "开展中";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_ZCG) {
					stateName = "转常规";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YZCG) {
					stateName = "已转常规";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_ZZ) {
					stateName = "终止";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YZZ) {
					stateName = "已终止";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YQ) {
					stateName = "延期";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_YYQ) {
					stateName = "已延期";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_FINISH) {
					stateName = "已完成";
				}
				if (d.Status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_DRAFT) {
					stateName = "草稿";
				}
				
				return assemblys.htmlEncode(stateName);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/functionModule/newProjectAndTechnology/getMyNewProjectAndTechnologyList.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			height : newDoctorInfo.returnHeight(),
			width : newDoctorInfo.returnWidth(-200),
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toViewmyNewProjectAndTechnology : myNewProjectAndTechnologyList.toViewmyNewProjectAndTechnology,
			}
		});
		
	},
	
	toViewmyNewProjectAndTechnology : function(d) {
		var customFormFilledCode = d.CustomFormFilledCode;
		var customFormCode = d.CustomFormCode;
		
		var url = basePath + "mdms/functionModule/mdmsCustomDetail/mdmsCustomDetail.html?noBack=true&appCode=" + parent.param.get("appCode") + "&funCode=" + parent.param.get("funCode") + "&customFormFilledCode=" + customFormFilledCode + "&approvalBelongCode=" + customFormFilledCode + "&customFormCode=" + customFormCode + "&customFormTypeCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_CODE + "&customFormTypeMenuCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_MENU_CODE;
		parent.layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditmyNewProjectAndTechnology",
			area : [ '98%', '98%' ],
			title : false,
			scrollbar : false,
			content : url
		});
		
	},

}