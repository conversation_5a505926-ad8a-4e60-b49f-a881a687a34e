//存放分类
var tableId = [];
var tabId = "";
var hasTip = false;
var newCertificateInfo = {
	
	init : function(divHtml) {
		$(divHtml).empty();
		tableId = [];
		tabId = divHtml;
		newCertificateInfo.getCertificateInfo().then(function(data) {
			//动态创建证件分类
			newCertificateInfo.createCerType(data.titleList, data.certificateClassList, divHtml);
			//创建分类table内容
			newCertificateInfo.createTable(data.certificateClassList);
			$("#certificateDiv").css("height", newDoctorInfo.returnHeight(5));
			$("#certificateDiv").css("width", newDoctorInfo.returnWidth());
			newCertificateInfo.initLayuiForm();
		});
	},
	
	initLayuiForm : function() {
		
		if (param.get("formStatus") != assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_APPROVE && (param.get("certifiCateAdd") == "true")) {
			$("#addCerButton").removeClass("layui-hide");
			$("#commitButton").removeClass("layui-hide");
		}
		
		layui.form.render();
	},
	
	getCertificateInfo : function() {
		return $.ajax({
			url : basePath + "/mdms/certificateManagement/getUserCertificateInfo.spring?customformfilledCode=" + param.get("customFormFilledCode") + "&businessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTORMANAGER + "&canShowNull=1",
			type : "get",
			data : {}
		}).then(function(data) {
			return data;
		});
	},
	
	createTable : function(data) {
		for (var i = 0; i < tableId.length; i++) {
			for ( var key in data) {
				if (key == tableId[i]) {
					layui.table.render({
						elem : '#' + tableId[i],
						data : data[key],
						limit : 50,
						page : false,
						cols : [ [ {
							title : '操作',
							width : "10%",
							align : "center",
							templet : function(d) {
								var html = '';
								if (param.get("certifiCateView") == "true") {
									html += '<i class="layui-icon layui-icon-search i_delete" title="查看"   onclick="newCertificateInfo.certifiEdit(' + d.CertificateId + ',1)"></i>';
								}
								//hwx 审核中不可操作医师证件
								if (param.get("formStatus") != assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_APPROVE) {
									if (param.get("certifiCateEdit") == "true") {
										html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑"  onclick="newCertificateInfo.certifiEdit(' + d.CertificateId + ',0)"></i>';
									}
									//hwx 2024年3月27日上午9:49:10 修改必须填写外的证件可删除
									if (param.get("certifiCateDel") == "true" && d.Necessary != 1) {
										html += '<i class="layui-icon layui-icon-delete i_delete" title="删除"  onclick="newCertificateInfo.certifiDel(' + d.CertificateId + ')"></i>';
									}
								}
								return html;
							}
						}, {
							title : '证件编号',
							width : "10%",
							align : "center",
							templet : function(d) {
								return assemblys.htmlEncode(d.CertifiName);
							}
						}, {
							title : '证件名称',
							width : "15%",
							align : "center",
							templet : function(d) {
								if (d.fileList.length == 0) {
									if (hasTip == false) {
										$("span[class='titleName']").eq(0).append("<span style='color:red;'>（请对红色证件名称的证件填写信息以及上传对应的附件！）</span>");
										hasTip = true;
									}
									return "<font style='color:red;'>" + d.DictName + "</font>";
								} else {
									return assemblys.htmlEncode(d.DictName);
								}
							}
						}, {
							title : '发证单位',
							width : "15%",
							align : "center",
							templet : function(d) {
								return assemblys.htmlEncode(d.signUnit)
							}
						}, {
							title : '到期日期',
							width : "15%",
							align : "center",
							templet : function(d) {
								var html = d.dueDate ? assemblys.dateToStr(d.dueDate, 'yyyy-MM-dd') : "";
								if (d.Status == 2) {
									html += "<font color='red'>(已过期)</font>";
								}
								return html;
							}
						}, {
							title : '附件',
							align : "center",
							templet : function(d) {
								
								var html = '';
								var files = d.fileList;
								var url = "";
								if (files.length > 0) {
									for (var j = 0; j < files.length; j++) {
										var suffix = files[j].attaType.toUpperCase();
										
										if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
											url = basePath + "/frame/fileUpload/downloadFile.spring?eifAttaUrl=" + files[j].attaUrl + "&eifAttaName=" + encodeURIComponent(files[j].attaName);
											html += '<img src="' + url + '" style="width:30px;height:30px;margin-right:10px" lay-event="toFindCertificateFile_' + files[j].CertificateFileID + '">';
										} else if (suffix == "PDF") {
											var fileName = files[j].fileName;
											html += "<i class=\"cattachqueue-remove layui-icon2\" id=\"pdfview\" title=\"预览：" + files[j].attaName + "\" onclick=\"newCertificateInfo.toShowPDF('" + fileName + "');\" >&#xe8bc;</i>";
										}
									}
								}
								return html;
							}
						} ] ],
					});
					
					layui.table.on("tool(" + tableId[i] + ")", function(obj) {
						
						newCertificateInfo.toFindCertificateFile(obj.data.fileList, obj.event);
						
					});
				}
			}
		}
	},
	
	toFindCertificateFile : function(data, eventId) {
		var certificateFileID = eventId.substring(eventId.lastIndexOf("_") + 1, eventId.length);
		for (var i = 0; i < data.length; i++) {
			if (data[i].CertificateFileID == certificateFileID) {
				pubUploader.preview(data[i].attaName, data[i].attaUrl);
			}
		}
	},
	// 预览PDF
	toShowPDF : function(fileName) {
		parent.layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toShowDocument",
			area : [ '70%', '90%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/medicalDocManager/toShowDocument.html?fileName=" + fileName
		});
	},
	
	createCerType : function(title, table, divHtml) {
		var newData = [];
		//无分类或无数据
		if (title.length == 0 || JSON.stringify(table) === "{}") {
			var mapping = [ {
				name : "操作",
			}, {
				name : "证件名称",
			}, {
				name : "附件",
			}, {
				name : "更新时间",
			}, {
				name : "备注",
			} ]
			// 渲染
			initCustomDetail.initTableList(divHtml, mapping, new Array());
			$(divHtml).children().before('<div style="float: right; margin-bottom: 5px;"><button id="addCerButton" type="button" class="layui-btn layui-btn-sm layui-hide" onclick="newCertificateInfo.certifiEdit(0, 0);">新增</button></div>');
		} else {
			//遍历证件总分类
			for (var i = 0; i < title.length; i++) {
				var html = {};
				//遍历个人证件还有分类分类
				for ( var key in table) {
					if (key == title[i].DictCode) {
						tableId.push(key);
						
						//标题（分类）
						var titleChildrenDiv = [ {
							"tagName" : "img",
							"className" : "titleImg",
							"attr" : {
								"src" : title[i].DictContent
							},
						}, {
							"tagName" : "span",
							"className" : "titleName",
							"innerHTML" : title[i].DictName
						} ];
						//第一个标题增加新增按钮
						if (tableId.length == 1) {
							titleChildrenDiv.push({
								"tagName" : "button",
								"type" : "button",
								"className" : "layui-btn layui-btn-sm layui-hide",
								"id" : "addCerButton",
								"attr" : {
									"onclick" : "newCertificateInfo.certifiEdit(0, 0)",
								},
								"innerHTML" : "上传证件"
							}, {
								"tagName" : "button",
								"type" : "button",
								"style" : {
									"float" : "right",
									"margin-top" : "16px",
									"margin-right" : "21px"
								},
								"className" : "layui-btn layui-btn-sm layui-hide",
								"id" : "commitButton",
								"attr" : {
									"onclick" : "newCertificateInfo.certifiCommit()",
								},
								"innerHTML" : "证件审批"
							});
						}
						html = {
							"tagName" : "div",
							"className" : "titleTable",
							"children" : [ {
								"tagName" : "div",
								"className" : "lhead",
								"children" : titleChildrenDiv
							}, {
								"tagName" : "div",
								"className" : "ltable",
								"children" : [ {
									"tagName" : "dev",
									"id" : key,
									"attr" : {
										"lay-filter" : key
									},
								} ]
							} ]
						
						}
					}
				}
				newData.push(html);
			}
		}
		assemblys.createElement(newData, $(divHtml)[0]);
	},
	
	//证件编辑
	certifiEdit : function(certificateId, showOrEdit) {
		var id = parseInt(certificateId);
		var html = "certificateManagementEdit.html";
		if (showOrEdit == 1) {
			html = "certificateManagementView.html";
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "certifiToEdit",
			area : [ '850px', '550px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&certificateId=" + id + "&funCode=" + param.get("funCode") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&userCode=" + $("#userCode").text()
		});
	},
	//证件审批
	certifiCommit : function() {
		layer.confirm("确定证件需要提交审批吗？", function() {
			$.ajax({
				url : basePath + "mdms/certificateManagement/certificommit.spring",
				type : "post",
				data : {
					customFormFilledCode : param.get("customFormFilledCode")
				}
			}).then(function(data) {
				assemblys.msg("提交成功", function() {
					window.location.reload();
				});
				return data;
			});
		})
	},
	//删除证件
	certifiDel : function(certificateId) {
		layer.confirm("确定要删除吗？", function() {
			$.ajax({
				url : basePath + "mdms/certificateManagement/deleteCertificateManagement.spring",
				type : "post",
				data : {
					certificateId : certificateId,
					formStatus : $("input[name='formStatus']").val()
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					newCertificateInfo.init(tabId);
				});
				return data;
			});
		})

	},

}