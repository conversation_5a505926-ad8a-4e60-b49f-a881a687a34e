var menuList = {};
var moduleList = [];
var userCode = "";
var deptId = "";
var customFormCompNo = "";
var operation;
var write;
var anaes;
var room;
var profession;
var isEmptyUser = false;//hwx 2022-12-05 控制空档案时不执行后续方法标志
var formComplaintDispute; // 是否显示医务表单的投诉纠纷记录
var newDoctorInfo = {
	init : function() {
		
		newDoctorInfo.getUserInfo().then(function(data) {
			
			if (data.customFormFilledCode == 0) {
				alert("您还没档案，请建立档案！");
				param.set("compNo", data.compNo);
				param.set("prevCustomFormCode", assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTORMANAGER);
				param.set("appCode", assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME);
				newDoctorInfo.doctorEdit(1);
				$("a[class='layui-layer-ico layui-layer-close layui-layer-close2']").parent().hide();
				isEmptyUser = true;
			}
			
			// 获取权限
			return newDoctorInfo.getDetailParam();
			
		}).then(function(data) {
			if (!isEmptyUser) {
				var data = data.data;
				
				//手术
				param.set("opertaionView", data.opertaionView);
				param.set("opertaionAdd", data.opertaionAdd);
				param.set("opertaionDel", data.opertaionDel);
				param.set("opertaionEdit", data.opertaionEdit);
				param.set("operationClassName", data.operationClassName);
				//麻醉
				param.set("anesthesiaView", data.anesthesiaView);
				param.set("anesthesiaAdd", data.anesthesiaAdd);
				param.set("anesthesiaDel", data.anesthesiaDel);
				param.set("anesthesiaEdit", data.anesthesiaEdit);
				//查房
				param.set("threeRoundView", data.threeRoundView);
				param.set("threeRoundAdd", data.threeRoundAdd);
				param.set("threeRoundDel", data.threeRoundDel);
				param.set("threeRoundEdit", data.threeRoundEdit);
				
				//处方
				param.set("prescriptionView", data.prescriptionView);
				param.set("prescriptionAdd", data.prescriptionAdd);
				param.set("prescriptionDel", data.prescriptionDel);
				param.set("prescriptionEdit", data.prescriptionEdit);
				
				//医师负面
				param.set("doctorNegativeView", data.doctorNegativeView);
				param.set("doctorNegativeAdd", data.doctorNegativeAdd);
				param.set("doctorNegativeDel", data.doctorNegativeDel);
				param.set("doctorNegativeEdit", data.doctorNegativeEdit);
				
				if (data.doctorNegativeView == false) {
					$("li[state='doctorNegative']").hide();
				} else {
					$("li[state='doctorNegative']").show();
				}
				
				//定期考核功能权限
				param.set("routineCheckView", data.routineCheckView);
				param.set("routineCheckAdd", data.routineCheckAdd);
				param.set("routineCheckDel", data.routineCheckDel);
				param.set("routineCheckEdit", data.routineCheckEdit);
				
				//三基考核功能权限
				param.set("baseExamView", data.baseExamView);
				param.set("baseExamAdd", data.baseExamAdd);
				param.set("baseExamDel", data.baseExamDel);
				param.set("baseExamEdit", data.baseExamEdit);
				
				//个人知识及技能考核功能权限
				param.set("personExamView", data.personExamView);
				param.set("personExamAdd", data.personExamAdd);
				param.set("personExamDel", data.personExamDel);
				param.set("personExamEdit", data.personExamEdit);
				
				if (data.routineCheckView == false && data.baseExamView == false && data.personExamView == false) {
					$("li[state='excamManage']").hide();
					
				} else {
					$("li[state='excamManage']").show();
				}
				
				//我的证件功能权限
				param.set("certifiCateView", data.certifiCateView);
				param.set("certifiCateAdd", data.certifiCateAdd);
				param.set("certifiCateDel", data.certifiCateDel);
				param.set("certifiCateEdit", data.certifiCateEdit);
				
				//轮转功能权限
				param.set("exchangeManageView", data.exchangeManageView);
				param.set("exchangeManageAdd", data.exchangeManageAdd);
				param.set("exchangeManageDel", data.exchangeManageDel);
				param.set("exchangeManageEdit", data.exchangeManageEdit);
				
				if (data.exchangeManageView == false) {
					$("li[state='rotationRecord']").hide();
				} else {
					$("li[state='rotationRecord']").show();
				}
				
				//奖励情况功能权限
				param.set("rewardPunishView", data.rewardPunishView);
				param.set("rewardPunishAdd", data.rewardPunishAdd);
				param.set("rewardPunishDel", data.rewardPunishDel);
				param.set("rewardPunishEdit", data.rewardPunishEdit);
				if (data.rewardPunishView == false) {
					$("li[state='rewardPunish']").hide();
				} else {
					$("li[state='rewardPunish']").show();
				}
				
				//安全行为功能权限
				param.set("behaviorRecordView", data.behaviorRecordView);
				param.set("behaviorRecordAdd", data.behaviorRecordAdd);
				param.set("behaviorRecordDel", data.behaviorRecordDel);
				param.set("behaviorRecordEdit", data.behaviorRecordEdit);
				if (data.behaviorRecordView == false) {
					$("li[state='behaviorRecord']").hide();
				} else {
					$("li[state='behaviorRecord']").show();
				}
				
				//重大差错功能权限
				param.set("majorAccidentView", data.majorAccidentView);
				param.set("majorAccidentAdd", data.majorAccidentAdd);
				param.set("majorAccidentDel", data.majorAccidentDel);
				param.set("majorAccidentEdit", data.majorAccidentEdit);
				
				//健康情况
				param.set("personalhealthView", data.personalhealthView);
				//个人监测
				param.set("dosemonitoringView", data.dosemonitoringView);
				
				//医疗活动 hwx 2023-5-6 
				param.set("medicalActivitiesView", data.medicalActivitiesView);
				
				if (data.medicalActivitiesView == false) {
					$("li[state='medicalActivities']").hide();
				} else {
					$("li[state='medicalActivities']").show();
				}
				
				if (data.majorAccidentView == false) {
					$("li[state='majorsAccident']").hide();
				} else {
					$("li[state='majorsAccident']").show();
				}
				var userType = param.get("userType");
				if (data.personalhealthView == false && userType != "防辐射人员") {
					$("li[state='personalhealth']").hide();
				} else {
					$("li[state='personalhealth']").show();
				}
				
				if (data.dosemonitoringView == false && userType != "防辐射人员") {
					$("li[state='dosemonitoring']").hide();
				} else {
					$("li[state='dosemonitoring']").show();
				}
				
				// 是否显示不良事件记录li  lcp 2022-11-17
//				if (data.eventReportSwitch == false) {
				$("li[state='adverseEvents']").hide();
//				} else {
//					$("li[state='adverseEvents']").show();
//				}
				// 是否显示投诉纠纷记录li  lcp 2022-11-17
//				if (data.complaintSwitch == false) {
				$("li[state='complainingDispute']").hide();
//				} else {
//					$("li[state='complainingDispute']").show();
//				}
//				
				// hwx 2023-7-28 是否显示考评情况li
				if (data.evaluationStatusView == false) {
					$("li[state='evaluationStatus']").hide();
				} else {
					$("li[state='evaluationStatus']").show();
				}
				// 是否显示医务表单的投诉纠纷记录
				formComplaintDispute = data.formComplaintDispute;
				
				//hwx 2023-2-14 判断是否显示工作量统计
				if (data.hasWorkStaticsRight == false) {
					$("li[state='workloadStatistics']").hide();
				} else {
					$("li[state='workloadStatistics']").show();
				}
				
				if (data.customFormFilledCode != 0) {
					return newDoctorInfo.getCustomFormData();//获取表单数据
				}
				
			}
			
		}).then(function(data) {
			if (!isEmptyUser) {
				// 计算容器高度
				newDoctorInfo.computeHeight();
				
				// 初始化监听
				newDoctorInfo.initMonitor();
				
				// 触发点击
				$("li[state='physicianInfoPhoto']").click();//默认点击医师基本信息
				
				// 是否显示医务表单的投诉纠纷记录
				if (formComplaintDispute == false) {
					$("li[state='complaintDisputeRecord']").hide();
				}
				
				newDoctorInfo.getEvent();
				newDoctorInfo.getCdms();
				newDoctorInfo.initList();
				newDoctorInfo.explatinWarn();
			}
		}).then(function(data) {
			importOut.getData();
			$(".headRight").removeClass("layui-hide");


			// <AUTHOR> @date 2023-08-17 10:49:34 @description 平台消息-负面提醒跳转个人信息定位页面
			if (param.get("skipStatus") == 1 && param.get("skipFunCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTORNEGATIVE) {
				newDoctorInfo.scrollHeight("doctorNegative");
			}
			
			//如果是工作台的编辑进来点击打开编辑页面 gx.zeng 20231205
            if (param.get("isEditFile") == 1) {
                    $("#btnEdit").click();
            }
            
		});
		
	},
	initMonitor : function() {
		
		// 监听 tab
		layui.element.on("tab(tabView)", function(data) {
			
			var state = $(this).attr("state");
			param.set("state", state)
			// 样式
			var $div = $("div[class='layui-tab-item'][id='" + state + "']");
			$div.siblings().removeClass("layui-show");
			$div.addClass("layui-show");
			
			// 当前
			$(this).siblings().removeClass("skin-div-font skin-div-border");
			$(this).siblings().find("label").remove();
			$(this).find("label").remove();
			$(this).addClass("skin-div-font skin-div-border");
			$(this).prepend("<label>● </label>");
			
			$("#" + state + "Div").empty();
			//hwx 2022-12-06 修复业务编号为空不显示问题
			if (!state) {
				assemblys.alert("请通知管理员配置医师档案分类的【业务编号】！");
				return;
			}
			// 加载
			newDoctorInfo.initDiv();
		});
		
		$('body').click(function(e) {
			if (e.target.id != 'showName' && e.target.id != 'myCer') {
				if ($('.rightmenu').is(':visible')) {
					$(".certifiDiv").removeClass("layui-hide");
					$(".rightmenu").css("display", "none");
				}
			}
		});
		
		// 展开和收起
		$("#mainBar").on("click", function() {
			if ($(this).hasClass("mainBar-up")) {
				$(this).removeClass("mainBar-up").addClass("mainBar-down");
				$(".main").hide();
			} else {
				$(this).removeClass("mainBar-down").addClass("mainBar-up");
				$(".main").show();
			}
			// 计算容器高度
			newDoctorInfo.computeHeight();
		});
		//hwx 搜索框
		$("#searchInput").on("input", function(e) {
			//获取input输入的值
			var searchVal = e.delegateTarget.value;
			var foundLi = $(this).parent().parent().next().find('li:contains("' + searchVal + '")');
			if (searchVal && foundLi.length > 0) {
				$(this).parent().parent().next().find("li").addClass("layui-hide");
				foundLi.each(function() {
					$(this).removeClass("layui-hide");
				});
			} else {
				$(this).parent().parent().next().find("li").removeClass("layui-hide");
			}
		});
		//hwx 左侧收缩
		$(".tree_custom_opt").on("click", function() {
			var tags = $(this).attr("data-tag");
			$(this).empty();
			if (tags == 1) {
				$(this).attr("title", "点击显示");
				$(this).append('<i class="layui-icon2">&#xe730;</i>');
				$(this).attr("data-tag", 0);
				$(".leftMenu").addClass("layui-hide");
				$(".tree_custom_opt").css("left", "10px");
				$(".rightContent").css("left", "0px");
				$(".rightContent").children("div").each(function() {
					$(this).css("min-width", (newDoctorInfo.returnWidth() + 175) + "px");
				});
			} else {
				$(this).attr("title", "点击隐藏");
				$(this).append('<i class="layui-icon2">&#xe71c;</i>');
				$(this).attr("data-tag", 1);
				$(".leftMenu").removeClass("layui-hide");
				$(".tree_custom_opt").css("left", "165px");
				$(".rightContent").css("left", "175px");
				$(".rightContent").children("div").each(function() {
					$(this).css("min-width", newDoctorInfo.returnWidth() + "px");
				});
			}
		});
		layui.form.render();
	},
	initDiv : function() {
		
		var state = param.get("state");
		
		//档案审批
		if (state == "doctorApprove") {
			approval.render("#doctorApproveDiv");
		} else
		//工作量统计
		if (state == "workloadStatistics") {
			param.set("userCode", $("#userCode").text());
			workloadStatistics.getDivHtml("#workloadStatisticsDiv");
		} else
		//医师证件
		if (state == "certificate") {
			newCertificateInfo.init("#certificateDiv");
		} else
		//操作记录
		if (state == "operationRecord") {
			pubBizsysLog.initOptLogList("#operationRecordDiv");
		} else
		//轮转记录
		if (state == "rotationRecord") {
			rotationRecord.deptExchangeList("#rotationRecordDiv");
		} else
		//考核管理
		if (state == "excamManage") {
			
			newDoctorInfo.showAllExam("excamManageDiv");
			
			if (param.get("routineCheckView") == 'true') {
				otherFormDetail.getRoutineCheckList("routineCheckFrame");
			}
			if (param.get("baseExamView") == 'true') {
				otherFormDetail.getBaseExamList("baseCheckFrame");
			}
			if (param.get("personExamView") == 'true') {
				otherFormDetail.getPersonCheckList("personCheckFrame");
			}
			
		} else
		//奖励情况
		if (state == "rewardPunish") {
			newRewardPunish.rewardPunishList("#rewardPunishDiv");
		} else
		//技术授权
		if (state == "technicalAuthorization") {
			technicalAuthorization.init("#technicalAuthorizationDiv");
		} else
		//新技术新项目
		if (state == "newTechNique") {
			myNewProjectAndTechnologyList.initDiv("#newTechNiqueDiv", $("#userCode").text());
		} else
		//不良事件	
		if (state == "adverseEvents") {
			var html = '<iframe id="adverseEventsFrame" src="" width="100%" height="' + newDoctorInfo.returnHeight(5) + '" frameborder="0"></iframe>';
			$("#adverseEventsDiv").append(html);
			var url = basePath + "mdms/functionModule/newDoctorFile/myAdverseEventsList.html?compNo=" + param.get("compNo") + "&funCode=" + param.get("funCode") + "&userCode=" + $("#userCode").text();
			$("#adverseEventsFrame").attr("src", url);
		} else
		//投诉纠纷记录	
		if (state == "complainingDispute") {
			var html = '<iframe id="complainingDisputeFrame" src="" width="100%" height="' + newDoctorInfo.returnHeight(5) + '" frameborder="0"></iframe>';
			$("#complainingDisputeDiv").append(html);
			var url = basePath + "mdms/functionModule/newDoctorFile/myComplainingDisputeList.html?compNo=" + param.get("compNo") + "&userCode=" + $("#userCode").text();
			//var url = "http://localhost:8080/P2251/cdmsv2/complaintHandling/complaintList/complaintList.html?funCode=COMPLAINT_CONSULT&complaintMethod=ComplaintMethodConsult&searchComplaintUser=" + $("#userCode").text();
			$("#complainingDisputeFrame").attr("src", url);
		} else
		//医疗安全行为记录
		if (state == "behaviorRecord") {
			behaviorRecord.init("#behaviorRecordDiv")
		} else
		//重大差错及事故处理
		if (state == "majorsAccident") {
			majorsAccident.init("#majorsAccidentDiv")
		} else
		//医师负面
		if (state == "doctorNegative") {
			doctorNegative.init("#doctorNegativeDiv",0);
		} else
		//剂量检测
		if (state == "dosemonitoring") {
			var html = '<iframe id="dosemonitoringFrame" src="" width="100%" height="' + newDoctorInfo.returnHeight(5) + '" frameborder="0"></iframe>';
			$("#dosemonitoringDiv").append(html);
			var url = basePath + "mdms/functionModule/dosemonitoring/dosemonitoringList.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&formStatus=" + param.get("formStatus");
			$("#dosemonitoringFrame").attr("src", url);
			
		} else
		//健康情况
		if (state == "personalhealth") {
			var html = '<iframe id="personalhealthFrame" src="" width="100%" height="' + newDoctorInfo.returnHeight(5) + '" frameborder="0"></iframe>';
			$("#personalhealthDiv").append(html);
			var url = basePath + "mdms/functionModule/personalHealth/personalHealthList.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&formStatus=" + param.get("formStatus");
			$("#personalhealthFrame").attr("src", url);
		} else if (state == "medicalActivities") {//医疗活动
			newDoctorInfo.selectmedicalActivities();
		} else if (state == "evaluationStatus") {//考评情况
			newDoctorInfo.selectEvaluationStatus("#evaluationStatusDiv");
		}
		
		//遍历菜单根据选中的加载数据
		for ( var name in menuList) {
			if (state == name) {
				var thtml = newDoctorInfo.getDataTypeToTabel(menuList[name]);
				$("#" + name + "Div").append(thtml);
				//补全确实的td
				var tdNums = $("#" + name + "Div").find("tr:last").find("td").length;//最后一行td数
				if (tdNums != 6) {
					$("#" + name + "Div").find("tr:last").find("td:last").attr("colspan", 6 - tdNums + 1);
				}
			}
		}
		
		if (param.get("formStatus") == 1 || param.get("formStatus") == 2 || param.get("formStatus") == 0) {
			setTimeout('importOut.getCustomFormLog(param.get("customFormFilledCode"))', 2000);
		}
		
	},
	returnHeight : function(num) {
		var _height = ($(window).height() - 145);
		if ($(".main").is(":visible")) {
			_height -= 45;
		} else {
			_height += 130;
		}
		if (num) {
			_height -= num;
		}
		return _height;
	},
	returnWidth : function(num) {
		var _width = ($(window).width() - 215);
		if (num) {
			_width -= num;
		}
		return _width;
	},
	computeHeight : function() {
		var _height = newDoctorInfo.returnHeight();
		var _width = newDoctorInfo.returnWidth();
		$("#tabView").height((Number(_height * 0.94)));
		$("#tabView").css("overflow-y", "auto");
		$("#tabView").css("overflow-x", "hidden");
		$("#contentID").children(".layui-tab-item").css("height", (_height * 0.98));
		if ($(".leftMenu").hasClass("layui-hide")) {
			_width = _width + 175;
		}
		$("#contentID").children(".layui-tab-item").css("min-width", _width);
	},
	//获取用户和医师档案信息
	getUserInfo : function() {
		return $.ajax({
			url : basePath + "mdms/mdmsCommon/getUserInfo.spring",
			dataType : "json",
			data : {
				"doctorCustomFormFilledCode" : param.get("customFormFilledCode"),
			},
			success : function(data) {
				param.set("customFormFilledCode", data.customFormFilledCode);
				param.set("formStatus", data.status);
				if (data.status != assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_APPROVE) {
					newDoctorInfo.showBtn();
				}
				
				var user = data.user;
				//hwx 2024年1月26日上午11:41:45增加判断用户信息是否存在
				if(user){
					deptId = user.deptId;
				}
				param.set("formStatus", data.status);
				//if (user != undefined) {
				userCode = data.docUserCode;
				param.set("userCode", userCode);
				//hwx 2023-4-6 处理状态显示
				newDoctorInfo.initBtn(data);
				if ($(window).width() < 1024) {
					$(".classTime").css("margin-left", "8%");
				}
				$(".docName").text(data.docUserName);
				var $showClass = "";
				var len = newDoctorInfo.strlen(data.docUserName);
				var len1 = newDoctorInfo.strlen(data.docDeptName);
				if (len > 8 && len < 13) {
					$(".docName").css("min-width", data.docUserName.length * 2 + "%");
					$(".docDept").css("width", len1 * 0.8 + "%");
					$(".docDept").css("padding-left", data.docUserName.length * 0.6 + "%");
					$showClass = "style='left:" + data.docUserName.length * 2.82 + "% !important;'";
				}
				var subDept = "";
				if(len1>10){
					subDept = data.docDeptName.substring(0,5)+"...";
				}else{
					subDept = data.docDeptName;
				}
				$(".docDept").html('<div class="docDeptImg" ' + $showClass + '><img src = "' + basePath + '/mdms/functionModule/newDoctorFile/image/dept.png"/></div><div class="docDeptText" title="'+data.docDeptName+'">' + subDept + '</div>');
				$(".docSchool").text(data.school);//毕业学校
				$(".profession").text(data.userMajor);//专业
				$(".education").text(data.education);//学历
				$(".studyClass").text(data.masterDegree);//学位
				if(!data.tel){
					$(".telNo").parent().addClass("layui-hide");
				}
				$(".telNo").text(data.tel);//
				$(".sex").text(data.docSex);//
				$(".status").text(data.userStatus);//状态
				$(".identityClass").text(data.identityClass);//身份类别
				$(".userCode").text(data.docUserCode);
				param.set("compNo", user != null ? user.compNo : data.compNo);
				$(".years").text(data.workYears + "年");//工作年限
				$(".introudice").text(data.physicianProfile);//简介
				if (data.timeVisits) {
					$("#timeText").text(data.timeVisits);//出诊时间
				} else {
					$("#timeText").text("未设置");//出诊时间
				}
				$("#shoushu").text(data.operationright);
				$("#mazui").text(data.anaesthesiaright);
				$("#chufang").text(data.writeright);
				//hwx 2023-6-25 隐藏功能
//				$("#chafang").text(data.checkroomright);
//				$("#yazhuanye").text(data.subProfessionRight);
				
				if (data.age) {
					$(".age").text(data.age + "岁");
				} else {
					$(".age").text('~ ' + "岁");
				}
				if (data.phtotUrl) {
					if (data.phtotUrl.indexOf("tx.png") > -1) {
						data.phtotUrl = basePath + "frame/images/tx.png";
					}
					$("#headMage").attr("src", data.phtotUrl);
				}
				if (data.userType) {
					param.set("userType", data.userType);
				}
				customFormCompNo = data.customFormCompNo;
				
				//医疗小组
				var groupList = data.groupList;
				var html = "";
				if (groupList.length > 0) {
					var showGroupName = "";
					for (var i = 0; i < groupList.length; i++) {
						var groupName = groupList[i].MTTeamName+"("+groupList[i].IdentityName+")";
						var imgUrl = groupList[i].ImgUrl;
						var newbasePath = basePath.substring(0, basePath.length - 1);
						var index = newbasePath.lastIndexOf("/");
						var str = newbasePath.substring(0, index + 1) + "/up";
						if (imgUrl) {
							imgUrl = str + imgUrl;
						} else {
							imgUrl = basePath + "mdms/functionModule/newDoctorFile/image/honor1.png";
						}
						showGroupName += groupName+"；";
						//hwx 2023年9月18日下午3:08:28 医疗技术小组最多显示3个
						if(i<3){
							html += '<div class="groupAll"><img class="groupImg" src="' + imgUrl + '"/><div class="groupName1">' + groupName + '</div></div>';
						}
					}
					if(groupList.length>3){
						html += '<i class="layui-icon2" style="color:#FF7301;" title="小组列表：'+showGroupName.substring(0, showGroupName.length - 1)+'">&#xe8d4;</i>';
					}
				} else {
					if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES) {
						html = "<font class='groupFont' title='点击设置分组' onclick='newDoctorInfo.showGroup();'>未分组</font>";
					} else {
						html = "<font class='goodFont'>未分组</font>";
					}
					//hwx 2023年9月18日上午11:50:48 小组为空不显示
					$("#groupId").parent().addClass("layui-hide");
				}
				$("#groupId").append(html);
				
				//执业范围
				var goodList = data.goodList;
				var goodHtml = "";
				if (goodList.length > 0) {
					var showGoodName = "";
					for (var i = 0; i < goodList.length; i++) {
						showGoodName += goodList[i].customTextValue+"；";
						//hwx 2023年9月18日下午3:08:28执业范围最多显示3个
						if(i<3){
							goodHtml +=  '<div class="good">' + goodList[i].customTextValue + '</div>';
						}
					}
					if(goodList.length>1){
						goodHtml += '&nbsp;&nbsp;<i class="layui-icon2" style="color:#0164FE;" title="执业范围列表：'+showGoodName.substring(0, showGoodName.length - 1)+'">&#xe910;</i>';
					}
					//hwx 2023年9月18日下午2:52:06 根据执业范围显示手机号的间距
					$(".tel").css("margin-left",(goodList.length*15));
				} else {
					goodHtml = "<font class='goodFont'>未设置</font>";
					//hwx 2023年9月18日上午11:50:48 小组为空不显示
					$("#goodness").parent().addClass("layui-hide");
				}
				
				$("#goodness").append(goodHtml);
				
				//}
			}
		})
	},
	showRight : function(type) {
		var titleName = "手术一览";
		var tabState = 99;
		if (type == 2) {
			titleName = "麻醉一览";
			tabState = 3;
		} else if (type == 3) {
			titleName = "查房一览";
			tabState = 2;
		} else if (type == 4) {
			titleName = "处方一览";
			tabState = 1;
		} else if (type == 5) {
			titleName = "亚专业一览";
			tabState = 4;
		}
		//hwx 2023-5-8 增加个人信息技术一览
		var url = basePath + "mdms/functionModule/newDoctorFile/myAuthorization.html?funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SHOUSHUQUANXIAN + "&titleName=" + titleName + "&tabState=" + tabState;
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toMyAuthorization",
			area : [ '90%', '90%' ],
			title : false,
			scrollbar : false,
			content : url
		});
	},
	doctorEdit : function(newOrEdit) {
		
		var url = basePath + "frame/customForm/customFormTemplate.html?customFormBusinessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSDAGL + "&customFormCode=&customFormFilledCode=" + param.get("customFormFilledCode") + "&type=2&hasSave=2&hasBack=0&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME + "&compNo=" + customFormCompNo + "";
		if(param.get("formStatus")=="0"){//hwx 2023年9月19日下午3:42:17 草稿状态下的完善档案
			url = basePath + "frame/customForm/customFormTemplate.html?customFormBusinessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSDAGL + "&customFormCode=&customFormFilledCode=" + param.get("customFormFilledCode") + "&type=1&hasBack=0&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME + "&compNo=" + customFormCompNo + "";
		}else if (newOrEdit == "1") {//新增
			url = basePath + "frame/customForm/customFormTemplate.html?customFormCode=&customFormBusinessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSDAGL + "&compNo=" + customFormCompNo + "&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME + "&type=1";
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditOperationRighti",
			area : [ '100%', '100%' ],
			title : false,
			scrollbar : false,
			content : url
		});
	},
	//hwx 2023-7-25 处理左侧li的标签
	getCustomFormData : function() {
		
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/newCustomForm/getCustomFormData.spring",
			type : "get",
			data : {
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"isDetail" : true
			},
			dataType : "json",
			success : function(data) {
				var customModularList = data.customModularList;
				var menuHtml = "";
				var divHtml = "";
				for (var i = 0; i < customModularList.length; i++) {
					var customFieldList = customModularList[i].customFieldList;//所有分类
					var detailValues = customModularList[i].detailValues;//所有值
					menuList[customModularList[i].businessCode] = customModularList[i]
					menuList[name] = customModularList[i].businessCode;
					var mtl = "<li state=" + customModularList[i].businessCode + ">" + customModularList[i].customModularName.split("（")[0] + "</li>";
					var mtlDiv = '<div class="layui-tab-item" id="' + customModularList[i].businessCode + '"  ><div  id="' + customModularList[i].businessCode + 'Div" ></div></div>';
					//菜单只要有数据的分类hwx 2023-8-15 修改显示所有
//					if (JSON.stringify(detailValues) != "{}") {
					menuHtml = menuHtml + mtl;
					divHtml = divHtml + mtlDiv;
//					}
				}
				$("#tabView").prepend(menuHtml);
				$("#contentID").prepend(divHtml);
			}
		});
	},
	//获取表单分类组装成表格
	getDataTypeToTabel : function(customModular) {
		var customFieldList = customModular.customFieldList;//所有分类
		var detailValues = customModular.detailValues;//所有值
		//根据下标获取多少个分类
		var arr = [];
		$.each(detailValues, function(index, element) {
			//object为需要被遍历的对象
			//里面的函数有2个参数：index是每个元素的索引号；element遍历内容
			var name = index;
			//hwx 2023年10月31日下午2:48:14 增加判断是否存在-0
			var seq = "0";
			if(index.indexOf("-")>-1){
				seq = name.substring(index.indexOf("-") + 1, name.length);
			}
			arr.push(seq);
		});
		
		var maxValueInArray = Math.max.apply(Math, arr);
		var tableHtml = '<table style="border:0px solid red;width:100%;align:center;" id="">';
		if (JSON.stringify(customModular.detailValues) == "{}") {//判断是否有数据 hwx 2023-8-15 修改显示所有
//			tableHtml = tableHtml + '<tr><td class="tdLeft skin-div-css" colspan="6">无数据</td></tr>';
			var nums = -1;//记录要显示的字段数，用以控制换行，因为部分字段不需要显示。
			var tdnums = 0;
			for (var j = 0; j < customFieldList.length; j++) {
				if (customFieldList[j] != undefined && customFieldList[j].businessCode != assemblys.top.mdms.mdmsConstant.PROFILEPHOTOCODE) {
					var trHtml = "";
					var optionValue = customFieldList[j].optionValue;
					var customFieldCode = customFieldList[j].customFieldCode;//字段编码
					var fieldData = customFieldList[j].fieldData;
					var customFieldSet = customFieldList[j].customFieldSet;
					
					if (customFieldSet != "textarea") {
						nums = nums + 1;
					}
					
					if (nums % 3 == 0 || customFieldSet == "textarea") {
						trHtml = trHtml + "<tr>";
					}
					trHtml = trHtml + '<td class="tdLeft skin-div-css">' + customFieldList[j].customFieldName + '</td>';
					trHtml = trHtml + '<td class="tdRight"';
					trHtml += newDoctorInfo.colspanTd(customFieldSet, trHtml, nums);

					if (customFieldList[j].customFieldName == "活动类型") {
						trHtml = trHtml + ' colspan="5"';
					}
					trHtml = trHtml + ' ></td>';
					trHtml += newDoctorInfo.endTr(customFieldSet, trHtml, nums);
					tableHtml = tableHtml + trHtml;
				}
			}
		} else {
			for (var q = 0; q <= maxValueInArray; q++) {
				if (maxValueInArray > 0) {
					tableHtml = tableHtml + '<tr><td class="tdLeft skin-div-css" colspan="6">' + customModular.customModularName + '' + (q + 1) + '</td></tr>';
				}
				var nums = -1;//记录要显示的字段数，用以控制换行，因为部分字段不需要显示。
				var tdnums = 0;
				for (var j = 0; j < customFieldList.length; j++) {//遍历所有字段
//					if (!detailValues[customFieldList[j].customFieldCode + "-0"]) {
//						continue;
//					}
					if (customFieldList[j] != undefined && customFieldList[j].businessCode != assemblys.top.mdms.mdmsConstant.PROFILEPHOTOCODE) {
						
						var trHtml = "";
						var optionValue = customFieldList[j].optionValue;
						var customFieldCode = customFieldList[j].customFieldCode;//字段编码
						var fieldData = customFieldList[j].fieldData;
						var customFieldSet = customFieldList[j].customFieldSet;
						var val = detailValues[customFieldCode + "-" + q];
						//hwx 2023年10月27日下午5:05:05 处理出生日期
						if(customFieldList[j].businessCode == assemblys.top.mdms.mdmsConstant.BIRDHDAY){
							val = assemblys.dateToStr(val,"yyyy-MM-dd");
						}
						// <AUTHOR> @date 2023-08-17 16:53:32 @description 处理执业范围重复显示问题
						if (val == "" || val == undefined) {
							continue;
						}

						if (customFieldSet != "textarea") {
							nums = nums + 1;
						}
						
						if (nums % 3 == 0 || customFieldSet == "textarea") {
							trHtml = trHtml + "<tr>";
						}
						trHtml = trHtml + '<td class="tdLeft skin-div-css">' + customFieldList[j].customFieldName + '</td>';
						trHtml = trHtml + '<td class="tdRight"';
						trHtml += newDoctorInfo.colspanTd(customFieldSet, trHtml, nums);

						if (customFieldList[j].customFieldName == "活动类型") {
							trHtml = trHtml + ' colspan="5"';
						}


						
						if(customFieldSet=="file" && val!=""){
							newDoctorInfo.getFieldByCustomField(customFieldCode + "_" + q).then(function(data3) {
								//hwx 2024年5月6日下午6:55:14 增加对档案的附件进行处理
								if(data3 && data3.data && data3.data.fileList && data3.data.fileList!="{}" && data3.data.fileList!="[]"){
									var fileList=data3.data.fileList;
									var baseImgPath=data3.data.baseImgPath;
									if(fileList.length>0){
										var fileHtml="";
										for (var g = 0; g < fileList.length; g++) {
											var attachmentType=fileList[g].AttachmentType;
											var attachmentURL=fileList[g].AttachmentURL;
											var attachmentName=fileList[g].AttachmentName;
											var url=baseImgPath+attachmentURL;
											if(attachmentType.toUpperCase()=="PNG" || attachmentType.toUpperCase()=="MPEG" || attachmentType.toUpperCase()=="GIF" ){
												var attaUrl = basePath + "frame/fileUpload/downloadFile.spring?eifAttaUrl=" + attachmentURL + "";
												fileHtml += '<img src="' + url + '" style="width:50px;height:50px;margin-right:10px;cursor: pointer;"  onclick="pubUploader.preview(\'' + attachmentName + '\',\'' +attaUrl + '\')"/>&nbsp;&nbsp;';
											}else if(attachmentType.toUpperCase()=="PDF"){
												var pdfList=fileList[g].pdfList;
												for (var k = 0; k < pdfList.length; k++) {
													fileHtml += '<img src="' + pdfList[k].base64Image + '" style="width:50px;height:50px;margin-right:10px;cursor: pointer;"  onclick="newDoctorInfo.showPic(this)"/>&nbsp;&nbsp;';
												}
											}else{
												fileHtml=fileHtml+'<a style="margin-left:5px;cursor: pointer;color:blue;" class="cattachqueue-remove" onclick="pubUploader.downLoadAttaPreview(\''+attachmentName+'\',\''+attachmentURL+'\');">'+attachmentName+'</a>&nbsp;&nbsp;';
											}
										}
										val=fileHtml;
									}
								}
							});
						}
						
						if(customFieldSet=="file"){//附件不要处理值
							trHtml = trHtml + ' style="height:85px;" >' + val + '</td>';
						}else{
							trHtml = trHtml + ' >' + assemblys.htmlEncode(val) + '</td>';
						} 
						
						trHtml += newDoctorInfo.endTr(customFieldSet, trHtml, nums);

						tableHtml = tableHtml + trHtml;

					}
					
				}
			}
		}
		tableHtml = tableHtml + '</table>';
		
		return tableHtml;
	},
	//如果是文本域合并单元格
	colspanTd : function(customFieldSet, trHtml, nums) {
		
		if (customFieldSet == "textarea" || customFieldSet == "checkbox" || customFieldSet == "file") {
			if (customFieldSet == "file") {
				return trHtml = ' colspan="5"';
			} else {
				return trHtml = ' colspan="5" style="text-align: left;text-indent: 2em;padding: 15px;"';
			}
			
		} else {
			return "";
		}
		
	},
	//如果是文本域合并单元格且换行
	endTr : function(customFieldSet, trHtml, nums) {
		if (customFieldSet == "textarea" || customFieldSet == "checkbox" || (nums + 1) % 3 == 0) {
			return trHtml = ' </tr>';
		} else {
			return "";
		}
	},
	getDetailParam : function() {
		return $.ajax({
			url : basePath + "/mdms/mdmsCommon/getDetailParam.spring",
			dataType : "json",
			type : "post",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"funCode" : param.get("funCode")
			}
		});
	},
	showMenu : function() {
		newDoctorInfo.getAllCustomFieldList().then(function() {
			//获取子页面
			var body = layer.getChildFrame('body', 0);
			//hwx 2023-7-19 处理锚点定位点击其他区域消失
			body.click(function(e) {
				if (body.find("#dirDiv").length > 0) {
					body.find("#dirDiv").hide();
				}
			});
			if (body.find("#dirDiv").length > 0) {
				body.find("#dirDiv").toggle();
			} else {
				var html = "";
				html = html + '<div id="dirDiv" class="layui-layer layui-layer-page layui-layer-dir" style="margin-left: 120px; margin-top: -110px;position:absolute;z-index:99999;overflow-y: auto;height:375px;">';
				html = html + '<div id="dirDivSon" class="layui-layer-content" style="padding: 10px;">';
				html = html + '<ul class="" style="display: block;">';
				for (var i = 0; i < moduleList.length; i++) {
					html = html + '<li style="">' + (i + 1) + '、<a style="text-decoration:underline" href="#' + moduleList[i].BusinessCode + '"><cite>' + moduleList[i].CustomModularName + '</cite></a></li>';
				}
				html = html + '</ul></div></div>';
				body.find("#customFormName").append(html);
			}
			return;
		});
		
	},
	getAllCustomFieldList : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "mdms/mdmsCommon/getCustomFormFieldAndModule.spring",
			type : "get",
			data : {
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
				"businessCode" : assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSDAGL
			},
			dataType : "json",
			// skipDataCheck : true, - 如果接口响应回来的数据有问题，请增加该参数
			success : function(data) {
				// 结果
				moduleList = data.moduleList;
				return data;
			}
		});
		
	},
	//考核管理
	showAllExam : function(contentID) {
		
		var paramStr = "?funCode=" + param.get("funCode") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&compNo=" + param.get("compNo") + "";
		var dataList = []
		if (param.get("routineCheckView") == 'true') {
			dataList.push({
				name : "医师定期考核情况",
				id : "module5",
				children : [ {
					"tagName" : "div",
					"style" : {
						"height" : "auto",
					},
					"id" : "routineCheck",
					"class" : "layui-layer-content",
					"children" : [ {
						"tagName" : "div",
						"style" : {
							"padding" : "10px",
						},
						"id" : "routineCheckFrame"
					} ]
				} ]
			});
		}
		if (param.get("baseExamView") == 'true') {
			dataList.push({
				name : "三基培训考核",
				id : "module6",
				children : [ {
					"tagName" : "div",
					"style" : {
						"height" : "auto",
					},
					"id" : "baseCheck",
					"class" : "layui-layer-content",
					"children" : [ {
						"tagName" : "div",
						"style" : {
							"padding" : "10px",
						},
						"id" : "baseCheckFrame"
					} ]
				} ]
			});
		}
		if (param.get("personExamView") == 'true') {
			dataList.push({
				name : "临床类人员知识及技能培训考核情况",
				id : "module7",
				children : [ {
					"tagName" : "div",
					"style" : {
						"height" : "auto",
					},
					"id" : "personCheck",
					"class" : "layui-layer-content",
					"children" : [ {
						"tagName" : "div",
						"style" : {
							"padding" : "10px",
						},
						"id" : "personCheckFrame"
					} ]
				} ]
			});
		}
		// 查数据
		initCustomDetail.initModuleList("#" + contentID, dataList);
		$("#" + contentID).find("ul:eq(0)").css("height", newDoctorInfo.returnHeight(10));
	},
	showBtn : function() {
		$("#btnEdit").parent().removeClass("layui-hide");
	},
	showGroup : function() {
		var url = basePath + "mdms/functionModule/medicalTechnologyTeam/medicalTechnologyMembersList.html?funCode=" + assemblys.top.mdms.mdmsConstant.MEDICAL_TECHNOLOGY_TEAM;
		assemblys.top.addTab(null, '医疗技术小组', url);
	},
	//hwx 2022-11-11 增加获取不良事件开关字典
	getEvent : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.EVENTREPORTSWITCH,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
			},
			dataType : "json",
			skipDataCheck : true,
			async : false,
			success : function(data) {
				if (data.dictList) {
					var showDict = [];
					for (var i = 0; i < data.dictList.length; i++) {
						var ditCode = data.dictList[i].dictCode;
						if (ditCode == assemblys.top.mdms.mdmsConstant.FTNEVENTURL) {
							showDict.push(ditCode);
						}
						if (ditCode == assemblys.top.mdms.mdmsConstant.OTHERURL) {
							showDict.push(ditCode);
						}
					}
					if (showDict.length > 0) {
						if (showDict.length > 1) {
							assemblys.alert("配置参数错误");
							$("li[state='adverseEvents']").addClass("layui-hide");
						} else {
							$("li[state='adverseEvents']").removeClass("layui-hide");
						}
					} else {
						$("li[state='adverseEvents']").addClass("layui-hide");
					}
				} else {
					$("li[state='adverseEvents']").addClass("layui-hide");
				}
			}
		});
	},
	//hwx 2022-11-11 增加获取投诉纠纷开关字典
	getCdms : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.COMPLAINTSWITCH,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
			},
			dataType : "json",
			skipDataCheck : true,
			async : false,
			success : function(data) {
				if (data.dictList) {
					if (data.dictList.length > 1) {
						assemblys.alert("配置参数错误");
						$("li[state='complainingDispute']").addClass("layui-hide");
					} else {
						$("li[state='complainingDispute']").removeClass("layui-hide");
					}
				} else {
					$("li[state='complainingDispute']").addClass("layui-hide");
				}
			}
		});
	},
	
	//导出
	exportOut : function() {
		$(".layui-table img").css("height", "61px");
		$("td[title='工号']").parent().before($("td[title='头像']").parent());
		var data = newDoctorInfo.tableToData($("div[class='docName']").text() + "档案信息", "", "", "formDetail", "申请信息");
//		var deptExchangeList = newDoctorInfo.tableToData("轮转信息", "", "", "rotationRecordDiv", "轮转信息");
		var rewardPunishObj = newDoctorInfo.tableToData("奖励情况", "", "", "rewardPunishDiv", "奖励情况");
//		var behaviorRecordObj = newDoctorInfo.tableToData("医疗安全行为记录", "", "", "behaviorRecordDiv", "医疗安全行为记录");
//		var majorsAccidentObj = newDoctorInfo.tableToData("重大差错及事故处理", "", "", "majorsAccidentDiv", "重大差错及事故处理");
//		var routineCheckObj = newDoctorInfo.tableToData("医师定期考核", "", "", "routineCheckFrame", "医师定期考核");
//		var baseCheckObj = newDoctorInfo.tableToData("三基培训考核", "", "", "baseCheckFrame", "三基培训考核");
//		var personCheckObj = newDoctorInfo.tableToData("临床类人员知识技能培训考核情况", "", "", "personCheckFrame", "临床类人员知识技能培训考核情况");
		
		var myNewProject = {};
		myNewProject.bigTitle = '新技术新项目'
		myNewProject.show = 1;
		myNewProject.type = 'list';
		
//		var dosemonitoring = {};
//		dosemonitoring.bigTitle = '个人剂量检测';
//		dosemonitoring.show = 1;
//		dosemonitoring.type = 'list';
		
//		var personHealthObj = {};
//		personHealthObj.bigTitle = '健康情况';
//		personHealthObj.show = 1;
//		personHealthObj.type = 'list';
		
		var medicalActivitiesObj = {};
		medicalActivitiesObj.bigTitle = '医疗活动';
		medicalActivitiesObj.show = 1;
		medicalActivitiesObj.type = 'list';
		
		var evaluationStatusObj = {};
		evaluationStatusObj.bigTitle = '考评情况';
		evaluationStatusObj.show = 1;
		evaluationStatusObj.type = 'list';
		
		var operationRecordObj = {};
		operationRecordObj.bigTitle = '操作日志';
		operationRecordObj.show = 1;
		operationRecordObj.type = 'list';
		
		var doctorApproveObj = {};
		doctorApproveObj.bigTitle = '档案审批';
		doctorApproveObj.show = 1;
		doctorApproveObj.type = 'list';
		
		var operationRightObj = {};
		operationRightObj.bigTitle = '手术授权';
		operationRightObj.show = 1;
		operationRightObj.type = 'list';
		operationRightObj.data = operation;
		
//		var checkRoomRightObj = {};
//		checkRoomRightObj.bigTitle = '查房授权';
//		checkRoomRightObj.show = 1;
//		checkRoomRightObj.type = 'list';
//		checkRoomRightObj.data = room;
		
		var amaesRightObj = {};
		amaesRightObj.bigTitle = '麻醉授权';
		amaesRightObj.show = 1;
		amaesRightObj.type = 'list';
		amaesRightObj.data = anaes;
		
		var writeRightObj = {};
		writeRightObj.bigTitle = '处方授权';
		writeRightObj.show = 1;
		writeRightObj.type = 'list';
		writeRightObj.data = write;
		
		var JBZZLObj = {};
		JBZZLObj.bigTitle = '医师证件';
		JBZZLObj.show = 1;
		JBZZLObj.type = 'list';
		
		// <AUTHOR> @date 2023-07-31 16:42:58 @description 医师负面
		var dns = doctorNegative.getCols().mappingArr;
		var aes = doctorNegative.getCols().aeArr;
		var cds = doctorNegative.getCols().cdArr;
		
		var doctorNegativeObj = {};
		var aeObj = {};
		var aeObj = {};
		var cdObj = {};
		var bigTitle = "医师负面";
		var show = 1;
		var type = "list";
		doctorNegativeObj.bigTitle = bigTitle;
		doctorNegativeObj.show = show;
		doctorNegativeObj.type = type;
		doctorNegativeObj.data = [ dns ];
		aeObj.bigTitle = "不良事件";
		aeObj.show = show;
		aeObj.type = type;
		aeObj.data = [ aes ];
		cdObj.bigTitle = "投诉纠纷";
		cdObj.show = show;
		cdObj.type = type;
		cdObj.data = [ cds ];
		
		//获取证件类型的列表数据
		var ZYJSZCLdata = newDoctorInfo.getTableDate($('#ZYJSZCL').next().find("div[class='layui-table-header']").children(), $('#ZYJSZCL').next().find("div[class='layui-table-body layui-table-main']").children());
		var XLXWLdata = newDoctorInfo.getTableDate($('#XLXWL').next().find("div[class='layui-table-header']").children(), $('#XLXWL').next().find("div[class='layui-table-body layui-table-main']").children());
		var WFLdata = newDoctorInfo.getTableDate($('#WFL').next().find("div[class='layui-table-header']").children(), $('#WFL').next().find("div[class='layui-table-body layui-table-main']").children());
		
		myNewProject.data = newDoctorInfo.getTableDate($('#myNewProjectAndTechnology').contents().find("div[class='layui-table-header']").children(), $('#myNewProjectAndTechnology').contents().find("div[class='layui-table-body layui-table-main']").children());
//		dosemonitoring.data = newDoctorInfo.getTableDate($('#dosemonitoringFrame').contents().find("div[class='layui-table-header']").children(), $('#dosemonitoringFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
//		personHealthObj.data = newDoctorInfo.getTableDate($('#personalhealthFrame').contents().find("div[class='layui-table-header']").children(), $('#personalhealthFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
		medicalActivitiesObj.data = newDoctorInfo.getTableDate($('#medicalActivitiesFrame').contents().find("div[class='layui-table-header']").children(), $('#medicalActivitiesFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
		evaluationStatusObj.data = newDoctorInfo.getTableDate($('#evaluationStatusDiv').children(), $('#evaluationStatusDiv').children());
		//医疗活动
		//medicalActivitiesObj.data = otherFormDetail.getDate($('#medicalActivitiesFrame').contents().find("div[class='layui-table-header']").children(), $('#medicalActivitiesFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
		operationRecordObj.data = newDoctorInfo.getTableDate($('#operationRecordDiv').next().children().find("div[class='layui-table-header']").children(), $('#operationRecordDiv').next().children().find("div[class='layui-table-body layui-table-main']").children());
		//operationRightObj.data = newDoctorInfo.getTableDate($('#technicalAuthorizationFrame').contents().find("div[class='layui-table-header']").children(), $('#technicalAuthorizationFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
		doctorApproveObj.data = newDoctorInfo.getTableDate($('#doctorApproveDiv').children("div:eq(1)").children("div:eq(1)").children("div:eq(0)").children().find("div[class='layui-table-header']").children(), $('#doctorApproveDiv').children("div:eq(1)").children("div:eq(1)").children("div:eq(0)").children().find("div[class='layui-table-body layui-table-main']").children());
		$('#technicalAuthorizationFrame').contents().find("li[tabstate='1']").click();
		//writeRightObj.data = newDoctorInfo.getTableDate($('#technicalAuthorizationFrame').contents().find("div[class='layui-table-header']").children(), $('#technicalAuthorizationFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
		
		$('#technicalAuthorizationFrame').contents().find("li[tabstate='2']").click();
		//amaesRightObj.data = newDoctorInfo.getTableDate($('#technicalAuthorizationFrame').contents().find("div[class='layui-table-header']").children(), $('#technicalAuthorizationFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
		$('#technicalAuthorizationFrame').contents().find("li[tabstate='3']").click();
		//checkRoomRightObj.data = newDoctorInfo.getTableDate($('#technicalAuthorizationFrame').contents().find("div[class='layui-table-header']").children(), $('#technicalAuthorizationFrame').contents().find("div[class='layui-table-body layui-table-main']").children());
		
		JBZZLObj.data = newDoctorInfo.getTableDate($('#JBZZL').next().find("div[class='layui-table-header']").children(), $('#JBZZL').next().find("div[class='layui-table-body layui-table-main']").children());
		//所有类型的证件列表数据合并到第一个类别,方法是多个数组合并
		JBZZLObj.data[1].push.apply(JBZZLObj.data[1], ZYJSZCLdata[1]);
		JBZZLObj.data[1].push.apply(JBZZLObj.data[1], XLXWLdata[1]);
		JBZZLObj.data[1].push.apply(JBZZLObj.data[1], WFLdata[1]);
		
		data.list.push(JBZZLObj);
		data.list.push(operationRightObj);
		data.list.push(writeRightObj);
		data.list.push(amaesRightObj);
//		data.list.push(checkRoomRightObj);
		
		data.list.push(myNewProject);
		if (medicalActivitiesObj.list) {
			data.list.push(medicalActivitiesObj.list[0]);
		}
		data.list.push(medicalActivitiesObj);
		//hwx 2023-7-28 新增导出考评情况
		data.list.push(evaluationStatusObj);
		
		//hwx 2022-12-26 增加判断非空
//		if (deptExchangeList.list) {
//			data.list.push(deptExchangeList.list[0]);
//		}
//		if (routineCheckObj.list) {
//			data.list.push(routineCheckObj.list[0]);
//		}
//		if (baseCheckObj.list) {
//			data.list.push(baseCheckObj.list[0]);
//		}
//		if (personCheckObj.list) {
//			data.list.push(personCheckObj.list[0]);
//		}
		if (rewardPunishObj.list) {
			data.list.push(rewardPunishObj.list[0]);
		}
//		if (behaviorRecordObj.list) {
//			data.list.push(behaviorRecordObj.list[0]);
//		}
//		if (majorsAccidentObj.list) {
//			data.list.push(majorsAccidentObj.list[0]);
//		}
		
//		data.list.push(dosemonitoring);
//		data.list.push(personHealthObj);
		// <AUTHOR> @date 2023-08-01 10:35:13 @description 医师负面
		var result = doctorNegative.getResult();
		if (doctorNegativeObj) {
			if (result.dnList) {
				var dnList = result.dnList;
				doctorNegativeObj.data.push(dnList);
			}
			
			data.list.push(doctorNegativeObj);
		}
		
		if (aeObj) {
			if (result.aeList) {
				var aeList = result.aeList;
				aeObj.data.push(aeList);
				data.list.push(aeObj);
			}
		}
		
		if (cdObj) {
			if (result.cdList) {
				var cdList = result.cdList;
				cdObj.data.push(cdList);
				data.list.push(cdObj);
			}
		}
		data.list.push(operationRecordObj);
		data.list.push(doctorApproveObj);
		
		var fileName = "";
		if ($(".docName").length > 0) {
			fileName += $(".docName").html() + "医师档案导出";
		} else {
			fileName += "个人医师档案导出";
		}
		commonExportUtil.exportWord({
			"data" : data,
			"tabName" : "预览效果",
			"fileName" : fileName
		});
	},
	// 处理导出数据
	tableToData : function(fileTitle, leftTitle, rightTitle, id, bigTitle) {
		var modulers = [];
		// 找出符合条件的模块
		var bigTitle = bigTitle;
		var $content = $("#" + id);
		if ($content.children().length == 0) {
			return true;
		}
		var $item = $content.find("li.layui-nav-item");
		if ($item.length > 0) {
			$item.each(function() {
				modulers.push({
					title : bigTitle,
					dom : $(this)
				});
			});
		} else {
			modulers.push({
				title : bigTitle,
				dom : $content
			});
		}
		//});
		
		// 动态识别数据
		if (modulers.length > 0) {
			var tempList = [];
			for (var i = 0; i < modulers.length; i++) {
				var temp = modulers[i];
				var bigTitle = temp.title;
				var $content = temp.dom;
				var type = $content.find("thead").length > 0 ? "list" : "table";
				// 数据集合
				var data = [];
				// 表格
				if (type == "table") {
					var $divs = $content.find("div.layui-colla-item");
					if ($divs.length > 0) {
						$divs.each(function() {
							var $h2 = $(this).find("h2:eq(0)").clone();
							$h2.find("i").remove();
							var title = $h2.text();
							var trs = [];
							$(this).find("table.layui-table tr.layui-table-tr").each(function() {
								var tds = [];
								$(this).children("td").each(function() {
									tds.push($.trim($(this).html()));
								});
								trs.push(tds);
							});
							data.push({
								title : title,
								list : trs
							});
						});
					} else if ($content.hasClass("layui-nav-item")) {
						$content.each(function() {
							var title = $(this).find(".main_table_title:eq(0)").text();
							var trs = [];
							$(this).find("table.layui-table tr.layui-table-tr").each(function() {
								var tds = [];
								var trFjIndex = 0;
								$(this).children("td").each(function(index) {
									//hwx 2024年5月24日下午2:23:21 去掉表单中的所有自定义附件组件信息
									var showHtml = $.trim($(this).html());
									if (showHtml.indexOf("em") !=-1) {
										var result ="";
										$(this).find("em").each(function() {
											result +=$(this).attr("title")+"</br>";
										});
										tds.push(result);
									}else{
										tds.push(showHtml);
									}
								});
								trs.push(tds);
							});
							data.push({
								title : title,
								list : trs
							});
						});
					}
				} else
				// 列表
				if (type == "list") {
					
					var title = "";
					var $title = $content.find(".main_table_title");
					if ($title.length > 0) {
						$title = $title.clone();
						$title.find("span").remove();
						title = $title.text();
					}
					
					var $table = $content.find("table");
					var heads = [];
					var hasFilterOpt = false;
					$table.find("thead th").each(function() {
						var text = $.trim($(this).text());
						if (text == "操作") {
							hasFilterOpt = true;
							return true;
						}
						heads.push(text);
					});
					var trs = [];
					$table.find("tbody tr.layui-table-tr").each(function() {
						var tds = [];
						$(this).children("td").each(function(tdIndex, td) {
							if (tdIndex == 0 && hasFilterOpt) {
								return true;
							}
							tds.push($.trim($(this).html()));
						});
						trs.push(tds);
					});
					data.push(heads);
					data.push(trs);
				}
				tempList.push({
					bigTitle : bigTitle,
					title : title,
					show : "1",
					type : type,
					data : data
				});
				
			}
			var newData = {
				title : fileTitle || "",
				leftTitle : leftTitle || "",
				rightTitle : rightTitle || "",
				list : tempList
			}
		}
		
		return newData;
	},
	//初始化全部数据
	initList : function() {
		myNewProjectAndTechnologyList.initDiv("#newTechNiqueDiv", $("#userCode").text());
		newCertificateInfo.init("#certificateDiv");
		pubBizsysLog.initOptLogList("#operationRecordDiv");
		rotationRecord.deptExchangeList("#rotationRecordDiv");
		newDoctorInfo.showAllExam("excamManageDiv");
		otherFormDetail.getRoutineCheckList("routineCheckFrame");
		otherFormDetail.getBaseExamList("baseCheckFrame");
		otherFormDetail.getPersonCheckList("personCheckFrame");
		newRewardPunish.rewardPunishList("#rewardPunishDiv");
		technicalAuthorization.init("#technicalAuthorizationDiv");
		behaviorRecord.init("#behaviorRecordDiv");
		majorsAccident.init("#majorsAccidentDiv");
		doctorNegative.init("#doctorNegativeDiv");
		approval.render("#doctorApproveDiv");
		var html = '<iframe id="dosemonitoringFrame" src="" width="100%" height="' + newDoctorInfo.returnHeight(5) + '" frameborder="0"></iframe>';
		$("#dosemonitoringDiv").append(html);
		var url = basePath + "mdms/functionModule/dosemonitoring/dosemonitoringList.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&formStatus=" + param.get("formStatus");
		$("#dosemonitoringFrame").attr("src", url);
		
		//个人健康
		var html = '<iframe id="personalhealthFrame" src="" width="100%" height="' + newDoctorInfo.returnHeight(5) + '" frameborder="0"></iframe>';
		$("#personalhealthDiv").append(html);
		var url = basePath + "mdms/functionModule/personalHealth/personalHealthList.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&formStatus=" + param.get("formStatus");
		$("#personalhealthFrame").attr("src", url);
		newDoctorInfo.selectmedicalActivities();
		newDoctorInfo.selectEvaluationStatus("#evaluationStatusDiv");
		getCustomFormDetail.getCustomFormData({
			"appCode" : param.get("appCode"),
			"compNo" : param.get("compNo"),
			"customFormCode" : param.get("customFormCode"),
			"customFormFilledCode" : param.get("customFormFilledCode"),
			"customFormBusinessCode" : param.get("customFormBusinessCode"),
			"hasAtta" : "after",
			"dom" : "formDetail"
		});
	},
	getTableDate : function($table, $table2) {
		var data = [];
		var heads = [];
		
		var hasFilterOpt = false;
		var $thIndex = 0;
		var imgIndex = 0;
		
		$table.find("thead th").each(function(thIndex) {
			var text = $.trim($(this).text());
			if (text == "操作") {
				hasFilterOpt = true;
				$thIndex = thIndex;
				return true;
			}
			if (text == "编号") {
				hasFilterOpt = true;
				$thIndex = thIndex;
				return true;
			}
			//hwx 2024年5月24日下午2:18:21 导出医师档案时，将证件附件中的图片导出，其他格式不导出
			if (text.indexOf("附件")>-1) {
				imgIndex = thIndex;
			}
			heads.push(text);
		});
		var trs = [];
		$table2.find("tbody tr").each(function() {
			var tds = [];
			$(this).find("td").each(function(tdIndex, td) {
				if (tdIndex == $thIndex && hasFilterOpt) {
					return true;
				}
				if ($(this).attr("class")) {
					if ($(this).attr("class").indexOf("layui-hide") > -1) {
						return true;
					}
				}
				if(imgIndex = tdIndex && $(this).find("img")){
					var $imgTd= "";
					$(this).find("img").each(function(){
						var src = $(this).attr("src");
						if(src){
							$imgTd += '<img style="width:30px;height:30px;" src="'+src+'">';
						}
					});
					if($imgTd){
						tds.push($imgTd);
					}else if($.trim($(this).text())==""){
						tds.push("");
					}else{
						tds.push($.trim($(this).text()));
					}
				}else{
					tds.push($.trim($(this).text()));
				}
			});
			trs.push(tds);
		});
		
		data.push(heads);
		data.push(trs);
		
		return data;
	},
	showMyCer : function() {
		$(".certifiDiv").addClass("layui-hide");
		$(".rightmenu").css("display", "block");
		
		//加载右侧证件列表
		newRightCertificateInfo.init("#certificateBottom");
	},
	// 初始化表单，界面渲染
	initBtn : function(data) {
		//hwx 2023-1-10 增加个人信息中审批状态的显示
		var titles = "";
		var names = "";
		var titleClass = "";
		var isHide = "";
		if (data.status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_APPROVE) {//审核中
			titles = "档案资料审核中";
			names = "档案待审";
			titleClass = "showDS";
			isHide = "layui-hide";
		} else if (data.status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_FINISH) {//已审批通过
			titles = "档案资料审核通过";
			names = "档案已审";
			titleClass = "showYS";
		} else if (data.status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_READY_RETURN) {//回退
			titles = "档案资料回退";
			names = "档案回退";
			titleClass = "showHT";
			//hwx 2023-6-14 增加对回退申请人的档案隐藏完善资料按钮
			if (data.approvalBelongFlowNode && data.approvalBelongFlowNode.approvalIndex && data.approvalBelongFlowNode.approvalIndex != 0) {
				isHide = "layui-hide";
			}
		} else if (data.status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_DRAFT) {//档案草稿
			titles = "草稿";
			names = "草稿";
			titleClass = "showHT";
		}else if (status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_ZJDS) {
			titles = "我的证件审核中";
			names = "证件待审";
			titleClass = "showDS";
		} else if (status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_ZJHT) {
			titles = "我的证件回退";
			names = "证件回退";
			titleClass = "showHT";
		} else if (status == assemblys.top.mdms.mdmsConstant.CUSTOMFOM_STATUS_ZJYS) {
			titles = "我的证件审核通过";
			names = "证件已审";
			titleClass = "showYS";
		} else {
			isHide = "layui-hide";
		}
		var item = "";
		item += '<ul lay-unselect class="layui-nav operation_ul fr">';
		item += '	<li class="layui-nav-item operation_li">';
		item += '		<a title="' + titles + '" class="' + titleClass + '" id="showName">';
		item += '			' + names;
		item += '			<span class="layui-nav-more"></span>';
		item += '		</a>';
		item += '		<dl class="layui-nav-child layui-anim layui-anim-upbit">';
		item += '			<div>';
		item += '				<dd>';
		item += '					<a onclick="newDoctorInfo.showMyCer()" id="myCer">我的证件</a>';
		item += '				</dd>';
		item += '				<dd>';
		item += '					<a onclick="newDoctorInfo.exportOut()" >导出档案</a>';
		item += '				</dd>';
		item += '				<dd class="' + isHide + '">';
		item += '					<a onclick="newDoctorInfo.doctorEdit(2)" id="btnEdit" >完善资料</a>';
		item += '				</dd>';
		item += '			</div>';
		item += '		</dl>';
		item += '	</li>';
		item += '</ul>';
		$("#addBtn").append(item);
		layui.form.render();
		layui.element.render();
	},
	selectmedicalActivities : function() {//医疗活动
		var html = '<iframe id="medicalActivitiesFrame" src="" width="100%" height="' + newDoctorInfo.returnHeight(20) + '" frameborder="0"></iframe>';
		$("#medicalActivitiesDiv").append(html);
		//hwx 2023-6-26 处理医疗活动功能编号不取档案
		var url = basePath + "mdms/functionModule/mdmsCustomList/mdmsCustomList.html?preCustomFormfilledCode=" + param.get("customFormFilledCode") + "&findUserCode=" + $("#userCode").text() + "&isCreditManagement=1&funCode=" + assemblys.top.mdms.mdmsConstant.MYCREDITMANAGEMENT + "&customFormTypeCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_CODE + "&customFormTypeMenuCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_TYPE_MENU_CODE + "&appCode="
				+ assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME + "&type=" + assemblys.top.mdms.mdmsConstant.MEDICALACTIVITIES + "&onlyShow=1&status=0&showSetting=1";
		$("#medicalActivitiesFrame").attr("src", url);
	},
	selectEvaluationStatus : function(contentID) {//考评情况
		var userCode = $("#userCode").text();
		$.ajax({
			url : basePath + "mdms/doctorRecord/getDoctorRecordList.spring",
			type : "get",
			data : {
				"userCode" : userCode
			},
			dataType : "json",
			success : function(data) {
				var doctorRecordList = data.doctorRecordList;
				$.each(doctorRecordList, function(index, temp) {
					// 序号
					temp["index"] = index + 1;
				});
				var mapping = [  {
					name : "操作",
					width : "10%",
					opt : [ {
						"classModel" : "1",
						"className" : "layui-icon-search",
						"title" : "查看",
						"onclick" : function(data) {
							newDoctorInfo.toSearchVeify(data);
						}
					} ]
				},{
					name : "序号",
					width : "8%",
					value : "DictName",
					templet : function(data) {
						return data.index;
					}
				}, {
					name : "考评时间",
					width : "15%",
					value : "assessmentValidityEnd"
				}, {
					name : "考评名称",
					width : "22%",
					value : "assessmentName"
				}, {
					name : "满分",
					width : "15%",
					value : "assessmentSumScore"
				}, {
					name : '合格分',
					width : "15%",
					value : "assessmentPassScore"
				}, {
					name : "结果",
					width : "15%",
					value : "statusName",
					templet:function(d){
						var html = "";
						if (d.statusName=="不合格") {
							html += '<a  style="color:red;"  >不合格</a>';
						} else if (d.statusName=="合格") {
							html += '<a  style="color:RGB(199,174,109,109)"  >合格</a>';
						} else if (d.statusName=="良好") {
							html += '<a  style="color:aquamarine"  >良好</a>';
						} else {
							html += '<a  style="color:green"  >优秀</a>';
						}
						return html;
					}
				} ];
				// 渲染
				initCustomDetail.initTableList(contentID, mapping, doctorRecordList);
			}
		});
	},
	// 医师档案刷新持有权限
	holdAuthority : function() {
		$.ajax({
			url : basePath + "mdms/mdmsCommon/holdAuthority.spring",
			dataType : "json",
			data : {
				"doctorCustomFormFilledCode" : param.get("customFormFilledCode"),
			},
			success : function(data) {
				$("#shoushu").text(data.operationright);
				$("#mazui").text(data.anaesthesiaright);
				$("#chufang").text(data.writeright);
				$("#chafang").text(data.checkroomright);
				$("#yazhuanye").text(data.subProfessionRight);
			}
		});
	},
	//说明提醒
	explatinWarn : function() {
		$("i[name=ownerForm]").hover(function() {
			assemblys.tips($(this), "本项内容为医师资料", 100, "top");
		}, function() {
			layer.closeAll('tips');
		});
		$("i[name=owner]").hover(function() {
			assemblys.tips($(this), "本项内容由医师填写", 100, "top");
		}, function() {
			layer.closeAll('tips');
		});
		$("i[name=admins]").hover(function() {
			assemblys.tips($(this), "本项内容由医务科登记", 100, "top");
		}, function() {
			layer.closeAll('tips');
		});
		$("i[name=association]").hover(function() {
			assemblys.tips($(this), "本项内容为关联查看数据", 100, "top");
		}, function() {
			layer.closeAll('tips');
		});
		$("i[name=system]").hover(function() {
			assemblys.tips($(this), "本项内容为系统生成记录", 100, "top");
		}, function() {
			layer.closeAll('tips');
		});
	},
	strlen : function(str){
	    var len = 0;
	    for (var i=0; i<str.length; i++) { 
		    　　var c = str.charCodeAt(i); 
		    　　//单字节加1 
		    　　if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) { 
		       　　	len++; 
		    　　} else { 
		      　	　 	len+=2; 
		    　　}
	    } 
	    return len;
	},
	/**
	 *    滚动定位高度
	 * @title: getScrollHeight
	 * @description:
	 * @author: zh
	 * @version
	 * @date:  2023-08-17 10:48:28 10:48
	 * @param:
	 * @return:
	 * @throws
	 */
	scrollHeight: function (state) {
		var find = $("#tabView").find("li");
		var top = 0;
		find.each(function (i, o) {
			top += $(o).height();
			if ($(o).attr("state") == state) {
				return;
			}
		});
		$("#tabView").scrollTop(top);
		$("li[state=" + state + "]").trigger("click");
	},
	//hwx 2023年9月15日下午3:21:42 打开考评情况详情
	toSearchVeify:function(data){
		var url = basePath + "mdms/functionModule/doctorVirtueManage/doctorTemplate.html?funCode=" + param.get("funCode") + "&assessmentCode=" + data.assessmentCode + "&search=true&veify=true&UserName=" + encodeURI(data.writeAssessmentUserName).replace(/%5B/g, '[').replace(/%5D/g, ']') + "&assessPojectUserCode=" + data.writeAssessmentUserCode + "&assessmentUserCode=" + data.assessmentUserCode + "&checkedUserCode=" + data.checkedUserCode;
		layer.open({
			title : false,
			content : url,
			area : [ '95%', '95%' ],
			type : 2
		});
	},
	getFieldByCustomField : function(customFieldCode) {
		return $.ajax({
			url : basePath + "mdms/certificateManagement/getFieldByCustomFieldCode.spring",
			type : "get",
			dataType : "json",
			skipDataCheck : true,
			async : false,
			data : {
				customFieldCode : customFieldCode,
				customFormFilledCode : param.get("customFormFilledCode")
			},
			success : function(data) {
			}
		});
	},
	showPic : function(obj) {
		param.set("src", $(obj).attr("src"));
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "picId",
			area : [ '60%', '80%' ],
			title : false,
			scrollbar : false,
			content : "documentBrowsingShowPic.html?funCode=" + param.get("funCode")
		});
	}
}
