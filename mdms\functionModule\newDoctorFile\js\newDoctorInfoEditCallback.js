//上报完成后回调
var saveCallback = {
	init : function(win, cwind) {
		
		var saveState = cwind.saveState;
		var customFormFilledCode = cwind.customFormFilled.customFormFilledCode;
		var customFormFilledID = cwind.customFormFilled.customFormFilledID;
		var createUserCode = cwind.customFormFilled.createUserCode;
		var createUserName = cwind.customFormFilled.createUserName;
		var createDate = cwind.customFormFilled.createDate;
		var saveName = "保存成功";
		if (saveState == 1) {
			saveName = "提交成功";
		}

		if(saveState==0){//已完成保存后修改表单状态为草稿（实际是编辑）
			saveMdmsCustomFormFilled.updateStatus(customFormFilledCode,0);
		}else{//提交完成后修改审批记录流程节点编码
			saveMdmsCustomFormFilled.updateApproveRecord(customFormFilledCode);
		}
		//hwx 2024年2月2日下午5:14:01 优化获取功能编号
		var funCode = param.get("funCode");
		if(!funCode){
			funCode = parent.param.get("funCode");
		}
		if(funCode == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF || funCode == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || funCode == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES){
			assemblys.msg(saveName, function() {
				if (win.customFormTemplate.isAutoSave==undefined || win.customFormTemplate.isAutoSave == 0) {
					if (win.param.get("customFormBusinessCode") == assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTOR_NE_RE) {
						doctorNegative.reload();
					}else{
						window.location.reload();
					}
					//hwx 2023年10月24日上午10:13:28 根据是档案管理入口的回调
					if(funCode == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES){
							parent.window.location.reload();
							win.parent.assemblys.closeWindow();
					}else if(funCode == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF){
						//hwx 2024年6月6日下午4:18:12 工作台档案提交
						if(win.parent.param.get("isEditFile")==1){
							var $obj = win.parent.assemblys.top;
							//hwx 2024年6月6日下午5:20:32 关掉tab
							$obj.closeTab('完善档案'); 
							//hwx 2024年6月6日下午5:20:44 刷新工作台
							$obj.pubSystem.workDesk();
						}
					}else{
						win.assemblys.closeWindow();
					}
				} else {
					if (saveState == 0) {
						win.$("input[name='customFormFilledCode']").val(customFormFilledCode);
						win.$("input[name='customFormFilledID']").val(customFormFilledID);
						win.$("input[name='createUserCode']").val(createUserCode);
						win.$("input[name='createUserName']").val(createUserName);
						win.$("input[name='createDate']").val(createDate);
						win.$("input[name='status']").val("0");
						win.isSubmit = false;
						win.customFormTemplate.initSaveTimer();
					}
				}
			});
		}
		
	
		if((param.get("funCode")==assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF||param.get("funCode")==assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES)  && saveState==1){
			
			$.ajax({
				url : basePath + "mdms/mdmsCommon/saveLastCustomForm.spring",
				data : {
					"customFormFilledCode":win.$("input[name='customFormFilledCode']").val(),
					"savetype":4
				},
				type : "POST",
				dataType : "json",
				async : false,
				success : function(data){
				} 		
			});
			
		}

		// <AUTHOR> @date 2023-08-03 10:06:14 @description 医师负面申诉
		if (win.param.get("customFormBusinessCode") == assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTOR_NE_RE) {
			var id = win.param.get("doctorNegativeId");
			if (id) {
				saveDoctorNegative(id, customFormFilledCode, saveState);
			}
		}
		
	}

	
}

//保存前执行
var index = 0;
var hasPubUser=0;
var saveBeforeCallback = {
	saveBefore : function(win, saveState) {
		//hwx 2024年2月2日下午5:14:01 优化获取功能编号
		var funCode = param.get("funCode");
		if(!funCode){
			funCode = parent.param.get("funCode");
		}
		//20230315
		//提交后台判断流程是否存在，如果存在先删除
		//保存前先保存表单数据和后台判断流程是否存在，如果存在先删除
		//hwx 2023年10月24日上午9:43:23 档案管理中编辑时也要删除流程 增加科室概览的档案管理提交删除流程
		if((funCode==assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF||funCode==assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES||funCode==assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES)  && saveState==1){
			$.ajax({
				url : basePath + "mdms/mdmsCommon/saveLastCustomForm.spring",
				data : {
					"customFormFilledCode":win.$("input[name='customFormFilledCode']").val(),
					"savetype":3
				},
				type : "POST",
				dataType : "json",
				async : false,
				success : function(data){
					saveMdmsCustomFormFilled.deleteFormFloes(win.$("input[name='customFormFilledCode']").val());
				} 		
			});
		}
		var msg = "";
		//hwx 2024年2月29日下午3:35:29 档案保存与提交时判断是否存在
    	if(funCode == assemblys.top.mdms.mdmsConstant.MDMS_EMPLOYEE_TRIAL || funCode == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || funCode == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES){
    		var userCode =  win.$("input[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.YGGH+"']").val();
    		$.ajax({
    			url : basePath + "mdms/EmployeeTrailWork/findFileUserCode.spring?isExits=1&userCode=" + userCode+"&customFormFilledCode="+win.$("input[name='customFormFilledCode']").val(),
    			type : "get",
    			dataType : "json",
    			async : false,
    			success : function(data){
    				index  = data.user.length
    				hasPubUser  = data.hasPubUser
    			} 		
    		});
    		if(index > 0 ){
    			msg="该员工档案已经存在，请重新输入!";
    		}else {
    			if(hasPubUser==0 && saveState==1){//hwx 2024年2月29日下午4:01:30 提交时如果没有用户才执行新增用户
    				var userName = win.$("input[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.USERNAME+"']").val()
        			var deptID = win.$("input[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.USERDEPT+"']").val()
        			var identityClass = win.$("select[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.IDENTITYCLASS+"']").children("option[option-selected='true']").text()
        			var education = win.$("select[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.USEREDUCATION+"']").children("option[option-selected='true']").text()
        			var Tel = win.$("input[customfieldbusinesscode='phone']").val()
        			var  email = win.$("input[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.EMAIL+"']").val()
        			var entryDate =  win.$("input[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.ENTRYDATE+"']").val()
        			var currentDuty = win.$("select[customfieldbusinesscode='"+assemblys.top.mdms.mdmsConstant.USEREMPLOYMENT+"']").children("option[option-selected='true']").text()
        			var compNo = param.get("compNo")
        			$.ajax({
	        			url : basePath + "mdms/EmployeeTrailWork/saveUser.spring",
	        			data : {
	        				userCode,
	        				userName,
	        				deptID,
	        				identityClass,
	        				education,
	        				Tel,
	        				email,
	        				entryDate,
	        				currentDuty,
	        				compON:compNo
	        			},
	        			type : "POST",
	        			dataType : "json",
	        			async : false,
	        			success : function(data){
	        				
	        			} 		
	        		});
    			}
    			msg = ""
    		}
    	}
    	return msg;
	}

}

var initObj = {
	//初始化加载完成后
	init : function(win) {
		if (win.param.get("customFormBusinessCode") == assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YSDAGL) {
			changeForm.changeStyle(win);
			//点击新增后的表格处理
			win.$("td[class='addCommoncustomModularTd']").click(function(){
				setTimeout(function (){setTd(win)},100);
			})
			//hwx 处理出生日期、年龄
			win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERIDENTITY + "']").keyup(function(){
				var vals = $(this).val();
				if(vals.length >= 14){//身份证号输入大于14位时
					setObjectValue(1,win,vals);
				}else{
					setObjectValue(1,win,"");
				}
			});
			//hwx 现任职称取得年限
			var dateObject = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.GETTITLECERTIFICATEDATE + "']");
			var timer = setInterval(function(){
				var vals = dateObject.val();
				if(vals){
					setObjectValue(2,win,vals);
				}
			},500);
		}
		customCommon.getCustomFormUserValue(win);
		//编辑时部分内容不可编辑
		doctorReadOnly(win);
		//控制提交和草稿按钮，审批完成时需要是提交回调，草稿不回调
		changeForm.initButton(win);
		
		// <AUTHOR> @date 2023-08-02 15:02:25 @description 医师负面申诉
		if (win.param.get("customFormBusinessCode") == assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTOR_NE_RE) {
			var cs = [
				assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYTYPE,
				assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYLEVEL,
				assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYDATE,
				assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYADRESS,
				assemblys.top.mdms.mdmsConstant.APPLYUSERCODE,
				assemblys.top.mdms.mdmsConstant.APPLYUSERNAME,
				assemblys.top.mdms.mdmsConstant.APPLYUSERDEPT,
				assemblys.top.mdms.mdmsConstant.APPLYUSERTEL,
				assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYDESC
			];
			var ob = {};
			ob[assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYTYPE] = win.param.get("negativeType");
			ob[assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYLEVEL] = win.param.get("natureEventName");
			ob[assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYDATE] = win.param.get("negativeDate");
			ob[assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYADRESS] = win.param.get("eventLocation");
			ob[assemblys.top.mdms.mdmsConstant.DOCTORNEGATIVITYDESC] = win.param.get("doctorNegativityDesc");

			for (var i = 0; i < cs.length; i++) {
				var csi = cs[i];
				if (ob[csi]) {
					win.$("input[customfieldbusinesscode='" + csi + "']").val(ob[csi]);
					win.$("textarea[customfieldbusinesscode='" + csi + "']").val(ob[csi]);
				}
				win.$("input[customfieldbusinesscode='" + csi + "']").prop("readOnly", true);

			}
			filledCustomFormUser(win);
			//hwx 2023年9月18日上午11:07:41 填写时申请人信息默认不显示
			win.$("div[custommodularbussinesscode='applyUserInfo']").addClass("layui-hide");
		}
	}
}
//hwx 2023-7-19 处理出生化输入组件值，设置其他组件值
function setObjectValue(tag,win,val){
	if(tag == 1 ){
		if(val){
			var birthdayStr =  val.substr(6,4) + "-"+val.substr(10,2) + "-"+val.substr(12,2);
			var birthdayStr1 =  val.substr(6,4) + "/"+val.substr(10,2) + "/"+val.substr(12,2);
			var birthDate = new Date(birthdayStr1);
			var ageStr = new Date().getFullYear()-birthDate.getFullYear();
			if(isNaN(ageStr) || ageStr<0 || ageStr>150){
				assemblys.msg("无效身份证，请正确输入");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.BIRDHDAY + "']").val("");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCTORAGE + "']").val("");
				return false;
			}
			win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.BIRDHDAY + "']").val(birthdayStr);
			win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.BIRDHDAY + "']").attr("readonly", "readonly");
			
			win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCTORAGE + "']").val(ageStr);
		}else{
			win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.BIRDHDAY + "']").val("");
			win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.BIRDHDAY + "']").attr("readonly", "readonly");
			win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCTORAGE + "']").val("");
		}
		
	}else if(tag == 2 ){
		var getdayStr =  val.replace(/-/g,"/");
		var getDate = new Date(getdayStr);
		var yearStr = new Date().getFullYear()-getDate.getFullYear();
		win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.GETTITLECERTIFICATEYEARS + "']").val(yearStr);
	}
}
function setTd(win){
	win.$("label[class='layui-form-label item_label item_label_hide']").each(function() {
		var titleName = "";
		if ($(this).children()) {
			titleName = $(this).text();
		} else {
			titleName = $(this).children().next().text()
		}
		$(this).parent().parent().attr("colspan", $(this).parent().parent().attr("colspan") - 1);
		$(this).parent().parent().before('<td style="text-align:left;vertical-align:middle;">' + $(this).html() + '</td>');
		$(this).addClass("layui-hide");
		
	});
}
//初始化后不可编辑的内容
function doctorReadOnly(win) {
	var yggh = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.YGGH + "']").val();
	var userName = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERNAME + "']").val();
	var userDept = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").val();
	//hwx 2024年1月26日上午11:25:38 增加对档案数据的判断
	if(yggh && userName && userDept){
		win.$("i[title='查询']").addClass("layui-hide");
		win.$("i[title='选择科室']").addClass("layui-hide");
		win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.YGGH + "']").attr("readonly", "readonly");
		win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERNAME + "']").attr("readonly", "readonly");
		//隐藏下拉单选加上文本显示达到不可编辑效果，暂时不用
		//hideAndNewDisplay(win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").parent(), win.parent.$("div[class='docDept']").text(), win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").parent().parent());
		//部门清除点击事件
		win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").removeAttr("onclick");
	}
	
}

function hideAndNewDisplay($hide, val, $parent) {
	$hide.addClass("layui-hide");
	$parent.append("<span class='layui-input input_item item1' readonly='readonly'>" + val + "</span>");
}

//获取用户信息
function getUserValue(win) {
	
	var userDeptName = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").val();
	var userCode = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.YGGH + "']").val();
	var sex = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCTORINFOSEX + "'][radio-checked='true']").attr("customoptionsetbusinessvalue");
	
	var phone = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.PHONE + "']").val();
	var email = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.EMAIL + "']").val();
	var isManager = win.$("select[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.EXECUTIVEOFFICER + "']").find("option:selected").text();
	var nowDuty = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.NOWDUTY + "']").val();
	var otherDuty = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.OTHERDUTY + "']").val();
	var entryDate = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.ENTRYDATE + "']").val();
	var birthDay = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.BIRDHDAY + "']").val();
	var certificateNo = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.CERTIFICATENO + "']").val();
	var firstCertificateTime = win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.FIRSTCERTIFICATETIME + "']").val();
	var identityClass = win.$("select[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.IDENTITYCLASS + "']").find("option[option-selected='true']").text();
	var education = win.$("select[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USEREDUCATION + "']").find("option:selected").text();
	var title = win.$("select[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERTITLE + "']").find("option[option-selected='true']").text();
	var executiveOfficer = win.$("select[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.EXECUTIVEOFFICER + "']").find("option:selected").text();
	
	var data = {
		"phone" : phone,
		"email" : email,
		"isManager" : isManager,
		"nowDuty" : nowDuty,
		"otherDuty" : otherDuty,
		"entryDate" : entryDate,
		"birthDay" : birthDay,
		"certificateNo" : certificateNo,
		"firstCertificateTime" : firstCertificateTime,
		"identityClass" : identityClass,
		"education" : education,
		"title" : title,
		"userCode" : userCode,
		"sex" : sex
	}
	return data;
}

//新增表单时填充用户信息
function filledCustomFormUser(win) {
	$.ajax({
		url : basePath + "mdms/mdmsCommon/getUserInfo.spring",
		dataType : "json",
		data : {},
		success : function(data) {
			var user = data.user;
			// <AUTHOR> @date 2023-08-03 11:17:54 @description 医师负面申诉
			if (win.param.get("customFormBusinessCode") == assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTOR_NE_RE) {
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.APPLYDATE + "']").val(data.curDate);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.APPLYUSERNAME + "']").val(user.userName);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.APPLYUSERNAME + "']").attr("readonly", "readonly");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.APPLYUSERCODE + "']").val(user.userCode);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.APPLYUSERCODE + "']").attr("readonly", "readonly");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCUSERDEPT + "']").addClass("layui-hide");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCUSERDEPT + "']").parent().find("i[class='layui-icon']").addClass("layui-hide");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCUSERDEPT + "']").parent().append("<span class='layui-input input_item item1'>" + user.deptShortName + "</span>");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.APPLYUSERDEPT + "']").val(user.deptShortName);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.APPLYUSERDEPT + "']").attr("readonly", "readonly");
			}else{
				//if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && param.get("customFormFilledCode") == "") {
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.YGGH + "']").val(user.userCode);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.YGGH + "']").attr("readonly", "readonly");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.YGGH + "']").parent().find("i[class='layui-icon2']").addClass("layui-hide");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERNAME + "']").val(user.userName);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERNAME + "']").attr("readonly", "readonly");
				
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").val(user.deptId + "/" + user.deptShortName);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").addClass("layui-hide");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").parent().find("i[class='layui-icon']").addClass("layui-hide");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.USERDEPT + "']").parent().append("<span class='layui-input input_item item1'>" + user.deptShortName + "</span>");

				//hwx 2023-2-14 初始化建档时不禁止对手机与邮箱只读
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.PHONE + "']").val(user.tel);
//				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.PHONE + "']").attr("readonly", "readonly");
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.EMAIL + "']").val(user.email);
//				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.EMAIL + "']").attr("readonly", "readonly");
				if (user.entryDate != undefined) {
					win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.ENTRYYEARS + "']").val(data.workYears);
					win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.ENTRYDATE + "']").val(user.entryDate ? user.entryDate.substring(0, 10) : "");
				}
				
				if (user.birthday != undefined) {
					win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.BIRDHDAY + "']").val(user.birthday.substring(0, 10));
					win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.DOCTORAGE + "']").val(data.docAge);
				}
				if (user.firstCertificateTime != undefined) {
					win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.FIRSTCERTIFICATETIME + "']").val(user.firstCertificateTime.substring(0, 10));
				}
				
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.OTHERDUTY + "']").val(user.otherDuty);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.NOWDUTY + "']").val(user.currentDuty);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.CERTIFICATENO + "']").val(user.certificateNo);
				win.$("input[customfieldbusinesscode='" + assemblys.top.mdms.mdmsConstant.ENTRYDATE + "']").attr("readonly", "readonly");
				sex = '男'
				if (user.sex == 1) {
					sex = "女";
				}
				if (user.sex == 2) {
					sex = "未知";
				}
				
				win.initCustomFormTemplate.setValue(win.$("input[optionremark='" + sex + "']").prop("name"), win.$("input[optionremark='" + sex + "']").val());
				
				initSelected(assemblys.top.mdms.mdmsConstant.IDENTITYCLASS, user.identityClass, true, win);
				
				var isManagerTitle="未知";
				var isManager=user.isManager;
				if(isManager==1){
					isManagerTitle="是";
				}else if(isManager==2){
					isManagerTitle="否";
				}
				initSelected(assemblys.top.mdms.mdmsConstant.EXECUTIVEOFFICER, isManagerTitle, true, win);
				
				initSelected(assemblys.top.mdms.mdmsConstant.USEREDUCATION, user.education, true, win);
				
				initSelected(assemblys.top.mdms.mdmsConstant.USERTITLE, user.title, true, win);
				//}
				win.$("input[name='compNo']").val(user.compNo);
			}
		}
	})

}

function initSelected(businessCode, val, isHide, win, seq) {
	var $select2 = win.$("select[customfieldbusinesscode='" + businessCode + "']");
	if (seq != "" && seq != undefined) {
		$select2 = win.$("select[customfieldbusinesscode='" + businessCode + "'][name$='" + seq + "']");
	}
	var $obj = win.$("dd[lay-content='" + val + "'");
	$select2.val($($obj).attr("lay-value")).change();
	var elem = $select2[0];
	if (elem != undefined) {
		var eventField = elem.eventField;
		var value = $select2.val();
		var index = elem.name.split("-")[1];
		var $dl = $select2.next().children("dl");
		var $dd = $dl.children("dd[lay-value='" + value + "']");
		$dl.prev().children("input").val($dd.text());
		$dd.addClass("layui-this").siblings().removeClass("layui-this");
		if (isHide) {
			if (seq != "") {
				win.$("select[customfieldbusinesscode='" + businessCode + "'][name$='" + seq + "']").parent().addClass("layui-hide");
				win.$("select[customfieldbusinesscode='" + businessCode + "'][name$='" + seq + "']").parent().parent().append("<span class='layui-input input_item item1'>" + val + "</span>");
			} else {
				win.$("select[customfieldbusinesscode='" + businessCode + "']").parent().addClass("layui-hide");
				win.$("select[customfieldbusinesscode='" + businessCode + "']").parent().parent().append("<span class='layui-input input_item item1'>" + val + "</span>");
			}
			
		}
	}
}
function setReadOnly(businessCode, val, seq, win) {
	win.$("input[customfieldbusinesscode='" + businessCode + "'][name$='" + seq + "']").attr("readonly", "readonly");
}

function saveDoctorNegative(id, customFilledCode, status) {
	return $.ajax({
		url: basePath + "mdms/doctorNegative/saveDoctorNegative.spring",
		type: "post",
		data: {
			"doctorNegativeId": id,
			"appealCustomFormFilledCode": customFilledCode,
			"status": status
		}
	});
}