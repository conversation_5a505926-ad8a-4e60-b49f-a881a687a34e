var newRewardPunish = {
	// 奖励情况
	rewardPunishList : function(contentID) {
		window.contentID = contentID;
		$(contentID).empty();
		$.ajax({
			url : basePath + "/mdms/rewardPunishManage/getRewardPunishList.spring",
			type : "get",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
				var list = data.rewardPunishList;
				
				$.each(list, function(index, temp) {
					// 序号
					temp["index"] = index + 1;
				});
				
				var length = list.length;
				var mapping = [ {
					name : "操作",
					width : "10%",
					opt : [ {
						"classModel" : "1",
						"className" : "layui-icon-search",
						"onclick" : function(data) {
							newRewardPunish.rewardPunishManageEdit(data.RewardPunishManageId, 1);
						}
					}, {
						"classModel" : "1",
						"className" : "layui-icon-edit",
						"show" : function(data) {
							if (((param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && userCode == data.OptUserCode) || param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES) && param.get("rewardPunishEdit") == 'true' && param.get("formStatus") != 1) {
								return true;
							} else {
								return false;
							}
						},
						"onclick" : function(data) {
							newRewardPunish.rewardPunishManageEdit(data.RewardPunishManageId, 0);
						}
					}, {
						"classModel" : "1",
						"className" : "layui-icon-delete",
						"show" : function(data) {
							if (((param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DOCTOR_INF && userCode == data.OptUserCode) || param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME_FILES || param.get("funCode") == assemblys.top.mdms.mdmsConstant.MDMS_DEPT_DOCTORFILES) && param.get("rewardPunishDel") == 'true' && param.get("formStatus") != 1) {
								return true;
								
							} else {
								return false;
							}
						},
						"onclick" : function(data) {
							newRewardPunish.rewardPunishManageDel(data.RewardPunishManageId);
						}
					} ]
				}, {
					name : "序号",
					width : "8%",
					value : "DictName",
					templet : function(data) {
						return data.index;
					}
				}, {
					name : "奖励类型",
					width : "20%",
					value : "DictName"
				}, {
					name : "奖励级别",
					width : "15%",
					value : "SortValue"
				}, {
					name : "获奖名称",
					width : "27%",
					value : "PromUnit"
				}, {
					name : "奖励时间",
					width : "20%",
					value : "PromTime"
				} ]
				// 渲染
				initCustomDetail.initTableList(contentID, mapping, list);
				if (param.get("formStatus") != 1 && param.get("rewardPunishAdd") == 'true') {
					$(contentID).children().before('<div style="float: right; margin-bottom: 5px;"><button type="button" class="layui-btn layui-btn-sm" onclick="newRewardPunish.addRewardPunishManage();">新增</button></div>');
				}
			}
		
		});
		
	},
	//奖励管理编辑
	rewardPunishManageEdit : function(rewardPunishManageId, showOrEdit) {
		var id = parseInt(rewardPunishManageId);
		var html = "rewardPunishManageEdit.html";
		if (showOrEdit == 1) {
			html = "rewardPunishManageView.html";
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "rewardPunishManageEdit",
			area : [ '900px', '60%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&rewardPunishManageId=" + rewardPunishManageId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
		});
	},
	
	//奖励管理删除
	rewardPunishManageDel : function(rewardPunishManageId) {
		layer.confirm("确定要删除吗？", function() {
			$.ajax({
				url : basePath + "/mdms/rewardPunishManage/deleteRewardPunishManage.spring",
				type : "post",
				data : {
					rewardPunishManageId : rewardPunishManageId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					newRewardPunish.rewardPunishList(window.contentID);
					
				});
				return data;
			});
		});
	},
	
	// 新增奖励记录
	addRewardPunishManage : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "rewardPunishManageEdit",
			area : [ '900px', '80%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/rewardPunishManageEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
}