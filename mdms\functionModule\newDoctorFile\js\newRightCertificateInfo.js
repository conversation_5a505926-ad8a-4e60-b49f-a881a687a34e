var newRightCertificateInfo = {
	iList : [],//存附件下标和内容
	init : function(divHtml) {
		$(divHtml).empty();
		newRightCertificateInfo.getCertificateInfo().then(function(data) {
			//动态创建右侧证件
			newRightCertificateInfo.createCerRightDiv(data.titleList, data.certificateClassList, divHtml);
			
			certificateClassList = newRightCertificateInfo.createCerName(data.certificateClassList);
			
			newRightCertificateInfo.initLayuiForm();
		});
		
	},
	
	initLayuiForm : function() {
		layui.form.render();
	},
	
	getCertificateInfo : function() {
		return $.ajax({
			url : basePath + "mdms/certificateManagement/getUserCertificateInfo.spring?customformfilledCode=" + param.get("customFormFilledCode") + "&businessCode=" + assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_DOCTORMANAGER,
			type : "get",
			data : {}
		}).then(function(data) {
			return data;
		});
	},
	
	createCerName : function(data) {
		for ( var key in data) {
			var cerClass = "rcContent_" + key;
			if (data[key].length > 0) {
				var html = '';
				for (var i = 0; i < data[key].length; i++) {
					html += '<div class="cerName"  name="cerFile_' + data[key][i].CertificateId + '" onclick="newRightCertificateInfo.toViewCer(this)" >' + data[key][i].DictName + '</div>'
				}
				$("." + cerClass).append(html);
			}
			
		}
	},
	
	toViewCer : function(obj) {
		var cerFiles = '';
		$(obj).attr("name", function(i, origValue) {
			cerFiles = origValue;
			return origValue;
		});
		var certificateId = cerFiles.substring(cerFiles.lastIndexOf("_") + 1, cerFiles.length);
		return $.ajax({
			url : basePath + "mdms/certificateManagement/getCertificateManagement.spring",
			data : {
				certificateId : certificateId
			}
		}).then(function(data) {
			if (data.fileList.length > 0) {
				newRightCertificateInfo.preview(data);
			} else {
				assemblys.msg("该证件暂无图片！");
			}
		});
		
	},
	
	preview : function(data2) {
		var data = data2.fileList
		var baseImgPath = data2.baseImgPath;
		var files = [];
		var n = 0; // 查看有没有pdf的附件
		for (var i = 0; i < data.length; i++) {
			var suffix = data[i].attaType.toUpperCase();
			
			if(suffix == "PDF"){
				newRightCertificateInfo.iList[i] = {
						"url" : baseImgPath + data[i].attaUrl,
						"type" : suffix
				};
				n++;
			}else if(suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF"){
				newRightCertificateInfo.iList[i] = {
						"imgName" : data[i].attaName,
						"src" : data[i].attaUrl,
						"type": suffix
				}
				
				var url = basePath + "/frame/fileUpload/downloadFile.spring?eifAttaUrl=" + data[i].attaUrl + "&eifAttaName=" + encodeURIComponent(data[i].attaName);
				
				files.push({
					"alt" : data[i].attaName,
					"src" : url
				});
			}
			
		}

		// 全是图片的使用图片层查看
		if(n == 0){
			layer.photos({
				photos : {
					"title" : "", //相册标题
					"data" : files
				}
			});
		}else{
			parent.layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				id : "toShowDocument",
				area : [ '70%', '90%' ],
				title : false,
				scrollbar : false,
				content : basePath + "mdms/functionModule/newDoctorFile/showPDF.html?list=" + encodeURIComponent(JSON.stringify(newRightCertificateInfo.iList)) 
			});
		}
	},
	
	createCerRightDiv : function(title, table, divHtml) {
		var newData = [];
		if (title.length == 0 || JSON.stringify(table) === "{}") {
			$("#certificateBottom").append('<div class="cerName">无相关数据</div>');
		} else {
			for (var i = 0; i < title.length; i++) {
				var html = {};
				
				for ( var key in table) {
					if (key == title[i].DictCode) {
						tableId.push(key);
						html = {
							"tagName" : "div",
							"className" : "rcContent_" + key,
							"children" : [ {
								"tagName" : "div",
								"className" : "rcTitle",
								"children" : [ {
									"tagName" : "img",
									"className" : "rtTitleImg",
									"attr" : {
										"src" : title[i].DictContent
									},
								}, {
									"tagName" : "span",
									"className" : "rtTitleName",
									"innerHTML" : title[i].DictName
								} ]
							} ]
						
						}
					}
				}
				newData.push(html);
			}
			
		}
		assemblys.createElement(newData, $(divHtml)[0]);
	}

}