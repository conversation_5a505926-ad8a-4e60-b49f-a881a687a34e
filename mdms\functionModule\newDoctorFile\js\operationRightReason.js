var operationRightReason = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		$("span[class='head1_text fw700']").text("手术授权");
		var isVaild = param.get("isValid")
		if (isVaild == "0") {
			$("#savebtn").val("暂停");
		}
		
		operationRightReason.initLayui();
		//hwx 2023年9月21日上午11:57:37 处理手术名称特殊字符
		param.set("operationName", parent.rightAddList.rightName);
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			operationRightReason.saveReason();
		});
	},
	saveReason : function() {
		//暂停 恢复
		if (param.get("isValid") == "0" || param.get("isValid") == "1") {
			operationRightReason.stopOrStarAR(param.get("operationRightId"), $(".showReason").val(), param.get("operationName"), param.get("customFormFilledCode"));
		} else {
			//回收
			operationRightReason.deleteAR(param.get("operationRightId"), $(".showReason").val(), param.get("operationName"), param.get("customFormFilledCode"));
		}
		
	},
	
	deleteAR : function(operationRightId, reason, operationName, customFormFilledCode) {
		layer.confirm("确定要回收吗？", function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/operationRight/updateOperationRighti.spring",
				type : "post",
				data : {
					operationRightId : operationRightId,
					reason : reason,
					operationName : operationName,
					customFormFilledCode : customFormFilledCode,
					type : 4
				}
			}).then(function(data) {
				if (data.res == "fail") {
					assemblys.msg(data.msg);
				} else {
					assemblys.msg("回收成功", function() {
						assemblys.closeWindow();
						parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
						parent.parent.newDoctorInfo.holdAuthority();
					});
				}
				window.isSubmit = false;
			});
		});
	},
	
	stopOrStarAR : function(operationRightId, reason, operationName, customFormFilledCode) {
		var reConfirm = "";
		var reMessage = "";
		if (param.get("isValid") == "0") {
			reConfirm = "确定要暂停吗？";
			reMessage = "暂停成功";
		}
		layer.confirm(reConfirm, function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/operationRight/saveOperationRighti.spring",
				type : "post",
				data : {
					operationRightId : operationRightId,
					reason : reason,
					operationName : operationName,
					customFormFilledCode : customFormFilledCode,
					type : 2,//
					isValid : param.get("isValid"),
					userCode : param.get("userCode"),
				}
			}).then(function(data) {
				assemblys.msg(reMessage, function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
					window.isSubmit = false;
				});
			});
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}