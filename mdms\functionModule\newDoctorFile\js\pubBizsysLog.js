var pubBizsysLog = {
	// 业务日志记录 - 来自【应用接口管理 - getBizLogList 接口】
	initOptLogList : function(contentID) {
		var mapping = {
			key : "bizLogList",
			cols : [ {
				title : "操作",
				align : "center",
				templet : function(d) {
					var html = "";
					if (d.signName == "表单修改") {
						html = '<i class="layui-icon layui-icon-search i_delete" title="查看" lay-event="showCustomFormLog"></i>';
					}
					return html;
				}
			}, {
				title : "操作类型",
				field : "signName",
				width : "10%",
				align : "center"
			}, {
				title : "日志内容",
				width : "60%",
				field : "logContent",
				templet : function(data) {
					return assemblys.htmlEncode(data.logContent);
				}
			}, {
				title : "操作人",
				width : "10%",
				field : "userName",
				align : "center",
			}, {
				title : "操作时间",
				width : "15%",
				field : "createDate",
				align : "center",
				templet : function(data) {
					return assemblys.dateToStr(data.createDate);
				}
			} ]
		};
		layui.use('table', function() {
			var table = layui.table;
			table.render({
				elem : contentID,
				url : basePath + "frame/pubBizsysLog/getBizLogList.spring",
				where : {
					"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME,
					"bizRelationCode" : param.get("customFormFilledCode")
				},
				cols : [ mapping.cols ],
				height : ($(contentID).parents(".layui-tab-item").height() - 15),
				width : ($(contentID).parents(".layui-tab-item").width()),
				limit : 20,
				page : { //支持传入 laypage 组件的所有参数（某些参数除外，如：jump/elem） - 详见文档
					layout : [ 'prev', 'page', 'next', 'limit', 'skip' ],
					groups : 1,
					first : false,
					last : false
				},
				parseData : function(res) {
					var dataList = res.bizLogList;
					// 分页
					var total = dataList.length; //请求的数据总数
					var list = []; //用来保存当前页显示的数据
					var page = $("div[id^='layui-table-page']").find(".layui-laypage-em").next().html();
					var limit = $("div[id^='layui-table-page']").find(".layui-laypage-limits select").val();
					if (page == undefined || page == null || page == "") {
						page = 1;
					}
					if (limit == undefined || limit == null || limit == "") {
						limit = 20;
					}
					//数据从哪条数据开始
					var start = (page - 1) * limit;
					//数据从哪条数据结束
					var end = page * limit;
					if (end > total) {
						end = total;
					}
					//取分页数据
					for (var i = start; i < end; i++) {
						list.push(dataList[i]);
					}
					return {
						"code" : 0,
						"msg" : "",
						"count" : total, //解析数据长度
						"data" : list
					}
				}
			});
		});
		layui.table.on("tool(" + window.$(contentID).attr("lay-filter") + ")", function(obj) {
			var fun = pubBizsysLog.showCustomFormLog;
			if (fun && typeof fun == "function") {
				fun(obj.data);
			}
		});
	},
	/**
	 * 显示表单修改日志
	 */
	showCustomFormLog : function(d) {
		var relationCode = d.signCode;
		$.ajax({
			url : basePath + "frame/newCustomForm/getCustomFormLogList.spring",
			type : "get",
			data : {
				"relationCode" : relationCode,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			},
			dataType : "json",
			success : function(data) {
				if (data.customFormLogList.length > 0) {
					pubBizsysLog.initTableListWin(data.customFormLogList);
				} else {
					assemblys.msg("暂无表单修改记录");
				}
			}
		});
	},
	initTableListWin : function(list) {
		// 打开窗口
		var id = "SHOW_NEW_LOG_WINDOW_" + new Date().getTime();
		layer.open({
			type : 1,
			skin : 'layui-layer-aems',
			title : "表单修改详情",
			scrollbar : false,
			area : [ "80%", "80%" ],
			content : "<div id='" + id + "' style='padding: 10px;'></div>"
		});
		
		var ths = [];
		var tbs = [];
		var mapping = [ {
			name : "修改字段",
			value : "customFieldName"
		}, {
			name : "修改前",
			value : "beforeValue",
			templet : function(data) {
				var showPoto = "";
				// 富文本
				if (data.isRichText == 1) {
					showPoto = "<font color='red'>" + data.beforeValue + "</font>";
				} else if (data.customFieldSet == "profile") {
					showPoto = "<img src='" + data.beforeValue + "' />";
				} else {
					showPoto = "<font color='red'>" + assemblys.htmlDecode(data.beforeValue) + "</font>";
				}
				return showPoto;
			}
		}, {
			name : "修改后",
			value : "afterValue",
			templet : function(data) {
				var showPoto = "";
				// 富文本
				if (data.isRichText == 1) {
					showPoto = "<font color='red'>" + data.afterValue + "</font>";
				} else if (data.customFieldSet == "profile") {
					showPoto = "<img src='" + data.afterValue + "' />";
				} else {
					showPoto = "<font color='red'>" + assemblys.htmlDecode(data.afterValue) + "</font>";
				}
				return showPoto;
			}
		}, {
			name : "修改人",
			value : "optUserName"
		}, {
			name : "修改时间",
			value : "optDate"
		} ];
		// 有数据
		if (list.length > 0) {
			for (var i = 0; i < list.length; i++) {
				var temp = list[i];
				var trs = {
					"tagName" : "tr",
					"children" : []
				};
				for (var j = 0; j < mapping.length; j++) {
					var map = mapping[j];
					// 头
					if (i == 0) {
						ths.push({
							"tagName" : "th",
							"style" : {
								"text-align" : "center"
							},
							"innerHTML" : map.name
						})
					}
					// 特殊处理
					var value = temp[map.value] || "";
					if ((typeof value === 'number') && value > 1000000000000) {
						value = assemblys.dateToStr(value);
					} else if (value instanceof Date) {
						value = assemblys.dateToStr(value.getTime());
					} else if (value instanceof Object && value.time) {
						value = assemblys.dateToStr(value.time);
					}
					// 操作按钮
					var opts = []
					var optList = map.opt || [];
					if (map.opt instanceof Array) {
						value = "";
						for (var w = 0; w < optList.length; w++) {
							var opt = optList[w];
							var hasShow = true;
							if (opt.show && typeof opt.show == 'function') {
								hasShow = opt.show(temp);
							}
							if (hasShow) {
								opts.push({
									"tagName" : "i",
									"className" : "layui-icon2 " + (opt.className || ""),
									"innerHTML" : opt.icon + " ",
									"attr" : {
										"row1" : i,
										"row2" : j,
										"index" : w
									},
									"onclick" : function() {
										var _optList = mapping[$(this).attr("row2")].opt;
										var _onclick = _optList[$(this).attr("index")].onclick;
										if (_onclick && typeof _onclick == 'function') {
											var _temp = list[$(this).attr("row1")];
											_onclick(_temp);
										}
									}
								})
							}
						}
					}
					// 自定义
					var templet = map.templet;
					if (templet && typeof templet == 'function') {
						value = map.templet(temp);
					}
					// 内容
					trs["children"].push({
						"tagName" : "td",
						"style" : (j == 0 ? {
							"text-align" : "center",
							"width" : "100px"
						} : {}),
						"innerHTML" : value,
						"children" : opts
					})
				}
				tbs.push(trs);
			}
		} else {
			var index = 0;
			// 无数据
			for (var j = 0; j < mapping.length; j++) {
				var map = mapping[j];
				ths.push({
					"tagName" : "th",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : map.name
				});
				index++;
			}
			tbs.push({
				"tagName" : "tr",
				"children" : [ {
					"tagName" : "td",
					"attr" : {
						"colspan" : index
					},
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : "无相关数据",
					"children" : []
				} ]
			})
		}
		// 组装
		var table = {
			"tagName" : "table",
			"className" : "layui-table",
			"children" : [ {
				"tagName" : "thead",
				"children" : [ {
					"tagName" : "tr",
					"children" : ths
				} ]
			}, {
				"tagName" : "tbody",
				"children" : tbs
			} ]
		}
		assemblys.createElement(table, $("#" + id)[0]);
		
		// 加载图片显示
		$("#" + id).find("img").on("click", function() {
			var url = $(this).attr("src");
			layer.photos({
				photos : {
					"title" : "", //相册标题
					"data" : [ { //相册包含的图片，数组格式
						"src" : url
					} ]
				}
			});
		});
		
	}
}