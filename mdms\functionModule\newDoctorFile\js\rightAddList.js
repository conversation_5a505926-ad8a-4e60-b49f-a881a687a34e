var rightAddList = {
	rightName : "",//hwx 2023-9-21 处理手术名称特殊字符
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]")).then(function() {
			rightAddList.getAnesthesiaClassPager(param.get("type"));
			rightAddList.initLayuiForm();
		});
	},
	initLayuiForm : function() {
		layui.form.render();
	},
	getAnesthesiaClassPager : function(type) {
		if (type == 99) {//手术
			var cols = [ {
				title : '操作',
				width : 80,
				align : "center",
				templet : function(d) {
					//d.hasRight --   0 未授权 2 申请中 3 已暂停 4 已授权
					var html = '';
					if (d.hasRight == '0' && d.status == 1) {
						html += '<i class="layui-icon2" title="授权" lay-event="toAddAnesthesiaClass">&#xe819;</i>';
					}
					if (d.hasRight == '3') {
						html += '<i class="layui-icon2" title="恢复" lay-event="toStartOrStopAnesthesiaClass">&#xeb98;</i>';
					}
					if (d.hasRight == '4') {
						html += '<i class="layui-icon2" title="暂停" lay-event="toStartOrStopAnesthesiaClass">&#xeb99;</i>&nbsp;&nbsp;';
						html += '<i class="layui-icon2" title="回收" lay-event="toCancelAnesthesiaClass">&#xe928;</i>';
					}
					return html;
				}
			}, {
				title : '手术编码',
				width : 130,
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.operationCode);
				}
			}, {
				title : '手术名称',
				width : 230,
				align : "center",
				templet : function(d) {
					return html = '<span style="color:#028bfd;cursor:pointer" onclick="rightAddList.toViewOperationClass(' + d.operationClassId + ')">' + d.operationName + '</span>'
				}
			}, {
				title : '手术等级',
				width : 100,
				align : "center",
				templet : function(d) {
					var html = '';
					var operationLevelName = d.operationLevelName;
					if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel1) {
						html += '<font style="color:#338100;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel2) {
						html += '<font style="color:#0056FF;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel3) {
						html += '<font style="color:#CC0000;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel4) {
						html += '<font style="color:#FF002B;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel5) {
						html += '<font style="color:orange" >';
					} else {
						html += '<font style="color:orange" >';
					}
					if (d.operationLevelName != undefined) {
						html += d.operationLevelName + '</font>';
					} else {
						html += '<font style="color:orange;" >暂无等级</font>';
					}
					return html;
				}
			}, {
				title : '手术类型',
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.operationTypeName);
				}
			}, {
				title : '状态',
				width : 90,
				align : "center",
				templet : function(d) {
					var statusName = "";
					if (d.hasRight == '0') {
						statusName = "<font color='red'>未授权</font>";
					}
					if (d.hasRight == '2') {
						statusName = "<font color='blue'>申请中</font>";
					}
					if (d.hasRight == '3') {
						statusName = "<font color='DarkGoldenRod'>已暂停</font>";
					}
					if (d.hasRight == '4') {
						statusName = "<font color='green'>已授权</font>"
					}
					return statusName;
				}
			} ];
			
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/deptOperation/getDoctorOperationPager.spring?userCode=" + param.get("docUserCode") + "&" + param.__form() + "&" + filterParam.__form() + "&operation_state=1",
				data : {
					"customFormFilledCode" : param.get("customFormFilledCode"),
					"userCode" : param.get("docUserCode")
				},
				cols : [ cols ],
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					$("#filterNum").text(count);
					
					layui.form.render();
				},
				events : {
					toAddAnesthesiaClass : rightAddList.toAddAnesthesiaClass,
					toCancelAnesthesiaClass : rightAddList.toCancelAnesthesiaClass,
					toStartOrStopAnesthesiaClass : rightAddList.toStartOrStopAnesthesiaClass,
				}
			});
		} else if (type == 1) {//处方
			var cols = [ {
				title : '操作',
				width : 80,
				align : "center",
				templet : function(d) {
					//d.hasRight --   0 未授权 2 申请中 3 已暂停 4 已授权
					var html = '';
					if (d.hasRight == '0' && d.status == 1) {
						html += '<i class="layui-icon2" title="授权" lay-event="toAddAnesthesiaClass">&#xe819;</i>';
					}
					if (d.hasRight == '3') {
						html += '<i class="layui-icon2" title="恢复" lay-event="toStartOrStopAnesthesiaClass">&#xeb98;</i>';
					}
					if (d.hasRight == '4') {
						html += '<i class="layui-icon2" title="暂停" lay-event="toStartOrStopAnesthesiaClass">&#xeb99;</i>&nbsp;&nbsp;';
						html += '<i class="layui-icon2" title="回收" lay-event="toCancelAnesthesiaClass">&#xe928;</i>';
					}
					return html;
				}
			}, {
				title : '处方分类',
				align : "center",
				minWidth : 80,
				templet : function(d) {
					return d.parentName;
				}
			}, {
				title : '处方名',
				align : "center",
				minWidth : 180,
				templet : function(d) {
					return d.rmosName;
				}
			}, {
				title : '注意事项',
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.remark);
				}
			
			}, {
				title : '状态',
				width : 90,
				align : "center",
				templet : function(d) {
					var statusName = "";
					if (d.hasRight == '0') {
						statusName = "<font color='red'>未授权</font>";
					}
					if (d.hasRight == '2') {
						statusName = "<font color='blue'>申请中</font>";
					}
					if (d.hasRight == '3') {
						statusName = "<font color='DarkGoldenRod'>已暂停</font>";
					}
					if (d.hasRight == '4') {
						statusName = "<font color='green'>已授权</font>"
					}
					return statusName;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/writeRight/getDoctorWritePager.spring?userCode=" + param.get("docUserCode") + "&" + filterParam.__form() + "&" + param.__form(),
				cols : [ cols ],
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					return res;
				},
				events : {
					toAddAnesthesiaClass : rightAddList.toAddAnesthesiaClass,
					toCancelAnesthesiaClass : rightAddList.toCancelAnesthesiaClass,
					toStartOrStopAnesthesiaClass : rightAddList.toStartOrStopAnesthesiaClass,
				}
			});
		} else if (type == 2) {//麻醉
			var cols = [ {
				title : '操作',
				width : 70,
				align : "center",
				templet : function(d) {
					//d.hasRight --   0 未授权 2 申请中 3 已暂停 4 已授权
					var html = '';
//					if (d.hasRight == '0' && d.status == 1 && d.certifiCateListSize > 0) {
					if (d.hasRight == '0' && d.status == 1) {
						html += '<i class="layui-icon2" title="授权" lay-event="toAddAnesthesiaClass">&#xe819;</i>';
					}
					if (d.hasRight == '3') {
						html += '<i class="layui-icon2" title="恢复" lay-event="toStartOrStopAnesthesiaClass">&#xeb98;</i>';
					}
					if (d.hasRight == '4') {
						html += '<i class="layui-icon2" title="暂停" lay-event="toStartOrStopAnesthesiaClass">&#xeb99;</i>&nbsp;&nbsp;';
						html += '<i class="layui-icon2" title="回收" lay-event="toCancelAnesthesiaClass">&#xe928;</i>';
					}
					return html;
				}
			}, {
				title : '麻醉名称',
				width : 120,
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.anClassName);
				}
			}, {
				title : '申请条件',
				align : "center",
				templet : function(d) {
					return assemblys.htmlEncode(d.attention);
				}
			}, {
				title : '状态',
				width : 90,
				align : "center",
				templet : function(d) {
					var statusName = "";
					if (d.hasRight == '0') {
						statusName = "<font color='red'>未授权</font>";
					}
					if (d.hasRight == '2') {
						statusName = "<font color='blue'>申请中</font>";
					}
					if (d.hasRight == '3') {
						statusName = "<font color='DarkGoldenRod'>已暂停</font>";
					}
					if (d.hasRight == '4') {
						statusName = "<font color='green'>已授权</font>"
					}
					return statusName;
				}
			} ];
			
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/anaesthesiaRight/getDoctorAnaesthesiaRightPager.spring?" + param.__form() + "&" + filterParam.__form() + "&operation_state=1",
				cols : [ cols ],
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					$("#filterNum").text(count);
					
					layui.form.render();
				},
				events : {
					toAddAnesthesiaClass : rightAddList.toAddAnesthesiaClass,
					toCancelAnesthesiaClass : rightAddList.toCancelAnesthesiaClass,
					toStartOrStopAnesthesiaClass : rightAddList.toStartOrStopAnesthesiaClass,
				}
			});
		} else if (type == 3) {//查房
			var cols = [ {
				title : '操作',
				width : 80,
				align : "center",
				templet : function(d) {
					//d.hasRight --   0 未授权 2 申请中 3 已暂停 4 已授权
					var html = '';
					if (d.hasRight == '0') {
						html += '<i class="layui-icon2" title="授权" lay-event="toAddAnesthesiaClass">&#xe819;</i>';
					}
					if (d.hasRight == '3') {
						html += '<i class="layui-icon2" title="恢复" lay-event="toStartOrStopAnesthesiaClass">&#xeb98;</i>';
					}
					if (d.hasRight == '4') {
						html += '<i class="layui-icon2" title="暂停" lay-event="toStartOrStopAnesthesiaClass">&#xeb99;</i>&nbsp;&nbsp;';
						html += '<i class="layui-icon2" title="回收" lay-event="toCancelAnesthesiaClass">&#xe928;</i>';
					}
					return html;
				}
			}, {
				title : '查房等级',
				align : "left",
				templet : function(d) {
					return assemblys.htmlEncode(d.dictName);
				}
			}, {
				title : '等级编码',
				width : 120,
				align : "left",
				templet : function(d) {
					return assemblys.htmlEncode(d.dictCode);
				}
			}, {
				title : '状态',
				width : 120,
				align : "left",
				templet : function(d) {
					var statusName = "";
					if (d.hasRight == '0') {
						statusName = "<font color='red'>未授权</font>";
					}
					if (d.hasRight == '2') {
						statusName = "<font color='blue'>申请中</font>";
					}
					if (d.hasRight == '3') {
						statusName = "<font color='DarkGoldenRod'>已暂停</font>";
					}
					if (d.hasRight == '4') {
						statusName = "<font color='green'>已授权</font>"
					}
					return statusName;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "mdms/checkRoomRight/getDoctorCheckRoomRightPager.spring?" + filterParam.__form() + "&" + param.__form(),
				cols : [ cols ],
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					$("#filterNum").text(count);
				},
				events : {
					toAddAnesthesiaClass : rightAddList.toAddAnesthesiaClass,
					toCancelAnesthesiaClass : rightAddList.toCancelAnesthesiaClass,
					toStartOrStopAnesthesiaClass : rightAddList.toStartOrStopAnesthesiaClass,
				}
			});
		} else {//亚专业
			var cols = [ {
				title : '操作',
				width : 80,
				align : "center",
				templet : function(d) {
					//d.hasRight --   0 未授权 2 申请中 3 已暂停 4 已授权
					var html = '';
					if (d.hasRight == '0') {
						html += '<i class="layui-icon2" title="授权" lay-event="toAddAnesthesiaClass">&#xe819;</i>';
					}
					if (d.hasRight == '3') {
						html += '<i class="layui-icon2" title="恢复" lay-event="toStartOrStopAnesthesiaClass">&#xeb98;</i>';
					}
					if (d.hasRight == '4') {
						html += '<i class="layui-icon2" title="暂停" lay-event="toStartOrStopAnesthesiaClass">&#xeb99;</i>&nbsp;&nbsp;';
						html += '<i class="layui-icon2" title="回收" lay-event="toCancelAnesthesiaClass">&#xe928;</i>';
					}
					return html;
				}
			}, {
				title : '院内代码',
				align : "center",
				width : 150,
				templet : function(d) {
					return d.subProClassHisCode;
				}
			}, {
				title : '亚专业名称',
				align : "center",
				templet : function(d) {
					return d.subProClassName;
				}
			}, {
				title : '状态',
				width : 120,
				align : "left",
				templet : function(d) {
					var statusName = "";
					if (d.hasRight == '0') {
						statusName = "<font color='red'>未授权</font>";
					}
					if (d.hasRight == '2') {
						statusName = "<font color='blue'>申请中</font>";
					}
					if (d.hasRight == '3') {
						statusName = "<font color='DarkGoldenRod'>已暂停</font>";
					}
					if (d.hasRight == '4') {
						statusName = "<font color='green'>已授权</font>"
					}
					return statusName;
				}
			} ];
			assemblys.tableRender({
				elem : '#list',
				url : basePath + "/mdms/subProfessionRight/getDoctorSubProfessionRightPager.spring?" + filterParam.__form() + "&" + param.__form(),
				cols : [ cols ],
				done : function(res, curr, count) {
					page.set("curPageNum", res.curPageNum);
					page.set("pageSize", res.pageSize);
					$("#filterNum").text(count);
				},
				events : {
					toAddAnesthesiaClass : rightAddList.toAddAnesthesiaClass,
					toCancelAnesthesiaClass : rightAddList.toCancelAnesthesiaClass,
					toStartOrStopAnesthesiaClass : rightAddList.toStartOrStopAnesthesiaClass,
				}
			});
		}
		
	},
	userRightInit : function() {
		return $.ajax({
			url : basePath + "mdms/mdmsCommon/userRightInit.spring?lookUserCode=" + param.get("lookUserCode")
		}).then(function(data) {
			return data;
		});
	},
	
	query : function() {
		var type = param.get("type");
		rightAddList.getAnesthesiaClassPager(type);
	},
	toAddAnesthesiaClass : function(d) {
		//手术授权新增
		if (param.get("type") == 99) {
			rightAddList.rightName = d.operationName;//hwx 2023-9-21 处理手术名称特殊字符
			var url = basePath + "/mdms/functionModule/newDoctorFile/operationRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SHOUSHUQUANXIAN + "&authType=" + param.get("type") + "&deptOperationId=" + d.deptOperationId + "&userCode=" + param.get("docUserCode") + "&isValid=1" + "&operationRightId=0&type=1";
		}
		
		//处方授权新增
		if (param.get("type") == 1) {
			var url = basePath + "/mdms/functionModule/newDoctorFile/writeRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_PRESCRIPTION + "&authType=" + param.get("type") + "&rmosDictId=" + d.rmosDictId + "&userCode=" + param.get("docUserCode") + "&isValid=1" + "&rmosName=" + d.rmosName + "&writeRightId=0&type=1";
		}
		//麻醉授权新增
		if (param.get("type") == 2) {
			var url = basePath + "/mdms/functionModule/newDoctorFile/anaesthesiaRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.FUN_ANESTHESIA + "&authType=" + param.get("type") + "&anClassId=" + d.anClassId + "&userCode=" + param.get("docUserCode") + "&isValid=1" + "&anClassName=" + d.anClassName + "&anaesthesiaRightId=0&type=1";
		}
		//查房授权新增
		if (param.get("type") == 3) {
			var url = basePath + "/mdms/functionModule/newDoctorFile/checkRoomRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_THREE_WARD_ROUND + "&authType=" + param.get("type") + "&checkRoomCode=" + d.dictCode + "&userCode=" + param.get("docUserCode") + "&isValid=1" + "&dictName=" + d.dictName + "&checkRoomRightId=0&type=1";
		}
		//亚专业授权新增
		if (param.get("type") == 4) {
			var url = basePath + "/mdms/functionModule/newDoctorFile/subProfessionRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SUBPROFESSIONRIGHT + "&authType=" + param.get("type") + "&deptSubProfessionCode=" + d.deptSubProfessionCode + "&userCode=" + param.get("docUserCode") + "&isValid=1" + "&subProClassName=" + d.subProClassName + "&subProfessionRightID=0&type=1";
			
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "anaesthesiaRightEdit",
			area : [ '700px', '400px' ],
			closeBtn : 0,
			title : false,
			scrollbar : false,
			content : url
		});
	},
	toCancelAnesthesiaClass : function(d) {
		
		//手术权回收
		if (param.get("type") == 99) {
			rightAddList.rightName = d.operationName;//hwx 2023-9-21 处理手术名称特殊字符
			url = basePath + "/mdms/functionModule/newDoctorFile/operationRightReason.html?operationRightId=" + d.operationRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SHOUSHUQUANXIAN + "&authType=" + param.get("type") + "&customFormFilledCode=" + param.get("customFormFilledCode");
		}
		
		//处方授权回收
		if (param.get("type") == 1) {
			var url = basePath + "/mdms/functionModule/newDoctorFile/writeRightReason.html?&writeRightId=" + d.writeRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_PRESCRIPTION + "&rmosName=" + d.rmosName + "&authType=" + param.get("type") + "&customFormFilledCode=" + param.get("customFormFilledCode");
		}
		
		//麻醉权回收
		if (param.get("type") == 2) {
			url = basePath + "mdms/functionModule/newDoctorFile/anaesthesiaRightReason.html?anaesthesiaRightId=" + d.AnaesthesiaRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.FUN_ANESTHESIA + "&anClassName=" + d.anClassName + "&authType=" + param.get("type") + "&customFormFilledCode=" + param.get("customFormFilledCode");
			
		}
		
		//查房权回收
		if (param.get("type") == 3) {
			url = basePath + "mdms/functionModule/newDoctorFile/checkRoomRightReason.html?checkRoomRightId=" + d.checkRoomRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_THREE_WARD_ROUND + "&dictName=" + d.dictName + "&authType=" + param.get("type") + "&customFormFilledCode=" + param.get("customFormFilledCode");
		}
		
		//亚专业回收
		if (param.get("type") == 4) {
			url = basePath + "mdms/functionModule/newDoctorFile/subProfessionRightReason.html?subProfessionRightID=" + d.subProfessionRightID + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SUBPROFESSIONRIGHT + "&subProClassName=" + d.subProClassName + "&authType=" + param.get("type") + "&customFormFilledCode=" + param.get("customFormFilledCode");
		}
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "anaesthesiaRightEdit",
			area : [ '700px', '400px' ],
			closeBtn : 0,
			title : false,
			scrollbar : false,
			content : url
		});
		
	},
	
	toStartOrStopAnesthesiaClass : function(d) {
		var url = '';
		var isValid = d.isValid;
		
		//手术
		if (param.get("type") == 99) {
			//恢復
			if (isValid == '0') {
				isValid = '1';
				rightAddList.rightName = d.operationName;//hwx 2023-9-21 处理手术名称特殊字符
				url = basePath + "/mdms/functionModule/newDoctorFile/operationRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SHOUSHUQUANXIAN + "&operationRightId=" + d.operationRightId + "&authType=" + param.get("type") + "&type=3&deptOperationId=" + d.deptOperationId + "&userCode=" + param.get("docUserCode") + "&isValid=" + isValid;
			} else {
				//手术授权暂停
				isValid = '0';
				rightAddList.rightName = d.operationName;//hwx 2023-9-21 处理手术名称特殊字符
				url = basePath + "mdms/functionModule/newDoctorFile/operationRightReason.html?operationRightId=" + d.operationRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SHOUSHUQUANXIAN + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&isValid=" + isValid + "&authType=" + param.get("type") + "&userCode=" + param.get("docUserCode");
			}
		}
		
		//处方
		if (param.get("type") == 1) {
			//恢復
			if (isValid == '0') {
				isValid = '1';
				url = basePath + "/mdms/functionModule/newDoctorFile/writeRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_PRESCRIPTION + "&writeRightId=" + d.writeRightId + "&authType=" + param.get("type") + "&type=3&rmosDictId=" + d.rmosDictId + "&userCode=" + param.get("docUserCode") + "&isValid=" + isValid + "&rmosName=" + d.rmosName
			} else {
				//处方授权暂停
				isValid = '0';
				url = basePath + "mdms/functionModule/newDoctorFile/writeRightReason.html?writeRightId=" + d.writeRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_PRESCRIPTION + "&rmosName=" + d.rmosName + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&isValid=" + isValid + "&authType=" + param.get("type") + "&userCode=" + param.get("docUserCode");
			}
		}
		
		//麻醉
		if (param.get("type") == 2) {
			//恢復
			if (isValid == '0') {
				isValid = '1';
				url = basePath + "/mdms/functionModule/newDoctorFile/anaesthesiaRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.FUN_ANESTHESIA + "&anaesthesiaRightId=" + d.AnaesthesiaRightId + "&authType=" + param.get("type") + "&type=3&anClassId=" + d.anClassId + "&userCode=" + param.get("docUserCode") + "&isValid=" + isValid + "&anClassName=" + d.anClassName
			} else {
				//麻醉授权暂停
				isValid = '0';
				url = basePath + "mdms/functionModule/newDoctorFile/anaesthesiaRightReason.html?anaesthesiaRightId=" + d.AnaesthesiaRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.FUN_ANESTHESIA + "&anClassName=" + d.anClassName + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&isValid=" + isValid + "&authType=" + param.get("type") + "&userCode=" + param.get("docUserCode");
			}
		}
		
		//查房
		if (param.get("type") == 3) {
			//恢復
			if (isValid == '0') {
				isValid = '1';
				url = basePath + "/mdms/functionModule/newDoctorFile/checkRoomRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_THREE_WARD_ROUND + "&checkRoomRightId=" + d.checkRoomRightId + "&authType=" + param.get("type") + "&type=3&checkRoomCode=" + d.dictCode + "&userCode=" + param.get("docUserCode") + "&isValid=" + isValid + "&dictName=" + d.dictName
			} else {
				//查房授权暂停
				isValid = '0';
				url = basePath + "mdms/functionModule/newDoctorFile/checkRoomRightReason.html?checkRoomRightId=" + d.checkRoomRightId + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_THREE_WARD_ROUND + "&dictName=" + d.dictName + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&isValid=" + isValid + "&authType=" + param.get("type") + "&userCode=" + param.get("docUserCode");
			}
		}
		
		//亚专业
		if (param.get("type") == 4) {
			//恢復
			if (isValid == '0') {
				isValid = '1';
				url = basePath + "/mdms/functionModule/newDoctorFile/subProfessionRightEdit.html?customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SUBPROFESSIONRIGHT + "&subProfessionRightID=" + d.subProfessionRightID + "&authType=" + param.get("type") + "&type=3&subProfessionClassID=" + d.subProfessionClassID + "&userCode=" + param.get("docUserCode") + "&isValid=" + isValid + "&subProClassName=" + d.subProClassName;
			} else {
				//亚专业授权暂停
				isValid = '0';
				url = basePath + "/mdms/functionModule/newDoctorFile/subProfessionRightReason.html?subProfessionRightID=" + d.subProfessionRightID + "&funCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SUBPROFESSIONRIGHT + "&subProClassName=" + d.subProClassName + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&isValid=" + isValid + "&authType=" + param.get("type") + "&userCode=" + param.get("docUserCode");
			}
		}
		
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "anaesthesiaRightEdit",
			area : [ '700px', '400px' ],
			closeBtn : 0,
			title : false,
			scrollbar : false,
			content : url
		});
	},
	
	toViewOperationClass : function(d) {
		parent.layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditOperationClass",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : basePath + "/mdms/functionModule/operationClass/operationClassView.html?operationClassId=" + d
		});
	},

}
