var rightLogList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		rightLogList.rightLogListInit().then(function(data) {
			rightLogList.getRightLogPager();
			rightLogList.initLayuiForm();
		});
	},
	rightLogListInit : function() {
		return $.ajax({
			url : basePath + "mdms/rightLog/rightLogListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		layui.form.render();
	},
	query : function() {
		rightLogList.getRightLogPager();
	},
	
	getRightLogPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			width : 40,
			type : 'numbers'
		}, {
			title : '类型',
			align : "center",
			width : 80,
			templet : function(d) {
				// 类型（1新增2暂停3恢复4回收5到期
				var html = '';
				switch (d.type) {
				case 1:
					html = '新增';
					break;
				case 2:
					html = '暂停';
					break;
				case 3:
					html = '恢复';
					break;
				case 4:
					html = '回收';
					break;
				case 5:
					html = '到期';
					break;
				case 8:
					html = '再授权';
					break;
				}
				return assemblys.htmlEncode(html);
			}
		}, {
			title : "权限名",
			align : "center",
			minWidth : 150,
			templet : function(d) {
				return assemblys.htmlEncode(d.className);
			}
		} ];
		//手術等級
		if (param.get("rightClass") == "3") {
			cols.push({
				title : "手术等級",
				align : "center",
				width : 90,
				templet : function(d) {
					var html = '';
					var operationLevelName = d.operationLevelName;
					if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel1) {
						html += '<font style="color:#338100;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel2) {
						html += '<font style="color:#0056FF;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel3) {
						html += '<font style="color:#CC0000;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel4) {
						html += '<font style="color:#FF002B;" >';
					} else if (d.operationLevelCode == assemblys.top.mdms.mdmsConstant.SSLevel5) {
						html += '<font style="color:orange" >';
					} else {
						html += '<font style="color:orange" >';
					}
					if (d.operationLevelName != undefined) {
						html += d.operationLevelName + '</font>';
					} else {
						html += '<font style="color:orange;" >暂无等级</font>';
					}
					return html;
				}
			});
			
		}
		cols.push({
			title : '授权开始时间',
			align : "center",
			width : 150,
			templet : function(d) {
				var html = "";
				if (d.startDate) {
					html = assemblys.dateToStr(d.startDate);
				}
				return html;
			}
		}, {
			title : '授权结束时间',
			align : "center",
			width : 150,
			templet : function(d) {
				var html = "";
				if (d.endDate) {
					html = assemblys.dateToStr(d.endDate);
				}
				return html;
			}
		}, {
			title : '操作时间',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.dateToStr(d.optDate);
			}
		}, {
			title : '操作人',
			align : "center",
			minWidth : 120,
			templet : function(d) {
				return assemblys.htmlEncode(d.optUserName);
			}
		}, {
			title : '原因说明',
			align : "left",
			width : 200,
			templet : function(d) {
				return assemblys.htmlEncode(d.reason);
			}
		});
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/rightLog/getRightLogPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditRightLog : rightLogList.toEditRightLog,
				deleteRightLog : rightLogList.deleteRightLog
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/rightLog/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditRightLog : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditRightLog",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "rightLogEdit.html?funCode=" + param.get("funCode") + "&logID=" + d.logID
		});
	},
	deleteRightLog : function(d) {
		return $.ajax({
			url : basePath + "mdms/rightLog/deleteRightLog.spring",
			type : "post",
			data : {
				logID : d.logID
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				rightLogList.getRightLogPager();
			});
			return data;
		});
	}
}