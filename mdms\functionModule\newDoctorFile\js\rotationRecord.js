var rotationRecord = {
	// 轮转管理
	deptExchangeList : function(contentID) {
		window.contentID = contentID;
		$.ajax({
			url : basePath + "/mdms/deptExchange/getDeptExchangeInfo.spring",
			type : "get",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
				var list = data.deptExchangeInfo;
				
				$.each(list, function(index, temp) {
					// 序号
					temp["index"] = index + 1;
				});
				
				var mapping = [ {
					name : "操作",
					width : 30,
					opt : [ {
						"classModel" : "1",
						"className" : "layui-icon-search",
						"onclick" : function(data) {
							rotationRecord.deptExchangeEdit(data.DeptExchangeId, 1);
						}
					}, {
						"classModel" : "1",
						"className" : "layui-icon-edit",
						"show" : function(data) {
							if (param.get("onlyShow") == 1) {
								return false;
							} else {
								if (param.get("exchangeManageEdit") == 'true' && data.State == 0) {
									return true;
								} else {
									return false;
								}
								
							}
						},
						"onclick" : function(data) {
							rotationRecord.deptExchangeEdit(data.DeptExchangeId, 0);
							
						}
					}, {
						"classModel" : "1",
						"className" : "layui-icon-delete",
						"show" : function(data) {
							if (param.get("onlyShow") == 1) {
								return false;
							} else {
								if (param.get("exchangeManageDel") == 'true') {
									return true;
								} else {
									return false;
								}
								
							}
						},
						"onclick" : function(data) {
							rotationRecord.deptExchangeDel(data.DeptExchangeId);
						}
					} ]
				}, {
					name : "序号",
					width : 30,
					value : "DictName",
					templet : function(data) {
						return data.index;
					}
				}, {
					name : "原科室",
					value : "DeptName"
				}, {
					name : "轮转科室",
					value : "ExchangeDeptName"
				}, {
					name : "开始时间",
					value : "BeginTime"
				}, {
					name : "结束时间",
					value : "EndTime"
				}, {
					name : "轮转类型",
					value : "DictName"
				}, {
					name : "轮转状态",
					value : "StateValue"
				} ]
				// 渲染
				initCustomDetail.initTableList(contentID, mapping, list);
				$(contentID).css("height", newDoctorInfo.returnHeight(35));
				$(contentID).css("width", newDoctorInfo.returnWidth());
				if (param.get("formStatus") != 1 && param.get("exchangeManageAdd") == 'true') {
					$(contentID).children().before('<div style="float: right; margin-bottom: 5px;"><button type="button" class="layui-btn layui-btn-sm" onclick="rotationRecord.addDeptExchange();">新增</button></div>');
				}
			}
		
		});
		
	},
	// 新增轮转登记
	addDeptExchange : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "deptExchangeEdit",
			area : [ '600px', '70%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/deptExchangeEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	//轮转信息编辑
	deptExchangeEdit : function(deptExchangeId, showOrEdit) {
		var height = "450px";
		var html = "deptExchangeEdit.html";
		if (showOrEdit == 1) {
			html = "deptExchangeView.html";
			height = "300px";
		}
		var id = parseInt(deptExchangeId);
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "certifiToEdit",
			area : [ '600px', height ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&deptExchangeId=" + deptExchangeId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
		
		});
	},
	//轮转信息删除
	deptExchangeDel : function(deptExchangeId) {
		layer.confirm("确定要删除吗？", function() {
			$.ajax({
				url : basePath + "/mdms/deptExchange/deleteDeptExchange.spring",
				type : "post",
				data : {
					deptExchangeId : deptExchangeId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					var $tbody = $(window.contentID).empty();
					rotationRecord.deptExchangeList(window.contentID);
				});
				return data;
			});
		});
	},
	//轮转详情
	searchExchangeDetail : function(deptExchangeId) {
		var id = parseInt(deptExchangeId);
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "certifiToEdit",
			area : [ '600px', '80%' ],
			title : false,
			scrollbar : false,
			content : "deptExchangeDetail.html?deptExchangeId=" + deptExchangeId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
		
		});
	}
}