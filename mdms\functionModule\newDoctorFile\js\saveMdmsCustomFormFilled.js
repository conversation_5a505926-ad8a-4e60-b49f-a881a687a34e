var saveMdmsCustomFormFilled = {
	//修改表单状态
	updateStatus : function(customFormFilledCode, state) {
		$.ajax({
			url : basePath + "mdms/mdmsCommon/saveFormState.spring",
			data : {
				"customFormFilledCode" : customFormFilledCode,
				"state" : state
			},
			type : "POST",
			dataType : "json",
			async : false,
			success : function(data) {
				
			}
		});
	},
	//更新表单审批数据
	/*updateStatus : function(customFormFilledCode, state) {
		$.ajax({
			url : basePath + "mdms/mdmsCommon/updateApproveRecord.spring",
			data : {
				"customFormFilledCode" : customFormFilledCode
			
			},
			type : "POST",
			dataType : "json",
			async : false,
			success : function(data) {
				
			}
		});
	},*/
	//删除表单流程
	deleteFormFloes : function(customFormFilledCode) {
		$.ajax({
			url : basePath + "mdms/mdmsCommon/deleteCustomFormFlowsByForm.spring",
			data : {
				"customFormFilledCode" : customFormFilledCode
			},
			type : "POST",
			dataType : "json",
			async : false,
			success : function(data) {
				
			}
		});
	},
	updateApproveRecord : function(customFormFilledCode) {
		$.ajax({
			url : basePath + "mdms/mdmsCommon/updateApproveRecord.spring",
			data : {
				"customFormFilledCode" : customFormFilledCode
			
			},
			type : "POST",
			dataType : "json",
			async : false,
			success : function(data) {
				
			}
		});
	}
}