var shuttleWindow = {
	attachCheckArr : [], 
	init : function() {
		shuttleWindow.getFile().then(function(data) {
			var list = [];
				if (data.attachmentsList.length > 0) {
					for (let attachments of data.attachmentsList) {
						list.push({
							name : attachments.attachmentName,
							title : attachments.title,
							attachmentType : attachments.attachmentType,
							value : attachments.attachmentURL,
							attachmentSize : attachments.attachmentSize
						})
					}
				}
			var transfer = layui.transfer;
			//渲染
			transfer.render({
				elem : '#test1',//绑定元素
				data : list,
				id : 'demo1',//定义索引
				width : "300px",
				height : "405px",
				title : [ "未选", "已选" ],
				showSearch : true,
				onchange : function(data, index) {
					for (let data_element of data) {
						if(index == 0){
							shuttleWindow.attachCheckArr.push(data_element)
						}else {
							for ( var arr_index in shuttleWindow.attachCheckArr) {
								if(data_element.value == shuttleWindow.attachCheckArr[arr_index].value){
									shuttleWindow.attachCheckArr.splice(arr_index,1)
								}
							}
						}
					}
				}
			});
		})
	},
	//获取文件信息
	getFile : function() {
		return $.ajax({
			url : basePath + "frame/fileUpload/getAttachments.spring",
			data : {
				belongToCode : param.get("approvalBelongFlowNodeRecordCode")
			},
			dataType : "json",
			type : "GET",
			async : false,
			skipDataCheck : true
		})
	},
	//下载文件
	toDownloadFile :  function(){
		if(shuttleWindow.attachCheckArr.length > 0){
			$("#download").removeAttr("onsubmit");
			$("#download").attr("action",basePath +  "mdms/approval/downLoadFile.spring").attr("method", "post");
			$("#download").children("input[name=fileStr]").val( JSON.stringify(shuttleWindow.attachCheckArr))
			$("#download").submit();
		}else{
			layer.alert("请选择文件");
		}
	}
}