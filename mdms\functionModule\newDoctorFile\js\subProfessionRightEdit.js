var subProfessionRightEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		subProfessionRightEdit.getLevel().then(function() {
			return subProfessionRightEdit.getSubProfessionRight()
		}).then(function() {
			subProfessionRightEdit.initLayui();
		})
	},
	initLayui : function() {
		layui.form.on("submit(save)", function() {
			subProfessionRightEdit.saveSubProfessionRight();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				min : new Date().getTime(),
				format : "yyyy-MM-dd HH:mm"
			});
		});
		
		layui.form.render();
	},
	getSubProfessionRight : function() {
		return $.ajax({
			url : basePath + "mdms/subProfessionRight/getSubProfessionRight.spring",
			data : {
				subProfessionRightID : param.get("subProfessionRightID")
			}
		}).then(function(data) {
			if (data.subProfessionRight) {
				param.set("level", data.subProfessionRight.level);
				param.set("createTime", data.subProfessionRight.createTime);
				param.set("createEndTime", data.subProfessionRight.createEndTime);
				param.set("deptSubProfessionCode", data.subProfessionRight.deptSubProfessionCode);
				$("#saveBtn").val("恢复");
				
			}
			return data;
		});
	},
	saveSubProfessionRight : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/subProfessionRight/saveSubProfessionRight.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("授权成功", function() {
				assemblys.closeWindow();
				parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
				parent.parent.newDoctorInfo.holdAuthority();
				window.isSubmit = false;
			});
			return data;
		});
	},
	//获取级别
	getLevel : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.SUBPROFESSIONALLEVEL,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			},
			dataType : "json",
			skipDataCheck : true
		}).then(function(data) {
			var html = "";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
			})
			$("#level").html(html);
			layui.form.render();
		});
	},

}