var subProfessionRightReason = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		$("span[class='head1_text fw700']").text("亚专业授权");
		
		var isVaild = param.get("isValid")
		if (isVaild == "0") {
			$("#savebtn").val("暂停");
		}
		
		subProfessionRightReason.initLayui();
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			subProfessionRightReason.saveReason();
		});
	},
	saveReason : function() {
		//暂停
		if (param.get("isValid") == "0") {
			subProfessionRightReason.stopAR(param.get("subProfessionRightID"), $(".showReason").val(), param.get("subProClassName"), param.get("customFormFilledCode"));
		} else {
			//回收
			subProfessionRightReason.deleteAR(param.get("subProfessionRightID"), $(".showReason").val(), param.get("subProClassName"), param.get("customFormFilledCode"));
		}
		
	},
	
	getSubProfessionRight : function() {
		return $.ajax({
			url : basePath + "mdms/subProfessionRight/getSubProfessionRight.spring",
			data : {
				subProfessionRightID : param.get("subProfessionRightID")
			}
		}).then(function(data) {
			param.set(null, data.subProfessionRight);
			return data;
		});
	},
	
	deleteAR : function(subProfessionRightID, reason, subProClassName, customFormFilledCode) {
		layer.confirm("确定要回收吗？", function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/subProfessionRight/updateSubProfessionRight.spring",
				type : "post",
				data : {
					subProfessionRightID : subProfessionRightID,
					reason : reason,
					subProClassName : subProClassName,
					customFormFilledCode : customFormFilledCode,
					type : 4
				}
			}).then(function(data) {
				assemblys.msg("回收成功", function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
				});
				window.isSubmit = false;
			});
		});
	},
	
	stopAR : function(subProfessionRightID, reason, subProClassName, customFormFilledCode) {
		var reConfirm = "";
		var reMessage = "";
		if (param.get("isValid") == "0") {
			reConfirm = "确定要暂停吗？";
			reMessage = "暂停成功";
		}
		layer.confirm(reConfirm, function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/subProfessionRight/saveSubProfessionRight.spring",
				type : "post",
				data : {
					subProfessionRightID : subProfessionRightID,
					reason : reason,
					subProClassName : subProClassName,
					customFormFilledCode : customFormFilledCode,
					type : 2,//
					isValid : param.get("isValid"),
					userCode : param.get("userCode"),
				}
			}).then(function(data) {
				assemblys.msg(reMessage, function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
					window.isSubmit = false;
				});
			});
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}