var writeRightEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		$("span[class='head1_text fw700']").text("处方授权");
		writeRightEdit.getWriteRight();
		writeRightEdit.initLayui();
		
	},
	initLayui : function() {
		layui.form.render();
		
		//加载时间选择器 - 更多请查看官网
		var layDate = layui.laydate;
		//hwx 2023年12月13日上午11:07:11 优化日期选择框
		layDate.render({
			elem : '#authorizeDate',
			type : 'datetime',
			format : 'yyyy-MM-dd HH:mm',
			trigger : 'click',
			min : mdmsCommon.dateToStr(new Date(), 'yyyy-MM-dd HH:mm'),
			ready : function(date) {
				// 可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
		layDate.render({
			elem : '#authorizeEndDate',
			trigger : 'click',
			type : 'datetime',
			format : 'yyyy-MM-dd HH:mm',
			min : mdmsCommon.dateToStr(new Date(), 'yyyy-MM-dd HH:mm'),
			ready : function(date) {
				// 可以自定义时分秒
				var now = new Date();
				this.dateTime.hours = now.getHours();
				this.dateTime.minutes = now.getMinutes();
			}
		});
		
		layui.form.on("submit(save)", function() {
			writeRightEdit.saveWriteRigh();
			return false;
		});
	},
	
	getWriteRight : function() {
		return $.ajax({
			url : basePath + "mdms/writeRight/getWriteRight.spring",
			data : {
				writeRightId : param.get("writeRightId")
			}
		}).then(function(data) {
			if (data.writeRight) {
				//恢复
				param.set("authorizeDate", data.writeRight.authorizeDate);
				param.set("authorizeEndDate", data.writeRight.authorizeEndDate);
				$("#saveBtn").val("恢复");
			}
			return data;
		});
	},
	
	saveWriteRigh : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/writeRight/saveWriteRight.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			if (data.res == "fail") {
				assemblys.msg(data.msg);
			} else {
				assemblys.msg("授权成功", function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
					//hwx 2024年3月21日下午3:51:00 重复提交问题
					window.isSubmit = false;
				});
			}
			return data;
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}