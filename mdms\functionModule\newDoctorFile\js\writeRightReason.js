var writeRightReason = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		$("span[class='head1_text fw700']").text("处方授权");
		var isVaild = param.get("isValid")
		if (isVaild == "0") {
			$("#saveBtn").val("暂停");
		}
		writeRightReason.initLayui();
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			writeRightReason.saveReason();
		});
	},
	saveReason : function() {
		//暂停
		if (param.get("isValid") == "0") {
			writeRightReason.stopOrStarAR(param.get("writeRightId"), $(".showReason").val(), param.get("rmosName"), param.get("customFormFilledCode"));
		} else {
			//回收
			writeRightReason.deleteAR(param.get("writeRightId"), $(".showReason").val(), param.get("rmosName"), param.get("customFormFilledCode"));
		}
		
	},
	
	deleteAR : function(writeRightId, reason, rmosName, customFormFilledCode) {
		layer.confirm("确定要回收吗？", function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/writeRight/updateWriteRight.spring",
				type : "post",
				data : {
					writeRightId : writeRightId,
					reason : reason,
					rmosName : rmosName,
					customFormFilledCode : customFormFilledCode,
					type : 4
				}
			}).then(function(data) {
				if (data.res == "fail") {
					assemblys.msg(data.msg);
				} else {
					assemblys.msg("回收成功", function() {
						assemblys.closeWindow();
						parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
						parent.parent.newDoctorInfo.holdAuthority();
					});
				}
				window.isSubmit = false;
			});
		});
	},
	
	stopOrStarAR : function(writeRightId, reason, rmosName, customFormFilledCode) {
		var reConfirm = "";
		var reMessage = "";
		if (param.get("isValid") == "0") {
			reConfirm = "确定要暂停吗？";
			reMessage = "暂停成功";
		}
		layer.confirm(reConfirm, function() {
			//hwx 2024年3月21日下午3:51:00 重复提交问题
			if (window.isSubmit) {
				return;
			}
			window.isSubmit = true;
			$.ajax({
				url : basePath + "mdms/writeRight/saveWriteRight.spring",
				type : "post",
				data : {
					writeRightId : writeRightId,
					reason : reason,
					rmosName : rmosName,
					customFormFilledCode : customFormFilledCode,
					type : 2,//
					isValid : param.get("isValid"),
					userCode : param.get("userCode"),
				}
			}).then(function(data) {
				assemblys.msg(reMessage, function() {
					assemblys.closeWindow();
					parent.rightAddList.getAnesthesiaClassPager(param.get("authType"));
					parent.parent.newDoctorInfo.holdAuthority();
					window.isSubmit = false;
				});
			});
		});
	},
	
	closebutton : function() {
		assemblys.closeWindow();
	}
}