<!DOCTYPE html>
<html>
<head>
<title>不良事件列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
</head>
<body style="background-color:#ffffff;">
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="compNo">
		<input type="hidden" name="userCode">
		<input type="hidden" name="showWidth">
		<input type="hidden" name="showHeight">
		<input type="hidden" name="funCode">
		<div class="head0">
			<span style="color: red;">注：医师请到不良事件系统进行申述操作</span>
			<div class="head0_right fr">
				<button type="button" id="btnExport" class="layui-btn layui-btn-sm layui-hide" onclick="exportList.exportToExcel('','不良事件记录');">导出</button>
			</div>
		</div>
	</form>
	<div id="table" class="layui-form" style="background: #fff;position: absolute;margin-top:-10px;">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		<div class="layui-row">
			<div id="list" lay-filter="list"></div>
		</div>		
	</div>
<!-- 	<div id="table2" style="height: 300px;">
	<div id='top-badevent' class='layui-form'>
	<table class='layui-table main_table ' style='margin-left: auto; margin-right: auto; max-width: auto' id='expenseTable'></table>
	</div>	
	<div class="layui-table-page  layui-form" style="border-width: 1px; height: 38px; padding: 0px; width: auto;" lay-filter="layui-table-page">
	   <div id="layui-table-page1" style="margin: 5px;"></div>
    </div>
	</div> -->
	<form class="layui-form" lay-filter="page">
		<input type="hidden" id="curPageNum" name="curPageNum" value="1">
		<input type="hidden" id="pageSize" name="pageSize" value="20">
	</form>
	
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/newDoctorFile/js/newDoctorInfo.js?r="+Math.random()></script>
<script type="text/javascript" src="js/myAdverseEventsList.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/mdmsCustomList/js/exportList.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		myAdverseEventsList.init();
	});
</script>
</html>