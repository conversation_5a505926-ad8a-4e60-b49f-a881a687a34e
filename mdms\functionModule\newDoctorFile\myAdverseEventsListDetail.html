<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>单点登录</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../frame/customDetail/css/customFormDetail.css">
<link rel="stylesheet" type="text/css" href="../../../frame/customApprovalFlow/approval/css/approvalFlow.css">
<link rel="stylesheet" type="text/css" href="css/myAdverseEventsListDetail.css">
</head>
<body>
	<!-- 	数据层 -->
	<form class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="funCode" >
		<input type="hidden" name="eventID" >
		<input type="hidden" name="eventInputID" >
		<input type="hidden" name="insertEventID" >
		<input type="hidden" name="url" >
		<input type="hidden" name="compNo" >
		<input type="hidden" name="isAnonymity" >
		<input type="hidden" name="eventCodeNo" attribute="eventCodeNo">
		<input type="hidden" name="eventName" attribute="eventName">
		<input type="hidden" name="eventInputTypeName" >
		<input type="hidden" name="dictCode" >
		<input type="hidden" name="detailInterfaceCode" >
		<input type="hidden" name="eventType" >
	</form>
	<div class="head0">
		<div class="top top_div">
			<!-- 标题 -->
			<div style="float: left">
				<span class="head1_text fw700">
					<i class="layui-icon layui-icon-form"></i>
				</span>
				<span class="num fw700" id="titleName">
					<span id="eventName"></span>
					&nbsp;事件编号：&nbsp;&nbsp;<span id="eventCodeNo"></span>
				</span>
			</div>
			 <div class="button head0_right fr layui-hide">
			    <button type='button' class="layui-btn layui-btn-sm skin-btn-minor"  onclick="myAdverseEventsListDetail.openOrClose();">全部收起</button>
				<button type='button' class="layui-btn layui-btn-sm skin-btn-minor"  onclick="myAdverseEventsListDetail.toExportDetail('FtnEventUrl');">导出</button>
				<button type='button' class="layui-btn layui-btn-sm skin-btn-normal"  onclick="assemblys.top.closeTab('事件详情');">关闭</button>
			</div>	
		</div>	
	</div>
	<div class="bodys" style="top: 43px;">
			<div class="tab layui-hide" id="tab">
				<ul id="tab-items" class="layui-tab-title head2_tab h28 ">
					<li class="layui-this" index="0" hasExport="true" onclick="myAdverseEventsListDetail.switchTab('eventDetail');">事件详情</li>
					<li right="hasAuditContent" class="layui-hide" onclick="myAdverseEventsListDetail.switchTab('auditContentInfo');">审核内容</li>
				</ul>
			</div>	
		<div class="layui-tab-content lr_box">
			<ul class="layui-nav layui-nav-tree left">
			   <li id ="formDetail" class="layui-nav-item layui-nav-itemed layui-hide">
					<ul class="layui-nav layui-nav-tree left" lay-filter="eventInputDetailUl">
						<li id="eventInputDetailLi" style="display: none;"></li>
					</ul>
				</li>
				<li id ="approvalList" class="layui-nav-item layui-nav-itemed layui-hide"></li>	
			</ul>
		</div>
	</div>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/myAdverseEventsListDetail.js?r="+Math.random()></script>
<!-- 下载附件 -->
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<!-- 打印导出详情 -->
<script type="text/javascript" src="../../../mdms/functionModule/mdmsCustomList/js/exportList.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../frame/customDetail/js/initCustomDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/components/commonExportUtil/js/commonExportUtil.js?r="+Math.random()></script>
<script>
	$(function(){
		myAdverseEventsListDetail.init();
	});
</script>
</html>
