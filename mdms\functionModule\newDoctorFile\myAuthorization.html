<!DOCTYPE html>
<html>
<head>
<title>我的授权列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="tabState" value="99">
		<input type="hidden" name="funCode" value="">
		<!-- 权限状态 1有效 0无效/暂停 -->
		<input type="hidden" name="state" value="1">
		<input type="hidden" name="status" value="99">
		<input type="hidden" name="compNo">
		<input type="hidden" name="isShow">
		<input type="hidden" name="findUserCode" value="">
		<input type="hidden" name="titleName">
		<input type="hidden" name="customFormFilledCode" id="customFormFilledCode" value="">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="layui-tab layui-hide" lay-filter="authTabView">
				<ul id="authTabView" class="layui-tab-title head2_tab h28">
					<li tabState="99">
						<i class="layui-icon2" title="">&#xe9f0;</i>
						&nbsp;手术
					</li>
					<li tabState="1">
						<i class="layui-icon2" title="">&#xea24;</i>
						&nbsp;处方
					</li>
					<li tabState="2">
						<i class="layui-icon2" title="">&#xeb42;</i>
						&nbsp;麻醉
					</li>
					<!-- //hwx 2023-6-25 隐藏功能
					<li tabState="3">
						<i class="layui-icon2" title="">&#xea29;</i>
						&nbsp;查房
					</li>
					<li tabState="4">
						<i class="layui-icon2" title="">&#xea29;</i>
						&nbsp;亚专业
					</li>
					 -->
				</ul>
			</div>
		</div>
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam" onsubmit="return false">
			<div class="layui-input-inline h28 lh28">
				<input class="layui-input" placeholder="技术关键内容查询" type="text" name="keyword" autocomplete="off">
			</div>
			<div class="layui-input-inline h28 lh28">
				<button type="button" class="layui-btn layui-btn-sm" onclick="technicalAuthorization.query()">查询</button>
			</div>
		</form>
		<div class="layui-row">
			<div id="list" lay-filter="list"></div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/myAuthorization.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		technicalAuthorization.initAuth();
	});
</script>
</html>