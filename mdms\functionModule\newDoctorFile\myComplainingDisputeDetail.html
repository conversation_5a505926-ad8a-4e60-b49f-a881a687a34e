<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<title>投诉详情</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="css/complaintDetail.css" />
<link rel="stylesheet" type="text/css" href="../../../frame/customApprovalFlow/approval/css/approvalFlow.css">
</head>
<body>
	<form id="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="customFormFilledCode">
		<input type="hidden" name="funCode" value="COMPLAINTS">
		<input type="hidden" name="complaintMethod" >
		<input type="hidden" name="url">
		<input type="hidden" name="compNo">
		<input type="hidden" name="listInterfaceCode">
		<input type="hidden" name="dictCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon layui-icon-form"></i>
				<span customFormFilledCode>投诉详情</span>
			</span>
			<div class="head0_right fr">
				<div class="top top_div">
				   <!--  <button id="packUp" type='button' class="layui-btn layui-btn-sm skin-btn-minor layui-hide"  onclick="myComplainingDisputeDetail.openOrClose();">全部收起</button> -->
					<button type="button" class="layui-btn layui-btn-sm  skin-btn-minor" onclick="myComplainingDisputeDetail.toExportDetail();">导出</button>
					<button type="button" class="layui-btn layui-btn-sm  skin-btn-normal" onclick="assemblys.top.closeTab('事件详情');">关闭</button>
				</div>
			</div>
		</div>
		<div class="layui-tab lr_box " style="font-size: 0;">
			<!-- 核心信息 -->
			<div class="bodys2 bodys" style="display: inline-block; margin-right: 0.5%;">
				<div class="layui-tab layui-hide" lay-filter="docDemoTabBrief" style="width: 90%;">
					<div class="layui-tab-div">
						<ul id="tabView" class="layui-tab-title head2_tab h28">
							<li wordType="table">事件详情</li>
							<li wordType="table">处理记录</li>
							<li wordType="table" right="hospitalLeadSwitch" >院内专家委员会意见</li>
							<li wordType="table" right="judiciaryOrgSwitch" >精神鉴定意见</li>
							<li wordType="table" right="hospitalDeanSwitch" >院长意见</li>
							<li wordType="table" right="insuranceSwitch" >保险理赔记录</li>
						</ul>
					</div>
				</div>
				<div id="container" class="layui-tab-content lr_box">
					<div id="customFormDetail" class="tab-content customFormDetail"></div>
					<div id="handleRecordDetail" class="tab-content"></div>
					<div id="hospitalLeadDetail" class="tab-content">
						<ul class="layui-nav layui-nav-tree left" lay-filter="hospitalLeadDetailUL">
							<li class="layui-nav-item layui-nav-itemed subject eventDetail" style="display: block;">
								<a class="main_table_title skin-div-css">
									<span class="layui-nav-more"></span>
								</a>
								<dl class="layui-nav-child main_table_box">
									<dd>
										<table class="layui-table main_table detail_table">
											<tbody>
												<tr>
													<td align="center">无相关数据！</td>
												</tr>
											</tbody>
										</table>
									</dd>
								</dl>
							</li>
						</ul>
					</div>
					<div id="judiciaryDetail" class="tab-content">
						<ul class="layui-nav layui-nav-tree left" lay-filter="judiciaryDetailUL">
							<li class="layui-nav-item layui-nav-itemed subject eventDetail" style="display: block;">
								<a class="main_table_title skin-div-css">
									<span class="layui-nav-more"></span>
								</a>
								<dl class="layui-nav-child main_table_box">
									<dd>
										<table class="layui-table main_table detail_table">
											<tbody>
												<tr>
													<td align="center">无相关数据！</td>
												</tr>
											</tbody>
										</table>
									</dd>
								</dl>
							</li>
						</ul>
					</div>
					<div id="hospitalDeanDetail" class="tab-content">
						<ul class="layui-nav layui-nav-tree left" lay-filter="hospitalDeanDetailUL">
							<li class="layui-nav-item layui-nav-itemed subject eventDetail" style="display: block;">
								<a class="main_table_title skin-div-css">
									<span class="layui-nav-more"></span>
								</a>
								<dl class="layui-nav-child main_table_box">
									<dd>
										<table class="layui-table main_table detail_table">
											<tbody>
												<tr>
													<td align="center">无相关数据！</td>
												</tr>
											</tbody>
										</table>
									</dd>
								</dl>
							</li>
						</ul>
					</div>
					<div id="insuranceDetail" class="tab-content">
						<ul class="layui-nav layui-nav-tree left" lay-filter="insuranceDetailUL">
							<li class="layui-nav-item layui-nav-itemed subject eventDetail" style="display: block;">
								<a class="main_table_title skin-div-css">
									<span class="layui-nav-more"></span>
								</a>
								<dl class="layui-nav-child main_table_box">
									<dd>
										<table class="layui-table main_table detail_table">
											<tbody>
												<tr>
													<td align="center">无相关数据！</td>
												</tr>
											</tbody>
										</table>
									</dd>
								</dl>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/myComplainingDisputeDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="js/initComplaintDetail.js?r="+Math.random()></script>
<!-- 获取表单数据 -->
<script type="text/javascript" src="../../../frame/customApprovalFlow/approval/js/approvalFlow.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../frame/customForm/js/getCustomFormDetail.js?r="+Math.random()></script>
<!-- 下载附件 -->
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<!-- 打印导出详情 -->
<script type="text/javascript" src="../../../mdms/functionModule/mdmsCustomList/js/exportList.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../frame/customDetail/js/initCustomDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/components/commonExportUtil/js/commonExportUtil.js?r="+Math.random()></script>
<script>
	$(function(){
		myComplainingDisputeDetail.init();
	});
</script>
</html>
