<!DOCTYPE html>
<html>
<head>
<title>投诉纠纷记录</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
</head>
<body style="background-color:#ffffff;">
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="compNo">
		<input type="hidden" name="userCode">
		<input type="hidden" name="showWidth">
		<input type="hidden" name="showHeight">
		<input type="hidden" name="funCode" value="COMPLAINTS">
		<div class="head0">
			<span style="color: red;">注：医师请到投诉纠纷系统进行申述操作</span>
			<div class="head0_right fr">
				<button type="button" id="btnExport" class="layui-btn layui-btn-sm" onclick="exportList.exportToExcel('','投诉纠纷记录');">导出</button>
			</div>
		</div>
	</form>
	<div class="layui-form" style="background: #fff;position: absolute;margin-top:-10px;">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		<div class="layui-row">
			<div id="list" lay-filter="list"></div>
		</div>		
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
	
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/newDoctorFile/js/newDoctorInfo.js?r="+Math.random()></script>
<script type="text/javascript" src="js/myComplainingDisputeList.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/mdmsCustomList/js/exportList.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		myComplainingDisputeList.init();
	});
</script>
</html>