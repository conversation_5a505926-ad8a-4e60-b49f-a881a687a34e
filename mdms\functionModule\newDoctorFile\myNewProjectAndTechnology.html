<!DOCTYPE html>
<html>
<head>
<title>RmosDict列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
</head>
<body style="background-color:#ffffff;">
	<form class="layui-form" lay-filter="param" method="post">
		<input type="hidden" name="funCode">
		<input type="hidden" name="state" value="99">
		<input type="hidden" id="userCode" name="userCode" value="">
		
	</form>
	<div class="layui-form" style="background: #fff;position: absolute;margin-top:-50px;">
		<form class="layui-form" lay-filter="filterParam" method="post"></form>
		
		<div class="layui-row">
			<div id="list" lay-filter="list"></div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/newDoctorFile/js/newDoctorInfo.js?r="+Math.random()></script>
<script type="text/javascript" src="js/myNewProjectAndTechnology.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../frame/customForm/js/getCustomFormDetail.js" ></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		myNewProjectAndTechnologyList.init();
	});
</script>
</html>