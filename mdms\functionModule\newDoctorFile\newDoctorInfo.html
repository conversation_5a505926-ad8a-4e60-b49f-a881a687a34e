<!DOCTYPE html>
<html>
<head>
<title>档案管理</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="css/newDoctorInfo.css">
<link rel="stylesheet" type="text/css" href="css/approval.css">
<link rel="stylesheet" type="text/css" href="../workloadStatistics/css/workloadStatistics.css">
<link rel="stylesheet" type="text/css" href="css/newCertificateInfo.css">
<link rel="stylesheet" type="text/css" href="css/ddModel.css">
<link rel="stylesheet" type="text/css" href="css/doctorNegative.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post"  onsubmit="return false">
		<input type="hidden" name="state" value="0">
		<input type="hidden" name="appCode" value="mdms">
		<input type="hidden" name="compNo" id="compNo" >
		<input type="hidden" name="funCode" value="MDMS_DOCTOR_INF">
		<input type="hidden" name="customFormFilledCode">
		<input type="hidden" name="menuList" id="menuList">
		<input type="hidden" name="onlyShow" id="onlyShow">
		<input type="hidden" name="currentDuty">
		<input type="hidden" name="formStatus">
		<input type="hidden" name="userCode">
		<input type="hidden" name="skipStatus">
		<input type="hidden" name="skipFunCode">
		<div>
			<!-- 状态栏 -->
<!-- 			<div class="head0"  right="hasFile-edit|hasFile-view" > -->
<!-- 				<b id="menuIcon"> -->
<!-- 					<i menuicon="" class="layui-icon2"></i> -->
<!-- 					<span>医师档案</span> -->
<!-- 				</b> -->
<!-- 				<div class="head0_right fr"> -->
<!-- 								<button type="button" class="layui-btn layui-btn-sm" id="btnEdit" onclick="newDoctorInfo.doctorEdit(2)">编辑</button> -->
<!-- 				</div> -->
<!-- 			</div> -->
			<!--container盒子  -->
			<div class="container">
				<div class="showMain">
				<div class="main">
					<div class="content">
						<!--左侧菜单  -->
						<div class="left">
<!-- 							<div class="showIcon"> -->
<!-- 								<i class="layui-icon2 layui-hide" onclick="newDoctorInfo.doctorEdit(2)" title="完善档案">&#xea24;</i> -->
<!-- 							</div> -->
							<div class="showTx">
								<img class="txk" src="image/txk.png">
								<img id="headMage" src="../../../frame/images/tx.png">
							</div>
						</div>
						<!--右侧背景  -->
						<div class="right">
							<div class="headRight layui-hide">
								<div class="headRow1">
									<div class="docName" title="姓名"></div>
									<div class="docDept"  title="科室"></div>
									<div class="docSchool"  title="毕业院校"></div>
									<div class="profession"  title="所学专业"></div>
									<div class="education"  title="最高学历"></div>
									<div class="studyClass"  title="最高学位"></div>
									<div class="classTime layui-hide">
										<span>出诊时间：</span>
										<span id="timeText"></span>
									</div>
<!-- 									<div class="certifiDiv" title="展开我的证件"></div> -->
									<div class="certifiDiv">
										<div id="addBtn" class="layui-input-inline" style="max-width: 100%;"></div>
									</div>
								</div>
								<div class="headRow2">
									<div class="sex"  title="性别"></div>
									<div class="split">&nbsp;|&nbsp;</div>
									<div class="age"  title="年龄"></div>
									<div class="split">&nbsp;|&nbsp;</div>
									<div class="identityClass"  title="执业类别"></div>
									<div class="docCode"  title="工号">
										<div id="userCode" class="userCode"></div>
									</div>
									<div class="workYear layui-hide"  title="工作年限">
										<div class="years"></div>
									</div>
									<div class="skillGroup" style="display: flex;">
										<div style="margin-top: 6px;"><span>持有权限:</span></div>
										<div class="skill skill1">
											手术
											<font id="shoushu" onclick="newDoctorInfo.showRight(1)">0</font>
											项
										</div>
										<div class="skill skill4">
											处方
											<font id="chufang" onclick="newDoctorInfo.showRight(4)">0</font>
											项
										</div>
										<div class="skill skill2">
											麻醉
											<font id="mazui" onclick="newDoctorInfo.showRight(2)">0</font>
											项
										</div>
										<!-- //hwx 2023-6-25 隐藏功能 
										<div class="skill skill3">
											查房
											<font id="chafang" onclick="newDoctorInfo.showRight(3)">0</font>
											项
										</div>
										<div class="skill skill5">
											亚专业
											<font id="yazhuanye" onclick="newDoctorInfo.showRight(5)">0</font>
											项
										</div>-->
									</div>
								</div>
								<div class="headRow3">
									<div class="group"  title="组别列表">
										<div id="groupId"></div>
									</div>
									<div class="beGoodAt">
										<div class="goodText"><div class="fonts">执业范围：</div><div id="goodness" class="goodness"></div></div>
									</div>
									<div class="tel"  title="电话">
										<img src="image/phone.png">
										<div class="telNo"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				</div>
				<div id="mainBar" class="mainBar mainBar-up"></div>
				<!-- 右侧菜单 -->
				<div class="dn">
					<div class="leftMenu layui-tab" lay-filter="tabView">
						<div class="searchDiv">
							<div class="skin-btn-minor searchLeftDiv">
								<i class="layui-icon2">&#xe777;</i>
							</div>
							<div class="searchRightDiv">
								<input type="text" placeholder="分类搜索" class="layui-input searchInput" id="searchInput" maxlength="10" />
							</div>
						</div>
						<ul class="layui-tab-title" id="tabView">
							<li state="certificate">我的证件 </li>
							<li state="technicalAuthorization">技术授权</li>
							<li state="newTechNique">新技术新项目</li>
							<li state="medicalActivities">医疗活动</li>
							<li state="evaluationStatus">考评情况</li>
							<li state="rotationRecord">轮转信息</li>
							<li state="excamManage">考核管理</li>
							<li state="rewardPunish">奖励情况</li>
							<li state="behaviorRecord">医疗安全行为记录</li>
							<li state="majorsAccident">重大差错及事故处理</li>
							<li state="doctorNegative">医师负面</li>
							<li state="dosemonitoring">个人剂量检测 </li>
							<li state="personalhealth">健康情况</li>
							<li state="doctorApprove">档案审批</li>
							<li state="operationRecord">操作记录</li>
							<li state="adverseEvents">不良事件记录</li>
							<li state="complainingDispute">投诉纠纷记录</li>
							<li state="workloadStatistics">工作量统计</li>
							<li state="11" class="layui-hide" hasexport="true">表单详情</li>
						</ul>
					</div>
					<div class="tree_custom_opt" data-tag="1" title="点击隐藏">
						<i class="layui-icon2">&#xe71c;</i>
					</div>
					<div class="rightContent" id="contentID">
						<div class="layui-tab-item" id="certificate">
							<div id="certificateDiv"></div>
						</div>
						<div class="layui-tab-item" id="doctorApprove">
							<div id="doctorApproveDiv"></div>
						</div>
						<div class="layui-tab-item" id="rotationRecord">
							<div id="rotationRecordDiv"></div>
						</div>
						<div class="layui-tab-item" id="technicalAuthorization">
							<div id="technicalAuthorizationDiv"></div>
						</div>
						<div class="layui-tab-item" id="newTechNique">
							<div id="newTechNiqueDiv"></div>
						</div>
						<div class="layui-tab-item" id="medicalActivities">
							<div id="medicalActivitiesDiv"></div>
						</div>
						<div class="layui-tab-item" id="evaluationStatus">
							<div id="evaluationStatusDiv"></div>
						</div>
						<div class="layui-tab-item" id="excamManage">
							<div id="excamManageDiv"></div>
						</div>
						<div class="layui-tab-item" id="rewardPunish">
							<div id="rewardPunishDiv"></div>
						</div>
						<div class="layui-tab-item" id="behaviorRecord">
							<div id="behaviorRecordDiv"></div>
						</div>
						<div class="layui-tab-item" id="majorsAccident">
							<div id="majorsAccidentDiv"></div>
						</div>
						<div class="layui-tab-item" id="doctorNegative">
							<div id="doctorNegativeDiv"></div>
						</div>
						<div class="layui-tab-item" id="dosemonitoring">
							<div id="dosemonitoringDiv"></div>
						</div>
						<div class="layui-tab-item" id="personalhealth">
							<div id="personalhealthDiv"></div>
						</div>
						<div class="layui-tab-item" id="operationRecord">
							<div id="operationRecordDiv"></div>
						</div>
						<div class="layui-tab-item" id="adverseEvents">
							<div id="adverseEventsDiv"></div>
						</div>
						<div class="layui-tab-item" id="complainingDispute">
							<div id="complainingDisputeDiv"></div>
						</div>
						<div class="layui-tab-item" id="workloadStatistics">
							<div id="workloadStatisticsDiv"></div>
						</div>
                        <div class="layui-tab-item" id="formDetailTable">
							<div id="formDetail"></div>
						</div>
					</div>
				</div>
				<div class="rightmenu">
					<div class="certificateTop">
						<div class="certificateImg" title="隐藏我的证件"></div>
						<div class="certificateName">我的证件</div>
					</div>
					<div class="certificateBottom" id="certificateBottom"></div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<script type="text/javascript" src="js/newDoctorInfo.js?r="+Math.random()></script>
<script type="text/javascript" src="js/newDoctorInfoEditCallback.js?r="+Math.random()></script>
<script type="text/javascript" src="js/newCertificateInfo.js?r="+Math.random()></script>
<script type="text/javascript" src="js/newRightCertificateInfo.js?r="+Math.random()></script>
<script type="text/javascript" src="js/technicalAuthorization.js?r="+Math.random()></script>
<script type="text/javascript" src="js/newRewardPunish.js?r="+Math.random()></script>
<script type="text/javascript" src="../workloadStatistics/js/workloadStatistics.js?r="+Math.random()></script>
<script type="text/javascript" src="js/approval.js?r="+Math.random()></script>
<script type="text/javascript" src="js/pubBizsysLog.js?r="+Math.random()></script>
<script type="text/javascript" src="js/behaviorRecord.js?r="+Math.random()></script>
<script type="text/javascript" src="js/majorsAccident.js?r="+Math.random()></script>
<script type="text/javascript" src="js/myNewProjectAndTechnology.js?r="+Math.random()></script>
<!-- 自定义详情组件 -->
<script type="text/javascript" src="../../../plugins/components/commonExportUtil/js/commonExportUtil.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/mdmsCustomDetail/js/otherFormDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="../mdmsCustomList/js/mdmBusinessList.js?r="+Math.random()></script>
<script type="text/javascript" src="../newTechniqueManager/js/routineCheckList.js?r="+Math.random()></script>
<script type="text/javascript" src="../newTechniqueManager/js/baseExamList.js?r="+Math.random()></script>
<script type="text/javascript" src="../newTechniqueManager/js/personalExamList.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/functionModule/base/js/changeForm.js?r="+Math.random()></script>
<!-- 表单组件 -->
<script type="text/javascript" src="../../../frame/customForm/js/getCustomFormDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../frame/customForm/js/initCustomFormTemplate.js?r="+Math.random()></script>
<!-- 自定义详情组件 -->
<script type="text/javascript" src="../mdmsCustomDetail/js/initCustomDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="js/rotationRecord.js?r="+Math.random()></script>
<script type="text/javascript" src="js/doctorNegative.js?r="+Math.random()></script>
<script type="text/javascript" src="js/importOut.js?r="+Math.random()></script>
<script type="text/javascript" src="js/customCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/saveMdmsCustomFormFilled.js?r="+Math.random()></script>


<script type="text/javascript">
	$(function() {
		newDoctorInfo.init();
	});
</script>