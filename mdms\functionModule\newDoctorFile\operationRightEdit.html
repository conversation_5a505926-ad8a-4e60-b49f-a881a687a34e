
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/operationRightEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="customFormFilledCode">
		<input type="hidden" name="funCode">
		<!-- 权限类型 99 手术 1处方 2麻醉 3 查房 -->
		<input type="hidden" name="authType">
		<input type="hidden" name="operationRightId">
		<input type="hidden" name="userCode">
		<input type="hidden" name="isValid" >
		<input type="hidden" name="operationName">
		<input type="hidden" name="deptOperationId">
		<input type="hidden" name="rightType" value="SQLXSD">
		<!-- 日志类型（1新增2暂停3恢复4回收5到期） -->
		<input type="hidden" name="type">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" id="saveBtn" class="layui-btn layui-btn-sm" value="授权" lay-submit lay-filter="save" />
				<input type="button" class="layui-btn layui-btn-sm" value="关闭" onclick="operationRightEdit.closebutton()" />
			</div>
		</div>
		<div class="bodys">
		<form class="layui-form" lay-filter="filterParam" method="post">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>授权开始时间
					</label>
					<div class="layui-input-inline">
						<input type="text" id="createTime" name="createTime" placeholder="请选择授权时间" lay-verify="required" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>授权结束时间
					</label>
					<div class="layui-input-inline">
						<input type="text" id="createEndTime" name="createEndTime" placeholder="请选择授权结束时间" lay-verify="required"  autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>说明
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="required|limit" limit="500" style="width:520px;" name="reason" value="" class="layui-textarea"></textarea>
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/operationRightEdit.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		operationRightEdit.init();
	});
</script>