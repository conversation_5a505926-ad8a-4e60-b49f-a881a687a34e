<!DOCTYPE html>
<html>
<head>
<title>授权管理</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/filterSearch/filterSearch.css">
<link rel="stylesheet" type="text/css" href="css/rightAddList.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post" onsubmit="return false">
		<input type="hidden" name="funCode">
		<input type="hidden" name="state" value="1">
		<input type="hidden" name="type" value="">
		<input type="hidden" name="findUserCode" value="">
		<input type="hidden" name="docUserCode" value="">
		<input type="hidden" name="findCompNo" value="">
		<input type="hidden" name="status" value="99">
		<input type="hidden" name="customFormFilledCode" value="0">
	</form>
	<div class="bodys layui-form">
		<form class="layui-form" lay-filter="filterParam" method="post" onsubmit="return false">
			<div class="layui-input-inline h28 lh28" style="left: 20px;">
				<input class="layui-input" style="width: 200px; display: inline;" placeholder="关键字查询" type="text" name="keyword" autocomplete="off">
			</div>
			<div class="layui-input-inline h28 lh28" style="left: 90px;">
				<button type="button" class="layui-btn layui-btn-sm" onclick="rightAddList.query()">查询</button>
			</div>
		</form>
		<div class="layui-row">
			<div class="tableDiv table_noTree">
				<div id="list" lay-filter="list"></div>
			</div>
		</div>
	</div>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="js/rightAddList.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		rightAddList.init();
	});
</script>
</html>