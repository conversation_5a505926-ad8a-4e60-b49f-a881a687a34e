<!DOCTYPE html>
<html>
<head>
<title>PDF预览</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" method="post"  onsubmit="return false">
		<input type="hidden" name="list">
		<div class="layui-layer-btn"><span style="line-height: 35px;">共 <span id="showNum">0</span> 份</span><a class="layui-layer-btn0" style="border-radius: 20px;" onclick="last()">上一份</a><a class="layui-layer-btn0" style="border-radius: 20px;" onclick="next()">下一份</a></div>
		<iframe style="border:0;height:100%;" width="100%" id="showPDFFrame"></iframe>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/filterSearch/filterSearch.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<script type="text/javascript">
	var list=[];
	var i=0;
	$(function() {
		list= JSON.parse(decodeURIComponent(param.get("list")));
		if(list[i].type == "PDF"){
			$("#showNum").html(list.length);
			showDetail(list[i].url);
		}else{
			pubUploader.preview(list[i].imgName,list[i].src);
		}
	});
	
	function showDetail(url){
		//hwx 2023-6-13 修改档案证件在iframe显示
		$("#showPDFFrame").attr("src",basePath + "mdms/functionModule/medicalDocManager/toShowDocument.html?fileName=" + url);
		$("#showPDFFrame").height($(window).height()-60);
	}
	
	function next(){
		if(i!=list.length-1){
			i=i+1;
			if(list[i].type == "PDF"){
				showDetail(list[i].url);
			}else{
				pubUploader.preview(list[i].imgName,list[i].src);
			}
		}else{
			i=0;
			if(list[i].type == "PDF"){
				showDetail(list[i].url);
			}else{
				pubUploader.preview(list[i].imgName,list[i].src);
			}
		}
	}
	function last(){
		if(i!=0){
			i=i-1;
			if(list[i].type == "PDF"){
				showDetail(list[i].url);
			}else{
				pubUploader.preview(list[i].imgName,list[i].src);
			}
		}else{
			i=list.length-1;
			if(list[i].type == "PDF"){
				showDetail(list[i].url);
			}else{
				pubUploader.preview(list[i].imgName,list[i].src);
			}
		}
	}

	function close(){
		assemblys.closeWindow();
	}
</script>