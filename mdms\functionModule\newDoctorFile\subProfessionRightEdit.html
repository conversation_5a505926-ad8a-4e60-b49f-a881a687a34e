<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/subProfessionRightEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="customFormFilledCode">
		<input type="hidden" name="funCode">
		<!-- 权限类型 99 手术 1处方 2麻醉 3 查房 4亚专业-->
		<input type="hidden" name="authType">
		<input type="hidden" name="deptSubProfessionCode">
		<input type="hidden" name="userCode">
		<input type="hidden" name="isValid">
		<input type="hidden" name="subProClassName">
		<input type="hidden" name="subProfessionRightID">
		<!-- 日志类型（1新增2暂停3恢复4回收5到期） -->
		<input type="hidden" name="type">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" id="saveBtn" class="layui-btn layui-btn-sm" value="授权" lay-submit lay-filter="save" />
				<input type="button" class="layui-btn layui-btn-sm layui-bg-black" value="关闭" onclick="assemblys.closeWindow();" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						能力水平
					</label>
					<div class="layui-input-inline">
						<select name="level" id="level" lay-verify="required" lay-filter="level"></select>
 					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						授权开始时间
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" placeholder="请选择授权开始时间" name="createTime" value="" class="layui-input" autocomplete="off" laydate/>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						授权结束时间
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" placeholder="请选择授权结束时间" name="createEndTime" value="" class="layui-input" autocomplete="off" laydate/>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red">*</span>说明
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="required|limit" limit="500" style="width:520px;" name="reason" value="" class="layui-textarea"></textarea>
					</div>
				</div>
			</div>	
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="js/subProfessionRightEdit.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		subProfessionRightEdit.init();
	});
</script>