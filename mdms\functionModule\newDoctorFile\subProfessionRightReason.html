<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>原因说明</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="subProClassName">
		<input type="hidden" name="customFormFilledCode">
		<input type="hidden" name="subProfessionRightID">
		<input type="hidden" name="authType">
		<input type="hidden" name="isValid">
		<input type="hidden" name="userCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" id="savebtn" class="layui-btn layui-btn-sm" value="回收" lay-submit lay-filter="save" />
				<input type="button" class="layui-btn layui-btn-sm" value="关闭" onclick="subProfessionRightReason.closebutton()" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				<div class="layui-form-item">
					<label class="layui-form-label" style="width:80px">
						<span style="color: red">*</span>说明
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="required|limit" limit="500" style="width:520px;" name="reason" value="" class="layui-textarea showReason"></textarea>
					</div>
				</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/rightAddList.js?r="+Math.random()></script>
<script type="text/javascript" src="js/subProfessionRightReason.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		subProfessionRightReason.init();
	});
</script>