var saveCallback = {
	// 保存方法
	save : function(win, cwind) {
		var saveState = cwind.saveState;
		var customFormFilledCode = cwind.customFormFilled.customFormFilledCode;
		var customFormFilledID = cwind.customFormFilled.customFormFilledID;
		var createUserCode = cwind.customFormFilled.createUserCode;
		var createUserName = cwind.customFormFilled.createUserName;
		var createDate = cwind.customFormFilled.createDate;
		var saveName = "保存成功！";
		if (saveState == 1) {
			saveName = "提交成功！";
		}
		assemblys.msg(saveName, function() {
			if (win.customFormTemplate.isAutoSave == undefined || win.customFormTemplate.isAutoSave == 0) {
				parent.mdmsCustomList.initTable();
				assemblys.closeWindow();
			} else {
				if (saveState == 0) {
					win.$("input[name='customFormFilledCode']").val(customFormFilledCode);
					win.$("input[name='customFormFilledID']").val(customFormFilledID);
					win.$("input[name='createUserCode']").val(createUserCode);
					win.$("input[name='createUserName']").val(createUserName);
					win.$("input[name='createDate']").val(createDate);
					win.$("input[name='status']").val("0");
					win.isSubmit = false;
					win.customFormTemplate.initSaveTimer();
				}
			}
		});
	}
//	save : function(data) {
//		
//		assemblys.msg("保存成功", function() {
//			//如果是医师管理提交新项目，直接改状态为已完成
//			if(parent.$("input[name='viewStatus']").val()==2){
//				if(data.saveState==1){
//					var customFormFilledCode=data.customFormFilled.customFormFilledCode;
//					alert("这里调用一件审核审核功能！"+customFormFilledCode);
//				}
//			}
//			// 出发父级查询
//			parent.newProjectAndTechnology.getFormList();
//			// 关闭tab
//			assemblys.closeWindow();
//		});
//	},
//	changeComplate:function(customFormFilledCode){
//		
//	}
}
var showSaveMsg = "asdfadf";
var saveBeforeCallback = {
	saveBefore : function(win) {
		return "";
	}
}
//hwx 2022-02-10 打开表单后的处理
var initObj = {
	// 保存方法
	init : function(win) {
//		alert(win.$("[customfieldbusinesscode='xingming3']").val());
		
	}
}
