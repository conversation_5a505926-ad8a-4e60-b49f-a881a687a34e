var saveCallback = {
	// 保存方法
	save : function(win, cwind) {
		var saveState = cwind.saveState;
		var customFormFilledCode = cwind.customFormFilled.customFormFilledCode;
		var customFormFilledID = cwind.customFormFilled.customFormFilledID;
		var createUserCode = cwind.customFormFilled.createUserCode;
		var createUserName = cwind.customFormFilled.createUserName;
		var createDate = cwind.customFormFilled.createDate;
		var saveName = "保存成功！";
		if (saveState == 1) {
			saveName = "提交成功！";
		}
		//hwx 2024年6月5日下午3:57:16 如果保存草稿调用方法建立主子表关系
		var prevCustomFormFilledCode = param.get("prevCustomFormFilledCode");
		if (prevCustomFormFilledCode && win.param.get("customFormBusinessCode") == assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YQ) {
			$.ajax({
				url : basePath + "mdms/functionModule/newProjectAndTechnology/saveNewptrelation.spring",
				data : {
					"customFormFilledCode" : customFormFilledCode,
					"preCustomFormFilledCode" : prevCustomFormFilledCode,
					"type" : 1
				},
				type : "POST",
				dataType : "json",
				async : false,
				success : function() {
					assemblys.msg(saveName, function() {
						if (win.customFormTemplate.isAutoSave == undefined || win.customFormTemplate.isAutoSave == 0) {
							assemblys.closeWindow();
							window.location.reload();
							parent.$("#tab").find("li:contains(延期申请信息)").click();
						} else {
							if (saveState == 0) {
								win.$("input[name='customFormFilledCode']").val(customFormFilledCode);
								win.$("input[name='customFormFilledID']").val(customFormFilledID);
								win.$("input[name='createUserCode']").val(createUserCode);
								win.$("input[name='createUserName']").val(createUserName);
								win.$("input[name='createDate']").val(createDate);
								win.$("input[name='status']").val("0");
								win.isSubmit = false;
								win.customFormTemplate.initSaveTimer();
							}
						}
					});
				}
			});
		}
	}
}