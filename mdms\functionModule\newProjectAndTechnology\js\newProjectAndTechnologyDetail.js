// 渲染层
var newProjectAndTechnologyDetail = {
	newProjectAndTechnology : function(businessCode, status) {
		//hwx 2022-12-06 补充显示历史单据信息
		newProjectAndTechnologyDetail.newProjectAndTechnologyZcg(businessCode, status);
		if (businessCode != assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_XXMJS) {
			return false;
		}
		var $buttonList = [];
		if (param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_MDMS_LAUNCH && (status == 88 || status == 55)) {//hwx 2022-10-09 修改开展才能转常规或者延期或者终止
			var jdpjRight = mdmBusinessList.getPermission(assemblys.top.mdms.mdmsConstant.MDMS_STAGEEV_ALUATION);
			if ((param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_MDMS_LAUNCH || param.get("funCode") == assemblys.top.mdms.mdmsConstant.FUN_POINT_NAME) && jdpjRight.hasView) {
				$buttonList.push({
					"title" : "阶段评价",
					"className" : "skin-btn-minor",
					"onclick" : function() {
						newProjectAndTechnologyDetail.toStageEvaluation();
					}
				});
			}
			//hwx 2024年6月5日下午3:12:17 延期获取功能权限
			var yqRight = mdmBusinessList.getPermission(assemblys.top.mdms.mdmsConstant.FUN_MDM_EXTENSION);
			var zcgRight = mdmBusinessList.getPermission(assemblys.top.mdms.mdmsConstant.FUN_CONVERSION_CONVENTIONAL);
			var zzRight = mdmBusinessList.getPermission(assemblys.top.mdms.mdmsConstant.FUN_MDMS_SUSPENSION);
			if (yqRight.hasAdd) {
				$buttonList.push({
					"title" : "延期",
					"className" : "skin-btn-minor",
					"onclick" : function() {
						newProjectAndTechnologyDetail.toYq();
					}
				});
			}
			if (zcgRight.hasAdd) {
				$buttonList.push({
					"title" : "转常规",
					"className" : "skin-btn-minor",
					"onclick" : function() {
						newProjectAndTechnologyDetail.toCommon();
					}
				});
			}
			if (zzRight.hasAdd) {
				$buttonList.push({
					"title" : "终止",
					"className" : "skin-btn-minor",
					"onclick" : function() {
						newProjectAndTechnologyDetail.toZz();
					}
				});
			}
		}
		window.buttonList = $buttonList;
	},
	newProjectAndTechnologyZcg : function(businessCode, status) {
		if (businessCode != assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_XXMJS && businessCode != assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_ZCG && businessCode != assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YQ && businessCode != assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_ZZ) {
			return false;
		}
		var prevCustomFormFilledCode = param.get("prevCustomFormFilledCode");
		var prevCustomFormCode = param.get("prevCustomFormCode");
		var prevCustomFormBusinessCode = param.get("prevCustomFormBusinessCode");
		
		var zcgBusinessCode = param.get("zcgBusinessCode");
		var zcgCustomFormCode = param.get("zcgCustomFormCode");
		var zcgCustomFormBusinessCode = param.get("zcgCustomFormBusinessCode");
		
		var yqBusinessCode = param.get("yqBusinessCode");
		var yqCustomFormCode = param.get("yqCustomFormCode");
		var yqCustomFormBusinessCode = param.get("yqCustomFormBusinessCode");
		
		var zzBusinessCode = param.get("zzBusinessCode");
		var zzCustomFormCode = param.get("zzCustomFormCode");
		var zzCustomFormBusinessCode = param.get("zzCustomFormBusinessCode");
		if (prevCustomFormCode != undefined && prevCustomFormCode != "") {
			window.tabList.push({
				contentID : "formDetail2",
				title : "原申请信息",
				style : {
					"background-color" : "#F2F2F2"
				},
				hasExport : 1,
				callback : function(data) {
					newProjectAndTechnologyDetail.initNewProjectAndTechnologyFormDetail(data.contentID, prevCustomFormCode, prevCustomFormFilledCode, prevCustomFormBusinessCode);
				}
			});
		}
		//hwx 2024年6月14日下午3:31:02 修改延期的contentID
		if (businessCode == assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_XXMJS) {
			if (yqBusinessCode != "" && yqBusinessCode != undefined) {
				if (yqBusinessCode.indexOf(",") > 0) {
					var yqBusinessCodes = yqBusinessCode.split(",");
					for (var i = 0; i < yqBusinessCodes.length; i++) {
						var yqCustomFormFilledCode = yqBusinessCodes[i];
						window.tabList.push({
							contentID : "formYqDetail" + i,
							title : "延期申请信息" + (i + 1) + " - ",
							style : {
								"background-color" : "#F2F2F2"
							},
							data : {
								"yqCustomFormFilledCode" : yqCustomFormFilledCode,
								"yqCustomFormCode" : yqCustomFormCode,
								"yqCustomFormBusinessCode" : yqCustomFormBusinessCode
							},
							hasExport : 1,
							callback : function(data) {
								newProjectAndTechnologyDetail.initNewProjectAndTechnologyFormDetail(data.contentID, data.yqCustomFormCode, data.yqCustomFormFilledCode, data.yqCustomFormBusinessCode);
							}
						});
					}
				} else {
					window.tabList.push({
						contentID : "formYqDetail",
						title : "延期申请信息",
						style : {
							"background-color" : "#F2F2F2"
						},
						hasExport : 1,
						callback : function(data) {
							newProjectAndTechnologyDetail.initNewProjectAndTechnologyFormDetail(data.contentID, yqCustomFormCode, yqBusinessCode, yqCustomFormBusinessCode);
						}
					});
				}
			}
			if (zcgBusinessCode != "" && zcgBusinessCode != undefined) {
				window.tabList.push({
					contentID : "formZcgDetail",
					title : "转常规申请信息",
					style : {
						"background-color" : "#F2F2F2"
					},
					hasExport : 1,
					callback : function(data) {
						newProjectAndTechnologyDetail.initNewProjectAndTechnologyFormDetail(data.contentID, zcgCustomFormCode, zcgBusinessCode, zcgCustomFormBusinessCode);
					}
				});
			}
			if (zzBusinessCode != "" && zzBusinessCode != undefined) {
				window.tabList.push({
					contentID : "formZzDetail",
					title : "终止申请信息",
					style : {
						"background-color" : "#F2F2F2"
					},
					hasExport : 1,
					callback : function(data) {
						newProjectAndTechnologyDetail.initNewProjectAndTechnologyFormDetail(data.contentID, zzCustomFormCode, zzBusinessCode, zzCustomFormBusinessCode);
					}
				});
			}
		}
	},
	// 获取原新项目、新技术表单详情
	initNewProjectAndTechnologyFormDetail : function(contentID, prevCustomFormCode, prevCustomFormFilledCode, prevCustomFormBusinessCode) {
		var customFormParam = {
			"appCode" : param.get("appCode"),
			"compNo" : param.get("compNo"),
			"customFormCode" : prevCustomFormCode,
			"customFormFilledCode" : prevCustomFormFilledCode,
			"customFormBusinessCode" : prevCustomFormBusinessCode,
			"hasAtta" : "after",
			"dom" : contentID
		};
		getCustomFormDetail.getCustomFormData(customFormParam).then(function() {
			newProjectAndTechnologyDetail.initZcgApprovalList(contentID, prevCustomFormFilledCode);
		});
	},
	// 转常规审核记录
	initZcgApprovalList : function(contentID, approvalBelongCode) {
		approvalFlow.getApprovalBelongFlowNodeRecordList({
			"appCode" : param.get("appCode"),
			"approvalBelongCode" : approvalBelongCode,
			"selector" : "#" + contentID
		});
	},
	toCommon : function() {
		var compNo = param.get("compNo");
		var appCode = param.get("appCode");
		var prevCustomFormCode = param.get("customFormCode");
		var prevCustomFormFilledCode = param.get("customFormFilledCode");
		var customApprovalFlowCode = param.get("customApprovalFlowCode");
		var customApprovalFlowBusinessCode = param.get("CustomApprovalFlowBusinessCode");
		var zcgBusinessCode = assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_ZCG;
		// 跳转自定义表单路径
		var url = basePath + "mdms/functionModule/newProjectAndTechnology/newProjectAndTechnologyZcgEdit.html";
		url += "?customFormBusinessCode=" + zcgBusinessCode;
		url += "&funCode=" + param.get("funCode");
		url += "&compNo=" + compNo;
		url += "&appCode=" + appCode;
		url += "&type=1&prevCustomFormCode=" + prevCustomFormCode;
		url += "&prevCustomFormFilledCode=" + prevCustomFormFilledCode;
		url += "&customApprovalFlowCode=" + customApprovalFlowCode;
		url += "&CustomApprovalFlowBusinessCode" + customApprovalFlowBusinessCode;
		// 弹窗方法
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '转常规申请',
			maxmin : true,
			area : [ '98%', '98%' ], // 设置弹窗打开大小
			content : url
		});
	},
	toYq : function() {
		var compNo = param.get("compNo");
		var appCode = param.get("appCode");
		var prevCustomFormCode = param.get("customFormCode");
		var prevCustomFormFilledCode = param.get("customFormFilledCode");
		var customApprovalFlowCode = param.get("customApprovalFlowCode");
		var customApprovalFlowBusinessCode = param.get("CustomApprovalFlowBusinessCode");
		var yqBusinessCode = assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_YQ;
		var title = "延期申请";
		var url = basePath + "mdms/functionModule/newProjectAndTechnology/newProjectAndTechnologyYqEdit.html";
		url += "?customFormBusinessCode=" + yqBusinessCode;
		url += "&funCode=" + param.get("funCode");
		url += "&compNo=" + compNo;
		url += "&appCode=" + appCode;
		url += "&type=1&prevCustomFormCode=" + prevCustomFormCode;
		url += "&prevCustomFormFilledCode=" + prevCustomFormFilledCode;
		url += "&customApprovalFlowCode=" + customApprovalFlowCode;
		url += "&CustomApprovalFlowBusinessCode" + customApprovalFlowBusinessCode;
		// 弹窗方法
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : title,
			maxmin : true,
			area : [ '98%', '98%' ], // 设置弹窗打开大小
			content : url
		});
	},
	toZz : function() {
		var compNo = param.get("compNo");
		var appCode = param.get("appCode");
		var prevCustomFormCode = param.get("customFormCode");
		var prevCustomFormFilledCode = param.get("customFormFilledCode");
		var customApprovalFlowCode = param.get("customApprovalFlowCode");
		var customApprovalFlowBusinessCode = param.get("CustomApprovalFlowBusinessCode");
		var zzBusinessCode = assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_ZZ;
		var title = "终止申请";
		var url = basePath + "mdms/functionModule/newProjectAndTechnology/newProjectAndTechnologyZzEdit.html";
		url += "?customFormBusinessCode=" + zzBusinessCode;
		url += "&funCode=" + param.get("funCode");
		url += "&compNo=" + compNo;
		url += "&appCode=" + appCode;
		url += "&type=1&prevCustomFormCode=" + prevCustomFormCode;
		url += "&prevCustomFormFilledCode=" + prevCustomFormFilledCode;
		url += "&customApprovalFlowCode=" + customApprovalFlowCode;
		url += "&CustomApprovalFlowBusinessCode" + customApprovalFlowBusinessCode;
		// 弹窗方法
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : title,
			maxmin : true,
			area : [ '98%', '98%' ], // 设置弹窗打开大小
			content : url
		});
	},
	toStageEvaluation : function() {
		var compNo = param.get("compNo");
		var appCode = param.get("appCode");
		var prevCustomFormCode = param.get("customFormCode");
		var prevCustomFormFilledCode = param.get("customFormFilledCode");
		var jdpjBusinessCode = assemblys.top.mdms.mdmsConstant.CUSTOM_FORM_STAGEEV_ALUATION;
		var title = "阶段评价";
		var url = basePath + "mdms/functionModule/newProjectAndTechnology/newProjectAndTechnologyZcgEdit.html";
		url += "?customFormBusinessCode=" + jdpjBusinessCode;
		url += "&funCode=" + param.get("funCode");
		url += "&compNo=" + compNo;
		url += "&appCode=" + appCode;
		url += "&type=1&prevCustomFormCode=" + prevCustomFormCode;
		url += "&prevCustomFormFilledCode=" + prevCustomFormFilledCode;
		// 弹窗方法
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : title,
			maxmin : true,
			area : [ '98%', '98%' ], // 设置弹窗打开大小
			content : url
		});
	}
}
