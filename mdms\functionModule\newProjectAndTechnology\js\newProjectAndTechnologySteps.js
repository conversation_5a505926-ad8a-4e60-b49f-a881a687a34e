var newProjectAndTechnologySteps = {
	$dataArr : [],
	$step : 0,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]")).then(function() {
			$("span[titleName]").text("进度追踪");
			if (param.get("createDate")) {//步骤1创建
				newProjectAndTechnologySteps.$step = 1;
				newProjectAndTechnologySteps.$dataArr.push({
					'title' : "申请",
					"desc" : param.get("createDate")
				});
			}
			newProjectAndTechnologySteps.getApproveNodeList(param.get("customFormFilledCode")).then(function(data) {
				var status = Number(param.get("status"));
				var createDate = data.data.approvalBelongFlowNodeRecordList.length > 0 ? data.data.approvalBelongFlowNodeRecordList[0].ApprovalUserName + "-" + assemblys.dateToStr(data.data.approvalBelongFlowNodeRecordList[0].CreateDate) : "";
				switch (status) {
				case 1:
					newProjectAndTechnologySteps.$dataArr.push({
						'title' : "审核中",
						"desc" : "&nbsp;"
					});
					newProjectAndTechnologySteps.$step = 1;
					newProjectAndTechnologySteps.getStep(2);
					newProjectAndTechnologySteps.showData();
					break;
				case 88:
					newProjectAndTechnologySteps.$dataArr.push({
						'title' : "审核中",
						"desc" : "&nbsp;"
					}, {
						'title' : "已通过开展中",
						"desc" : createDate ? createDate : "&nbsp;"
					});
					newProjectAndTechnologySteps.getStep(3);
					newProjectAndTechnologySteps.$step = 3;
					newProjectAndTechnologySteps.showData();
					break;
				case 33:
					newProjectAndTechnologySteps.getotherApproveNotList(createDate);
					break;
				case 55:
					newProjectAndTechnologySteps.getotherApproveNotList(createDate);
					break;
				case 22:
					newProjectAndTechnologySteps.getotherApproveNotList(createDate);
					break;
				case 44:
					newProjectAndTechnologySteps.getotherApproveNotList(createDate);
					break;
				case 11:
					newProjectAndTechnologySteps.getotherApproveNotList(createDate);
					break;
				case 66:
					newProjectAndTechnologySteps.getotherApproveNotList(createDate);
					break;
				case 99:
					newProjectAndTechnologySteps.getotherApproveNotList(createDate);
					break;
				}
			});
		});
	},
	getStep : function(tag) {
		if (tag == 2) {
			newProjectAndTechnologySteps.$dataArr.push({
				'title' : "已通过开展中",
				"desc" : "&nbsp;"
			});
		}
		newProjectAndTechnologySteps.$dataArr.push({
			'title' : "延期/终止/转常规",
			"desc" : "&nbsp;",
			"isMultiple" : "1"
		});
	},
	getotherApproveNotList : function(createDate) {
		newProjectAndTechnologySteps.$dataArr.push({
			'title' : "审核中",
			"desc" : "&nbsp;"
		}, {
			'title' : "已通过开展中",
			"desc" : createDate ? createDate : "&nbsp;"
		});
		newProjectAndTechnologySteps.getData().then(function(dd) {
			if (dd && dd.dataList) {
				var $tag = 1;
				for (var i = 0; i < dd.dataList.length; i++) {
					var yqCustomFormFilledCode = dd.dataList[i].NewPtZYqCode;
					if (yqCustomFormFilledCode) {
						var $title = "延期审核中";
						var $title2 = "已延期";
						if (dd.dataList.length == 1) {
							newProjectAndTechnologySteps.getFormDetail(yqCustomFormFilledCode).then(function(yqData) {
								var yqStatus = yqData.data[0].Status;
								newProjectAndTechnologySteps.getApproveNodeList(yqCustomFormFilledCode).then(function(yq) {
									var yqDate = yq.data.approvalBelongFlowNodeRecordList.length > 0 ? yq.data.approvalBelongFlowNodeRecordList[0].ApprovalUserName + "-" + assemblys.dateToStr(yq.data.approvalBelongFlowNodeRecordList[0].CreateDate) : "";
									newProjectAndTechnologySteps.$dataArr.push({
										'title' : $title,
										"desc" : "&nbsp;"
									});
									newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length - 1;
									if (yqStatus == 99) {
										newProjectAndTechnologySteps.$dataArr.push({
											'title' : $title2,
											"desc" : yqDate ? yqDate : "&nbsp;"
										});
										newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length;
										newProjectAndTechnologySteps.$dataArr.push({
											'title' : "延期/终止/转常规",
											"desc" : "&nbsp;",
											"isMultiple" : "1"
										});
									} else {
										newProjectAndTechnologySteps.$dataArr.push({
											'title' : "已延期",
											"desc" : "&nbsp;"
										}, {
											'title' : "延期/终止/转常规",
											"desc" : "&nbsp;",
											"isMultiple" : "1"
										});
									}
								});
							});
						} else if (dd.dataList.length > 1) {
							$title = "延期" + $tag + "审核中";
							$title2 = "已延期" + $tag;
							newProjectAndTechnologySteps.getFormDetail(yqCustomFormFilledCode).then(function(yqData) {
								var yqStatus = yqData.data[0].Status;
								newProjectAndTechnologySteps.getApproveNodeList(yqCustomFormFilledCode).then(function(yq) {
									var yqDate = yq.data.approvalBelongFlowNodeRecordList.length > 0 ? yq.data.approvalBelongFlowNodeRecordList[0].ApprovalUserName + "-" + assemblys.dateToStr(yq.data.approvalBelongFlowNodeRecordList[0].CreateDate) : "";
									newProjectAndTechnologySteps.$dataArr.push({
										'title' : $title,
										"desc" : "&nbsp;"
									});
									newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length - 1;
									if (yqStatus == 99) {
										newProjectAndTechnologySteps.$dataArr.push({
											'title' : $title2,
											"desc" : yqDate ? yqDate : "&nbsp;"
										});
										newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length + 1;
									} else {
										newProjectAndTechnologySteps.$dataArr.push({
											'title' : "延期/终止/转常规",
											"desc" : "&nbsp;",
											"isMultiple" : "1"
										});
									}
								});
							});
							$tag++;
						}
					}
				}
				for (var i = 0; i < dd.dataList.length; i++) {
					var zzCustomFormFilledCode = dd.dataList[i].NewPtZzCode;
					if (zzCustomFormFilledCode) {
						newProjectAndTechnologySteps.getFormDetail(zzCustomFormFilledCode).then(function(zzData) {
							var zzStatus = zzData.data[0].Status;
							newProjectAndTechnologySteps.getApproveNodeList(zzCustomFormFilledCode).then(function(zz) {
								var zzDate = zz.data.approvalBelongFlowNodeRecordList.length > 0 ? zz.data.approvalBelongFlowNodeRecordList[0].ApprovalUserName + "-" + assemblys.dateToStr(zz.data.approvalBelongFlowNodeRecordList[0].CreateDate) : "";
								newProjectAndTechnologySteps.$dataArr.push({
									'title' : "终止审核中",
									"desc" : "&nbsp;"
								});
								newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length - 1;
								if (zzStatus == 99) {
									newProjectAndTechnologySteps.$dataArr.push({
										'title' : "已终止",
										"desc" : zzDate ? zzDate : "&nbsp;"
									});
									newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length + 1;
								} else {
									newProjectAndTechnologySteps.$dataArr.push({
										'title' : "已终止",
										"desc" : "&nbsp;"
									});
								}
							});
						});
					}
				}
				for (var i = 0; i < dd.dataList.length; i++) {
					var zcgCustomFormFilledCode = dd.dataList[i].NewPtZcgCode;
					if (zcgCustomFormFilledCode) {
						newProjectAndTechnologySteps.getFormDetail(zcgCustomFormFilledCode).then(function(zcgData) {
							var zcgStatus = zcgData.data[0].Status;
							newProjectAndTechnologySteps.getApproveNodeList(zcgCustomFormFilledCode).then(function(zcg) {
								var zcgDate = zcg.data.approvalBelongFlowNodeRecordList.length > 0 ? zcg.data.approvalBelongFlowNodeRecordList[0].ApprovalUserName + "-" + assemblys.dateToStr(zcg.data.approvalBelongFlowNodeRecordList[0].CreateDate) : "";
								newProjectAndTechnologySteps.$dataArr.push({
									'title' : "转常规审核中",
									"desc" : "&nbsp;"
								});
								newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length - 1;
								if (zcgStatus == 99) {
									newProjectAndTechnologySteps.$dataArr.push({
										'title' : "已转常规",
										"desc" : zcgDate ? zcgDate : "&nbsp;"
									});
									newProjectAndTechnologySteps.$step = newProjectAndTechnologySteps.$dataArr.length + 1;
								} else {
									newProjectAndTechnologySteps.$dataArr.push({
										'title' : "已转常规",
										"desc" : "&nbsp;"
									});
								}
							});
						});
					}
				}
			}
		}).then(function() {
			newProjectAndTechnologySteps.showData();
		});
	},
	getFormDetail : function(customFormFilledCode) {
		return $.ajax({
			url : basePath + "mdms/functionModule/newProjectAndTechnology/getCustomFormFilled.spring",
			type : "get",
			async : false,
			data : {
				customFormFilledCode : customFormFilledCode
			},
			dataType : "json",
			success : function(data) {
				return data;
			}
		});
	},
	getApproveNodeList : function(customFormFilledCode) {
		return $.ajax({
			url : basePath + "frame/approvalFlowRecord/getApprovalBelongFlowNodeRecordList.spring",
			data : {
				approvalBelongCode : customFormFilledCode,
				appCode : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			},
			dataType : "json",
			type : "GET",
			async : false,
			skipDataCheck : true,
			success : function(data) {
				return data;
			}
		});
	},
	getData : function() {
		return $.ajax({
			url : basePath + "mdms/functionModule/newProjectAndTechnology/getStepsByCustomFormFilledCode.spring",
			data : {
				customFormFilledCode : param.get("customFormFilledCode")
			}
		}).then(function(data) {
			return data;
		});
	},
	showData : function(data) {
		layui.use('steps', function() {
			var steps = layui.steps;
			var data = newProjectAndTechnologySteps.$dataArr;
			steps.make(data, '#steps', newProjectAndTechnologySteps.$step);
		});
	}
}
newProjectAndTechnologySteps.init();