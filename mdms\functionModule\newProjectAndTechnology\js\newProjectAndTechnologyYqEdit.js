var newProjectAndTechnology = {
	// 初始化
	initIframe : function() {
		
		var compNo = param.get("compNo");
		var appCode = param.get("appCode");
		var businessCode = param.get("customFormBusinessCode");
		var customFormCode = param.get("customFormCode");
		var customFormFilledCode = param.get("customFormFilledCode");
		var customApprovalFlowCode = param.get("customApprovalFlowCode");
		var url = "";
		if (customFormCode == "") {//新增页面 hwx 2020-12-18
			url = basePath + "frame/customForm/customFormTemplate.html?customFormBusinessCode=" + businessCode + "&compNo=" + parent.param.get("customFormCompNo") + "&appCode=" + appCode + "&type=1&hasSave=2&hasBack=0&customApprovalFlowCode=" + customApprovalFlowCode;
		} else {//编辑页面 hwx2020-12-18
			url = basePath + "frame/customForm/customFormTemplate.html?customFormCode=" + customFormCode + "&customFormFilledCode=" + customFormFilledCode + "&appCode=" + appCode + "&compNo=" + compNo + "&type=2&hasBack=0&customApprovalFlowCode=" + customApprovalFlowCode;
		}
		// 设置iframe高度
		$("#report").css("height", $(window).height() - 30);
		$("#report").attr("src", url);
		
	}
}
$(function() {
	newProjectAndTechnology.initIframe();
});