var newProjectAndTechnology = {
	
	// 状态
	statusMapping : {},
	
	// 初始化
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			assemblys.initSessionStoragePram();
			// 过滤条件
			return newProjectAndTechnology.getParamInfo();
		}).then(function() {
			assemblys.initSessionStoragePram();
			return newProjectAndTechnology.getFormList();
		}).then(function(data) {
			newProjectAndTechnology.initButton();
			newProjectAndTechnology.initLayuiForm(data);
			$("div.head0").removeClass("layui-hide");
		});
		
	},
	initButton : function() {
		// 设置选中的tab
		var status = param.get("status");
		$("div[lay-filter=docDemoTabBrief]").find("li[status='" + status + "']").addClass("layui-this");
		$("div[lay-filter=docDemoTabBrief]").find("li[status='" + status + "']").siblings().removeClass("layui-this");
		var url = basePath + "mdms/utils/custormFieldSetting/customFieldSetting.html?funCode=" + param.get("funCode") + "&businessCode=" + param.get("businessCode") + "&packageName=functionModule/newProjectAndTechnology&parentObject=newProjectAndTechnology";
		$("#customFieldSetting").click(function(e) {
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				id : "layer-preview",
				title : false,
				scrollbar : false,
				maxmin : false,
				area : [ '520px', '450px' ],
				content : url
			});
		});
		
		$("#filter,#closeFilter").on("click", function(e) {
			newProjectAndTechnology.changeFilter();
		});
		
		$("#export").click(function(e) {
			newProjectAndTechnology.exportnewProjectAndTechnology();
		});
		
	},
	changeFilter : function(hasOpen) {
		var $filterSearch = $("#filterSearch");
		var $filter = $("#filter");
		var $img = $filter.children("img");
		var img0 = basePath + "plugins/static/image/filter.png";
		var img1 = basePath + "plugins/static/image/filter1.png";
		var img2 = basePath + "plugins/static/image/filter2.png";
		
		if ($filterSearch.css('display') == 'none' || hasOpen) {
			$filterSearch.css('display', 'block');
		} else {
			$filterSearch.css('display', 'none');
		}
		
		// 显示/隐藏
		if ($img.attr("src") != img2 || hasOpen) {
			if ($filterSearch.css('display') == 'none') {
				$img.attr("src", img0);
			} else {
				$img.attr("src", img1);
			}
		}
		
	},
	search : function() {
		var $filter = $("#filter");
		var $img = $filter.children("img");
		var img2 = basePath + "plugins/static/image/filter2.png";
		// 是否有过滤条件
		var customOptionSetCodes = param.get("customOptionSetCode");
		var customTextValues = param.get("customTextValue");
		var hasParam = false;
		if (customOptionSetCodes && customTextValues) {
			customOptionSetCodes.push.apply(customOptionSetCodes, customTextValues);
			for ( var i in customOptionSetCodes) {
				if (customOptionSetCodes[i]) {
					hasParam = true;
					break;
				}
			}
		}
		
		if (hasParam) {
			$img.attr("src", img2);
		} else {
			newProjectAndTechnology.changeFilter(true);
		}
		
		newProjectAndTechnology.getFormList();
	},
	initLayuiForm : function(data) {
		layui.form.render();
		mdmsCommon.loadCompNo(function(data) {
			var url = "newProjectAndTechnologyYqList.html?funCode=" + param.get("funCode") + "&compNo=" + data.value;
			location.href = url;
		});
		layui.element.render();
		layui.element.on("tab(docDemoTabBrief)", function(data) {
			page.set("curPageNum", "1");
			page.set("pageSize", "20");
			param.set("status", data.elem.find("li:eq(" + data.index + ")").attr("status"));
			newProjectAndTechnology.getFormList();
		});
	},
	initTab : function(data) {
		
		newProjectAndTechnology.statusMapping = data.statusMapping;
		
		$("div[lay-filter=docDemoTabBrief]").find("li:visible").each(function(i, e) {
			var $this = $(this);
			var status = $this.attr("status");
			$this.text(newProjectAndTechnology.statusMapping["status" + status]);
		});
		
	},
	getParamInfo : function() {
		return $.ajax({
			url : basePath + "/mdms/functionModule/NewProjectAndTechnologyYq/getParamInfo.spring",
			data : param.__form()
		}).then(function(data) {
			var elemAry = [ newProjectAndTechnology.getParamDiv({
				customFieldName : "关键字",
				customFieldSet : "keyword"
			}), newProjectAndTechnology.getParamDiv({
				customFieldName : "申请时间",
				customFieldSet : "createDate"
			}), newProjectAndTechnology.getParamDiv({
				customFieldName : "科室",
				customFieldSet : "deptID"
			}) ];
			for ( var i in data.paramInfo) {
				if (!data.paramInfo[i].customFieldSet || data.paramInfo[i].customFieldSet == "label") {
					continue;
				}
				var div = newProjectAndTechnology.getParamDiv(data.paramInfo[i]);
				
				elemAry.push(div);
			}
			
			var $filterSearchDiv = $("#filterSearchDiv").empty();
			
			assemblys.createElement(elemAry, $filterSearchDiv[0]);
			
			newProjectAndTechnology.initDateSearch($filterSearchDiv);
			newProjectAndTechnology.initTab(data);
			
		});
		
	},
	getParamDiv : function(paramInfo) {
		return {
			"tagName" : "div",
			"className" : "layui-form-item mgb20 inblock",
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label lh13 fw700",
				"title" : paramInfo.customFieldName,
				"innerText" : paramInfo.customFieldName
			}, {
				"tagName" : "div",
				"className" : "layui-input-block mgl86",
				"children" : newProjectAndTechnology[paramInfo.customFieldSet](paramInfo)
			} ]
		};
	},
	initDateSearch : function($filterSearchDiv) {
		$filterSearchDiv.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	"interface" : function(customField) {
		return newProjectAndTechnology.getTextElement(customField);
	},
	text : function(customField) {
		return newProjectAndTechnology.getTextElement(customField);
	},
	textarea : function(customField) {
		return newProjectAndTechnology.getTextElement(customField);
	},
	getTextElement : function(customField) {
		return [ {
			"attr" : {
				"placeholder" : customField.customFieldName
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "customTextValue"
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldCode",
			"value" : customField.customFieldCode
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldSet",
			"value" : customField.customFieldSet
		} ];
	},
	radio : function(customField) {
		return newProjectAndTechnology.getSelectElement(customField, true);
	},
	checkbox : function(customField) {
		return newProjectAndTechnology.getSelectElement(customField, true);
	},
	select : function(customField) {
		return newProjectAndTechnology.getSelectElement(customField, true);
	},
	getSelectElement : function(customField, isCustomOptionSet) {
		return [ {
			"attr" : {
				"lay-filter" : customField.customFieldCode,
				"lay-search" : ""
			},
			"tagName" : "select",
			"name" : isCustomOptionSet ? "customOptionSetCode" : "customTextValue",
			"children" : newProjectAndTechnology.getSelectOption(customField.optionList)
		}, isCustomOptionSet ? null : {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldCode",
			"value" : customField.customFieldCode
		}, isCustomOptionSet ? null : {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldSet",
			"value" : customField.customFieldSet
		} ];
	},
	getSelectOption : function(optionList) {
		var optionAry = [ {
			"tagName" : "option",
			"innerText" : "全部",
			"value" : ""
		} ];
		for ( var i in optionList) {
			optionAry.push({
				"tagName" : "option",
				"innerText" : optionList[i].customOptionSetContent,
				"value" : optionList[i].customOptionSetCode
			});
		}
		return optionAry;
	},
	datetime : function(customField) {
		return [ {
			"attr" : {
				"laydate" : "",
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "customTextValue"
		}, {
			"tagName" : "i",
			"className" : "layui-icon layui-icon-date i_time2",
			"style" : {
				"right" : "4px"
			}
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldCode",
			"value" : customField.customFieldCode
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldSet",
			"value" : customField.customFieldSet
		} ];
	},
	user : function(customField) {
		return newProjectAndTechnology.getSelectElement(customField);
	},
	department : function(customField) {
		return newProjectAndTechnology.getSelectElement(customField);
	},
	company : function(customField) {
		return newProjectAndTechnology.getSelectElement(customField);
	},
	keyword : function(customField) {
		return [ {
			"attr" : {
				"placeholder" : "新技术编号/医师名称"
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "keyword"
		} ];
	},
	createDate : function(customField) {
		return [ {
			"attr" : {
				"laydate" : "",
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "createDate"
		}, {
			"tagName" : "i",
			"className" : "layui-icon layui-icon-date i_time2",
			"style" : {
				"right" : "4px"
			}
		} ];
	},
	deptID : function(customField) {
		var optionAry = new Array();
		$.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			async : false,
			data : {
				compNo : param.get("compNo"),
				funCode : param.get("funCode")
			},
			dataType : "json",
			// 架构一的接口，需要增加这字段
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var deptList = data.deptList;
					optionAry.push({
						"tagName" : "option",
						"innerText" : "全部",
						"value" : ""
					});
					for ( var index in deptList) {
						var dept = deptList[index];
						optionAry.push({
							"tagName" : "option",
							"innerText" : dept["DeptName"],
							"value" : dept["DeptID"]
						});
					}
				} else {
					assemblys.alert("获取部门接口失败");
				}
			}
		})

		return [ {
			"attr" : {
				"lay-filter" : customField.customFieldSet,
				"lay-search" : ""
			},
			"tagName" : "select",
			"name" : customField.customFieldSet,
			"children" : optionAry
		} ];
		
	},
	getFormList : function() {
		return $.ajax({
			url : basePath + "mdms/functionModule/NewProjectAndTechnologyYq/getNewProjectAndTechnologyYqList.spring",
			data : param.__form() + "&" + page.__form() + "&" + filterParam.__form(),
			type : "post"
		}).then(function(data) {
			newProjectAndTechnology.tableRender(data.trainingList, data.customFieldSetting);
			param.set("appCode", data.appCode);
			param.set("businessCode", data.businessCode);
			
			return data;
		});
	},
	tableRender : function(pager, customFieldSetting) {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		} ];
		cols.push({
			title : '操作',
			width : 80,
			align : "center",
			templet : function(d) {
				var item = "";
				if (d.status == 0) {
					item += '<i class="layui-icon layui-icon-edit" lay-event="edit" style="cursor: pointer;" title="编辑"></i>';
//					item += '<i class="layui-icon layui-icon-delete" lay-event="delete" style="cursor: pointer;" title="删除"></i>'
					return item;
				} else {
					return "";
				}
			}
		});
		cols.push({
			title : '新项目、技术编号',
			minWidth : 165,
			align : "center",
			templet : function(d) {
				return layui.util.escape(d.prevCustomFormFilledCode);
			}
		});
		cols.push({
			title : '延期申请编号',
			minWidth : 165,
			align : "center",
			templet : function(d) {
				return '<a class="layui-a-hasClick" lay-event="detail">' + layui.util.escape(d.customFormFilledCode) + '</a>';
			}
		});
		cols.push({
			title : '医师名称',
			width : 120,
			align : "center",
			field : "createUserName"
		});
		cols.push({
			title : '申请时间',
			align : "center",
			width : 160,
			templet : function(d) {
				return assemblys.dateToStr(d.createDate);
			}
		});
		for ( var i in customFieldSetting) {
			cols.push({
				"title" : customFieldSetting[i].customFieldName,
				"width" : 200,
				"align" : "left",
				"fieldKey" : customFieldSetting[i].customFieldCode,
				"templet" : function(d) {
					return "<pre>" + layui.util.escape(d[this.fieldKey]) + "</pre>";
				}
			});
		}
		cols.push({
			title : '状态',
			width : 130,
			align : "center",
			templet : function(d) {
				return newProjectAndTechnology.statusMapping["status" + d.status];
			}
		});
		
		if (window.$("#list").next().hasClass("layui-table-view") || !window["page"]) {
			window["page"].set(null, {
				curPageNum : 1,
				pageSize : 20
			});
		}
		
		layui.table.render({
			elem : '#list',
			data : pager.items,
			height : window.$("#list").parent().height() - (window.$("#list").next().hasClass("layui-table-view") ? 0 : $("#tablePage").height()),
			limit : 100,
			cols : [ cols ]
		});
		
		layui.laypage.render({
			"elem" : "tablePage",
			"count" : pager.totalCount,
			"curr" : pager.curPageNum || 1,
			"limit" : pager.pageSize || 20,
			"limits" : [ 10, 20, 50, 100 ],
			"layout" : [ 'prev', 'page', 'next', 'limit', 'skip', 'count' ],
			"jump" : function(obj, first) {
				page.set("curPageNum", obj.curr)
				page.set("pageSize", obj.limit);
				// 首次不执行
				if (!first) {
					newProjectAndTechnology.getFormList();
				}
			}
		});
		
		layui.table.on("tool(list)", function(obj) {
			if (obj.event == "edit") {
				newProjectAndTechnology.editForm(obj.data);
			}
			if (obj.event == "detail") {
				newProjectAndTechnology.toDetail(obj.data);
			}
			if (obj.event == "delete") {
				newProjectAndTechnology.deleteEvent(obj.data);
			}
		});
		$("#filterNum").text(pager.totalCount);
	},
	toDetail : function(d) {
		location.url({
			url : "newProjectAndTechnologyYqDetail.html",
			param : {
				funCode : param.get("funCode"),
				appCode : param.get("appCode"),
				prevCustomFormFilledCode : d.prevCustomFormFilledCode,
				prevCustomFormCode : d.prevCustomFormCode,
				customFormCode : d.customFormCode,
				customFormFilledCode : d.customFormFilledCode,
				compNo : param.get("compNo"),
				status : d.status
			}
		});
	},
	editForm : function(d) {
		var customFormCode = d.customFormCode;
		var customFormFilledCode = d.customFormFilledCode;
		var prevCustomFormFilledCode = d.prevCustomFormFilledCode;
		var prevCustomFormCode = d.prevCustomFormCode;
		var appCode = param.get("appCode");
		// 编辑页面路径
		var url = "newProjectAndTechnologyYqEdit.html?customFormCode=" + customFormCode + "&customFormFilledCode=" + customFormFilledCode + "&appCode=" + appCode + "&compNo=" + param.get("compNo") + "&prevCustomFormCode=" + prevCustomFormCode + "&prevCustomFormFilledCode=" + prevCustomFormFilledCode;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '转常规编辑',
			maxmin : true,
			area : [ '100%', '100%' ], // 设置弹窗打开大小
			content : url
		});
	},
	deleteEvent : function(d) {
		layer.confirm('确认删除该记录吗？', {
			icon : 2,
			title : '警告'
		}, function(index) {
			$.ajax({
				url : basePath + "/mdms/base/deleteEvent.spring",
				async : false,
				data : {
					customFormFilledCode : d.customFormFilledCode,
				},
				dataType : "json",
				// 架构一的接口，需要增加这字段
				skipDataCheck : true,
				success : function(data) {
					if (data.result == "success") {
						assemblys.msg("删除成功", function() {
							newProjectAndTechnology.getFormList();
						});
					} else {
						assemblys.msg("删除失败", function() {
						});
					}
				}
			})

			layer.close(index);
		});
	},
	exportnewProjectAndTechnology : function() {
		var filterParamJson = filterParam.__json();
		var inputAry = [];
		for ( var key in filterParamJson) {
			inputAry.push({
				"attr" : {
					"exportParam" : "1"
				},
				"tagName" : "input",
				"type" : "hidden",
				"name" : key,
				"value" : filterParamJson[key]
			});
		}
		assemblys.createElement(inputAry, param.__);
		
		param.__.action = basePath + "/mdms/functionModule/NewProjectAndTechnologyYq/exportNewProjectAndTechnologyYqList.spring";
		param.__.submit();
		
		param.__$.find("input[exportParam='1']").remove();
	}
}
newProjectAndTechnology.init();