// 数据层
var getnewProjectAndTechnologyData = {
	cache : {
		deptNames : {},
		userNames : {},
		userDeptNames : {}
	},
	// 初始化
	init : function() {
		
		// 加载图标
		assemblys.getMenuIcon(param.get("funCode"));
		
		getnewProjectAndTechnologyData.getDeptNameAndUserNames().then(function() {
			// 获取详情
			getnewProjectAndTechnologyData.getCustomFormDetail();
			
			// 附件
			getnewProjectAndTechnologyData.getAttaList();
			
			// 获取日志
			getnewProjectAndTechnologyData.getOptList();
			
			// 获取流程图
			getnewProjectAndTechnologyData.getFlowView();
			
			// 获取新项目、技术详情
			getnewProjectAndTechnologyData.getPrevCustomFormDetail();
			
			// 新项目、技术附件
			getnewProjectAndTechnologyData.getPrevAttaList();
			
			// 获取新项目、技术日志
			getnewProjectAndTechnologyData.getPrevOptList();
			
			// 加载监听
			getnewProjectAndTechnologyDetail.loadTab();
		});
		
	},
	getDeptNameAndUserNames : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptNameAndUserNames.spring",
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					getnewProjectAndTechnologyData.cache.deptNames = data.deptNames;
					getnewProjectAndTechnologyData.cache.userNames = data.userNames;
					getnewProjectAndTechnologyData.cache.userDeptNames = data.userDeptNames;
					
				}
			}
		});
	},
	// 获取详情
	getCustomFormDetail : function() {
		var path = basePath;
		var customFormCode = param.get("customFormCode");
		var customFormBusinessCode = param.get("businessCode");
		var customFormFilledCode = param.get("customFormFilledCode");
		var appCode = param.get("appCode");
		var compNo = param.get("compNo");
		var dom = "eventDetail";
		// 页面显示事件编号
//		$("span[customFormFilledCode]").text(" - " + customFormFilledCode);
		//显示事件填报详情
		getCustomFormDetail.getCustomFormData(path, customFormCode, customFormFilledCode, appCode, dom, customFormBusinessCode, compNo);
	},
	// 附件
	getAttaList : function() {
		
		$.ajax({
			url : basePath + "frame/fileUpload/getAttachments.spring",
			dataType : "json",
			data : {
				"belongToCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var attachmentsList = data.attachmentsList;
					var length = attachmentsList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(attachmentsList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							newData.push(tool.createAttaTr(temp));
						});
						var $tbody = $("#attaDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
		
	},
	// 日志
	getOptList : function() {
		
		$.ajax({
			url : basePath + "/mdms/base/getLogInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode")
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var logInfoList = data.data.logInfo;
					var length = logInfoList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(logInfoList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							temp["deptName"] = getnewProjectAndTechnologyData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createLogTr(temp));
						});
						var $tbody = $("#optLogDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
	},
	
	// 获取流程图
	getFlowView : function() {
		approvalFlow.initFlow(param.get("appCode"), basePath, param.get("customFormFilledCode"), param.get("funCode"), "div.newProjectAndTechnologyFlow");
	},
	// 获取详情
	getPrevCustomFormDetail : function() {
		var path = basePath;
		var customFormCode = param.get("prevCustomFormCode");
		var customFormBusinessCode = param.get("businessCode");
		var customFormFilledCode = param.get("prevCustomFormFilledCode");
		var appCode = param.get("appCode");
		var compNo = param.get("compNo");
		var dom = "prevEventDetail";
		// 页面显示事件编号
		$("span[customFormFilledCode]").text(" - " + customFormFilledCode);
		//显示事件填报详情
		getCustomFormDetail.getCustomFormData(path, customFormCode, customFormFilledCode, appCode, dom, customFormBusinessCode, compNo);
	},
	// 附件
	getPrevAttaList : function() {
		
		$.ajax({
			url : basePath + "frame/fileUpload/getAttachments.spring",
			dataType : "json",
			data : {
				"belongToCode" : param.get("prevCustomFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var attachmentsList = data.attachmentsList;
					var length = attachmentsList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(attachmentsList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							newData.push(tool.createAttaTr(temp));
						});
						var $tbody = $("#prevAttaDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
		
	},
	// 日志
	getPrevOptList : function() {
		
		$.ajax({
			url : basePath + "/mdms/base/getLogInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("prevCustomFormFilledCode"),
				"businessCode" : param.get("prevCustomFormCode")
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var logInfoList = data.data.logInfo;
					var length = logInfoList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(logInfoList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							temp["deptName"] = getnewProjectAndTechnologyData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createLogTr(temp));
						});
						var $tbody = $("#prevOptLogDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
	}
}

// 渲染层
var getnewProjectAndTechnologyDetail = {
	
	// 加载tab监听
	loadTab : function() {
		$("#tabView").children("li").on("click", function() {
			$(this).addClass("layui-this");
			$(this).siblings().removeClass("layui-this");
			
			var index = $(this).index();
			var $content = $("#container").children("div:eq(" + index + ")");
			$content.show();
			$content.siblings().hide();
			
		});
	},
	// 返回上一个页面
	back : function() {
		history.back();
	}
}

getnewProjectAndTechnologyData.init();