/**
 * 用于处理页面渲染
 */
var tool = {
	// 加载TR
	createAttaTr : function(temp) {
		var trTemplate = {
				"tagName" : "tr",
				"children" : [ {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"children" : [ {
						"tagName" : "i",
						"className" : "layui-icon layui-icon-download-circle i_icon",
						"style" : {
							"cursor" : "pointer"
						},
						"attr" : {
							"title" : "点击下载",
							"onclick" : "pubUploader.downLoadAttaPreview('" + temp.attachmentName + "','" + temp.attachmentURL + "');"
						}
					} ]
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : temp.index
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "left"
					},
					"innerHTML" : temp.attachmentName
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : temp.attachmentSize
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : (temp.createUserCode + " / " + temp.createUserName)
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : temp.createDate==undefined?"":assemblys.dateToStr(temp.createDate.time)
				} ]
			};
			return trTemplate;
	},
	createLogTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : ""
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.OptType
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.FunName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"innerHTML" : "审核意见："+(temp.AuditOpinion==undefined?"":temp.AuditOpinion)+ "<br>"+ " 备注："+(temp.Remark==undefined?"":temp.Remark)
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : (temp.deptName + " - " + temp.OptUserCode) + "<br>"+ (temp.OptDate==undefined?"":assemblys.dateToStr(temp.OptDate))
			} ]
		};
	},
	createExpertScore : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.ScoreTypeName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.ExpertScore
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"innerHTML" : temp.ScoreRemark==undefined?"":temp.ScoreRemark
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : (temp.deptName + " - " + temp.OptUserCode) + "<br>"+ (temp.OptDate==undefined?"":assemblys.dateToStr(temp.OptDate))
			} ]
		};
	},
	// 流程下标
	currFollowIndex : -1,
	// 流程图
	createFlowLi : function(flow) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : (function() {
							if (flow.hasCurrent == 1) {
								tool.currFollowIndex = flow.followIndex;
								return basePath + "plugins/static/image/image3/jinxingzhong.png";
							} else if (tool.currFollowIndex == -1) {
								return basePath + "plugins/static/image/image3/wancheng.png";
							} else {
								return basePath + "plugins/static/image/image3/dengdai.png";
							}
						})(),
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" :  (function() {
						if (flow.hasCurrent == 1) {
							return "right_item  item_red";
						} else if (tool.currFollowIndex == -1) {
							return "right_item  item_green";
						} else {
							return "right_item  item_gray";
						}
					})(),
					"children" : [ {
						"tagName" : "h3",
						"innerHTML" : flow.ProcessName || "节点"
					}, {
						"tagName" : "span",
						"className" : "item_content",
						"innerHTML" :""
					}, {
						"tagName" : "div",
						"style" : {
							"max-height" : "250px",
							"overflow" : "auto",
							"text-align" : "left",
						},
						"children" : [ {
							"tagName" : "blockquote",
							"className" : "layui-elem-quote ",
							"title" : "审核人员名单",
							"style" : {
								"display" : flow.userList && flow.userList.length > 0 /*&& flow.hasCurrent == 1*/ ? "block" : "none"
							},
							"children" : (function() {
								var userList = flow.userList;
								var userHtml = [];
								if (userList && userList.length != 0) {
									$.each(userList, function(i, u) {
										userHtml.push({
											"tagName" : "span",
											"className" : "item_content",
											"title" : (u.deptName + " - " + u.userName),
											"innerText" : (u.deptName + " - " + u.userName),
										});
									});
								}
								return userHtml;
							})()
						} ]
					} ]
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : flow.followIndex
					},
					"style" : {
						"text-align" : "left"
					}
				},{
					"tagName" : "div",
					"children" :(function() {
						var followInfo = flow.flowInfo;
						var userHtml = [];
						if (followInfo && followInfo.length != 0) {
							$.each(followInfo, function(i, u) {
								userHtml.push({
									"tagName" : "ul",
									"children" :[{
										"tagName" : "li",
										"className" : "right_data",
										"innerHTML" : assemblys.dateToStr(u.OptDate)
									},
									{
										"tagName" : "li",
										"className" : "right_text",
										"innerHTML" : u.OptUserName+u.OptType
									}]
								});
							});
						}
						return userHtml;
					})()
					
				}  ]
			} ]
		};
	},
	createFlowOneLi : function(followName,title,followInfo) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : basePath + "plugins/static/image/image3/wancheng.png",
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" : "right_item  item_green",
					"children" : [ {
						"tagName" : "h3",
						"innerHTML" : followName
					}, {
						"tagName" : "span",
						"className" : "item_content",
						"innerHTML" : title
					} ]
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : "0"
					},
					"style" : {
						"text-align" : "left"
					}
				} ,{
					"tagName" : "div",
					"children" :(function() {
						var userHtml = [];
						if (followInfo && followInfo.length != 0) {
							$.each(followInfo, function(i, u) {
								userHtml.push({
									"tagName" : "ul",
									"children" :[{
										"tagName" : "li",
										"className" : "right_data",
										"innerHTML" : assemblys.dateToStr(u.OptDate)
									},
									{
										"tagName" : "li",
										"className" : "right_text",
										"innerHTML" : u.OptUserName+u.OptType
									}]
								});
							});
						}
						return userHtml;
					})()
					
				} ]
			}]
		};
	},
	createFlowLastLi : function(temp) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : basePath + "plugins/static/image/image3/" + (true ? "wancheng" : "dengdai") + ".png",
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" : "item_unfinished",
					"innerHTML" : "已结束"
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : "-6"
					},
					"style" : {
						"text-align" : "left"
					}
				} ]
			} ]
		};
	}
}
