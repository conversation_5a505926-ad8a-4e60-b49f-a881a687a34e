<!DOCTYPE html>
<html>
<head>
<title>新项目、新技术延期审核列表</title>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../../plugins/static/css/search.css">
<link rel="stylesheet" type="text/css" href="css/newProjectAndTechnology.css">
</head>
<body>
	<form class="layui-form" lay-filter="param" target="exportIframe" method="post">
		<input type="hidden" name="funCode" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="businessCode" />
		<input type="hidden" name="status" value="99" />
		<div class="head0 layui-hide">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<div class="layui-input-inline h28 lh28 layui-hide" style="margin-top: 1px; font-weight: 800;">医院：</div>
				<div class="layui-input-inline h28 lh28 layui-hide">
					<select name="compNo" lay-filter="compNo"></select>
				</div>
			</div>
		</div>
	</form>
	<form class="layui-form" lay-filter="filterParam" target="exportIframe" method="post">
		<div class="bodys">
			<div class="inblock filter_box fr">
				<div class="inblock filter_item fr">
					共
					<span id="filterNum" class="filter_num">0</span>
					条结果
				</div>
				<div id="filter" class="inblock filter fr">
					<img src="../../../plugins/static/image/filter.png" alt="">
					<span>过滤</span>
				</div>
				<div id="export" class="inblock filter_item fr">
					<i class="layui-icon layui-icon-export skin-div-font" style="color: rgb(26, 161, 148);"></i>
					导出
				</div>
				<div id="customFieldSetting" class="inblock filter_item fr">
					<i class="layui-icon layui-icon-set-fill skin-div-font" style="color: rgb(26, 161, 148);"></i>
					字段
				</div>
				<div id="filterSearch" class="filter_search" style="height: 400px">
					<div class="head3_title">
						<span class="fw700">过滤</span>
						<i class="layui-icon layui-icon-close i_head3_title fr" title="关闭" id="closeFilter"></i>
						<i class="layui-icon layui-icon-refresh-3 i_head3_title fr mgr5" title="重置" onclick="$(this).parents('form')[0].reset();newProjectAndTechnology.search();"></i>
					</div>
					<div id="filterSearchDiv" style="overflow: auto; height: 350px;"></div>
					<br>
					<div class="layui-form-item mgb20">
						<input type="button" onclick="page.set('curPageNum',1);newProjectAndTechnology.search();" value="查询" class="layui-btn h31 lh31" style="position: absolute;right: 20px;">
					</div>
				</div>
			</div>
			<div class="layui-tab" lay-filter="docDemoTabBrief">
				<div class="layui-tab-div">
					<ul class="layui-tab-title head2_tab h28">
						<li status="99" class="layui-this" >全部</li>
						<li status="0"></li>
						<li status="1"></li>
						<li status="88"></li>
					</ul>
				</div>
			</div>
			<div class="layui-row">
				<div class="tableDiv table_noTree" style="top: 45px;">
					<div id="list" lay-filter="list"></div>
				</div>
				<div id="tablePage" class="tablePage"></div>
			</div>
		</div>
	</form>
	<form class="layui-form" lay-filter="page">
		<input type="hidden" name="curPageNum" value="1">
		<input type="hidden" name="pageSize" value="20">
	</form>
	<iframe name="exportIframe" class="layui-hide"></iframe>
	<script type="text/javascript" src="../../../../plugins/layui/layui.all.js?r="+Math.random()></script>
	<script type="text/javascript" src="../../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
	<script type="text/javascript" src="js/newProjectAndTechnologyYqList.js?r="+Math.random()></script>
</body>
</html>