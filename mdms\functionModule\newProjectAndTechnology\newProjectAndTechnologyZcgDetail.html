<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1,IE=8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>新项目、技术转常规详情</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/detail.css">
<link rel="stylesheet" type="text/css" href="css/newProjectAndTechnologyDetail.css">
<link rel="stylesheet" type="text/css" href="../../../frame/customApprovalFlow/approval/css/approvalFlow.css">
</head>
<body>
	<form id="form1" class="layui-form" lay-filter="param" onsubmit="return false;">
		<input type="hidden" name="funCode" />
		<input type="hidden" name="appCode" />
		<input type="hidden" name="compNo" />
		<input type="hidden" name="prevCustomFormFilledCode" />
		<input type="hidden" name="customFormFilledCode" />
		<input type="hidden" name="customFormCode" />
		<input type="hidden" name="processID" />
		<input type="hidden" name="seqNo" />
		<input type="hidden" name="auditIndex" />
		<input type="hidden" name="status" />
		<input type="hidden" name="funPointName" />
		<input type="hidden" name="rightPoint" />
		<input type="hidden" name="dictTypeCode" />
		<input type="hidden" name="userCode" id="userCode"/>
		<input type="hidden" name="userName" id="userName" />
		
		<div class="head0">
			<span class="head1_text fw700">
				<i class="layui-icon2" menuicon=""></i>
				<span>新项目、技术转常规详情</span>
				<span customFormFilledCode></span>
			</span>
			<div class="head0_right fr">
				<div class="top top_div">
					<button type="button" class="layui-btn layui-btn-sm fr" onclick="getnewProjectAndTechnologyDetail.back();">返回</button>
				</div>
			</div>
		</div>
		<div class="layui-tab lr_box " style="font-size: 0;">
			<!-- 核心信息 -->
			<div class="bodys" style="display: inline-block; margin-right: 0.5%;">
				<div class="layui-tab" lay-filter="docDemoTabBrief">
					<div class="layui-tab-div">
						<ul id="tabView" class="layui-tab-title head2_tab h28">
							<li class="layui-this">基本信息</li>
							<li>附件</li>
							<li>操作日志</li>
							<li>新项目、新技术详情</li>
							<li>新项目、新技术附件</li>
							<li>新项目、新技术操作日志</li>
						</ul>
					</div>
				</div>
				<div id="container" class="layui-tab-content lr_box">
					<div id="eventDetail" class="tab-content" style="display: block;">
						<ul class="layui-nav layui-nav-tree left" lay-filter="customFormDetailUL">
						</ul>
					</div>
					<div id="attaDetail" class="tab-content" style="display: none;">
						<table class="layui-table " cellpadding="0" cellspacing="0" style="width: 100%; margin: 0 auto;">
							<colgroup>
								<col width="80">
								<col width="80">
								<col width="48%">
								<col width="80">
								<col width="150">
								<col width="150">
							</colgroup>
							<thead>
								<tr class="skin-div-css">
									<td align="center">操作</td>
									<td align="center">序号</td>
									<td align="left">附件名称</td>
									<td align="center">大小</td>
									<td align="center">上传人</td>
									<td align="center">上传日期</td>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td colspan="6" align="center">无相关数据！</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div id="optLogDetail" class="tab-content" style="display: none;">
						<table class="layui-table " cellpadding="0" cellspacing="0" style="width: 100%; margin: 0 auto;">
							<colgroup>
								<col width="80">
								<col width="80">
								<col width="100">
								<col width="120">
								<col>
								<col width="185">
							</colgroup>
							<thead>
								<tr class="skin-div-css">
									<td align="center">操作</td>
									<td align="center">序号</td>
									<td align="center">操作类型</td>
									<td align="center">审核环节</td>
									<td align="center">操作内容</td>
									<td align="center">操作人</td>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td colspan="9" align="center">无相关数据！</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div id="prevEventDetail" class="tab-content" style="display: none;">
						<ul class="layui-nav layui-nav-tree left" lay-filter="customFormDetailUL">
						</ul>
					</div>
					<div id="prevAttaDetail" class="tab-content" style="display: none;">
						<table class="layui-table " cellpadding="0" cellspacing="0" style="width: 100%; margin: 0 auto;">
							<colgroup>
								<col width="80">
								<col width="80">
								<col width="48%">
								<col width="80">
								<col width="150">
								<col width="150">
							</colgroup>
							<thead>
								<tr class="skin-div-css">
									<td align="center">操作</td>
									<td align="center">序号</td>
									<td align="left">附件名称</td>
									<td align="center">大小</td>
									<td align="center">上传人</td>
									<td align="center">上传日期</td>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td colspan="6" align="center">无相关数据！</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div id="prevOptLogDetail" class="tab-content" style="display: none;">
						<table class="layui-table " cellpadding="0" cellspacing="0" style="width: 100%; margin: 0 auto;">
							<colgroup>
								<col width="80">
								<col width="80">
								<col width="100">
								<col width="120">
								<col>
								<col width="185">
							</colgroup>
							<thead>
								<tr class="skin-div-css">
									<td align="center">操作</td>
									<td align="center">序号</td>
									<td align="center">操作类型</td>
									<td align="center">审核环节</td>
									<td align="center">操作内容</td>
									<td align="center">操作人</td>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td colspan="9" align="center">无相关数据！</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<!-- 流程图 -->
			<div class="bodys1">
				<div class="right newProjectAndTechnologyFlow" style="width: 97%;">
				</div>
			</div> 
		</div>
	</form>
</body>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/fileUpload/pubUploader.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../frame/customForm/js/getCustomFormDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="js/tool.js?r="+Math.random()></script>
<script type="text/javascript" src="js/newProjectAndTechnologyZcgDetail.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../frame/customApprovalFlow/approval/js/approvalFlow.js?r="+Math.random()></script>

</html>