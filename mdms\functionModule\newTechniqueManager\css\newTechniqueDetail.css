.bodys1 {
	min-height: auto;
	padding: 10px;
	background: #fff;
	overflow: auto;
	position: absolute;
	top: 48px;
	bottom: 10px;
	right: 10px;
	width: 250px;
}

.bodys {
	min-height: auto;
	padding: 10px 15px 15px;
	background: #fff;
	overflow: auto;
	position: absolute;
	top: 48px;
	bottom: 10px;
	left: 10px;
	right: 260px;
}

body {
	width: 100%;
	min-width: 805px;
	padding: 48px 10px 10px;
	background: #eee;
	font-size: 0px;
	margin: 0;
	color: #484848;
	overflow: visible;
	font: 14px Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1, Tahoma, Arial, sans-serif;
}

html {
	height: 100%;
}

* {
	box-sizing: border-box;
}

.inblock {
	display: inline-block;
}

.h13 {
	height: 13px;
}

.lh13 {
	line-height: 13px;
}

.h28 {
	height: 28px;
}

.lh28 {
	line-height: 28px;
}

.h31 {
	height: 31px;
}

.lh31 {
	line-height: 31px;
}

.fw700 {
	font-weight: 700;
}

.mgl95 {
	margin-left: 95px;
}

.top-1 {
	position: relative;
	top: -1px;
	top: -5px\9\0;
}

.tright {
	text-align: right;
}

.tleft {
	text-align: left;
}

.fr {
	float: right;
}

.fl {
	float: left;
}

/* --------top---------- */
.head0 {
	width: 100%;
	background: #fff;
	position: absolute;
	top: 0;
	left: 0;
	line-height: 38px;
	padding: 0 5px;
}

.top {
	line-height: 30px;
	padding: 4px 0;
}

.head0 form, .head0 form input {
	display: inline-block;
	height: 28px;
	line-height: 28px;
	z-index: 100;
}

/* .bodys {
	min-width: 840px;
	min-height: auto;
	background: #fff;
	overflow: auto;
	position: absolute;
	top: 10px;
	bottom: 10px;
	right: 10px;
	left: 10px;
} */

.head1_text, .num {
	line-height: 30px;
}

a.operation_a {
	border: none;
	line-height: 30px;
}

.lr_box {
	margin: 0;
	display: table;
	width: 100%;
	border-spacing: 5px;
}

/* ----------------------  content1  --------------------- */
.l_box {
	position: relative;
	width: 100%;
	display: inline-block;
	vertical-align: top;
}
/* ----  left  --- */
.audit_content_table td, .audit_content_table th {
	padding: 8px 0px 7px;
	text-align: center;
}

.layui-tab-content {
	padding: 10px 0 0 0;
}

.left {
	display: inline-block;
	width: 100%;
	vertical-align: top;
	background: none;
}

.left .layui-nav-item {
	margin-bottom: 10px;
	line-height: 40px;
}

.head2_tab {
	border: none;
	width: 100%;
	white-space: normal;
	line-height: 28px;
	height: auto;
}

.head2_tab li {
	height: 28px;
	line-height: 28px;
	padding: 0 20px;
}

.layui-tab-title .layui-this {
	display: inline-block;
	padding: 0 20px;
	white-space: nowrap;
	text-align: center;
	font-size: 14px;
	border: none;
	cursor: pointer;
	opacity: .9;
	color: #009688;
	border-bottom: 2px solid #009688;
}

.layui-tab-title .layui-this:after {
	border: none;
}

.left .layui-nav-item  .main_table_title {
	height: 40px;
	background: #dbf0e6;
	color: #000 !important;
	border: 1px solid #d0d0d0;
}

.left .layui-nav-more {
	border-color: #4e4e4e transparent transparent;
}

.layui-nav-itemed .layui-nav-more {
	border-color: transparent transparent #4e4e4e;
}

.left .layui-nav-item  .main_table_title:hover {
	color: #666;
}

.main_table_box dd {
	background: #f2f2f2;
	margin-top: -1px;
}

.layui-table td, .layui-table th, .layui-table-header, .layui-table-tool, .layui-table-view, .layui-table[lay-skin=row], .layui-table[lay-skin=line] {
	border-color: #d0d0d0;
}

.main_table {
	margin: 0;
}

.main_table td {
	white-space: normal;
	width: 25%;
	height: 40px;
	word-break: break-all;
}

.main_table1 td {
	padding: 0;
	/* text-align: center; */
	height: 40px;
	font-size: 12px;
	padding-left: 3px;
	padding-right: 3px;
}

.main_title td, th {
	height: 40px;
	background: #dbf0e6;
	font-size: 14px;
	padding: 0 5px;
	text-align: center;
}

.layui-nav-tree .layui-nav-child dd.layui-this {
	background: #efefef;
}

.right {
	display: inline-block;
	width: 100%;
	margin-top: 5px;
	padding-left: 4px;
}

.operation_ul {
	width: 110px;
	height: 30px;
	display: inline-block;
	background: #009688;
	vertical-align: top;
}

.operation_ul .operation_li {
	line-height: 30px;
}

.operation_ul .operation_li .operation_a {
	padding: 0;
}

.operation_ul .operation_a span {
	top: 13px;
	right: -16px;
}

.operation_ul .operation_li .layui-nav-mored {
	top: 15px;
}

.operation_dl {
	top: 35px;
	left: -13px;
}

.layui-btn-sm {
	height: 30px;
	line-height: 30px;
	padding: 0 20px;
	font-size: 14px;
	color: rgba(255, 255, 255, .7);
	margin-left: 10px;
}

.right_main {
	white-space: normal;
	height: 90%;
}

.right_item, .item_finished, .item_unfinished {
	padding: 10px 10px 5px;
	position: relative;
	text-align: center;
	cursor: pointer;
	background: #f2f2f2;
	border: 1px solid #d0d0d0;
	border-radius: 3px;
}

.item_green h3 {
	color: #009688;
	font-size: 16px;
	text-align: left;
}

.item_red h3 {
	color: red;
	font-size: 16px;
	text-align: left;
}

.item_gray h3 {
	color: gray;
	font-size: 16px;
	text-align: left;
}

.item_red h3 {
	color: #ee4f4f;
	font-size: 16px;
	text-align: left;
}

.item_gray h3 {
	color: #A0A0A0;
	font-size: 16px;
	text-align: left;
}

.item_yellow h3 {
	color: #D75C07;
	font-size: 16px;
	text-align: left;
}

.item_content {
	display: block;
	color: #000;
	text-align: left;
}

.hide_item {
	display: none;
}

.layui-text .right_text {
	list-style-type: none;
}

.item_finished {
	background: #f2f2f2;
	color: #19a094;
	padding: 8px 10px;
	text-align: left;
}

.item_finished h3 {
	font-size: 16px;
	text-align: left;
}

.lr_box_cancel .item_finished {
	color: #aaa;
}

.item_unfinished {
	background: #f2f2f2;
	color: #aaa;
	padding: 8px 10px;
	text-align: left;
}

.more {
	color: #009688;
	cursor: pointer;
	margin-top: 15px;
	padding-left: 15px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.more i {
	display: inline-block;
	transition: all .3s;
}

.more_click i {
	-ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=-1, M12=1.2246467991473532e-16, M21=-1.2246467991473532e-16, M22=-1, SizingMethod='auto expand')";
	-moz-transform: rotate(180deg) translateY(2px);
	-o-transform: rotate(180deg) translateY(2px);
	-webkit-transform: rotate(180deg) translateY(2px);
	transform: rotate(180deg) translateY(2px);
}

.void {
	position: absolute;
	left: 50%;
	top: 37%;
	margin: 0 -104px;
	width: 210px;
	height: 90px;
	line-height: 86px;
	font-weight: 700;
	font-size: 45px;
	border: 5px solid #ff3f3f;
	color: #ff3f3f;
	text-align: center;
	z-index: 100;
}

.voidSmall {
	position: absolute;
	left: 87%;
	top: 5%;
	margin: 0 -104px;
	width: 90px;
	height: 40px;
	line-height: 33px;
	font-weight: 700;
	font-size: 20px;
	border: 5px solid #ff3f3f;
	color: #ff3f3f;
	text-align: center;
	z-index: 100;
	display: none;
}

.audit_content_box {
	width: 100%;
}

.audit_content_title {
	font-size: 15px;
	color: #009688;
	font-weight: 900;
}

.audit_content_table {
	margin-top: 0;
}

.audit_content_table thead th {
	background: #dbf0e6;
	color: #000;
}

.audit_content_table td {
	text-align: center;
	white-space: normal
}

.layui-icon-download-circle {
	color: #009688;
	margin-right: 5px;
}

.back {
	position: absolute;
	top: 50%;
	left: -2px;
	margin-top: -27px;
	width: 14px;
	font-size: 14px;
	z-index: 1000;
	color: #f00;
}

.right .i_back {
	width: 14px;
	height: 14px;
	background: url('../images/back2.png') no-repeat;
}

.back_info {
	margin-left: 4px;
}

.back_info ul {
	font-size: 14px;
	margin-left: 45px;
}

.lr_box .right .back_info h3 {
	background: none;
	color: #009688;
	margin-bottom: 10px;
}

.back_info li {
	font-size: 14px;
	color: #000;
	padding: 2px 0px;
}

.right .back_info li span {
	background: none;
	color: #000;
}

.disc {
	list-style: disc;
	font-weight: 700;
	margin-left: -27px;
}

.lr_box_cancel .right {
	/* display: inline-block;
    width: 100%;
    margin-top: 0;
    padding-left: 4px; */
	
}

.lr_box_cancel .right .right_item, .lr_box_cancel .right span, .lr_box_cancel  .right h3 {
	background: #aaa;
	color: #fff;
}

.lr_box_cancel .right .i_back {
	width: 14px;
	height: 14px;
	background: url('../images/back.png') no-repeat;
}

.lr_box_cancel .right .back {
	color: #aaa;
}

.lr_box_cancel  .void {
	display: block;
}

.lr_box_cancel  .voidSmall {
	display: block;
}

.rollBack {
	margin-left: -45px;
	background: none;
	color: #009688;
	margin-bottom: 10px;
	font-size: 14px;
	font-weight: 400;
}

body .layui-layer-aems {
	max-height: 98%;
	max-width: 98%;
}

body .layui-layer-aems .layui-layer-title {
	background-color: #dcefe6;
}

.layui-nav-tree {
	max-width: 100% !important;
}

.layui-nav-itemed>a .layui-nav-more {
	border-color: transparent transparent #4e4e4e;
}

.tab-content {
	display: none;
}

.operation_ul {
	width: 110px;
	height: 30px;
	display: inline-block;
	background: #009688;
	vertical-align: top;
}

.operation_ul .operation_li {
	line-height: 30px;
}

.operation_ul .operation_li .operation_a {
	padding: 0;
}

.operation_ul .operation_a span {
	top: 13px;
	right: -16px;
}

.operation_dl {
	top: 35px;
	left: -13px;
}

.layui-btn-sm {
	height: 30px;
	line-height: 30px;
	padding: 0 20px;
	font-size: 14px;
	color: rgba(255, 255, 255, .7);
	margin-left: 10px;
}

.operation_ul {
	width: 100px;
	height: 30px;
	line-height: 30px;
	background: #009688;
	padding: 0;
	text-align: center;
	cursor: pointer;
}

.operation_ul .operation_li {
	width: 100%;
}

.operation_ul .operation_li #buttonGroup1 {
	padding-left: 0px;
}

.operation_ul .layui-nav-child {
	top: 35px;
}

.top_div button {
	color: #fff;
}

.layui-elem-quote {
	margin-top: 5px;
	padding: 9px;
	border-left: 3px solid #009688;
}

.right_item  h3 {
	padding: 1px 0px;
}

.tleft[colspan='3'] {
	width: 75% !important;
}

td.tleft[colspan='3'] * {
	width: auto !important;
}

.layui-elem-quote {
	display: none;
}

.more_click i {
	-ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=-1, M12=1.2246467991473532e-16, M21=-1.2246467991473532e-16, M22=-1, SizingMethod='auto expand')";
	-moz-transform: rotate(180deg) translateY(2px);
	-o-transform: rotate(180deg) translateY(2px);
	-webkit-transform: rotate(180deg) translateY(2px);
	transform: rotate(180deg) translateY(2px);
}

span[customFormFilledCode] {
	font-size: 10px;
}