var anaesthesiaRightEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		if (param.get("anaesthesiaRightId") == 0) {//新增时下拉为多选
			$("#anClassId").attr("xm-select", "anClassId");
			$("#anClassId").attr("xm-select-search", "");
			$("#customFormFilledCode").val(param.get("customFormFilledCode"));
			
		} else {
			$("#anClassId").attr("lay-search", "");
		}
		pubMethod.initOperationAcceptType();
		anaesthesiaRightEdit.getFormEmpInfo();
		
		if (param.get("onlyShow") == 1) {
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
			
		}
		
		anaesthesiaRightEdit.initAnesthesiaClassList().then(function(data) {
			
			return anaesthesiaRightEdit.getAnaesthesiaRight();
			
		}).then(function() {
			
			anaesthesiaRightEdit.initLayui();
			$("span[class='head1_text fw700']").text("麻醉授权");
			$("dl[xid='anClassId']").css("width", "500px");
		});
		
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			anaesthesiaRightEdit.saveAnaesthesiaRight();
			return false;
		});
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
	},
	getAnaesthesiaRight : function() {
		return $.ajax({
			url : basePath + "mdms/anaesthesiaRight/getAnaesthesiaRight.spring",
			data : {
				anaesthesiaRightId : param.get("anaesthesiaRightId")
			}
		}).then(function(data) {
			param.set(null, data.anaesthesiaRight);
			return data;
		});
	},
	saveAnaesthesiaRight : function() {
		if (window.isSubmit) {
			return;
		}
		if (param.get("anaesthesiaRightId") == 0) {//新增
			var anClassName = $(".xm-input").attr("title");
			param.set("anClassName", anClassName);
			param.set("type", 1);
		} else {//编辑
			var anClassName = $("#anClassId :selected").text();
			param.set("anClassName", anClassName);
			var isValid = $("input[name='isValid']:checked").val();
			if (isValid == 1) {//有效
				param.set("type", 3);
			} else if (isValid == 0) {//暂停
				param.set("type", 2);
			}
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/anaesthesiaRight/saveAnaesthesiaRight.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				var $tbody = parent.$("#anaesthesiaFrame").empty();
				parent.otherFormDetail.getAnaesthesiaList("anaesthesiaFrame");
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},
	getFormEmpInfo : function() {
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			
			$("input[name='userCode']").val(data.userCode);
			$("input[name='userName']").val(data.userName);
			
		});
	},
	//获取麻醉分级
	initAnesthesiaClassList : function() {
		return $.ajax({
			url : basePath + "mdms/anaesthesiaRight/getAnesthesiaClassList.spring",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"anaesthesiaRightId" : $("input[name='anaesthesiaRightId']").val()
			}
		}).then(function(data) {
			
			if (data.anesthesiaClassList) {
				var htmlTemp = "";
				for (var i = 0; i < data.anesthesiaClassList.length; i++) {
					var temp = data.anesthesiaClassList[i];
					htmlTemp += "<option value='" + temp["AnClassID"] + "' >" + temp["AnClassName"] + "--" + temp["AnClassHisCode"] + "</option>";
				}
				$("#anClassId").append(htmlTemp);
				//如果是新增渲染为多选
				if (param.get("anaesthesiaRightId") == 0) {
					var formSelects = layui.formSelects;
					formSelects.render('anClassId');
				}
				
			}
			
			return data;
		});
	}
}