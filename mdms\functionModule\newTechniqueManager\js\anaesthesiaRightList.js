var anaesthesiaRightList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		anaesthesiaRightList.anaesthesiaRightListInit().then(function(data) {
			anaesthesiaRightList.getAnaesthesiaRightPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : anaesthesiaRightList.exportList
			} ];
			filterSearch.init(basePath, anaesthesiaRightList.getFilterParams(data), anaesthesiaRightList.getAnaesthesiaRightPager, customBtnDom);
			anaesthesiaRightList.initLayuiForm();
			$("input[name='customFormFilledCode']").val(param.get("customFormFilledCode"));
			if ($(window.parent.document).find("#onlyShow").val() == 1) {
				pubMethod.hideAddBtn();
				$("div[class='bodys layui-form']").addClass("bodys_noTop");
			}
			if (parent.param.get("hasDocEditRight") == 'false') {
				$("button:contains(新增)").addClass("layui-hide");
			}
		});
	},
	anaesthesiaRightListInit : function() {
		return $.ajax({
			url : basePath + "mdms/anaesthesiaRight/anaesthesiaRightListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", anaesthesiaRightList.stateMap[state].color).text(anaesthesiaRightList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			anaesthesiaRightList.getAnaesthesiaRightPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "麻醉分级,类型",
			title : "关键字"
		} ];
		return params;
	},
	getAnaesthesiaRightPager : function() {
		var cols = [ {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-search i_check" title="查看" lay-event="toShowAnaesthesiaRight"></i>';
				if (parent.param.get("hasDocEditRight") == 'true' && $(window.parent.document).find("#onlyShow").val() == 0) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditAnaesthesiaRight"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteAnaesthesiaRight"></i>';
				}
				
				return html;
			}
		}, {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '员工工号',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.userCode);
			}
		}, {
			title : '麻醉分级',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.AnClassName);
			}
		}, {
			title : '类型',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.rightTypeName);
			}
		}, {
			title : '授权时间',
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.createTime);
			}
		}, {
			title : '是否有效',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.isValid);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/anaesthesiaRight/getAnaesthesiaRightPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditAnaesthesiaRight : anaesthesiaRightList.toEditAnaesthesiaRight,
				toShowAnaesthesiaRight : anaesthesiaRightList.toShowAnaesthesiaRight,
				deleteAnaesthesiaRight : anaesthesiaRightList.deleteAnaesthesiaRight
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/anaesthesiaRight/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditAnaesthesiaRight : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditAnaesthesiaRight",
			area : [ '900px', '450px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/anaesthesiaRightEdit.html?onlyShow=0&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&anaesthesiaRightId=" + d.anaesthesiaRightId
		});
	},
	toShowAnaesthesiaRight : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditAnaesthesiaRight",
			area : [ '850px', '300px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/anaesthesiaRightView.html?onlyShow=1&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&anaesthesiaRightId=" + d.anaesthesiaRightId
		});
	},
	deleteAnaesthesiaRight : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toDeleteAnaesthesiaRight",
			area : [ '850px', '220px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/anaesthesiaRightReason.html?anaesthesiaRightId=" + d.anaesthesiaRightId + "&funCode=" + param.get("funCode") + "&anClassName=" + d.anClassName + "&customFormFilledCode=" + d.customFormFilledCode
		});
	},
	deleteAR : function(anaesthesiaRightId, reason, anClassName, customFormFilledCode) {
		layer.confirm("确定要回收吗？", function() {
			return $.ajax({
				url : basePath + "mdms/anaesthesiaRight/deleteAnaesthesiaRight.spring",
				type : "post",
				data : {
					anaesthesiaRightId : anaesthesiaRightId,
					reason : reason,
					anClassName : anClassName,
					customFormFilledCode : customFormFilledCode,
					type : 4
				}
			}).then(function(data) {
				assemblys.msg("回收成功", function() {
					var $tbody = $("#anaesthesiaFrame").empty();
					otherFormDetail.getAnaesthesiaList("anaesthesiaFrame");
				});
				return data;
			});
		});
	},
	toAnaesthesiaRightLog : function() {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toAnaesthesiaRightLog",
			area : [ '850px', '400px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/anaesthesiaRightLog.html?funCode=" + param.get("funCode") + "&customFormFilledCode=" + param.get("customFormFilledCode")
		});
	}
}