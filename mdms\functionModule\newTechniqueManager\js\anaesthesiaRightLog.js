var anaesthesiaRightLogList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		anaesthesiaRightLogList.getAnaesthesiaRightPager()
		var customBtnDom = [ {
			title : "导出",
			className : "layui-icon layui-icon-export skin-div-font",
			onclick : anaesthesiaRightLogList.exportList
		} ];
		filterSearch.init(basePath, anaesthesiaRightLogList.getFilterParams(), anaesthesiaRightLogList.getAnaesthesiaRightPager, customBtnDom);
		//隐藏搜索框
		$("#filter").addClass("layui-hide");
		anaesthesiaRightLogList.initLayuiForm();
		$("span[class='head1_text fw700']").text("麻醉授权日志");
	},
	initLayuiForm : function() {
		layui.form.render();
	},
	getFilterParams : function() {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "麻醉分级,类型",
			title : "关键字"
		} ];
		return params;
	},
	getAnaesthesiaRightPager : function() {
		var cols = [ {
			title : '授权医师',
			width : 170,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.userCode + "/" + d.UserName);
			}
		}, {
			title : '操作',
			width : 60,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.typeName);
			}
		}, {
			title : '麻醉分级',
			align : "center",
			width : 100,
			templet : function(d) {
				return assemblys.htmlEncode(d.AnClassName);
			}
		}, {
			title : '类型',
			width : 60,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.rightTypeName);
			}
		}, {
			title : '授权开始时间',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.dateToStr(d.createTime);
			}
		}, {
			title : '授权结束时间',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.dateToStr(d.createEndTime);
			}
		}, {
			title : '说明',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.htmlEncode(d.reason);
			}
		}, {
			title : '操作时间',
			align : "center",
			width : 150,
			templet : function(d) {
				return assemblys.dateToStr(d.optDate);
			}
		}, {
			title : '操作人',
			align : "center",
			width : 170,
			templet : function(d) {
				return assemblys.htmlEncode(d.optUserCode + "/" + d.optUserName);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/anaesthesiaRight/getAnaesthesiaRightLogPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/anaesthesiaRight/exportLogList.spring?" + param.__form() + "&" + filterParam.__form();
	}
}