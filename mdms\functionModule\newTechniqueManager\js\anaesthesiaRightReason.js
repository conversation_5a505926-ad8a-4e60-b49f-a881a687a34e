var anaesthesiaRightReason = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		$("span[class='head1_text fw700']").text("麻醉授权");
		anaesthesiaRightReason.initLayui();
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			anaesthesiaRightReason.saveReason();
		});
	},
	saveReason : function() {
		assemblys.closeWindow();
		parent.anaesthesiaRightList.deleteAR(param.get("anaesthesiaRightId"), $(".showReason").val(), param.get("anClassName"), param.get("customFormFilledCode"));
	}
}