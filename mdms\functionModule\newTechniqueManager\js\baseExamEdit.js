var baseExamEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		baseExamEdit.getBaseExam();
		if (param.get("baseExamId") == 0) {
			$("#customFormFilledCode").val(param.get("customFormFilledCode"));
		}
		pubMethod.getFormEmpInfo();
		baseExamEdit.initLayui();
		$("span[class='head1_text fw700']").text("三基培训考核");
		if (param.get("onlyShow") == 1) {//浏览时不可编辑
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
		}
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			baseExamEdit.saveBaseExam();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "datetime",
				max : 'today',
				format : "yyyy-MM-dd HH:mm"
			});
		});
	},
	getBaseExam : function() {
		return $.ajax({
			url : basePath + "mdms/baseExam/getBaseExam.spring",
			data : {
				baseExamId : param.get("baseExamId")
			}
		}).then(function(data) {
			param.set(null, data.baseExam);
			return data;
		});
	},
	saveBaseExam : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/baseExam/saveBaseExam.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.$("#baseCheckFrame").empty();
				parent.otherFormDetail.getBaseExamList("baseCheckFrame");
				assemblys.closeWindow();
			});
			return data;
		});
	}
}