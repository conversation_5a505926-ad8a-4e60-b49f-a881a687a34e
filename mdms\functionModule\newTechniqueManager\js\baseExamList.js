var baseExamList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		baseExamList.baseExamListInit().then(function(data) {
			baseExamList.getBaseExamPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : baseExamList.exportList
			} ];
			filterSearch.init(basePath, baseExamList.getFilterParams(data), baseExamList.getBaseExamPager, customBtnDom);
			baseExamList.initLayuiForm();
			if ($(window.parent.document).find("#onlyShow").val() == 1) {
				$("button").addClass("layui-hide");
				$("div[class='bodys layui-form']").addClass("bodys_noTop");
			}
			if (parent.param.get("hasDocEditRight") == 'false') {
				$("button:contains(新增)").addClass("layui-hide");
			}
		});
	},
	baseExamListInit : function() {
		return $.ajax({
			url : basePath + "mdms/baseExam/baseExamListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", baseExamList.stateMap[state].color).text(baseExamList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			baseExamList.getBaseExamPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "培训主题,培训内容",
			title : "关键字"
		} ];
		return params;
	},
	getBaseExamPager : function() {
		var cols = [ {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				
				html += '<i class="layui-icon layui-icon-search i_check" title="查看" lay-event="toShowBaseExam"></i>';
				if (parent.param.get("hasDocEditRight") == 'true' && $(window.parent.document).find("#onlyShow").val() == 0) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditBaseExam"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteBaseExam"></i>';
				}
				
				return html;
			}
		}, {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '培训内容',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.trainSummary);
			}
		}, {
			title : '培训时间',
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.trainDate);
			}
		}, {
			title : '考核成绩',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.score);
			}
		}, {
			title : '培训类型',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.trainType);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/baseExam/getBaseExamPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditBaseExam : baseExamList.toEditBaseExam,
				toShowBaseExam : baseExamList.toShowBaseExam,
				deleteBaseExam : baseExamList.deleteBaseExam
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/baseExam/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditBaseExam : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditBaseExam",
			area : [ '850px', '450px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/baseExamEdit.html?onlyShow=" + $(window.parent.document).find("#onlyShow").val() + "&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&baseExamId=" + d.baseExamId
		});
	},
	toShowBaseExam : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditBaseExam",
			area : [ '850px', '350px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/baseExamView.html?onlyShow=1&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&baseExamId=" + d.baseExamId
		});
	},
	deleteBaseExam : function(d) {
		layer.confirm("确定要删除吗？", function() {
			return $.ajax({
				url : basePath + "mdms/baseExam/deleteBaseExam.spring",
				type : "post",
				data : {
					baseExamId : d.baseExamId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					$("#baseCheckFrame").empty();
					otherFormDetail.getBaseExamList("baseCheckFrame");
				});
				return data;
			});
		});
	}
}