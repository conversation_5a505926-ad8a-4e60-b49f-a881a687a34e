var behaviorRecordEdit = {
	clickNum : 0,
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		behaviorRecordEdit.getBehaviorTypeList().then(function(data) {
			behaviorRecordEdit.getBehaviorRecord();
			behaviorRecordEdit.initLayui();
			var title = "医疗安全行为新增";
			behaviorRecordEdit.getUserInFo().then(function(data) {
				if (data.user) {
					$("input[name=userCode]").val(data.user.userCode);
					$("input[name=userName]").val(data.user.userName);
				}
			})
			if (param.get("behaviorRecordId")) {
				title = "医疗安全行为编辑";
			}
			$("span[class='head1_text fw700']").text(title);
		});
		$("#customformfilledCode").val(param.get("customFormFilledCode"));
		if (param.get("behaviorRecordId") == 0) {
			pubMethod.getFormEmpInfo();
		}
		//如果是医师个人功能点时仅查看
		if (param.get("showOrEdit") == 1) {
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
		}
		//hwx 2022-5-24授权监管生成安全行为记录
		behaviorRecordEdit.behaviorRecordBySupervise();
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			behaviorRecordEdit.saveBehaviorRecord();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			var superiveseDate = new Date();
			var isSuperivese = param.get("isSuperivese");
			if (isSuperivese && isSuperivese == 1) {
				superiveseDate = new Date(param.get("superiveseDate"));
			}
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "datetime",
				max : 'today',
				value : superiveseDate,
				format : "yyyy-MM-dd HH:mm"
			});
		});
	},
	
	getBehaviorRecord : function() {
		return $.ajax({
			url : basePath + "mdms/behaviorRecord/getBehaviorRecord.spring",
			data : {
				behaviorRecordId : param.get("behaviorRecordId")
			}
		}).then(function(data) {
			param.set(null, data.behaviorRecord);
			behaviorRecordEdit.initTypeFile(data.fileList);
			return data;
		});
	},
	saveBehaviorRecord : function() {
		
		if (behaviorRecordEdit.clickNum != 0) {
			window.isSubmit = true;
		}
		behaviorRecordEdit.clickNum++;
		
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		
		var behaviorRecordFileList = [];
		$("#ueditorFileDiv-0").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			behaviorRecordFileList.push(typeFiles);
		});
		var behaviorRecordFileListJson = JSON.stringify(behaviorRecordFileList);
		$("#behaviorRecordFileListJson").val(behaviorRecordFileListJson);
		
		return $.ajax({
			url : basePath + "mdms/behaviorRecord/saveBehaviorRecord.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				var isSuperivese = param.get("isSuperivese");
				var osID = param.get("osID");
				if (isSuperivese && isSuperivese == 1) {
					if (param.get("behaviorRecord") != "" && param.get("behaviorRecord")) {
						parent.behaviorRecord.init("#behaviorRecordDiv");
					} else {
						parent.operationSuperviseList.toUpdateStatus(osID, 3, "");
					}
				} else {
					var $tbody = parent.$("#behaviorRecordDiv").empty();
					if (param.get("behaviorRecord") != "" && param.get("behaviorRecord")) {
						parent.behaviorRecord.init("#behaviorRecordDiv");
					} else {
						parent.otherFormDetail.showBehaviorRecordList("behaviorRecordDiv");
						$("#behaviorRecordFileListJson").val(behaviorRecordFileListJson);
					}
				}
				assemblys.closeWindow();
			});
			return data;
		});
	},
	
	//获取医疗安全行为记录所有事件性质
	getBehaviorTypeList : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.YLANSJXZ,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
			},
			error : function() {
			}
		}).then(function(data) {
			var html = "";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
			})
			$("#behaviorTypeDictCode").html(html);
			return data;
		});
	},
	
	attaCallback : function(result) {// 自定义上传图片后的回调
		var fileHtml = "";
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		for (var i = 0; i < result.length; i++) {
			fileHtml += "<li style='width: 500px;'>";
			fileHtml += "	<em title=\"" + result[i].title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + result[i].title + "&nbsp;&nbsp;" + result[i].size + "</em>";
			var suffix = result[i].type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" id=\"preview\" onclick=\"pubUploader.preview('" + result[i].title + "','" + result[i].url + "');\"  >预览图片</a></span>";
			}
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + result[i].title + "','" + result[i].url + "');\">下载</a></span>";
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove attaDelete\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a></span>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + result[i].title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + result[i].url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + result[i].size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + result[i].type + "\"/>";
			fileHtml += "</li>";
		}
		$("#ueditorFileDiv-0").append(fileHtml);
		if (param.get("showOrEdit") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
	},
	
	initTypeFile : function(behaviorRecordFileList) {
		var filesData = behaviorRecordFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.title = typeFileTemp.AttaName;
				files.url = typeFileTemp.AttaUrl;
				files.size = typeFileTemp.AttaSize;
				files.type = typeFileTemp.AttaType;
				result.push(files);
			}
			//param.set("fileIndex", i);
			behaviorRecordEdit.attaCallback(result);
		}
		
	},
	behaviorRecordBySupervise : function() {
		var isSuperivese = param.get("isSuperivese");
		var superiveseSummary = param.get("superiveseSummary");
		var superiveseDate = param.get("superiveseDate");
		if (isSuperivese && isSuperivese == 1) {
			$("#behaviorTypeDictCode").val("YLANYZ");
			$("#behaviorTypeDictCode").attr("disabled", "disabled");
			$("#optDate").attr("disabled", "disabled");
			$("#optDate").append('<input type="text" name="optDate" value="' + superiveseDate + '">');
			$("#optDate").append('<input type="text" name="behaviorTypeDictCode" value="YLANYZ">');
			$("#remark").val(superiveseSummary);
			layui.laydate.render()
		}
	},
	getUserInFo : function() {
		return $.ajax({
			url : basePath + "mdms/mdmsCommon/getUserInfo.spring",
			type : "get",
			dataType : "json",
		})
	}
}