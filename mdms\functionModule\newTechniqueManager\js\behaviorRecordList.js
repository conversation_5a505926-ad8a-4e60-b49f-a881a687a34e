var behaviorRecordList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		behaviorRecordList.behaviorRecordListInit().then(function(data) {
			behaviorRecordList.getBehaviorRecordPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : behaviorRecordList.exportList
			} ];
			filterSearch.init(basePath, behaviorRecordList.getFilterParams(data), behaviorRecordList.getBehaviorRecordPager, customBtnDom);
			behaviorRecordList.initLayuiForm();
		});
	},
	behaviorRecordListInit : function() {
		return $.ajax({
			url : basePath + "mdms/behaviorRecord/behaviorRecordListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", behaviorRecordList.stateMap[state].color).text(behaviorRecordList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			behaviorRecordList.getBehaviorRecordPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "自定义表单数据编码,事件性质字典编码,摘要",
			title : "关键字"
		} ];
		return params;
	},
	getBehaviorRecordPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditBehaviorRecord"></i>';
				html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteBehaviorRecord"></i>';
				return html;
			}
		}, {
			title : '自定义表单数据编码',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.customFormFilledCode);
			}
		}, {
			title : '事件性质字典编码',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.behaviorTypeDictCode);
			}
		}, {
			title : '摘要',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.summary);
			}
		}, {
			title : '操作时间',
			align : "left",
			templet : function(d) {
				return assemblys.dateToStr(d.optDate);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/behaviorRecord/getBehaviorRecordPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditBehaviorRecord : behaviorRecordList.toEditBehaviorRecord,
				deleteBehaviorRecord : behaviorRecordList.deleteBehaviorRecord
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/behaviorRecord/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditBehaviorRecord : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditBehaviorRecord",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "behaviorRecordEdit.html?funCode=" + param.get("funCode") + "&behaviorRecordId=" + d.behaviorRecordId
		});
	},
	deleteBehaviorRecord : function(d) {
		return $.ajax({
			url : basePath + "mdms/behaviorRecord/deleteBehaviorRecord.spring",
			type : "post",
			data : {
				behaviorRecordId : d.behaviorRecordId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				behaviorRecordList.getBehaviorRecordPager();
			});
			return data;
		});
	}
}