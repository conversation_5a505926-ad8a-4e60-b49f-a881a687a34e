var certificateManagementEdit = {
	baseImgPath : "",
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		//如果是草稿时不要提交按钮,取消提交按钮
		
		certificateManagementEdit.getZslxList().then(function(data) {
			certificateManagementEdit.getCertificateManagement();
			certificateManagementEdit.initLayui();
			$("span[class='head1_text fw700']").text("上传证件");
			certificateManagementEdit.initFilelUpload();
			certificateManagementEdit.explatinWarn();
		});
		
		$("#customformfilledCode").val(param.get("customFormFilledCode"));
		
		if (param.get("showOrEdit") == 1) {
			
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
			
		}
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			certificateManagementEdit.saveCertificateManagement(0);
			return false;
		});
		layui.form.on("submit(submit)", function() {
			certificateManagementEdit.saveCertificateManagement(1);
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				format : "yyyy-MM-dd"
			});
		});
	},
	getCertificateManagement : function() {
		return $.ajax({
			url : basePath + "mdms/certificateManagement/getCertificateManagement.spring",
			data : {
				certificateId : param.get("certificateId")
			}
		}).then(function(data) {
			param.set(null, data.certificateManagement);
			//hwx 2024年3月22日上午11:37:55 重新赋值日期
			if (data.certificateManagement && data.certificateManagement.dueDate) {
				param.set("dueDate", assemblys.dateToStr(data.certificateManagement.dueDate, "yyyy-MM-dd"));
			}
			certificateManagementEdit.baseImgPath = data.baseImgPath;
			certificateManagementEdit.initTypeFile(data.fileList);
			return data;
		});
	},
	saveCertificateManagement : function(saveType) {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		
		var certifiFileList = [];
		$("#ueditorFileDiv").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			certifiFileList.push(typeFiles);
		});
		//hwx 2023-7-20 增加证件上传必填
		if (certifiFileList.length == 0) {
			assemblys.msg("请上传证件附件！");
			window.isSubmit = false;
			return;
		}
		var certifiFileListJson = JSON.stringify(certifiFileList);
		$("#certifiFileListJson").val(certifiFileListJson);
		
		return $.ajax({
			url : basePath + "mdms/certificateManagement/saveCertificateManagement.spring?saveType=" + saveType,
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.newCertificateInfo.init("#certificateDiv");
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},
	getZslxList : function() {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getBaseDictListByCode.spring",
			data : {
				customFormFilledCode : param.get("customFormFilledCode"),
			}
		}).then(function(data) {
			if (data.dictList) {
				var htmlTemp = "";
				for (var i = 0; i < data.dictList.length; i++) {
					var temp = data.dictList[i];
					htmlTemp += "<option value='" + temp["DictCode"] + "' >" + temp["DictName"] + "</option>";
				}
				$("#certifiType").append(htmlTemp);
			}
			return data;
		});
	},
	initFilelUpload : function() {
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		// 文件
		pubUploader.initCustomUpload("customFile", {
			type : "file",
			fileAllow : [ ".JPG,.JPEG,.PNG,.DPF,.jpg,.jpeg,.png,.pdf" ],
			fileAllowNum : 10
		}, function(list) {
			$("#ueditorFileDiv").empty();
			pubUploader.setFileList(list, "#ueditorFileDiv");
		});
		if (param.get("showOrEdit") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
	},
	initTypeFile : function(certifiFileList) {
		var filesData = certifiFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.attaName = typeFileTemp.attaName;
				files.attaUrl = typeFileTemp.attaUrl;
				files.attaSize = typeFileTemp.attaSize || '0KB';
				files.attaType = typeFileTemp.attaType;
				result.push(files);
			}
			pubUploader.setFileList(result, "#ueditorFileDiv");
		}
		
	},
	// 预览PDF
	toShowPDF : function(fileName) {
		parent.layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toShowDocument",
			area : [ '70%', '90%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/medicalDocManager/toShowDocument.html?fileName=" + fileName
		});
		
	},
	//说明提醒
	explatinWarn : function() {
		$("i[name=notice]").hover(function() {
			assemblys.tips($("i[name=notice]"), "到期日期填写方可进行证件到期提醒！", 200, "right");
		}, function() {
			layer.closeAll('tips');
		})
	}
}