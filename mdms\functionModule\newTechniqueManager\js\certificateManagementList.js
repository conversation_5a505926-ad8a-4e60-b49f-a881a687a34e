var certificateManagementList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		certificateManagementList.certificateManagementListInit().then(function(data) {
			certificateManagementList.getCertificateManagementPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : certificateManagementList.exportList
			} ];
			filterSearch.init(basePath, certificateManagementList.getFilterParams(data), certificateManagementList.getCertificateManagementPager, customBtnDom);
			certificateManagementList.initLayuiForm();
		});
	},
	certificateManagementListInit : function() {
		return $.ajax({
			url : basePath + "mdms/certificateManagement/certificateManagementListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", certificateManagementList.stateMap[state].color).text(certificateManagementList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			certificateManagementList.getCertificateManagementPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : ",,,,,",
			title : "关键字"
		} ];
		return params;
	},
	getCertificateManagementPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditCertificateManagement"></i>';
				html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteCertificateManagement"></i>';
				return html;
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.certifiType);
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.certifiName);
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.remark);
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.optUserCode);
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.optUserName);
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.dateToStr(d.optDate);
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.belongUserCode);
			}
		}, {
			title : '',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.belongUserName);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/certificateManagement/getCertificateManagementPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditCertificateManagement : certificateManagementList.toEditCertificateManagement,
				deleteCertificateManagement : certificateManagementList.deleteCertificateManagement
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/certificateManagement/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditCertificateManagement : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditCertificateManagement",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "certificateManagementEdit.html?funCode=" + param.get("funCode") + "&certificateId=" + d.certificateId
		});
	},
	deleteCertificateManagement : function(d) {
		layer.confirm("确定要删除吗？", function() {
			alert(123);
		});
		return false;
		return $.ajax({
			url : basePath + "mdms/certificateManagement/deleteCertificateManagement.spring",
			type : "post",
			data : {
				certificateId : d.certificateId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				certificateManagementList.getCertificateManagementPager();
			});
			return data;
		});
	}
}