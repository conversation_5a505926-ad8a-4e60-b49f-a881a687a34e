var checkRoomRightEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		if (param.get("checkRoomRightId") == 0) {//新增时下拉为多选
			$("#checkRoomCode").attr("xm-select", "checkRoomCode");
			$("#checkRoomCode").attr("xm-select-search", "");
			$("#customFormFilledCode").val(param.get("customFormFilledCode"));
		} else {//否则为单选
			$("#checkRoomCode").attr("lay-search", "");
		}
		checkRoomRightEdit.getFormEmpInfo();
		pubMethod.initOperationAcceptType();
		checkRoomRightEdit.initCheckRoomLevel().then(function(data) {
			return checkRoomRightEdit.getCheckRoomRight();
			
		}).then(function() {
			checkRoomRightEdit.initLayui();
			$("span[class='head1_text fw700']").text("查房授权");
			$("dl[xid='checkRoomCode']").css("width", "350px");
			
		});
		$("#titleName").text("查房权限");
		if (param.get("onlyShow") == 1) {
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
		}
		
	},
	initLayui : function() {
		layui.form.render();
		var formSelects = layui.formSelects;
		layui.form.on("submit(save)", function() {
			checkRoomRightEdit.saveCheckRoomRight();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	getCheckRoomRight : function() {
		return $.ajax({
			url : basePath + "mdms/checkRoomRight/getCheckRoomRight.spring",
			data : {
				checkRoomRightId : param.get("checkRoomRightId")
			}
		}).then(function(data) {
			param.set(null, data.checkRoomRight);
			if (data.checkRoomRight == null) {
				$("input[name='optUserCode']").val(data.user.userCode);
				$("input[name='optUserName']").val(data.user.userName);
				
			}
			return data;
		});
	},
	saveCheckRoomRight : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/checkRoomRight/saveCheckRoomRight.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				var $tbody = parent.$("#checkRoomRightFrame").empty();
				parent.otherFormDetail.getCheckRoomList("checkRoomRightFrame");
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},
	//获取查房等级
	initCheckRoomLevel : function(dictCode, name) {
		return $.ajax({
			//url : basePath + "mdms/functionModule/newTechniqueManager/getBaseDictList.spring",
			url : basePath + "mdms/checkRoomRight/getCheckRoomLevel.spring",
			data : {
				dictTypeCode : 'LEVELROUNDAUTH',
				systemName : "mdms",
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"checkRoomRightId" : $("input[name='checkRoomRightId']").val()
			}
		}).then(function(data) {
			if (data.dictList) {
				
				var htmlTemp = "";
				var sonhtmlTemp = "";
				for (var i = 0; i < data.dictList.length; i++) {
					var temp = data.dictList[i];
					htmlTemp += "<option value='" + temp["dictCode"] + "' >" + temp["dictName"] + "</option>";
				}
				$("#checkRoomCode").append(htmlTemp);
				//如果是新增渲染为多选
				if (param.get("checkRoomRightId") == 0) {
					var formSelects = layui.formSelects;
					formSelects.render('checkRoomCode');
					//$("dl[xid='operationCode']").css("top", "50px");
				}
				
			}
			
			return data;
		});
	},
	getFormEmpInfo : function() {
		
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			
			$("input[name='userCode']").val(data.userCode);
			$("input[name='userName']").val(data.userName);
			
		});
	}
}