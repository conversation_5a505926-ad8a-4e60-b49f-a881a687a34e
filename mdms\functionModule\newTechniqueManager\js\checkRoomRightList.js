var checkRoomRightList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		checkRoomRightList.checkRoomRightListInit().then(function(data) {
			checkRoomRightList.getCheckRoomRightPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : checkRoomRightList.exportList
			} ];
			filterSearch.init(basePath, checkRoomRightList.getFilterParams(data), checkRoomRightList.getCheckRoomRightPager, customBtnDom);
			checkRoomRightList.initLayuiForm();
			$("input[name='customFormFilledCode']").val(param.get("customFormFilledCode"));
			if ($(window.parent.document).find("#onlyShow").val() == 1) {
				pubMethod.hideAddBtn();
				$("div[class='bodys layui-form']").addClass("bodys_noTop");
			}
			
			if (parent.param.get("hasDocEditRight") == 'false') {
				$("button:contains(新增)").addClass("layui-hide");
			}
			
		});
	},
	checkRoomRightListInit : function() {
		return $.ajax({
			url : basePath + "mdms/checkRoomRight/checkRoomRightListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			checkRoomRightList.getCheckRoomRightPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "工号,姓名,查房等级",
			title : "关键字"
		} ];
		return params;
	},
	getCheckRoomRightPager : function() {
		var cols = [ {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				
				html += '<i class="layui-icon layui-icon-search i_check" title="" lay-event="toShowCheckRoomRight"></i>';
				if (parent.param.get("hasDocEditRight") == 'true' && $(window.parent.document).find("#onlyShow").val() == 0) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditCheckRoomRight"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteCheckRoomRight"></i>';
				}
				
				return html;
			}
		}, {
			title : '序号',
			width : 60,
			align : "center",
			type : 'numbers'
		}, {
			title : '查房等级',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.checkRoomLevel);
			}
		}, {
			title : '院内编码',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.hospitalCode);
			}
		}, {
			title : '操作人工号',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.optUserCode);
			}
		}, {
			title : '操作人姓名',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.optUserName);
			}
		}, {
			title : '授权时间',
			width : 150,
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.optDate);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/checkRoomRight/getCheckRoomRightPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				param.set("hasDocEditRight", res.hasDocEditRight)
				$("#filterNum").text(count);
			},
			events : {
				toEditCheckRoomRight : checkRoomRightList.toEditCheckRoomRight,
				toShowCheckRoomRight : checkRoomRightList.toShowCheckRoomRight,
				deleteCheckRoomRight : checkRoomRightList.deleteCheckRoomRight
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/checkRoomRight/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditCheckRoomRight : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditCheckRoomRight",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "checkRoomRightEdit.html?onlyShow=" + $(window.parent.document).find("#onlyShow").val() + "&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&checkRoomRightId=" + d.checkRoomRightId
		});
	},
	toEditCheckRoomRight : function(id, onlyShow) {
		var html = "checkRoomRightEdit.html";
		if (onlyShow == 1) {
			html = "checkRoomRightView.html";
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditCheckRoomRight",
			area : [ '900px', '350px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?onlyShow=0&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&checkRoomRightId=" + id
		});
	},
	deleteCheckRoomRight : function(id) {
		layer.confirm("确定要删除吗？", function() {
			return $.ajax({
				url : basePath + "mdms/checkRoomRight/deleteCheckRoomRight.spring",
				type : "post",
				data : {
					checkRoomRightId : id
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					var $tbody = $("#checkRoomRightFrame").empty();
					otherFormDetail.getCheckRoomList("checkRoomRightFrame");
				});
				return data;
			});
		});
	}
}