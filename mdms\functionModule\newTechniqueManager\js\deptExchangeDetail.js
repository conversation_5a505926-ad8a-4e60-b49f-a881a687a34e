var deptExchangeDetail = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		deptExchangeDetail.getDeptExchange().then(function(data) {
			
			deptExchangeDetail.getExchangeType().then(function(typeData) {
				$.each(typeData.dictList, function(i, val) {
					if (data.deptExchange.exchangeType == val.dictCode) {
						$("#exchangeType").val(val.dictName);
					}
				})
			});
			
			deptExchangeDetail.getDeptList().then(function(deptData) {
				
				$.each(deptData.deptList, function(i, val) {
					if (data.deptExchange.deptId == val.DeptID) {
						$("#deptId").val(val.DeptName);
					}
					if (data.deptExchange.exchangeDeptId == val.DeptID) {
						$("#exchangeDeptId").val(val.DeptName);
						
					}
				})
			});
			
			if (data.deptExchange.state == 0) {
				$("#state2").val("未轮转");
			}
			if (data.deptExchange.state == 1) {
				$("#state2").val("已轮转");
			}
			deptExchangeDetail.initLayui();
			
		});
		
		$("#customformfilledCode").val(param.get("customFormFilledCode"));
		deptExchangeDetail.initLayui();
	},
	
	initLayui : function() {
		layui.form.render();
		
//		param.__$.find("input[laydate]").each(function(i, e) {
//			layui.laydate.render({
//				elem : e,
//				trigger : "click",
//				type : "date",
//				format : "yyyy-MM-dd"
//			});
//		});
	},
	
	getDeptList : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			type : "get",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			skipDataCheck : true, //- 如果接口响应回来的数据有问题，请增加该参数
			success : function(data) {
			}
		});
	},
	
	//获取轮转类型
	getExchangeType : function() {
		var resultName = "";
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : "EXCHANGETYPE",
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
			}
		});
		
	},
	
	getDeptExchange : function() {
		return $.ajax({
			url : basePath + "mdms/deptExchange/getDeptExchange.spring",
			data : {
				deptExchangeId : param.get("deptExchangeId")
			}
		}).then(function(data) {
			param.set(null, data.deptExchange);
			return data;
		});
	},
	
	//根据表单编号获取医师表单信息
	getCerInfo : function() {
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			$("#empNo").val(data.empNo);
			$("#empName").val(data.empName);
		});
	},
	
	saveDeptExchange : function(data) {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		$("#userCode").removeAttr("disabled");
		$("#userName").removeAttr("disabled");
		$("#state").val(data);
		return $.ajax({
			url : basePath + "mdms/deptExchange/saveDeptExchange.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.getNewTechniqueData.getDeptExchange();
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},

}