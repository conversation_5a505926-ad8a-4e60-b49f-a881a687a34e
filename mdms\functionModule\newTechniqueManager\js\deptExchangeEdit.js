var deptExchangeEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		$("span[class='head1_text fw700']").text("轮转管理");
		
		deptExchangeEdit.getDeptList();
		deptExchangeEdit.getExchangeType();
		deptExchangeEdit.getDeptExchange().then(function(data) {
			if (param.get("showOrEdit") == 1) {
				pubMethod.hideAddBtn();
				pubMethod.formReadOnly();
			}
			deptExchangeEdit.initLayui();
			
		});
		
	},
	
	initLayui : function() {
		layui.form.render();
		//轮转状态  0  未轮转   1已轮转 2轮转中
		layui.form.on("submit(save)", function() {
			deptExchangeEdit.saveDeptExchange(0);
			return false;
		});
		
		layui.form.on("submit(saveAndExch)", function() {
			deptExchangeEdit.saveDeptExchange(2);
			return false;
		});
		
		var beginTime = layui.laydate.render({
			elem : "#beginTime",
			trigger : "click",
			type : "date",
			min : new Date().toLocaleString('chinese', {
				hour12 : false
			}),
			max : $("#endTime").val().toLocaleString('chinese', {
				hour12 : false
			}),
			format : "yyyy-MM-dd",
			done : function(value, date) {
				//结束时间最小值>开始时间 
				endTime.config.min = {
					year : date.year,
					month : date.month - 1,//关键
					date : date.date,
				};
			}
		});
		var $beginTime = $("#beginTime").val();
		if ($beginTime) {
		} else {
			$beginTime = new Date();
		}
		var endTime = layui.laydate.render({
			elem : "#endTime",
			trigger : "click",
			type : "date",
			min : $beginTime.toLocaleString('chinese', {
				hour12 : false
			}),
			format : "yyyy-MM-dd",
			done : function(value, date) {
				beginTime.config.min = {
					year : date.year,
					month : date.month - 1,//关键
					date : date.date - 1,
				};
				//开始时间最大值<结束时间
				beginTime.config.max = {
					year : date.year,
					month : date.month - 1,//关键
					date : date.date,
				}
			}
		});
	},
	
	getDeptList : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			type : "get",
			data : {
				"compNo" : param.get("compNo"),
			},
			dataType : "json",
			//如果接口响应回来的数据有问题，请增加该参数
			skipDataCheck : true
		}).then(function(data) {
			var html = "";
			$.each(data.deptList, function(i, val) {
				html += "<option value='" + val.DeptID + "' >" + val.DeptName + "</option>";
			})
			$("#exchangeDeptId").html(html);
			layui.form.render();
			return data
		});
	},
	
	//获取轮转类型
	getExchangeType : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.EXCHANGETYPE,
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true
		}).then(function(data) {
			var html = "";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
			})
			$("#exchangeType").html(html);
			layui.form.render();
			return data;
		});
	},
	
	getDeptExchange : function() {
		return $.ajax({
			url : basePath + "mdms/deptExchange/getDeptExchange.spring",
			data : {
				deptExchangeId : param.get("deptExchangeId")
			}
		}).then(function(data) {
			if (JSON.stringify(data) != "{}") {
				param.set(null, data.deptExchange);
				param.set("beginTime", assemblys.dateToStr(data.deptExchange.beginTime, "yyyy-MM-dd"));
				param.set("endTime", assemblys.dateToStr(data.deptExchange.endTime, "yyyy-MM-dd"));
			} else {
				//新增页面
				deptExchangeEdit.getCerInfo();
			}
			return data;
		});
	},
	
	//根据表单编号获取医师表单信息
	getCerInfo : function() {
		
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			$("#userCode").val(data.userCode);
			$("#userName").val(data.userName);
		});
	},
	
	saveDeptExchange : function(data) {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		$("#state").val(data);
		return $.ajax({
			url : basePath + "mdms/deptExchange/saveDeptExchange.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			if (data.hasSave == true) {
				assemblys.msg("保存成功", function() {
					if (param.get("deptExchangeController")) {
						parent.deptExchangeList.getDeptExchangePager();
					} else {
						if (parent.$("#exchangeManageDiv").length == 1) {
							var $tbody = parent.$("#exchangeManageDiv").empty();
							parent.otherFormDetail.deptExchangeList("exchangeManageDiv");
						} else {
							//parent.mdmsCustomList.initTable();
							//parent.deptExchangeList("exchangeManageDiv");
							parent.$("#rotationRecordDiv").empty();
							parent.rotationRecord.deptExchangeList("#rotationRecordDiv");
							assemblys.closeWindow();
						}
					}
					
					assemblys.closeWindow();
				});
				window.isSubmit = false;
				return data;
			} else {
				assemblys.msg("该时间段已有轮转记录！");
				window.isSubmit = false;
			}
			
		});
	},

}