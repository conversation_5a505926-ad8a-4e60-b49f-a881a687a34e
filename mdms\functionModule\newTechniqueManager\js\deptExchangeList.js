var deptExchangeList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		deptExchangeList.deptExchangeListInit().then(function(data) {
			deptExchangeList.getDeptExchangePager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : deptExchangeList.exportList
			} ];
			filterSearch.init(basePath, deptExchangeList.getFilterParams(data), deptExchangeList.getDeptExchangePager, customBtnDom);
			deptExchangeList.initLayuiForm();
		});
	},
	deptExchangeListInit : function() {
		return $.ajax({
			url : basePath + "mdms/deptExchange/deptExchangeListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			deptExchangeList.getDeptExchangePager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "自定义表单数据编码,工号,姓名,原科室Id,原科室名称,轮转科室Id,轮转科室名称",
			title : "关键字"
		} ];
		return params;
	},
	getDeptExchangePager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditDeptExchange"></i>';
				html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteDeptExchange"></i>';
				return html;
			}
		}, {
			title : '自定义表单数据编码',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.customFormFilledCode);
			}
		}, {
			title : '工号',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.userCode);
			}
		}, {
			title : '姓名',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.userName);
			}
		}, {
			title : '原科室Id',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.deptId);
			}
		}, {
			title : '原科室名称',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.deptName);
			}
		}, {
			title : '轮转时间',
			align : "left",
			templet : function(d) {
				return assemblys.dateToStr(d.exchangeTime);
			}
		}, {
			title : '轮转科室Id',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.exchangeDeptId);
			}
		}, {
			title : '轮转科室名称',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.exchangeDeptName);
			}
		}, {
			title : '轮转类型',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.exchangeType);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/deptExchange/getDeptExchangePager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditDeptExchange : deptExchangeList.toEditDeptExchange,
				deleteDeptExchange : deptExchangeList.deleteDeptExchange
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/deptExchange/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditDeptExchange : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditDeptExchange",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "deptExchangeEdit.html?funCode=" + param.get("funCode") + "&deptExchangeId=" + d.deptExchangeId
		});
	},
	deleteDeptExchange : function(d) {
		return $.ajax({
			url : basePath + "mdms/deptExchange/deleteDeptExchange.spring",
			type : "post",
			data : {
				deptExchangeId : d.deptExchangeId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				deptExchangeList.getDeptExchangePager();
			});
			return data;
		});
	}
}