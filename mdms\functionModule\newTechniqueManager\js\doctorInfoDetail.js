// 数据层
var doctorInfoDetail = {
	cache : {
		deptNames : {},
		userNames : {},
		userDeptNames : {}
	},
	// 初始化
	init : function() {
		
		doctorInfoDetail.getDetailInfo().then(function() {
			// 加载图标
			assemblys.getMenuIcon(param.get("funCode"));
			
			var customFormFilledCode = $("input[name='customFormFilledCode']").val();
			//查房权限iframe赋地址及参数
			$("#checkRoom").attr('src', "../../../mdms/functionModule/newTechniqueManager/checkRoomRightList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			//手术权限iframe赋地址及参数
			$("#operationRightFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/operationRightiList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			//处方权限iframe赋地址及参数
			$("#writeRightFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/writeRightList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			//麻醉权限iframe赋地址及参数
			$("#anaesthesiaFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/anaesthesiaRightList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			//定期考核iframe赋地址及参数
			$("#routineCheckFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/routineCheckList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			//"三基"培训考核iframe赋地址及参数
			$("#baseExamFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/baseExamList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			//个人知识技能和培训考核iframe赋地址及参数
			$("#personalExamFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/personalExamList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			
			$("#newProjectAndTechnologyFrame").attr('src', "../../../mdms/functionModule/newProjectAndTechnology/newProjectAndTechnologyList.html?viewStatus=1&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
			$("#newProjectAndTechnologyFrame").css("height", $(window).height() - 30);
			doctorInfoDetail.getDeptNameAndUserNames().then(function() {
				// 获取详情
				//doctorInfoDetail.getCustomFormDetail();
				//$("#detailFrame").attr('src',"../../../frame/customForm/customFormTemplate.html?compNo="+param.get("compNo")+"&prevCustomFormCode="+param.get("customFormCode")+"&prevCustomFormFilledCode="+customFormFilledCode+"&customFormCode="+param.get("customFormCode")+"&customFormFilledCode="+customFormFilledCode+"&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME+ "&type=1") ;
				$("#detailFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/newTechniqueEdit.html?compNo=" + param.get("compNo") + "&customFormCode=" + param.get("customFormCode") + "&customFormFilledCode=" + customFormFilledCode + "&appCode=" + assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME+ "");
				$("#detailFrame").css("height", $(window).height() - 30);
				
				//alert($("[id='detailFrame']").parent.find("input[name='CFDt0Xu5Jja8uvUf2Q40CX-0']").val());
				//$(window.frames["detailFrame"].document).find("input[name='CFDt0Xu5Jja8uvUf2Q40CX-0']").val();
				
				//获取表单工号、姓名
				doctorInfoDetail.getCerInfo();
				
				// 附件
				doctorInfoDetail.getAttaList();
				
				// 获取日志
				doctorInfoDetail.getOptList();
				
				// 获取流程图
				doctorInfoDetail.getFlowView();
				
				// 加载监听
				getnewTechniqueDetail.loadTab();
				
				// 获取证件
				doctorInfoDetail.getCertificate();
				// 获取转科
				doctorInfoDetail.getDeptExchange();
				
				// 获取惩奖信息
				doctorInfoDetail.getRewardPunishList();
				
				//获取医疗安全行为
				doctorInfoDetail.getBehaviorRecordList();
				//获取重大差错及事故处理情况
				doctorInfoDetail.getMajorsAccidentList();
				
			});
			
			//是否审核标识，1表示进入审核
			$("#onlyShow").val(param.get("onlyShow"));
			//隐藏新增
			$("button[onclick='getnewTechniqueDetail.addDeptExchange()']").addClass("layui-hide");
			$("button[onclick='getnewTechniqueDetail.addRewardPunishManage()']").addClass("layui-hide");
			$("button[onclick='getnewTechniqueDetail.addBehaviorRecord()']").addClass("layui-hide");
			$("button[onclick='getnewTechniqueDetail.addMajorsAccident()']").addClass("layui-hide");
			
		});
	},
	getDeptNameAndUserNames : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptNameAndUserNames.spring",
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					doctorInfoDetail.cache.deptNames = data.deptNames;
					doctorInfoDetail.cache.userNames = data.userNames;
					doctorInfoDetail.cache.userDeptNames = data.userDeptNames;
					
				}
			}
		});
	},
	getDetailInfo : function() {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getDetailInfo.spring",
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					
					if (data.data.isExist == 1) {
						var appCode = data.data.appCode;
						var businessCode = data.data.businessCode;
						var compNo = data.data.compNo;
						var customFormCode = data.data.customFormCode;
						var customFormFilledCode = data.data.customFormFilledCode;
						
						$("input[name='customFormFilledCode']").val(customFormFilledCode);
						$("input[name='compNo']").val(compNo);
						$("input[name='customFormCode']").val(customFormCode);
						$("input[name='appCode']").val(appCode);
						
						param.set("businessCode", businessCode);
						param.set("compNo", compNo);
						param.set("appCode", appCode);
						param.set("customFormCode", customFormCode);
						param.set("customFormFilledCode", customFormFilledCode);
					} else {
						var appCode = data.data.appCode;
						var businessCode = data.data.businessCode;
						var compNo = data.data.compNo;
						
						// 跳转自定义表单路径
						var url = "newTechniqueEdit.html?customFormBusinessCode=" + businessCode + "&compNo=" + compNo + "&appCode=" + appCode + "&type=1";
						// 弹窗方法
						layer.open({
							type : 2,
							skin : 'layui-layer-aems',
							title : '新增医师档案',
							maxmin : true,
							area : [ '100%', '100%' ], // 设置弹窗打开大小
							content : url
						});
					}
					
				}
			}
		});
	},
	
	// 获取详情
	getCustomFormDetail : function() {
		/*var path = basePath;
		var customFormCode = param.get("customFormCode");
		var customFormBusinessCode = param.get("businessCode");
		var customFormFilledCode = param.get("customFormFilledCode");
		var appCode = param.get("appCode");
		var compNo = param.get("compNo");
		var dom = "eventDetail";
		// 页面显示事件编号
		$("span[customFormFilledCode]").text(" - " + customFormFilledCode);
		//显示事件填报详情
		getCustomFormDetail.getCustomFormData(path, customFormCode, customFormFilledCode, appCode, dom, customFormBusinessCode, compNo);*/
	},
	// 附件
	getAttaList : function() {
		
		$.ajax({
			url : basePath + "frame/fileUpload/getAttachments.spring",
			dataType : "json",
			data : {
				"belongToCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var attachmentsList = data.attachmentsList;
					var length = attachmentsList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(attachmentsList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							newData.push(tool.createAttaTr(temp));
						});
						var $tbody = $("#attaDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
		
	},
	// 日志
	getOptList : function() {
		
		$.ajax({
			url : basePath + "/mdms/base/getLogInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode")
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var logInfoList = data.data.logInfo;
					var length = logInfoList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(logInfoList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							temp["deptName"] = doctorInfoDetail.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createLogTr(temp));
						});
						var $tbody = $("#optLogDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
	},
	
	// 获取证件
	getCertificate : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/certificateManagement/getUserCertificateInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode"),
				"customformfilledCode" : customformfilledCode
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var certificateInfoList = data.data.certificateInfo;
					var length = certificateInfoList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(certificateInfoList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							//temp["index"] = index + 1;
							//temp["deptName"] = doctorInfoDetail.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createCertificateTr(temp));
						});
						var $tbody = $("#certificateManagement").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
	},
	
	// 获取轮转信息
	getDeptExchange : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/deptExchange/getDeptExchangeInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var deptExchangeInfo = data.data.deptExchangeInfo;
					var length = deptExchangeInfo.length;
					if (length != 0) {
						// 生成TR
						var newData = [];
						$.each(deptExchangeInfo, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = doctorInfoDetail.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createDeptExchangeTr(temp));
						});
						var $tbody = $("#exchangeManageDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
				}
			}
		});
	},
	// 获取技术授权管理
	getTechnologyManage : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/certificateManagement/getUserCertificateInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode"),
				"customformfilledCode" : customformfilledCode
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					
				}
			}
		});
	},
	
	//获取惩奖信息
	getRewardPunishList : function() {
		
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/rewardPunishManage/getRewardPunishList.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var rewardPunishList = data.data.rewardPunishList;
					
					var length = rewardPunishList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(rewardPunishList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = doctorInfoDetail.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createRewardPunishTr(temp));
							
						});
						var $tbody = $("#rewardPunishManageDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						if ($("#onlyShow").val() == 1) {
							$("#jcDel").addClass("layui-hide");
							$("#jcEdit").removeClass("layui-icon layui-icon-edit");
							$("#jcEdit").addClass("layui-icon layui-icon-search");
						}
					}
					
				}
			}
		});
	},
	
	//获取医疗安全行为记录
	getBehaviorRecordList : function() {
		
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/behaviorRecord/getBehaviorRecordList.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var behaviorRecordList = data.data.behaviorRecordList;
					
					var length = behaviorRecordList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(behaviorRecordList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = doctorInfoDetail.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createBehaviorRecordTr(temp));
						});
						var $tbody = $("#behaviorRecordDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						if ($("#onlyShow").val() == 1) {
							$("#ylDel").addClass("layui-hide");
							$("#ylEdit").removeClass("layui-icon layui-icon-edit");
							$("#ylEdit").addClass("layui-icon layui-icon-search");
						}
					}
					
				}
			}
		});
	},
	
	//获取重大差错及事故处理情况
	getMajorsAccidentList : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/majorsAccident/getMajorsAccidentList.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var majorsAccidentList = data.data.majorsAccidentList;
					
					var length = majorsAccidentList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(majorsAccidentList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = doctorInfoDetail.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createMajorsAccidentTr(temp));
						});
						var $tbody = $("#majorsAccidentDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						if ($("#onlyShow").val() == 1) {
							$("#zdccDel").addClass("layui-hide");
							$("#zdccEdit").removeClass("layui-icon layui-icon-edit");
							$("#zdccEdit").addClass("layui-icon layui-icon-search");
						}
					}
					
				}
			}
		});
	},
	
	//根据表单编号获取医师表单信息
	getCerInfo : function() {
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			$("#userCode").val(data.userCode);
			$("#userName").val(data.userName);
		});
	},
	
	// 获取流程图
	getFlowView : function() {
		
		$.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getFlowInfoList.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode")
			},
			skipDataCheck : true,
			success : function(data) {
				
				if (data.result == "success") {
					// 数据
					var flowInfoList = data.data.flowInfoList;
					var length = flowInfoList.length;
					if (length != 0) {
						// 渲染
						var newData = [];
						// 生成TR
						$.each(flowInfoList, function(index, temp) {
							if (index == 0) {
								var title = "";
								if (temp.userList != undefined) {
									title += doctorInfoDetail.cache.deptNames[temp.userList[index].deptId] + "-" + doctorInfoDetail.cache.userNames[temp.userList[index].userCode];
								}
								newData.push(tool.createFlowOneLi(temp.ProcessName, title, temp.flowInfo));//hwx 第一个节点的数据
							} else {//hex 中间节点的数据
								if (temp.hasCurrent == 1) {
									param.set("processID", temp.ProcessID);
									param.set("seqNo", temp.SeqNo);
									param.set("auditIndex", Number(temp.SeqNo) + 1);
									//判断是否生成专家组打分
									if (temp.FunPointName != undefined) {
										param.set("funPointName", temp.FunPointName);
										param.set("rightPoint", temp.RightPoint);
									}
								}
								// 序号
								temp["index"] = index + 1;
								temp["followName"] = temp.ProcessName;
								newData.push(tool.createFlowLi(temp));
							}
						});
						//hwx 结束节点的数据
						newData.push(tool.createFlowLastLi());
						
						// 渲染
						assemblys.createElement(newData, $("#flowView")[0]);
						
						// 控制按钮
						getnewTechniqueDetail.loadButton();
					}
				}
			}
		});
	},

}

// 渲染层
var getnewTechniqueDetail = {
	
	// 加载tab监听
	loadTab : function() {
		$("#tabView").children("li").on("click", function() {
			$(this).addClass("layui-this");
			$(this).siblings().removeClass("layui-this");
			
			var index = $(this).index();
			var $content = $("#container").children("div:eq(" + index + ")");
			$content.show();
			$content.siblings().hide();
			
		});
		
		//手术权限具体li展开时重新加载一下数据
		$("#jsView").children("li").on("click", function() {
			var index = $(this).index();
			if ($(this).attr("class") == "layui-nav-item subject eventDetail layui-nav-itemed") {
				if ($(this).attr("id") == "shouShu") {
					$("#operationRightFrame")[0].contentWindow.operationRightiList.getOperationRightiPager();
				}
				if ($(this).attr("id") == "chaFang") {
					$("#checkRoom")[0].contentWindow.checkRoomRightList.getCheckRoomRightPager();
				}
				if ($(this).attr("id") == "chuFang") {
					$("#writeRightFrame")[0].contentWindow.writeRightList.getWriteRightPager();
				}
				if ($(this).attr("id") == "maZui") {
					$("#anaesthesiaFrame")[0].contentWindow.anaesthesiaRightList.getAnaesthesiaRightPager();
				}
				
			}
			;
			
		});
		
		//考核管理具体li展开时重新加载一下数据
		$("#examineView").children("li").on("click", function() {
			var index = $(this).index();
			if ($(this).attr("class") == "layui-nav-item subject eventDetail layui-nav-itemed") {
				if ($(this).attr("id") == "guDing") {
					$("#routineCheckFrame")[0].contentWindow.routineCheckList.getRoutineCheckPager();
				}
				if ($(this).attr("id") == "sanJi") {
					$("#baseExamFrame")[0].contentWindow.baseExamList.getBaseExamPager();
				}
				if ($(this).attr("id") == "zhiShi") {
					$("#personalExamFrame")[0].contentWindow.personalExamList.getPersonalExamPager();
				}
				
			}
			;
			
		});
	},
	// 新增
	add : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "certifiToEdit",
			area : [ '850px', '60%' ],
			title : false,
			scrollbar : false,
			content : "certificateManagementEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode")
		});
	},
	
	// 新增轮转登记
	addDeptExchange : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "deptExchangeEdit",
			area : [ '600px', '70%' ],
			title : false,
			scrollbar : false,
			content : "deptExchangeEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	// 新增奖励记录
	addRewardPunishManage : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "rewardPunishManageEdit",
			area : [ '900px', '60%' ],
			title : false,
			scrollbar : false,
			content : "rewardPunishManageEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	// 新增医疗安全行为记录
	addBehaviorRecord : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "behaviorRecordEdit",
			area : [ '850px', '60%' ],
			title : false,
			scrollbar : false,
			content : "behaviorRecordEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	// 新增重大差错及事故处理情况
	addMajorsAccident : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "majorsAccidentEdit",
			area : [ '900px', '70%' ],
			title : false,
			scrollbar : false,
			content : "majorsAccidentEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	//状态控制按钮显示
	loadButton : function() {
		var status = param.get("status");
		var $buttonDiv = $(".top_div");
		if (status == 1) {//待审
			var addbutton = '<button type="button" class="layui-btn layui-btn-sm sp" onclick="getnewTechniqueDetail.goNext();">审批</button>';
			addbutton += '<button type="button" class="layui-btn layui-btn-sm ht" onclick="getnewTechniqueDetail.goReturn();">回退</button>';
			addbutton += '<button type="button" class="layui-btn layui-btn-sm zf" onclick="getnewTechniqueDetail.goVoid();">作废</button>';
			$buttonDiv.append(addbutton);
		}
		if (status == -100) {//作废
			var addbutton = '<button type="button" class="layui-btn layui-btn-sm qh" onclick="getnewTechniqueDetail.goRetrieve();">取回</button>';
			$buttonDiv.append(addbutton);
		}
	},
	// 返回上一个页面
	back : function() {
		history.back();
	},
	//回退
	goReturn : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		var processID = param.get("processID");
		var seqNo = param.get("seqNo");
		var auditIndex = param.get("auditIndex");
		var customFormCode = param.get("customFormCode");
		
		// 编辑页面路径
		var url = basePath + "mdms/utils/processReturn/processReturn.html?customFormFilledCode=" + customFormFilledCode + "&processID=" + processID + "&seqNo=" + seqNo + "&auditIndex=" + auditIndex + "&customFormCode=" + customFormCode + "&state=" + 1;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '医师档案回退',
			maxmin : true,
			area : [ '80%', '100%' ], // 设置弹窗打开大小
			content : url
		});
	},
	//审批下一步
	goNext : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		var processID = param.get("processID");
		var seqNo = param.get("seqNo");
		var auditIndex = param.get("auditIndex");
		var customFormCode = param.get("customFormCode");
		
		// 编辑页面路径
		var url = basePath + "mdms/utils/processAudit/processAudit.html?customFormFilledCode=" + customFormFilledCode + "&processID=" + processID + "&seqNo=" + seqNo + "&auditIndex=" + auditIndex + "&customFormCode=" + customFormCode + "&state=" + 1;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '医师档案审批',
			maxmin : true,
			area : [ '80%', '100%' ], // 设置弹窗打开大小
			content : url
		});
	},
	//作废
	goVoid : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		var processID = param.get("processID");
		var seqNo = param.get("seqNo");
		var auditIndex = param.get("auditIndex");
		var customFormCode = param.get("customFormCode");
		// 编辑页面路径
		var url = basePath + "mdms/utils/processCancel/processCancel.html?customFormFilledCode=" + customFormFilledCode + "&processID=" + processID + "&seqNo=" + seqNo + "&auditIndex=" + auditIndex + "&customFormCode=" + customFormCode + "&state=" + 1;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '医师档案作废',
			maxmin : true,
			area : [ '80%', '90%' ], // 设置弹窗打开大小
			content : url
		});
	},
	//取回
	goRetrieve : function() {
		layer.confirm('确定取回该记录吗？', {
			icon : 3,
			title : '提示'
		}, function(index) {
			var customFormFilledCode = param.get("customFormFilledCode");
			var seqNo = param.get("seqNo");
			var customFormCode = param.get("customFormCode");
			var auditIndex = param.get("auditIndex");
			$.ajax({
				url : basePath + "mdms/base/retrieveEvent.spring",
				data : {
					"customFormFilledCode" : customFormFilledCode,
					"seqNo" : seqNo,
					"customFormCode" : customFormCode,
					"auditIndex" : auditIndex
				},
				skipDataCheck : true,
				success : function(data) {
					assemblys.msg("取回成功！", function() {
						//返回列表
						parent.history.back();
					});
				}
			});
			
		});
	},

}

//证件编辑
function certifiEdit(certificateId) {
	var id = parseInt(certificateId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "certifiToEdit",
		area : [ '850px', '60%' ],
		title : false,
		scrollbar : false,
		content : "certificateManagementEdit.html?certificateId=" + id + "&funCode=" + param.get("funCode")
	});
}
//删除证件
function certifiDel(certificateId) {
	
	$.ajax({
		url : basePath + "mdms/certificateManagement/deleteCertificateManagement.spring",
		type : "post",
		data : {
			certificateId : certificateId
		}
	}).then(function(data) {
		assemblys.msg("删除成功", function() {
			doctorInfoDetail.getCertificate();
		});
		return data;
	});
}

//轮转信息编辑
function deptExchangeEdit(deptExchangeId) {
	
	var id = parseInt(deptExchangeId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "certifiToEdit",
		area : [ '600px', '70%' ],
		title : false,
		scrollbar : false,
		content : "deptExchangeEdit.html?deptExchangeId=" + deptExchangeId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
	
	});
}
//轮转信息删除
function deptExchangeDel(deptExchangeId) {
	
	$.ajax({
		url : basePath + "/mdms/deptExchange/deleteDeptExchange.spring",
		type : "post",
		data : {
			deptExchangeId : deptExchangeId
		}
	}).then(function(data) {
		assemblys.msg("删除成功", function() {
			doctorInfoDetail.getDeptExchange();
		});
		return data;
	});
}

//奖励管理编辑
function rewardPunishManageEdit(rewardPunishManageId) {
	var id = parseInt(rewardPunishManageId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "rewardPunishManageEdit",
		area : [ '900px', '60%' ],
		title : false,
		scrollbar : false,
		content : "rewardPunishManageEdit.html?rewardPunishManageId=" + rewardPunishManageId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
	});
}

//奖励管理删除
function rewardPunishManageDel(rewardPunishManageId) {
	$.ajax({
		url : basePath + "/mdms/rewardPunishManage/deleteRewardPunishManage.spring",
		type : "post",
		data : {
			rewardPunishManageId : rewardPunishManageId
		}
	}).then(function(data) {
		assemblys.msg("删除成功", function() {
			doctorInfoDetail.getRewardPunishList();
		});
		return data;
	});
}

//医疗安全行为记录编辑
function behaviorRecordEdit(behaviorRecordId) {
	
	var id = parseInt(behaviorRecordId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "certifiToEdit",
		area : [ '600px', '60%' ],
		title : false,
		scrollbar : false,
		content : "behaviorRecordEdit.html?behaviorRecordId=" + behaviorRecordId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo")
	
	});
}

//医疗安全行为记录删除
function behaviorRecordDel(behaviorRecordId) {
	$.ajax({
		url : basePath + "/mdms/behaviorRecord/deleteBehaviorRecord.spring",
		type : "post",
		data : {
			behaviorRecordId : behaviorRecordId
		}
	}).then(function(data) {
		assemblys.msg("删除成功", function() {
			doctorInfoDetail.getBehaviorRecordList();
		});
		return data;
	});
}

//重大差错及事故处理编辑
function majorsAccidentEdit(majorsAccidentId) {
	
	var id = parseInt(majorsAccidentId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "majorsAccidentEdit",
		area : [ '900px', '70%' ],
		title : false,
		scrollbar : false,
		content : "majorsAccidentEdit.html?majorsAccidentId=" + majorsAccidentId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
	
	});
}

//重大差错及事故处理删除
function majorsAccidentDel(majorsAccidentId) {
	$.ajax({
		url : basePath + "/mdms/majorsAccident/deleteMajorsAccident.spring",
		type : "post",
		data : {
			majorsAccidentId : majorsAccidentId
		}
	}).then(function(data) {
		assemblys.msg("删除成功", function() {
			doctorInfoDetail.getMajorsAccidentList();
		});
		return data;
	});
}

doctorInfoDetail.init();

setTimeout(function() {
	$('#detailFrame').contents().find('#report').contents().find("input[name='CFDLIkWpkRCHDbeGiSkzxQ-0']").attr("readonly", "readonly");
	$('#detailFrame').contents().find('#report').contents().find("input[name='CFDt0Xu5Jja8uvUf2Q40CX-0']").attr("readonly", "readonly");
	$('#detailFrame').contents().find('#report').contents().find("button[id='commitButton']").addClass("layui-hide");
	$('#detailFrame').contents().find('#report').contents().find("button[id='backButton']").addClass("layui-hide");
	$('#detailFrame').contents().find('#report').contents().find("i[class='layui-icon2'][title='查询']").addClass("layui-hide");
}, 2000);