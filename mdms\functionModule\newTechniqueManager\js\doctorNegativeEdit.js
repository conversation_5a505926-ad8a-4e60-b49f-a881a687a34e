var doctorNegativeEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		doctorNegativeEdit.getDictList().then(function(data) {
			doctorNegativeEdit.getDoctorNegative().then(function(data) {
				if (param.get("onlyShow") == 1) {
					pubMethod.hideAddBtn();
					pubMethod.formReadOnly();
				}
				
			});
			$("#negativeType").val(param.get("negativeTypev"));
			layui.form.render();
		});
		doctorNegativeEdit.getSJXZDictList();
		doctorNegativeEdit.initLayui();
		param.set("customFormFilledCode", parent.param.get("customFormFilledCode"))
		pubMethod.getFormEmpInfo();
		$("span[class='head1_text fw700']").text("医师负面");
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			doctorNegativeEdit.saveDoctorNegative();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "datetime",
				//max : 'today',
				format : "yyyy-MM-dd HH:mm"
			});
		});
	},
	getDoctorNegative : function() {
		return $.ajax({
			url : basePath + "mdms/doctorNegative/getDoctorNegative.spring",
			data : {
				doctorNegativeId : param.get("doctorNegativeId")
			}
		}).then(function(data) {
			param.set(null, data.doctorNegative);
			doctorNegativeEdit.initTypeFile(data.fileList);
			return data;
		});
	},
	saveDoctorNegative : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		
		var fileList = [];
		$("#ueditorFileDiv-0").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			fileList.push(typeFiles);
		});
		var fileListJson = JSON.stringify(fileList);
		$("#fileListJson").val(fileListJson);
		
		return $.ajax({
			url : basePath + "mdms/doctorNegative/saveDoctorNegative.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				var $tbody = parent.$("#doctorNegativeDiv").empty();
				//parent.otherFormDetail.showDoctorNegativeList("DoctorNegative");
				parent.doctorNegative.init("#doctorNegativeDiv", 0);
				parent.window.negativeType = param.get("negativeTypev");
				assemblys.closeWindow();
			});
			return data;
		});
	},
	
	attaCallback : function(result) {// 自定义上传图片后的回调
		var fileHtml = "";
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		for (var i = 0; i < result.length; i++) {
			fileHtml += "<li style='width: 500px;'>";
			fileHtml += "	<em title=\"" + result[i].title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + result[i].title + "&nbsp;&nbsp;" + result[i].size + "</em>";
			var suffix = result[i].type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" id=\"preview\" onclick=\"pubUploader.preview('" + result[i].title + "','" + result[i].url + "');\"  >预览图片</a></span>";
			}
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + result[i].title + "','" + result[i].url + "');\">下载</a></span>";
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove attaDelete\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a></span>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + result[i].title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + result[i].url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + result[i].size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + result[i].type + "\"/>";
			fileHtml += "</li>";
		}
		$("#ueditorFileDiv-0").append(fileHtml);
		if (param.get("onlyShow") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
		
	},
	initTypeFile : function(certifiFileList) {
		var filesData = certifiFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.title = typeFileTemp.attaName;
				files.url = typeFileTemp.attaUrl;
				files.size = typeFileTemp.attaSize;
				files.type = typeFileTemp.attaType;
				result.push(files);
			}
			//param.set("fileIndex", i);
			doctorNegativeEdit.attaCallback(result);
		}
		
	},
	getDictList : function() {
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.FMSJLX,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.dictList) {
					var htmlTemp = "";
					for (var i = 0; i < data.dictList.length; i++) {
						var temp = data.dictList[i];
						htmlTemp += "<option value='" + temp["dictCode"] + "' >" + temp["dictName"] + "</option>";
					}
					$("#negativeType").append(htmlTemp);
					layui.form.render();
				}
				return data;
			}
		});
	},
	getSJXZDictList : function() {
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.YLANSJXZ,
				"appCode" : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.dictList) {
					var htmlTemp = "";
					for (var i = 0; i < data.dictList.length; i++) {
						var temp = data.dictList[i];
						htmlTemp += "<option value='" + temp["dictCode"] + "' >" + temp["dictName"] + "</option>";
					}
					$("#natureEvent").append(htmlTemp);
					layui.form.render();
				}
				return data;
			}
		});
	}
}
