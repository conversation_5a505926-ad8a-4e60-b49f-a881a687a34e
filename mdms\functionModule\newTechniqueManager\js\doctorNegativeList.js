var doctorNegativeList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		doctorNegativeList.doctorNegativeListInit().then(function(data) {
			doctorNegativeList.getDoctorNegativePager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : doctorNegativeList.exportList
			} ];
			filterSearch.init(basePath, doctorNegativeList.getFilterParams(data), doctorNegativeList.getDoctorNegativePager, customBtnDom);
			doctorNegativeList.initLayuiForm();
		});
	},
	doctorNegativeListInit : function() {
		return $.ajax({
			url : basePath + "mdms/doctorNegative/doctorNegativeListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", doctorNegativeList.stateMap[state].color).text(doctorNegativeList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			doctorNegativeList.getDoctorNegativePager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "表单编码,用户编码",
			title : "关键字"
		} ];
		return params;
	},
	getDoctorNegativePager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditDoctorNegative"></i>';
				html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteDoctorNegative"></i>';
				return html;
			}
		}, {
			title : '负面事件',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.negativeType);
			}
		}, {
			title : '负面发生日期',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.negativeDate);
			}
		}, {
			title : '备注',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.remark);
			}
		}, {
			title : '表单编码',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.customFormFilledCode);
			}
		}, {
			title : '用户编码',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.userCode);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/doctorNegative/getDoctorNegativePager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditDoctorNegative : doctorNegativeList.toEditDoctorNegative,
				deleteDoctorNegative : doctorNegativeList.deleteDoctorNegative
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/doctorNegative/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditDoctorNegative : function(d, type) {
		var html = "doctorNegativeEdit.html";
		if (type == 1) {
			html = "doctorNegativeView.html";
		}
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditDoctorNegative",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?onlyShow=" + type + "&funCode=" + param.get("funCode") + "&doctorNegativeId=" + d.doctorNegativeId
		});
	},
	deleteDoctorNegative : function(d) {
		return $.ajax({
			url : basePath + "mdms/doctorNegative/deleteDoctorNegative.spring",
			type : "post",
			data : {
				doctorNegativeId : d.doctorNegativeId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				doctorNegativeList.getDoctorNegativePager();
			});
			return data;
		});
	}
}