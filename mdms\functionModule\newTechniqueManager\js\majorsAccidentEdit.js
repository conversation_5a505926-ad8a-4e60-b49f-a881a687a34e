var majorsAccidentEdit = {
	init : function() {
		//assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		majorsAccidentEdit.getAccidentLevelList().then(function(data) {
			majorsAccidentEdit.getResponsibilityList().then(function(data) {
				majorsAccidentEdit.getMajorsAccident();
				majorsAccidentEdit.initLayui();
			});
		});
		
		$("#customFormFilledCode").val(param.get("customFormFilledCode"));
		majorsAccidentEdit.initLayui();
		
		if (param.get("majorsAccidentId") == 0) {
			pubMethod.getFormEmpInfo();
		}
		
		if (param.get("showOrEdit") == 1) {
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
		}
		
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			majorsAccidentEdit.saveMajorsAccident();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "datetime",
//				range : "~",
//				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	getMajorsAccident : function() {
		return $.ajax({
			url : basePath + "mdms/majorsAccident/getMajorsAccident.spring",
			data : {
				majorsAccidentId : param.get("majorsAccidentId")
			}
		}).then(function(data) {
			param.set(null, data.majorsAccident);
			majorsAccidentEdit.initTypeFile(data.fileList);
			return data;
		});
	},
	saveMajorsAccident : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		
		var majorsAccidentFileList = [];
		$("#ueditorFileDiv-0").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			majorsAccidentFileList.push(typeFiles);
		});
		var majorsAccidentFileListJson = JSON.stringify(majorsAccidentFileList);
		$("#majorsAccidentFileListJson").val(majorsAccidentFileListJson);
		
		return $.ajax({
			url : basePath + "mdms/majorsAccident/saveMajorsAccident.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				//parent.getNewTechniqueData.getMajorsAccidentList();
				var $tbody = parent.$("#majorsAccidentDiv").empty();
				//hwx 2022-09-23保存后回调
				parent.majorsAccident.init("#majorsAccidentDiv");
//				parent.otherFormDetail.showMajorsAccidentList("majorsAccidentDiv");
				assemblys.closeWindow();
			});
			return data;
		});
	},
	
	getAccidentLevelList : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.ACCIDENTLEVEL,
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
			},
			error : function() {
			}
		}).then(function(data) {
			var html = "";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
			})
			$("#accidentLevel").html(html);
			return data;
		});
		layui.form.render();
	},
	
	getResponsibilityList : function() {
		// 返回JSON形式
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.RESPONSIBILITY,
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
		}).then(function(data) {
			var html = "";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
			})
			$("#responsibility").html(html);
			return data;
		});
		
		layui.form.render();
	},
	
	attaCallback : function(result) {// 自定义上传图片后的回调
		var fileHtml = "";
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		for (var i = 0; i < result.length; i++) {
			fileHtml += "<li style='width: 500px;'>";
			fileHtml += "	<em title=\"" + result[i].title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + result[i].title + "&nbsp;&nbsp;" + result[i].size + "</em>";
			var suffix = result[i].type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" id=\"preview\" onclick=\"pubUploader.preview('" + result[i].title + "','" + result[i].url + "');\"  >预览图片</a></span>";
			}
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + result[i].title + "','" + result[i].url + "');\">下载</a></span>";
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove attaDelete\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a></span>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + result[i].title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + result[i].url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + result[i].size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + result[i].type + "\"/>";
			fileHtml += "</li>";
		}
		$("#ueditorFileDiv-0").append(fileHtml);
		if (param.get("showOrEdit") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
	},
	
	initTypeFile : function(majorsAccidentFileList) {
		var filesData = majorsAccidentFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.title = typeFileTemp.AttaName;
				files.url = typeFileTemp.AttaUrl;
				files.size = typeFileTemp.AttaSize;
				files.type = typeFileTemp.AttaType;
				result.push(files);
			}
			//param.set("fileIndex", i);
			majorsAccidentEdit.attaCallback(result);
		}
		
	}

}