var majorsAccidentList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		majorsAccidentList.majorsAccidentListInit().then(function(data) {
			majorsAccidentList.getMajorsAccidentPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : majorsAccidentList.exportList
			} ];
			filterSearch.init(basePath, majorsAccidentList.getFilterParams(data), majorsAccidentList.getMajorsAccidentPager, customBtnDom);
			majorsAccidentList.initLayuiForm();
		});
	},
	majorsAccidentListInit : function() {
		return $.ajax({
			url : basePath + "mdms/majorsAccident/majorsAccidentListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "datetime",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd HH:mm"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", majorsAccidentList.stateMap[state].color).text(majorsAccidentList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			majorsAccidentList.getMajorsAccidentPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "自定义表单数据编码,发生原由,事故级别,处分情况",
			title : "关键字"
		} ];
		return params;
	},
	getMajorsAccidentPager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditMajorsAccident"></i>';
				html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteMajorsAccident"></i>';
				return html;
			}
		}, {
			title : '自定义表单数据编码',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.customFormFilledCode);
			}
		}, {
			title : '事故发生时间',
			align : "left",
			templet : function(d) {
				return assemblys.dateToStr(d.accidentTime);
			}
		}, {
			title : '发生原由',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.reason);
			}
		}, {
			title : '事故级别',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.accidentLevel);
			}
		}, {
			title : '处分情况',
			align : "left",
			templet : function(d) {
				return assemblys.htmlEncode(d.punishment);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/majorsAccident/getMajorsAccidentPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditMajorsAccident : majorsAccidentList.toEditMajorsAccident,
				deleteMajorsAccident : majorsAccidentList.deleteMajorsAccident
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/majorsAccident/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditMajorsAccident : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditMajorsAccident",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "majorsAccidentEdit.html?funCode=" + param.get("funCode") + "&majorsAccidentId=" + d.majorsAccidentId
		});
	},
	deleteMajorsAccident : function(d) {
		return $.ajax({
			url : basePath + "mdms/majorsAccident/deleteMajorsAccident.spring",
			type : "post",
			data : {
				majorsAccidentId : d.majorsAccidentId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				majorsAccidentList.getMajorsAccidentPager();
			});
			return data;
		});
	}
}