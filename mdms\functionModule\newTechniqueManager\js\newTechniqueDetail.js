// 数据层
var getNewTechniqueData = {
	cache : {
		deptNames : {},
		userNames : {},
		userDeptNames : {}
	},
	// 初始化
	init : function() {
		
		// 加载图标
		assemblys.getMenuIcon(param.get("funCode"));
		
		var customFormFilledCode = $("input[name='customFormFilledCode']").val();
		//查房权限iframe赋地址及参数
		$("#checkRoom").attr('src', "../../../mdms/functionModule/newTechniqueManager/checkRoomRightList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		//手术权限iframe赋地址及参数
		$("#operationRightFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/operationRightiList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		//处方权限iframe赋地址及参数
		$("#writeRightFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/writeRightList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		//麻醉权限iframe赋地址及参数
		$("#anaesthesiaFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/anaesthesiaRightList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		//定期考核iframe赋地址及参数
		$("#routineCheckFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/routineCheckList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		//"三基"培训考核iframe赋地址及参数
		$("#baseExamFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/baseExamList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		//个人知识技能和培训考核iframe赋地址及参数
		$("#personalExamFrame").attr('src', "../../../mdms/functionModule/newTechniqueManager/personalExamList.html?compNo=" + param.get("compNo") + "&customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		//新技术新项目iframe赋地址及参数
		$("#newProjectAndTechnologyFrame").attr('src', "../../../mdms/functionModule/newProjectAndTechnology/newProjectAndTechnologyList.html?viewStatus=2&compNo=" + param.get("compNo") + "&doctorFormCode=" + customFormFilledCode + "&funCode=" + param.get("funCode"));
		$("#newProjectAndTechnologyFrame").css("height", $(window).height() - 30);
		
		getNewTechniqueData.getDeptNameAndUserNames().then(function() {
			// 获取详情
			getNewTechniqueData.getCustomFormDetail();
			
			//获取表单工号、姓名
			getNewTechniqueData.getCerInfo();
			
			// 附件
			getNewTechniqueData.getAttaList();
			
			// 获取日志
			getNewTechniqueData.getOptList();
			
			// 获取流程图
			getNewTechniqueData.getFlowView();
			
			// 加载监听
			getnewTechniqueDetail.loadTab();
			
			// 获取证件
			getNewTechniqueData.getCertificate();
			// 获取转科
			getNewTechniqueData.getDeptExchange();
			
			// 获取惩奖信息
			getNewTechniqueData.getRewardPunishList();
			
			//获取医疗安全行为
			getNewTechniqueData.getBehaviorRecordList();
			//获取重大差错及事故处理情况
			getNewTechniqueData.getMajorsAccidentList();
			
		});
		//是否审核标识，1表示进入审核
		$("#onlyShow").val(param.get("onlyShow"));
		$("#isApprove").val(param.get("isApprove"));
		if (param.get("onlyShow") == 0) {
			$("#approvePass").addClass("layui-hide");
			$("#approveBack").addClass("layui-hide");
		} else {
			$("button[onclick='getnewTechniqueDetail.addDeptExchange()']").addClass("layui-hide");
			$("button[onclick='getnewTechniqueDetail.addRewardPunishManage()']").addClass("layui-hide");
			$("button[onclick='getnewTechniqueDetail.addBehaviorRecord()']").addClass("layui-hide");
			$("button[onclick='getnewTechniqueDetail.addMajorsAccident()']").addClass("layui-hide");
			
		}
		
	},
	getDeptNameAndUserNames : function() {
		return $.ajax({
			url : basePath + "frame/common/getDeptNameAndUserNames.spring",
			dataType : "json",
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					getNewTechniqueData.cache.deptNames = data.deptNames;
					getNewTechniqueData.cache.userNames = data.userNames;
					getNewTechniqueData.cache.userDeptNames = data.userDeptNames;
					
				}
			}
		});
	},
	// 获取详情
	getCustomFormDetail : function() {
		var path = basePath;
		var customFormCode = param.get("customFormCode");
		var customFormBusinessCode = param.get("businessCode");
		var customFormFilledCode = param.get("customFormFilledCode");
		var appCode = param.get("appCode");
		var compNo = param.get("compNo");
		var dom = "eventDetail";
		// 页面显示事件编号
		$("span[customFormFilledCode]").text(" - " + customFormFilledCode);
		//显示事件填报详情
		getCustomFormDetail.getCustomFormData(path, customFormCode, customFormFilledCode, appCode, dom, customFormBusinessCode, compNo);
	},
	// 附件
	getAttaList : function() {
		
		$.ajax({
			url : basePath + "frame/fileUpload/getAttachments.spring",
			dataType : "json",
			data : {
				"belongToCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var attachmentsList = data.attachmentsList;
					var length = attachmentsList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(attachmentsList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							newData.push(tool.createAttaTr(temp));
						});
						var $tbody = $("#attaDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
		
	},
	// 日志
	getOptList : function() {
		
		$.ajax({
			url : basePath + "/mdms/base/getLogInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode")
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var logInfoList = data.data.logInfo;
					var length = logInfoList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(logInfoList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							temp["deptName"] = getNewTechniqueData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createLogTr(temp));
						});
						var $tbody = $("#optLogDetail").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
					}
					
				}
			}
		});
	},
	
	// 获取证件
	getCertificate : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/certificateManagement/getUserCertificateInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode"),
				"customformfilledCode" : customformfilledCode
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var certificateInfoList = data.data.certificateInfo;
					var length = certificateInfoList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(certificateInfoList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							//temp["index"] = index + 1;
							//temp["deptName"] = getNewTechniqueData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createCertificateTr(temp));
						});
						var $tbody = $("#certificateManagement").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						
						if ($("#onlyShow").val() == 1 && $("#isApprove").val() == 1) {
							$("#zjglDel").addClass("layui-hide");
							$("#zjglEdit").removeClass("layui-icon layui-icon-edit");
							$("#zjglEdit").addClass("layui-icon layui-icon-search");
						}
						
					}
					
				}
			}
		});
	},
	
	// 获取轮转信息
	getDeptExchange : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/deptExchange/getDeptExchangeInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var deptExchangeInfo = data.data.deptExchangeInfo;
					var length = deptExchangeInfo.length;
					if (length != 0) {
						// 生成TR
						var newData = [];
						$.each(deptExchangeInfo, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = getNewTechniqueData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createDeptExchangeTr(temp));
						});
						var $tbody = $("#exchangeManageDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						layui.form.render();
					}
				}
			}
		});
	},
	// 获取技术授权管理
	getTechnologyManage : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/certificateManagement/getUserCertificateInfo.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
				"businessCode" : param.get("customFormCode"),
				"customformfilledCode" : customformfilledCode
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					
				}
			}
		});
	},
	
	//获取惩奖信息
	getRewardPunishList : function() {
		
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/rewardPunishManage/getRewardPunishList.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var rewardPunishList = data.data.rewardPunishList;
					
					var length = rewardPunishList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(rewardPunishList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = getNewTechniqueData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createRewardPunishTr(temp));
						});
						var $tbody = $("#rewardPunishManageDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						if ($("#onlyShow").val() == 1) {
							$("#jcDel").addClass("layui-hide");
							$("#jcEdit").removeClass("layui-icon layui-icon-edit");
							$("#jcEdit").addClass("layui-icon layui-icon-search");
						}
					}
					
				}
			}
		});
	},
	
	//获取医疗安全行为记录
	getBehaviorRecordList : function() {
		
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/behaviorRecord/getBehaviorRecordList.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var behaviorRecordList = data.data.behaviorRecordList;
					
					var length = behaviorRecordList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(behaviorRecordList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = getNewTechniqueData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createBehaviorRecordTr(temp));
						});
						var $tbody = $("#behaviorRecordDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						if ($("#onlyShow").val() == 1) {
							$("#ylDel").addClass("layui-hide");
							$("#ylEdit").removeClass("layui-icon layui-icon-edit");
							$("#ylEdit").addClass("layui-icon layui-icon-search");
						}
					}
					
				}
			}
		});
	},
	
	//获取重大差错及事故处理情况
	getMajorsAccidentList : function() {
		var customformfilledCode = $("input[name='customFormFilledCode']").val();
		$.ajax({
			url : basePath + "/mdms/majorsAccident/getMajorsAccidentList.spring",
			dataType : "json",
			data : {
				"customFormFilledCode" : param.get("customFormFilledCode"),
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					// 数据
					var majorsAccidentList = data.data.majorsAccidentList;
					
					var length = majorsAccidentList.length;
					if (length != 0) {
						var newData = [];
						// 生成TR
						$.each(majorsAccidentList, function(index, temp) {
							// 序号
							temp["index"] = index + 1;
							temp["index"] = index + 1;
							//temp["deptName"] = getNewTechniqueData.cache.deptNames[temp.OptDeptID];
							
							newData.push(tool.createMajorsAccidentTr(temp));
						});
						var $tbody = $("#majorsAccidentDiv").find("tbody");
						$tbody.empty();
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						if ($("#onlyShow").val() == 1) {
							$("#zdccDel").addClass("layui-hide");
							$("#zdccEdit").removeClass("layui-icon layui-icon-edit");
							$("#zdccEdit").addClass("layui-icon layui-icon-search");
						}
					}
					
				}
			}
		});
	},
	
	// 获取流程图
	getFlowView : function() {
		approvalFlow.initFlow(param.get("appCode"), basePath, param.get("customFormFilledCode"), param.get("funCode"), "div.flowView");
	},
	
	//根据表单编号获取医师表单信息
	getCerInfo : function() {
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			$("#userCode").val(data.userCode);
			$("#userName").val(data.userName);
		});
	},

// 获取流程图
/*getFlowView : function() {
	
	$.ajax({
		url : basePath + "mdms/functionModule/newTechniqueManager/getFlowInfoList.spring",
		dataType : "json",
		data : {
			"customFormFilledCode" : param.get("customFormFilledCode"),
			"businessCode" : param.get("customFormCode")
		},
		skipDataCheck : true,
		success : function(data) {
			
			if (data.result == "success") {
				// 数据
				var flowInfoList = data.data.flowInfoList;
				var length = flowInfoList.length;
				if (length != 0) {
					// 渲染
					var newData = [];
					// 生成TR
					$.each(flowInfoList, function(index, temp) {
						if (index == 0) {
							var title = "";
							if(temp.userList!=undefined){
								title+= getNewTechniqueData.cache.deptNames[temp.userList[index].deptId] + "-" + getNewTechniqueData.cache.userNames[temp.userList[index].userCode];
							}
							newData.push(tool.createFlowOneLi(temp.ProcessName,title,temp.flowInfo));//hwx 第一个节点的数据
						}else{//hex 中间节点的数据
							if(temp.hasCurrent==1){
								param.set("processID",temp.ProcessID);
								param.set("seqNo",temp.SeqNo);
								param.set("auditIndex",Number(temp.SeqNo)+1);
								//判断是否生成专家组打分
								if(temp.FunPointName!=undefined){
									param.set("funPointName",temp.FunPointName);
									param.set("rightPoint",temp.RightPoint);
								}
							}
							// 序号
							temp["index"] = index + 1;
							temp["followName"] = temp.ProcessName;
							newData.push(tool.createFlowLi(temp));
						}
					});
					//hwx 结束节点的数据
					newData.push(tool.createFlowLastLi());
					
					// 渲染
					assemblys.createElement(newData, $("#flowView")[0]);
					
					// 控制按钮
					getnewTechniqueDetail.loadButton();
				}
			}
		}
	});
},*/

}

// 渲染层
var getnewTechniqueDetail = {
	
	// 加载tab监听
	loadTab : function() {
		$("#tabView").children("li").on("click", function() {
			$(this).addClass("layui-this");
			$(this).siblings().removeClass("layui-this");
			
			var index = $(this).index();
			var $content = $("#container").children("div:eq(" + index + ")");
			$content.show();
			$content.siblings().hide();
			
		});
		
		//手术权限具体li展开时重新加载一下数据
		$("#jsView").children("li").on("click", function() {
			var index = $(this).index();
			if ($(this).attr("class") == "layui-nav-item subject eventDetail layui-nav-itemed") {
				if ($(this).attr("id") == "shouShu") {
					$("#operationRightFrame")[0].contentWindow.operationRightiList.getOperationRightiPager();
				}
				if ($(this).attr("id") == "chaFang") {
					$("#checkRoom")[0].contentWindow.checkRoomRightList.getCheckRoomRightPager();
				}
				if ($(this).attr("id") == "chuFang") {
					$("#writeRightFrame")[0].contentWindow.writeRightList.getWriteRightPager();
				}
				if ($(this).attr("id") == "maZui") {
					$("#anaesthesiaFrame")[0].contentWindow.anaesthesiaRightList.getAnaesthesiaRightPager();
				}
				
			}
			;
			
		});
		
		//考核管理具体li展开时重新加载一下数据
		$("#examineView").children("li").on("click", function() {
			var index = $(this).index();
			if ($(this).attr("class") == "layui-nav-item subject eventDetail layui-nav-itemed") {
				if ($(this).attr("id") == "guDing") {
					$("#routineCheckFrame")[0].contentWindow.routineCheckList.getRoutineCheckPager();
				}
				if ($(this).attr("id") == "sanJi") {
					$("#baseExamFrame")[0].contentWindow.baseExamList.getBaseExamPager();
				}
				if ($(this).attr("id") == "zhiShi") {
					$("#personalExamFrame")[0].contentWindow.personalExamList.getPersonalExamPager();
				}
				
			}
			;
			
		});
		
		//轮转执行多选框监听
		layui.form.on("checkbox(deptExchangeCheckedAllId)", function(data) {
			if (data.elem.checked) {
				$("input[name=deptExchangeCheckedIds]").not(":checked").next().click();
			} else {
				$("input[name=deptExchangeCheckedIds]:checked").next().click();
			}
		});
	},
	
	//执行轮转
	execDeptExchange : function() {
		var deptExchangeCheckedIds = param.get("deptExchangeCheckedIds").toString();
		$.ajax({
			url : basePath + "/mdms/deptExchange/execDeptExchange.spring",
			dataType : "json",
			type : "post",
			data : {
				"deptExchangeCheckedIds" : deptExchangeCheckedIds
			}
		}).then(function(data) {
			assemblys.msg("执行成功", function() {
				getNewTechniqueData.getDeptExchange();
				layui.form.render();
			});
			return data;
		});
		
	},
	
	// 新增
	add : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "certifiToEdit",
			area : [ '850px', '60%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/certificateManagementEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode")
		});
	},
	
	// 新增轮转登记
	addDeptExchange : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "deptExchangeEdit",
			area : [ '600px', '70%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/deptExchangeEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	// 新增奖励记录
	addRewardPunishManage : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "rewardPunishManageEdit",
			area : [ '900px', '80%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/rewardPunishManageEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	// 新增医疗安全行为记录
	addBehaviorRecord : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "behaviorRecordEdit",
			area : [ '850px', '80%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/behaviorRecordEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	// 新增重大差错及事故处理情况
	addMajorsAccident : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "majorsAccidentEdit",
			area : [ '900px', '80%' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/majorsAccidentEdit.html?customFormFilledCode=" + customFormFilledCode + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + param.get("userCode") + "&userName=" + param.get("userName")
		});
	},
	
	//状态控制按钮显示
	loadButton : function() {
		var status = param.get("status");
		var $buttonDiv = $(".top_div");
		if (status == 1) {//待审
			var addbutton = '<button type="button" class="layui-btn layui-btn-sm sp" onclick="getnewTechniqueDetail.goNext();">审批</button>';
			addbutton += '<button type="button" class="layui-btn layui-btn-sm ht" onclick="getnewTechniqueDetail.goReturn();">回退</button>';
			addbutton += '<button type="button" class="layui-btn layui-btn-sm zf" onclick="getnewTechniqueDetail.goVoid();">作废</button>';
			$buttonDiv.append(addbutton);
		}
		if (status == -100) {//作废
			var addbutton = '<button type="button" class="layui-btn layui-btn-sm qh" onclick="getnewTechniqueDetail.goRetrieve();">取回</button>';
			$buttonDiv.append(addbutton);
		}
	},
	// 返回上一个页面
	back : function() {
		history.back();
	},
	//回退
	goReturn : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		var processID = param.get("processID");
		var seqNo = param.get("seqNo");
		var auditIndex = param.get("auditIndex");
		var customFormCode = param.get("customFormCode");
		
		// 编辑页面路径
		var url = basePath + "mdms/utils/processReturn/processReturn.html?customFormFilledCode=" + customFormFilledCode + "&processID=" + processID + "&seqNo=" + seqNo + "&auditIndex=" + auditIndex + "&customFormCode=" + customFormCode + "&state=" + 1;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '医师档案回退',
			maxmin : true,
			area : [ '80%', '100%' ], // 设置弹窗打开大小
			content : url
		});
	},
	//审批下一步
	goNext : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		var processID = param.get("processID");
		var seqNo = param.get("seqNo");
		var auditIndex = param.get("auditIndex");
		var customFormCode = param.get("customFormCode");
		
		// 编辑页面路径
		var url = basePath + "mdms/utils/processAudit/processAudit.html?customFormFilledCode=" + customFormFilledCode + "&processID=" + processID + "&seqNo=" + seqNo + "&auditIndex=" + auditIndex + "&customFormCode=" + customFormCode + "&state=" + 1;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '医师档案审批',
			maxmin : true,
			area : [ '80%', '100%' ], // 设置弹窗打开大小
			content : url
		});
	},
	//作废
	goVoid : function() {
		var customFormFilledCode = param.get("customFormFilledCode");
		var processID = param.get("processID");
		var seqNo = param.get("seqNo");
		var auditIndex = param.get("auditIndex");
		var customFormCode = param.get("customFormCode");
		// 编辑页面路径
		var url = basePath + "mdms/utils/processCancel/processCancel.html?customFormFilledCode=" + customFormFilledCode + "&processID=" + processID + "&seqNo=" + seqNo + "&auditIndex=" + auditIndex + "&customFormCode=" + customFormCode + "&state=" + 1;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '医师档案作废',
			maxmin : true,
			area : [ '80%', '90%' ], // 设置弹窗打开大小
			content : url
		});
	},
	//取回
	goRetrieve : function() {
		layer.confirm('确定取回该记录吗？', {
			icon : 3,
			title : '提示'
		}, function(index) {
			var customFormFilledCode = param.get("customFormFilledCode");
			var seqNo = param.get("seqNo");
			var customFormCode = param.get("customFormCode");
			var auditIndex = param.get("auditIndex");
			$.ajax({
				url : basePath + "mdms/base/retrieveEvent.spring",
				data : {
					"customFormFilledCode" : customFormFilledCode,
					"seqNo" : seqNo,
					"customFormCode" : customFormCode,
					"auditIndex" : auditIndex
				},
				skipDataCheck : true,
				success : function(data) {
					assemblys.msg("取回成功！", function() {
						//返回列表
						parent.history.back();
					});
				}
			});
			
		});
	},

}

//证件编辑
function certifiEdit(certificateId, showOrEdit) {
	var id = parseInt(certificateId);
	var html = "certificateManagementEdit.html";
	if (showOrEdit == 1) {
		html = "certificateManagementView.html";
	}
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "certifiToEdit",
		area : [ '850px', '60%' ],
		title : false,
		scrollbar : false,
		content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&certificateId=" + id + "&funCode=" + param.get("funCode")
	});
}
//删除证件
function certifiDel(certificateId) {
	layer.confirm("确定要删除吗？", function() {
		$.ajax({
			url : basePath + "mdms/certificateManagement/deleteCertificateManagement.spring",
			type : "post",
			data : {
				certificateId : certificateId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				var $tbody = $("#certificateManagement").empty();
				otherFormDetail.certifiCateList("certificateManagement");
			});
			return data;
		});
	})

}

function toEditDoctorNegative(id, type) {
	var html = "doctorNegativeEdit.html";
	if (type == 1) {
		html = "doctorNegativeView.html";
	}
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "toEditDoctorNegative",
		area : [ '850px', '450px' ],
		title : false,
		scrollbar : false,
		content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?onlyShow=" + type + "&funCode=" + param.get("funCode") + "&doctorNegativeId=" + id
	});
}

function deleteDoctorNegative(id) {
	layer.confirm("确定要删除吗？", function() {
		return $.ajax({
			url : basePath + "mdms/doctorNegative/deleteDoctorNegative.spring",
			type : "post",
			data : {
				doctorNegativeId : id
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				var $tbody = $("#DoctorNegative").empty();
				otherFormDetail.showDoctorNegativeList("DoctorNegative");
			});
			return data;
		});
	})
}

//轮转信息编辑
function deptExchangeEdit(deptExchangeId, showOrEdit) {
	var height = "450px";
	var html = "deptExchangeEdit.html";
	if (showOrEdit == 1) {
		html = "deptExchangeView.html";
		height = "300px";
	}
	var id = parseInt(deptExchangeId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "certifiToEdit",
		area : [ '600px', height ],
		title : false,
		scrollbar : false,
		content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&deptExchangeId=" + deptExchangeId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
	
	});
}
//轮转信息删除
function deptExchangeDel(deptExchangeId) {
	layer.confirm("确定要删除吗？", function() {
		$.ajax({
			url : basePath + "/mdms/deptExchange/deleteDeptExchange.spring",
			type : "post",
			data : {
				deptExchangeId : deptExchangeId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				//getNewTechniqueData.getDeptExchange();
				var $tbody = $("#exchangeManageDiv").empty();
				otherFormDetail.deptExchangeList("exchangeManageDiv");
			});
			return data;
		});
	});
}

//轮转详情
function searchExchangeDetail(deptExchangeId) {
	var id = parseInt(deptExchangeId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "certifiToEdit",
		area : [ '600px', '80%' ],
		title : false,
		scrollbar : false,
		content : "deptExchangeDetail.html?deptExchangeId=" + deptExchangeId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
	
	});
}

//奖励管理编辑
function rewardPunishManageEdit(rewardPunishManageId, showOrEdit) {
	var id = parseInt(rewardPunishManageId);
	var html = "rewardPunishManageEdit.html";
	if (showOrEdit == 1) {
		html = "rewardPunishManageView.html";
	}
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "rewardPunishManageEdit",
		area : [ '900px', '60%' ],
		title : false,
		scrollbar : false,
		content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&rewardPunishManageId=" + rewardPunishManageId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
	});
}

//奖励管理删除
function rewardPunishManageDel(rewardPunishManageId) {
	layer.confirm("确定要删除吗？", function() {
		$.ajax({
			url : basePath + "/mdms/rewardPunishManage/deleteRewardPunishManage.spring",
			type : "post",
			data : {
				rewardPunishManageId : rewardPunishManageId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				//getNewTechniqueData.getRewardPunishList();
				var $tbody = $("#rewardPunishManageDiv").empty();
				otherFormDetail.showRewardPunishList("rewardPunishManageDiv");
			});
			return data;
		});
	});
}

//医疗安全行为记录编辑
function behaviorRecordEdit(behaviorRecordId, showOrEdit) {
	var html = "behaviorRecordEdit.html";
	if (showOrEdit == 1) {
		html = "behaviorRecordView.html";
	}
	var id = parseInt(behaviorRecordId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "certifiToEdit",
		area : [ '800px', '60%' ],
		title : false,
		scrollbar : false,
		content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&behaviorRecordId=" + behaviorRecordId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo")
	
	});
}

//医疗安全行为记录删除
function behaviorRecordDel(behaviorRecordId) {
	layer.confirm("确定要删除吗？", function() {
		$.ajax({
			url : basePath + "/mdms/behaviorRecord/deleteBehaviorRecord.spring",
			type : "post",
			data : {
				behaviorRecordId : behaviorRecordId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				//getNewTechniqueData.getBehaviorRecordList();
				var $tbody = $("#behaviorRecordDiv").empty();
				otherFormDetail.showBehaviorRecordList("behaviorRecordDiv");
			});
			return data;
		});
	});
}

//重大差错及事故处理编辑
function majorsAccidentEdit(majorsAccidentId, showOrEdit) {
	var html = "majorsAccidentEdit.html";
	if (showOrEdit == 1) {
		html = "majorsAccidentView.html";
	}
	var id = parseInt(majorsAccidentId);
	layer.open({
		type : 2,
		skin : 'layui-layer-aems',
		id : "majorsAccidentEdit",
		area : [ '900px', '70%' ],
		title : false,
		scrollbar : false,
		content : basePath + "mdms/functionModule/newTechniqueManager/" + html + "?showOrEdit=" + showOrEdit + "&majorsAccidentId=" + majorsAccidentId + "&funCode=" + param.get("funCode") + "&compNo=" + param.get("compNo") + "&appCode=" + param.get("appCode")
	
	});
}

//重大差错及事故处理删除
function majorsAccidentDel(majorsAccidentId) {
	layer.confirm("确定要删除吗？", function() {
		$.ajax({
			url : basePath + "/mdms/majorsAccident/deleteMajorsAccident.spring",
			type : "post",
			data : {
				majorsAccidentId : majorsAccidentId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				//getNewTechniqueData.getMajorsAccidentList();
				var $tbody = $("#majorsAccidentDiv").empty();
				otherFormDetail.showMajorsAccidentList("majorsAccidentDiv");
				
			});
			return data;
		});
	});
}

//getNewTechniqueData.init();