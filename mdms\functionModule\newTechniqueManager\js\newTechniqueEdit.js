var newTechnique = {
	// 初始化
	initIframe : function() {
		
		var compNo = param.get("compNo");
		var appCode = param.get("appCode");
		var businessCode = param.get("customFormBusinessCode");
		var customFormCode = param.get("customFormCode");
		var customFormFilledCode = param.get("customFormFilledCode");
		var url ="";
		if(customFormCode==""){//新增页面 hwx 2020-12-18
			url = basePath+"frame/customForm/customFormTemplate.html?customFormBusinessCode="+businessCode+"&compNo="+compNo+"&appCode="+appCode+"&type=1";
		}else{//编辑页面 hwx2020-12-18
			url = basePath+"frame/customForm/customFormTemplate.html?customFormCode="+customFormCode+ "&customFormFilledCode=" + customFormFilledCode + "&appCode=" + appCode + "&type=1";
		}
		// 设置iframe高度
		$("#report").css("height", $(window).height() - 30);
		$("#report").attr("src", url);
		
	}
}

newTechnique.initIframe();