var onlyShow = 0;//1时仅查看不可编辑，0时可以编辑
var newTechnique = {
	
	// 状态
	statusMapping : {},
	//hwx 2024年5月23日上午10:02:38
	customFormFilledCodes : "",
	// 初始化
	init : function() {
		newTechnique.initButton();
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"), $("select[name='compNo']")[0]).then(function() {
			assemblys.initSessionStoragePram();
			// 过滤条件
			return newTechnique.getParamInfo();
		}).then(function() {
			assemblys.initSessionStoragePram();
			return newTechnique.getFormList();
		}).then(function(data) {
			newTechnique.initLayuiForm(data);
			$("div.head0").removeClass("layui-hide");
		});
		
	},
	initButton : function() {
		
		// 设置选中的tab
		var status = param.get("status");
		$("div[lay-filter=docDemoTabBrief]").find("li[status='" + status + "']").addClass("layui-this");
		$("div[lay-filter=docDemoTabBrief]").find("li[status='" + status + "']").siblings().removeClass("layui-this");
		$("#customFieldSetting").click(function(e) {
			layer.open({
				type : 2,
				skin : 'layui-layer-aems',
				id : "layer-preview",
				title : false,
				scrollbar : false,
				maxmin : false,
				area : [ '520px', '450px' ],
				content : basePath + "mdms/utils/custormFieldSetting/customFieldSetting.html?funCode=" + param.get("funCode") + "&businessCode=" + param.get("businessCode") + "&packageName=functionModule/newTechniqueManager&parentObject=newTechnique"
			});
		});
		
		$("#filter,#closeFilter").on("click", function(e) {
			newTechnique.changeFilter();
		});
		
		$("#export").click(function(e) {
			newTechnique.exportnewTechnique();
		});
		
	},
	changeFilter : function(hasOpen) {
		var $filterSearch = $("#filterSearch");
		var $filter = $("#filter");
		var $img = $filter.children("img");
		var img0 = basePath + "plugins/static/image/filter.png";
		var img1 = basePath + "plugins/static/image/filter1.png";
		var img2 = basePath + "plugins/static/image/filter2.png";
		
		if ($filterSearch.css('display') == 'none' || hasOpen) {
			$filterSearch.css('display', 'block');
		} else {
			$filterSearch.css('display', 'none');
		}
		
		// 显示/隐藏
		if ($img.attr("src") != img2 || hasOpen) {
			if ($filterSearch.css('display') == 'none') {
				$img.attr("src", img0);
			} else {
				$img.attr("src", img1);
			}
		}
		
	},
	search : function() {
		var $filter = $("#filter");
		var $img = $filter.children("img");
		var img2 = basePath + "plugins/static/image/filter2.png";
		// 是否有过滤条件
		var customOptionSetCodes = param.get("customOptionSetCode");
		var customTextValues = param.get("customTextValue");
		var hasParam = false;
		if (customOptionSetCodes && customTextValues) {
			customOptionSetCodes.push.apply(customOptionSetCodes, customTextValues);
			for ( var i in customOptionSetCodes) {
				if (customOptionSetCodes[i]) {
					hasParam = true;
					break;
				}
			}
		}
		
		if (hasParam) {
			$img.attr("src", img2);
		} else {
			newTechnique.changeFilter(true);
		}
		
		newTechnique.getFormList();
	},
	initLayuiForm : function(data) {
		layui.form.render();
		mdmsCommon.loadCompNo(function(data) {
			var url = "newTechniqueList.html?funCode=" + param.get("funCode") + "&compNo=" + data.value;
			location.href = url;
		});
		
		layui.form.on("checkbox(selectCustomFormFilledCodeAll)", function(data) {
			if (data.elem.checked) {
				$("input[name=customFormFilledCodes]").not(":checked").next().click();
			} else {
				$("input[name=customFormFilledCodes]:checked").next().click();
			}
		});
		
		layui.element.render();
		layui.element.on("tab(docDemoTabBrief)", function(data) {
			page.set("curPageNum", "1");
			page.set("pageSize", "20");
			param.set("status", data.elem.find("li:eq(" + data.index + ")").attr("status"));
			newTechnique.getFormList();
		});
	},
	initTab : function(data) {
		
		newTechnique.statusMapping = data.statusMapping;
		
		$("div[lay-filter=docDemoTabBrief]").find("li:visible").each(function(i, e) {
			var $this = $(this);
			var status = $this.attr("status");
			$this.text(newTechnique.statusMapping["status" + status]);
		});
		
	},
	getParamInfo : function() {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getParamInfo.spring",
			data : param.__form()
		}).then(function(data) {
			var elemAry = [ newTechnique.getParamDiv({
				customFieldName : "关键字",
				customFieldSet : "keyword"
			}), newTechnique.getParamDiv({
				customFieldName : "申请时间",
				customFieldSet : "createDate"
			}), newTechnique.getParamDiv({
				customFieldName : "科室",
				customFieldSet : "deptID"
			}) ];
			for ( var i in data.paramInfo) {
				if (!data.paramInfo[i].customFieldSet || data.paramInfo[i].customFieldSet == "label") {
					continue;
				}
				var div = newTechnique.getParamDiv(data.paramInfo[i]);
				
				elemAry.push(div);
			}
			
			var $filterSearchDiv = $("#filterSearchDiv").empty();
			
			assemblys.createElement(elemAry, $filterSearchDiv[0]);
			
			newTechnique.initDateSearch($filterSearchDiv);
			newTechnique.initTab(data);
			
		});
		
	},
	getParamDiv : function(paramInfo) {
		return {
			"tagName" : "div",
			"className" : "layui-form-item mgb20 inblock",
			"children" : [ {
				"tagName" : "label",
				"className" : "layui-form-label lh13 fw700",
				"title" : paramInfo.customFieldName,
				"innerText" : paramInfo.customFieldName
			}, {
				"tagName" : "div",
				"className" : "layui-input-block mgl86",
				"children" : newTechnique[paramInfo.customFieldSet](paramInfo)
			} ]
		};
	},
	initDateSearch : function($filterSearchDiv) {
		$filterSearchDiv.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	"interface" : function(customField) {
		return newTechnique.getTextElement(customField);
	},
	text : function(customField) {
		return newTechnique.getTextElement(customField);
	},
	textarea : function(customField) {
		return newTechnique.getTextElement(customField);
	},
	getTextElement : function(customField) {
		return [ {
			"attr" : {
				"placeholder" : customField.customFieldName
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "customTextValue"
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldCode",
			"value" : customField.customFieldCode
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldSet",
			"value" : customField.customFieldSet
		} ];
	},
	radio : function(customField) {
		return newTechnique.getSelectElement(customField, true);
	},
	checkbox : function(customField) {
		return newTechnique.getSelectElement(customField, true);
	},
	select : function(customField) {
		return newTechnique.getSelectElement(customField, true);
	},
	getSelectElement : function(customField, isCustomOptionSet) {
		return [ {
			"attr" : {
				"lay-filter" : customField.customFieldCode,
				"lay-search" : ""
			},
			"tagName" : "select",
			"name" : isCustomOptionSet ? "customOptionSetCode" : "customTextValue",
			"children" : newTechnique.getSelectOption(customField.optionList)
		}, isCustomOptionSet ? null : {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldCode",
			"value" : customField.customFieldCode
		}, isCustomOptionSet ? null : {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldSet",
			"value" : customField.customFieldSet
		} ];
	},
	getSelectOption : function(optionList) {
		var optionAry = [ {
			"tagName" : "option",
			"innerText" : "全部",
			"value" : ""
		} ];
		for ( var i in optionList) {
			optionAry.push({
				"tagName" : "option",
				"innerText" : optionList[i].customOptionSetContent,
				"value" : optionList[i].customOptionSetCode
			});
		}
		return optionAry;
	},
	datetime : function(customField) {
		return [ {
			"attr" : {
				"laydate" : "",
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "customTextValue"
		}, {
			"tagName" : "i",
			"className" : "layui-icon layui-icon-date i_time2",
			"style" : {
				"right" : "4px"
			}
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldCode",
			"value" : customField.customFieldCode
		}, {
			"tagName" : "input",
			"type" : "hidden",
			"name" : "customFieldSet",
			"value" : customField.customFieldSet
		} ];
	},
	user : function(customField) {
		return newTechnique.getSelectElement(customField);
	},
	department : function(customField) {
		return newTechnique.getSelectElement(customField);
	},
	company : function(customField) {
		return newTechnique.getSelectElement(customField);
	},
	keyword : function(customField) {
		return [ {
			"attr" : {
				"placeholder" : "医师档案编号/医师名称"
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "keyword"
		} ];
	},
	createDate : function(customField) {
		return [ {
			"attr" : {
				"laydate" : "",
				"readonly" : "readonly"
			},
			"tagName" : "input",
			"className" : "layui-input",
			"type" : "text",
			"name" : "createDate"
		}, {
			"tagName" : "i",
			"className" : "layui-icon layui-icon-date i_time2",
			"style" : {
				"right" : "4px"
			}
		} ];
	},
	deptID : function(customField) {
		var optionAry = new Array();
		$.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			async : false,
			data : {
				compNo : param.get("compNo"),
				funCode : param.get("funCode")
			},
			dataType : "json",
			// 架构一的接口，需要增加这字段
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var deptList = data.deptList;
					optionAry.push({
						"tagName" : "option",
						"innerText" : "全部",
						"value" : ""
					});
					for ( var index in deptList) {
						var dept = deptList[index];
						optionAry.push({
							"tagName" : "option",
							"innerText" : dept["DeptName"],
							"value" : dept["DeptID"]
						});
					}
				} else {
					assemblys.alert("获取部门接口失败");
				}
			}
		})

		return [ {
			"attr" : {
				"lay-filter" : customField.customFieldSet,
				"lay-search" : ""
			},
			"tagName" : "select",
			"name" : customField.customFieldSet,
			"children" : optionAry
		} ];
		
	},
	getFormList : function() {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getNewTechniqueList.spring",
			data : param.__form() + "&" + page.__form() + "&" + filterParam.__form(),
			type : "post"
		}).then(function(data) {
			newTechnique.tableRender(data.trainingList, data.customFieldSetting);
			param.set("appCode", data.appCode);
			param.set("businessCode", data.businessCode);
			
			return data;
		});
	},
	
	tableRender : function(pager, customFieldSetting) {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		} ];
		
		cols.push({
			title : '<input type="checkbox" lay-skin="primary" lay-filter="selectCustomFormFilledCodeAll">',
			width : 65,
			align : "center",
			templet : function(d) {
				if (d.pauseStatus == 0) {
					return '<input type="checkbox" name="customFormFilledCodes"  lay-skin="primary" value="' + layui.util.escape(d.customFormFilledCode) + '">';
				} else {
					return '';
				}
				
			}
		});
		cols.push({
			title : '操作',
			width : 80,
			align : "center",
			templet : function(d) {
				var item = "";
				if (d.status == 0) {
					item += '<i class="layui-icon layui-icon-edit" lay-event="edit" style="cursor: pointer;" title="编辑"></i>';
					item += '<i class="layui-icon layui-icon-search i_check" lay-event="detail" style="cursor: pointer;" title="详情"></i>';
					item += '<i class="layui-icon layui-icon-delete" lay-event="delete" style="cursor: pointer;" title="删除"></i>'
					return item;
				} else {
					return "";
				}
			}
		});
		cols.push({
			title : '医师档案表单编号',
			minWidth : 165,
			align : "center",
			templet : function(d) {
				if (d.status && d.status != 0) {
					return '<a class="layui-a-hasClick" lay-event="onlyShow">' + layui.util.escape(d.customFormFilledCode) + '</a>';
				} else {
					return layui.util.escape(d.customFormFilledCode);
				}
			}
		});
		
		/*cols.push({
			title : '医师工号',
			width : 120,
			align : "center",
			field : "createUserCode"
		});
		cols.push({
			title : '医师名称',
			width : 120,
			align : "center",
			field : "createUserName"
		});*/
		cols.push({
			title : '申请时间',
			align : "center",
			width : 160,
			templet : function(d) {
				return assemblys.dateToStr(d.createDate);
			}
		});
		for ( var i in customFieldSetting) {
			cols.push({
				"title" : customFieldSetting[i].customFieldName,
				"width" : 200,
				"align" : "left",
				"fieldKey" : customFieldSetting[i].customFieldCode,
				"templet" : function(d) {
					return "<pre>" + layui.util.escape(d[this.fieldKey]) + "</pre>";
				}
			});
		}
		cols.push({
			title : '状态',
			width : 130,
			align : "center",
			templet : function(d) {
				return newTechnique.statusMapping["status" + d.status];
			}
		});
		
		if (window.$("#list").next().hasClass("layui-table-view") || !window["page"]) {
			window["page"].set(null, {
				curPageNum : 1,
				pageSize : 20
			});
		}
		
		layui.table.render({
			elem : '#list',
			data : pager.items,
			height : window.$("#list").parent().height() - (window.$("#list").next().hasClass("layui-table-view") ? 0 : $("#tablePage").height()),
			limit : 100,
			cols : [ cols ]
		});
		
		layui.laypage.render({
			"elem" : "tablePage",
			"count" : pager.totalCount,
			"curr" : pager.curPageNum || 1,
			"limit" : pager.pageSize || 20,
			"limits" : [ 10, 20, 50, 100 ],
			"layout" : [ 'prev', 'page', 'next', 'limit', 'skip', 'count' ],
			"jump" : function(obj, first) {
				page.set("curPageNum", obj.curr)
				page.set("pageSize", obj.limit);
				// 首次不执行
				if (!first) {
					newTechnique.getFormList();
				}
			}
		});
		
		layui.table.on("tool(list)", function(obj) {
			if (obj.event == "edit") {
				newTechnique.editForm(obj.data);
			}
			if (obj.event == "detail") {
				onlyShow = 0;
				newTechnique.toDetail(obj.data);
			}
			if (obj.event == "onlyShow") {
				onlyShow = 1;
				newTechnique.toDetail(obj.data);
			}
			if (obj.event == "delete") {
				newTechnique.deleteEvent(obj.data);
			}
			
		});
		$("#filterNum").text(pager.totalCount);
	},
	toDetail : function(d) {
		location.url({
			url : "newTechniqueDetail.html",
			param : {
				funCode : param.get("funCode"),
				appCode : param.get("appCode"),
				customFormCode : d.customFormCode,
				customFormFilledCode : d.customFormFilledCode,
				compNo : param.get("compNo"),
				status : d.status,
				isApprove : onlyShow,
				onlyShow : onlyShow,
				formUserCode : d.userCode
			}
		});
	},
	editForm : function(d) {
		var customFormCode = d.customFormCode;
		var customFormFilledCode = d.customFormFilledCode;
		var appCode = param.get("appCode");
		// 编辑页面路径
		var url = "newTechniqueEdit.html?customFormCode=" + customFormCode + "&customFormFilledCode=" + customFormFilledCode + "&appCode=" + appCode;
		// 弹出窗口
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '医师档案编辑',
			maxmin : true,
			area : [ '100%', '100%' ], // 设置弹窗打开大小
			content : url
		});
	},
	deleteEvent : function(d) {
		layer.confirm('确认删除该记录吗？', {
			icon : 2,
			title : '警告'
		}, function(index) {
			$.ajax({
				url : basePath + "/mdms/base/deleteEvent.spring",
				async : false,
				data : {
					customFormFilledCode : d.customFormFilledCode,
				},
				dataType : "json",
				// 架构一的接口，需要增加这字段
				skipDataCheck : true,
				success : function(data) {
					if (data.result == "success") {
						assemblys.msg("删除成功", function() {
							newTechnique.getFormList();
						});
					} else {
						assemblys.msg("删除失败", function() {
						});
					}
				}
			})

			layer.close(index);
		});
	},
	exportnewTechnique : function() {
		var filterParamJson = filterParam.__json();
		var inputAry = [];
		for ( var key in filterParamJson) {
			inputAry.push({
				"attr" : {
					"exportParam" : "1"
				},
				"tagName" : "input",
				"type" : "hidden",
				"name" : key,
				"value" : filterParamJson[key]
			});
		}
		assemblys.createElement(inputAry, param.__);
		
		param.__.action = basePath + "mdms/functionModule/newTechniqueManager/exportNewTechniqueList.spring";
		param.__.submit();
		
		param.__$.find("input[exportParam='1']").remove();
	},
	// 点击新增跳转新增页面
	toNewTechniqueAdd : function() {
		var compNo = param.get("compNo");
		var appCode = param.get("appCode");
		var businessCode = param.get("businessCode");
		
		// 跳转自定义表单路径
		var url = "newTechniqueEdit.html?customFormBusinessCode=" + businessCode + "&compNo=" + compNo + "&appCode=" + appCode + "&type=1";
		// 弹窗方法
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : '新增医师档案',
			maxmin : true,
			area : [ '100%', '100%' ], // 设置弹窗打开大小
			content : url
		});
	},
	// 打开离职选择日期界面
	selectDate : function(type) {
		
		var ids = "";
		$('input[type="checkbox"]:checked').each(function(index, value) {
			if ($(value).val() != 'on') {
				ids += $(this).val() + ',';
			}
		});
		if (ids == "") {
			assemblys.msg("请选择数据后执行操作！");
			return false;
		} else {
			ids = ids.substring(0, ids.length - 1);
		}
		newTechnique.customFormFilledCodes = ids;
		var url = basePath + "mdms/functionModule/newTechniqueManager/selectleaveOutDate.html?type=" + type;
		//hwx 2023年12月8日上午11:07:57 专科弹出框大小
		var widths = '550px';
		var heights = '400px';
		if (type != '1') {
			widths = '650px';
			heights = '500px';
		}
		// 弹窗方法
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			title : type == '1' ? '离职设置' : '转科设置',
			maxmin : true,
			area : [ widths, heights ], // 设置弹窗打开大小
			content : url
		});
	},
	
	toUserLevaeOut : function(val, type) {
		
		var ids = "";
		$('input[type="checkbox"]:checked').each(function(index, value) {
			if ($(value).val() != 'on') {
				ids += $(this).val() + ',';
			}
		});
		if (ids == "") {
			layer.alert("请选择数据后执行！");
			return false;
		} else {
			ids = ids.substring(0, ids.length - 1);
		}
		//hwx 2024年3月7日上午9:29:55 离职/转科成功
		var msgName = "离职";
		if (type != '1') {
			msgName = "转科";
		}
		$.ajax({
			url : basePath + "/mdms/functionModule/newTechniqueManager/saveUserLevaeOut.spring",
			async : false,
			data : {
				"customFormFilledCodes" : ids,
				"compNo" : param.get("compNo"),
				"val" : val,
				"type" : type
			},
			dataType : "json",
			// 架构一的接口，需要增加这字段
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					assemblys.msg(msgName + "成功", function() {
						mdmsCustomList.initTable();
					});
				} else {
					assemblys.msg(msgName + "失败", function() {
					});
				}
			}
		})
	},
	
	// 新增轮转登记
	addDeptExchange : function() {
		
		var ids = "";
		$('input[type="checkbox"]:checked').each(function(index, value) {
			if ($(value).val() != 'on') {
				ids += $(this).val() + ',';
			}
		});
		if (ids == "") {
			layer.alert("请选择数据后执行！");
			return false;
		} else {
			ids = ids.substring(0, ids.length - 1);
		}
		
		var customFormFilledCodes = ids.split(",");
		
		if (customFormFilledCodes.length == 1 && customFormFilledCodes[0] != "") {
			$.ajax({
				url : basePath + "mdms/deptExchange/getCerInfo.spring",
				type : "post",
				data : {
					"compNo" : param.get("compNo"),
					"customFormFilledCode" : customFormFilledCodes[0]
				},
				dataType : "json",
				success : function(data) {
				}
			}).then(function(data) {
				layer.open({
					type : 2,
					skin : 'layui-layer-aems',
					id : "deptExchangeEdit",
					area : [ '600px', '70%' ],
					title : false,
					scrollbar : false,
					content : basePath + "mdms/functionModule/newTechniqueManager/deptExchangeEdit.html?customFormFilledCode=" + customFormFilledCodes[0] + "&funCode=" + param.get("funCode") + "&appCode=" + param.get("appCode") + "&userCode=" + data.userCode + "&userName=" + data.userName
				});
			});
		} else {
			assemblys.msg("请选择需要轮转的一条记录");
		}
	},
	//hwx 2022-4-28手动调用接口同步医师档案
	interfaceDoctorFile : function() {
		$.ajax({
			url : basePath + "/mdms/functionModule/newTechniqueManager/interfaceDoctorFile.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo")
			},
			dataType : "json",
			success : function(data) {
				var rtn = data.rtn;
				if (rtn == "success") {
					assemblys.msg(data.msg);
				}
			}
		});
	}

}
//newTechnique.init();