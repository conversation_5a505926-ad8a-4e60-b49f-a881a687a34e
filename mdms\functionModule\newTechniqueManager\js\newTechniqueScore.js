var rate
var rateLength = 1;
var newTechniqueScore = {
	cache : {
		deptNames : {}
	},
	dictName : [],
	// 初始化
	init : function() {
		//加载评分控件
		layui.use([ 'rate' ], function() {
			rate = layui.rate;
		});
		// 加载图标
		assemblys.getMenuIcon(param.get("funCode"));
		//加载打分字典
		newTechniqueScore.getScoreType();
		
	},
	getScoreType : function() {
		$.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			dataType : "json",
			data : {
				"dictTypeCode" : parent.param.get("dictTypeCode"),
				"appCode" : param.get("appCode")
			},
			skipDataCheck : true,
			success : function(data) {
				if (data.result == "success") {
					var dictList = data.dictList;
					if (dictList != undefined) {
						// 渲染
						var newData = [];
						var newData1 = [];
						// 生成TR
						$.each(dictList, function(index, temp) {
							temp["index"] = index + 1;
							var dictInfo = {};
							dictInfo.dictIndex = index + 1;
							dictInfo.dictName = temp.dictName;
							newTechniqueScore.dictName.push(dictInfo);
							newData.push(newTechniqueScoreRender.createPfbzView(temp));
							newData1.push(newTechniqueScoreRender.createExpertScore(temp));
							rateLength++;
						});
						var $tbody = $("#showpfbz").find("tbody");
						$tbody.empty();
						var $dfFrom = $("#dfFrom");
						// 渲染
						assemblys.createElement(newData, $tbody[0]);
						assemblys.createElement(newData1, $dfFrom[0]);
						for (var i = 1; i < rateLength; i++) {
							rate.render({
								elem : '#expertScore' + i,
								theme : '#FE0000',
								value : 1,
								text : true,
								setText : function(value) {
									this.span.text(value + "分");
								}
							})
						}
					}
				}
			}
		});
	},
	checkScore : function() {
		layer.confirm('确定提交打分记录吗？', {
			icon : 3,
			title : '提示'
		}, function(index) {
			if (rateLength >= 1) {
				//1、保存业务数据
				for (var i = 1; i < rateLength; i++) {
					var $span = $("#expertScore" + i).find("span");
					var score = $span[0].innerText.replace("分", "");
					$.ajax({
						url : basePath + "mdms/functionModule/newTechniqueManager/saveScore.spring",
						data : {
							"customFormFilledCode" : param.get("customFormFilledCode"),
							"auditIndex" : param.get("auditIndex"),
							"seqNo" : param.get("seqNo"),
							"scoreTypeName" : newTechniqueScore.dictName[i - 1].dictName,
							"expertScore" : score,
							"scoreRemark" : $("#scoreRemark" + i).val()
						},
						skipDataCheck : true
					});
				}
				//2、保存审核记录
				$.ajax({
					url : basePath + "mdms/functionModule/newTechniqueManager/saveScoreAudit.spring",
					data : param.__form(),
					skipDataCheck : true,
					success : function(data) {
						assemblys.msg("保存成功！", function() {
							// 返回列表
							parent.history.back();
						});
					}
				});
			}
		});
	}
}
//渲染层
var newTechniqueScoreRender = {
	createPfbzView : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.dictName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.remark
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : "1~" + temp.dictContent
			} ]
		}
	},
	createExpertScore : function(temp) {
		return {
			"tagName" : "table",
			"className" : "layui-table table table1",
			"children" : [ {
				"tagName" : "thead",
				"children" : [ {
					"tagName" : "tr",
					"children" : [ {
						"tagName" : "th",
						"attr" : {
							"colspan" : "2"
						},
						"children" : [ {
							"tagName" : "span",
							"style" : {
								"color" : "red"
							},
							"innerHTML" : " *"
						}, {
							"tagName" : "span",
							"innerHTML" : temp.dictName
						} ]
					} ]
				} ]
			}, {
				"tagName" : "tbody",
				"children" : [ {
					"tagName" : "tr",
					"children" : [ {
						"tagName" : "td",
						"style" : {
							"width" : "30%"
						},
						"children" : [ {
							"tagName" : "div",
							"style" : {
								"text-align" : "center"
							},
							"children" : [ {
								"tagName" : "div",
								"attr" : {
									"id" : "expertScore" + temp.index
								}
							} ]
						} ]
					}, {
						"tagName" : "td",
						"children" : [ {
							"tagName" : "textarea",
							"id" : "scoreRemark" + temp.index,
							"name" : "scoreRemark" + temp.index,
							"attr" : {
								"required" : "",
								"lay-verify" : "required",
								"placeholder" : "",
								"cols" : "80",
								"rows" : "3",
							},
							"style" : {
								"width" : "100%"
							},
							"onkeyup" : function() {
								handleTextAreaLength("scoreRemark" + temp.index, "scoreRemark_txtNum" + temp.index, 500, "专家意见");
							}
						
						}, {
							"tagName" : "span",
							"innerText" : "(专家意见还可以输入"
						
						}, {
							"tagName" : "font",
							"id" : "scoreRemark_txtNum" + temp.index,
							"style" : {
								"color" : "#000"
							},
							"className" : "comEdit_TxtArea_numShow",
							"innerText" : "500"
						}, {
							"tagName" : "span",
							"innerText" : "字)"
						} ]
					} ]
				} ]
			} ]
		};
	}
}
newTechniqueScore.init();