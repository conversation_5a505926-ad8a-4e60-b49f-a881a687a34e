var operationRightiEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		if (param.get("operationRightId") == 0) {//新增时下拉为多选
			$("#deptOperationId").attr("xm-select", "deptOperationId");
			$("#deptOperationId").attr("xm-select-search", "");
			$("#customFormFilledCode").val(param.get("customFormFilledCode"));
			
		} else {
			$("#deptOperationId").attr("lay-search", "");
		}
		operationRightiEdit.initOperationAcceptType();
		operationRightiEdit.getFormEmpInfo();
		operationRightiEdit.initDeptOperationList().then(function(data) {
			
			if (param.get("isDetail") == 1) {//浏览时不可编辑
			
				$("#btnSave").addClass("layui-hide");
				pubMethod.formReadOnly();
				
			}
			
			return operationRightiEdit.getOperationRighti();
			
		}).then(function() {
			operationRightiEdit.initLayui();
			$("span[class='head1_text fw700']").text("手术授权");
			$("dl[xid='deptOperationId']").css("width", "500px");
		});
		
		$("#titleName").text("手术权限");
		
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			operationRightiEdit.saveOperationRighti();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	getOperationRighti : function() {
		return $.ajax({
			url : basePath + "mdms/operationRight/getOperationRighti.spring",
			data : {
				operationRightId : param.get("operationRightId")
			}
		}).then(function(data) {
			
			param.set(null, data.operationRight);
			return data;
		});
	},
	saveOperationRighti : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/operationRight/saveOperationRighti.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.$("#operationRightFrame").empty();
				parent.otherFormDetail.getOperationRightList("operationRightFrame");
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},
	//获取科室手术类
	initDeptOperationList : function(dictCode, name) {
		return $.ajax({
			url : basePath + "mdms/operationRight/getOrgDeptOperationList.spring",
			data : {
				"operationRightId" : $("input[name='operationRightId']").val(),
				"customFormFilledCode" : param.get("customFormFilledCode"),
			}
		}).then(function(data) {
			if (data.deptOperationList) {
				var htmlTemp = "";
				for (var i = 0; i < data.deptOperationList.length; i++) {
					var temp = data.deptOperationList[i];
					htmlTemp += "<option value='" + temp["deptOperationId"] + "' >" + temp["deptName"] + "--" + temp["operationCode"] + "--" + temp["operationName"] + "--" + temp["operationLevelName"] + "--" + temp["operationTypeName"] + "</option>";
				}
				$("#deptOperationId").append(htmlTemp);
				//如果是新增渲染为多选
				if (param.get("operationRightId") == 0) {
					var formSelects = layui.formSelects;
					formSelects.render('deptOperationId');
				}
				
			}
			
			return data;
		});
	},
	getFormEmpInfo : function() {
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			
			$("input[name='userCode']").val(data.userCode);
			$("input[name='userName']").val(data.userName);
			
		});
	},
	//获取授权类型
	initOperationAcceptType : function(dictCode, name) {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getBaseDictList.spring",
			data : {
				dictTypeCode : assemblys.top.mdms.mdmsConstant.SSSQLX,
				systemName : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			}
		}).then(function(data) {
			if (data.dictList) {
				
				var htmlTemp = "";
				for (var i = 0; i < data.dictList.length; i++) {
					var temp = data.dictList[i];
					htmlTemp += "<option value='" + temp["dictCode"] + "' >" + temp["dictName"] + "</option>";
				}
				$("#rightType").append(htmlTemp);
				
			}
			
			return data;
		});
	},
}