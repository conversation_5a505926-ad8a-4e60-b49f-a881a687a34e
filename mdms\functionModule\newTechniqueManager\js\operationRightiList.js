var operationRightiList = {
	init : function() {
		
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		operationRightiList.operationRightiListInit().then(function(data) {
			operationRightiList.getOperationRightiPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : operationRightiList.exportList
			} ];
			filterSearch.init(basePath, operationRightiList.getFilterParams(data), operationRightiList.getOperationRightiPager, customBtnDom);
			operationRightiList.initLayuiForm();
			//hwx 2022-09-23 增加对查看列表进行显示。
			if (param.get("showType")) {
				//隐藏搜索框
				$("#filter").addClass("layui-hide");
			}
			$("input[name='customFormFilledCode']").val(param.get("customFormFilledCode"));
		});
		
		if ($(window.parent.document).find("#onlyShow").val() == 1) {
			$("button").addClass("layui-hide");
			$("div[class='bodys layui-form']").addClass("bodys_noTop");
		}
		if (parent.param.get("hasDocEditRight") == 'false') {
			$("button:contains(新增)").addClass("layui-hide");
		}
		//hwx 2022-09-23 增加对查看列表进行显示。
		if (param.get("showType")) {
			$(".head0").addClass("layui-hide");
			$(".bodys").css("top", "26px");
		}
	},
	operationRightiListInit : function() {
		return $.ajax({
			url : basePath + "mdms/operationRight/operationRightiListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", operationRightiList.stateMap[state].color).text(operationRightiList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			operationRightiList.getOperationRightiPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "手术编码,手术名称,部门,手术等级,操作类型",
			title : "关键字"
		} ];
		return params;
	},
	getOperationRightiPager : function() {
		//hwx 2022-09-23 增加对查看列表进行显示。
		var cols = [];
		if (!param.get("showType")) {
			cols.push({
				title : '操作',
				width : 120,
				align : "center",
				templet : function(d) {
					var html = '';
					html += '<i class="layui-icon layui-icon-search i_check" title="查看" lay-event="toShowOperationRighti"></i>';
					if (parent.param.get("hasDocEditRight") == 'true' && $(window.parent.document).find("#onlyShow").val() == 0) {
						html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditOperationRighti"></i>';
						html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteOperationRighti"></i>';
					}
					
					return html;
				}
			})
		}
		cols.push({
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '手术编码',
			width : 140,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.operationCode);
			}
		}, {
			title : '手术名称',
			width : 140,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.operationName);
			}
		}, {
			title : '部门',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.deptName);
			}
		}, {
			title : '手术等级',
			width : 120,
			align : "center",
			templet : function(d) {
				if (d.operationLevelName) {
					return assemblys.htmlEncode(d.operationLevelName);
				} else {
					return "暂无等级";
				}
			}
		}, {
			title : '操作类型',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.operationTypeName);
			}
		}, {
			title : '授权类型',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.rightTypeName);
			}
		}, {
			title : '授权时间',
			width : 160,
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.createTime);
			}
		}, {
			title : '授权人',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.acceptUserCode);
			}
		}, {
			title : '是否有效',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.isValid);
			}
		});
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/operationRight/getOperationRightiPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditOperationRighti : operationRightiList.toEditOperationRighti,
				toShowOperationRighti : operationRightiList.toShowOperationRighti,
				deleteOperationRighti : operationRightiList.deleteOperationRighti
			}
		});
		
	},
	exportList : function() {
		var url = basePath + "mdms/operationRight/exportList.spring?";
		url = url + "customFormFilledCode=" + param.get("customFormFilledCode") + "&keyWork=" + $("#operationKey").val();
		location.href = url;
	},
	toEditOperationRighti : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditOperationRighti",
			area : [ '900px', '370px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/operationRightiEdit.html?isDetail=0&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&operationRightId=" + d.operationRightId
		});
	},
	toShowOperationRighti : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toShowOperationRighti",
			area : [ '850px', '300px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/operationRightiView.html?isDetail=1&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&operationRightId=" + d.operationRightId
		});
	},
	deleteOperationRighti : function(d) {
		layer.confirm("确定要删除吗？", function() {
			return $.ajax({
				url : basePath + "mdms/operationRight/deleteOperationRighti.spring",
				type : "post",
				data : {
					operationRightId : d.operationRightId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					$("#operationRightFrame").empty();
					otherFormDetail.getOperationRightList("operationRightFrame");
				});
				return data;
			});
		});
	}
}