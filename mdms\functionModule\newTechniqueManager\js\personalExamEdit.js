var personalExamEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		personalExamEdit.getPersonalExam();
		if (param.get("personalExamId") == 0) {
			$("#customFormFilledCode").val(param.get("customFormFilledCode"));
		}
		pubMethod.getFormEmpInfo();
		personalExamEdit.initLayui();
		$("span[class='head1_text fw700']").text("临床类知识及技能培训考核情况");
		if (param.get("onlyShow") == 1) {//浏览时不可编辑
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
		}
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			personalExamEdit.savePersonalExam();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "year"
			});
		});
	},
	getPersonalExam : function() {
		return $.ajax({
			url : basePath + "mdms/personalExam/getPersonalExam.spring",
			data : {
				personalExamId : param.get("personalExamId")
			}
		}).then(function(data) {
			param.set(null, data.personalExam);
			return data;
		});
	},
	savePersonalExam : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/personalExam/savePersonalExam.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.$("#personCheckFrame").empty();
				parent.otherFormDetail.getPersonCheckList("personCheckFrame");
				assemblys.closeWindow();
			});
			return data;
		});
	}
}