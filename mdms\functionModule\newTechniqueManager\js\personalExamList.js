var personalExamList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		personalExamList.personalExamListInit().then(function(data) {
			personalExamList.getPersonalExamPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : personalExamList.exportList
			} ];
			filterSearch.init(basePath, personalExamList.getFilterParams(data), personalExamList.getPersonalExamPager, customBtnDom);
			personalExamList.initLayuiForm();
			if ($(window.parent.document).find("#onlyShow").val() == 1) {
				$("button").addClass("layui-hide");
				$("div[class='bodys layui-form']").addClass("bodys_noTop");
			}
			if (parent.param.get("hasDocEditRight") == 'false') {
				$("button:contains(新增)").addClass("layui-hide");
			}
		});
	},
	personalExamListInit : function() {
		return $.ajax({
			url : basePath + "mdms/personalExam/personalExamListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", personalExamList.stateMap[state].color).text(personalExamList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			personalExamList.getPersonalExamPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "考核年度",
			title : "关键字"
		} ];
		return params;
	},
	getPersonalExamPager : function() {
		var cols = [ {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				
				html += '<i class="layui-icon layui-icon-search i_check" title="查看" lay-event="toShowPersonalExam"></i>';
				if (parent.param.get("hasDocEditRight") == 'true' && $(window.parent.document).find("#onlyShow").val() == 0) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditPersonalExam"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deletePersonalExam"></i>';
				}
				
				return html;
			}
		}, {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '考核年度',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.personExamYear);
			}
		}, {
			title : '医师定期考核',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.regularAssessment);
			}
		}, {
			title : '三基考核',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.baseAssessment);
			}
		}, {
			title : '住院医师培训',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.doctorTrain);
			}
		}, {
			title : '急救技能考核',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.emergencyTrain);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/personalExam/getPersonalExamPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditPersonalExam : personalExamList.toEditPersonalExam,
				toShowPersonalExam : personalExamList.toShowPersonalExam,
				deletePersonalExam : personalExamList.deletePersonalExam
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/personalExam/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditPersonalExam : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditPersonalExam",
			area : [ '850px', '350px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/personalExamEdit.html?onlyShow=" + $(window.parent.document).find("#onlyShow").val() + "&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&personalExamId=" + d.personalExamId
		});
	},
	toShowPersonalExam : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditPersonalExam",
			area : [ '850px', '350px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/personalExamView.html?onlyShow=1&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&personalExamId=" + d.personalExamId
		});
	},
	deletePersonalExam : function(d) {
		layer.confirm("确定要删除吗？", function() {
			return $.ajax({
				url : basePath + "mdms/personalExam/deletePersonalExam.spring",
				type : "post",
				data : {
					personalExamId : d.personalExamId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					$("#personCheckFrame").empty();
					otherFormDetail.getPersonCheckList("personCheckFrame");
				});
				return data;
			});
		});
	}
}