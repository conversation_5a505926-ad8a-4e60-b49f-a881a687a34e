var pubMethod = {
	
	getFormEmpInfo : function() {
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			
			$("input[name='userCode']").val(data.userCode);
			$("input[name='userName']").val(data.userName);
			
		});
	},
	//获取授权类型
	initOperationAcceptType : function() {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getBaseDictList.spring",
			data : {
				dictTypeCode : 'SSSQLX',
				systemName : "mdms"
			}
		}).then(function(data) {
			if (data.dictList) {
				
				var htmlTemp = "";
				for (var i = 0; i < data.dictList.length; i++) {
					var temp = data.dictList[i];
					htmlTemp += "<option value='" + temp["dictCode"] + "' >" + temp["dictName"] + "</option>";
				}
				$("#rightType").append(htmlTemp);
				
			}
			
			return data;
		});
	},
	hideAddBtn : function() {
		//if($(window.parent.document).find("#onlyShow").val()==1){
		$(":button").addClass("layui-hide");
		//}
	},
	formReadOnly : function() {
		//if($(window.parent.document).find("#onlyShow").val()==1){
		$("select").attr("disabled", "disabled");
		$("input").attr("disabled", "disabled");
		$("radio").attr("disabled", "disabled");
		$("textarea").attr("disabled", "disabled");
		//}
	},
	getFormEmpInfoForTable : function() {
		$.ajax({
			url : basePath + "mdms/deptExchange/getCerInfo.spring",
			type : "post",
			data : {
				"compNo" : param.get("compNo"),
				"customFormFilledCode" : param.get("customFormFilledCode")
			},
			dataType : "json",
			success : function(data) {
			}
		}).then(function(data) {
			$("#userCode").text(data.userCode);
			$("#userName").text(data.userName);
			if ($("#userCode").length == 0) {
				$("td:contains(工号)").next().text(data.userCode);
				$("td:contains(姓名)").next().text(data.userName);
			}
			
		});
	},
	getDictList : function(val, name, dictTypeCode) {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getBaseDictList.spring",
			data : {
				dictTypeCode : dictTypeCode,
				systemName : assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME
			}
		}).then(function(data) {
			if (data.dictList) {
				for (var i = 0; i < data.dictList.length; i++) {
					var temp = data.dictList[i];
					if (val == temp["dictCode"]) {
						$("#" + name).text(temp["dictName"]);
					}
				}
			}
			return data;
		});
	},
	
	initTable : function(selector, dataList) {
		if (!dataList) {
			dataList = [ {
				name : "分类1",
				list : [ {
					title : "独立一行",
					value : "值",
					one : 1
				}, {
					title : "测试数据1",
					value : "值1"
				}, {
					title : "测试数据2",
					value : "值2"
				}, {
					title : "最后一行自动合并",
					value : "值"
				} ]
			} ]
		}
		
		var lis = []
		if (dataList.length > 0) {
			for (var i = 0; i < dataList.length; i++) {
				var temp = dataList[i];
				var trs = [];
				var childrens = temp.list || [];
				if (childrens.length > 0) {
					for (var j = 0; j < childrens.length; j++) {
						var temp2 = childrens[j];
						var nextTemp2 = childrens[j + 1] || null;
						var tds = []
						//独立一行，即一列
						if (temp2.one && temp2.one == 1 || nextTemp2 == null || nextTemp2.one && nextTemp2.one == 1) {
							tds = [ {
								"tagName" : "td",
								"className" : "tright",
								"attr" : {
									"colspan" : "1"
								
								},
								"style" : {
									"text-align" : "right",
									"background" : "#f1f1f1",
									"width" : "15%"
								},
								"innerText" : temp2.title
							}, {
								"tagName" : "td",
								"className" : "tleft",
								"attr" : {
									"colspan" : "3"
								},
								"style" : {
									"width" : "35%"
								},
								"innerText" : temp2.value
							} ]
						} else {//四列
							tds = [ {
								"tagName" : "td",
								"className" : "tright",
								"attr" : {
									"colspan" : "1"
								},
								"style" : {
									"text-align" : "right",
									"background" : "#f1f1f1",
									"width" : "15%"
								},
								"innerText" : temp2.title
							}, {
								"tagName" : "td",
								"className" : "tleft",
								"attr" : {
									"colspan" : "1"
								},
								"style" : {
									"width" : "35%"
								},
								"innerText" : temp2.value
							}, {
								"tagName" : "td",
								"className" : "tleft",
								"attr" : {
									"colspan" : "1"
								},
								"style" : {
									"text-align" : "right",
									"background" : "#f1f1f1",
									"width" : "15%"
								},
								"innerText" : nextTemp2.title
							}, {
								"tagName" : "td",
								"className" : "tleft",
								"attr" : {
									"colspan" : "1"
								},
								"innerText" : nextTemp2.value
							} ];
							j++;
						}
						
						trs.push({
							"tagName" : "tr",
							"children" : tds
						});
						
					}
				}
				var li = [ {
					"tagName" : "table",
					"className" : "layui-table main_table detail_table",
					"children" : [ {
						"tagName" : "tbody",
						"children" : trs
					} ]
				} ]
				lis.push(li);
			}
		} else {
			lis.push({
				"tagName" : "table",
				"className" : "layui-table main_table detail_table",
				"children" : [ {
					"tagName" : "tbody",
					"children" : [ {
						"tagName" : "tr",
						"children" : [ {
							"tagName" : "td",
							"className" : "tright",
							"style" : {
								"text-align" : "center"
							},
							"attr" : {
								"colspan" : "4"
							},
							"innerText" : "暂无数据"
						} ]
					} ]
				} ]
			});
		}
		var table = {
			"tagName" : "div",
			"children" : lis,
			"id" : "idDiv"
		}
		assemblys.createElement(table, $(selector)[0]);
		layui.element.render("nav", selector + "UL");
	},
	getDictByCode : function(dictCode, dictTypeCode) {
		// 返回JSON形式
		var dictName = "";
		$.ajax({
			url : basePath + "mdms/mdmsCommon/getDictByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : dictTypeCode,
				"dictCode" : dictCode
			},
			dataType : "json",
			async : false,
			skipDataCheck : true,
			success : function(data) {
				dictName = data.data.dictName;
			},
			error : function() {
			}
		})
		return dictName;
	},
	getDeptName : function(deptId) {
		var deptName = "";
		$.ajax({
			url : basePath + "frame/common/getDeptList.spring",
			type : "get",
			data : {
				"compNo" : param.get("compNo"),
			},
			async : false,
			dataType : "json",
			//如果接口响应回来的数据有问题，请增加该参数
			skipDataCheck : true
		}).then(function(data) {
			$.each(data.deptList, function(i, val) {
				if (val.DeptID == deptId) {
					deptName = val.DeptName
				}
			})

		});
		return deptName;
	}
}