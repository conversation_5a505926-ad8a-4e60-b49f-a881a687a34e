var rewardPunishManageEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		rewardPunishManageEdit.getRewardPunishTypeList().then(function(data) {
			
			rewardPunishManageEdit.getRewardPunishManage();
			//初始化惩奖类型下拉框
			var html = "";
			$.each(data.dictList, function(i, val) {
				html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
			})
			$("#rewardPunishType").html(html);
			rewardPunishManageEdit.getRewardPunishLevelList().then(function(data) {
				var html = "";
				$.each(data.dictList, function(i, val) {
					html += "<option value='" + val.dictCode + "' >" + val.dictName + "</option>";
				})
				$("#sort").html(html);
				rewardPunishManageEdit.initLayui();
				$("span[class='head1_text fw700']").text("奖励管理");
				if (param.get("rewardPunishManageId") == 0) {
					pubMethod.getFormEmpInfo();
				}
				
				if (param.get("showOrEdit") == 1) {
					pubMethod.hideAddBtn();
					pubMethod.formReadOnly();
				}
			});
		});
		
	},
	initLayui : function() {
		layui.form.on("submit(save)", function() {
			rewardPunishManageEdit.saveRewardPunishManage();
			return false;
		});
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				format : "yyyy-MM-dd"
			});
		});
		layui.form.render();
	},
	getRewardPunishManage : function() {
		return $.ajax({
			url : basePath + "mdms/rewardPunishManage/getRewardPunishManage.spring",
			data : {
				rewardPunishManageId : param.get("rewardPunishManageId")
			}
		}).then(function(data) {
			param.set(null, data.rewardPunishManage);
			rewardPunishManageEdit.initTypeFile(data.fileList);
			return data;
		});
	},
	saveRewardPunishManage : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		
		var rewardPunishFileList = [];
		$("#ueditorFileDiv-0").children().find("input[name='attaName']").each(function() {
			var typeFiles = {};
			typeFiles.attaName = $(this).val();
			typeFiles.attaUrl = $(this).parent().find("input[name='attaUrl']").val();
			typeFiles.attaSize = $(this).parent().find("input[name='attaSize']").val();
			typeFiles.attaType = $(this).parent().find("input[name='attaType']").val();
			rewardPunishFileList.push(typeFiles);
		});
		var rewardPunishFileListJson = JSON.stringify(rewardPunishFileList);
		$("#rewardPunishFileListJson").val(rewardPunishFileListJson);
		
		return $.ajax({
			url : basePath + "mdms/rewardPunishManage/saveRewardPunishManage.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.newRewardPunish.rewardPunishList("#rewardPunishDiv");
				assemblys.closeWindow();
			});
			return data;
		});
	},
	
	getRewardPunishTypeList : function() {
		// 返回JSON形式
		
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.REWARDPUNISHTYPE,
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
		}).then(function(data) {
			return data;
		});
	},
	getRewardPunishLevelList : function() {
		// 返回JSON形式
		
		return $.ajax({
			url : basePath + "frame/dict/getDictListByCode.spring",
			type : "get",
			data : {
				"dictTypeCode" : assemblys.top.mdms.mdmsConstant.JLTYPE,
				"appCode" : param.get("appCode")
			},
			dataType : "json",
			skipDataCheck : true,
		}).then(function(data) {
			return data;
		});
	},
	
	attaCallback : function(result) {// 自定义上传图片后的回调
		var fileHtml = "";
		pubUploader.fileSpace = assemblys.top.mdms.mdmsConstant.MDMS_SYS_NAME;
		for (var i = 0; i < result.length; i++) {
			fileHtml += "<li style='width: 500px;'>";
			fileHtml += "	<em title=\"" + result[i].title + "\"><img title=\"附件\"  src=\"" + basePath + "frame/images/default/ico/attachment.gif\" >" + result[i].title + "&nbsp;&nbsp;" + result[i].size + "</em>";
			var suffix = result[i].type.toUpperCase();
			if (suffix == "BMP" || suffix == "JPG" || suffix == "JPEG" || suffix == "PNG" || suffix == "GIF") {
				fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" id=\"preview\" onclick=\"pubUploader.preview('" + result[i].title + "','" + result[i].url + "');\"  >预览图片</a></span>";
			}
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove\" onclick=\"pubUploader.downLoadAttaPreview('" + result[i].title + "','" + result[i].url + "');\">下载</a></span>";
			fileHtml += "	<span style='margin-left:5px;'><a  class=\"cattachqueue-remove attaDelete\" onclick=\"pubUploader.delAttaPreview(this);\">删除</a></span>";
			fileHtml += "	<input type=\"hidden\" name=\"attaName\"  value=\"" + result[i].title + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaUrl\"  value=\"" + result[i].url + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaSize\"  value=\"" + result[i].size + "\"/>";
			fileHtml += "	<input type=\"hidden\" name=\"attaType\"   value=\"" + result[i].type + "\"/>";
			fileHtml += "</li>";
		}
		$("#ueditorFileDiv-0").append(fileHtml);
		if (param.get("showOrEdit") == 1) {
			$("a[class='cattachqueue-remove attaDelete']").hide();
		}
	},
	
	initTypeFile : function(awardPunishFileList) {
		var filesData = awardPunishFileList;
		if (filesData) {
			var result = [];
			for (var k = 0; k < filesData.length; k++) {
				var typeFileTemp = filesData[k];
				var files = {};
				files.title = typeFileTemp.AttaName;
				files.url = typeFileTemp.AttaUrl;
				files.size = typeFileTemp.AttaSize;
				files.type = typeFileTemp.AttaType;
				result.push(files);
			}
			//param.set("fileIndex", i);
			rewardPunishManageEdit.attaCallback(result);
		}
		
	},

}