var rewardPunishManageList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		rewardPunishManageList.rewardPunishManageListInit().then(function(data) {
			rewardPunishManageList.getRewardPunishManagePager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : rewardPunishManageList.exportList
			} ];
			filterSearch.init(basePath, rewardPunishManageList.getFilterParams(data), rewardPunishManageList.getRewardPunishManagePager, customBtnDom);
			rewardPunishManageList.initLayuiForm();
		});
	},
	rewardPunishManageListInit : function() {
		return $.ajax({
			url : basePath + "mdms/rewardPunishManage/rewardPunishManageListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", rewardPunishManageList.stateMap[state].color).text(rewardPunishManageList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			rewardPunishManageList.getRewardPunishManagePager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "自定义表单数据编码,工号,姓名,奖励类型,奖励内容,奖励或处罚单位",
			title : "关键字"
		} ];
		return params;
	},
	getRewardPunishManagePager : function() {
		var cols = [ {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditRewardPunishManage"></i>';
				html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteRewardPunishManage"></i>';
				return html;
			}
		}, {
			title : '自定义表单数据编码',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.customFormFilledCode);
			}
		}, {
			title : '工号',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.userCode);
			}
		}, {
			title : '姓名',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.userName);
			}
		}, {
			title : '类型',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.sort);
			}
		}, {
			title : '类型',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.type);
			}
		}, {
			title : '内容',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.content);
			}
		}, {
			title : '时间',
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.promTime);
			}
		}, {
			title : '单位名称',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.promUnit);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/rewardPunishManage/getRewardPunishManagePager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditRewardPunishManage : rewardPunishManageList.toEditRewardPunishManage,
				deleteRewardPunishManage : rewardPunishManageList.deleteRewardPunishManage
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/rewardPunishManage/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditRewardPunishManage : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditRewardPunishManage",
			area : [ '850px', '90%' ],
			title : false,
			scrollbar : false,
			content : "rewardPunishManageEdit.html?funCode=" + param.get("funCode") + "&rewardPunishManageId=" + d.rewardPunishManageId
		});
	},
	deleteRewardPunishManage : function(d) {
		return $.ajax({
			url : basePath + "mdms/rewardPunishManage/deleteRewardPunishManage.spring",
			type : "post",
			data : {
				rewardPunishManageId : d.rewardPunishManageId
			}
		}).then(function(data) {
			assemblys.msg("删除成功", function() {
				rewardPunishManageList.getRewardPunishManagePager();
			});
			return data;
		});
	}
}