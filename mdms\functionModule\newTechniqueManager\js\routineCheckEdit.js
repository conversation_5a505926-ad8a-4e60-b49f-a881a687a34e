var routineCheckEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		routineCheckEdit.getRoutineCheck().then(function(data) {
			if (param.get("routineCheckId") == 0) {
				$("#customFormFilledCode").val(param.get("customFormFilledCode"));
			}
			pubMethod.getFormEmpInfo();
		}).then(function() {
			routineCheckEdit.initLayui();
			$("span[class='head1_text fw700']").text("医师定期考核情况");
		});
		if (param.get("onlyShow") == 1) {//浏览时不可编辑
			pubMethod.hideAddBtn();
			pubMethod.formReadOnly();
		}
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			routineCheckEdit.saveRoutineCheck();
			return false;
		});
		
		layui.form.on("radio(caculate)", function() {
			routineCheckEdit.caculate();
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "year"
			//range : "~",
			//max : 'today',
			
			});
		});
	},
	
	getRoutineCheck : function() {
		return $.ajax({
			url : basePath + "mdms/routineCheck/getRoutineCheck.spring",
			data : {
				routineCheckId : param.get("routineCheckId")
			}
		}).then(function(data) {
			param.set(null, data.routineCheck);
			return data;
		});
	},
	saveRoutineCheck : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/routineCheck/saveRoutineCheck.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.$("#routineCheckFrame").empty();
				parent.otherFormDetail.getRoutineCheckList("routineCheckFrame");
				assemblys.closeWindow();
			});
			return data;
		});
	},
	caculate : function() {
		var workScore = Number($("input[name='workScore']").val() == "" ? 0 : $("input[name='workScore']").val());
		var occupationScore = Number($("input[name='occupationScore']").val() == "" ? 0 : $("input[name='occupationScore']").val());
		var businessScore = Number($("input[name='businessScore']").val() == "" ? 0 : $("input[name='businessScore']").val());
		var hasGoodBehaviour = Number($("input[name='hasGoodBehaviour']:checked").val() == 1 ? 15 : 0);
		var hasBadBehaviour = Number($("input[name='hasBadBehaviour']:checked").val() == 1 ? 0 : 15);
		var summaryProcedure = Number($("input[name='summaryProcedure']:checked").val() == 1 ? 0 : 20);
		var total = workScore + occupationScore + businessScore + hasGoodBehaviour + hasBadBehaviour + summaryProcedure;
		$("input[name='score']").val(total);
	}

}