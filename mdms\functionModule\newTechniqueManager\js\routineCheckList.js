var routineCheckList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		routineCheckList.routineCheckListInit().then(function(data) {
			routineCheckList.getRoutineCheckPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : routineCheckList.exportList
			} ];
			filterSearch.init(basePath, routineCheckList.getFilterParams(data), routineCheckList.getRoutineCheckPager, customBtnDom);
			routineCheckList.initLayuiForm();
			if ($(window.parent.document).find("#onlyShow").val() == 1) {
				$("button").addClass("layui-hide");
				$("div[class='bodys layui-form']").addClass("bodys_noTop");
			}
			if (parent.param.get("hasDocEditRight") == 'false') {
				$("button:contains(新增)").addClass("layui-hide");
			}
		});
	},
	routineCheckListInit : function() {
		return $.ajax({
			url : basePath + "mdms/routineCheck/routineCheckListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", routineCheckList.stateMap[state].color).text(routineCheckList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			routineCheckList.getRoutineCheckPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "备注",
			title : "关键字"
		} ];
		return params;
	},
	getRoutineCheckPager : function() {
		var cols = [ {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				
				html += '<i class="layui-icon layui-icon-search i_check" title="查看" lay-event="toShowRoutineCheck"></i>';
				if (parent.param.get("hasDocEditRight") == 'true' && $(window.parent.document).find("#onlyShow").val() == 0) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditRoutineCheck"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteRoutineCheck"></i>';
				}
				
				return html;
			}
		}, {
			title : '序号',
			width : 60,
			align : "center",
			type : 'numbers'
		}, {
			title : '考核年度',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.checkYear);
			}
		}, {
			title : '有无良好行为记录',
			width : 150,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.hasGoodBehaviour);
			}
		}, {
			title : '有无不良行为记录',
			width : 150,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.hasBadBehaviour);
			}
		}, {
			title : '工作成绩评定',
			width : 140,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.workScore);
			}
		}, {
			title : '职业道德评定',
			width : 140,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.occupationScore);
			}
		}, {
			title : '是否简易程序',
			width : 140,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.summaryProcedure);
			}
		}, {
			title : '业务水平评定',
			width : 140,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.businessScore);
			}
		}, {
			title : '总成绩',
			width : 120,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.score);
			}
		}, {
			title : '备注',
			width : 200,
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.remark);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/routineCheck/getRoutineCheckPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditRoutineCheck : routineCheckList.toEditRoutineCheck,
				toShowRoutineCheck : routineCheckList.toShowRoutineCheck,
				deleteRoutineCheck : routineCheckList.deleteRoutineCheck
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/routineCheck/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditRoutineCheck : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditRoutineCheck",
			area : [ '950px', '600px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/routineCheckEdit.html?onlyShow=0&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&routineCheckId=" + d.routineCheckId
		});
	},
	toShowRoutineCheck : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditRoutineCheck",
			area : [ '750px', '450px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/routineCheckView.html?onlyShow=1&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&routineCheckId=" + d.routineCheckId
		});
	},
	deleteRoutineCheck : function(d) {
		layer.confirm("确定要删除吗？", function() {
			return $.ajax({
				url : basePath + "mdms/routineCheck/deleteRoutineCheck.spring",
				type : "post",
				data : {
					routineCheckId : d.routineCheckId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					$("#routineCheckFrame").empty();
					otherFormDetail.getRoutineCheckList("routineCheckFrame");
				});
				return data;
			});
		});
	}
}