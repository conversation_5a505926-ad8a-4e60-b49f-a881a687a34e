/**
 * 用于处理页面渲染
 */
var tool = {
	// 加载TR
	createAttaTr : function(temp) {
		var trTemplate = {
				"tagName" : "tr",
				"children" : [ {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"children" : [ {
						"tagName" : "i",
						"className" : "layui-icon layui-icon-download-circle i_icon",
						"style" : {
							"cursor" : "pointer"
						},
						"attr" : {
							"title" : "点击下载",
							"onclick" : "pubUploader.downLoadAttaPreview('" + temp.attachmentName + "','" + temp.attachmentURL + "');"
						}
					} ]
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : temp.index
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "left"
					},
					"innerHTML" : temp.attachmentName
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : temp.attachmentSize
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : (temp.createUserCode + " / " + temp.createUserName)
				}, {
					"tagName" : "td",
					"className" : "comTab_Td",
					"style" : {
						"text-align" : "center"
					},
					"innerHTML" : temp.createDate==undefined?"":assemblys.dateToStr(temp.createDate.time)
				} ]
			};
			return trTemplate;
	},
	createLogTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : ""
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.OptType
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.FunName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"innerHTML" : "审核意见："+(temp.AuditOpinion==undefined?"":temp.AuditOpinion)+ "<br>"+ " 备注："+(temp.Remark==undefined?"":temp.Remark)
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : (temp.deptName + " - " + temp.OptUserCode) + "<br>"+ (temp.OptDate==undefined?"":assemblys.dateToStr(temp.OptDate))
			} ]
		};
	},
	createCertificateTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				//"className" : "comTab_Td layui-icon layui-icon-edit",
				"style" : {
					"text-align" : "center",
					"cursor": "pointer"
				},
				/*"onclick":function() {
					certifiEdit(temp.CertificateId);
				},*/
				"innerHTML" : "<i class='layui-icon layui-icon-edit ' id='zjglEdit' title='编辑' onclick='certifiEdit("+temp.CertificateId+")'></i>" +
							  "<i class='layui-icon layui-icon-delete' id='zjglDel' title='删除' onclick='certifiDel("+temp.CertificateId+")'></i>"
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.DictName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.CertifiName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "left"
				},
				"innerHTML" : temp.Remark==undefined?"":temp.Remark
			} ]
		};
	},
	createDeptExchangeTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"style" : {
					"text-align" : "center",
					"cursor": "pointer"
				},
				 "children" : temp.State == 0? [ {
                     "attr":{
                             "lay-skin":"primary"
                     },
                     "tagName" : "input",
                     "type":"checkbox",
                     "name":"deptExchangeCheckedIds",
                     "value":temp.DeptExchangeId
             }]:""
			},{
				"tagName" : "td",
				"style" : {
					"text-align" : "center",
					"cursor": "pointer"
				},
				"innerHTML" : temp.State == 1 ? "<i class='layui-icon layui-icon-search ' title='查询' onclick='searchExchangeDetail("+temp.DeptExchangeId+")'></i>" 
					: "<i class='layui-icon layui-icon-edit ' title='编辑' onclick='deptExchangeEdit("+temp.DeptExchangeId+")'></i>" +
				"<i class='layui-icon layui-icon-delete' title='删除' onclick='deptExchangeDel("+temp.DeptExchangeId+")'></i>"
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.UserCode
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.UserName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.DeptName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.ExchangeDeptName
			} , {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.BeginTime==undefined?"":temp.BeginTime
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.EndTime==undefined?"":temp.EndTime
			},{
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.DictName
			},{
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.StateValue
			}  ]
		};
	},
	//技术授权
	createTechnologyTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"style" : {
					"text-align" : "center",
					"cursor": "pointer"
				},
				"innerHTML" : "<i class='layui-icon layui-icon-edit ' title='编辑' onclick='certifiEdit("+temp.CertificateId+")'></i>" +
				"<i class='layui-icon layui-icon-delete' title='删除' onclick='certifiDel("+temp.CertificateId+")'></i>"
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : ""
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : ""
			} ]
		};
	},

	//奖励管理
	createRewardPunishTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"style" : {
					"text-align" : "center",
					"cursor": "pointer"
				},
				"innerHTML" : "<i class='layui-icon layui-icon-edit ' id='jcEdit' title='编辑' onclick='rewardPunishManageEdit("+temp.RewardPunishManageId+")'></i>" +
				"<i class='layui-icon layui-icon-delete' id='jcDel' title='删除' onclick='rewardPunishManageDel("+temp.RewardPunishManageId+")'></i>"
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.PromTime==undefined?"":assemblys.dateToStr(temp.PromTime)
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.SortValue
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.DictName
			} , {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.Content
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.PromUnit
			}]
		};
	},
	
	
	//医疗安全行为记录
	createBehaviorRecordTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"style" : {
					"text-align" : "center",
					"cursor": "pointer"
				},
				"innerHTML" : "<i class='layui-icon layui-icon-edit ' id='ylEdit' title='编辑' onclick='behaviorRecordEdit("+temp.BehaviorRecordId+")'></i>" +
				"<i class='layui-icon layui-icon-delete' title='删除' id='ylDel' onclick='behaviorRecordDel("+temp.BehaviorRecordId+")'></i>"
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.OptDate==undefined?"":assemblys.dateToStr(temp.OptDate)
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.DictName
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.Summary
			} ]
		};
	},
	
	
	
	
	//重大差错及事故处理
	createMajorsAccidentTr : function(temp) {
		return {
			"tagName" : "tr",
			"children" : [ {
				"tagName" : "td",
				"style" : {
					"text-align" : "center",
					"cursor": "pointer"
				},
				"innerHTML" : "<i class='layui-icon layui-icon-edit ' id='zdccEdit' title='编辑' onclick='majorsAccidentEdit("+temp.MajorsAccidentId+")'></i>" +
				"<i class='layui-icon layui-icon-delete' title='删除' id='zdccDel' onclick='majorsAccidentDel("+temp.MajorsAccidentId+")'></i>"
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.index
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.AccidentTime==undefined?"":assemblys.dateToStr(temp.AccidentTime)
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.Reason
			}, {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.AccidentName
			} , {
				"tagName" : "td",
				"className" : "comTab_Td",
				"style" : {
					"text-align" : "center"
				},
				"innerHTML" : temp.Punishment
			}]
		};
	},
	
	
	
	
	
	// 流程下标
	currFollowIndex : -1,
	// 流程图
	createFlowLi : function(flow) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : (function() {
							if (flow.hasCurrent == 1) {
								tool.currFollowIndex = flow.followIndex;
								return basePath + "plugins/static/image/image3/jinxingzhong.png";
							} else if (tool.currFollowIndex == -1) {
								return basePath + "plugins/static/image/image3/wancheng.png";
							} else {
								return basePath + "plugins/static/image/image3/dengdai.png";
							}
						})(),
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" :  (function() {
						if (flow.hasCurrent == 1) {
							return "right_item  item_red";
						} else if (tool.currFollowIndex == -1) {
							return "right_item  item_green";
						} else {
							return "right_item  item_gray";
						}
					})(),
					"children" : [ {
						"tagName" : "h3",
						"innerHTML" : flow.ProcessName || "节点"
					}, {
						"tagName" : "span",
						"className" : "item_content",
						"innerHTML" :""
					}, {
						"tagName" : "div",
						"style" : {
							"max-height" : "250px",
							"overflow" : "auto",
							"text-align" : "left",
						},
						"children" : [ {
							"tagName" : "blockquote",
							"className" : "layui-elem-quote ",
							"title" : "审核人员名单",
							"style" : {
								"display" : flow.userList && flow.userList.length > 0 /*&& flow.hasCurrent == 1*/ ? "block" : "none"
							},
							"children" : (function() {
								var userList = flow.userList;
								var userHtml = [];
								if (userList && userList.length != 0) {
									$.each(userList, function(i, u) {
										userHtml.push({
											"tagName" : "span",
											"className" : "item_content",
											"title" : (u.deptName + " - " + u.userName),
											"innerText" : (u.deptName + " - " + u.userName),
										});
									});
								}
								return userHtml;
							})()
						} ]
					} ]
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : flow.followIndex
					},
					"style" : {
						"text-align" : "left"
					}
				},{
					"tagName" : "div",
					"children" :(function() {
						var followInfo = flow.flowInfo;
						var userHtml = [];
						if (followInfo && followInfo.length != 0) {
							$.each(followInfo, function(i, u) {
								userHtml.push({
									"tagName" : "ul",
									"children" :[{
										"tagName" : "li",
										"className" : "right_data",
										"innerHTML" : assemblys.dateToStr(u.OptDate)
									},
									{
										"tagName" : "li",
										"className" : "right_text",
										"innerHTML" : u.OptUserName+u.OptType
									}]
								});
							});
						}
						return userHtml;
					})()
					
				}  ]
			} ]
		};
	},
	createFlowOneLi : function(followName,title,followInfo) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : basePath + "plugins/static/image/image3/wancheng.png",
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" : "right_item  item_green",
					"children" : [ {
						"tagName" : "h3",
						"innerHTML" : followName
					}, {
						"tagName" : "span",
						"className" : "item_content",
						"innerHTML" : title
					} ]
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : "0"
					},
					"style" : {
						"text-align" : "left"
					}
				} ,{
					"tagName" : "div",
					"children" :(function() {
						var userHtml = [];
						if (followInfo && followInfo.length != 0) {
							$.each(followInfo, function(i, u) {
								userHtml.push({
									"tagName" : "ul",
									"children" :[{
										"tagName" : "li",
										"className" : "right_data",
										"innerHTML" : assemblys.dateToStr(u.OptDate)
									},
									{
										"tagName" : "li",
										"className" : "right_text",
										"innerHTML" : u.OptUserName+u.OptType
									}]
								});
							});
						}
						return userHtml;
					})()
					
				} ]
			}]
		};
	},
	createFlowLastLi : function(temp) {
		return {
			"tagName" : "li",
			"className" : "layui-timeline-item",
			"children" : [ {
				"tagName" : "i",
				"className" : "layui-icon layui-timeline-axis",
				"children" : [ {
					"tagName" : "img",
					"attr" : {
						"src" : basePath + "plugins/static/image/image3/" + (true ? "wancheng" : "dengdai") + ".png",
					}
				} ]
			}, {
				"tagName" : "div",
				"className" : "layui-timeline-content layui-text",
				"children" : [ {
					"tagName" : "div",
					"className" : "item_unfinished",
					"innerHTML" : "已结束"
				}, {
					"tagName" : "div",
					"attr" : {
						"flowIndex" : "-6"
					},
					"style" : {
						"text-align" : "left"
					}
				} ]
			} ]
		};
	}
}
