var writeRightEdit = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), false, $("i[menuIcon]"));
		
		if (param.get("writeRightId") == 0) {//新增时下拉为多选
			$("#rmosDictId").attr("xm-select", "rmosDictId");
			$("#rmosDictId").attr("xm-select-search", "");
			$("#customFormFilledCode").val(param.get("customFormFilledCode"));
			pubMethod.getFormEmpInfo();
		} else {
			$("#rmosDictId").attr("lay-search", "");
		}
		
		writeRightEdit.initOperationAcceptType();
		
		writeRightEdit.initRmosList().then(function(data) {
			
			return writeRightEdit.getWriteRight();
			
		}).then(function() {
			if (param.get("onlyShow") == 1) {//浏览时不可编辑
				$(":button").addClass("layui-hide");
				pubMethod.formReadOnly();
			}
			
			writeRightEdit.initLayui();
			$("span[class='head1_text fw700']").text("处方授权");
			$("dl[xid='rmosDictId']").css("width", "500px");
		});
		
	},
	initLayui : function() {
		layui.form.render();
		layui.form.on("submit(save)", function() {
			writeRightEdit.saveWriteRight();
			return false;
		});
		
		param.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
	},
	getWriteRight : function() {
		return $.ajax({
			url : basePath + "mdms/writeRight/getWriteRight.spring",
			data : {
				writeRightId : param.get("writeRightId")
			}
		}).then(function(data) {
			param.set(null, data.writeRight);
			return data;
		});
	},
	saveWriteRight : function() {
		if (window.isSubmit) {
			return;
		}
		window.isSubmit = true;
		return $.ajax({
			url : basePath + "mdms/writeRight/saveWriteRight.spring",
			type : "post",
			data : param.__form()
		}).then(function(data) {
			assemblys.msg("保存成功", function() {
				parent.$("#writeRightFrame").empty();
				parent.otherFormDetail.getWriteRightList("writeRightFrame");
				assemblys.closeWindow();
			});
			window.isSubmit = false;
			return data;
		});
	},
	//获取授权类型
	initOperationAcceptType : function(dictCode, name) {
		return $.ajax({
			url : basePath + "mdms/functionModule/newTechniqueManager/getBaseDictList.spring",
			data : {
				dictTypeCode : 'SSSQLX',
				systemName : "mdms"
			}
		}).then(function(data) {
			if (data.dictList) {
				
				var htmlTemp = "";
				for (var i = 0; i < data.dictList.length; i++) {
					var temp = data.dictList[i];
					htmlTemp += "<option value='" + temp["dictCode"] + "' >" + temp["dictName"] + "</option>";
				}
				$("#rightType").append(htmlTemp);
				
			}
			
			return data;
		});
	},
	//获取处方权
	initRmosList : function() {
		return $.ajax({
			url : basePath + "mdms/writeRight/getRmosdList.spring",
			data : {
				"writeRightId" : $("input[name='writeRightId']").val(),
				"customFormFilledCode" : param.get("customFormFilledCode"),
			}
		}).then(function(data) {
			if (data.rmosdList) {
				var htmlTemp = "";
				for (var i = 0; i < data.rmosdList.length; i++) {
					var temp = data.rmosdList[i];
					htmlTemp += "<option value='" + temp["rmosDictId"] + "' >" + temp["rmosName"] + "--" + temp["rmosHisCode"] + "--" + temp["parentName"] + "</option>";
				}
				$("#rmosDictId").append(htmlTemp);
				//如果是新增渲染为多选
				if (param.get("writeRightId") == 0) {
					var formSelects = layui.formSelects;
					formSelects.render('rmosDictId');
				}
				
			}
			
			return data;
		});
	}

}