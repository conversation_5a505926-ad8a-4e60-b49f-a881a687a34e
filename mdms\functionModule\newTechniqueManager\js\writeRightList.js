var writeRightList = {
	init : function() {
		assemblys.getMenuIcon(param.get("funCode"), true, $("i[menuIcon]"));
		writeRightList.writeRightListInit().then(function(data) {
			writeRightList.getWriteRightPager();
			var customBtnDom = [ {
				title : "导出",
				className : "layui-icon layui-icon-export skin-div-font",
				onclick : writeRightList.exportList
			} ];
			filterSearch.init(basePath, writeRightList.getFilterParams(data), writeRightList.getWriteRightPager, customBtnDom);
			writeRightList.initLayuiForm();
			$("input[name='customFormFilledCode']").val(param.get("customFormFilledCode"));
		});
		
		if ($(window.parent.document).find("#onlyShow").val() == 1) {
			$("#writeBtn").addClass("layui-hide");
			$("div[class='bodys layui-form']").addClass("bodys_noTop");
		}
		if (parent.param.get("hasDocEditRight") == 'false') {
			$("button:contains(新增)").addClass("layui-hide");
		}
		
	},
	writeRightListInit : function() {
		return $.ajax({
			url : basePath + "mdms/writeRight/writeRightListInit.spring"
		}).then(function(data) {
			return data;
		});
	},
	initLayuiForm : function() {
		filterParam.__$.find("input[laydate]").each(function(i, e) {
			layui.laydate.render({
				elem : e,
				trigger : "click",
				type : "date",
				range : "~",
				max : 'today',
				format : "yyyy-MM-dd"
			});
		});
		
		$("#tabView li:gt(0)").each(function(i, e) {
			var state = $(this).attr("state");
//			$(this).css("color", writeRightList.stateMap[state].color).text(writeRightList.stateMap[state].name);
		});
		
		$("#tabView li[state=" + param.get("state") + "]").click();
		
		layui.element.on("tab(tabView)", function(data) {
			param.set("state", $(this).attr("state"));
			writeRightList.getWriteRightPager();
		});
		
		layui.form.render();
	},
	getFilterParams : function(data) {
		var params = [ {
			type : "text",
			name : "keyword",
			placeholder : "处方权,授权类型",
			title : "关键字"
		} ];
		return params;
	},
	getWriteRightPager : function() {
		var cols = [ {
			title : '操作',
			width : 120,
			align : "center",
			templet : function(d) {
				var html = '';
				
				html += '<i class="layui-icon layui-icon-search i_check" title="查看" lay-event="toShowWriteRight"></i>';
				
				if (parent.param.get("hasDocEditRight") == 'true' && $(window.parent.document).find("#onlyShow").val() == 0) {
					html += '<i class="layui-icon layui-icon-edit i_delete" title="编辑" lay-event="toEditWriteRight"></i>';
					html += '<i class="layui-icon layui-icon-delete i_delete" title="删除" lay-event="deleteWriteRight"></i>';
				}
				
				return html;
			}
		}, {
			title : '序号',
			align : "center",
			type : 'numbers'
		}, {
			title : '员工部门名称',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.uDeptName);
			}
		}, {
			title : '员工工号',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.userCode);
			}
		}, {
			title : '处方权',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.RmosName);
			}
		}, {
			title : '授权时间',
			align : "center",
			templet : function(d) {
				return assemblys.dateToStr(d.authorizeDate);
			}
		}, {
			title : '授权类型',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.rightTypeName);
			}
		}, {
			title : '是否有效',
			align : "center",
			templet : function(d) {
				return assemblys.htmlEncode(d.isValid);
			}
		} ];
		assemblys.tableRender({
			elem : '#list',
			url : basePath + "mdms/writeRight/getWriteRightPager.spring?" + param.__form() + "&" + filterParam.__form(),
			cols : [ cols ],
			done : function(res, curr, count) {
				page.set("curPageNum", res.curPageNum);
				page.set("pageSize", res.pageSize);
				$("#filterNum").text(count);
			},
			events : {
				toEditWriteRight : writeRightList.toEditWriteRight,
				toShowWriteRight : writeRightList.toShowWriteRight,
				deleteWriteRight : writeRightList.deleteWriteRight
			}
		});
		
	},
	exportList : function() {
		location.href = basePath + "mdms/writeRight/exportList.spring?" + param.__form() + "&" + filterParam.__form();
	},
	toEditWriteRight : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditWriteRight",
			area : [ '900px', '370px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/writeRightEdit.html?onlyShow=" + $(window.parent.document).find("#onlyShow").val() + "&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&writeRightId=" + d.writeRightId
		});
	},
	toShowWriteRight : function(d) {
		layer.open({
			type : 2,
			skin : 'layui-layer-aems',
			id : "toEditWriteRight",
			area : [ '850px', '300px' ],
			title : false,
			scrollbar : false,
			content : basePath + "mdms/functionModule/newTechniqueManager/writeRightView.html?onlyShow=1&compNo=" + param.get("compNo") + "&customFormFilledCode=" + param.get("customFormFilledCode") + "&funCode=" + param.get("funCode") + "&writeRightId=" + d.writeRightId
		});
	},
	deleteWriteRight : function(d) {
		layer.confirm("确定要删除吗？", function() {
			return $.ajax({
				url : basePath + "mdms/writeRight/deleteWriteRight.spring",
				type : "post",
				data : {
					writeRightId : d.writeRightId
				}
			}).then(function(data) {
				assemblys.msg("删除成功", function() {
					$("#writeRightFrame").empty();
					otherFormDetail.getWriteRightList("writeRightFrame");
				});
				return data;
			});
		});
	}
}