<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/routineCheckEdit.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="routineCheckId">
		<input type="hidden" name="customFormFilledCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
				
				<div class="layui-form-item">
					<label class="layui-form-label">
						工号
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="userCode" value="" readonly="readonly" class="layui-input showReadOnly" />
					</div>
					<label class="layui-form-label">
						姓名
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="userName" value="" readonly="readonly" class="layui-input showReadOnly" />
					</div>
				</div>
				
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						考核年度
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="checkYear" value="" class="layui-input" laydate/>
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						有无良好行为记录(15分)
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="hasGoodBehaviour" lay-filter="caculate" value="0" title="无"  checked="checked"  />
						<input type="radio" name="hasGoodBehaviour" lay-filter="caculate" value="1" title="有"  lay-filter="isValid" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						工作成绩评定(15分)
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="number" lay-verify="required" onkeyup="routineCheckEdit.caculate()" maxlength="2" name="workScore" value="" class="layui-input" />
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						有无不良行为记录(15分)
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="hasBadBehaviour" lay-filter="caculate" value="0" title="无"  checked="checked"  />
						<input type="radio" name="hasBadBehaviour" lay-filter="caculate" value="1" title="有"  lay-filter="isValid" />
					</div>
					
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						职业道德评定(15分)
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="number" maxlength="2"  lay-verify="required" onkeyup="routineCheckEdit.caculate()" name="occupationScore" value="" class="layui-input" />
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						是否简易程序(20分)
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="summaryProcedure" lay-filter="caculate" value="0" title="否"  checked="checked"  />
						<input type="radio" name="summaryProcedure" lay-filter="caculate" value="1" title="是"  lay-filter="isValid" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						业务水平评定(20分)
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="number" maxlength="2"  lay-verify="required" onkeyup="routineCheckEdit.caculate()" name="businessScore" value="" class="layui-input" />
					</div>
					<label class="layui-form-label">
						<span style="color: red;"></span>
						总成绩
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="score" value="" readonly class="layui-input" />
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						备注
					</label>
					<div class="layui-input-inline">
						<textarea type="text" lay-verify="limit" limit="500" maxlength="500" style="width:520px;" name="remark" id="remark" value="" class="layui-textarea"></textarea>
					</div>
				</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/routineCheckEdit.js?r="+Math.random()></script>
<script type="text/javascript" src="js/pubMethod.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		routineCheckEdit.init();
	});
</script>