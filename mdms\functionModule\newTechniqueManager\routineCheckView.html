<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/routineCheckEdit.css">
<style type="text/css">
	.tright{
		background:#f1f1f1;
		text-align:right;
	}

	td.tright{
		text-align:right;
	}
</style>
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="routineCheckId">
		<input type="hidden" name="customFormFilledCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName>医师定期考核情况</span>
			</span>
			
		</div>
		
		<div class="bodys">
			<div class="layui-table main_table">
				<table class="layui-table main_table detail_table">
		           <tr>
			            <td class="tright" style="width:20%"  >工号</td>
			            <td class="tleft" style="width:30%" id="userCode" name="userCode"></td>
			            <td class="tright"  style="width:20%"> 姓名</td>
			            <td class="tleft"  style="width:30%"id="userName" name="userName"></td>
		           </tr>
		           
		           <tr>
			            <td class="tright"  >考核年度</td>
			            <td class="tleft" id="checkYear"></td>
			            <td class="tright"  >有无良好行为记录(15分)</td>
			            <td class="tleft" id="hasGoodBehaviour" name="hasGoodBehaviour"></td>
		           </tr>
		           
		          <tr>
			            <td class="tright"  >工作成绩评定(15分)</td>
			            <td class="tleft" id="workScore"></td>
			            <td class="tright"  >有无不良行为记录(15分)</td>
			            <td class="tleft" id="hasBadBehaviour" name="hasBadBehaviour"></td>
		           </tr>
		           
		           <tr>
			            <td class="tright"  >职业道德评定(15分)</td>
			            <td class="tleft" id="occupationScore"></td>
			            <td class="tright"  >是否简易程序(20分)</td>
			            <td class="tleft" id="summaryProcedure" name="summaryProcedure"></td>
		           </tr>
		           
		            <tr>
			            <td class="tright"  >业务水平评定(20分)</td>
			            <td class="tleft" id="businessScore"></td>
			            <td class="tright"  >总成绩</td>
			            <td class="tleft" id="score" name="score"></td>
		           </tr>
		          
		          <tr>
			          <td class="tright" >备注</td>
			          <td class="tleft" style="word-break: break-all;" colspan="3" id="remark" name="remark"></td>
		          </tr>
		          
	         </table>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/routineCheckEdit.js?r="+Math.random()></script>
<script type="text/javascript" src="js/pubMethod.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		routineCheckEdit.getRoutineCheck().then(function(data) {
				pubMethod.getFormEmpInfoForTable();
				$("#businessScore").text(data.routineCheck.businessScore);
				$("#checkYear").text(data.routineCheck.checkYear);
				$("#occupationScore").text(data.routineCheck.occupationScore);
				$("#remark").text(data.routineCheck.remark);
				$("#score").text(data.routineCheck.score);
				$("#workScore").text(data.routineCheck.workScore);
				$("#hasBadBehaviour").text(data.routineCheck.hasBadBehaviour==1?"有":"无");
				$("#summaryProcedure").text(data.routineCheck.summaryProcedure==1?"是":"否");
				$("#hasGoodBehaviour").text(data.routineCheck.hasGoodBehaviour==1?"有":"无");
				return "";
			})
		});
	
</script>