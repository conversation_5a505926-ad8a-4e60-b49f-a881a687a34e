<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
			</div>
		</div>
		
		<div class="bodys">
			<div class="layui-table main_table">
			
				<div id="lzDiv" class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						离职日期
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="pauseDate" id="pauseDate" value="" class="layui-input" autocomplete="off" laydate/>
					</div>
				</div>
				
				<div id="zkDiv" class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						科室
					</label>
					<div class="layui-input-inline">
						<select id=exchangeDeptId lay-verify="required" lay-search=""  name="exchangeDeptId"  ></select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						人员名单
					</label>
					<div id="showDeptUser">
					</div>
				</div>
			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/formSelects/formSelects-v4.min.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
			getDeptList();
			showDeptUser();
			layui.form.render();
			if(param.get("type")==1){
				$("#zkDiv").addClass("layui-hide");
				$("#exchangeDeptId").attr("lay-verify","");
			}else{
				$("#lzDiv").addClass("layui-hide");
				$("#pauseDate").attr("lay-verify","");
			}
			layui.form.on("submit(save)", function() {
				var tip="<font color='red'>离职会回收该档案已授权的权限，是否确定执行离职操作？</font>"
				var pauseDate=$("input[name='pauseDate']").val();
				var deptId=$("#exchangeDeptId").val();
				var val="";
				if(param.get("type")==1){
					val=pauseDate;
				}else{
					val=deptId;
					tip="<font color='red'>转科会回收该档案已授权的权限，是否确定执行转科操作？</font>"
				}
				assemblys.confirm(tip, function() {
					parent.newTechnique.toUserLevaeOut(val,param.get("type"));
					assemblys.closeWindow();
				});
			});
			param.__$.find("input[laydate]").each(function(i, e) {
				layui.laydate.render({
					elem : e,
					trigger : "click",
					type : "date",
					max : 'today',
					value : new Date(),
					format : "yyyy-MM-dd"
				});
			});
		
	});
	
	//hwx 2024年5月23日上午10:04:08 显示专科/离职的科室人员名单
	 function showDeptUser() {
		 $("#showDeptUser").empty();
		 var customFormFilledCodes = parent.newTechnique.customFormFilledCodes;
		 var html = "<table><tr>";
		 if(customFormFilledCodes && customFormFilledCodes.indexOf(",")>-1){
			 var cCodeArr = customFormFilledCodes.split(",");
			 for (var i = 0; i < cCodeArr.length; i++) {
				 getCustomFormData(cCodeArr[i]).then(function(data){
					 if(data){
						if(i!=0 && i%3==0){
					 		html += "<td style='width:20%;text-align:center;'>"+data.docDeptName+"</br>"+data.docUserName+"</td>";
					 		html += "</tr>";
						}else{
					 		html += "<td style='width:20%;text-align:center;'>"+data.docDeptName+"</br>"+data.docUserName+"</td>";
						} 
					 }
				 });
			}
		 }else{
			 getCustomFormData(customFormFilledCodes).then(function(data){
				 if(data){
				 	html += "<td>"+data.docDeptName+" - "+data.docUserName+"</td>";
				 }
			 });
		 }
		 html += "</tr></table>";
		 $("#showDeptUser").append(html);
	 }
	 function getCustomFormData(customFormFilledCode){
		 return $.ajax({
				url : basePath + "mdms/mdmsCommon/getUserInfo.spring",
				dataType : "json",
				async:false,
				data : {
					"doctorCustomFormFilledCode" : customFormFilledCode
				}
			});
	 }
	 function getDeptList() {
			return $.ajax({ 
				url : basePath + "frame/common/getDeptList.spring",
				type : "get",
				data : {
				   "compNo" : parent.param.get("compNo"),
				},
				dataType : "json",
				 skipDataCheck : true, //- 如果接口响应回来的数据有问题，请增加该参数
				success : function(data) {
				  // 结果
					var html = "";
					$.each(data.deptList,function(i,val){
						html += "<option value='" + val.DeptID + "' >" + val.DeptName + "</option>";
					})
					$("#exchangeDeptId").html(html);
					layui.form.render('select');
					
				},
				error : function(){
				}
			});
		}
	
</script>