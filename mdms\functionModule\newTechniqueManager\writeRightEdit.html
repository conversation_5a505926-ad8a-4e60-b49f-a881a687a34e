<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/writeRightEdit.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="writeRightId">
		<input type="hidden" name="uDeptId">
		<input type="hidden" name="uDeptName">
		<input type="hidden" name="authorizeDate">
		<input type="hidden" name="compNo">
		<input type="hidden" name="authorizeUserCode">
		<input type="hidden" name="cancelDate">
		<input type="hidden" name="cancelUserCode">
		<input type="hidden" name="customFormFilledCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName></span>
			</span>
			<div class="head0_right fr">
				<input type="button" class="layui-btn layui-btn-sm" value="保存" lay-submit lay-filter="save" />
				
			</div>
		</div>
		<div class="bodys">
			<div class="layui-table main_table">
			
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						工号
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="userCode" value="" readonly="readonly" class="layui-input showReadOnly" />
					</div>
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						姓名
					</label>
					<div class="layui-input-inline">
						<input type="text" lay-verify="required" name="userName" value="" readonly="readonly" class="layui-input showReadOnly" />
					</div>
				</div>
				<div class="layui-form-item">
					
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						处方权
					</label>
					<div class="layui-input-inline">
						<select lay-verify="required" id="rmosDictId" name="rmosDictId"  xm-select-height="31px" style="width:500px;" >
						</select>
					</div>
					
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						授权类型
					</label>
					<div class="layui-input-inline">
						<select  id="rightType" name="rightType"  layui-search >
						</select>
					</div>
				</div>
				
				
				<div class="layui-form-item">
					<label class="layui-form-label">
						<span style="color: red;">*</span>
						是否有效
					</label>
					<div class="layui-input-inline">
						<input type="radio" name="isValid" value="0" title="否"  lay-filter="isValid" />
						<input type="radio" name="isValid" value="1" title="是"  checked="checked" lay-filter="isValid" />
					</div>

			</div>
		</div>
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/writeRightEdit.js?r="+Math.random()></script>
<script type="text/javascript" src="js/pubMethod.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/formSelects/formSelects-v4.min.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		writeRightEdit.init();
	});
</script>