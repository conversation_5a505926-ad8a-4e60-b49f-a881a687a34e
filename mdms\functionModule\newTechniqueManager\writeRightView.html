<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8" http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache, no-store,must-revalidate">
<meta http-equiv="Cache" content="no-cache">
<title>新增/编辑</title>
<link rel="stylesheet" type="text/css" href="../../../plugins/layui/css/layui.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/static/css/edit.css">
<link rel="stylesheet" type="text/css" href="css/writeRightEdit.css">
<link rel="stylesheet" type="text/css" href="../../../plugins/formSelects/formSelects-v4.css">
<style type="text/css">
	.tright{
		background:#f1f1f1;
		text-align:right;
	}

	td.tright{
		text-align:right;
	}
</style>
</head>
<body>
	<form class="layui-form" lay-filter="param">
		<input type="hidden" name="funCode">
		<input type="hidden" name="writeRightId">
		<input type="hidden" name="uDeptId">
		<input type="hidden" name="uDeptName">
		<input type="hidden" name="authorizeDate">
		<input type="hidden" name="compNo">
		<input type="hidden" name="authorizeUserCode">
		<input type="hidden" name="cancelDate">
		<input type="hidden" name="cancelUserCode">
		<input type="hidden" name="customFormFilledCode">
		<div class="head0">
			<span class="head1_text fw700">
				<i menuIcon class="layui-icon2"></i>
				<span titleName>处方授权</span>
			</span>
		</div>
		
		<div class="bodys">
			<div class="layui-table main_table">
				<table class="layui-table main_table detail_table">
		           <tr>
			            <td class="tright"   style="width:10%;">工号</td>
			            <td class="tleft" style="width:40%;" id="userCode" name="userCode"></td>
			            <td class="tright"  style="width:10%;"> 姓名</td>
			            <td class="tleft" style="width:40%;" id="userName" name="userName"></td>
		           </tr>
		           
		           <tr>
			            <td class="tright"  >处方</td>
			            <td class="tleft" id="rmosDictId"></td>
			            <td class="tright"  >授权类型</td>
			            <td class="tleft" id="rightType" name="rightType"></td>
		           </tr>
		           
		          <tr>
			            <td class="tright"  >是否有效</td>
			            <td class="tleft" id="isValid"></td>
			            <td class="tright"  ></td>
			            <td class="tleft" id="" name=""></td>
		           </tr>
		          
	         </table>
			</div>
		</div>
		
	</form>
</body>
</html>
<script type="text/javascript" src="../../../plugins/layui/layui.all.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/layui/assemblys2.js?r="+Math.random()></script><script type="text/javascript" src="../../../mdms/js/mdmsCommon.js?r="+Math.random()></script>
<script type="text/javascript" src="js/writeRightEdit.js?r="+Math.random()></script>
<script type="text/javascript" src="js/pubMethod.js?r="+Math.random()></script>
<script type="text/javascript" src="../../../plugins/formSelects/formSelects-v4.min.js?r="+Math.random()></script>
<script type="text/javascript">
	var isSubmit = false;
	$(function() {
		 writeRightEdit.getWriteRight().then(function(data) {
			pubMethod.getFormEmpInfoForTable();
			getRmosList(data.writeRight.rmosDictId,"rmosDictId",data.writeRight.writeRightId);
			pubMethod.getDictList(data.writeRight.rightType,"rightType",assemblys.top.mdms.mdmsConstant.SSSQLX);
			if(data.writeRight.isValid==1){
				$("#isValid").text("是");
			}else{
				$("#isValid").text("否");
			}
			return "";
		})
	});
	
	 function getRmosList(val,name,writeRightId) {
		return $.ajax({
			url : basePath + "mdms/writeRight/getRmosdList.spring",
			data : {
				"writeRightId" : writeRightId,
				"customFormFilledCode" : param.get("customFormFilledCode"),
			}
		}).then(function(data) {
			if (data.rmosdList) {
				var htmlTemp = "";
				for (var i = 0; i < data.rmosdList.length; i++) {
					var temp = data.rmosdList[i];
					htmlTemp += "<option value='" + temp["rmosDictId"] + "' >" + temp["rmosName"] + "--" + temp["rmosHisCode"] + "--" + temp["parentName"] + "</option>";
					if(val==temp.rmosDictId){
						var str=temp.rmosName + "--" + temp.rmosHisCode + "--" + temp.parentName;
						$("#"+name).text(temp.rmosName);
					}
				}
				
				
			}
			
			return data;
		});
	}
	
</script>